{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### 导入所需的库"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "from bs4 import BeautifulSoup\n", "import re\n", "import xlwt\n", "import random\n", "from fake_useragent import UserAgent\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.爬取数据\n", "爬取链家网站的租房信息，要求为：\n", "获取” 链家/成都市/高新区/金融城/整租”租房信息（爬取 40 页数据），从各房屋信息中提取” 楼盘名称/面积/装修/近地铁/楼层/总楼层/租金”信息，并将数据写入 lianjia.xls 文件（标题行为['name', 'area', 'floor', 'totalFloor', 'decorate', 'nearSubway','rentFee']）"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在爬取第1页...\n", "找到36个房源条目\n", "当前条目数据：N/A | 楼层:0/0 | 装修:未知 | 地铁:否 | 租金:2500\n", "成功写入第1行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2850\n", "成功写入第2行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 南 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5500\n", "成功写入第3行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 北 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第4行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:7689\n", "成功写入第5行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2700\n", "成功写入第6行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:1390\n", "成功写入第7行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第8行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:4300\n", "成功写入第9行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第10行数据\n", "当前条目数据：整租·中国华商金融中心 1室2厅 西 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:5811\n", "成功写入第11行数据\n", "当前条目数据：整租·润富国际公寓 1室0厅 东 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第12行数据\n", "当前条目数据：整租·高盛金融中心 1室0厅 东 | 楼层:1/48 | 装修:未知 | 地铁:是 | 租金:9500\n", "成功写入第13行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 东 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第14行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:未知 | 地铁:否 | 租金:2800\n", "成功写入第15行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第16行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:3600\n", "成功写入第17行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 东 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第18行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 3室2厅 东 | 楼层:2/41 | 装修:未知 | 地铁:是 | 租金:32000\n", "成功写入第19行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2700\n", "成功写入第20行数据\n", "当前条目数据：整租·人居天府汇城 3室2厅 西 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第21行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 西 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第22行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2800\n", "成功写入第23行数据\n", "当前条目数据：整租·奥克斯广场 2室1厅 西南 | 楼层:2/29 | 装修:精装 | 地铁:是 | 租金:4466\n", "成功写入第24行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 东 | 楼层:3/35 | 装修:精装 | 地铁:是 | 租金:16000\n", "成功写入第25行数据\n", "当前条目数据：整租·中国华商金融中心 1室2厅 东 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:6000\n", "成功写入第26行数据\n", "当前条目数据：整租·中海城南华府 2室1厅 南 | 楼层:1/30 | 装修:精装 | 地铁:是 | 租金:5417\n", "成功写入第27行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:4300\n", "成功写入第28行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2300\n", "成功写入第29行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第30行数据\n", "当前条目数据：整租·中国华商金融中心 2室1厅 北 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:7800\n", "成功写入第31行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2428\n", "成功写入第32行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:8933\n", "成功写入第33行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:8098\n", "成功写入第34行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:11256\n", "成功写入第35行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:5178\n", "成功写入第36行数据\n", "第1页爬取完成，当前已爬取36条数据\n", "等待4.60秒后继续...\n", "正在爬取第2页...\n", "找到30个房源条目\n", "当前条目数据：整租·誉峰三期 1室0厅 东 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第37行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5600\n", "成功写入第38行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2700\n", "成功写入第39行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2960\n", "成功写入第40行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 西 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第41行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2797\n", "成功写入第42行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 北 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:3000\n", "成功写入第43行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2598\n", "成功写入第44行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第45行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 北 | 楼层:2/27 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第46行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2850\n", "成功写入第47行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东南 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:5800\n", "成功写入第48行数据\n", "当前条目数据：整租·瑞禧公馆 1室0厅 东南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第49行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 南 | 楼层:2/37 | 装修:精装 | 地铁:是 | 租金:24000\n", "成功写入第50行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第51行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2428\n", "成功写入第52行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 东 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第53行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:3300\n", "成功写入第54行数据\n", "当前条目数据：整租·中海城南一号一期 4室2厅 南 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:11000\n", "成功写入第55行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 东南 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:4800\n", "成功写入第56行数据\n", "当前条目数据：整租·高盛金融中心 2室1厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:4800\n", "成功写入第57行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2626\n", "成功写入第58行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:7500\n", "成功写入第59行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第60行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3300\n", "成功写入第61行数据\n", "当前条目数据：整租·银泰中心 1室2厅 西 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第62行数据\n", "当前条目数据：整租·银泰中心 2室2厅 西 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:15000\n", "成功写入第63行数据\n", "当前条目数据：整租·中海城南一号二期 4室2厅 南/北 | 楼层:3/23 | 装修:精装 | 地铁:否 | 租金:12000\n", "成功写入第64行数据\n", "当前条目数据：整租·仁和春天国际花园 4室2厅 西北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:14000\n", "成功写入第65行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第66行数据\n", "第2页爬取完成，当前已爬取66条数据\n", "等待4.20秒后继续...\n", "正在爬取第3页...\n", "找到30个房源条目\n", "当前条目数据：整租·礼顿山1号 2室2厅 南 | 楼层:2/32 | 装修:精装 | 地铁:否 | 租金:4030\n", "成功写入第67行数据\n", "当前条目数据：整租·银泰中心 3室2厅 西南 | 楼层:3/46 | 装修:精装 | 地铁:否 | 租金:50000\n", "成功写入第68行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2700\n", "成功写入第69行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2221\n", "成功写入第70行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第71行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2598\n", "成功写入第72行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:3100\n", "成功写入第73行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2715\n", "成功写入第74行数据\n", "当前条目数据：整租·招商玺荟 2室2厅 东北 | 楼层:3/51 | 装修:精装 | 地铁:是 | 租金:7100\n", "成功写入第75行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 东南 | 楼层:3/38 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第76行数据\n", "当前条目数据：整租·礼顿山1号 2室1厅 东南 | 楼层:3/32 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第77行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 西 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2300\n", "成功写入第78行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 东/西 | 楼层:2/30 | 装修:精装 | 地铁:否 | 租金:6800\n", "成功写入第79行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 南 | 楼层:2/49 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第80行数据\n", "当前条目数据：整租·中海九号公馆 4室2厅 南/北 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第81行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:2598\n", "成功写入第82行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:3600\n", "成功写入第83行数据\n", "当前条目数据：整租·仁和春天国际广场 1室2厅 西 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:4300\n", "成功写入第84行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2900\n", "成功写入第85行数据\n", "当前条目数据：整租·仁和春天国际广场 1室0厅 东 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:3600\n", "成功写入第86行数据\n", "当前条目数据：整租·四川武警森林总队 3室2厅 东南 | 楼层:1/17 | 装修:精装 | 地铁:否 | 租金:4400\n", "成功写入第87行数据\n", "当前条目数据：N/A | 楼层:0/0 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第88行数据\n", "当前条目数据：整租·仁和春天国际花园 4室2厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:12800\n", "成功写入第89行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第90行数据\n", "当前条目数据：整租·中海城南一号一期 2室1厅 跃层 北 | 楼层:2/23 | 装修:精装 | 地铁:是 | 租金:5200\n", "成功写入第91行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 南/北 | 楼层:2/24 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第92行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 东南 | 楼层:2/52 | 装修:精装 | 地铁:是 | 租金:3300\n", "成功写入第93行数据\n", "当前条目数据：整租·润富国际公寓 1室0厅 西 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第94行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 南/西 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第95行数据\n", "当前条目数据：整租·中海城南一号一期 3室2厅 北 | 楼层:1/24 | 装修:精装 | 地铁:是 | 租金:11000\n", "成功写入第96行数据\n", "第3页爬取完成，当前已爬取96条数据\n", "等待2.16秒后继续...\n", "正在爬取第4页...\n", "找到0个房源条目\n", "第4页爬取完成，当前已爬取96条数据\n", "等待4.71秒后继续...\n", "正在爬取第5页...\n", "找到0个房源条目\n", "第5页爬取完成，当前已爬取96条数据\n", "等待3.49秒后继续...\n", "正在爬取第6页...\n", "找到0个房源条目\n", "第6页爬取完成，当前已爬取96条数据\n", "等待3.63秒后继续...\n", "正在爬取第7页...\n", "找到30个房源条目\n", "当前条目数据：整租·西派国际 5室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:16200\n", "成功写入第97行数据\n", "当前条目数据：整租·中海城南华府 2室2厅 南 | 楼层:1/30 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第98行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东南 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2600\n", "成功写入第99行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东南/南/西南 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第100行数据\n", "当前条目数据：整租·银泰中心 3室2厅 西南 | 楼层:3/46 | 装修:精装 | 地铁:否 | 租金:50000\n", "成功写入第101行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 南 | 楼层:2/49 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第102行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:3600\n", "成功写入第103行数据\n", "当前条目数据：整租·银泰中心 4室2厅 东南 | 楼层:3/46 | 装修:精装 | 地铁:是 | 租金:49000\n", "成功写入第104行数据\n", "当前条目数据：整租·仁和春天国际广场 1室2厅 西 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:4300\n", "成功写入第105行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2900\n", "成功写入第106行数据\n", "当前条目数据：整租·仁和春天国际广场 1室0厅 东 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:3600\n", "成功写入第107行数据\n", "当前条目数据：整租·四川武警森林总队 3室2厅 东南 | 楼层:1/17 | 装修:精装 | 地铁:否 | 租金:4400\n", "成功写入第108行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第109行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6300\n", "成功写入第110行数据\n", "当前条目数据：整租·中海城南一号一期 2室1厅 跃层 西/北 | 楼层:1/23 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第111行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第112行数据\n", "当前条目数据：整租·誉峰一期 4室2厅 西北 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:16000\n", "成功写入第113行数据\n", "当前条目数据：整租·复地金融岛 5室2厅 南 | 楼层:2/28 | 装修:未知 | 地铁:否 | 租金:10000\n", "成功写入第114行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 东 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第115行数据\n", "当前条目数据：整租·合景誉舍 3室2厅 东南 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:15000\n", "成功写入第116行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 北 | 楼层:1/31 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第117行数据\n", "当前条目数据：整租·银泰中心 1室1厅 北 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:5500\n", "成功写入第118行数据\n", "当前条目数据：整租·银泰中心 2室1厅 东 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:12000\n", "成功写入第119行数据\n", "当前条目数据：整租·中国华商金融中心 2室2厅 南 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:10000\n", "成功写入第120行数据\n", "当前条目数据：整租·高盛金融中心 2室2厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:5200\n", "成功写入第121行数据\n", "当前条目数据：整租·奥克斯广场 3室2厅 北 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第122行数据\n", "当前条目数据：整租·复地金融岛 4室1厅 东 | 楼层:2/45 | 装修:精装 | 地铁:是 | 租金:6100\n", "成功写入第123行数据\n", "当前条目数据：整租·润富国际公寓 1室0厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第124行数据\n", "当前条目数据：整租·银泰中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:14000\n", "成功写入第125行数据\n", "当前条目数据：整租·保利锦外小户 1室0厅 南 | 楼层:1/24 | 装修:未知 | 地铁:是 | 租金:2104\n", "成功写入第126行数据\n", "第7页爬取完成，当前已爬取126条数据\n", "等待2.66秒后继续...\n", "正在爬取第8页...\n", "找到30个房源条目\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/27 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第127行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:1/27 | 装修:精装 | 地铁:否 | 租金:2775\n", "成功写入第128行数据\n", "当前条目数据：整租·中国华商金融中心 2室1厅 南 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:10000\n", "成功写入第129行数据\n", "当前条目数据：整租·首座MAX 3室1厅 南 | 楼层:2/27 | 装修:未知 | 地铁:否 | 租金:8200\n", "成功写入第130行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第131行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:6500\n", "成功写入第132行数据\n", "当前条目数据：整租·中航国际交流中心 2室2厅 北 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第133行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 西 | 楼层:1/24 | 装修:精装 | 地铁:否 | 租金:7500\n", "成功写入第134行数据\n", "当前条目数据：整租·中海城南一号一期 4室2厅 南 | 楼层:1/24 | 装修:精装 | 地铁:否 | 租金:15000\n", "成功写入第135行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2700\n", "成功写入第136行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:2800\n", "成功写入第137行数据\n", "当前条目数据：整租·中国华商金融中心 2室2厅 北 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:10500\n", "成功写入第138行数据\n", "当前条目数据：整租·仁和春天国际花园 4室1厅 南/北 | 楼层:3/31 | 装修:未知 | 地铁:是 | 租金:5000\n", "成功写入第139行数据\n", "当前条目数据：整租·誉峰二期 3室2厅 西北 | 楼层:1/30 | 装修:精装 | 地铁:否 | 租金:18000\n", "成功写入第140行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:3/51 | 装修:未知 | 地铁:是 | 租金:4600\n", "成功写入第141行数据\n", "当前条目数据：整租·中航国际交流中心 1室0厅 南 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第142行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第143行数据\n", "当前条目数据：整租·西派国际 4室2厅 南/北 | 楼层:3/29 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第144行数据\n", "当前条目数据：整租·银泰中心 1室1厅 西南 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:9000\n", "成功写入第145行数据\n", "当前条目数据：整租·奥克斯广场 1室0厅 东 | 楼层:2/29 | 装修:未知 | 地铁:是 | 租金:2800\n", "成功写入第146行数据\n", "当前条目数据：整租·中海城南一号一期 2室1厅 跃层 北 | 楼层:2/23 | 装修:精装 | 地铁:是 | 租金:5200\n", "成功写入第147行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 南/北 | 楼层:2/24 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第148行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:4300\n", "成功写入第149行数据\n", "当前条目数据：整租·中海城南一号一期 2室2厅 西 | 楼层:1/23 | 装修:精装 | 地铁:是 | 租金:5800\n", "成功写入第150行数据\n", "当前条目数据：整租·中航国际交流中心 1室0厅 东 | 楼层:1/26 | 装修:精装 | 地铁:是 | 租金:2300\n", "成功写入第151行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第152行数据\n", "当前条目数据：整租·中海城南一号二期 5室2厅 南/北 | 楼层:2/26 | 装修:精装 | 地铁:否 | 租金:17000\n", "成功写入第153行数据\n", "当前条目数据：整租·中海城南一号一期 2室1厅 南 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第154行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 东 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第155行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:6599\n", "成功写入第156行数据\n", "第8页爬取完成，当前已爬取156条数据\n", "等待2.37秒后继续...\n", "正在爬取第9页...\n", "找到0个房源条目\n", "第9页爬取完成，当前已爬取156条数据\n", "等待4.76秒后继续...\n", "正在爬取第10页...\n", "找到30个房源条目\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:3/51 | 装修:未知 | 地铁:是 | 租金:4600\n", "成功写入第157行数据\n", "当前条目数据：整租·中航国际交流中心 1室0厅 南 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第158行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第159行数据\n", "当前条目数据：整租·西派国际 4室2厅 南/北 | 楼层:3/29 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第160行数据\n", "当前条目数据：整租·银泰中心 1室1厅 西南 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:9000\n", "成功写入第161行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 西/北 | 楼层:2/52 | 装修:精装 | 地铁:是 | 租金:6500\n", "成功写入第162行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:6000\n", "成功写入第163行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南/西 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第164行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 南/北 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:6800\n", "成功写入第165行数据\n", "当前条目数据：整租·合景誉舍 1室1厅 东南 | 楼层:1/30 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第166行数据\n", "当前条目数据：整租·中航国际交流中心 2室2厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第167行数据\n", "当前条目数据：整租·天府世家 3室2厅 西南 | 楼层:1/33 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第168行数据\n", "当前条目数据：整租·誉峰二期 4室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:否 | 租金:15000\n", "成功写入第169行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东南/南 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第170行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东/南 | 楼层:3/44 | 装修:精装 | 地铁:是 | 租金:7900\n", "成功写入第171行数据\n", "当前条目数据：整租·中海城南一号一期 4室2厅 南 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:12000\n", "成功写入第172行数据\n", "当前条目数据：整租·润富国际 3室2厅 东 | 楼层:2/29 | 装修:精装 | 地铁:是 | 租金:13000\n", "成功写入第173行数据\n", "当前条目数据：整租·中国华商金融中心 1室2厅 西 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:5811\n", "成功写入第174行数据\n", "当前条目数据：整租·誉峰二期 4室2厅 东 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:16800\n", "成功写入第175行数据\n", "当前条目数据：整租·中航国际广场 2室1厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:4300\n", "成功写入第176行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第177行数据\n", "当前条目数据：整租·誉峰一期 5室2厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第178行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第179行数据\n", "当前条目数据：整租·中海九号公馆 5室2厅 南/北 | 楼层:1/30 | 装修:精装 | 地铁:否 | 租金:20000\n", "成功写入第180行数据\n", "当前条目数据：整租·中海城南一号二期 4室2厅 南 | 楼层:2/26 | 装修:精装 | 地铁:否 | 租金:20000\n", "成功写入第181行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东南 | 楼层:2/37 | 装修:精装 | 地铁:是 | 租金:19200\n", "成功写入第182行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2550\n", "成功写入第183行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东南 | 楼层:3/37 | 装修:精装 | 地铁:是 | 租金:22000\n", "成功写入第184行数据\n", "当前条目数据：整租·四川武警森林总队 2室1厅 南 | 楼层:1/17 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第185行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西南/西 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第186行数据\n", "第10页爬取完成，当前已爬取186条数据\n", "等待2.73秒后继续...\n", "正在爬取第11页...\n", "找到30个房源条目\n", "当前条目数据：整租·仁和春天国际花园 3室1厅 东南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:7900\n", "成功写入第187行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第188行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 3室1厅 东/南 | 楼层:3/41 | 装修:未知 | 地铁:是 | 租金:8800\n", "成功写入第189行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第190行数据\n", "当前条目数据：整租·中海九号公馆 2室2厅 东 | 楼层:3/26 | 装修:精装 | 地铁:否 | 租金:7000\n", "成功写入第191行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:9500\n", "成功写入第192行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 北 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:5500\n", "成功写入第193行数据\n", "当前条目数据：整租·银泰泰悦湾 3室2厅 东南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第194行数据\n", "当前条目数据：整租·金房大榕湾人家 1室1厅 东 | 楼层:3/4 | 装修:精装 | 地铁:是 | 租金:3100\n", "成功写入第195行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:15800\n", "成功写入第196行数据\n", "当前条目数据：整租·中海城南一号一期 6室2厅 南/北 | 楼层:2/23 | 装修:精装 | 地铁:否 | 租金:10000\n", "成功写入第197行数据\n", "当前条目数据：整租·中海城南一号一期 5室2厅 南 | 楼层:1/24 | 装修:精装 | 地铁:否 | 租金:15800\n", "成功写入第198行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第199行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第200行数据\n", "当前条目数据：整租·银泰泰悦湾 3室2厅 东南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:7200\n", "成功写入第201行数据\n", "当前条目数据：整租·中航国际广场 1室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第202行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:3040\n", "成功写入第203行数据\n", "当前条目数据：整租·保利锦外小户 1室0厅 西南 | 楼层:1/24 | 装修:精装 | 地铁:是 | 租金:2000\n", "成功写入第204行数据\n", "当前条目数据：整租·中海城南一号一期 2室2厅 西 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第205行数据\n", "当前条目数据：整租·南苑小区B区 4室2厅 南 | 楼层:1/16 | 装修:精装 | 地铁:是 | 租金:11000\n", "成功写入第206行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东南 | 楼层:2/37 | 装修:精装 | 地铁:是 | 租金:25000\n", "成功写入第207行数据\n", "当前条目数据：整租·中国华商金融中心 1室2厅 南 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:6900\n", "成功写入第208行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 东南 | 楼层:3/37 | 装修:精装 | 地铁:是 | 租金:22500\n", "成功写入第209行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:9200\n", "成功写入第210行数据\n", "当前条目数据：整租·誉峰三期 2室2厅 南 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第211行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 北 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第212行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第213行数据\n", "当前条目数据：整租·仁和春天国际广场 1室1厅 西南 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:3650\n", "成功写入第214行数据\n", "当前条目数据：整租·银泰中心 1室1厅 北 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:4880\n", "成功写入第215行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第216行数据\n", "第11页爬取完成，当前已爬取216条数据\n", "等待4.58秒后继续...\n", "正在爬取第12页...\n", "找到30个房源条目\n", "当前条目数据：整租·复地金融岛 4室2厅 东 | 楼层:1/28 | 装修:精装 | 地铁:是 | 租金:10800\n", "成功写入第217行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 东 | 楼层:1/25 | 装修:精装 | 地铁:是 | 租金:14000\n", "成功写入第218行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南 | 楼层:2/25 | 装修:精装 | 地铁:是 | 租金:14500\n", "成功写入第219行数据\n", "当前条目数据：整租·南苑小区A区 3室2厅 南/北 | 楼层:3/16 | 装修:精装 | 地铁:是 | 租金:8000\n", "成功写入第220行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3300\n", "成功写入第221行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3100\n", "成功写入第222行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 东南 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:15800\n", "成功写入第223行数据\n", "当前条目数据：整租·润富国际公寓 1室0厅 东 | 楼层:2/31 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第224行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:3350\n", "成功写入第225行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2300\n", "成功写入第226行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:3800\n", "成功写入第227行数据\n", "当前条目数据：整租·中航国际广场 1室1厅 东 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:4050\n", "成功写入第228行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第229行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2100\n", "成功写入第230行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:5300\n", "成功写入第231行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:4500\n", "成功写入第232行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 东 | 楼层:1/25 | 装修:精装 | 地铁:是 | 租金:12500\n", "成功写入第233行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 西 | 楼层:3/49 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第234行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:3/28 | 装修:未知 | 地铁:否 | 租金:2500\n", "成功写入第235行数据\n", "当前条目数据：整租·中国华商金融中心 2室1厅 东南 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:16000\n", "成功写入第236行数据\n", "当前条目数据：整租·奥克斯广场 2室2厅 北 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:4500\n", "成功写入第237行数据\n", "当前条目数据：整租·首座MAX 4室2厅 南 | 楼层:3/27 | 装修:精装 | 地铁:否 | 租金:15000\n", "成功写入第238行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东南 | 楼层:1/52 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第239行数据\n", "当前条目数据：整租·复地金融岛 3室2厅 东 | 楼层:1/38 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第240行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西南 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:3500\n", "成功写入第241行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 5室1厅 东北 | 楼层:3/41 | 装修:精装 | 地铁:是 | 租金:22000\n", "成功写入第242行数据\n", "当前条目数据：整租·银泰中心 4室2厅 东南 | 楼层:3/46 | 装修:精装 | 地铁:是 | 租金:49000\n", "成功写入第243行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西南 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:3600\n", "成功写入第244行数据\n", "当前条目数据：整租·礼顿山1号 3室2厅 南 | 楼层:3/32 | 装修:精装 | 地铁:否 | 租金:4950\n", "成功写入第245行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 东 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3200\n", "成功写入第246行数据\n", "第12页爬取完成，当前已爬取246条数据\n", "等待4.80秒后继续...\n", "正在爬取第13页...\n", "找到0个房源条目\n", "第13页爬取完成，当前已爬取246条数据\n", "等待3.35秒后继续...\n", "正在爬取第14页...\n", "找到0个房源条目\n", "第14页爬取完成，当前已爬取246条数据\n", "等待4.52秒后继续...\n", "正在爬取第15页...\n", "找到30个房源条目\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 西 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第247行数据\n", "当前条目数据：整租·仁和春天国际广场 2室1厅 南 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:5800\n", "成功写入第248行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 西 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:5600\n", "成功写入第249行数据\n", "当前条目数据：整租·奥克斯广场 1室0厅 西 | 楼层:1/29 | 装修:未知 | 地铁:是 | 租金:2700\n", "成功写入第250行数据\n", "当前条目数据：整租·中海城南一号一期 4室2厅 南/北 | 楼层:2/24 | 装修:精装 | 地铁:是 | 租金:11000\n", "成功写入第251行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 东南 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:6500\n", "成功写入第252行数据\n", "当前条目数据：整租·枫丹国际 2室1厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:3500\n", "成功写入第253行数据\n", "当前条目数据：整租·高盛金融中心 3室2厅 北 | 楼层:1/34 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第254行数据\n", "当前条目数据：整租·润富国际公寓 1室0厅 西 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第255行数据\n", "当前条目数据：整租·礼顿山1号 2室2厅 东南 | 楼层:3/32 | 装修:精装 | 地铁:否 | 租金:4300\n", "成功写入第256行数据\n", "当前条目数据：整租·高盛金融中心 1室0厅 东 | 楼层:1/48 | 装修:精装 | 地铁:是 | 租金:3500\n", "成功写入第257行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 南 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:8800\n", "成功写入第258行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 西 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:3800\n", "成功写入第259行数据\n", "当前条目数据：整租·中国华商金融中心 2室2厅 南 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:16000\n", "成功写入第260行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第261行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/27 | 装修:精装 | 地铁:否 | 租金:4000\n", "成功写入第262行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 北 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:6000\n", "成功写入第263行数据\n", "当前条目数据：整租·中海城南一号一期 4室2厅 东 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:8000\n", "成功写入第264行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东南 | 楼层:2/49 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第265行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 东 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第266行数据\n", "当前条目数据：整租·誉峰一期 4室2厅 东北 | 楼层:3/24 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第267行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 西南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第268行数据\n", "当前条目数据：整租·中海城南华府 2室1厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第269行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 东 | 楼层:1/38 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第270行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5600\n", "成功写入第271行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东南 | 楼层:2/51 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第272行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第273行数据\n", "当前条目数据：整租·奥克斯广场 1室1厅 南 | 楼层:2/29 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第274行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 南 | 楼层:2/37 | 装修:精装 | 地铁:是 | 租金:24000\n", "成功写入第275行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:3300\n", "成功写入第276行数据\n", "第15页爬取完成，当前已爬取276条数据\n", "等待3.25秒后继续...\n", "正在爬取第16页...\n", "找到30个房源条目\n", "当前条目数据：整租·保利锦湖林语北区 3室2厅 西 | 楼层:1/22 | 装修:未知 | 地铁:是 | 租金:4800\n", "成功写入第277行数据\n", "当前条目数据：整租·誉峰二期 4室2厅 南 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:16044\n", "成功写入第278行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:1/31 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第279行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东/东北 | 楼层:2/37 | 装修:精装 | 地铁:否 | 租金:28000\n", "成功写入第280行数据\n", "当前条目数据：整租·布鲁明顿广场 1室0厅 北 | 楼层:1/28 | 装修:未知 | 地铁:否 | 租金:2600\n", "成功写入第281行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3300\n", "成功写入第282行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第283行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2750\n", "成功写入第284行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 2室1厅 东南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第285行数据\n", "当前条目数据：整租·中海城南华府 2室1厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:5200\n", "成功写入第286行数据\n", "当前条目数据：整租·奥克斯广场 2室1厅 南 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:4700\n", "成功写入第287行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第288行数据\n", "当前条目数据：整租·四川武警森林总队 2室1厅 南 | 楼层:1/17 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第289行数据\n", "当前条目数据：整租·誉峰三期 1室2厅 东 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第290行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 东 | 楼层:2/52 | 装修:精装 | 地铁:是 | 租金:3300\n", "成功写入第291行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西南/西 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第292行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 北 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:5800\n", "成功写入第293行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东 | 楼层:1/51 | 装修:精装 | 地铁:是 | 租金:6500\n", "成功写入第294行数据\n", "当前条目数据：整租·仁和春天国际广场 1室1厅 西 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第295行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第296行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 北 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:5800\n", "成功写入第297行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 北 | 楼层:3/18 | 装修:精装 | 地铁:是 | 租金:8000\n", "成功写入第298行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 东 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3600\n", "成功写入第299行数据\n", "当前条目数据：整租·新和领航 1室1厅 东南 | 楼层:2/9 | 装修:精装 | 地铁:否 | 租金:1600\n", "成功写入第300行数据\n", "当前条目数据：整租·中海城南华府 2室2厅 南 | 楼层:1/30 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第301行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 南 | 楼层:3/44 | 装修:精装 | 地铁:是 | 租金:8500\n", "成功写入第302行数据\n", "当前条目数据：整租·南苑小区B区 4室2厅 南 | 楼层:1/16 | 装修:精装 | 地铁:是 | 租金:8500\n", "成功写入第303行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第304行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:5300\n", "成功写入第305行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东南 | 楼层:2/45 | 装修:未知 | 地铁:是 | 租金:7500\n", "成功写入第306行数据\n", "第16页爬取完成，当前已爬取306条数据\n", "等待3.09秒后继续...\n", "正在爬取第17页...\n", "找到0个房源条目\n", "第17页爬取完成，当前已爬取306条数据\n", "等待3.11秒后继续...\n", "正在爬取第18页...\n", "找到30个房源条目\n", "当前条目数据：整租·中海城南华府 2室1厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第307行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5600\n", "成功写入第308行数据\n", "当前条目数据：整租·招商玺荟 2室2厅 东南 | 楼层:2/49 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第309行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 南 | 楼层:1/24 | 装修:精装 | 地铁:是 | 租金:27000\n", "成功写入第310行数据\n", "当前条目数据：整租·金房大榕湾人家 2室2厅 东/东北 | 楼层:2/4 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第311行数据\n", "当前条目数据：整租·高盛金融中心 3室2厅 南 | 楼层:2/34 | 装修:精装 | 地铁:是 | 租金:6200\n", "成功写入第312行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:3/42 | 装修:未知 | 地铁:否 | 租金:8000\n", "成功写入第313行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2800\n", "成功写入第314行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 东 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第315行数据\n", "当前条目数据：整租·中航国际交流中心 2室1厅 西 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3900\n", "成功写入第316行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 西南 | 楼层:1/31 | 装修:精装 | 地铁:否 | 租金:6200\n", "成功写入第317行数据\n", "当前条目数据：整租·中海九号公馆 4室2厅 南 | 楼层:1/31 | 装修:精装 | 地铁:否 | 租金:12000\n", "成功写入第318行数据\n", "当前条目数据：整租·高盛金融中心 2室1厅 南 | 楼层:2/34 | 装修:精装 | 地铁:是 | 租金:5200\n", "成功写入第319行数据\n", "当前条目数据：整租·银泰泰悦湾 2室1厅 西 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:5300\n", "成功写入第320行数据\n", "当前条目数据：整租·中航国际交流中心 2室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3700\n", "成功写入第321行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第322行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2380\n", "成功写入第323行数据\n", "当前条目数据：整租·中海城南一号一期 2室2厅 东 | 楼层:2/23 | 装修:精装 | 地铁:是 | 租金:5600\n", "成功写入第324行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 西 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6500\n", "成功写入第325行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2589\n", "成功写入第326行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 北 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2656\n", "成功写入第327行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2900\n", "成功写入第328行数据\n", "当前条目数据：整租·银泰中心 1室1厅 北 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第329行数据\n", "当前条目数据：整租·银泰中心 1室1厅 北 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:4800\n", "成功写入第330行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室1厅 东南/西 | 楼层:3/38 | 装修:精装 | 地铁:是 | 租金:12800\n", "成功写入第331行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 东 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第332行数据\n", "当前条目数据：整租·布鲁明顿广场 1室0厅 东 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第333行数据\n", "当前条目数据：整租·保利锦湖林语北区 3室1厅 西 | 楼层:1/22 | 装修:精装 | 地铁:是 | 租金:4800\n", "成功写入第334行数据\n", "当前条目数据：整租·新园大道26号院 2室1厅 南 | 楼层:3/6 | 装修:未知 | 地铁:是 | 租金:2000\n", "成功写入第335行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 西 | 楼层:3/51 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第336行数据\n", "第18页爬取完成，当前已爬取336条数据\n", "等待2.92秒后继续...\n", "正在爬取第19页...\n", "找到30个房源条目\n", "当前条目数据：整租·天府世家 3室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第337行数据\n", "当前条目数据：整租·银泰中心 1室1厅 东南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第338行数据\n", "当前条目数据：整租·四川武警森林总队 3室2厅 南 | 楼层:2/17 | 装修:未知 | 地铁:否 | 租金:4200\n", "成功写入第339行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室2厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第340行数据\n", "当前条目数据：整租·中海城南一号二期 4室2厅 南 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:9900\n", "成功写入第341行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:5000\n", "成功写入第342行数据\n", "当前条目数据：整租·仁和春天国际广场 2室1厅 东/北 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:7000\n", "成功写入第343行数据\n", "当前条目数据：整租·布鲁明顿广场 2室1厅 东/西 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第344行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 南 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2800\n", "成功写入第345行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第346行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:18000\n", "成功写入第347行数据\n", "当前条目数据：整租·银泰中心 1室1厅 东北 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第348行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 东南 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第349行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:4800\n", "成功写入第350行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南/北 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:11500\n", "成功写入第351行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 西南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第352行数据\n", "当前条目数据：整租·中海城南一号一期 2室1厅 南 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第353行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 东 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第354行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:6599\n", "成功写入第355行数据\n", "当前条目数据：整租·天府世家 3室1厅 南 | 楼层:1/26 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第356行数据\n", "当前条目数据：整租·中国华商金融中心 2室2厅 南 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:11500\n", "成功写入第357行数据\n", "当前条目数据：整租·南苑小区B区 4室2厅 南 | 楼层:2/16 | 装修:精装 | 地铁:否 | 租金:10000\n", "成功写入第358行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第359行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第360行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第361行数据\n", "当前条目数据：整租·仁和春天国际花园 4室2厅 南/北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:12000\n", "成功写入第362行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 东南 | 楼层:2/28 | 装修:未知 | 地铁:是 | 租金:7000\n", "成功写入第363行数据\n", "当前条目数据：整租·天府世家 3室2厅 南 | 楼层:1/33 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第364行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第365行数据\n", "当前条目数据：整租·奥克斯广场 2室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:否 | 租金:4500\n", "成功写入第366行数据\n", "第19页爬取完成，当前已爬取366条数据\n", "等待2.44秒后继续...\n", "正在爬取第20页...\n", "找到0个房源条目\n", "第20页爬取完成，当前已爬取366条数据\n", "等待2.27秒后继续...\n", "正在爬取第21页...\n", "找到0个房源条目\n", "第21页爬取完成，当前已爬取366条数据\n", "等待4.45秒后继续...\n", "正在爬取第22页...\n", "找到0个房源条目\n", "第22页爬取完成，当前已爬取366条数据\n", "等待4.00秒后继续...\n", "正在爬取第23页...\n", "找到30个房源条目\n", "当前条目数据：整租·中航国际广场 1室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第367行数据\n", "当前条目数据：整租·银泰中心 1室2厅 西 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第368行数据\n", "当前条目数据：整租·银泰中心 2室2厅 西 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:15000\n", "成功写入第369行数据\n", "当前条目数据：整租·中海城南一号二期 4室2厅 南/北 | 楼层:3/23 | 装修:精装 | 地铁:否 | 租金:12000\n", "成功写入第370行数据\n", "当前条目数据：整租·仁和春天国际花园 4室2厅 西北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:14000\n", "成功写入第371行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第372行数据\n", "当前条目数据：整租·誉峰三期 3室2厅 南 | 楼层:3/27 | 装修:精装 | 地铁:否 | 租金:7200\n", "成功写入第373行数据\n", "当前条目数据：整租·润富国际公寓 1室0厅 东/南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:3500\n", "成功写入第374行数据\n", "当前条目数据：整租·中航国际交流中心 1室0厅 南 | 楼层:1/29 | 装修:精装 | 地铁:是 | 租金:2320\n", "成功写入第375行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:4680\n", "成功写入第376行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南/北 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:16000\n", "成功写入第377行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室2厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:5600\n", "成功写入第378行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东南 | 楼层:2/31 | 装修:精装 | 地铁:否 | 租金:6500\n", "成功写入第379行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 北 | 楼层:2/52 | 装修:精装 | 地铁:是 | 租金:3200\n", "成功写入第380行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 东 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:3200\n", "成功写入第381行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南 | 楼层:1/20 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第382行数据\n", "当前条目数据：整租·仁和春天国际广场 1室1厅 东南 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第383行数据\n", "当前条目数据：整租·仁和春天国际花园 3室1厅 东南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:7900\n", "成功写入第384行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第385行数据\n", "当前条目数据：整租·中海九号公馆 2室2厅 东 | 楼层:3/26 | 装修:精装 | 地铁:否 | 租金:7000\n", "成功写入第386行数据\n", "当前条目数据：整租·银泰泰悦湾 3室2厅 东南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第387行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:9500\n", "成功写入第388行数据\n", "当前条目数据：整租·银泰中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第389行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 北 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:5500\n", "成功写入第390行数据\n", "当前条目数据：整租·泛林格兰晴天 3室1厅 南 | 楼层:1/16 | 装修:未知 | 地铁:否 | 租金:4200\n", "成功写入第391行数据\n", "当前条目数据：整租·金房大榕湾人家 1室1厅 东 | 楼层:3/4 | 装修:精装 | 地铁:是 | 租金:3100\n", "成功写入第392行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:15800\n", "成功写入第393行数据\n", "当前条目数据：整租·中海城南一号一期 5室2厅 南 | 楼层:1/24 | 装修:精装 | 地铁:否 | 租金:15800\n", "成功写入第394行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第395行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第396行数据\n", "第23页爬取完成，当前已爬取396条数据\n", "等待4.07秒后继续...\n", "正在爬取第24页...\n", "找到30个房源条目\n", "当前条目数据：整租·润富国际公寓 1室1厅 东/南 | 楼层:1/31 | 装修:精装 | 地铁:否 | 租金:4000\n", "成功写入第397行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 南 | 楼层:2/24 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第398行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第399行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 东 | 楼层:1/38 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第400行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5600\n", "成功写入第401行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 北 | 楼层:2/27 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第402行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第403行数据\n", "当前条目数据：整租·枫丹国际 1室1厅 东 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3600\n", "成功写入第404行数据\n", "当前条目数据：整租·奥克斯广场 1室1厅 南 | 楼层:2/29 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第405行数据\n", "当前条目数据：整租·复地金融岛湾流汇 2室1厅 西/北 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:6390\n", "成功写入第406行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 南 | 楼层:2/37 | 装修:精装 | 地铁:是 | 租金:24000\n", "成功写入第407行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:3300\n", "成功写入第408行数据\n", "当前条目数据：整租·银泰中心 1室1厅 西 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:10500\n", "成功写入第409行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东南 | 楼层:1/37 | 装修:精装 | 地铁:是 | 租金:20000\n", "成功写入第410行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3300\n", "成功写入第411行数据\n", "当前条目数据：整租·中航国际广场 1室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第412行数据\n", "当前条目数据：整租·银泰中心 1室2厅 西 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第413行数据\n", "当前条目数据：整租·银泰中心 2室2厅 西 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:15000\n", "成功写入第414行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 南 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:7500\n", "成功写入第415行数据\n", "当前条目数据：整租·仁和春天国际花园 4室2厅 西北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:14000\n", "成功写入第416行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 西 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:6200\n", "成功写入第417行数据\n", "当前条目数据：整租·保利锦湖林语南区 3室2厅 西 | 楼层:2/22 | 装修:未知 | 地铁:否 | 租金:4500\n", "成功写入第418行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东 | 楼层:2/31 | 装修:未知 | 地铁:否 | 租金:6500\n", "成功写入第419行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南/北 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:16000\n", "成功写入第420行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 南 | 楼层:1/24 | 装修:未知 | 地铁:是 | 租金:2200\n", "成功写入第421行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第422行数据\n", "当前条目数据：整租·南苑小区B区 4室1厅 东南 | 楼层:3/16 | 装修:未知 | 地铁:否 | 租金:4400\n", "成功写入第423行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 南 | 楼层:1/45 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第424行数据\n", "当前条目数据：整租·中海城南华府 2室2厅 西 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第425行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 东/南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:8800\n", "成功写入第426行数据\n", "第24页爬取完成，当前已爬取426条数据\n", "等待4.89秒后继续...\n", "正在爬取第25页...\n", "找到30个房源条目\n", "当前条目数据：整租·招商玺荟 2室1厅 东南 | 楼层:3/49 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第427行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南 | 楼层:1/20 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第428行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第429行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2800\n", "成功写入第430行数据\n", "当前条目数据：整租·银泰泰悦湾 3室2厅 东南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第431行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:9500\n", "成功写入第432行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 北 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:5500\n", "成功写入第433行数据\n", "当前条目数据：整租·泛林格兰晴天 3室1厅 南 | 楼层:1/16 | 装修:未知 | 地铁:否 | 租金:4200\n", "成功写入第434行数据\n", "当前条目数据：整租·金房大榕湾人家 1室1厅 东 | 楼层:3/4 | 装修:精装 | 地铁:是 | 租金:3100\n", "成功写入第435行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:15800\n", "成功写入第436行数据\n", "当前条目数据：整租·中海城南一号一期 5室2厅 南 | 楼层:1/24 | 装修:精装 | 地铁:否 | 租金:15800\n", "成功写入第437行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第438行数据\n", "当前条目数据：整租·银泰泰悦湾 3室2厅 东南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:7200\n", "成功写入第439行数据\n", "当前条目数据：整租·中航国际广场 1室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第440行数据\n", "当前条目数据：整租·高盛金融中心 2室2厅 南 | 楼层:3/38 | 装修:精装 | 地铁:是 | 租金:4403\n", "成功写入第441行数据\n", "当前条目数据：整租·保利锦外小户 1室0厅 西南 | 楼层:1/24 | 装修:精装 | 地铁:是 | 租金:2000\n", "成功写入第442行数据\n", "当前条目数据：整租·中海城南一号一期 3室1厅 西 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第443行数据\n", "当前条目数据：整租·中海城南一号一期 2室2厅 北 | 楼层:2/23 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第444行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:3040\n", "成功写入第445行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 西 | 楼层:1/28 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第446行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 东 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第447行数据\n", "当前条目数据：整租·南苑小区B区 4室2厅 南 | 楼层:1/16 | 装修:精装 | 地铁:是 | 租金:11000\n", "成功写入第448行数据\n", "当前条目数据：整租·南苑小区A区 4室1厅 南 | 楼层:3/16 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第449行数据\n", "当前条目数据：整租·誉峰M5行政公寓 2室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6900\n", "成功写入第450行数据\n", "当前条目数据：整租·誉峰二期 5室2厅 南/北 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:16500\n", "成功写入第451行数据\n", "当前条目数据：整租·布鲁明顿广场 3室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:4900\n", "成功写入第452行数据\n", "当前条目数据：整租·银泰中心 2室2厅 西北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:32000\n", "成功写入第453行数据\n", "当前条目数据：整租·誉峰二期 4室1厅 南 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:15000\n", "成功写入第454行数据\n", "当前条目数据：整租·誉峰三期 2室1厅 北 | 楼层:2/27 | 装修:精装 | 地铁:否 | 租金:5000\n", "成功写入第455行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2700\n", "成功写入第456行数据\n", "第25页爬取完成，当前已爬取456条数据\n", "等待2.26秒后继续...\n", "正在爬取第26页...\n", "找到30个房源条目\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 东南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第457行数据\n", "当前条目数据：整租·瑞禧公馆 1室0厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:3300\n", "成功写入第458行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2638\n", "成功写入第459行数据\n", "当前条目数据：整租·中航国际广场 1室1厅 北 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3800\n", "成功写入第460行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 北 | 楼层:2/51 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第461行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:8500\n", "成功写入第462行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第463行数据\n", "当前条目数据：整租·誉峰M5行政公寓 2室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第464行数据\n", "当前条目数据：整租·枫丹国际 2室1厅 东南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3500\n", "成功写入第465行数据\n", "当前条目数据：整租·复地金融岛 4室1厅 东南/南 | 楼层:1/44 | 装修:精装 | 地铁:是 | 租金:14800\n", "成功写入第466行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第467行数据\n", "当前条目数据：整租·誉峰M5行政公寓 2室2厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:9500\n", "成功写入第468行数据\n", "当前条目数据：整租·中海城南华府 2室1厅 西 | 楼层:1/30 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第469行数据\n", "当前条目数据：整租·润富国际 3室2厅 西 | 楼层:3/29 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第470行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第471行数据\n", "当前条目数据：整租·银泰中心 1室1厅 北 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:5500\n", "成功写入第472行数据\n", "当前条目数据：整租·高盛金融中心 2室1厅 南 | 楼层:3/38 | 装修:精装 | 地铁:是 | 租金:5800\n", "成功写入第473行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 4室2厅 南 | 楼层:1/41 | 装修:精装 | 地铁:是 | 租金:19200\n", "成功写入第474行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2900\n", "成功写入第475行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第476行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第477行数据\n", "当前条目数据：整租·首座MAX 3室2厅 南 | 楼层:2/27 | 装修:精装 | 地铁:否 | 租金:12800\n", "成功写入第478行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第479行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3500\n", "成功写入第480行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 东 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第481行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:15000\n", "成功写入第482行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/33 | 装修:未知 | 地铁:否 | 租金:2700\n", "成功写入第483行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 西 | 楼层:3/51 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第484行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室1厅 东 | 楼层:3/35 | 装修:精装 | 地铁:是 | 租金:10800\n", "成功写入第485行数据\n", "当前条目数据：整租·保利锦湖林语南区 4室2厅 东/西 | 楼层:1/22 | 装修:精装 | 地铁:否 | 租金:6200\n", "成功写入第486行数据\n", "第26页爬取完成，当前已爬取486条数据\n", "等待2.33秒后继续...\n", "正在爬取第27页...\n", "找到0个房源条目\n", "第27页爬取完成，当前已爬取486条数据\n", "等待4.40秒后继续...\n", "正在爬取第28页...\n", "找到30个房源条目\n", "当前条目数据：整租·润富国际 3室2厅 南 | 楼层:1/30 | 装修:精装 | 地铁:是 | 租金:20000\n", "成功写入第487行数据\n", "当前条目数据：整租·银泰中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:14000\n", "成功写入第488行数据\n", "当前条目数据：整租·保利锦外小户 1室0厅 南 | 楼层:1/24 | 装修:未知 | 地铁:是 | 租金:2104\n", "成功写入第489行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/27 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第490行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:1/27 | 装修:精装 | 地铁:否 | 租金:2775\n", "成功写入第491行数据\n", "当前条目数据：整租·中国华商金融中心 2室1厅 南 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:10000\n", "成功写入第492行数据\n", "当前条目数据：整租·首座MAX 3室1厅 南 | 楼层:2/27 | 装修:未知 | 地铁:否 | 租金:8200\n", "成功写入第493行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第494行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:6500\n", "成功写入第495行数据\n", "当前条目数据：整租·中航国际交流中心 2室2厅 北 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第496行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 西 | 楼层:1/24 | 装修:精装 | 地铁:否 | 租金:7500\n", "成功写入第497行数据\n", "当前条目数据：整租·中海城南一号一期 4室2厅 南 | 楼层:1/24 | 装修:精装 | 地铁:否 | 租金:15000\n", "成功写入第498行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2700\n", "成功写入第499行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:2800\n", "成功写入第500行数据\n", "当前条目数据：整租·中国华商金融中心 2室2厅 北 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:10500\n", "成功写入第501行数据\n", "当前条目数据：整租·仁和春天国际花园 4室1厅 南/北 | 楼层:3/31 | 装修:未知 | 地铁:是 | 租金:5000\n", "成功写入第502行数据\n", "当前条目数据：整租·誉峰二期 3室2厅 西北 | 楼层:1/30 | 装修:精装 | 地铁:否 | 租金:18000\n", "成功写入第503行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:3/51 | 装修:未知 | 地铁:是 | 租金:4600\n", "成功写入第504行数据\n", "当前条目数据：整租·中航国际交流中心 1室0厅 南 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第505行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第506行数据\n", "当前条目数据：整租·西派国际 4室2厅 南/北 | 楼层:3/29 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第507行数据\n", "当前条目数据：整租·银泰中心 1室1厅 西南 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:9000\n", "成功写入第508行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 西/北 | 楼层:2/52 | 装修:精装 | 地铁:是 | 租金:6500\n", "成功写入第509行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:6000\n", "成功写入第510行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南/西 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第511行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 南/北 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:6800\n", "成功写入第512行数据\n", "当前条目数据：整租·合景誉舍 1室1厅 东南 | 楼层:1/30 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第513行数据\n", "当前条目数据：整租·中航国际交流中心 2室2厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第514行数据\n", "当前条目数据：整租·天府世家 3室2厅 西南 | 楼层:1/33 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第515行数据\n", "当前条目数据：整租·誉峰二期 4室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:否 | 租金:15000\n", "成功写入第516行数据\n", "第28页爬取完成，当前已爬取516条数据\n", "等待4.81秒后继续...\n", "正在爬取第29页...\n", "找到30个房源条目\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第517行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 4室2厅 东南 | 楼层:2/41 | 装修:精装 | 地铁:是 | 租金:19800\n", "成功写入第518行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:4000\n", "成功写入第519行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2800\n", "成功写入第520行数据\n", "当前条目数据：整租·瑞禧公馆 1室0厅 东 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2900\n", "成功写入第521行数据\n", "当前条目数据：整租·高盛金融中心 2室1厅 南 | 楼层:2/34 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第522行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:6500\n", "成功写入第523行数据\n", "当前条目数据：整租·银泰中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:5500\n", "成功写入第524行数据\n", "当前条目数据：整租·中海九号公馆 3室2厅 东/西 | 楼层:2/26 | 装修:精装 | 地铁:否 | 租金:7200\n", "成功写入第525行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 北 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:3400\n", "成功写入第526行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 北 | 楼层:1/18 | 装修:精装 | 地铁:是 | 租金:11000\n", "成功写入第527行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 南 | 楼层:2/24 | 装修:精装 | 地铁:是 | 租金:2100\n", "成功写入第528行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 南/西南/西 | 楼层:1/37 | 装修:精装 | 地铁:是 | 租金:16500\n", "成功写入第529行数据\n", "当前条目数据：整租·保利锦湖林语南区 3室2厅 西 | 楼层:1/22 | 装修:精装 | 地铁:否 | 租金:4900\n", "成功写入第530行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 东南 | 楼层:3/43 | 装修:精装 | 地铁:是 | 租金:11600\n", "成功写入第531行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第532行数据\n", "当前条目数据：整租·中航国际交流中心 2室2厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:4300\n", "成功写入第533行数据\n", "当前条目数据：整租·银泰泰悦湾 2室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第534行数据\n", "当前条目数据：整租·保利锦湖林语北区 4室2厅 东 | 楼层:1/22 | 装修:精装 | 地铁:是 | 租金:5300\n", "成功写入第535行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 3室2厅 南 | 楼层:3/43 | 装修:未知 | 地铁:是 | 租金:10000\n", "成功写入第536行数据\n", "当前条目数据：整租·人居天府汇城 3室2厅 西 | 楼层:1/28 | 装修:未知 | 地铁:是 | 租金:4500\n", "成功写入第537行数据\n", "当前条目数据：整租·中航国际交流中心 2室2厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3700\n", "成功写入第538行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 北 | 楼层:2/24 | 装修:精装 | 地铁:是 | 租金:3200\n", "成功写入第539行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:1/31 | 装修:精装 | 地铁:否 | 租金:3000\n", "成功写入第540行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 东南 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:2900\n", "成功写入第541行数据\n", "当前条目数据：整租·西派国际 2室1厅 东 | 楼层:1/29 | 装修:精装 | 地铁:是 | 租金:5700\n", "成功写入第542行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东/东北 | 楼层:2/37 | 装修:精装 | 地铁:否 | 租金:28000\n", "成功写入第543行数据\n", "当前条目数据：整租·布鲁明顿广场 1室0厅 北 | 楼层:1/28 | 装修:未知 | 地铁:否 | 租金:2600\n", "成功写入第544行数据\n", "当前条目数据：整租·奥克斯广场 2室1厅 南 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:4700\n", "成功写入第545行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第546行数据\n", "第29页爬取完成，当前已爬取546条数据\n", "等待2.14秒后继续...\n", "正在爬取第30页...\n", "找到0个房源条目\n", "第30页爬取完成，当前已爬取546条数据\n", "等待4.83秒后继续...\n", "正在爬取第31页...\n", "找到30个房源条目\n", "当前条目数据：整租·复地金融岛 4室2厅 南 | 楼层:1/45 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第547行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 东 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:3200\n", "成功写入第548行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东南 | 楼层:3/49 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第549行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南 | 楼层:1/20 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第550行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 西 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第551行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 西 | 楼层:2/52 | 装修:精装 | 地铁:是 | 租金:6500\n", "成功写入第552行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 3室1厅 东/南 | 楼层:3/41 | 装修:未知 | 地铁:是 | 租金:8800\n", "成功写入第553行数据\n", "当前条目数据：整租·中海九号公馆 2室2厅 东 | 楼层:3/26 | 装修:精装 | 地铁:否 | 租金:7000\n", "成功写入第554行数据\n", "当前条目数据：整租·银泰泰悦湾 3室2厅 东南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第555行数据\n", "当前条目数据：整租·银泰中心 1室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第556行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 北 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:5500\n", "成功写入第557行数据\n", "当前条目数据：整租·泛林格兰晴天 3室1厅 南 | 楼层:1/16 | 装修:未知 | 地铁:否 | 租金:4200\n", "成功写入第558行数据\n", "当前条目数据：整租·金房大榕湾人家 1室1厅 东 | 楼层:3/4 | 装修:精装 | 地铁:是 | 租金:3100\n", "成功写入第559行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:15800\n", "成功写入第560行数据\n", "当前条目数据：整租·中海城南一号一期 6室2厅 南/北 | 楼层:2/23 | 装修:精装 | 地铁:否 | 租金:10000\n", "成功写入第561行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第562行数据\n", "当前条目数据：整租·誉峰M5行政公寓 2室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:7900\n", "成功写入第563行数据\n", "当前条目数据：整租·银泰泰悦湾 3室2厅 东南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:7200\n", "成功写入第564行数据\n", "当前条目数据：整租·中航国际广场 1室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第565行数据\n", "当前条目数据：整租·高盛金融中心 2室2厅 南 | 楼层:3/38 | 装修:精装 | 地铁:是 | 租金:4403\n", "成功写入第566行数据\n", "当前条目数据：整租·保利锦外小户 1室0厅 西南 | 楼层:1/24 | 装修:精装 | 地铁:是 | 租金:2000\n", "成功写入第567行数据\n", "当前条目数据：整租·中海城南一号一期 3室1厅 西 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第568行数据\n", "当前条目数据：整租·中海城南一号一期 2室2厅 西 | 楼层:3/23 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第569行数据\n", "当前条目数据：整租·南苑小区B区 4室2厅 南 | 楼层:1/16 | 装修:精装 | 地铁:是 | 租金:11000\n", "成功写入第570行数据\n", "当前条目数据：整租·南苑小区A区 4室1厅 南 | 楼层:3/16 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第571行数据\n", "当前条目数据：整租·誉峰M5行政公寓 2室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6900\n", "成功写入第572行数据\n", "当前条目数据：整租·南苑小区A区 3室2厅 南/北 | 楼层:3/16 | 装修:精装 | 地铁:是 | 租金:8000\n", "成功写入第573行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3300\n", "成功写入第574行数据\n", "当前条目数据：整租·成都文儒德 4室2厅 南/北 | 楼层:2/26 | 装修:精装 | 地铁:是 | 租金:18000\n", "成功写入第575行数据\n", "当前条目数据：整租·瑞禧公馆 1室0厅 南 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第576行数据\n", "第31页爬取完成，当前已爬取576条数据\n", "等待4.71秒后继续...\n", "正在爬取第32页...\n", "找到30个房源条目\n", "当前条目数据：整租·枫丹国际 2室1厅 南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:4300\n", "成功写入第577行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:2/18 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第578行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:8000\n", "成功写入第579行数据\n", "当前条目数据：整租·仁和春天国际广场 2室1厅 东/北 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:7000\n", "成功写入第580行数据\n", "当前条目数据：整租·银泰泰悦湾 4室2厅 南/北 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:11500\n", "成功写入第581行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:4800\n", "成功写入第582行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:8000\n", "成功写入第583行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 南 | 楼层:2/24 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第584行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 东 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第585行数据\n", "当前条目数据：整租·誉峰一期 4室2厅 东北 | 楼层:3/24 | 装修:精装 | 地铁:是 | 租金:17000\n", "成功写入第586行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 西南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第587行数据\n", "当前条目数据：整租·中海城南华府 2室1厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第588行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第589行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5600\n", "成功写入第590行数据\n", "当前条目数据：整租·招商玺荟 2室2厅 东南 | 楼层:2/49 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第591行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 南 | 楼层:1/24 | 装修:精装 | 地铁:是 | 租金:27000\n", "成功写入第592行数据\n", "当前条目数据：整租·金房大榕湾人家 2室2厅 东/东北 | 楼层:2/4 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第593行数据\n", "当前条目数据：整租·高盛金融中心 3室2厅 南 | 楼层:2/34 | 装修:精装 | 地铁:是 | 租金:6200\n", "成功写入第594行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 南 | 楼层:3/42 | 装修:未知 | 地铁:否 | 租金:8000\n", "成功写入第595行数据\n", "当前条目数据：整租·枫丹国际 1室1厅 东 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3600\n", "成功写入第596行数据\n", "当前条目数据：整租·复地金融岛湾流汇 2室1厅 西/北 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:6390\n", "成功写入第597行数据\n", "当前条目数据：整租·誉峰三期 1室2厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:4800\n", "成功写入第598行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东南 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:5800\n", "成功写入第599行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 南 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:7500\n", "成功写入第600行数据\n", "当前条目数据：整租·银泰中心 1室2厅 西 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第601行数据\n", "当前条目数据：整租·银泰中心 2室2厅 西 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:15000\n", "成功写入第602行数据\n", "当前条目数据：整租·仁和春天国际花园 4室2厅 西北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:14000\n", "成功写入第603行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第604行数据\n", "当前条目数据：整租·誉峰三期 3室2厅 南 | 楼层:3/27 | 装修:精装 | 地铁:否 | 租金:7200\n", "成功写入第605行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第606行数据\n", "第32页爬取完成，当前已爬取606条数据\n", "等待3.30秒后继续...\n", "正在爬取第33页...\n", "找到30个房源条目\n", "当前条目数据：整租·银泰中心 1室1厅 东北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第607行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 西南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第608行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第609行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第610行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第611行数据\n", "当前条目数据：整租·仁和春天国际花园 4室2厅 南/北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:12000\n", "成功写入第612行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 东南 | 楼层:2/28 | 装修:未知 | 地铁:是 | 租金:7000\n", "成功写入第613行数据\n", "当前条目数据：整租·天府世家 3室2厅 南 | 楼层:1/33 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第614行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:3/32 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第615行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 南 | 楼层:1/24 | 装修:精装 | 地铁:是 | 租金:2400\n", "成功写入第616行数据\n", "当前条目数据：整租·南苑小区A区 4室2厅 南/北 | 楼层:1/16 | 装修:精装 | 地铁:是 | 租金:5800\n", "成功写入第617行数据\n", "当前条目数据：整租·仁和春天国际广场 1室1厅 南 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:5950\n", "成功写入第618行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:未知 | 地铁:否 | 租金:2800\n", "成功写入第619行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 南 | 楼层:1/44 | 装修:精装 | 地铁:否 | 租金:7800\n", "成功写入第620行数据\n", "当前条目数据：整租·高盛金融中心 1室0厅 东 | 楼层:1/48 | 装修:未知 | 地铁:是 | 租金:9500\n", "成功写入第621行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 3室2厅 东 | 楼层:2/41 | 装修:未知 | 地铁:是 | 租金:32000\n", "成功写入第622行数据\n", "当前条目数据：整租·人居天府汇城 3室2厅 西 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第623行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 东南 | 楼层:2/28 | 装修:未知 | 地铁:是 | 租金:6000\n", "成功写入第624行数据\n", "当前条目数据：整租·奥克斯广场 2室1厅 西南 | 楼层:2/29 | 装修:精装 | 地铁:是 | 租金:4466\n", "成功写入第625行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 东 | 楼层:3/35 | 装修:精装 | 地铁:是 | 租金:16000\n", "成功写入第626行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:3400\n", "成功写入第627行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第628行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 北 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:2900\n", "成功写入第629行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:3000\n", "成功写入第630行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:4600\n", "成功写入第631行数据\n", "当前条目数据：整租·布鲁明顿广场 1室0厅 东 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第632行数据\n", "当前条目数据：整租·中国华商金融中心 2室1厅 北 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:8500\n", "成功写入第633行数据\n", "当前条目数据：整租·中海城南一号一期 2室2厅 跃层 南 | 楼层:2/23 | 装修:精装 | 地铁:是 | 租金:5500\n", "成功写入第634行数据\n", "当前条目数据：整租·鑫信合中心 2室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第635行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 北 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:8000\n", "成功写入第636行数据\n", "第33页爬取完成，当前已爬取636条数据\n", "等待4.29秒后继续...\n", "正在爬取第34页...\n", "找到30个房源条目\n", "当前条目数据：整租·中国华商金融中心 2室1厅 北 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:16000\n", "成功写入第637行数据\n", "当前条目数据：整租·仁和春天国际花园 3室1厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:9800\n", "成功写入第638行数据\n", "当前条目数据：整租·枫丹国际 2室1厅 东 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:3800\n", "成功写入第639行数据\n", "当前条目数据：整租·誉峰二期 4室2厅 东南 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:18000\n", "成功写入第640行数据\n", "当前条目数据：整租·瑞禧公馆 2室1厅 南 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第641行数据\n", "当前条目数据：整租·天府世家 3室1厅 南 | 楼层:1/18 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第642行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东/南 | 楼层:3/51 | 装修:精装 | 地铁:是 | 租金:12000\n", "成功写入第643行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第644行数据\n", "当前条目数据：整租·金房大榕湾人家 2室1厅 北 | 楼层:2/4 | 装修:精装 | 地铁:否 | 租金:5500\n", "成功写入第645行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 北 | 楼层:2/49 | 装修:精装 | 地铁:是 | 租金:6000\n", "成功写入第646行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第647行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:2/52 | 装修:精装 | 地铁:是 | 租金:4200\n", "成功写入第648行数据\n", "当前条目数据：整租·枫丹国际 1室1厅 东/东南/南/西南 | 楼层:2/31 | 装修:未知 | 地铁:是 | 租金:3200\n", "成功写入第649行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东南 | 楼层:2/45 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第650行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:4300\n", "成功写入第651行数据\n", "当前条目数据：整租·鑫信合中心 4室2厅 南 | 楼层:2/29 | 装修:未知 | 地铁:是 | 租金:8000\n", "成功写入第652行数据\n", "当前条目数据：整租·保利锦外小户 1室0厅 东南/南 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第653行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2700\n", "成功写入第654行数据\n", "当前条目数据：整租·誉峰三期 2室1厅 南 | 楼层:3/27 | 装修:精装 | 地铁:否 | 租金:5200\n", "成功写入第655行数据\n", "当前条目数据：整租·银泰中心 1室1厅 东北 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:5000\n", "成功写入第656行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3300\n", "成功写入第657行数据\n", "当前条目数据：整租·中国华商金融中心 2室2厅 北 | 楼层:1/42 | 装修:精装 | 地铁:否 | 租金:12500\n", "成功写入第658行数据\n", "当前条目数据：整租·招商玺荟 1室2厅 北 | 楼层:1/51 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第659行数据\n", "当前条目数据：整租·枫丹国际 2室1厅 南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:3400\n", "成功写入第660行数据\n", "当前条目数据：整租·誉峰二期 4室3厅 东南 | 楼层:1/30 | 装修:精装 | 地铁:否 | 租金:16000\n", "成功写入第661行数据\n", "当前条目数据：整租·中国华商金融中心 1室2厅 北 | 楼层:3/42 | 装修:精装 | 地铁:否 | 租金:6800\n", "成功写入第662行数据\n", "当前条目数据：整租·奥克斯广场 3室1厅 东南 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第663行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2500\n", "成功写入第664行数据\n", "当前条目数据：整租·西派国际 3室2厅 东 | 楼层:3/29 | 装修:精装 | 地铁:是 | 租金:8500\n", "成功写入第665行数据\n", "当前条目数据：整租·金房大榕湾人家 1室0厅 东 | 楼层:3/4 | 装修:精装 | 地铁:否 | 租金:3500\n", "成功写入第666行数据\n", "第34页爬取完成，当前已爬取666条数据\n", "等待3.61秒后继续...\n", "正在爬取第35页...\n", "找到30个房源条目\n", "当前条目数据：整租·誉峰三期 1室0厅 南 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2400\n", "成功写入第667行数据\n", "当前条目数据：整租·天府欣苑东区 4室1厅 南 | 楼层:3/16 | 装修:精装 | 地铁:否 | 租金:10000\n", "成功写入第668行数据\n", "当前条目数据：整租·中国华商金融中心 1室1厅 北 | 楼层:2/42 | 装修:精装 | 地铁:否 | 租金:6000\n", "成功写入第669行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:4000\n", "成功写入第670行数据\n", "当前条目数据：整租·天府欣苑西区 4室1厅 南 | 楼层:2/16 | 装修:精装 | 地铁:否 | 租金:12000\n", "成功写入第671行数据\n", "当前条目数据：整租·中航国际交流中心 2室1厅 东 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第672行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 东南 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第673行数据\n", "当前条目数据：整租·枫丹国际 2室1厅 东南 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3500\n", "成功写入第674行数据\n", "当前条目数据：整租·复地金融岛 4室1厅 东南/南 | 楼层:1/44 | 装修:精装 | 地铁:是 | 租金:14800\n", "成功写入第675行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第676行数据\n", "当前条目数据：整租·誉峰M5行政公寓 2室2厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:9500\n", "成功写入第677行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 北 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:8000\n", "成功写入第678行数据\n", "当前条目数据：整租·润富国际 3室2厅 西 | 楼层:3/29 | 装修:精装 | 地铁:是 | 租金:7800\n", "成功写入第679行数据\n", "当前条目数据：整租·高盛金融中心 2室1厅 南 | 楼层:3/38 | 装修:精装 | 地铁:是 | 租金:5800\n", "成功写入第680行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2400\n", "成功写入第681行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:1/51 | 装修:精装 | 地铁:是 | 租金:4200\n", "成功写入第682行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第683行数据\n", "当前条目数据：整租·中海城南一号二期 4室1厅 南 | 楼层:2/23 | 装修:精装 | 地铁:否 | 租金:13500\n", "成功写入第684行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 东南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3500\n", "成功写入第685行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 东 | 楼层:1/28 | 装修:精装 | 地铁:否 | 租金:2600\n", "成功写入第686行数据\n", "当前条目数据：整租·仁和春天国际广场 1室1厅 东 | 楼层:2/24 | 装修:未知 | 地铁:否 | 租金:3680\n", "成功写入第687行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:15000\n", "成功写入第688行数据\n", "当前条目数据：整租·天府世家 3室2厅 南/北 | 楼层:1/33 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第689行数据\n", "当前条目数据：整租·保利锦外小户 1室1厅 东/东南 | 楼层:3/24 | 装修:精装 | 地铁:否 | 租金:1800\n", "成功写入第690行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 西 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:4500\n", "成功写入第691行数据\n", "当前条目数据：整租·中海城南华府 2室1厅 西 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:5300\n", "成功写入第692行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 东南 | 楼层:1/52 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第693行数据\n", "当前条目数据：整租·中海城南华府 2室2厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第694行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2200\n", "成功写入第695行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 南 | 楼层:3/49 | 装修:精装 | 地铁:是 | 租金:4300\n", "成功写入第696行数据\n", "第35页爬取完成，当前已爬取696条数据\n", "等待3.99秒后继续...\n", "正在爬取第36页...\n", "找到30个房源条目\n", "当前条目数据：整租·润富国际公寓 1室1厅 东南/南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2880\n", "成功写入第697行数据\n", "当前条目数据：整租·誉峰M5行政公寓 2室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:6500\n", "成功写入第698行数据\n", "当前条目数据：整租·誉峰三期 2室1厅 南 | 楼层:3/27 | 装修:精装 | 地铁:否 | 租金:4500\n", "成功写入第699行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:3/30 | 装修:精装 | 地铁:是 | 租金:9000\n", "成功写入第700行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东南 | 楼层:2/44 | 装修:精装 | 地铁:是 | 租金:9300\n", "成功写入第701行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 西 | 楼层:3/52 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第702行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东南 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2100\n", "成功写入第703行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:4500\n", "成功写入第704行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东南 | 楼层:1/52 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第705行数据\n", "当前条目数据：整租·复地金融岛 3室2厅 东 | 楼层:1/38 | 装修:精装 | 地铁:是 | 租金:7500\n", "成功写入第706行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西南 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:3500\n", "成功写入第707行数据\n", "当前条目数据：整租·复地金融岛湾流锦宫 5室1厅 东北 | 楼层:3/41 | 装修:精装 | 地铁:是 | 租金:22000\n", "成功写入第708行数据\n", "当前条目数据：整租·银泰中心 4室2厅 东南 | 楼层:3/46 | 装修:精装 | 地铁:是 | 租金:49000\n", "成功写入第709行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西南 | 楼层:2/40 | 装修:精装 | 地铁:否 | 租金:3600\n", "成功写入第710行数据\n", "当前条目数据：整租·礼顿山1号 3室2厅 南 | 楼层:3/32 | 装修:精装 | 地铁:否 | 租金:4950\n", "成功写入第711行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 东 | 楼层:3/31 | 装修:精装 | 地铁:是 | 租金:3200\n", "成功写入第712行数据\n", "当前条目数据：整租·复地金融岛 3室1厅 西 | 楼层:2/45 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第713行数据\n", "当前条目数据：整租·招商大魔方玺悦轩 4室2厅 东 | 楼层:1/43 | 装修:精装 | 地铁:是 | 租金:12000\n", "成功写入第714行数据\n", "当前条目数据：整租·瑞禧公馆 1室0厅 东/西 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2500\n", "成功写入第715行数据\n", "当前条目数据：整租·中航国际交流中心 1室1厅 东 | 楼层:1/31 | 装修:精装 | 地铁:是 | 租金:3500\n", "成功写入第716行数据\n", "当前条目数据：整租·奥克斯广场 1室0厅 西 | 楼层:1/29 | 装修:未知 | 地铁:是 | 租金:2700\n", "成功写入第717行数据\n", "当前条目数据：整租·中海城南一号一期 2室1厅 东 | 楼层:2/23 | 装修:精装 | 地铁:是 | 租金:6600\n", "成功写入第718行数据\n", "当前条目数据：整租·中航国际交流中心 2室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:4000\n", "成功写入第719行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 西 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:2900\n", "成功写入第720行数据\n", "当前条目数据：整租·复地金融岛 4室2厅 东/南 | 楼层:2/45 | 装修:精装 | 地铁:是 | 租金:8500\n", "成功写入第721行数据\n", "当前条目数据：整租·仁和春天国际广场 2室2厅 南 | 楼层:2/24 | 装修:精装 | 地铁:否 | 租金:8800\n", "成功写入第722行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 东南 | 楼层:2/49 | 装修:精装 | 地铁:是 | 租金:6800\n", "成功写入第723行数据\n", "当前条目数据：整租·中航国际交流中心 2室2厅 西 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3600\n", "成功写入第724行数据\n", "当前条目数据：整租·誉峰二期 4室2厅 东 | 楼层:3/30 | 装修:精装 | 地铁:否 | 租金:16800\n", "成功写入第725行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 西南 | 楼层:1/31 | 装修:精装 | 地铁:否 | 租金:6200\n", "成功写入第726行数据\n", "第36页爬取完成，当前已爬取726条数据\n", "等待2.63秒后继续...\n", "正在爬取第37页...\n", "找到0个房源条目\n", "第37页爬取完成，当前已爬取726条数据\n", "等待2.65秒后继续...\n", "正在爬取第38页...\n", "找到0个房源条目\n", "第38页爬取完成，当前已爬取726条数据\n", "等待3.92秒后继续...\n", "正在爬取第39页...\n", "找到0个房源条目\n", "第39页爬取完成，当前已爬取726条数据\n", "等待3.92秒后继续...\n", "正在爬取第40页...\n", "找到30个房源条目\n", "当前条目数据：整租·南苑小区A区 4室1厅 南 | 楼层:1/16 | 装修:未知 | 地铁:是 | 租金:7500\n", "成功写入第727行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:10000\n", "成功写入第728行数据\n", "当前条目数据：整租·誉峰M5行政公寓 1室1厅 南 | 楼层:3/28 | 装修:精装 | 地铁:是 | 租金:5500\n", "成功写入第729行数据\n", "当前条目数据：整租·中海城南一号一期 2室1厅 南 | 楼层:2/23 | 装修:精装 | 地铁:否 | 租金:6500\n", "成功写入第730行数据\n", "当前条目数据：整租·奥克斯广场 2室2厅 南 | 楼层:2/30 | 装修:精装 | 地铁:否 | 租金:5000\n", "成功写入第731行数据\n", "当前条目数据：整租·西派国际 5室2厅 西 | 楼层:2/30 | 装修:精装 | 地铁:是 | 租金:22000\n", "成功写入第732行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东南 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2352\n", "成功写入第733行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2300\n", "成功写入第734行数据\n", "当前条目数据：整租·中海城南华府 3室2厅 东/西 | 楼层:2/30 | 装修:精装 | 地铁:否 | 租金:6800\n", "成功写入第735行数据\n", "当前条目数据：整租·誉峰三期 1室0厅 西 | 楼层:1/33 | 装修:精装 | 地铁:否 | 租金:2200\n", "成功写入第736行数据\n", "当前条目数据：整租·中航国际交流中心 2室1厅 南 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:3800\n", "成功写入第737行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第738行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 东 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:3200\n", "成功写入第739行数据\n", "当前条目数据：整租·复地金融岛湾流汇 1室1厅 西南 | 楼层:3/40 | 装修:精装 | 地铁:否 | 租金:3100\n", "成功写入第740行数据\n", "当前条目数据：整租·布鲁明顿广场 1室1厅 东 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2800\n", "成功写入第741行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:3/28 | 装修:精装 | 地铁:否 | 租金:2650\n", "成功写入第742行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2279\n", "成功写入第743行数据\n", "当前条目数据：整租·招商玺荟 1室1厅 西南 | 楼层:1/51 | 装修:精装 | 地铁:是 | 租金:2800\n", "成功写入第744行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2100\n", "成功写入第745行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:2350\n", "成功写入第746行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 北 | 楼层:2/32 | 装修:精装 | 地铁:是 | 租金:2550\n", "成功写入第747行数据\n", "当前条目数据：整租·润富国际公寓 1室0厅 南 | 楼层:3/33 | 装修:精装 | 地铁:否 | 租金:2100\n", "成功写入第748行数据\n", "当前条目数据：整租·润富国际公寓 1室1厅 东 | 楼层:2/33 | 装修:精装 | 地铁:否 | 租金:2152\n", "成功写入第749行数据\n", "当前条目数据：整租·仁和春天国际花园 3室2厅 西北 | 楼层:2/31 | 装修:精装 | 地铁:是 | 租金:6300\n", "成功写入第750行数据\n", "当前条目数据：整租·布鲁明顿广场 1室0厅 北 | 楼层:1/28 | 装修:未知 | 地铁:否 | 租金:2500\n", "成功写入第751行数据\n", "当前条目数据：整租·高盛金融中心 2室1厅 南 | 楼层:2/38 | 装修:精装 | 地铁:是 | 租金:4800\n", "成功写入第752行数据\n", "当前条目数据：整租·招商玺荟 2室1厅 南 | 楼层:2/51 | 装修:精装 | 地铁:是 | 租金:7000\n", "成功写入第753行数据\n", "当前条目数据：整租·誉峰三期 1室1厅 东 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:3100\n", "成功写入第754行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:否 | 租金:2740\n", "成功写入第755行数据\n", "当前条目数据：整租·瑞禧公馆 1室1厅 南 | 楼层:2/28 | 装修:精装 | 地铁:是 | 租金:3325\n", "成功写入第756行数据\n", "第40页爬取完成，当前已爬取756条数据\n", "等待4.51秒后继续...\n", "数据爬取完成，共爬取756条数据，已保存至lianjia.xls\n"]}], "source": ["# 创建Excel文件\n", "workbook = xlwt.Workbook(encoding='utf-8')\n", "worksheet = workbook.add_sheet('链家租房')\n", "headers = ['name', 'area', 'floor', 'totalFloor', 'decorate', 'nearSubway', 'rentFee']\n", "\n", "# 写入标题行\n", "for col, header in enumerate(headers):\n", "    worksheet.write(0, col, header)\n", "\n", "# 设置完整的请求头\n", "headers = {\n", "    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "    'accept-encoding': 'gzip, deflate, br, zstd',\n", "    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',\n", "    'cache-control': 'max-age=0',\n", "    'connection': 'keep-alive',\n", "    'cookie': 'SECKEY_ABVK=2yp+tGJouvoLtZBH0+9TOnzUhuXQWZ+cQ0etzp/kfEY8qSg5Yerhy6Hdit21mQfntV1dAO9yViOSqOo7w2b28Q%3D%3D; BMAP_SECKEY=2yp-tGJouvoLtZBH0-9TOnzUhuXQWZ-cQ0etzp_kfEbPgKmA78yRnvK07Ch46hSIA8vXaX0deUUXBXVqe445xuy82NWIhztBPIiAK27nClTZOBLNvavLG202EdLzBuDlJ02EXD-24zBOfSBOeBQJ6UZtcKGJkiHBoP3zUbxOGUs0bJrVpVUGby3RfVS5sCXTpKXgLWBaqCupKzm7PHqqjg; select_city=510100; lianjia_ssid=d1bdfdad-f54f-4c34-805f-e6ebd58f6641; lianjia_uuid=e96fad15-a959-4c7b-8cd7-5fd7471085b8; GUARANTEE_POPUP_SHOW=true; GUARANTEE_BANNER_SHOW=true; beikeBaseData=%7B%22parentSceneId%22:%22526805410469094401%22%7D; hip=twOo-akxFmE1k8hlqgckjCXw0GFPxH112LAyu87J1viyZ2r2oKOLaKBWbsUoLopThzWuRGIZ1lYBjV38I6p81FTOKQiYlehWADGAdbrY1psq8X94ek91kWqVtYyx08uZiMKiMz-_RQKk_u0I7lH7ks3ktH9hXIlfybjuIVr1OdnQ1w8LBeAcHuARwQ%3D%3D; srcid=eyJ0Ijoie1wiZGF0YVwiOlwiNGQ1ZGY2NTkxZGJiZjYxYmZkZmQ1OTA2OTgxNjcyNzY4ODNjZmI2OTc3ODU5MTBkMTliYzExNDYzODUxNGVhYjQxZTgyODc5NTE1ZWNhNTlkMTVjMzM2M2JiZWRlZjQwYjU0NzViOGM4YzFmMWU2YzU3MWYxMjM0YTVhNjAzY2IzNjdiZGM0YmIwZTE5Njg5MTJlNDVhN2NkYWU5ZmRmNzI3MjRlYjc4MTE4MzIwYWI0MGI1NTBjZTU4NTdiOWE2Y2M4NjEzNjAyMDMzYzJiZDcyM2IyYzdjM2VkM2I1NjkwZDViOTk4MGU0OWFmNjI0OGQwYjA1YTA4MWUyODJkNDBmZjY0NWQ3YjU3OGQyMjk4NTc4NjdmNDQzMWUxZDljMGNjNjJjYjYyNTY2ZDU0MDJjYWE2Yzc4MTE2MzM3NGJcIixcImtleV9pZFwiOlwiMVwiLFwic2lnblwiOlwiYTIwZDBjY2RcIn0iLCJyIjoiaHR0cHM6Ly9jZC5saWFuamlhLmNvbS96dWZhbmcvamlucm9uZ2NoZW5nL3BnMnJ0MjAwNjAwMDAwMDAxLyIsIm9zIjoid2ViIiwidiI6IjAuMSJ9',\n", "    'host': 'cd.lianjia.com',\n", "    'referer': 'https://hip.lianjia.com/',\n", "    'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"',\n", "    'sec-ch-ua-mobile': '?0',\n", "    'sec-ch-ua-platform': '\"Windows\"',\n", "    'sec-fetch-dest': 'document',\n", "    'sec-fetch-mode': 'navigate',\n", "    'sec-fetch-site': 'same-site',\n", "    'sec-fetch-user': '?1',\n", "    'upgrade-insecure-requests': '1',\n", "    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'\n", "}\n", "\n", "row_counter = 1  # 数据行计数器\n", "\n", "# 修改第一页的URL，确保能够正确获取数据\n", "first_page_url = 'https://cd.lianjia.com/zufang/jinrongcheng/rt200600000001/'\n", "\n", "for page in range(1, 41):\n", "    print(f'正在爬取第{page}页...')\n", "    \n", "    # 为第一页使用特殊URL，其他页面使用常规URL\n", "    if page == 1:\n", "        url = first_page_url\n", "    else:\n", "        url = f'https://cd.lianjia.com/zufang/jinrongcheng/pg{page}rt200600000001/'\n", "    \n", "    try:\n", "        # 使用requests发送请求\n", "        response = requests.get(url, headers=headers, timeout=10)\n", "        response.raise_for_status()  # 检查请求是否成功\n", "        html = response.text\n", "        soup = BeautifulSoup(html, 'html.parser')\n", "        \n", "        # 查找所有租房条目\n", "        items = soup.find_all('div', class_='content__list--item')\n", "        print(f'找到{len(items)}个房源条目')\n", "        \n", "        for item in items:\n", "            try:\n", "                # 楼盘名称\n", "                name_tag = item.find('a', class_='twoline')\n", "                name = name_tag.get_text(strip=True) if name_tag else 'N/A'\n", "    \n", "                # 面积提取\n", "                area = '0'\n", "                desc_list = item.find('p', class_='content__list--item--des')\n", "                if desc_list:\n", "                    for text in desc_list.stripped_strings:\n", "                        area_match = re.search(r'(\\d+\\.?\\d*)㎡', text)\n", "                        if area_match:\n", "                            area = area_match.group(1)\n", "                            break\n", "    \n", "                # 获取房源描述信息\n", "                desc_texts = list(desc_list.stripped_strings) if desc_list else []\n", "                \n", "                # 装修情况\n", "                decorate = '未知'\n", "                bottom_section = item.find('p', class_='content__list--item--bottom')\n", "                if bottom_section:\n", "                    decoration_tag = bottom_section.find('i', class_='content__item__tag--decoration')\n", "                    if decoration_tag:\n", "                        decorate = decoration_tag.get_text(strip=True)\n", "                \n", "                # 近地铁标识\n", "                near_subway = '否'\n", "                if bottom_section:\n", "                    subway_tag = bottom_section.find('i', class_='content__item__tag--is_subway_house')\n", "                    if subway_tag:\n", "                        near_subway = '是'\n", "                \n", "                # 楼层信息处理\n", "                floor, total_floor = '0', '0'\n", "                for text in desc_texts:\n", "                    # 尝试匹配\"中楼层（45层）\"格式\n", "                    match1 = re.search(r'(高|中|低)楼层\\s*[（(](\\d+)层[)）]', text)\n", "                    if match1:\n", "                        floor_dict = {'高': '3', '中': '2', '低': '1'}\n", "                        floor = floor_dict.get(match1.group(1), '0')\n", "                        total_floor = match1.group(2)\n", "                        break\n", "                    \n", "                    # 尝试匹配\"11/33层\"格式\n", "                    match2 = re.search(r'(\\d+)/(\\d+)层', text)\n", "                    if match2:\n", "                        floor = match2.group(1)\n", "                        total_floor = match2.group(2)\n", "                        break\n", "                    \n", "                    # 尝试匹配单纯的\"33层\"格式\n", "                    match3 = re.search(r'(\\d+)层', text)\n", "                    if match3 and not re.search(r'[高中低]楼层', text):\n", "                        total_floor = match3.group(1)\n", "                        break\n", "                \n", "                # 如果只找到总楼层，尝试从其他文本判断所在楼层\n", "                if total_floor != '0' and floor == '0':\n", "                    for text in desc_texts:\n", "                        if '高楼层' in text:\n", "                            floor = '3'\n", "                            break\n", "                        elif '中楼层' in text:\n", "                            floor = '2'\n", "                            break\n", "                        elif '低楼层' in text:\n", "                            floor = '1'\n", "                            break\n", "    \n", "                # 租金提取\n", "                rent_tag = item.find('span', class_='content__list--item-price')\n", "                rent_fee = '0'\n", "                if rent_tag:\n", "                    rent_match = re.search(r'\\d+', rent_tag.get_text(strip=True))\n", "                    if rent_match:\n", "                        rent_fee = rent_match.group()\n", "    \n", "                # 调试输出\n", "                print(f'当前条目数据：{name} | 楼层:{floor}/{total_floor} | 装修:{decorate} | 地铁:{near_subway} | 租金:{rent_fee}')\n", "                \n", "                # 写入Excel\n", "                try:\n", "                    # 确保所有数值字段都有有效值\n", "                    safe_floor = int(floor) if floor and floor.isdigit() else 0\n", "                    safe_total_floor = int(total_floor) if total_floor and total_floor.isdigit() else 0\n", "                    safe_area = float(area) if area else 0\n", "                    safe_rent = int(rent_fee) if rent_fee and rent_fee.isdigit() else 0\n", "                    \n", "                    data = [name, safe_area, safe_floor, safe_total_floor, decorate, near_subway, safe_rent]\n", "                    for col, value in enumerate(data):\n", "                        worksheet.write(row_counter, col, value)\n", "                    row_counter += 1\n", "                    print(f'成功写入第{row_counter-1}行数据')\n", "                except Exception as e:\n", "                    print(f'数据写入失败：{str(e)}')\n", "                    continue\n", "            \n", "            except Exception as e:\n", "                print(f'处理房源信息失败：{str(e)}')\n", "                continue\n", "            \n", "        # 每爬取一页保存一次，防止中途中断导致数据丢失\n", "        workbook.save('d:/Python code/Python/爬虫学习/lianjia_temp.xls')\n", "        print(f'第{page}页爬取完成，当前已爬取{row_counter-1}条数据')\n", "        \n", "        # 随机延时，模拟人类行为\n", "        sleep_time = random.uniform(2, 5)\n", "        print(f'等待{sleep_time:.2f}秒后继续...')\n", "        time.sleep(sleep_time)\n", "    \n", "    except Exception as e:\n", "        print(f'第{page}页爬取失败：{str(e)}')\n", "        # 遇到错误时等待更长时间\n", "        time.sleep(10)\n", "\n", "# 保存Excel文件\n", "workbook.save('d:/Python code/Python/爬虫学习/lianjia.xls')\n", "print(f'数据爬取完成，共爬取{row_counter-1}条数据，已保存至lianjia.xls')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2、机器学习数据准备（数据清洗、统计分析和数据变换等）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （1）读取 lianjia.xls 数据，存储到 dataframe 对象。提取['name','area','decorate','nearSubway','rentFee']共 5 列数据，用于后续机器学习"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据集形状: (756, 7)\n", "数据集列名: ['name', 'area', 'floor', 'totalFloor', 'decorate', 'nearSubway', 'rentFee']\n", "\n", "提取后的数据形状: (756, 5)\n", "提取后的数据列名: ['name', 'area', 'decorate', 'nearSubway', 'rentFee']\n", "\n", "提取后的数据前5行:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>area</th>\n", "      <th>decorate</th>\n", "      <th>nearSubway</th>\n", "      <th>rentFee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>未知</td>\n", "      <td>否</td>\n", "      <td>2500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>整租·誉峰三期 1室1厅 南</td>\n", "      <td>41.56</td>\n", "      <td>精装</td>\n", "      <td>是</td>\n", "      <td>2850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>整租·誉峰M5行政公寓 1室1厅 南</td>\n", "      <td>76.88</td>\n", "      <td>精装</td>\n", "      <td>是</td>\n", "      <td>5500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>整租·润富国际公寓 1室1厅 北</td>\n", "      <td>51.61</td>\n", "      <td>精装</td>\n", "      <td>否</td>\n", "      <td>3100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>119.00</td>\n", "      <td>精装</td>\n", "      <td>是</td>\n", "      <td>7689</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name    area decorate nearSubway  rentFee\n", "0                 NaN    0.00       未知          否     2500\n", "1      整租·誉峰三期 1室1厅 南   41.56       精装          是     2850\n", "2  整租·誉峰M5行政公寓 1室1厅 南   76.88       精装          是     5500\n", "3    整租·润富国际公寓 1室1厅 北   51.61       精装          否     3100\n", "4                 NaN  119.00       精装          是     7689"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取lianjia.xls文件\n", "df = pd.read_excel('lianjia.xls')\n", "\n", "# 查看数据基本信息\n", "print(\"数据集形状:\", df.shape)\n", "print(\"数据集列名:\", df.columns.tolist())\n", "\n", "# 提取指定的5列数据\n", "selected_columns = ['name', 'area', 'decorate', 'nearSubway', 'rentFee']\n", "df_selected = df[selected_columns]\n", "\n", "# 查看提取后的数据信息\n", "print(\"\\n提取后的数据形状:\", df_selected.shape)\n", "print(\"提取后的数据列名:\", df_selected.columns.tolist())\n", "\n", "# 查看提取后的数据前5行\n", "print(\"\\n提取后的数据前5行:\")\n", "df_selected.head()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据基本统计信息:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area</th>\n", "      <th>rentFee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>756.000000</td>\n", "      <td>756.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>101.182937</td>\n", "      <td>6910.424603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>61.993534</td>\n", "      <td>6064.473745</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>1390.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>51.480000</td>\n", "      <td>3000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>86.990000</td>\n", "      <td>5089.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>134.865000</td>\n", "      <td>8000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>388.740000</td>\n", "      <td>50000.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             area       rentFee\n", "count  756.000000    756.000000\n", "mean   101.182937   6910.424603\n", "std     61.993534   6064.473745\n", "min      0.000000   1390.000000\n", "25%     51.480000   3000.000000\n", "50%     86.990000   5089.000000\n", "75%    134.865000   8000.000000\n", "max    388.740000  50000.000000"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# 数据基本统计信息\n", "print(\"\\n数据基本统计信息:\")\n", "df_selected.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 处理缺失值，将name为NaN的行删除"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理前name列缺失值数量: 21\n", "处理后的数据形状: (735, 5)\n", "处理后name列缺失值数量: 0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>area</th>\n", "      <th>decorate</th>\n", "      <th>nearSubway</th>\n", "      <th>rentFee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>整租·誉峰三期 1室1厅 南</td>\n", "      <td>41.56</td>\n", "      <td>精装</td>\n", "      <td>是</td>\n", "      <td>2850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>整租·誉峰M5行政公寓 1室1厅 南</td>\n", "      <td>76.88</td>\n", "      <td>精装</td>\n", "      <td>是</td>\n", "      <td>5500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>整租·润富国际公寓 1室1厅 北</td>\n", "      <td>51.61</td>\n", "      <td>精装</td>\n", "      <td>否</td>\n", "      <td>3100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>整租·誉峰三期 1室1厅 东</td>\n", "      <td>39.78</td>\n", "      <td>精装</td>\n", "      <td>否</td>\n", "      <td>2700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>整租·润富国际公寓 1室1厅 西</td>\n", "      <td>39.14</td>\n", "      <td>精装</td>\n", "      <td>否</td>\n", "      <td>2400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name   area decorate nearSubway  rentFee\n", "1      整租·誉峰三期 1室1厅 南  41.56       精装          是     2850\n", "2  整租·誉峰M5行政公寓 1室1厅 南  76.88       精装          是     5500\n", "3    整租·润富国际公寓 1室1厅 北  51.61       精装          否     3100\n", "5      整租·誉峰三期 1室1厅 东  39.78       精装          否     2700\n", "7    整租·润富国际公寓 1室1厅 西  39.14       精装          否     2400"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 检查name列的缺失值数量\n", "print(\"处理前name列缺失值数量:\", df_selected['name'].isna().sum())\n", "\n", "# 在已提取的df_selected基础上处理缺失值\n", "df_selected = df_selected.dropna(subset=['name'])\n", "\n", "# 查看处理后的数据信息（可选验证）\n", "print(\"处理后的数据形状:\", df_selected.shape)\n", "print(\"处理后name列缺失值数量:\", df_selected['name'].isna().sum())\n", "df_selected.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （2）房屋面积 area 是回归分析的核心参数，不能有缺失值，过滤 area 列缺失值"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理前area列缺失值数量: 0\n", "回归分析核心参数清洗后数据形状: (735, 5)\n", "剩余样本量: 735\n", "处理后area列缺失值数量: 0\n"]}], "source": ["print(\"处理前area列缺失值数量:\", df_selected['area'].isna().sum())\n", "\n", "# 新增area列缺失值过滤（增量修改）\n", "df_selected = df_selected[df_selected['area'] != 0.0]\n", "\n", "# 验证核心参数完整性\n", "print(\"回归分析核心参数清洗后数据形状:\", df_selected.shape)\n", "print(\"剩余样本量:\", len(df_selected))\n", "print(\"处理后area列缺失值数量:\", df_selected['area'].isna().sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （3）查看'decorate'和'nearSubway'缺失值，并将: 'decorate'缺失值填充为'非精装','nearSubway'缺失值填充为'否'\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "替换前统计:\n", "装修-未知: 55\n", "地铁-未知: 0\n", "\n", "替换后统计:\n", "装修-非精装: 55\n", "地铁-否: 301\n"]}], "source": ["# 查看缺失值情况\n", "print(\"\\n替换前统计:\")\n", "print(\"装修-未知:\", (df_selected['decorate'] == '未知').sum())\n", "print(\"地铁-未知:\", (df_selected['nearSubway'] == '未知').sum())\n", "\n", "# 将\"未知\"替换为指定值\n", "df_selected['decorate'] = df_selected['decorate'].replace('未知', '非精装')\n", "df_selected['nearSubway'] = df_selected['nearSubway'].replace('未知', '否')\n", "\n", "# 验证替换结果\n", "print(\"\\n替换后统计:\")\n", "print(\"装修-非精装:\", (df_selected['decorate'] == '非精装').sum())\n", "print(\"地铁-否:\", (df_selected['nearSubway'] == '否').sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### (4)查看’name’列楼盘信息，提取特定楼盘数据共后续使用（提取’ 誉峰三期’数据）"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "誉峰三期房源数量: 115\n", "提取数据样例:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>area</th>\n", "      <th>decorate</th>\n", "      <th>nearSubway</th>\n", "      <th>rentFee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>整租·誉峰三期 1室1厅 南</td>\n", "      <td>41.56</td>\n", "      <td>精装</td>\n", "      <td>是</td>\n", "      <td>2850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>整租·誉峰三期 1室1厅 东</td>\n", "      <td>39.78</td>\n", "      <td>精装</td>\n", "      <td>否</td>\n", "      <td>2700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>整租·誉峰三期 1室1厅 南</td>\n", "      <td>38.16</td>\n", "      <td>非精装</td>\n", "      <td>否</td>\n", "      <td>2800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>整租·誉峰三期 1室1厅 东</td>\n", "      <td>40.13</td>\n", "      <td>精装</td>\n", "      <td>否</td>\n", "      <td>2300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>整租·誉峰三期 1室0厅 东</td>\n", "      <td>42.85</td>\n", "      <td>精装</td>\n", "      <td>是</td>\n", "      <td>2200</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              name   area decorate nearSubway  rentFee\n", "1   整租·誉峰三期 1室1厅 南  41.56       精装          是     2850\n", "5   整租·誉峰三期 1室1厅 东  39.78       精装          否     2700\n", "14  整租·誉峰三期 1室1厅 南  38.16      非精装          否     2800\n", "15  整租·誉峰三期 1室1厅 东  40.13       精装          否     2300\n", "17  整租·誉峰三期 1室0厅 东  42.85       精装          是     2200"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 提取特定楼盘数据\n", "yufeng_data = df_selected[df_selected['name'].str.contains(r'誉峰三期', case=False, regex=True)]\n", "\n", "# 验证提取结果\n", "print(\"\\n誉峰三期房源数量:\", len(yufeng_data))\n", "print(\"提取数据样例:\")\n", "yufeng_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （5）特征编码：'decorate'列，'非精装'编码为 0,'精装'编码为 1；'decorate'列，'否'编码为 0,'是'编码为 1\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "特征编码统计：\n", "装修情况分布：\n", "decorate\n", "1    680\n", "0     55\n", "Name: count, dtype: int64\n", "\n", "地铁情况分布：\n", "nearSubway\n", "1    434\n", "0    301\n", "Name: count, dtype: int64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23612\\1734961291.py:2: FutureWarning: Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df_selected['decorate'] = df_selected['decorate'].replace({'非精装':0, '精装':1})\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23612\\1734961291.py:3: FutureWarning: Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df_selected['nearSubway'] = df_selected['nearSubway'].replace({'否':0, '是':1})\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>area</th>\n", "      <th>decorate</th>\n", "      <th>nearSubway</th>\n", "      <th>rentFee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>整租·誉峰三期 1室1厅 南</td>\n", "      <td>41.56</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>整租·誉峰M5行政公寓 1室1厅 南</td>\n", "      <td>76.88</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>整租·润富国际公寓 1室1厅 北</td>\n", "      <td>51.61</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>整租·誉峰三期 1室1厅 东</td>\n", "      <td>39.78</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>整租·润富国际公寓 1室1厅 西</td>\n", "      <td>39.14</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name   area  decorate  nearSubway  rentFee\n", "1      整租·誉峰三期 1室1厅 南  41.56         1           1     2850\n", "2  整租·誉峰M5行政公寓 1室1厅 南  76.88         1           1     5500\n", "3    整租·润富国际公寓 1室1厅 北  51.61         1           0     3100\n", "5      整租·誉峰三期 1室1厅 东  39.78         1           0     2700\n", "7    整租·润富国际公寓 1室1厅 西  39.14         1           0     2400"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 特征编码\n", "df_selected['decorate'] = df_selected['decorate'].replace({'非精装':0, '精装':1})\n", "df_selected['nearSubway'] = df_selected['nearSubway'].replace({'否':0, '是':1})\n", "\n", "# 验证编码结果\n", "print(\"\\n特征编码统计：\")\n", "print(\"装修情况分布：\")\n", "print(df_selected['decorate'].value_counts())\n", "print(\"\\n地铁情况分布：\")\n", "print(df_selected['nearSubway'].value_counts())\n", "df_selected.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （6）'rentFee'列除以 1000，单位为千元"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "租金单位转换后数据样例：\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>rentFee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>整租·誉峰三期 1室1厅 南</td>\n", "      <td>2.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>整租·誉峰M5行政公寓 1室1厅 南</td>\n", "      <td>5.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>整租·润富国际公寓 1室1厅 北</td>\n", "      <td>3.10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name  rentFee\n", "1      整租·誉峰三期 1室1厅 南     2.85\n", "2  整租·誉峰M5行政公寓 1室1厅 南     5.50\n", "3    整租·润富国际公寓 1室1厅 北     3.10"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 租金数值缩放\n", "df_selected['rentFee'] = df_selected['rentFee'] / 1000  # 转换为千元单位\n", "\n", "# 验证缩放结果\n", "print(\"\\n租金单位转换后数据样例：\")\n", "df_selected[['name', 'rentFee']].head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 单变量回归分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 构建数据集：以'area'列数据为特征 ,'rentFee'列为标注信息，进行线性回归分析，分别计算并打印训练和测试误差"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "训练集 MSE: 6.90  R²: 0.77\n", "测试集 MSE: 12.84  R²: 0.76\n", "\n", "测试集前3个样本预测对比：\n", "真实值: [11.5  5.8  5.8]\n", "预测值: [14.51  6.26  5.53]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>rentFee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>整租·誉峰三期 1室1厅 南</td>\n", "      <td>2.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>整租·誉峰M5行政公寓 1室1厅 南</td>\n", "      <td>5.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>整租·润富国际公寓 1室1厅 北</td>\n", "      <td>3.10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name  rentFee\n", "1      整租·誉峰三期 1室1厅 南     2.85\n", "2  整租·誉峰M5行政公寓 1室1厅 南     5.50\n", "3    整租·润富国际公寓 1室1厅 北     3.10"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 划分数据集\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "\n", "X = df_selected[['area']]  # 特征矩阵\n", "y = df_selected['rentFee']  # 目标变量\n", "\n", "# 按7:3划分训练测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)\n", "\n", "# 创建并训练模型\n", "model = LinearRegression()\n", "model.fit(X_train, y_train)\n", "\n", "# 预测结果\n", "train_pred = model.predict(X_train)\n", "test_pred = model.predict(X_test)\n", "\n", "# 计算误差指标\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "train_mse = mean_squared_error(y_train, train_pred)\n", "test_mse = mean_squared_error(y_test, test_pred)\n", "train_r2 = r2_score(y_train, train_pred)\n", "test_r2 = r2_score(y_test, test_pred)\n", "\n", "# 打印结果（保留两位小数）\n", "print(f\"\\n训练集 MSE: {train_mse:.2f}  R²: {train_r2:.2f}\")\n", "print(f\"测试集 MSE: {test_mse:.2f}  R²: {test_r2:.2f}\")\n", "\n", "# 查看前3个样本预测对比\n", "print(\"\\n测试集前3个样本预测对比：\")\n", "print(\"真实值:\", y_test.head(3).values)\n", "print(\"预测值:\", test_pred[:3].round(2))\n", "\n", "df_selected[['name', 'rentFee']].head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4 多变量回归分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 构建数据集：以['area', 'decorate', 'nearSubway']共 3 列数据为特征 ,'rentFee'列为标注信息，特征数据经归一化处理后，进行线性回归分析，分别计算并打印训练和测试误差"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "归一化后特征样例：\n", "[[ 0.49805974]\n", " [-0.92300115]\n", " [ 0.53661952]]\n", "\n", "模型系数：[0.08] (area, decorate, nearSubway)\n", "截距项：-1.31\n"]}], "source": ["# 修改特征矩阵\n", "X = df_selected[['area', 'decorate', 'nearSubway']]  # 更新为3个特征\n", "\n", "# 新增特征归一化\n", "from sklearn.preprocessing import StandardScaler\n", "scaler = StandardScaler()\n", "X_train = scaler.fit_transform(X_train)  # 训练集归一化\n", "X_test = scaler.transform(X_test)        # 测试集归一化\n", "\n", "# ... 原有模型训练代码保持不变 ...\n", "\n", "# 新增归一化后数据样例展示\n", "print(\"\\n归一化后特征样例：\")\n", "print(X_train[:3])  # 展示前3个样本的归一化后特征值\n", "\n", "# ... 原有误差计算和打印代码保持不变 ...\n", "\n", "# 新增模型参数展示\n", "print(f\"\\n模型系数：{model.coef_.round(2)} (area, decorate, nearSubway)\")\n", "print(f\"截距项：{model.intercept_.round(2)}\")"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}