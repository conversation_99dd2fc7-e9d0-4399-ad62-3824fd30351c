<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="7d04489ff4ee4c9cbd244a7c12b964d5" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_7d04489ff4ee4c9cbd244a7c12b964d5 = echarts.init(
            document.getElementById('7d04489ff4ee4c9cbd244a7c12b964d5'), 'light', {renderer: 'canvas'});
        var option_7d04489ff4ee4c9cbd244a7c12b964d5 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u9500\u552e\u989d",
            "legendHoverLink": true,
            "data": [
                59242,
                58479,
                52336,
                35696,
                58860,
                41759,
                41773,
                44128,
                49503,
                54727,
                47016,
                53135,
                53255,
                63884,
                49894,
                44303,
                55537,
                50421,
                50566,
                53065,
                50314,
                52801,
                49948,
                47443,
                44106,
                57587,
                44043,
                44722,
                47773,
                40372,
                45670,
                50241,
                94426,
                102784,
                53034,
                61597,
                52711,
                42777,
                49396,
                46977,
                46224,
                50699,
                51962,
                52717,
                49266,
                44201,
                53097,
                60878,
                51653,
                47383,
                46295,
                54194,
                41492,
                52743,
                55043,
                49319,
                53441,
                51796,
                48385
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9500\u552e\u989d"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2011-01-01",
                "2011-01-02",
                "2011-01-03",
                "2011-01-04",
                "2011-01-05",
                "2011-01-06",
                "2011-01-07",
                "2011-01-08",
                "2011-01-09",
                "2011-01-10",
                "2011-01-11",
                "2011-01-12",
                "2011-01-13",
                "2011-01-14",
                "2011-01-15",
                "2011-01-16",
                "2011-01-17",
                "2011-01-18",
                "2011-01-19",
                "2011-01-20",
                "2011-01-21",
                "2011-01-22",
                "2011-01-23",
                "2011-01-24",
                "2011-01-25",
                "2011-01-26",
                "2011-01-27",
                "2011-01-28",
                "2011-01-29",
                "2011-01-30",
                "2011-01-31",
                "2011-02-01",
                "2011-02-02",
                "2011-02-03",
                "2011-02-04",
                "2011-02-05",
                "2011-02-06",
                "2011-02-07",
                "2011-02-08",
                "2011-02-09",
                "2011-02-10",
                "2011-02-11",
                "2011-02-12",
                "2011-02-13",
                "2011-02-14",
                "2011-02-15",
                "2011-02-16",
                "2011-02-17",
                "2011-02-18",
                "2011-02-19",
                "2011-02-20",
                "2011-02-21",
                "2011-02-22",
                "2011-02-23",
                "2011-02-24",
                "2011-02-25",
                "2011-02-26",
                "2011-02-27",
                "2011-02-28"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "2011\u5e741\u6708\u548c2\u6708\u9500\u552e\u989d\u7edf\u8ba1",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_7d04489ff4ee4c9cbd244a7c12b964d5.setOption(option_7d04489ff4ee4c9cbd244a7c12b964d5);
    </script>
</body>
</html>
