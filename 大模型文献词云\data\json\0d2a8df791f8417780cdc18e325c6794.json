[{"ArticleId": 82555469, "Title": "Face image retrieval via sum and difference histograms of elliptical local ternary pattern", "Abstract": "<p>This paper presents a new feature extraction method called sum and difference histograms of elliptical local ternary pattern (SDH-ELTP) for face image retrieval. This technique first calculates sparse local ternary pattern (LTP) in an elliptical shaped neighborhood and then higher order statistical texture information is extracted via sum and difference histograms (SDH) of elliptical LTP features. In sparse elliptical LTP, a 4 point LTP from horizontal and vertical elliptical neighborhoods and 4 point LTP from simple diagonal neighborhood is considered. The SDH is calculated only in relevant directions. Since the sum and difference histogram provides higher order statistical information, the calculation of SDH of elliptical LTP features further enhances the discriminativeness of proposed descriptor. The SDH-ELTP is finally tested on two popular face image databases and the results are compared with several recent state of the art techniques. The SDH-ELTP is low dimensional and show the best retrieval results as compared to all other face image retrieval techniques.</p>", "Keywords": "Face image retrieval; Elliptical local ternary pattern; Sum and difference histogram; Feature descriptor; Texture; Sparse", "DOI": "10.1007/s40012-020-00292-6", "PubYear": 2020, "Volume": "8", "Issue": "2", "JournalId": 1271, "JournalTitle": "CSI Transactions on ICT", "ISSN": "2277-9078", "EISSN": "2277-9086", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, School of Engineering, Tezpur University (A Central University), Napaam, Tezpur, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of ECE, School of Engineering, Tezpur University (A Central University), Napaam, Tezpur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, School of Engineering, Tezpur University (A Central University), Napaam, Tezpur, India"}], "References": [{"Title": "An effective texture descriptor for retrieval of biomedical and face images based on co-occurrence of similar center-symmetric local binary edges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "6", "Page": "589", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": 82555726, "Title": "Child maltreatment data in the state of New Mexico across space and time", "Abstract": "<p>Child maltreatment is a serious public health problem. Previous research demonstrates that child maltreatment clusters in low-income, racially homogenous neighborhoods. Little is known, however, about the structural correlates of spatial risk in small areas such as census tracts. Here we present additional information regarding the data and methods used in the recent article published in <i>Child Abuse &amp; Neglect</i> entitled &quot;Variability and stability in child maltreatment risk across time and space and its association with neighborhood social &amp; housing vulnerability in New Mexico: A Bayesian space-time model&quot; [1]. The present dataset merges child maltreatment data from the New Mexico Department of Public Health with multiple sources of publicly available data to create a novel public health analysis. Bayesian spatio-temporal modeling techniques were used to map the relative risk of substantiated child maltreatment across census tracts in the state, and to elucidate spatial and temporal heterogeneity in risk. The data was initially collected by the New Mexico Children, Youth and Families Department, the state organization that suspected child abuse and neglect cases are reported to and the organization that then substantiates these cases. The data were then sent to the New Mexico Community Data Collaborative, a data analytic organization under the umbrella of the New Mexico Department of Health. The point file consisting of home addresses of substantiated cases of child abuse was then aggregated by census tract, mapped for the entire state of New Mexico and made available to the public for research and analysis by different public health organizations and researchers (including the present researchers). The very purpose of making the data available to the public was to allow deeper investigations into trends and associations with other social determinants of health. This analysis demonstrates the public health importance of data sharing and accessibility.</p><p>© 2020 Published by Elsevier Inc.</p>", "Keywords": "Child abuse and neglect ; Child maltreatment ; New Mexico ; Public health ; Social determinants of health, Housing and food insecurity ; Bayesian spatiotemporal model", "DOI": "10.1016/j.dib.2020.105759", "PubYear": 2020, "Volume": "31", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Gia Barboza", "Affiliation": "University of Colorado Springs, Criminal Justice, 1420 Austin Bluffs Pkwy, Colorado Springs, CO 80918, United States."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "New Mexico Community Data Collaborative 12401 Los Arboles Ave NE, Albuquerque, NM 87112, United States."}], "References": []}, {"ArticleId": 82555757, "Title": "A General Non-hydrostatic Hyperbolic Formulation for Boussinesq Dispersive Shallow Flows and Its Numerical Approximation", "Abstract": "<p>In this paper, we propose a novel first-order reformulation of the most well-known Boussinesq-type systems that are used in ocean engineering. This has the advantage of collecting in a general framework many of the well-known systems used for dispersive flows. Moreover, it avoids the use of high-order derivatives which are not easy to treat numerically, due to the large stencil usually needed. These first-order PDE dispersive systems are then approximated by a novel set of first-order hyperbolic equations. Our new hyperbolic approximation is based on a relaxed augmented system in which the divergence constraints of the velocity flow variables are coupled with the other conservation laws via an evolution equation for the depth-averaged non-hydrostatic pressures. The most important advantage of this new hyperbolic formulation is that it can be easily discretized with explicit and high-order accurate numerical schemes for hyperbolic conservation laws. There is no longer need of solving implicitly some linear system as it is usually done in many classical approaches of Boussinesq-type models. Here a third-order finite volume scheme based on a CWENO reconstruction has been used. The scheme is well-balanced and can treat correctly wet–dry areas and emerging topographies. Several numerical tests, which include idealized academic benchmarks and laboratory experiments are proposed, showing the advantage, efficiency and accuracy of the technique proposed here.</p>", "Keywords": "Non-hydrostatic shallow water flows; Boussinesq-type systems; Hyperbolic reformulation; Breaking waves; Path-conservative finite volume methods; Well-balanced schemes", "DOI": "10.1007/s10915-020-01244-7", "PubYear": 2020, "Volume": "83", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Matemática Aplicada, Universidad de Málaga, Málaga, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Matemáticas, Universidad de Córdoba, Córdoba, Spain"}], "References": []}, {"ArticleId": 82555879, "Title": "The origins of Objective-C at PPI/Stepstone and its evolution at NeXT", "Abstract": "The roots of Objective-C began at ITT in the early 1980s in a research group led by <PERSON> investigating improving programmer productivity by an order of magnitude, a concern motivated by the perceived \"software crisis\" articulated in the late 1960s. In 1981, <PERSON>, a member of this group, began to investigate Smalltalk and object-oriented programming for this purpose, but needed a language compatible with the Unix and C environments used by ITT. That year, <PERSON> quickly wrote up the Object-Oriented Pre-Compiler (OOPC) that would translate a Smalltalk-like syntax into C<PERSON> felt there was a market for object-oriented solutions that could coexist with legacy languages and platforms, and after a brief stint at Schlumberger-Doll, co-founded with Cox Productivity Products International (PPI), later renamed as Stepstone, to pursue this. At PPI, <PERSON> developed OOPC into Objective-C. <PERSON> saw Objective-C as a crucial link in his larger vision of creating a market for \"pre-fabricated\" software components (\"software-ICs\"), which could be bought off the shelf and which, <PERSON> believed, would unleash a \"software industrial revolution.\"\n <PERSON> joined Stepstone in 1986 as <PERSON>' NeXT Computer became an important customer for Objective-C, as it was being used in its NeXTSTEP operating system. <PERSON><PERSON><PERSON> became the primary Stepstone developer addressing NeXT's issues with Objective-C, solving a key fragility problem preventing NeXT from deploying forwards-compatible object libraries. Impressed with NeXT, <PERSON><PERSON><PERSON> left Stepstone for NeXT in 1988, and once there, added Objective-C support to <PERSON>allman's GNU GCC compiler, which NeXT was using as its C compiler, removing the need to use Stepstone's ObjC to C translator. Over the next several years, Naroff and others would add significant new features to Objective-C, such as \"categories,\" \"protocols,\" and the ability to mix in C++ code. When Stepstone folded in 1994, all rights to Objective-C were acquired by NeXT. This eventually transferred to Apple when NeXT was acquired by Apple in 1997. Objective-C became the basis for Apple's Mac OS X and then iOS platforms, and Naroff and others at Apple added additional features to the language in the late 2000s as the iPhone App Store greatly expanded Objective-C's user base.", "Keywords": "Apple; C++; ITT; NeXT; OOPC; Objective-C; PPI; Smalltalk; Stepstone; categories; dynamic binding; message passing; protocols; software crisis; software-ICs", "DOI": "10.1145/3386332", "PubYear": 2020, "Volume": "4", "Issue": "HOPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "n.n., n.n."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "n.n., n.n."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer History Museum, USA"}], "References": []}, {"ArticleId": 82555961, "Title": "Data on differentially expressed proteins in rock inhibitor-treated human trabecular meshwork cells using SWATH-based proteomics", "Abstract": "Rho-associated coiled coil-forming protein kinase (ROCK) inhibitors represent a novel class of anti-glaucoma drugs because of their ocular hypotensive effects. However, the underlying mechanisms responsible for lowering intraocular pressure (IOP) are not completely clear. The protein profile changes in primary human trabecular meshwork (TM) cells after two days treatment with a ROCK inhibitor were studied using label-free SWATH acquisition. These results provided significant data of key protein candidates underlying the effect of ROCK inhibitor. Using the sensitive label-free mass spectrometry approach with data-independent acquisition (SWATH-MS), we established a comprehensive TM proteome library. All raw data generated from IDA and SWATH acquisitions were uploaded and published in the Peptide Atlas public repository ( http://www.peptideatlas.org/ ) for general release (Data ID PASS01254).", "Keywords": "Rock inhibitor;Swath;Trabecular meshwork;glaucoma", "DOI": "10.1016/j.dib.2020.105846", "PubYear": 2020, "Volume": "31", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Experimental Optometry, Centre for Myopia Research, School of Optometry, the Hong Kong Polytechnic University, Kowloon, Hong Kong, China"}, {"AuthorId": 2, "Name": "Chi-Wai Do", "Affiliation": "Laboratory of Experimental Optometry, Centre for Myopia Research, School of Optometry, the Hong Kong Polytechnic University, Kowloon, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory of Experimental Optometry, Centre for Myopia Research, School of Optometry, the Hong Kong Polytechnic University, Kowloon, Hong Kong, China;The Hong Kong Polytechnic University Shenzhen Research Institute, Shenzhen, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Experimental Optometry, Centre for Myopia Research, School of Optometry, the Hong Kong Polytechnic University, Kowloon, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ophthalmology, Duke University, Durham, NC, United States;Department of Biomedical Engineering, Duke University,Durham, NC, United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Experimental Optometry, Centre for Myopia Research, School of Optometry, the Hong Kong Polytechnic University, Kowloon, Hong Kong, China"}], "References": [{"Title": "Data on assessment of safety and tear proteome change in response to orthokeratology lens – Insight from integrating clinical data and next generation proteomics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "", "Page": "105186", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 82555973, "Title": "Total Bregman divergence-based fuzzy local information C-means clustering for robust image segmentation", "Abstract": "The fuzzy local information C-means clustering algorithm (FLICM) is an important robust fuzzy clustering segmentation method, which has attracted considerable attention over the years. However, it lacks certain robustness to high noise or severe outliers. To improve the accuracy and robustness of the FLICM algorithm for images corrupted by high noise, a novel fuzzy local information c-means clustering utilizing total Bregman divergence (TFLICM) is proposed in this paper. The total Bregman divergence is modified by the local neighborhood information of sample to further enhance the ability to suppress noise, and then modified total Bregman divergence is introduced into the FLICM to construct a new objective function of robust fuzzy clustering, and the iterative clustering algorithm with high robustness is obtained through optimization theory. The convergence of the TFLICM algorithm is proved by the <PERSON><PERSON><PERSON> theorem. In addition, the validity of the TFLICM algorithm applied in noise image segmentation is explained by means of sample weighting fuzzy clustering. Meanwhile, the generalized total Bregman divergence unifies the Bregman divergence with the total Bregman divergence and enhances the universality of the TFLICM algorithm applied in segmenting complex medical and remote sensing images. Some experimental results show that the TFLICM algorithm can obtain better segmentation quality and stronger anti-noise robustness than the existing FLICM algorithm.", "Keywords": "Image segmentation ; Fuzzy clustering ; Total Bregman divergence ; Fuzzy local information C-means clustering ; Sample weighting", "DOI": "10.1016/j.asoc.2020.106468", "PubYear": 2020, "Volume": "94", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xi’an University of Posts & Telecommunications, 710121 Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xi’an University of Posts & Telecommunications, 710121 Xi’an, China;Corresponding author"}], "References": [{"Title": "Fuzzy C-Means clustering through SSIM and patch for image segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105928", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 82556059, "Title": "Quantitative reductions and vertex-ranked infinite games", "Abstract": "We introduce quantitative reductions, a novel technique for structuring the space of quantitative games and solving them that does not rely on a reduction to qualitative games. We show that such reductions exhibit the same desirable properties as qualitative ones and that they additionally retain the optimality of solutions. Moreover, we introduce vertex-ranked games as general-purpose targets for quantitative reductions and show how to solve them. In such games, the value of a play is determined only by a qualitative winning condition and a vertex-ranking. We provide quantitative reductions of quantitative request-response games and of quantitative Muller games to vertex-ranked games, thus showing ExpTime -completeness of solving the former two kinds of games. In addition, we exhibit the usefulness and flexibility of vertex-ranked games by using them to compute fault-resilient strategies for safety specifications. This lays the foundation for a general study of fault-resilient strategies for more complex winning conditions.", "Keywords": "", "DOI": "10.1016/j.ic.2020.104596", "PubYear": 2021, "Volume": "278", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "German Aerospace Center (DLR), Institute for Software Technology, Linder Höhe, 51147 Köln, Germany"}], "References": []}, {"ArticleId": 82556072, "Title": "Scalable and robust unsupervised Android malware fingerprinting using community-based network partitioning", "Abstract": "The daily amount of Android malicious applications (apps) targeting the app repositories is increasing, and their number is overwhelming the process of fingerprinting. To address this issue, we propose an enhanced Cypider framework, a set of techniques and tools aiming to perform a systematic detection of mobile malware by building a scalable and obfuscation resilient similarity network infrastructure of malicious apps. Our approach is based on our proposed concept, namely malicious community , in which we consider malicious instances that share common features are the most likely part of the same malware family. Using this concept, we presumably assume that multiple similar Android apps with different authors are most likely to be malicious. Specifically, <PERSON><PERSON><PERSON> leverages this assumption for the detection of variants of known malware families and zero-day malicious apps. <PERSON><PERSON><PERSON> applies community detection algorithms on the similarity network, which extracts sub-graphs considered as suspicious and possibly malicious communities. Furthermore, we propose a novel fingerprinting technique, namely community fingerprint , based on a one-class machine learning model for each malicious community. Besides, we proposed an enhanced Cypider framework, which requires less memory, ≈ x 650%, and less time to build the similarity network, ≈ x 700, compared to the original version, without affecting the fingerprinting performance of the framework. We introduce a systematic approach to locate the best threshold on different feature content vectors, which simplifies the overall detection process. <PERSON><PERSON><PERSON> shows excellent results by detecting 60 − 80 % coverage of the malware dataset in one detection iteration with higher precision 85 − 99 % in the detected malicious communities. On the other hand, the community fingerprints are promising as we achieved 86%, 93%, and 94% in the detection of the malware family, general malware, and benign apps, respectively.", "Keywords": "Malicious software ; Android ; Graph partition ; Community detection ; Unsupervised fingerprinting", "DOI": "10.1016/j.cose.2020.101932", "PubYear": 2020, "Volume": "96", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "ElMou<PERSON>z <PERSON>", "Affiliation": "Security Research Centre, Concordia University, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Security Research Centre, Concordia University, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center of Excellence in Information Assurance (CoEIA), King Saud University, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Sharjah, United Arab Emirates"}], "References": []}, {"ArticleId": 82556083, "Title": "A novel DRET and FRET combined fluorescent molecule and its applications in sensing and bioimaging", "Abstract": "The work presented herein is focused on the construction of a donor-acceptor system with large Stokes shift, which could be used for the detection of Fe<sup>3+</sup> in different polarity environments by combining dark resonance energy transfer (DRET) and fluorescence resonance energy transfer (FRET) strategies. A silole derivative and a rhodamine 6 G derivative, linked by a flexible diethylenetriamine spacer, are chosen as donor and acceptor, respectively. Attributed to its AIE features, silole fluorophore could act both as dark energy donor in solution state and emissive energy donor in aggregation state, and the molecule possesses polarity-sensitive DRET and FRET processes from silole to rhodamine after bonding with Fe<sup>3+</sup> ion. Thus, intense fluorescence emission from the rhodamine acceptor is obtained no matter whether the test is carried out in solution with high organic solvent content or high water content. Imaging experiments suggest that this probe could be an effective Fe<sup>3+</sup> imaging agent for use in live cells.", "Keywords": "Dark resonance energy transfer ; Silole ; Aggregation-induced emission ; Fluorescence resonance energy transfer", "DOI": "10.1016/j.snb.2020.128457", "PubYear": 2020, "Volume": "320", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Analysis Centre, School of Pharmacy, Marine Medical Research Institute of Guangdong Zhanjiang, Guangdong Medical University, Dongguan 523808, Guangdong Province, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "First Affiliated Hospital of Guangzhou Medical University, Guangzhou Medical University, Guangzhou 510120, Guangdong Province, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Analysis Centre, School of Pharmacy, Marine Medical Research Institute of Guangdong Zhanjiang, Guangdong Medical University, Dongguan 523808, Guangdong Province, China"}, {"AuthorId": 4, "Name": "Yihua Gao", "Affiliation": "Analysis Centre, School of Pharmacy, Marine Medical Research Institute of Guangdong Zhanjiang, Guangdong Medical University, Dongguan 523808, Guangdong Province, China;Corresponding author"}], "References": []}, {"ArticleId": 82556103, "Title": "A smart mobile robot commands predictor using recursive neural network", "Abstract": "Autonomous navigation of mobile robot via classic neural network (NN) models are no more valid in terms of efficiency and accuracy due to the development of new advanced techniques. However, the necessity of finding an implementable Recursive Neural Network (RNN) model to predict the motor control of the robot with both speed and accuracy constraints still remains stagnant because of the nonlinearity and complexity of the trajectories. To provide new solutions for smart navigation problems, this paper proposes a new implementable recursive neural network controller (RNNC) predictor that calculates the Pulse Width Modulation (PMW) signals of the motors. Such proposed Multi-input Multi-output (MIMO) Controller succeeded to solve the problem of speed and accuracy of autonomous navigation. The Smart RNNC model design is illustrated with its architecture in details. Due to the complexity and the non-efficiency of the training process in real-world, a 3D Simulator was developed to create all possible scenarios. The machine learning and navigation predictions processes for designing the new RNNC model are presented together in details. In addition, the motor commands generation speed and accuracy as well as their efficiency are theoretically and practically proven. Moreover, numerical studies, 3D scenarios of trajectory tracking and obstacle avoidance prove the effectiveness and robustness of the proposed technique.", "Keywords": "Virtual simulator ; Smart navigation ; Mobile robot ; RNNC ; Prediction controller ; TurtleBot", "DOI": "10.1016/j.robot.2020.103593", "PubYear": 2020, "Volume": "131", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tunis, Department of Electrical Engineering, CEREP, ENSIT, 5, Av. <PERSON><PERSON>, 1008, Tunis, Tunisia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tunis, Department of Electrical Engineering, CEREP, ENSIT, 5, Av. <PERSON><PERSON>, 1008, Tunis, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tunis, Department of Electrical Engineering, CEREP, ENSIT, 5, Av. <PERSON><PERSON>, 1008, Tunis, Tunisia"}], "References": []}, {"ArticleId": 82556180, "Title": "Design of optimal search engine using text summarization through artificial intelligence techniques", "Abstract": "Natural language processing is the trending topic in the latest research areas, which allows the developers to create the human-computer interactions to come into existence. The natural language processing is an integration of artificial intelligence, computer science and computer linguistics. The research towards natural Language Processing is focused on creating innovations towards creating the devices or machines which operates basing on the single command of a human. It allows various Bot creations to innovate the instructions from the mobile devices to control the physical devices by allowing the speech-tagging. In our paper, we design a search engine which not only displays the data according to user query but also performs the detailed display of the content or topic user is interested for using the summarization concept. We find the designed search engine is having optimal response time for the user queries by analyzing with number of transactions as inputs. Also, the result findings in the performance analysis show that the text summarization method has been an efficient way for improving the response time in the search engine optimizations.", "Keywords": "artificial intelligence;bot creation;natural language processing;search engine;text summarization", "DOI": "10.12928/telkomnika.v18i3.14028", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vignan Institute of Technology and Science"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Vignan Institute of Technology and Science"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vignan Institute of Technology and Science"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> N. <PERSON>", "Affiliation": "Al-Mustaqbal University College"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beirut Arab Univeristy"}], "References": []}, {"ArticleId": 82556181, "Title": "Automated Bangla sign language translation system for alphabets by means of MobileNet", "Abstract": "Individuals with hearing and speaking impairment communicate using sign language. The movement of hand, body and expressions of face are the means by which the people, who are unable to hear and speak, can communicate. Bangla sign alphabets are formed with one or two hand movements. There are some features which differentiates the signs. To detect and recognize the signs, analyzing its shape and comparing its features is necessary. This paper aims to propose a model and build a computer systemthat can recognize Bangla Sign Lanugage alphabets and translate them to corresponding Bangla letters by means of deep convolutional neural network (CNN). CNN has been introduced in this model in form of a pre-trained model called “MobileNet” which produced an average accuracy of 95.71% in recognizing 36 Bangla Sign Language alphabets.", "Keywords": "accuracy;Bangla sign language (BSL);CNN;convolution;MobileNet", "DOI": "10.12928/telkomnika.v18i3.15311", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Professionals"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Bangladesh University of Professionals"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Bangladesh University of Professionals"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Professionals"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Professionals"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Bangladesh University of Professionals"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Professionals"}], "References": []}, {"ArticleId": 82556185, "Title": "Radiation beam scanning for leaky wave antenna by using slots", "Abstract": "This paper provides an insight of a new, microstrip leaky wave antenna. It holds the ability to continue steer its beam at a swapping frequency. This is done with acceptable impedance matching while scanning and very little gain variation. Investigation is carried out on LWAs’ control radiation pattern in steps at a band frequency via vertical and horizontal slots. The enhancement is realized by etching horizontal and vertical slots on the radiation element. This study also presents a novel half-width microstrip leaky wave antenna (LWA). The antenna is made up of the following basic structures group’s vertical and horizontal slots. The reactance profile at the microstrip’s free edge and thus the main beam direction is changed once the control-cell states are changed. The radiation pattern direction changes by sweeping the operating frequency between 4 GHz to 6 GHz.The main beam may be directed by the antenna between 15 o and 55 o . C band achieved the measured peak gain of the antenna of 10 dBi at 4.3 GHz beam scanning range.", "Keywords": "beam steering;HW-LWA array;radiation pattern;slots", "DOI": "10.12928/telkomnika.v18i3.15720", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 2, "Name": "M.S. M. Isa", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "United Arab Emirates University"}, {"AuthorId": 5, "Name": "Mowafak K<PERSON>", "Affiliation": "University of Kerbala"}], "References": []}, {"ArticleId": 82556188, "Title": "Comparison of machine learning performance for earthquake prediction in Indonesia using 30 years historical data", "Abstract": "Indonesia resides on most earthquake region with more than 100 active volcanoes,and high number of seismic activities per year. In order to reduce the casualty, some method to predict earthquake have been developed to estimate the seismic movement. However, most prediction use only short term of historical data to predict the incoming earthquake, which has limitation on model performance. This work uses medium to long term earthquake historical data that were collected from 2 local government bodies and 8 legitimate international sources. We make an estimation of a mediumto-long term prediction via Machine Learning algorithms, which are Multinomial Logistic Regression, Support Vector Machine and Na¨ıve Bayes, and compares their performance. This work shows that the Support Vector Machine outperforms other method. We compare the Root Mean Square Error computation results that lead us into how concentrated data is around the line of best fit, where the Multinomial Logistic Regression is 0.777, <PERSON><PERSON><PERSON><PERSON> Bayes is 0.922 and Support Vector Machine is 0.751. In predicting future earthquake, Support Vector Machine outperforms other two methods that produce significant distance and magnitude to current earthquake report.", "Keywords": "big data;earthquake;machine learning;multinomial logistic regression;naive bayes;prediction;SVM", "DOI": "10.12928/telkomnika.v18i3.14756", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": "Universitas Pelita Harapan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Pelita Harapan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Pelita Harapan"}], "References": []}, {"ArticleId": 82556189, "Title": "Knowledge internalization in e-learning management system", "Abstract": "Knowledge management (KM) is gaining significance as a worthy research subject due to its contribution to the success of wide range of organizations, including higher education institutions. Knowledge internalization is mainly related to capability to see the relevance of one’s knowledge in a real situation. e-learning management system (eLMS) provides an online teaching and learning platform for students (as novice users) and lecturers (as experts in their specific domains) with the potential to improve students' knowledge acquisition. Thus, this empirical study was conducted to investigate the impact of knowledge internalization in eLMS among students in Iraq. To achieve these aims, survey research design was adopted and the sample comprised of 109 undergraduate students attending College of Information Technology in Iraq, all of whom were actively engaged in eLMS activities. The findings show that knowledge can be effectively transferred from lecturers to students via eLMS. Additionally, eLMS enable students to improve their prior knowledge through the internalization process, while also motivating them to share their knowledge with other students.", "Keywords": "e-learning management system (eLMS);explicit knowledge;internalization;knowledge management (KM);tacit knowledge", "DOI": "10.12928/telkomnika.v18i3.14817", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON><PERSON> Green University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Utara Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Utara Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Utara Malaysia"}], "References": []}, {"ArticleId": 82556190, "Title": "Application of recommendation system with AHP method and sentiment analysis", "Abstract": "Over time, people needs are increasing. Needs that must be met, often cause problems in the people in determining a choice. People must make the right choice and according to their needs. Not an easy thing for people to make these choices. Therefore, a recommendation system is needed to support people in making decisions that fit their criteria. This research provides a system that can provide recommendations for decision support people according to their criteria, which are web-based. The decision-making system in this research uses the analytical hierarchy process (AHP) method. AHP is a multi-criteria decision-making method, which in this research one of the criteria is using sentiment analysis. Sentiment analysis is the process of understanding, extracting, and processing textual data to get sentiment information from an opinion sentence. The opinion sentiment value of each alternative will be included in the AHP calculation to get the best alternative recommendations according to the criteria of people. The result of this research is that the system can provide recommendations to people or users according to their criteria and alternatives as well as public opinion about each alternative.", "Keywords": "analytical hierarchy process (AHP);decision-making system;sentiment analysis", "DOI": "10.12928/telkomnika.v18i3.14778", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 3, "Name": "<PERSON> Priyantoro", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}], "References": []}, {"ArticleId": 82556191, "Title": "Insomnia analysis based on internet of things using electrocardiography and electromyography", "Abstract": "Insomnia is a disorder to start, maintain, and wake up from sleep, has many sufferers in the world. For patients in remote locations who suffer from insomnia, which requires testing, the gold standard performed requires patients to take the time and travel to the health care center. By making alternatives to remote sleep insomnia testing using electrocardiography and electromyography connected to the internet of things can solve the problem of patients' access to treatment. Delivery of patient data to the server is done to make observations from the visualization of patient data in real-time. Furthermore, using artificial neural networks was used to classify EMG, ECG, and combine patient data to determine patients who have Insomnia get resulted in patient classification errors around 0.2% to 2.7%.", "Keywords": "electrocardiography;electromyography;insomnia;internet of things", "DOI": "10.12928/telkomnika.v18i3.14897", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Universitas Nasional"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Nasional"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universitas Nasional"}], "References": []}, {"ArticleId": 82556192, "Title": "Cleveree: an artificially intelligent web service for Jacob voice chatbot", "Abstract": "<PERSON> is a voice chatbot that use Wit.ai to get the context of the question and give an answer based on that context. However, <PERSON> has no variation in answer and could not recognize the context well if it has not been learned previously by the Wit.ai. Thus, this paper proposes two features of artificial intelligence (AI) built as a web service: the paraphrase of answers using the Stacked Residual LSTM model and the question summarization using Cosine Similarity with pre-trained Word2Vec and TextRank algorithm. These two features are novel designs that are tailored to <PERSON>, this AI module is called Cleveree. The evaluation of <PERSON>levere<PERSON> is carried out using the technology acceptance model (TAM) method and interview with <PERSON> admins. The results show that 79.17% of respondents strongly agree that both features are useful and 72.57% of respondents strongly agree that both features are easy to use.", "Keywords": "cosine similarity;LSTM;stacked residual;textrank;web service", "DOI": "10.12928/telkomnika.v18i3.14791", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Multimedia Nusantara"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Multimedia Nusantara"}], "References": []}, {"ArticleId": 82556193, "Title": "Transfer learning with multiple pre-trained network for fundus classification", "Abstract": "Transfer learning (TL) is a technique of reuse and modify a pre-trained network. It reuses feature extraction layer at a pre-trained network. A target domain in TL obtains the features knowledge from the source domain. TL modified classification layer at a pre-trained network. The target domain can do new tasks according to a purpose. In this article, the target domain is fundus image classification includes normal and neovascularization. Data consist of 100 patches. The comparison of training and validation data was 70:30. The selection of training and validation data is done randomly. Steps of TL i.e load pre-trained networks, replace final layers, train the network, and assess network accuracy. First, the pre-trained network is a layer configuration of the convolutional neural network architecture. Pre-trained network used are AlexNet, VGG16, VGG19, ResNet50, ResNet101, GoogLeNet, Inception-V3, InceptionResNetV2, and squeezenet. Second, replace the final layer is to replace the last three layers. They are fully connected layer, softmax, and output layer. The layer is replaced with a fully connected layer that classifies according to number of classes. Furthermore, it's followed by a softmax and output layer that matches with the target domain. Third, we trained the network. Networks were trained to produce optimal accuracy. In this section, we use gradient descent algorithm optimization. Fourth, assess network accuracy. The experiment results show a testing accuracy between 80% and 100%.", "Keywords": "classification;convolutional neural network;multiple pre-trained network;neovascularization;transfer learning", "DOI": "10.12928/telkomnika.v18i3.14868", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Trunojoyo Madura"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Universitas Airlangga"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Airlangga"}], "References": []}, {"ArticleId": 82556194, "Title": "Convolutional neural network for maize leaf disease image classification", "Abstract": "This article discusses the maize leaf disease image classification. The experimental images consist of 200 images with 4 classes: healthy, cercospora, common rust and northern leaf blight. There are 2 steps: feature extraction and classification. Feature extraction obtains features automatically using convolutional neural network (CNN). Seven CNN models were tested i.e AlexNet, virtual geometry group (VGG) 16, VGG19, GoogleNet, Inception-V3, residual network 50 (ResNet50) and ResNet101. While the classification using machine learning methods include k-Nearest neighbor, decision tree and support vector machine. Based on the testing results, the best classification was AlexNet and support vector machine with accuracy, sensitivity, specificity of 93.5%, 95.08%, and 93%, respectively.", "Keywords": "alexnet;classification;convolutional neural network;k-nearest neighbor;maize leaf image", "DOI": "10.12928/telkomnika.v18i3.14840", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Trunojoyo Madura"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Trunojoyo Madura"}], "References": []}, {"ArticleId": 82556197, "Title": "Communication between PLC different vendors using OPC server improved with application device", "Abstract": "Many industries often use different devices and controllers in automation systems. They all face the same difficulty how to exchange data between all those components. This paper proposed the implementation of OPC Server as software interface on communication between two different controllers, PLC Mitsubishi and PLC Omron. The main advantage of the method is  the compatibility and solution for the factory difficulty problem because of using several driver controller. The compatibility among the different platforms of both controller, PLC Mitsubishi and PLC Omron, can be reached by use of KEPServerEx6 (OPC server) as a software interface.  To test the compatibility amongst two different controllers, there was developed and implemented two field application devices, bottle unscramble and bottle filling station. This implementation shows OPC Server technology resolving data compatibility issues between different platforms and reducing development costs. It is envisaged that the method can be very useful to realize integration.", "Keywords": "data communication;different platform;KEPServerEX6;OPC server;PLC;SCADA", "DOI": "10.12928/telkomnika.v18i3.14757", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Mekatronika Sanata Dharma"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Politeknik Mekatronika Sanata Dharma"}, {"AuthorId": 3, "Name": "Thomas A. <PERSON>", "Affiliation": "Politeknik Mekatronika Sanata Dharma"}], "References": []}, {"ArticleId": 82556200, "Title": "Identifier of human emotions based on convolutional neural network for assistant robot", "Abstract": "This paper proposes a solution for the problem of continuous prediction in real-time of the emotional state of a human user from the identification of characteristics in facial expressions. In robots whose main task is the care of people (children, sick or elderly people) is important to maintain a close relationship man-machine, anld a rapid response of the robot to the actions of the person under care. We propose to increase the level of intimacy of the robot, and its response to specific situations of the user, identifying in real time the emotion reflected by the person's face. This solution is integrated with algorithms of the research group related to the tracking of people for use on an assistant robot. The strategy used involves two stages of processing, the first involves the detection of faces using HOG and linear SVM, while the second identifies the emotion in the face using a CNN. The strategy was completely tested in the laboratory on our robotic platform, demonstrating high performance with low resource consumption. Through various controlled laboratory tests with different people, which forced a certain emotion on their faces, the scheme was able to identify the emotions with a success rate of 92%.", "Keywords": "autonomous robot;convolutional neuronal network;human emotions;service robot", "DOI": "10.12928/telkomnika.v18i3.14777", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Distrital Francisco José <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Distrital Francisco José <PERSON>"}, {"AuthorId": 3, "Name": "Angélica Rendón", "Affiliation": "Universidad Distrital Francisco José <PERSON>"}], "References": []}, {"ArticleId": 82556201, "Title": "Towards cognitive artificial intelligence device: an intelligent processor based on human thinking emulation", "Abstract": "The intervention of computer technology began the era of a more intelligent and independent instrumentation system based on intelligent methods such as artificial neural networks, fuzzy logic, and genetic algorithm. On the other hand, processor with artificial cognitive ability has also been discovered in 2016. The architecture of the processor was designed based on knowledge growing system (KGS) algorithm, a new concept in artificial intelligence (AI) which is focused on the emulation of the process of the growing of knowledge in human brain after getting new information from human sensory organs. KGS is considered as the main method of a new perspective in AI called as cognitive artificial intelligence (CAI). The design is to obtain the architecture of the data path of the processor. We found that the complexity of the processor circuit is determined by the number of combinations of sensors and hypotheses as the main inputs to the processor. This paper addresses the development of an intelligence processor based on cognitive AI in order to realize an Intelligence Instrumentation System. The processor is implemented in field programmable gate array (FPGA) and able to perform human thinking emulation by using KGS algorithm.", "Keywords": "cognitive artificial intelligence;human thinking emulation;intelligent instrumentation;intelligent processor;knowledge growing system", "DOI": "10.12928/telkomnika.v18i3.14835", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Katolik Indonesia Atma Jaya Jakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Applied Master on Electrical Engineering State Polytechnic of Malang"}, {"AuthorId": 3, "Name": "Trio Adiono", "Affiliation": "Institut Teknologi Bandung"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Teknologi Bandung"}], "References": []}, {"ArticleId": 82556204, "Title": "Chaos synchronization in a 6-D hyperchaotic system with self-excited attractor", "Abstract": "This paper presented stability application for chaos synchronization using a 6-D hyperchaotic system of different controllers and two tools: Lyapunov stability theory and Linearization methods. Synchronization methods based on nonlinear control strategy is used. The selecting controller's methods have been modified by applying complete synchronization. The Linearization methods can achieve convergence according to the of complete synchronization. Numerical simulations are carried out by using MATLAB to validate the effectiveness of the analytical technique.", "Keywords": "6-D hyperchaotic system;chaos synchronization;Lyapunov stability theory;nonlinear control steategy;self-excited attractor", "DOI": "10.12928/telkomnika.v18i3.13672", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Mosul"}, {"AuthorId": 2, "Name": "<PERSON>ad <PERSON>-Azzawi", "Affiliation": "University of Mosul"}], "References": []}, {"ArticleId": 82556205, "Title": "Velocity control of ROV using modified integral SMC with optimization tuning based on <PERSON><PERSON><PERSON><PERSON> analysis", "Abstract": "Remotely Operated Vehicle also known as ROV is a vehicle with high nonlinearity and uncertainty parameters that requires a robust control system to maintain stability. The nonlinearity and uncertainty of ROV are caused by underwater environmental conditions and by the movement of the vehicle. SMC is one of the control systems that can overcome nonlinearity and uncertainty with the given robust system. This work aims to control velocity of the vehicle with proposes the use of modified integral SMC compensate error in ROV and the use of particle swarm optimization (PSO) to optimize the adjustment of SMC parameters. The ROV used in this paper has a configuration of six thrusters with five DoF movements that can be controlled. Modified integral sliding mode is used to control all force direction to increase the convergence of speed error. Adjustment optimization techniques with PSO are used to determine four values of sliding control parameters for five DoF. Using Lyapunov stability approach control law of sliding mode is derived and its global stability proved mathematically. Simulation results are conducted to evaluate the effectiveness of Modified Integral SMC and compared with nonlinear control.", "Keywords": "dynamics control;remotely operated vehicle;sliding mode control;tuning optimization", "DOI": "10.12928/telkomnika.v18i3.14781", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}], "References": []}, {"ArticleId": 82556206, "Title": "Deep hypersphere embedding for real-time face recognition", "Abstract": "With the advancement of human-computer interaction capabilities of robots, computer vision surveillance systems involving security yields a large impact in the research industry by helping in digitalization of certain security processes. Recognizing a face in the computer vision involves identification and classification of which faces belongs to the same person by means of comparing face embedding vectors. In an organization that has a large and diverse labelled dataset on a large number of epoch, oftentimes, creates a training difficulties involving incompatibility in different versions of face embedding that leads to poor face recognition accuracy. In this paper, we will design and implement robotic vision security surveillance system incorporating hybrid combination of MTCNN for face detection, and FaceNet as the unified embedding for face recognition and clustering.", "Keywords": "deep hypersphere embedding;real-time face detection;real-time face recognition;robotic visions;security surveillance system", "DOI": "10.12928/telkomnika.v18i3.14787", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "De La Salle University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "De La Salle University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "De La Salle University"}, {"AuthorId": 4, "Name": "Shearyl <PERSON>s", "Affiliation": "De La Salle University"}], "References": []}, {"ArticleId": 82556207, "Title": "Stereo vision-based obstacle avoidance module on 3D point cloud data", "Abstract": "This paper deals in building a 3D vision-based obstacle avoidance and navigation. In order for an autonomous system to work in real life condition, a capability of gaining surrounding environment data, interpret the data and take appropriate action is needed. One of the required capability in this matter for an autonomous system is a capability to navigate cluttered, unorganized environment and avoiding collision with any present obstacle, defined as any data with vertical orientation and able to take decision when environment update exist. Proposed in this work are two-step strategy of extracting the obstacle position and orientation from point cloud data using plane based segmentation and the resultant segmentation are mapped based on obstacle point position relative to camera using occupancy grid map to acquire obstacle cluster position and recorded the occupancy grid map for future use and global navigation, obstacle position gained in grid map is used to plan the navigation path towards target goal without going through obstacle position and modify the navigation path to avoid collision when environment update is present or platform movement is not aligned with navigation path based on timed elastic band method.", "Keywords": "3D vision;obstacle avoidance;obstacle extraction;timed elastic band", "DOI": "10.12928/telkomnika.v18i3.14829", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Robotics and Intelligent System Center"}], "References": []}, {"ArticleId": 82556208, "Title": "On the dynamic behavior of the current in the condenser of a boost converter controlled with ZAD", "Abstract": "In this paper, an analytical and numerical study is conducted on the dynamics of the current in the condenser of a boost converter controlled with ZAD, using a pulse PWM to the symmetric center. A stability analysis of periodic 1T-orbits was made by the analytical calculation of the eigenvalues of the Jacobian matrix of the dynamic system, where the presence of flip and Neimar–Sacker-type bifurcations was determined. The presence of chaos, which is controlled by ZAD and FPIC techniques, is shown from the analysis of L<PERSON><PERSON>nov exponents.", "Keywords": "boost converter;flip bifurcation;Neimar-Sacker bifurcation;nonlinearity;ZAD control technique", "DOI": "10.12928/telkomnika.v18i3.14109", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Darío Del Cristo <PERSON>", "Affiliation": "Institución Educativa San Marcos, San Marcos – Sucre, Colombia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>rujillo", "Affiliation": "Universidad Nacional de Colombia, Sede Manizales, Investigation Group: Scientific Calculation and Mathematical Modeling, Manizales, Colombia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Nacional de Colombia, Sede Medellín, Facultad de Ciencias, Escuela de Física"}], "References": []}, {"ArticleId": 82556209, "Title": "Pulmonary rontgen classification to detect pneumonia disease using convolutional neural networks", "Abstract": "Every organism is known to have different structural and biological system, specifically in human immunity. If the immune system weakens, the body is susceptible to disease especially pneumonia disease. Pneumonia disease is caused by the bacterium Streptococcus pneumonia, and according to the World Health Organization (WHO), it is identified as the leading cause of death in children worldwide, which is about 16%, for those under the age of 5. Meanwhile, someone who is predicted to have pneumonia by a doctor is recommended for an X-ray. Convolutional neural networks (CNNs) is an accurate method to help the doctor's predicted correctly. CNNs is divided into two important parts, feature extraction layer (convolutional layer and pooling layer) and fully connected layer. CNNs method is commonly used for image data classification. Therefore, CNNs is suitable to classify pneumonia based on lung X-ray in order to obtain accurate prediction results. And then, the results can be seen based on the graph of the accuracy value and the loss value. When CNNs method applied on the dataset, an accuracy rate of 97% was obtained. Based on accuracy rate, it shows that CNNs can be applied to image data (especially lung X-ray) for classification of pneumonia disease.", "Keywords": "classification;CNNs;pneumonia;X-ray", "DOI": "10.12928/telkomnika.v18i3.14839", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Indonesia"}, {"AuthorId": 2, "Name": "Rivan <PERSON>", "Affiliation": "University of Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Indonesia"}, {"AuthorId": 4, "Name": "Chelvian Aroef", "Affiliation": "University of Indonesia"}], "References": []}, {"ArticleId": ********, "Title": "WSN nodes power consumption using multihop routing protocol for illegal cutting forest", "Abstract": "The need for an automation system from a remote area cannot be separated from the role of the wireless sensor network. However, the battery consumption is still a problem that influences the lifetime of the system. This research focused on studying how to characterize the power consumption on each sensor node using multihop routing protocol in the illegal logging field, to get the prediction lifetime of the network. The system is designed by using six sensor nodes in a master-slave connection and implemented in a tree topology. Each sensor node is consisting of a sound sensor, vibration sensor, Xbee communication, current and voltage sensor, and Arduino nano. The system is tested using battery 10050 mAH with several scenarios to have calculated how long the battery lifetime can be predicted. The results stated that the master node on the network depleted the power of the battery faster than the slave node since the more slaves connected to the master, the more energy the battery consumes.", "Keywords": "energy efficient;illegal cutting tree;multihop routing protocol;sensor node;wireless sensor network", "DOI": "10.12928/telkomnika.v18i3.14844", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> And<PERSON>", "Affiliation": "Telkom University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka"}], "References": []}, {"ArticleId": ********, "Title": "A new model for large dataset dimensionality reduction based on teaching learning-based optimization and logistic regression", "Abstract": "One of the human diseases with a high rate of mortality each year is breast cancer (BC). Among all the forms of cancer, BC is the commonest cause of death among women globally. Some of the effective ways of data classification are data mining and classification methods. These methods are particularly efficient in the medical field due to the presence of irrelevant and redundant attributes in medical datasets. Such redundant attributes are not needed to obtain an accurate estimation of disease diagnosis. Teaching learning-based optimization (TLBO) is a new metaheuristic that has been successfully applied to several intractable optimization problems in recent years. This paper presents the use of a multi-objective TLBO algorithm for the selection of feature subsets in automatic BC diagnosis. For the classification task in this work, the logistic regression (LR) method was deployed. From the results, the projected method produced better BC dataset classification accuracy (classified into malignant and benign). This result showed that the projected TLBO is an efficient features optimization technique for sustaining data-based decision-making systems.", "Keywords": "feature selection;FSS;IDS;NTLBO;subset", "DOI": "10.12928/telkomnika.v18i3.13764", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Al Salam University College"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Al Salam University College"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mustansiriyah University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Aliraqia University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University Polytechnic of Bucharest"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 82556212, "Title": "Single camera depth control in micro class ROV", "Abstract": "Navigation is one of the main challenges in an underwater vehicle. To measure and sustain the depth in the micro class remotely operated vehicle (ROV) robot is one of the main demands in the underwater robot competition. There are many sensors that can be used to measure the depth; one of the sensors is using a single camera sensor. In this works, camera-based depth control is developed and evaluated for micro class ROV, namely as fitoplankton SAS ROV. Fitoplankton SAS ROV is a micro ROV prototype with six thrusters. To maintain the depth position, a PID control system with a camera-based depth sensor as the input of the setpoint is used. Moreover, the method for the camera to measure the distance is using the triangle similarity method. In this paper, the experimental scenario is using the rectangular marker to measure the distance, and the value of the depth is processing in the ground control station (GCS). The GCS will send the thruster value to control the depth, which depends on the PID control system. The experiment results show an average of depth accuracy of 95.74% to the depth setpoint.", "Keywords": "ardusub;camera-based depth control;distance measure;ROV;triangle similarity", "DOI": "10.12928/telkomnika.v18i3.14885", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Telkom University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Telkom University"}, {"AuthorId": 3, "Name": "Sintong Tu<PERSON>", "Affiliation": "Telkom University"}], "References": []}, {"ArticleId": 82556228, "Title": "Design and modeling of solenoid inductor integrated with FeNiCo in high frequency", "Abstract": "In this work, the design and modeling of the solenoid inductor are discussed. The layout of integrated inductors with magnetic cores and their geometrical parameters are developed. The quality factor Q and inductance value L are extracted from the S-parameters and plotted versus frequency. The effect of solenoid inductor geometry on inductance and quality factor are studied via simulation using MATLAB. The solenoid inductor geometry parameters considered are the number of turns, length of the magnetic core, the width of a magnetic core, the gap between turns, the thickness of the magnetic core, the thickness of the coil and oxide thickness of solenoid inductor. The performance of the proposed solenoid inductor integrated with FeNiCo is compared with other solenoid inductors.", "Keywords": "high frequency;integrated;magnetic core;solenoid inductor", "DOI": "10.12928/telkomnika.v18i4.12139", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ahmed <PERSON> University Centre"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassiba Benbouali University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassiba Benbouali University"}], "References": []}, {"ArticleId": 82556229, "Title": "Enhancement of student performance prediction using modified K-nearest neighbor", "Abstract": "The traditional K-nearest neighbor (KNN) algorithm uses an exhaustive search for a complete training set to predict a single test sample. This procedure can slow down the system to consume more time for huge datasets. The selection of classes for a new sample depends on a simple majority voting system that does not reflect the various significance of different samples (i.e. ignoring the similarities among samples). It also leads to a misclassification problem due to the occurrence of a double majority class. In reference to the above-mentioned issues, this work adopts a combination of moment descriptor and KNN to optimize the sample selection. This is done based on the fact that classifying the training samples before the searching actually takes place can speed up and improve the predictive performance of the nearest neighbor. The proposed method can be called as fast KNN (FKNN). The experimental results show that the proposed FKNN method decreases original KNN consuming time within a range of (75.4%) to (90.25%), and improve the classification accuracy percentage in the range from (20%) to (36.3%) utilizing three types of student datasets to predict whether the student can pass or fail the exam automatically.", "Keywords": "consuming time;educational data mining moments;KNN;prediction", "DOI": "10.12928/telkomnika.v18i4.13849", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Iraqi Commission for Computers & Informatics (IIPS-ICCI)"}, {"AuthorId": 2, "Name": "Rafah Al-Hamdani", "Affiliation": "Iraqi Commission for Computers & Informatics (IIPS-ICCI)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Technology Baghdad"}], "References": []}, {"ArticleId": 82556230, "Title": "Approximation of regression-based fault minimization for network traffic", "Abstract": "This research associates three distinct approaches for computer network traffic prediction. They are the traditional stochastic gradient descent (SGD) using a few random samplings instead of the complete dataset for each iterative calculation, the gradient descent algorithm (GDA) which is a well-known optimization approach in Deep Learning, and the proposed method. The network traffic is computed from the traffic load (data and multimedia) of the computer network nodes via the Internet. It is apparent that the SGD is a modest iteration but can conclude suboptimal solutions. The GDA is a complicated one, can function more accurate than the SGD but difficult to manipulate parameters, such as the learning rate, the dataset granularity, and the loss function. Network traffic estimation helps improve performance and lower costs for various applications, such as an adaptive rate control, load balancing, the quality of service (QoS), fair bandwidth allocation, and anomaly detection. The proposed method confirms optimal values out of parameters using simulation to compute the minimum figure of specified loss function in each iteration.", "Keywords": "approximation;deep learning;error minimization;network traffic;regression-based prediction", "DOI": "10.12928/telkomnika.v18i4.13192", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Chan<PERSON>orn Jittawiriyanu<PERSON>", "Affiliation": "Assumption University"}], "References": []}, {"ArticleId": 82556231, "Title": "Influence of SME characteristics on the implementation of ERP", "Abstract": "The ERP market has recently experienced a significant evolution in recent years, both in large companies and in small and medium-sized enterprises (SMEs). Compared to large companies, SMEs are distinguished by specific characteristics that can influence the implementation of the ERP system in these organizations. The purpose of this study is to analyse how these characteristics can determine the success or the failure of ERP implementation in SMEs. First, a set of characteristics, specific to SMEs has been identified from the relevant literature. Then, the influence of each characteristic on the different ERP lifecycle activities were studied. A multiple case study of four SMEs from different sectors was conducted. The data collection was carried out through 28 individual interviews with several stakeholders (users, external consultants, internal IT specialists and managers) in the four cases. The analysis of the interview data showed first that financial resources, Decision making and, the type of ownership of the company were identified as the most influential contextual factors. Then the two phases of the ERP life cycle \"implementation\" and \"use and maintenance\" were identified as being the most affected by the context of SMEs. The study results have significant implications for experts, managers and information.", "Keywords": "Enterprise resource planning system;ERP implementation;ERP lifecycle;SMEs", "DOI": "10.12928/telkomnika.v18i4.13537", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University Hassan II"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University Hassan II"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University Hassan II"}, {"AuthorId": 4, "Name": "Assia Bakali", "Affiliation": "Royal Naval School"}], "References": []}, {"ArticleId": 82556232, "Title": "A Survey on the applications of IoT: an investigation into existing environments, present challenges and future opportunities", "Abstract": "In today’s digital environment, devices are able to interconnect and react to contextual data more than ever before: artificial intelligence is beginning to coordinate how data collected from sensors and de-vices within the network is analysed, and device ecosystems are replacing standalone devices to deliver solutions to the user. In this paper, the researcher explores current implementations of IoT that have led to positive outcomes for the user; but also, the challenges that remain in today’s applications. Moreover, ex-ploring these current barriers may be able to infer future applications capable of being deployed on a global scale", "Keywords": "articial intelligence;internet of things;wireless sensor networks", "DOI": "10.12928/telkomnika.v18i3.15604", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northwestern Polytechnical University"}, {"AuthorId": 3, "Name": "Cheng<PERSON> Tang", "Affiliation": "Northwestern Polytechnical University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University"}], "References": []}, {"ArticleId": 82556233, "Title": "Effects of noises on near infrared sensor for blood glucose level measurement", "Abstract": "This paper proposed the method of measuring glucose level in solution using near infrared light (NIR) and photodiode sensor. We studied noises that occurred on the output signal of NIR sensor in three different room conditions in order to know the effects on this sensor output voltage stability. The sensor’s circuit consisted of a 1450 nm NIR light emitting diode, a photodiode as the receiver, transimpedance amplifier, a notch filter, and a 4th order low pass filter. The results indicated that sunlight passing through windows was the most influencing factor caused the unstable sensor output voltage. Filters removed the effective voltages and the average sensor output voltages from the three rooms were 4.6825 V for air media, 2.2809 V for water media and 2.3368 V for glucose solution media. The output voltages tended to increase for one-hour measurement about 10 to 40 mV for air media, 40 to 90 mV for water media and 30 to 80 mV for glucose solution media. This sensor could only be used in a short time and suitable in a room without sunlight. Based on the voltage difference of the average sensor output voltage with water and glucose solution media, the sensor had the potential to be a blood glucose level meter.", "Keywords": "light emitting diode;near infrared light;NIR sensor;photodiode;sunlight", "DOI": "10.12928/telkomnika.v18i3.14760", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Trisakti University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Trisakti University"}], "References": []}, {"ArticleId": 82556234, "Title": "Semi-supervised auto-encoder for facial attributes recognition", "Abstract": "The particularity of our faces encourages many researchers to exploit their features in different domains such as user identification, behaviour analysis, computer technology, security, and psychology. In this paper, we present a method for facial attributes analysis. The work addressed to analyse facial images and extract features in the purpose to recognize demographic attributes: age, gender, and ethnicity (AGE). In this work, we exploited the robustness of deep learning (DL) using an updating version of autoencoders called the deep sparse autoencoder (DSAE). In this work we used a new architecture of DSAE by adding the supervision to the classic model and we control the overfitting problem by regularizing the model. The pass from DSAE to the semi-supervised autoencoder (DSSAE) facilitates the supervision process and achieves an excellent performance to extract features. In this work we focused to estimate AGE jointly. The experiment results show that DSSAE is created to recognize facial features with high precision. The whole system achieves good performance and important rates in AGE using the MORPH II database", "Keywords": "age estimation;deep learning;gender recognition;softmax classifier;supervised autoencoder", "DOI": "10.12928/telkomnika.v18i4.14836", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Gabès"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Gabès"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "University of Sfax"}], "References": []}, {"ArticleId": 82556240, "Title": "Voronoi diagram with fuzzy number and sensor data in an indoor navigation for emergency situation", "Abstract": "Finding shortest and safest path during emergency situation is critical. In this paper, an indoor navigation during an emergency time is investigated using the combination of Voronoi Diagram and fuzzy number. The challenge in indoor navigation is to analyses the network when the shortest path algorithm does not work as always expected. There are some existing methods to generate the network model. First, this paper will discuss the feasibility and accuracy of each method when it is implemented on building environment. Next, this paper will discuss selected algorithms that determine the selection of the best route during an emergency situation. The algorithm has to make sure that the selected route is the shortest and the safest route to the destination. During a disaster, there are many uncertainties to deal with in determining the shortest and safest route. Fuzzy logic can be hardly called for to deal with these uncertainties. Based on sensor data, this paper will also discuss how to solve shortest path problem using a fuzzy number.", "Keywords": "disaster management;emergency navigation, GIS;indoor navigation;shortest path algorithm", "DOI": "10.12928/telkomnika.v18i4.14905", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}], "References": []}, {"ArticleId": 82556241, "Title": "Assessment of quality indicators of the automatic control system influence of accident interference", "Abstract": "This work concentrates the analysis of the system of automatic control of the directive diagram of the moving active electronically scanned array with a limited number of transceiver modules. The analysis revealed a number of shortcomings that lead to a significant increase in standard deviations, quadratic integral estimates, and an increase in transient time. The identified disadvantages lead to a decrease in the efficiency of the antenna system, an increase in the error rate at the reception, the inability of the system to react to disturbances applied to any point of the system in the event of a mismatch of a given signal/noise level. In accordance with the analysis, the mathematical model of the automatic control system of the directional diagram of the moving active electronically scanned array was considered, considering this a new method of estimating the quality indicators of the automatic control diagram of the directional diagram of the active electronically scanned array in a random setting and disturbing action was developed. The difference between the proposed method and the existing method is in the construction of an automatic control system with differential coupling equivalent to the combination due to the introduction of derivatives of the random setting action of the open compensation connection.", "Keywords": "active electronically scanned array;control system;differential coupling;mean-square error;radiation pattern;repeater", "DOI": "10.12928/telkomnika.v18i4.15601", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Technical University of Ukraine \"Igor <PERSON> Kyiv Polytechnic Institute\""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Khmelnytsky National University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Technical University of Ukraine \"Igor <PERSON> Kyiv Polytechnic Institute\""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Technical University of Ukraine \"Igor <PERSON> Kyiv Polytechnic Institute\""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Khmelnytsky National University"}], "References": []}, {"ArticleId": 82556242, "Title": "Uric acid detection in visible spectrum", "Abstract": "The measurement of uric acid based on the optical absorption at visible light spectrum is investigated and tested. Sensing in the visible region was conducted for determination of suitable wavelength that produces high sensitivity and accuracy performance based on the Beer-Lambert law calculation. In this work, the uric acid is detected by detecting sodium urate as a product of chemical reaction between uric acid with sodium hydroxide buffer. The setup has been tested for uric acid concentration ranging from 15 mg/dL to 85 mg/dL. Three wavelengths have been analyzed which are 460 nm, 525 nm and 630 nm. Measured data at 460nm wavelength exhibits the highest sensitivity, which is 0.0012 (mg/dL)-with 86.51% accuracy. Detection of uric acid at visible light spectrum offers a low-cost sensor based on visible LEDs and photodiode is possible to be realized.", "Keywords": "Beer-Lambert law;spectrophotometer;uric acid", "DOI": "10.12928/telkomnika.v18i4.14993", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}], "References": []}, {"ArticleId": 82556243, "Title": "Design of shunt hybrid active power filter for compensating harmonic currents and reactive power", "Abstract": "For the past two decades, tremendous advancements have been achieved in the electricity industry. The usage of non-linear loads in the daily life has affected the power quality of the system and caused the presence of harmonics. To compensate the harmonic currents and reactive power in the system, the design of shunt hybrid active power filter has been proposed in this research. The design of the filter has included several control systems of instantaneous active and reactive power (p-q) theory and PI controller to investigate the performance of the filter. The robustness of the designed hybrid power filter has also been benchmarked with the other filter topologies available in literature. The hybrid power filter will combine passive power filter and active power filter configurated in shunt connection. The result of this research showed that the total harmonic distortion analyzed is below than 5% according to IEEE-519 standard requirements and reactive power is compensated proved by the increase in power factor. The shunt hybrid active power filter is designed, and simulation result is analyzed by using MATLAB-Simulink environment.", "Keywords": "harmonic;hybrid active filter;P-q theory;Pi controller;power factor correction", "DOI": "10.12928/telkomnika.v18i4.15156", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universiti Kuala Lumpur (UniKL BMI)"}, {"AuthorId": 2, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": "Universiti Kuala Lumpur (UniKL BMI)"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universiti Sains Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universiti Kuala Lumpur (UniKL BMI)"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 82556244, "Title": "Na3Ce(PO4)2:Tb3+ and Na(Mg2–xMnX)LiSi4O10F2:Mn phosphors: a suitable selection for enhancing color quality and luminous flux of remote white light-emitting diodes", "Abstract": "This study proposed the TRP, a remote phosphor structure that has 3 phosphor layers, to ehance the chromatic quality and lumen output for white light-emitting diodes devices (WLEDs). The arrangment of phosphor layers is yellow YAG:Ce3+ phosphor, green Na3Ce(PO4)2:Tb3+ phosphor, and red Na(Mg2–xMnX)LiSi4O10F2:Mn phosphor from bottom to top. Red Na(Mg2–xMnX)LiSi4O10F2:Mn phosphor is used for the red light component to boost color rendering index (CRI). The green layer Na3Ce(PO4)2:Tb3+ phosphor is utilized for the green light component to produce higher luminous flux (LF). With the addition of red and green phosphor, the yellow YAG:Ce3+ concentration must decrease to maintain the 6000 K color temperature. The research results show that red phosphor Na(Mg2–xMnX)LiSi4O10F2:Mn concentration is beneficial for CRI, while green phosphor Na3Ce(PO4)2:Tb3+ is detrimental to CRI. Morever, CQS reaction with red and green phosphor is also studied, which show notable improvement when Na(Mg2–xMnX)LiSi4O10F2:Mn concentration is from 10%-14%, regardless of Na3Ce(PO4)2:Tb3+. The luminous flux (LF) can also increase for more than 40% with the reduced light loss and added green phosphor. Research results are valuable references for producers to enhance the color quality and the light emission of WLEDs.", "Keywords": "color quality scale;luminous flux;Na(Mg2–xMnX)LiSi4O10F2:Mn Na3Ce(PO4)2:Tb3+;remote phosphor structure", "DOI": "10.12928/telkomnika.v18i4.13723", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Posts and Telecommunications Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>n <PERSON> University"}], "References": []}, {"ArticleId": 82556246, "Title": "Comparative analysis of electrochemical energy storage technologies for smart grid", "Abstract": "This paper presents a comparative analysis of different forms of electrochemical energy storage technologies for use in the smart grid. This paper addresses various energy storage techniques that are used in the renewable energy sources connected to the smart grid. Energy storage technologies will most likely improve the penetrations of renewable energy on the electricity network. Consequently, energy storage systems could be the key to finally replacing the need for fossil fuel with renewable energy. It is hard to evaluate the different types of energy storage techniques between themselves due to the fact that each technology could be used in a different way and are more like compliments. Subsequently, for the purposes of this paper, it is seen that the use of energy storage technologies will increase the supply, and balances out the demand for energy.", "Keywords": "battery storage;energy storage;renewable energy resources;smart grid", "DOI": "10.12928/telkomnika.v18i4.14039", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Woosong University"}], "References": []}, {"ArticleId": 82556265, "Title": "Wireless communication system with frequency selective channel OFDM modulation technique", "Abstract": "This paper introduces the design and implementation of a wireless communication system with MATLAB based on orthognal frequency division multiplexing technique (OFDM). The constructed system is consisting of transmitter, fading channel and receiver. At the transmitter, the transmitted signal first modulated with PSK modulation, and then multiplexed with OFDM technique to achieve a higher bit rates transmission. The signal was then transmitted through a frequency selective channel with 6 taps. In the receiver parity. The received faded signal processed to be de-multiplexed and de-modulated. Then, a frequency domain equalizer was adopted to remove the fading noise and the inter-symbol interference from the received signal that introduced due to the fading channel. In order to inspect the performance of the frequency equilizer, bit error rate for the overall system was calculated at the receiving point and to recover the original information signal. The simulation results of the designed system as well as the frequency equilizer showed a robustness against the frequency selective faded channel effects. The maximum obtained bit error rate was around 10-5, which means that original signal was effectively recovered.", "Keywords": "equalizer;fading channels;frequency selective channels;OFDM;wireless communication", "DOI": "10.12928/telkomnika.v18i3.14683", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Islamic University"}, {"AuthorId": 2, "Name": "Mazen M. A. <PERSON>", "Affiliation": "University of Al-Qadisiyah"}], "References": []}, {"ArticleId": 82556271, "Title": "Performance analysis of image transmission with various channel conditions/modulation techniques", "Abstract": "This paper investigates the impact of different modulation techniques for digital communication systems that employ quadrature phase shift keying (QPSK) and quadrature amplitude modulation (16-QAM and 64-QAM) to transmit images over AWGN and Rayleigh fading channels for the cellular mobile networks. In the further steps, wiener and median filters has been adopted to the simulation are used at the receiver side to remove the impulsive noise present in the received image. This work is performed to evaluate the transmission of two dimensional (2D) gray-scale and color-scale (RGB) images with different values from signal to noise ratios (SNR), such as; (5, 10 and 15) dB over different channels. The correct conclusions are made by comparing many of the observed Matlab simulation results. This was carried out through the results that measure the quality of received image, which is analyzes in terms of SNRimage peak signal to noise ratio (PSNR) and mean square error (MSE).", "Keywords": "AWGN;gray-scale and color-scale (RGB) images;mean square error (MSE);peak signal to noise ratio (PSNR);Rayleigh fading channel", "DOI": "10.12928/telkomnika.v18i3.14172", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Furat Al-Awsat Technical University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Furat Al-Awsat Technical University"}, {"AuthorId": 3, "Name": "Bashar J<PERSON>", "Affiliation": "Al-Furat Al-Awsat Technical University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Universiti Malaysia Pahang (UMP)"}], "References": []}, {"ArticleId": 82556272, "Title": "Stochastic renewable energy resources integrated multi-objective optimal power flow", "Abstract": "The modern state of electrical system consists the conventional generating units along with the sources of renewable energy. The proposed article recommends a method for the solution of single and multi-objective optimal power flow, integrating wind and solar output energy with traditional coal-based generating stations. In the first part of the article, the two wind power plants and one solar PV power plants are incorporated with the thermal power plants. The optimal power flow problem of single and conflicting multi-objectives are taken with this scenario. The second part of the paper, solar power plant is replaced with another wind power plant with the conventional coal-based power plants. The techno-economic analysis are done with this state of electrical system. In proposed work, lognormal and weibull probability distribution functions are also utilized for predicting solar and wind outputs, respectively. A non-dominated multi-objective moth flame optimization technique is used for the optimization issue. The fuzzy decision-making approach is applied for extracting the best compromise solution. The results are validated though adapted IEEE-30 bus test system, which is incorporated with wind and solar generating plants.", "Keywords": "meta-heuristics;probability density function;solar PV energy;stochastic;wind units", "DOI": "10.12928/telkomnika.v18i3.13466", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Sundaram B<PERSON>", "Affiliation": "S. V. National Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "S. V. National Institute of Technology"}], "References": []}, {"ArticleId": 82556273, "Title": "Maximising system throughput in wireless powered sub-6 GHz and millimetre-wave 5G heterogeneous networks", "Abstract": "Millimetre wave (mm-Wave) bands and sub-6 GHz are key technologies in solving the spectrum critical situation in the fifth generation (5G) wireless networks in achieving high throughput with low transmission power. This paper studies the performance of dense small cells that involve a millimetre wave (mm-Wave) band and sub-6 GHz that operate in high frequency to support massive multiple-input-multiple-output systems (MIMO). In this paper, we analyse the propagation path loss and wireless powered transfer for a 5G wireless cellular system from both macro cells and femtocells in the sub-6 GHz (µWave) and mm-Wave tiers. This paper also analyses the tier heterogeneous in downlink for both mm-Wave and sub-6 GHz. It further proposes a novel distributed power to mitigate the inter-beam interference directors and achieve high throughput under game theory-based power constraints across the sub-6 GHz and mm-Wave interfaces. From the simulation results, the proposed distributed powers in femtocell suppresses inter-beam interference by minimising path loss to active users (UEs) and provides substantial power saving by controlling the distributed power algorithm to achieve high throughput.", "Keywords": "active users;fifth generation (5G);massive MIMO;mm-wave;throughput", "DOI": "10.12928/telkomnika.v18i3.15049", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON> (UTHM)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Jeddah"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Tun <PERSON>"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Putra Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "South Ural State University"}], "References": []}, {"ArticleId": 82556274, "Title": "Design of high gain dual T-shaped stub antenna for satellite communication", "Abstract": "The ultra wide band (UWB) antennas play a vital role in supporting different wireless standards and are suitable for wide variety of applications. This paper is aimed to present a novel UWB dual notch microstrip antenna with modified ground plane. The antenna is designed to operate in UWB ranging from 2 GHz to 12 GHz with multi band operation. This will help in operating the antenna for different operations independently. The proposed structure will operate in two notch bands 3.3-4 GHz (Wi-MAX), 5.05-5.9 GHz (WLAN) and the structure is suitable for long distance communications because of its increased directivity. The structure can also be used for X-Band applications for various applications of traffic control, weather forecasting and vehicle speed detection systems. It is observed that, the proposed structure is offering a gain of 5.2 dBi with improved directivity with a beam width of 42.230. This makes the antenna structure suitable for long distance satellite communications. The antenna is supporting the circular polarization at higher the frequencies and can be useful for the upcoming 5G mobile applications. Moreover, the proposed structure offers the less interference at the receiver. The structure is found to be smaller in dimensions, easily fabricated at low costs and can be integrated into any compact wireless devices. The structure is simulated using a commercially available software Ansys-HFSS and is analyzed.", "Keywords": "5G communications;band width;dual T-shaped stub;high directivity;high gain;satellite applications", "DOI": "10.12928/telkomnika.v18i3.14992", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Vardhaman College of Engineering"}, {"AuthorId": 2, "Name": "D. M. K. Chi<PERSON>", "Affiliation": "Vardhaman College of Engineering"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Lendi Institute of Engineering & Technology"}], "References": []}, {"ArticleId": 82556275, "Title": "Simple broadband circularly polarized monopole antenna with two asymmetrically connected U-shaped parasitic strips and defective ground plane", "Abstract": "A simple compact broadband circularly polarized monopole antenna, which comprises a simple monopole, a modified ground plane with an implementing triangular stub and two asymmetrically connected U-shaped parasitic strips, is proposed. Simulation results show that the proposed compact antenna (0.62λo×0.68λo) achieves a 10-dB impedance bandwidth (IBW) of 111% (1.7 to 5.95 GHz) and a 3-dB axial ratio bandwidth (ARBW) of 61% (3.3–6.2 GHz) with a peak gain between 2.9–4 dBi for the entire ARBW. With its broad IBW and ARBW, compact size and simple structure, the proposed antenna is suitable for different wireless communications.", "Keywords": "axial ratio (AR);broadband antenna;circularly polarised (CP);monopole antenna", "DOI": "10.12928/telkomnika.v18i3.14313", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}, {"AuthorId": 3, "Name": "A. A. M. Isa", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Multimedia Universty"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "University Teknikal Malaysia Melaka (UTeM)"}], "References": []}, {"ArticleId": 82556276, "Title": "Novel dependencies of currents and voltages in power system steady state mode on regulable parameters of three-phase systems symmetrization", "Abstract": "The unbalanced mode, negative/zero sequence, variation of real power are caused by the nonlinear or unbalanced loads increase the power transmission losses in distributing power systems and also harmful to the electric devices. Reactive power compensation is considered as the common methods for overcoming asymmetry. The critical issue in reactive power compensation is the optimal calculation of compensation values that is extremely difficult in complex circuits. We proposed a novel approach to overcome these difficulties by providing the creation of new analytical connections of the steady-state mode parameters (voltages, currents) depends on the controlled parameter for the arbitrary circuits. The base of our approach to reactive power compensation is the fractional-polynomial functions. We present a new description of the behavior of voltages and currents depending on the controlled parameters of the reactive power compensation devices, and we prove its effectiveness.", "Keywords": "fractional-polynomial function;mesh current method;node voltage method;optimization;regulable parameter;symmetrization;three-phase systems", "DOI": "10.12928/telkomnika.v18i3.15113", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Phu Tran Tin", "Affiliation": "Industrial University of Ho Chi Minh City"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>n <PERSON> University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON>n <PERSON> University"}, {"AuthorId": 4, "Name": "Quang Sy Vu", "Affiliation": "Hong Bang International University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>n <PERSON> University"}], "References": []}, {"ArticleId": 82556277, "Title": "Comparison between piezoelectric transformer and electromagnetic transformer used in electronic circuits", "Abstract": "This paper presents study, modeling and simulation of the piezoelectric material works as transformer (piezoelectric transformer (PT)) in power electronic circuits, comparisons are made with the regular transformer (iron core) works in the same circuit, the tested circuit is the full bridge converter which used in the simulation as dc power supply circuit. As a result, a detailed simulation for both the piezoelectric transformer and traditional transformer are achieved, as well as the output voltage from the dc power supply is tested by varying the load resistance. The dc power supply circuit has been simulated using PSIM (V9.1) power electronic circuit simulation software.", "Keywords": "piezoelectric material;piezoelectric transformer;PZT", "DOI": "10.12928/telkomnika.v18i3.14334", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Wedian <PERSON><PERSON>", "Affiliation": "Mustansiriyah University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mustansiriyah University"}, {"AuthorId": 3, "Name": "Ammar Al-Gizi", "Affiliation": "Mustansiriyah University"}], "References": []}, {"ArticleId": ********, "Title": "Image processing analysis of sigmoidal Hadamard wavelet with PCA to detect hidden object", "Abstract": "Innovative tactics are employed by terrorists to conceal weapons and explosives to perpetrate violent attacks, accounting for the deaths of millions of lives every year and contributing to huge economic losses to the global society. Achieving a high threat detection rate during an inspection of crowds to recognize and detect threat elements from a secure distance is the motivation for the development of intelligent image data analysis from a machine learning perspective. A method proposed to reduce the image dimensions with support vector, linearity and orthogonal. The functionality of CWD is contingent upon the plenary characterization of fusion data from multiple image sensors. The proposed method combines multiple sensors by hybrid fusion of sigmoidal Hadamard wavelet transform and PCA basis functions. Weapon recognition and the detection system, using Image segmentation and K means support vector machine A classifier is an autonomous process for the recognition of threat weapons regardless of make, variety, shape, or position on the suspect’s body despite concealment.", "Keywords": "concealed weapon detection;IR image;principle component analysis;sigmoidal hadamard;support vocter machine", "DOI": "10.12928/telkomnika.v18i3.13541", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Furat Al-Awsat Technical University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Imam Al- University College Najaf"}], "References": []}, {"ArticleId": 82556279, "Title": "Architecture of the global navigation satellite system for maritime applications", "Abstract": "This paper introduces architecture of the global navigation satellite system (GNSS) networks in the function of the maritime space communications, navigation and surveillance (CNS) for enhanced navigation and positioning of vessels deploying passive, active and hybrid global determination satellite systems (GDSS) networks. These GNSS networks have to enhance safety and control oceangoing ships in navigation across the ocean and inland waters, to improve logistics and freight of goods, security of crew and passengers onboard ships. The maritime GNSS networks integrated with geostationary earth orbit (GEO) satellite constellations are providing important global satellite augmentation systems (GSAS) architecture, which is established by two first generations known GNSS as GNSS-1 infrastructures. The GNSS-1 network is the composition of two subnets such as the US global position system (GPS) and Russian global satellite navigation system (GLONASS). Both GNSS-1 networks play a significant contribution in very precise timing, tracking, guidance, determination and navigation of the oceangoing ships. At this point, both GNSS-1 networks, GPS and GLONASS, are used in maritime and many other mobile and fixed applications to provide enhanced accuracy and high integrity monitoring usable for positioning of the oceangoing ships. To provide improvements of GNSS-1 network it will be necessary to carry out their augmentation within several regional satellite augmentation systems (RSAS) as integration parts of GSAS infrastructures.", "Keywords": "GLONASS;CNS;GNSS;GPS;GSAS;passive/active/hybrid GDSS;PVT;RSAS", "DOI": "10.12928/telkomnika.v18i3.15640", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Dimov Stojce Ilcev", "Affiliation": "Durban University of Technogy"}], "References": []}, {"ArticleId": 82556280, "Title": "Architecture of the regional satellite augmentation system for maritime applications", "Abstract": "This paper describes architecture of regional satellite augmentation system (RSAS) in the function of the maritime space communications, navigation and surveillance (CNS) and global navigation satellite systems (GNSS) networks for enhanced safety and surveying of oceangoing ships, management and tracking of cargo, security of Mariners onboard commercial and passenger ships, yachts, sea platforms and other types of craft. The RSAS network are designed to improve vessel management and transport operation because of the enormous expansion of the world's merchant fleet. However, this network with a special ship tracking system can also improve the protection of merchant ships and their crews against piracy, violence, robbery and terrorist attacks. The international maritime organization (IMO) and shipping flag states have project for development of the international ship and port security (ISPS) and design to implement an approaching and port control system (APCS) by special code for all merchant vessels including determination, tracking and positioning of all ships movements in and out of the seaport area. The Maritime RSAS and CNS systems are integration components of the global satellite augmentation systems (GSAS) of two operational GNSS-1 military networks, such as the US global position system (GPS) and Russian global satellite navigation system (GLONASS). In this paper are also introduced the special effects of the ships RSAS networks and coastal movement guidance and control (CMGC) system for maritime application at sea and in seaports areas.", "Keywords": "CMGC;CNS;CNSO;GNSS;ISPS;PVT;RSAS", "DOI": "10.12928/telkomnika.v18i3.15641", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Dimov Stojce Ilcev", "Affiliation": "Durban University of Technogy"}], "References": []}, {"ArticleId": 82556281, "Title": "Contour evolution method for precise boundary delineation of medical images", "Abstract": "Image segmentation is an important precursor to boundary delineation of medical images. One of the major challenges in applying automatic image segmentation in medical images is the imperfection in the imaging process which can result in inconsistent contrast and brightness levels, and low image sharpness and vanishing boundaries. Although recent advances in deep learning produce vast improvements in the quality of image segmentation, the accuracy of segmentation around object boundaries still requires improvement. We developed a new approach to contour evolution that is more intuitive but shares some common principles with the active contour model method. The method uses two concepts, namely the boundary grid and sparse boundary representation, as an implicit and explicit representation of the boundary points. We tested our method using lumbar spine MRI images of 515 patients. The experiment results show that our method performs up to 10.2 times faster and more flexible than the geodesic active contours method. Using BF-score contour-based metric, we show that our method improves the boundary accuracy from 74% to 84% as opposed to 63% by the latter method.", "Keywords": "boundary delineation;contour evolution;image segmentation;MRI images", "DOI": "10.12928/telkomnika.v18i3.14746", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Multimedia Nusantara"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Multimedia Nusantara"}, {"AuthorId": 3, "Name": "Nunik Afriliana", "Affiliation": "Universitas Multimedia Nusantara"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universitas Multimedia Nusantara"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Multimedia Nusantara"}], "References": []}, {"ArticleId": 82556282, "Title": "Fractal analysis of electrical tree grown in silicone rubber nanocomposites", "Abstract": "Electrical treeing is one of the main reasons for long-term degradation of high voltage insulation especially in the cable accessory which commonly made from silicone rubber due to non-uniformly structures of the cable accessories. Recently, the combination of nanofillers with the silicone rubber matrix can reduce the possibility of the electrical treeing to grow further by changing its patterns and slow-down its propagation. However, the influences of nanofillers on the tree hindrance and its patterns are not well understood. This paper explores the influence of nanofiller on tree pattern in silicon rubber. The electrical tree patterns were characterized using fractal analysis. The box-counting method was used to measure the fractal dimension and lacunarity to obtain the structure of the tree pattern during the electrical tree growth. The structure of the electrical tree in silicone rubber nanocomposites has higher fractal dimension and lacunarity. Sample with nanofiller possesses dominant fractal dimension of tree growth compared to the sample without nanofiller.", "Keywords": "electrical tree;fractal analysis;nanocomposites;silicone rubber", "DOI": "10.12928/telkomnika.v18i3.13389", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Sriwijaya"}, {"AuthorId": 2, "Name": "M. A. B. Sidik", "Affiliation": "Universitas Sriwijaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Universitas Sriwijaya"}, {"AuthorId": 4, "Name": "R. F. Kurnia", "Affiliation": "Universitas Sriwijaya"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}], "References": []}, {"ArticleId": 82556283, "Title": "OFDM synchronization system using wavelet transform for symbol rate detection", "Abstract": "In radio communications, using wavelet signal analysis to recover the symbol rate timing clock of orthogonal frequency-division multiplexing (OFDM) is a new approach that can tolerate signal distortion from intersymbol interference (ISI) and intercarrier interference of encoding digital data on multiple carrier frequencies. Typically, the reception synchronization with wavelet signal analysis in OFDM can improve the performance over the fourier transform-based OFDM. However, a synchronization procedure that is stable against distortion and noise is essential to diminish the symbol synchronization establishment and operation sampling period. In this paper, we propose an OFDM synchronization system and analyze the impact of the wavelet denoise procedure on the OFDM system, which extracts the symbol rate of the OFDM frame. The evaluation results show that the proposed system can optimize the frequency window size to enable an efficient timing and frequency offset estimation with high and stable performance in terms of bit error rate (BER) and Frame Error Rate (FER) especially when the value of EbN0 (a normalized signal-to-noise ratio SNR measure) is greater than 8 dB, thanks to the wavelet transform.", "Keywords": "orthogonal frequency-division multiplexing (OFDM);symbol rate detection;synchronization;timing recovery system (TRS);wavelet", "DOI": "10.12928/telkomnika.v18i3.14834", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "President <PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Waseda University"}], "References": []}, {"ArticleId": 82556285, "Title": "Editor’s Note", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10766-020-00666-y", "PubYear": 2020, "Volume": "48", "Issue": "4", "JournalId": 12927, "JournalTitle": "International Journal of Parallel Programming", "ISSN": "0885-7458", "EISSN": "1573-7640", "Authors": [], "References": []}, {"ArticleId": 82556286, "Title": "Correction to: Impacts of High Resolution Data on Traveler Compliance Levels in Emergency Evacuation Simulations", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13177-020-00227-0", "PubYear": 2020, "Volume": "18", "Issue": "3", "JournalId": 6726, "JournalTitle": "International Journal of Intelligent Transportation Systems Research", "ISSN": "1348-8503", "EISSN": "1868-8659", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computational Science and Engineering Division, Oak Ridge National Laboratory, Oak Ridge, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil & Environmental Engineering, The University of Tennessee, Knoxville, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computational Science and Engineering Division, Oak Ridge National Laboratory, Oak Ridge, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computational Science and Engineering Division, Oak Ridge National Laboratory, Oak Ridge, USA"}], "References": []}, {"ArticleId": 82556290, "Title": "Reduce the probability of blocking for handoff and calls in cellular systems based on fixed and dynamic channel assignment", "Abstract": "In cellular systems the high probability of blocking represents a big problem for users, The proposed solution by reducing the blocking probability and investigation cellular systems by method channels assignment. The aim from apaper is studying the effect the channel assignment on the value of blocking probability. The results showed that the fixe channeld assignment gives a large probability of blocking for high loads, While  (FCA) reduce probability of blocking for handoff and calls according to cluster size. The cellular system representation in the case of (DCA), in (3-cell reuse) and (7-cell reuse), the results showed the first best way to reduce blocking probability and lead to reduce to approximately zero when loads that are less than 200%. Increasing  the cluster size causes to reduce blocking  probability. the results showed that the probability blocking for handoff  less than from probability of  blocking for new calls.", "Keywords": "blocking probability;dynamic channel assignment;fixed channel assignment;handoff;traffic load", "DOI": "10.12928/telkomnika.v18i4.15729", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tikrit University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tikrit University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tikrit University"}], "References": []}, {"ArticleId": 82556292, "Title": "Measurements to design a coverage area by using high altitude platform systems", "Abstract": "This paper proposes the principles of how to design UMTS coverage area for Baghdad city the capital of Iraq country that occupy space about 204.2 km², by using new and promising technology for providing wireless narrowband and broadband telecommunication services as well as broadcasting services with either airships or airplanes which is named HAPs, Viewed from its altitude, HAPs floats within the stratosphere layer in the airspace, positioned between satellite and terrestrial platforms.this study also consider the affect of interference with the current broadband technology It will start with brief introduction for HAPS with its advantages, comparison between HAPS system and other services and specify requirements for design. Such as, enumerate the center of coverage area to find the coordinates. Then, supposed the coverage area for the city, and find the radius, elevation angle, and the location of earth stations which will connect HAPS with other networks and all other services location depending on latitude and longitude, finally the reduction of interference technique.", "Keywords": "elevation angle;latitude;line of sight;longitude", "DOI": "10.12928/telkomnika.v18i4.14541", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "kurdustan technical institute"}], "References": []}, {"ArticleId": 82556298, "Title": "The design of a smart home controller based on ADALINE", "Abstract": "This paper proposes a prototype of an improved smart home controller that implements a neural network-based algorithm for enabling the controller to make decisions and act based on the current condition. Unlike previous approaches, this design also utilizes the use of IoT (internet of thing) technology and neural network based-algorithm for developing the controller. Since a smart home is equipped with various sensors, actuators, smart appliances, and mobile terminals, all of these devices need to be connected to the Internet to be able to communicate and provide services for its occupants. The construction of the proposed controller is carried out through several procedures, i.e. the implementation of the ADALINE (adaptive linear) as the neural network method, the design of the smart home controller prototype, and the validation process using mean average percentage error (MAPE) calculation. This prototype integrates functionalities of several household appliances into one application controlled by a smartphone. ADALINE is applied as an algorithm to predict output when the controller is in automatic mode. Although the obtained accuracy value is still not satisfactory, the value is bound to change when testing on more data. The work published in this paper may encourage the implementation of smart technology in more households in Indonesia.", "Keywords": "ADALINE;home automation;IoT;neural network;smart home", "DOI": "10.12928/telkomnika.v18i4.14893", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sekolah Tinggi Teknik PLN"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sekolah Tinggi Teknik PLN"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sekolah Tinggi Teknik PLN"}, {"AuthorId": 4, "Name": "Indrianto Indrianto", "Affiliation": "Sekolah Tinggi Teknik PLN"}], "References": []}, {"ArticleId": 82556300, "Title": "Ant-colony and nature-inspired heuristic models for NOMA systems: a review", "Abstract": "The increasing computational complexity in scheduling the large number of users for non-orthogonal multiple access (NOMA) system and future cellular networks lead to the need for scheduling models with relatively lower computational complexity such as heuristic models. The main objective of this paper is to conduct a concise study on ant-colony optimization (ACO) methods and potential nature-inspired heuristic models for NOMA implementation in future high-speed networks. The issues, challenges and future work of ACO and other related heuristic models in NOMA are concisely reviewed. The throughput result of the proposed ACO method is observed to be close to the maximum theoretical value and stands 44% higher than that of the existing method. This result demonstrates the effectiveness of ACO implementation for NOMA user scheduling and grouping.", "Keywords": "ant-colony;heuristic;orthogonal", "DOI": "10.12928/telkomnika.v18i4.14995", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 6, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Multimedia University"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}], "References": []}, {"ArticleId": 82556301, "Title": "Optimal cost allocation algorithm of transmission losses to bilateral contracts", "Abstract": "One of the trends in electricity reform is the involvement of bilateral contracts that will participate in electricity business development. Bilateral agreements require fair transmission loss costs compared with the integrated power system. This paper proposes a new algorithm in determining the optimal allocation of transmission loss costs for bilateral contracts based on the direct method in economic load dispatch. The calculation for an optimal power flow applies fast decoupled methods. At the same time, the determination of a fair allocation of transmission losses uses the decomposition method. The simulation results of the optimal allocation of power flow provide comparable results with previous studies. This method produces a fair allocation of optimal transmission loss costs for both integrated and bilateral parties. The proportion allocation of the transmission lines loss incurred by the integrated system and bilateral contracts reflects a fair allocation of R. 852.589 and R. 805.193, respectively.", "Keywords": "bilateral contract transaction;direct method;economic dispatch;fuel cost;optimal loss cost", "DOI": "10.12928/telkomnika.v18i4.14226", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Negeri Bandung"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Bandung"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Bandung"}], "References": []}, {"ArticleId": 82556303, "Title": "Developing barcode scan system of a small-scaled reverse vending machine to sorting waste of beverage containers", "Abstract": "Reduce, Reuse, and Recycle is a campaign which aims to reduce the production of waste. Industry used plastic bottle and cans to store the beverage. A research was done by University of Georgia, United States and was published by Wall Street Journal stated that Indonesia is the second predicate country which produced and mismanaged plastic waste in the world. This condition shoud be overcome and this research project was intended to develop reverse vending machine (RVM) to sorting waste of beverage containers either plastic bottles or cans as a campaign to reduce the production of waste. This RVM machine uses barcode scanning as the sorting system to determine whether the plastic bottle or can could be recycled or not. In order to check the weight of the beverage container, a load cell sensor is used to check whether the beverage container is empty or not. The machine will receive the container from the conveyor station, check the weight, and finally transfer it to the sorting station. The container will be sorted as cans or plastic bottle by the aid of barcode scanning and compare it to database. Furthermore, the plastic bottle will be sorted as clear or colored plastic bottle. Unrecyclable plastic or can container or any unemptied container will be classified as rejected container and be returned to the user through the outlet passage. The performance testing was done with 12 different types of plastic bottle and can and 10 samples for each type, so there were total 120 items tested and the result showed that the success rate was 94% while the processing time was varying in between 8 to 13 seconds.", "Keywords": "barcode scanner;beverage container;load cell;reverse vending machine (RVM)", "DOI": "10.12928/telkomnika.v18i4.14776", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Swiss German University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Swiss German University"}], "References": []}, {"ArticleId": 82556306, "Title": "Simulation and optimization of a tuneable rectangular microstrip patch antenna based on hybrid metal-graphene and FSS superstrate for fifth-generation applications", "Abstract": "In this paper, a tuneable rectangular microstrip patch antenna (MPA) is simulated and optimized to operate in four frequency bands of the next generation of wireless communication systems. The proposed design incorporates a copper radiating patch with four implanted graphene strips for tuning purposes. The reconfigurable surface impedance of graphene can easily be altered by applying a DC voltage bias directly to the graphene strips, allowing the operating frequency of the antenna to be tuned as desired. The capability of the applied voltage to tune the operating frequency band of the proposed antenna is studied via computer simulation technology (CST) microwave studio (MWS). Frequency selective surfaces (FSSs) are introduced in order to improve the radiation parameters of the antenna. The operating frequency band of the tuneable rectangular MPA increases directly as the applied DC voltage bias is increased. Based on the simulation results, a tuneable rectangular MPA placed between two FSSs is proposed for fifth-generation applications.", "Keywords": "60 GHz;5G;FSSs;graphene;microstrip patch antenna;tuneable antenna", "DOI": "10.12928/telkomnika.v18i4.14988", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Ahliyya Amman University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Al-Esraa University College"}], "References": []}, {"ArticleId": 82556307, "Title": "Early detection of breast cancer using mammography images and software engineering process", "Abstract": "The breast cancer has affected a wide region of women as a particular case. Therefore, different researchers have focused on the early detection of this disease to overcome it in efficient way. In this paper, an early breast cancer detection system has been proposed based on mammography images. The proposed system adopts deep-learning technique to increase the accuracy of detection. The convolutional neural network (CNN) model is considered for preparing the datasets of training and test. It is important to note that the software engineering process model has been adopted in constructing the proposed algorithm. This is to increase the reliably, flexibility and extendibility of the system. The user interfaces of the system are designed as a website used at country side general purpose (GP) health centers for early detection to the disease under lacking in specialist medical staff. The obtained results show the efficiency of the proposed system in terms of accuracy up to more than 90% and decrease the efforts of medical staff as well as helping the patients. As a conclusion, the proposed system can help patients by early detecting the breast cancer at far places from hospital and referring them to nearest specialist center.", "Keywords": "breast cancer;deep-learning;software engineering;pattern recognition;website design", "DOI": "10.12928/telkomnika.v18i4.14718", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Technology-Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Technology-Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Technology-Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Technology-Iraq"}], "References": []}, {"ArticleId": 82556308, "Title": "QR code based authentication method for IoT applications using three security layers", "Abstract": "A quick response code-based authentication method (QRAM) is proposed. QRAM is applicable for lots of internet of things (IoT) applications. QRAM aims to verify requests of such an access to IoT applications. Requests are made using a quick response code (QRC). To authenticate contents of QRC, users will scan QRC to access IoT applications. To authenticate contents of QRC, three procedures are applied. QRAM contributes to IoT automatic access systems or smart applications in terms of authentication and safety of access. QRAM is evaluated in term of security factors (e.g., authentication). Computation time of authentication procedures for several IoT applications has become a considerable issue. QRAM aims to reduce computation time consumed to authenticate each QRC. Some authentication techniques still face difficulties when an IoT application requires fast response to users; therefore, QRAM aims to enhance so to meet real-time applications. Thus, QRAM is compared to several competitive methods used to verify QRC in term of computation time. Results confirmed that QRAM is faster than other competitive techniques. Besides, results have shown a high level of complexity in term of decryption time needed to deduce private contents of QRC. QRAM also is robust against unauthorized requests of access.", "Keywords": "data authentication;data security;internet of things;QR code", "DOI": "10.12928/telkomnika.v18i4.14748", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universiti Tenaga Nasional (UNITEN)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tenaga Nasional (UNITEN)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Tenaga Nasional (UNITEN)"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tenaga Nasional (UNITEN)"}], "References": []}, {"ArticleId": 82556309, "Title": "Modified moth swarm algorithm for optimal economic load dispatch problem", "Abstract": "In this study, optimal economic load dispatch problem (OELD) is resolved by a novel improved algorithm. The proposed modified moth swarm algorithm (MMSA), is developed by proposing two modifications on the classical moth swarm algorithm (MSA). The first modification applies an effective formula to replace an ineffective formula of the mutation technique. The second modification is to cancel the crossover technique. For proving the efficient improvements of the proposed method, different systems with discontinuous objective functions as well as complicated constraints are used. Experiment results on the investigated cases show that the proposed method can get less cost and achieve stable search ability than MSA. As compared to other previous methods, MMSA can archive equal or better results. From this view, it can give a conclusion that MMSA method can be valued as a useful method for OELD problem.", "Keywords": "discontinuous objective function;modified moth swarm algorithm;optimal economic load dispatch", "DOI": "10.12928/telkomnika.v18i4.15032", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Saigon University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>hu <PERSON> College of Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Industrial University of Ho Chi Minh City"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Ton <PERSON> university"}], "References": []}, {"ArticleId": 82556310, "Title": "Comparative performance of optical amplifiers: Raman and EDFA", "Abstract": "The in-line optical signal amplification is often used in optical communication systems to accomplish longer transmission distances and larger capacity. In this proposed paper, the operation of two types of optical amplifiers for 16×10 Gbps wavelength division multiplexing system had been examined by changing transmission distance from 10 to 200 km with a dispersion equals to 16.75 ps/nm/km. The analysis and design of such systems ordinarily includes many signal channels, nonlinear devices, several topologies with many noise sources, is extremely complex and effort-exhaustive. Therefore, theoretical studies with simulation CAD software of systems are become necessity to predict and optimize system performance. The comparison between EDFA and Raman has already explored by many researchers in varying ways in this work and to achieve obove objectives, the OptiSystem software was used to design the proposed fiber optic communications system and to simulate results. Performance for the present system was evaluated for parameters like bit error rate (BER), quality factor (QF), total gain with eye opening factor. It was saw that EDFA provides better results, in the maximal transmission distance 64% better than Raman amplifier, 57.5% for gain and 26.7% for maximum quality factor. As a future study a hybrid amplifier can produce better quality of amplification.", "Keywords": "BER;EDFA;eye opening;quality factor;Raman", "DOI": "10.12928/telkomnika.v18i4.15706", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northern Technical University"}, {"AuthorId": 2, "Name": "Basma M<PERSON>", "Affiliation": "Northern Technical University"}], "References": []}, {"ArticleId": 82556311, "Title": "Arabic digits speech recognition and speaker identification in noisy environment using a hybrid model of VQ and GMM", "Abstract": "This paper presents an automatic speaker identification and speech recognition for Arabic digits in noisy environment. In this work, the proposed system is able to identify the speaker after saving his voice in the database and adding noise. The mel frequency cepstral coefficients (MFCC) is the best approach used in building a program in the Matlab platform; also, the quantization is used for generating the codebooks. The Gaussian mixture modelling (GMM) algorithms are used to generate template, feature-matching purpose. In this paper, we have proposed a system based on MFCC-GMM and MFCC-VQ Approaches on the one hand and by using the Hybrid Approach MFCC-VQ-GMM on the other hand for speaker modeling. The White Gaussian noise is added to the clean speech at several signal-to-noise ratio (SNR) levels to test the system in a noisy environment. The proposed system gives good results in recognition rate.", "Keywords": "Arabic digits;GMM;MFCC;SNR;VQ", "DOI": "10.12928/telkomnika.v18i4.14215", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "LAC Laboratory Caen-Normandie University"}], "References": []}, {"ArticleId": 82556312, "Title": "QR code based two-factor authentication to verify paper-based documents", "Abstract": "Important paper-based documents Exposed to forgery such as: official certificates, birth, marriage, death certificates, selling and buying documents and other legal documents is more and more serious and sophisticated. With the purposes of fraud, appropriation of property, job application and assignment in order to swindle public authorities, this forgery has led to material loss, belief deterioration as well as social instability. There are many techniques has been proposed to overcome this issues such as: ink stamps, live signatures, documented the transaction in third party like the court or notary. In this paper, it’s proposed a feasible solution for forgery prevention for paper-based documents using cloud computing application. With the application of Quick Response bidirectional barcode and the usage of hash algorithm. The study aims at developing an electronic verification system for official and issued books (documents, endorsements, and other official books) to/from different sections of the Institute using QR technology.", "Keywords": "authentication methods;cloud computing;e-Government;hash algorithm;QR code", "DOI": "10.12928/telkomnika.v18i4.14339", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology University of Basrah"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Technology University of Basrah"}, {"AuthorId": 3, "Name": "<PERSON>der M<PERSON> Al-<PERSON>", "Affiliation": "Technology University of Basrah"}], "References": []}, {"ArticleId": 82556313, "Title": "Machine learning based lightweight interference mitigation scheme for wireless sensor network", "Abstract": "The interference issue is most vibrant on low-powered networks like wireless sensor network (WSN). In some cases, the heavy interference on WSN from different technologies and devices result in life threatening situations. In this paper, a machine learning (ML) based lightweight interference mitigation scheme for WSN is proposed. The scheme detects and identifies heterogeneous interference like Wifi, bluetooth and microwave oven using a lightweight feature extraction method and ML lightweight decision tree. It also provides WSN an adaptive interference mitigation solution by helping to choose packet scheduling, Acknowledgement (ACK)-retransmission or channel switching as the best countermeasure. The scheme is simulated with test data to evaluate the accuracy performance and the memory consumption. Evaluation of the proposed scheme’s memory profile shows a 14% memory saving compared to a fast fourier transform (FFT) based periodicity estimation technique and 3% less memory compared to logistic regression-based ML model, hence proving the scheme is lightweight. The validation test shows the scheme has a high accuracy at 95.24%. It shows a precision of 100% in detecting WiFi and microwave oven interference while a 90% precision in detecting bluetooth interference.", "Keywords": "interference;machine learning;RSSI", "DOI": "10.12928/telkomnika.v18i4.14879", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}], "References": []}, {"ArticleId": 82556314, "Title": "Spatial association discovery process using frequent subgraph mining", "Abstract": "Spatial associations are one of the most relevant kinds of patterns used by business intelligence regarding spatial data. Due to the characteristics of this particular type of information, different approaches have been proposed for spatial association mining. This wide variety of methods has entailed the need for a process to integrate the activities for association discovery, one that is easy to implement and flexible enough to be adapted to any particular situation, particularly for small and medium-size projects to guide the useful pattern discovery process. Thus, this work proposes an adaptable knowledge discovery process that uses graph theory to model different spatial relationships from multiple scenarios, and frequent subgraph mining to discover spatial associations. A proof of concept is presented using real data.", "Keywords": "frequent subgraph mining;SARM;spatial association mining;spatial data mining;spatial knowledge discovery", "DOI": "10.12928/telkomnika.v18i4.13858", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Nacional de La Plata"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Lanús"}], "References": []}, {"ArticleId": 82556315, "Title": "Green coffee beans feature extractor using image processing", "Abstract": "This study offers a novel solution to deal with the low signal-to-noise ratio and slow execution rate of the first derivative edge detection algorithms namely, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> algorithms. Since the two problems are brought about by the complex mathematical operations being used by the algorithms, these were replaced by a discriminant. The developed discriminant, equivalent to the product of total difference and intensity divided by the normalization values, is based on the “pixel pair formation” that produces optimal peak signal to noise ratio. Results of the study applying the discriminant for the edge detection of green coffee beans shows improvement in terms of peak signal to noise ratio (PSNR), mean square error (MSE), and execution time. It was determined that accuracy level varied according to the total difference of pixel values, intensity, and normalization values. Using the developed edge detection technique led to improvements in the PSNR of 2.091%, 1.16 %, and 2.47% over <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> respectively. Meanwhile, improvement in the MSE was measured to be 13.06%, 7.48 %, and 15.31% over the three algorithms. Likewise, improvement in execution time was also achieved at values of 69.02%, 67.40 %, and 65.46% over <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> respectively.", "Keywords": "image processing;new edge detection algorithm;<PERSON><PERSON><PERSON>;<PERSON>;<PERSON><PERSON>", "DOI": "10.12928/telkomnika.v18i4.13968", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cavite State University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Manuel L<PERSON> University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technological Institute of the Philippines"}], "References": []}, {"ArticleId": 82556316, "Title": "End-effector wheeled robotic arm gaming prototype for upper limb coordination control in home-based therapy", "Abstract": "The stroke patient will be having difficulty to control their upper limb, which causes their handling to become weak and unorganized. Conventional therapy designed to retrain the subject ability when it's losses due to stroke. As previous study concern that a subject with strong motivation and concentration of treatment tends to recover better than people who don't follow the program. This research focused on intensifying the training by providing user with Wheel robotic arm to train their upper limb. User will be asked to do exercising on moving particular objects to another position repetitively during a specific period. The robot will help the user to assist and relearn their motoric skill and improve muscle strength and coordination. The result of training is quite convincing when the user gives a positive response toward the practice. Around 86 percent of subject likely prefer the proposed system as their home-based rehabilitation system. The Anova testing with alpha 0.05 shows that there is no significant difference between trained subject and untrained subject on operating the wheeled robotic arm. It's mean the proposed system is reliable and user friendly to be used without an assistant so user can have more flexibility and improve their accomplishment on regaining their motoric skills. The future work will focus on stroke patient testing with more challenge and obstacle in enhancing the effectiveness of the stroke rehabilitation system.", "Keywords": "character recognition;child education;edutainment;intelligence;robotics", "DOI": "10.12928/telkomnika.v18i4.3775", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "King <PERSON><PERSON><PERSON> University"}], "References": []}, {"ArticleId": 82556317, "Title": "Medium term load demand forecast of Kano zone using neural network algorithms", "Abstract": "Electricity load forecasting refers to projection of future load requirements of an area or region or country through appropriate use of historical load data. One of several challenges faced by the Nigerian power distribution sectors is the overloaded power distribution network which leads to poor voltage distribution and frequent power outages. Accurate load demand forecasting is a key in addressing this challenge. This paper presents a comparison of generalized regression neural network (GRNN), feed-forward neural network (FFNN) and radial basis function neural network for medium term load demand estimation. Experimental data from Kano electricity distribution company (KEDCO) were used in validating the models. The simulation results indicated that the neural network models yielded promising results having achieved a mean absolute percentage error (MAPE) of less than 10% in all the considered scenarios. The generalization capability of FFNN is slightly better than that of RBFNN and GRNN model. The models could serve as a valuable and promising tool for the forecasting of the load demand.", "Keywords": "capability;layer;load;neural network;weight", "DOI": "10.12928/telkomnika.v18i4.14032", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bayero University Kano"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Kano University of Science and Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Bayero University Kano"}], "References": []}, {"ArticleId": 82556318, "Title": "GWO-based estimation of input-output parameters of thermal power plants", "Abstract": "The fuel cost curve of thermal generators was very important in the calculation of economic dispatch and optimal power flow. Temperature and aging could make changes to fuel cost curve so curve estimation need to be done periodically. The accuracy of the curve parameters estimation strongly affected the calculation of the dispatch. This paper aims to estimate the fuel cost curve parameters by using the grey wolf optimizer method. The problem of curve parameter estimation was made as an optimization problem. The objective function to be minimized was the total number of absolute error or the difference between the actual value and the estimated value of the fuel cost function. The estimated values of parameter that produce the smallest total absolute error were the values of final solution. The simulation results showed that parameter estimation using gray wolf optimizer method further minimized the value of objective function. By using three models of fuel cost curve and given test data, parameter estimation using grey wolf optimizer method produced the better estimation results than those estimation results obtained using least square error, particle swarm optimization, genetic algorithm, artificial bee colony and cuckoo search methods.", "Keywords": "fuel cost curve;grey wolf optimizer;input-output parameters;parameter estimation", "DOI": "10.12928/telkomnika.v18i4.12957", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Osea Zebua", "Affiliation": "University of Lampung"}, {"AuthorId": 2, "Name": "I <PERSON>", "Affiliation": "University of Mataram"}, {"AuthorId": 3, "Name": "I <PERSON>", "Affiliation": "University of Mataram"}], "References": []}, {"ArticleId": 82556320, "Title": "Semantics-based clustering approach for similar research area detection", "Abstract": "The manual process of searching out individuals in an already existing research field is cumbersome and time-consuming. Prominent and rookie researchers alike are predisposed to seek existing research publications in a research field of interest before coming up with a thesis. From extant literature, automated similar research area detection systems have been developed to solve this problem. However, most of them use keyword-matching techniques, which do not sufficiently capture the implicit semantics of", "Keywords": "k-means clustering;latent semantic indexing;Nigeria University;Ontology-based preprocessing;semantics-based clustering", "DOI": "10.12928/telkomnika.v18i4.15001", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Landmark University Omu Aran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Covenant University Ota"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Ogundokun", "Affiliation": "Landmark University Omu Aran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Landmark University Omu Aran"}, {"AuthorId": 5, "Name": "Peace Ayegba", "Affiliation": "Landmark University Omu Aran"}, {"AuthorId": 6, "Name": "Olufunke O. Oladipupo", "Affiliation": "Covenant University Ota"}], "References": []}, {"ArticleId": 82556321, "Title": "Rice seed image classiﬁcation based on HOG descriptor with missing values imputation", "Abstract": "Rice is a primary source of food consumed by almost half of world population. Rice quality mainly depends on the purity of the rice seed. In order to ensure the purity of rice variety, the recognition process is an essential stage. In this paper, we ﬁrstly propose to use histogram of oriented gradient (HOG) descriptor to characterize rice seed images. Since the size of image is totally random and the features extracted by HOG can not be used directly by classiﬁer due to the different dimensions. We apply several imputation methods to ﬁll the missing data for HOG descriptor. The experiment is applied on the VNRICE benchmark dataset to evaluate the proposed approach.", "Keywords": "HOG descriptor;KNN imputation;linear interpolation;missing value imputation;rice seed image classiﬁcation;zero imputation", "DOI": "10.12928/telkomnika.v18i4.14069", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ho <PERSON> Minh City Open University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ho <PERSON> Minh City Open University"}], "References": []}, {"ArticleId": 82556323, "Title": "A progressive domain expansion method for solving optimal control problem", "Abstract": "Electricity generation at the hydropower stations in Nigeria has been below the expected value. While the hydro stations have a capacity to generate up to 2,380 MW, the daily average energy generated in 2017 was estimated at around 846 MW. A factor responsible for this is the lack of a proper control system to manage the transfer of resources between the cascaded Kainji-Jebba Hydropower stations operating in tandem. This paper addressed the optimal regulation of the operating head of the Jebba hydropower reservoir in the presence of system constraints, flow requirement and environmental factors that are weather-related. The resulting two-point boundary value problem was solved using the progressive expansion of domain technique as against the shooting or multiple shooting techniques. The results provide the optimal inflow required to keep the operating head of the Jebba reservoir at a nominal level, hence ensuring that the maximum number of turbo-alternator units are operated.", "Keywords": "constraints, inflow;optimal control;operating head;Pontryagin", "DOI": "10.12928/telkomnika.v18i4.15047", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kwara State University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Petroleum Resources"}, {"AuthorId": 3, "Name": "Madugu I. Sani", "Affiliation": "Kano State University of Science and Technology Wudi"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Achievers University"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Ilorin"}], "References": []}, {"ArticleId": 82556325, "Title": "Vulnerabilities detection using attack recognition technique in multi-factor authentication", "Abstract": "Authentication is one of the essentials components of information security. It has become one of the most basic security requirements for network communication. Today, there is a necessity for a strong level of authentication to guarantee a significant level of security is being conveyed to the application. As such, it expedites challenging issues on security and efficiency. Security issues such as privacy and data integrity emerge because of the absence of control and authority. In addition, the bigger issue for multi-factor authentication is on the high execution time that leads to overall performance degradation. Most of existing studies related to multi-factor authentication schemes does not detect weaknesses based on user behavior. Most recent research does not look at the efficiency of the system by focusing only on improving the security aspect of authentication. Hence, this research proposes a new multi-factor authentication scheme that can withstand attacks, based on user behavior and maintaining optimum efficiency. Experiments have been conducted to evaluate this scheme. The results of the experiment show that the processing time of the proposed scheme is lower than the processing time of other schemes. This is particularly important after additional security features have been added to the scheme.", "Keywords": "attack recognition;efficiency;multi-factor authentication;security", "DOI": "10.12928/telkomnika.v18i4.14898", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Putra Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Tenaga Nasional"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Putra Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tenaga Nasional"}], "References": []}, {"ArticleId": 82556327, "Title": "Fair and trustworthy: Lock-free enhanced tendermint blockchain algorithm", "Abstract": "Blockchain Technology is exclusively used to make online transactions secure by maintaining a distributed and decentralized ledger of records across multiple computers. Tendermint is a general-purpose blockchain engine that is composed of two parts; Tendermint Core and the blockchain application interface. The application interface makes Tendermint suitable for a wide range of applications. In this paper, we analyze and improve Practical Byzantine Fault Tolerant (PBFT), a consensus-based Tendermint blockchain algorithm. In order to avoid negative issues of locks, we first propose a lock-free algorithm for blockchain in which the proposal and voting phases are concurrent whereas the commit phase is sequential. This consideration in the algorithm allows parallelism. Secondly, a new methodology is used to decide the size of the voter set which is a subset of blockchain nodes, further investigating the block sensitivity and trustworthiness of nodes. Thirdly, to fairly select the voter set nodes, we employ the random walk algorithm. Fourthly, we imply the wait-freedom property by using a timeout due to which all blocks are eventually committed or aborted. In addition, we have discussed voting conflicts and consensuses issues that are used as a correctness property, and provide some supportive techniques.", "Keywords": "blockchain protocol;consensus protocols;correctness;cryptocurrency;fairness;lock-free;smart contracts", "DOI": "10.12928/telkomnika.v18i4.15701", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jazan University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jazan University"}], "References": []}, {"ArticleId": 82556328, "Title": "The study of reducing the cost of investment in wind energy based on the cat swarm optimization with high reliability", "Abstract": "Wind and solar are the most important source of renewable energy for power supply in remote locations involves serious consideration of the reliability of these unconventional energy sources. We apply the cat swarm meta-heuristic optimization method to solve the problem of wind power system design optimization. The electrical power components of the system are characterized by their cost, capacity and reliability. This study seeks to optimize the design of parallel power systems in which multiple choices of generators wind, transformers and lines. Our plan has the advantage of allowing electrical components with different parameters to be customized in electrical power systems. The UMGF method is applied to allow rapid reliability estimation. A computer program is developed for the UMGF application and CS algorithm. An example is provided to explain.", "Keywords": "multi-statessystem;universal generating function grey wolf optimization reliability", "DOI": "10.12928/telkomnika.v18i4.15046", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dr. <PERSON><PERSON><PERSON> University of SAIDA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dr. <PERSON><PERSON><PERSON> University of SAIDA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dr. <PERSON><PERSON><PERSON> University of SAIDA"}], "References": []}, {"ArticleId": 82556329, "Title": "Identification of paddy leaf diseases based on texture analysis of Blobs and color segmentation", "Abstract": "There are three types of paddy leaf diseases that have similar symptoms, making it difficult for farmers to identify them, namely blast, brown-spot, and narrow brown-spot. This study aims to identification paddy plant diseases based on texture analysis of Blobs and color segmentation. Blobs analysis is used to get the number of objects, area and perimeter. Color segmentation is used to find out some color parameters of paddy leaf disease such as the color of the lesion boundary, the color of the spot of the lesion, and the color of the paddy leaf lesion. To get the best results, four methods have been chosen to obtained the threshold value, Otsu threshold value, variable threshold value, local threshold value and global threshold value. The best accuracy of the four methods using threshold variables is 90.7%. The results of this study indicate that the method used has been very satisfactory in identifying paddy plant disease.", "Keywords": "blast;blobs;brown-spot;color segmentation;narrow brown-spot", "DOI": "10.12928/telkomnika.v18i4.14614", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Islamic University of Sultan <PERSON><PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Islamic University of Sultan <PERSON><PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Islamic University of Sultan <PERSON><PERSON><PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Educational Quality Assurance Institutions of Riau Province"}], "References": []}, {"ArticleId": 82556331, "Title": "A simplified spatial Modulation MISO-OFDM scheme", "Abstract": "Index modulation is one of the promising techniques for future communications systems due to many improvement over the classical Orthogonal frequency division multiplexing systems such as single RF chain, increased throughput for the same modulation order, achieved tradeoff between the efficiencies of the power and the spectral, and elimination of inter-channel interference. Many forms of index modulation researches exist where symbols are conveyed in antennas, subcarriers, time slots, and the space-time matrix.  Spatial modulation is one member of index modulation family where symbols are conveyed in activating transmit/receive antennas. In this paper, a modification to a standard multiple input single output scheme by integrating spatial modulation using simplified mathematical procedure is achieved. In the transmitter side, data and activation symbols are distributed simultaneously using mathematical module and floor functions. At the receiver, a simplified maximum likelihood detector is used to obtain transmitted pair of symbols. To verify this, MATLAB SIMULINK is used to simulate a downlink system where spatial modulation is applied to a base station. Results for different transmit antenna number and modulation order are obtained in the form of bit error rate versus signal to noise ratio.", "Keywords": "index modulation;MISO-OFDM-SM;spatial modulation (SM)", "DOI": "10.12928/telkomnika.v18i4.13873", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Vian S. Al-Doori", "Affiliation": "Al-Rafidain University College"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Nahrain University"}], "References": []}, {"ArticleId": 82556333, "Title": "SVC device optimal location for voltage stability enhancement based on a combined particle swarm optimization-continuation power flow technique", "Abstract": "The increased power system loading combined with the worldwide power industry deregulation requires more reliable and efficient control of the power flow and network stability. Flexible AC transmission systems (FACTS) devices give new opportunities for controlling power and enhancing the usable capacity of the existing transmission lines. This paper presents a combined application of the particle swarm optimization (PSO) and the continuation power flow (CPF) technique to determine the optimal placement of static var compensator (SVC) in order to achieve the static voltage stability margin. The PSO objective function to be maximized is the loading factor to modify the load powers. In this scope, two SVC constraints are considered: the reference voltage in the first case and the total reactance and SVC reactive power in the second case. To test the performance of the proposed method, several simulations were performed on IEEE 30-Bus test systems. The results obtained show the effectiveness of the proposed method to find the optimal placement of the static var compensator and the improvement of the voltage stability.", "Keywords": "FACTS devices;optimal location;PSO-CPF technique;static var compensator;voltage stability", "DOI": "10.12928/telkomnika.v18i4.13073", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Oum El Fadhel Loubeba Bekri", "Affiliation": "Dr. <PERSON><PERSON><PERSON> University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Quebec in Chicoutimi UQAC"}, {"AuthorId": 3, "Name": "Tounsia Djamah", "Affiliation": "Mo<PERSON>ud Ma<PERSON>i University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "DjillaliLiabès University"}], "References": []}, {"ArticleId": 82556334, "Title": "Evaluation of load balancing approaches for Erlang concurrent application in cloud systems", "Abstract": "Cloud system accommodates the computing environment including PaaS (platform as a service), SaaS (software as a service), and IaaS (infrastructure as service) that enables the services of cloud systems.  Cloud system allows multiple users to employ computing services through browsers, which reflects an alternative service model that alters the local computing workload to a distant site. Cloud virtualization is another characteristic of the clouds that deliver virtual computing services and imitate the functionality of physical computing resources. It refers to an elastic load balancing management that provides the flexible model of on-demand services. The virtualization allows organizations to improve high levels of reliability, accessibility, and scalability by having a capability to execute applications on multiple resources simultaneously. In this paper we use a queuing model to consider a flexible load balancing and evaluate performance metrics such as mean queue length, throughput, mean waiting time, utilization, and mean traversal time. The model is aware of the arrival of concurrent applications with an Erlang distribution. Simulation results regarding performance metrics are investigated. Results point out that in Cloud systems both the fairness and load balancing are to be significantly considered.", "Keywords": "cloud virtualization;Erlang distribution function;load balancing;queueing network analysis;simulation", "DOI": "10.12928/telkomnika.v18i4.13150", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Chan<PERSON>orn Jittawiriyanu<PERSON>", "Affiliation": "Assumption University"}], "References": []}, {"ArticleId": ********, "Title": "RPL routing protocol performance under sinkhole and selective forwarding attack: experimental and simulated evaluation", "Abstract": "To make possible dream of connecting 30 billion smart devices assessable from anywhere, anytime and to fuel the engine growth of Internet of things (IoT) both in terms of physical and virtual things, Internet Engineering Task Force (IETF) came up with a concept of 6LoWPAN possessing characteristics like low power, bandwidth and cost. To bridge the routing gap and to collaborate between low power private area network and the outside world, IETF ROLL group proposed IPv6 based lightweight standard RPL (Routing protocol for low power and lossy networks). Due to large chunks of random data generated on daily basis security either externally or internally always remain bigger threat which may lead to devastation and eventually degrades the quality of service parameters affecting network resources. This paper evaluates and compare the effect of internal attacks like sinkhole and selective forwarding attacks on routing protocol for low power and lossy network topology. Widely known IoT operating system Contiki and Cooja as the simulator are used to analyse different consequences on low power and lossy network.", "Keywords": "6LoWPAN;Contiki;Cooja;internet of things (IoT);RPL;selective forwarding attack;sinkhole attack", "DOI": "10.12928/telkomnika.v18i4.15768", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Bimal H<PERSON>", "Affiliation": "Assistant Professor,\"CHARUSAT University\""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CHARUSAT University"}], "References": []}, {"ArticleId": ********, "Title": "Software engineering model based smart indoor localization system using deep-learning", "Abstract": "During the last few years, the allocation of objects or persons inside a specific building is highly required. It is well known that the global positioning system (GPS) cannot be adopted in indoor environment due to the lack of signals. Therefore, it is important to discover a new way that works inside. The proposed system uses the deep learning techniques to classify places based on capturing images. The proposed system contains two parts: software part and hardware part. The software part is built based on software engineering model to increase the reliability, flexibility, and scalability. In addition, this part, the dataset is collected using the Raspberry Pi III camera as training and validating data set. This dataset is used as an input to the proposed deep learning model. In the hardware part, Raspberry Pi III is used for loading the proposed model and producing prediction results and a camera that is used to collect the images dataset. Two wheels’ car is adopted as an object for introducing indoor localization project. The obtained training accuracy is 99.6% for training dataset and 100% for validating dataset.", "Keywords": "deep learning;CNN;GPS;indoor localization;Raspberry Pi;robotic car;software engineering", "DOI": "10.12928/telkomnika.v18i4.14318", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Technology-Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Technology-Iraq"}], "References": []}, {"ArticleId": 82556348, "Title": "Analysis sentiment about islamophobia when Christchurch attack on social media", "Abstract": "Islamophobia is formed by \"Islam\" with \"-phobia\" which means \"fear of Islam\". This shows the view of Islam as \"other\" and can threaten Western culture. The recent horrific terror attack that took place at the Christchurch mosque in New Zealand, is the result of allowing an attitude of hatred towards Islam in the West. Twitter is social media that allows users send real-time messages and can be used for sentiment analysis because it has a large amount of data. The lexical based method using VADER is used for automatic labeling of crawling data from Twitter. And then compare Supervised Machine Learning Naïve Bayes and SVM algorithm. Addition of SMOTE for Imbalanced Data. As result, SVM with SMOTE is proven the highest performance value and short processing time.", "Keywords": "Christchurch attack;islamophobia;Naïve Bayes;SMOTE;SVM;VADER", "DOI": "10.12928/telkomnika.v18i4.14179", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "STMIK Nusa Mandiri Jakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "STMIK Nusa Mandiri Jakarta"}], "References": []}, {"ArticleId": 82556350, "Title": "Study on transmission over Nakagami-m fading channel for multiple access scheme without orthogonal signal", "Abstract": "In this paper, a downlink performance in non-orthogonal multiple access (NOMA) system is considered. With regard to different priority for two NOMA users, we exploit the closed-form expressions of outage probability over wireless fading channel following Nakagami-m fading. The fixed power allocation factor scheme is examined to reduce the complexity in computation regarding performance analysis. In our analysis, perfect successive interference cancellation (SIC) is applied in order to achieve perfect signal decoding operation. Simulation results show that the considered NOMA downlink scheme is affected by transmit SNR, power allocation factors, fading parameters.", "Keywords": "NOMA;perfect SIC;Na<PERSON>gami-m fading;downlink", "DOI": "10.12928/telkomnika.v18i4.14227", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Chi-<PERSON><PERSON>", "Affiliation": "Industrial University of Ho Chi Minh City"}, {"AuthorId": 2, "Name": "Dinh-Thuan Do", "Affiliation": "Industrial University of Ho Chi Minh City (IUH)"}], "References": []}, {"ArticleId": 82556355, "Title": "Mobile cloud game in high performance computing environment", "Abstract": "Mobile cloud game is a solution to play high-end games in indigent thin clients with a diversity of end-user devices, and as real-time gaming, mobile cloud game hosting game engines in the cloud. Moreover, frequent change in network quality is another issue that should be limited to run the real fast cloud game. Thus, reliable software components between cloud and user devices as clients, including using artificial intelligence (AI) algorithms such as machine learning, deep learning and so on will enhance the game performance, particularly in multiplayer and real-time conditions. In this paper, we list the mobile cloud game architecture in the high-performance computing (HPC) environment, where a load of the game will be distributed between servers as cloud and clients. The server node as clouds or clients will consist of more than one server with many processors (cores) or sometimes can be recognized as distributed computing. Using HPC for cloud games will boost the game performance where the execution times will be dispersed not only in some node in servers and clients but in many cores of each server or client. The involvement of the internet of things (IoT) and ubiquitous access from heterogeneous devices will give benefit to enjoyment in the game itself.", "Keywords": "cloud gaming;distributed cloud game;HPC cloud game;HPC mobile cloud game;mobile cloud game", "DOI": "10.12928/telkomnika.v18i4.14896", "PubYear": 2020, "Volume": "18", "Issue": "4", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Raharja University"}, {"AuthorId": 2, "Name": "Ferry Sudarto", "Affiliation": "Raharja University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}], "References": []}, {"ArticleId": 82556381, "Title": "Crystal structure of Oryza sativa TDC reveals the substrate specificity for TDC-mediated melatonin biosynthesis", "Abstract": "Plant tryptophan decarboxylase (TDC) is a type II Pyridoxal-5′-phosphate-dependent decarboxylase (PLP_DC) that could be used as a target to genetically improve crops. However, lack of accurate structural information on plant TDC hampers the understanding of its decarboxylation mechanisms. In the present study, the crystal structures of Oryza sativa TDC ( O sTDC) in its complexes with pyridoxal-5′-phosphate, tryptamine and serotonin were determined. The structures provide detailed interaction information between TDC and its substrates. The Y359 residue from the loop gate is a proton donor and forms a Lewis acid-base pair with serotonin/tryptamine, which is associated with product release. The H214 residue is responsible for PLP binding and proton transfer, and its proper interaction with Y359 is essential for Os TDC enzyme activity. The extra hydrogen bonds formed between the 5-hydroxyl group of serotonin and the backbone carboxyl groups of F104 and P105 explain the discrepancy between the catalytic activity of TDC in tryptophan and in 5-hydroxytryptophan. In addition, an evolutionary analysis revealed that type II PLP_DC originated from glutamic acid decarboxylase, potentially as an adaptive evolution of mechanism in organisms in extreme environments. This study is, to our knowledge, the first to present a detailed analysis of the crystal structure of Os TDC in these complexes. The information regarding the catalytic mechanism described here could facilitate the development of protocols to regulate melatonin levels and thereby contribute to crop improvement efforts to improve food security worldwide.", "Keywords": "Crystal structure;Melatonin biosynthesis;Substrate specificity;Tryptophan decarboxylase", "DOI": "10.1016/j.jare.2020.06.004", "PubYear": 2020, "Volume": "24", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Crop Genetic Improvement, Huazhong Agricultural University, Wuhan 430070, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Crop Genetic Improvement, Huazhong Agricultural University, Wuhan 430070, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Liu", "Affiliation": "National Key Laboratory of Crop Genetic Improvement, Huazhong Agricultural University, Wuhan 430070, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Crop Genetic Improvement, Huazhong Agricultural University, Wuhan 430070, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Crop Genetic Improvement, Huazhong Agricultural University, Wuhan 430070, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Crop Genetic Improvement, Huazhong Agricultural University, Wuhan 430070, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Key Laboratory of Bioactive Natural Product Research and State Key Laboratory of Natural Medicines, China Pharmaceutical University, Nanjing 210014, China"}, {"AuthorId": 8, "Name": "Yucheng Zhao", "Affiliation": "Jiangsu Key Laboratory of Bioactive Natural Product Research and State Key Laboratory of Natural Medicines, China Pharmaceutical University, Nanjing 210014, China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shandong Provincial Key Laboratory of Microbial Engineering, College of Bioengineering, Qilu University of Technology, Jinan 250353, China;Corresponding authors"}], "References": []}, {"ArticleId": 82556434, "Title": "Immersive Interconnected Virtual and Augmented Reality: A 5G and IoT Perspective", "Abstract": "<p>Despite remarkable advances, current augmented and virtual reality (AR/VR) applications are a largely individual and local experience. Interconnected AR/VR, where participants can virtually interact across vast distances, remains a distant dream. The great barrier that stands between current technology and such applications is the stringent end-to-end latency requirement, which should not exceed 20 ms in order to avoid motion sickness and other discomforts. Bringing AR/VR to the next level to enable immersive interconnected AR/VR will require significant advances towards 5G ultra-reliable low-latency communication (URLLC) and a Tactile Internet of Things (IoT). In this article, we articulate the technical challenges to enable a future AR/VR end-to-end architecture, that combines 5G URLLC and Tactile IoT technology to support this next generation of interconnected AR/VR applications. Through the use of IoT sensors and actuators, AR/VR applications will be aware of the environmental and user context, supporting human-centric adaptations of the application logic, and lifelike interactions with the virtual environment. We present potential use cases and the required technological building blocks. For each of them, we delve into the current state of the art and challenges that need to be addressed before the dream of remote AR/VR interaction can become reality.</p>", "Keywords": "Augmented and virtual reality; Tactile Internet; 5G; Internet of Things", "DOI": "10.1007/s10922-020-09545-w", "PubYear": 2020, "Volume": "28", "Issue": "4", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent University – imec, Ghent, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Ioannina, Ioannina, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Politècnica de Catalunya, Barcelona, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Ioannina, Ioannina, Greece"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitat Politècnica de Catalunya, Barcelona, Spain"}, {"AuthorId": 6, "Name": "Bel<PERSON><PERSON><PERSON>", "Affiliation": "Samsung Research, Surrey, UK"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Turkcell Technology, Ankara, Turkey"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Turkcell Technology, Ankara, Turkey"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Technical University of Kosice, Kosice, Slovakia"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Technical University of Kosice, Kosice, Slovakia"}, {"AuthorId": 11, "Name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitat Politècnica de Catalunya, Barcelona, Spain"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Technische Universität Kaiserslautern, Kaiserslautern, Germany"}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": "IDLab, Ghent University – imec, Ghent, Belgium"}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IDLab, University of Antwerp – imec, Antwerp, Belgium"}], "References": []}, {"ArticleId": 82556684, "Title": "The impact of information security management practices on organisational agility", "Abstract": "<h3>Purpose</h3> <p>This study aims to determine the extent to which information security management (ISM) practices impact the organisational agility by examining the relationship between both concepts.</p> <h3>Design/methodology/approach</h3> <p>A quantitative method research design has been used in this study. This study was conducted throughout Malaysia with a total of 250 valid questionnaires obtained from managers and executives from the Multimedia Super Corridor (MSC)-status companies. Structural equation modelling (SEM) using partial least square was used to analyse the data and to test all nine hypotheses developed in this study.</p> <h3>Findings</h3> <p>Findings from this study indicate that operational agility (OA) is significantly related to ISM practices in MSC-status companies. The validation of the structural model of nine hypotheses developed for this study has demonstrated satisfactory results, exhibited six significant direct relationships and three insignificant relationships.</p> <h3>Research limitations/implications</h3> <p>This study has addressed the needs for a comprehensive, coherent and empirically tested ISM practices and organisational agility framework. The current theoretical framework used in this study emphasised on the ISM–organisational agility dimensions that are predominantly important to ascertain high level of ISM practices and perceived agility level among the information technology (IT) business companies in Malaysia. With the application of SEM for powerful analysis, the empirical-based framework established in this study was validated by the empirical findings, thus contributing significantly to the field of information security (InfoSec).</p> <h3>Originality/value</h3> <p>This study has filled the research gap between different constructs of ISM practices and OA. The model put forth in this study contributes in several ways to the InfoSec research community. The recognition of InfoSec practices that could facilitate organisational agility in the IT industry in Malaysia is vital and contributes to more value creation for the organisations.</p>", "Keywords": "Information security;Risk management;Information security management;Organisational agility", "DOI": "10.1108/ICS-02-2020-0020", "PubYear": 2020, "Volume": "28", "Issue": "5", "JournalId": 31004, "JournalTitle": "Information and Computer Security", "ISSN": "2056-4961", "EISSN": "2056-497X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Management, Universiti Teknologi MARA , Shah <PERSON>, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Management, Universiti Teknologi MARA , Shah <PERSON>, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Management, Universiti Teknologi MARA , Shah <PERSON>, Malaysia"}], "References": []}, {"ArticleId": 82556728, "Title": "Parametric control for multiscroll generation: Electronic implementation and equilibrium analysis", "Abstract": "This paper presents a novel electronic implementation based on a simple methodology to control the number of scrolls generated, by means of operational amplifiers and a single control parameter. Through the use of a Saturated Non Linear Function (SNLF) for the generation of a 9-scroll attractor, the number of scrolls is modified through a single parameter. In addition, the implemented system is also capable of generating a family of bistable behaviors. The phenomenon for the scroll generation, induced by a control parameter, is explained through the system equilibrium points. This methodology can be extended to system’s with higher number of scrolls, or systems with larger basins of attraction. The results shown have high potential application in secure communication systems, mobile surveillance devices, pseudo-random number generators and complex networks, among others.", "Keywords": "Multiscroll systems ; Parametric control ; n-scroll control generation ; Electronic implementation ; Equilibrium analysis", "DOI": "10.1016/j.nahs.2020.100929", "PubYear": 2020, "Volume": "38", "Issue": "", "JournalId": 5866, "JournalTitle": "Nonlinear Analysis: Hybrid Systems", "ISSN": "1751-570X", "EISSN": "1878-7460", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dynamical Systems Laboratory, CULagos, Universidad de Guadalajara, Centro Universitario de los Lagos, Enrique Díaz de León 1144, Paseos de la Montaña, 47460, Lagos de Moreno, Jalisco, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dynamical Systems Laboratory, CULagos, Universidad de Guadalajara, Centro Universitario de los Lagos, Enrique Díaz de León 1144, Paseos de la Montaña, 47460, Lagos de Moreno, Jalisco, Mexico"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dynamical Systems Laboratory, CULagos, Universidad de Guadalajara, Centro Universitario de los Lagos, Enrique Díaz de León 1144, Paseos de la Montaña, 47460, Lagos de Moreno, Jalisco, Mexico"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Dynamical Systems Laboratory, CULagos, Universidad de Guadalajara, Centro Universitario de los Lagos, <PERSON> León 1144, Paseos de la Montaña, 47460, Lagos de Moreno, Jalisco, Mexico;Applied Mathematics Division, Instituto Potosino de Investigación Científica y Tecnológica, IPICYT, Camino a la Presa San José 2055, Col. Lo<PERSON> 4ta. Sección, 78216, <PERSON> Luis Potosí, S. L. P., Mexico;Corresponding author at: Dynamical Systems Laboratory, CULagos, Universidad de Guadalajara, Centro Universitario de los Lagos, <PERSON> de León 1144, Paseos de la Montaña, 47460, Lagos de Moreno, Jalisco, Mexico"}], "References": []}, {"ArticleId": 82556797, "Title": "An analytical model of laser bending angle under preload", "Abstract": "<p>An analytical model establishment of laser bending angle is important in understanding its forming mechanism and predicting its bending angle. Meanwhile, laser thermal bending under preload is a new direction of laser research. Therefore, an analytical model of laser bending angle for buckling mechanism under preload is established in this work. The proposed model is established on a thermal buckling angle model without preload by analyzing the stress and strain in the heating zone and considering the preload factors. In addition, the range of machining parameters and the preload values that satisfy the model are presented. The accuracy of the bending angle that is predicted by the model is verified by experiments. Compared with the experimental results, the average error of bending angle is less than 3.7% under different preloads and 5.1% under different laser powers. Overall, results showed that the proposed model can accurately predict the bending angle.</p>", "Keywords": "Analytical model; Preload; Bending angle prediction; Buckling mechanism", "DOI": "10.1007/s00170-020-05521-5", "PubYear": 2020, "Volume": "108", "Issue": "7", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;College of Mechanical & Electronic Engineering, China University of Petroleum, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical & Electronic Engineering, China University of Petroleum, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;College of Mechanical & Electronic Engineering, China University of Petroleum, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;College of Mechanical & Electronic Engineering, China University of Petroleum, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;College of Mechanical & Electronic Engineering, China University of Petroleum, Qingdao, China"}], "References": []}]