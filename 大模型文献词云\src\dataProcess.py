import orjson
import os
from collections import Counter
import matplotlib.pyplot as plt
from wordcloud import WordCloud, STOPWORDS
from utils import Utils
from api import Api

class DataProcess:
    def __init__(self, floder_path):
        self.floder_path = floder_path # 文件夹路径
        self.keywords = {} # 关键词列表

    # 读取文件夹下的所有json文件
    def read_all_json(self):
        """
        读取文件夹下的所有json文件
        """
        print(f"正在读取{self.floder_path}目录下的所有json文件")
        # 函数主体
        for file in os.listdir(self.floder_path):
            self.read_json_file(os.path.join(self.floder_path, file))

        print(f"读取完成，共读取{len(self.keywords)}个期刊\n")

    # 读取json文件
    def read_json_file(self, file_path):
        """
        读取json文件
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = orjson.loads(f.read()) # data是list，每个元素是dict
            for item in data:
                # 提取期刊名称和关键词
                journal_title = item['JournalTitle']
                words = item['Keywords']
                # 清洗关键词
                keywords = Utils().split_string(words)
                # 判断列表是否为空
                if not keywords:
                    continue
                # 将关键词列表添加到字典中，如果期刊名称不存在，则创建一个空列表
                if journal_title not in self.keywords:
                    self.keywords[journal_title] = []
                self.keywords[journal_title].extend(keywords)

    # 处理关键词列表，将除白名单以外的关键词改为小写
    def process_keywords_list(self):
        """
        将除白名单以外的关键词改为小写

        :param whitelist: 白名单列表，白名单中的关键词不做小写处理
        """
        print("正在处理关键词列表，将除白名单以外的关键词改为小写")
        # 函数主体
        # 如果白名单文件不存在，则获取白名单
        if not os.path.exists("D:/项目/大模型文献词云/data/processed/whitelist.txt"):
            whitelist = self.get_whitelist()
            self.write_whitelist_to_file(whitelist)
        # 如果白名单文件存在，则读取白名单，每行为一个关键词，读取为字符串列表
        with open("D:/项目/大模型文献词云/data/processed/whitelist.txt", "r", encoding="utf-8") as f:
            whitelist = [line.strip() for line in f if line.strip()]
        
        # 处理关键词列表
        for journal, keywords in self.keywords.items():
            new_keywords = []
            for kw in keywords:
                if kw in whitelist:
                    new_keywords.append(kw)
                else:
                    new_keywords.append(kw.lower())
            self.keywords[journal] = new_keywords

        print("处理完成\n")

    # 获取白名单
    def get_whitelist(self):

        #  """
        # 获取白名单
        # """
        # print("正在获取白名单")
        # # 先把keywords字典中的所有value值放入一个字符串列表whitelist中
        # whitelist = []
        # for keywords in self.keywords.values():
        #     whitelist.extend(keywords)
        # # 将所有关键词合并为一个字符串，分号分隔
        # whitelist_str = ";".join(whitelist)
        # # 构造prompt
        # prompt = "请从以下的关键词中提取出所有不能将大写字母转换为小写的词汇（如：AI、AI-based、AI-based等），返回格式为：[词汇];[词汇];[词汇];[词汇]……只需要返回这些关键词，不需要其他内容。关键词：\n" + whitelist_str
        # # 调用API
        # api = Api()
        # response = api.get_api_response(prompt, "deepseek-ai/DeepSeek-R1")
        
        # if not response:
        #     print("警告：API未返回有效白名单")
        #     return []  # 返回空列表作为默认值
        
        # wl = api.process_api_response(response)
        # # 用工具类分割字符串
        # whitelist = Utils().split_string(wl)
        # # 去重
        # whitelist = list(set(whitelist))
        # print("获取完成\n")
        # return whitelist

        print("正在获取白名单")
        # 先把keywords字典中的所有value值放入一个字符串列表whitelist中
        whitelist = []
        for keywords in self.keywords.values():
            whitelist.extend(keywords)
        
        # 去重，减少处理量
        unique_keywords = list(set(whitelist))
        print(f"去重后共有 {len(unique_keywords)} 个唯一关键词")
        
        # 大幅减少批次大小，避免超过token限制
        batch_size = 400  # 每批只处理400个关键词
        all_uppercase_words = []
        
        api = Api()
        
        for i in range(0, len(unique_keywords), batch_size):
            batch = unique_keywords[i:i+batch_size]
            batch_str = ";".join(batch)
            
            print(f"处理第 {i//batch_size + 1}/{(len(unique_keywords)-1)//batch_size + 1} 批，包含 {len(batch)} 个关键词")
            
            # 构造更简洁的prompt
            prompt = f"从以下关键词中找出不能转为小写的词汇（如AI、GPU等），用分号分隔返回：\n{batch_str}"
            
            # 调用API
            response = api.get_api_response(prompt, "Qwen/Qwen3-235B-A22B-Instruct-2507")
            
            if not response:
                print(f"第 {i//batch_size + 1} 批API调用失败，跳过")
                continue
            
            wl = api.process_api_response(response)
            if wl:
                # 用工具类分割字符串
                batch_whitelist = Utils().split_string(wl)
                all_uppercase_words.extend(batch_whitelist)
        
        # if not all_uppercase_words:
        #     print("警告：所有API调用都失败，使用默认白名单")
        #     return ["AI", "ML", "NLP", "CNN", "RNN", "LSTM", "GAN", "API", "GPU", "CPU", "IoT", "5G", "COVID-19"]
        
        # 去重
        whitelist = list(set(all_uppercase_words))
        print(f"获取完成，共找到 {len(whitelist)} 个白名单词汇\n")
        return whitelist

    # 将白名单写入文件
    def write_whitelist_to_file(self,whitelist):
        """
        将白名单写入文件

        白名单为列表，列表中的元素为字符串
        """
        print("正在将白名单写入文件")
        with open("D:/项目/大模型文献词云/data/processed/whitelist.txt", "w", encoding="utf-8") as f:
            for keyword in whitelist:
                f.write(keyword + "\n")
        print("写入完成\n")

    # 将关键词列表转换为字典，记录每个关键词出现的次数
    def convert_keywords_to_count_dict(self):
        """
        将self.keywords中的所有value（原为关键词列表）转换为字典，记录每个关键词出现的次数

        用函数Counter()实现
        """
        print("正在将关键词列表转换为字典，记录每个关键词出现的次数")
        # 函数主体
        for journal, keywords in self.keywords.items():
            self.keywords[journal] = Counter(keywords)

        print("转换完成\n")

    # 把self.keywords写入文件内
    def write_keywords_to_file(self):
        """
        把self.keywords写入文件内
        """
        print("正在将关键词列表写入文件")
        # 如果关键词列表已经转换为字典，则写入文件
        if isinstance(self.keywords, dict):
            # 写入的时候遇见同名文件全部覆盖
            with open("D:/项目/大模型文献词云/data/processed/keywords.json", "w", encoding="utf-8") as f:
                f.write(orjson.dumps(self.keywords, ensure_ascii=False).decode('utf-8'))
        # 如果关键词列表没有转换为字典，则抛出异常
        else:
            raise ValueError("关键词列表没有转换为字典，请先调用convert_keywords_to_count_dict()函数")
        
        print("写入完成\n")

    # 读取keywords.json文件并返回关键词字典
    def read_keywords_from_file(self):
        """
        读取keywords.json文件
        """
        print("正在读取keywords.json文件")
        # 函数主体
        with open("D:/项目/大模型文献词云/data/processed/keywords.json", "r", encoding="utf-8") as f:
            keywords_dict = orjson.load(f)
            print("读取完成\n")
            return keywords_dict

    # 根据传入的关键词字典生成词云
    def generate_word_cloud(self, keywords_dict, journal_title):
        """
        根据传入的关键词字典生成词云
        """
        print(f"正在生成{journal_title}的词云")
        # 函数主体
        wordcloud = WordCloud(font_path='simhei.ttf', width=800, height=400, background_color='white').generate_from_frequencies(keywords_dict)
        # 设置词云图片的尺寸
        plt.figure(figsize=(10, 5))
        # 隐藏坐标轴
        plt.axis("off")
        # 设置词云图片的标题
        plt.title(journal_title)
        plt.imshow(wordcloud, interpolation='bilinear')
        # 保存词云图片
        plt.savefig(f"D:/项目/大模型文献词云/data/output/{journal_title}.png")
        # 显示词云图片
        plt.show()
        print("成功生成词云\n")

if __name__ == "__main__":
    data_process = DataProcess("D:/项目/大模型文献词云/data/json")
    data_process.read_all_json()
    print(data_process.keywords)
    print(len(data_process.keywords))
    print(type(data_process.keywords))
