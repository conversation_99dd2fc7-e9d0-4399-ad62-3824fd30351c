{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["实现待办管理"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from IPython.display import clear_output\n", "class Task:\n", "    def __init__(self,text):\n", "        self.text = text\n", "        self.status = \"未完成\"\n", "\n", "    def mark_finished(self):\n", "        self.status = \"已完成\"\n", "\n", "class TaskList:\n", "    # 初始化函数，空列表\n", "    def __init__(self):\n", "        self.tasks = []\n", "\n", "    # 添加待办，接收一个待办对象，使用append\n", "    def add_task(self,task):\n", "        self.tasks.append(task)\n", "\n", "    # 删除待办，接收一个对象，删除它，使用del\n", "    def remove_task(self,idx):\n", "        del self.tasks[idx]\n", "\n", "    # 获取所有已完成的待办，无参数。循环，完成的加入result列表\n", "    def get_finished_tasks(self):\n", "        result = []\n", "        for i in range(0,len(self.tasks)):\n", "            if self.tasks[i].status == \"已完成\":\n", "                result.append(self.tasks[i])\n", "        return result\n", "    \n", "    # 获取所有未完成的待办，与上面类似\n", "    def get_unfinished_tasks(self):\n", "        result = []\n", "        for i in range(0,len(self.tasks)):\n", "            if self.tasks[i].status == \"未完成\":\n", "                result.append(self.tasks[i])\n", "        return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["实现打印模块"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def print_menu():\n", "    print(\"Hello\")\n", "    print(\"你想要做什么：\")\n", "    print(\"1.添加待办\")\n", "    print(\"2.删除待办\")\n", "    print(\"3.标记待办已完成\")\n", "    print(\"4.退出\")\n", "\n", "def print_tasks(finished,unfinished):\n", "    print(\"未完成的待办：\")\n", "    for i in range(0,len(unfinished)):\n", "        print(i,unfinished[i].text)\n", "    print(\"\")\n", "    print(\"已完成的待办：\")\n", "    for i in range(0,len(finished)):\n", "        print(1,finished[i].text)\n", "    print(\"\")\n", "\n", "def print_all_tasks(all_tasks):\n", "    for i in range(0,len(all_tasks)):\n", "        print(i,all_tasks[i].text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["实现整合模块"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["my_task_list = TaskList()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["未完成的待办：\n", "0 看书\n", "1 玩游戏\n", "\n", "已完成的待办：\n", "\n", "Hello\n", "你想要做什么：\n", "1.添加待办\n", "2.删除待办\n", "3.标记待办已完成\n", "4.退出\n"]}], "source": ["while True:\n", "    clear_output()\n", "\n", "    # 获取已完成和未完成的待办\n", "    finished_tasks = my_task_list.get_finished_tasks()\n", "    unfinished_tasks = my_task_list.get_unfinished_tasks()\n", "\n", "    # 打印待办，直接调用print_tasks函数\n", "    print_tasks(finished_tasks,unfinished_tasks)\n", "\n", "    # 打印命令菜单\n", "    print_menu()\n", "\n", "    # 获取用户输入的命令\n", "    command = input(\"请输入操作序号：\")\n", "\n", "    # 判断用户输入的代码\n", "    if command == \"1\":\n", "        text = input(\"请输入希望添加的待办内容\")\n", "        task = Task(text)\n", "        my_task_list.add_task(task)\n", "    elif command == \"2\":\n", "        clear_output()\n", "        print_all_tasks(my_task_list.tasks)\n", "        idx = input(\"请输入想删除的待办序号\")\n", "        idx = int(idx)\n", "        my_task_list.remove_task(idx)\n", "    elif command == \"3\":\n", "        clear_output()\n", "        print_all_tasks(my_task_list.tasks)\n", "        idx = input(\"请输入希望标记完成的待办序号\")\n", "        idx = int(idx)\n", "        my_task_list.tasks[idx].mark_finished()\n", "    elif command == \"4\":\n", "        break;"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}