# # 希尔排序数字序列实现

# # 准备数据
# data = [5,8,3,1,2,7,4,6]
# # 排序前
# print("排序前：",data)
# # 希尔排序
# def shell_sort(data):
#     n = len(data)
#     gap = n // 2 # 初始增量
#     while gap > 0:
#         # 对序列进行插入排序
#         for i in range(gap,n):
#             temp = data[i]
#             j = i
#             # 向前移动，找到插入位置
#             while j >= gap and data[j-gap] > temp:
#                 data[j] = data[j-gap]
#                 j -= gap
#             data[j] = temp # 插入
#         gap //= 2 # 缩小增量
#     return data

# # 排序后
# print("排序后：",shell_sort(data))


# 创建一个Person类
class Person:
    def __init__(self, name, age, score):
        self.name = name
        self.age = age # 年龄
        self.score = score # 分数

    def __str__(self):
        return f"{self.name}, {self.age},{self.score}"

# 准备数据
p1 = Person("张三", 18, 90)
p2 = Person("李四", 20, 85)
p3 = Person("王五", 19, 95)
p4 = Person("赵六", 21, 80)
p5 = Person("钱七", 20, 88)

data = [p1, p2, p3, p4, p5]

# 排序前
print("排序前：")
for p in data:
    print(p)

# 对象序列的希尔排序
def shell_sort(data):
    n = len(data)
    gap = n // 2
    while gap > 0:
        for i in range(gap,n):
            temp = data[i]
            j = i
            while j >= gap and data[j-gap].age >= temp.age:
                if data[j-gap].age == temp.age and data[j-gap].score <= temp.score:
                    break
                data[j] = data[j-gap]
                j -= gap
            data[j] = temp
        gap //= 2
    return data

# 排序后
print("排序后：")
for p in shell_sort(data):
    print(p)