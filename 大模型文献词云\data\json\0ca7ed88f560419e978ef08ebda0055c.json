[{"ArticleId": 92634071, "Title": "Universal Algebraic Methods for Constraint Satisfaction Problems", "Abstract": "<p>After substantial progress over the last 15 years, the \"algebraic CSP-dichotomy conjecture\" reduces to the following: every local constraint satisfaction problem (CSP) associated with a finite idempotent algebra is tractable if and only if the algebra has a Taylor term operation. Despite the tremendous achievements in this area (including recently announce proofs of the general conjecture), there remain examples of small algebras with just a single binary operation whose CSP resists direct classification as either tractable or NP-complete using known methods. In this paper we present some new methods for approaching such problems, with particular focus on those techniques that help us attack the class of finite algebras known as \"commutative idempotent binars\" (CIBs). We demonstrate the utility of these methods by using them to prove that every CIB of cardinality at most 4 yields a tractable CSP.</p>", "Keywords": "Primary: 08A70, Secondary: 03C05, 08A30, 08A40;Mathematics - Logic;Computer Science - Logic in Computer Science", "DOI": "10.46298/lmcs-18(1:12)2022", "PubYear": 2022, "Volume": "18, Issue 1", "Issue": "", "JournalId": 10075, "JournalTitle": "Logical Methods in Computer Science", "ISSN": "", "EISSN": "1860-5974", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Iowa State University, Ames, IA, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Thmpr Laboratory, New York, NY, United States"}], "References": []}, {"ArticleId": 92634137, "Title": "Rezension „e-Tot“", "Abstract": "", "Keywords": "", "DOI": "10.1365/s40702-022-00835-2", "PubYear": 2022, "Volume": "59", "Issue": "1", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hochschule Darmstadt, Darmstadt, Deutschland"}], "References": []}, {"ArticleId": 92634219, "Title": "JPM-4 Proton Precession Magnetometer and Sensitivity Estimation", "Abstract": "A proton precession magnetometer (PPM) is a traditional quantum magnetometer based on the Larmor precession of hydrogen protons in Earth's magnetic field. PPMs are widely used in various fields, such as magnetic observation and detection of buried objects. A coaxial solenoid is typically used to construct the PPM sensor. However, the Larmor signal slowly weakens when the axial direction of the sensor gradually approaches the direction of Earth's magnetic field. Thus, a dead zone exists when Earth's magnetic field is nearly parallel to the axis of the sensor. An omnidirectional sensor with an \"8\"-type structure is designed in this study. The signal quality is slightly affected by the orientation of the sensor owing to the orthogonal polarized magnetic field components. The measured signal-to-noise ratio (SNR) of the Larmor signal is approximately 31/1, and the decay constant of the free induced decay (FID) signal is 0.95 s. The electrical parameters of the sensor coil are optimized and the polarization power is 8.0 W. Multiple hourly observations of Earth's magnetic field in noisy and quiet environments indicate the satisfactory consistency of the measurement results of the two PPMs with the proposed sensor. The standard deviations (STDs) of the measured results for a single instrument in noisy and quiet environments are 6.4 and 0.076 nT, respectively, which effectively reflect the environmental noise level. The sensitivity of the instrument is estimated to be 0.04 nT at a 5 s cycling rate for the two synchronized instruments. This is higher than the sensitivity of most commercial magnetometers of 0.1 nT. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "Larmor precession; Magnetic sensors; Proton magnetometer; Scale quantum magnetometer; Sensitivity", "DOI": "10.18494/SAM3719", "PubYear": 2022, "Volume": "34", "Issue": "1", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Science and Engineering, Jilin University, Changchun, 130012, China"}, {"AuthorId": 2, "Name": "Shu<PERSON> Chen", "Affiliation": "College of Electronic Science and Engineering, Jilin University, Changchun, 130012, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Science and Engineering, Jilin University, Changchun, 130012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physics, Jilin University, Changchun, 130012, China"}], "References": []}, {"ArticleId": 92634221, "Title": "Quantitative Evaluation of Post-Lunch Dip Using Event-Related Potential", "Abstract": "<p>The sense of sleepiness and fatigue that occurs at around 2 p.m. is known as the “post-lunch dip (PLD).” It causes a transient decline in brain function, including cognitive function, attentiveness, and arousal level. Various research hypotheses have been proposed for the mechanism of occurrence of PLD, including explanations involving blood sugar spikes or the inhibition of neuropeptides. However, the evidence for these hypotheses is poorly constructed, and none of them is widely recognized as an explanation for the mechanism. The establishment of quantitative evaluation indicators for the decline in brain function caused by PLD is essential to clarify the mechanism of occurrence of PLD. In this study, a demonstration experiment was conducted focusing on P300 and contingent negative variation (CNV), which are types of event-related potentials (ERP), as evaluation indicators of PLD. The subjects were 14 healthy young people, and the meal load used was two slices of white bread and 285 mL of water. In the experiment, measurements were taken four times in total (preprandial, immediately postprandial, 40 min postprandial, and 80 min postprandial). The Stanford sleepiness scale (SSS) and a subjective questionnaire about fatigue using a visual analog scale (VAS) were administered before each measurement. The results confirmed that, at 40 min postprandial, when a significant increase in SSS was observed, there was a reduction in the area of early CNV and late CNV and a prolongation of P300 latency (p<0.05). An evaluation using late CNV also confirmed a reduction in area immediately postprandial that could not be confirmed in the SSS.</p>", "Keywords": "post-lunch dip (PLD);P300;contingent negative variation (CNV);Stanford sleepiness scale (SSS);visual analogue scale (VAS)", "DOI": "10.20965/jaciii.2022.p0067", "PubYear": 2022, "Volume": "26", "Issue": "1", "JournalId": 13600, "JournalTitle": "Journal of Advanced Computational Intelligence and Intelligent Informatics", "ISSN": "1343-0130", "EISSN": "1883-8014", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems Engineering, Graduate School of Engineering, Toyama Prefectural University 5180 Kurokawa, Imizu-shi, Toyama 939-0398, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems Engineering, Graduate School of Engineering, Toyama Prefectural University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems Engineering, Graduate School of Engineering, Toyama Prefectural University"}], "References": []}, {"ArticleId": 92634412, "Title": "Artificial neural network controller based cleaner battery-less fuel cell vehicle with EF2 resonant DC-DC converter", "Abstract": "The evolution of an eco-friendly, sustainable and efficient energy conversion system is becoming inevitable for automotive manufactures and consumers worldwide in the current scenario, especially in highly polluted areas. The use of Fuel Cells (FC) in automotive applications is a lucrative option in response to the critical need for cleaner energy technology. The net voltage output for a single FC is 0.6 to 0.8 V and these cells are arranged in series to form an FC stack. To compensate for FC stack voltage reduction during acceleration of vehicle requires additional battery or ultra-capacitor, which increases the overall cost of the vehicle. An 8 KW FC stack along with Elongation Factor 2 (EF<sub>2</sub>) resonant converter fed 10 hp Polymer Electrolyte Membrane Fuel Cell (PMSM) drive for powering two-wheelers is proposed which improves the dynamic performance and vehicle reliability. The proposed system reduces the overall size and cost of the FC stack compared to the existing system and regulates the voltage fed to the PMSM drive under different working conditions without additional battery. The proposed drive’s closed-loop speed control is modeled with a State Space Averaged (SSA) dynamic EF<sub>2</sub> converter model and a PI controller, followed by a Levenberg–<PERSON>rdt algorithm-based Artificial Neural Network (LM-ANN) controller. The performance of the drive is evaluated for various conditions such as starting, climbing, normal running, and braking condition. Comparison of simulation results indicates that EF<sub>2</sub> resonant converter operating at greater switching frequency provides high voltage gain at lower duty ratios and LM-ANN based controller seems to be better to provide smooth operation with reduced torque ripple.", "Keywords": "Fuel cells ; Reliability ; Fuel cell stack ; Torque ripple ; Resonant converter ; Dynamic response ; Artificial neural network ; State space averaged ; PMSM ; EF<sub>2</sub> ; FCVs", "DOI": "10.1016/j.suscom.2022.100667", "PubYear": 2022, "Volume": "35", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "Renjini G.", "Affiliation": "Department of Electrical and Electronics Engineering, NSS College of Engineering, APJ <PERSON> Kerala Technological University, Palakkad, Kerala, India;Corresponding author"}, {"AuthorId": 2, "Name": "Devi V.", "Affiliation": "Department of Electrical and Electronics Engineering, Royal College of Engineering and Technology, Akkikkavu, Kerala, India"}], "References": []}, {"ArticleId": 92634524, "Title": "Considering Structural and Vocabulary Heterogeneity in XML Query: FPTPQ and Holistic Evaluation.", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijdms.2021.13601", "PubYear": 2021, "Volume": "13", "Issue": "6", "JournalId": 16205, "JournalTitle": "International Journal of Database Management Systems", "ISSN": "0975-5985", "EISSN": "0975-5705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92634674, "Title": "Large Scale Multilingual Sticker Recommendation In Messaging Apps", "Abstract": "<p>Stickers are popularly used while messaging to visually express nuanced thoughts. We describe a real-time sticker recommendation (SR) system. We decompose SR into two steps: predict the message that is likely to be sent, and substitute that message with an appropriate sticker. To address the challenges caused by transliteration of message from users’ native language to the Roman script, we learn message embeddings by employing character-level CNN in an unsupervised manner. We use them to cluster semantically similar messages. Next, we predict the message cluster instead of the message. Except for validation, our system does not require human labeled data, leading to a fully auto-matic tuning pipeline. We propose a hybrid message prediction model, which can easily run on low-end phones. We discuss message cluster to sticker mapping, addressing the multilingual needs of our users, automated tuning of the system and also propose a novel application of community detection algorithm. As of November 2020, our system contains 100k+ stickers, has been deployed for 15+ months, and is being used by millions of users.</p>", "Keywords": "", "DOI": "10.1609/aimag.v42i4.15098", "PubYear": 2022, "Volume": "42", "Issue": "4", "JournalId": 18861, "JournalTitle": "AI Magazine", "ISSN": "0738-4602", "EISSN": "0738-4602", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Messenger"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> Messenger"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Messenger"}, {"AuthorId": 4, "Name": "Parth <PERSON>", "Affiliation": "<PERSON><PERSON> Messenger"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Messenger"}], "References": []}, {"ArticleId": 92634794, "Title": "Symmetry-Based Brain Abnormality Detection Using Machine Learning", "Abstract": "<p>Medical image processing, which includes many applications such as magnetic resonance image (MRI) processing, is one of the most significant fields of computer-aided diagnostic (CAD) systems. the detection and identification of abnormalities in the magnetic resonance imaging of the brain is one of the important applications that uses magnetic resonance imaging and digital image processing techniques. In this study, we present a method that relies on the symmetry and similarity between the two lobes of the brain to determine if there are any abnormalities in the brain because tumours cause deformations in the shape of one of the lobes, which affects this symmetry. The proposed approach overcomes the challenge arising from different shapes of brain images of different people, which poses an obstacle to some approaches that rely on comparing one person’s brain image with other people's brain images. In the proposed method the image of the brain is divided into two parts, one for the left lobe and the other for the right lobe. Some measures are extracted from the features of the image of each lobe separately and the distance between the corresponding metrics are calculated. These distances are used as the independent variables of the classification algorithm which determines the class to which the brain belongs. Metrics extracted from various features, such as colour and texture, were studied, discussed and used in the classification process. The proposed algorithm was applied to 366 images from standard datasets and four classifiers were tested namely Naïve Bayes (NB), random forest (RF), logistic regression (LR), and support vector machine (SVM). The obtained results from these classifiers have been discussed thoroughly and it was found that the best results were obtained from RF classifiers where the accuracy was 98.2%. Finally, The results obtained and the limitations were discussed and benchmarked with state-of-the-art approaches.</p>", "Keywords": "Machine learning;Image identification;brain MRI;Brain tumour;CAD;Features;symmetry;Classifier;AI", "DOI": "10.4114/intartif.vol24iss68pp138-150", "PubYear": 2021, "Volume": "24", "Issue": "68", "JournalId": 16278, "JournalTitle": "INTELIGENCIA ARTIFICIAL", "ISSN": "1137-3601", "EISSN": "1988-3064", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Oman College of Management and Technology, Oman"}], "References": []}, {"ArticleId": 92634813, "Title": "Multi-domain Spoken Language Understanding Using Domain- and Task-aware Parameterization", "Abstract": "<p> Spoken language understanding (SLU) has been addressed as a supervised learning problem, where a set of training data is available for each domain. However, annotating data for a new domain can be both financially costly and non-scalable. One existing approach solves the problem by conducting multi-domain learning where parameters are shared for joint training across domains, which is domain-agnostic and task-agnostic . In the article, we propose to improve the parameterization of this method by using domain-specific and task-specific model parameters for fine-grained knowledge representation and transfer. Experiments on five domains show that our model is more effective for multi-domain SLU and obtain the best results. In addition, we show its transferability when adapting to a new domain with little data, outperforming the prior best model by 12.4%. Finally, we explore the strong pre-trained model in our framework and find that the contributions from our framework do not fully overlap with contextualized word representations (RoBERTa). </p>", "Keywords": "Multi-domain spoken language understanding; domain-specific and task-specific model; fine-grained knowledge representation and transfer", "DOI": "10.1145/3502198", "PubYear": 2022, "Volume": "21", "Issue": "4", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "Libo Qin", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Westlake University, Hangzhou, Zhejiang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Che", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Li", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang, China"}], "References": []}, {"ArticleId": 92634847, "Title": "Searchable encryption on the cloud: a survey", "Abstract": "<p>Outsourcing data to the cloud can be considered as a perfect solution for the storage and computationally constrained data owners. However, data confidentiality is compromised when data are shared through the cloud. Cryptographic techniques like searchable encryption offer possible solutions of protect data from unauthorized access while facilitating searching without disclosure. Different searchable encryption techniques have been proposed; however, the techniques are limited in one way or the other, and none of them satisfies all the needs of confidentiality while sharing of data outsourced on the cloud. In this survey, we have identified the key performance indicators for confidentiality while sharing for cloud data along with possible attacks on searchable encryption techniques. A comprehensive study of the existing techniques searchable encryption on the anvil of the key performance indicators and robustness to attacks has been done. It was found that a technique may be suitable for an application with specific data confidentiality requirements, although no cryptographic silver bullet exists that satisfies all the performance metrics or is impervious to all the attacks. Moreover, most of the existing techniques are impracticable due to paradoxical requirements of data confidentiality and performance. A single mechanism may not suffice, and there is a need for a basket of efficient techniques that may provide accurate search with data confidentiality to make data sharing over the cloud an economically attractive option.</p>", "Keywords": "Cloud; Searchable encryption; Bilinear pairing; Homomorphic encryption", "DOI": "10.1007/s11227-022-04309-6", "PubYear": 2022, "Volume": "78", "Issue": "7", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jaypee Institute of Information Technology, Noida, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, Prayagraj, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bennett University, Greater Noida, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, Prayagraj, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, Prayagraj, India"}], "References": [{"Title": "Multi-Keyword Ranked Searchable Encryption with the Wildcard Keyword for Data Sharing in Cloud Computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "1", "Page": "184", "JournalTitle": "The Computer Journal"}, {"Title": "Conjunctive multi-key searchable encryption with attribute-based access control for EHR systems", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "82", "Issue": "", "Page": "103606", "JournalTitle": "Computer Standards & Interfaces"}]}, {"ArticleId": 92634868, "Title": "Arrhythmia classification based on improved monarch butterfly optimization algorithm", "Abstract": "Heart attack is a major cause of mortality all over the world also heart-related diseases have increased the expenditure of health care. Electrocardiogram (ECG) is one of the simple ways to diagnose heart diseases. Arrhythmia is characterised by abnormalities in the rhythmic pace of heart, which may occur on a random basis in a everyday life of people. It was necessary to use long-term ECG recording devices to capture these rare occurrences. Morphological feature and wavelet coefficients-based features obtained from the recorded ECG signals. The feature vector is optimized by an Improved Monarch Butterfly optimization (IMBO) algorithm to reduce the dimensionality. These optimized features are applied to convolution neural networks to classify signals. The experimental results of the proposed method give a 99.49% accuracy, 99.58% sensitivity, and 98.83% specificity which is comparative to previous methods, and found better classification accuracy which may help the physician for diagnosing an arrhythmia.", "Keywords": "ECG ; ECG classification ; Monarch Butterfly Optimization ; Convolution Neural Network", "DOI": "10.1016/j.jksuci.2022.01.002", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of ECE, FET <PERSON><PERSON><PERSON> Deemed to be University Haridwar, Uttarakhand, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Electrical Engg., GBPIET Pauri Garhwal, Uttarakhand, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Electrical Engg., GBPIET Pauri Garhwal, Uttarakhand, India"}], "References": [{"Title": "GGA: A modified genetic algorithm with gradient-based local search for solving constrained optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "136", "JournalTitle": "Information Sciences"}, {"Title": "Network traffic classification using deep convolutional recurrent autoencoder neural networks for spatial–temporal features extraction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "102890", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 92634922, "Title": "Without detection: Two‐step clustering features with local–global attention for image captioning", "Abstract": "The current image captioning methods usually integrate an object detection network to obtain image features at the level of objects and other salient regions. However, the detection network needs to be independently pre‐trained on additional data. Thus, mainly due to the demand for extra training data and computing resources, the detection network's utilization will impose higher training costs on the overall captioning model. In this work, the authors propose a local–global attention model based on two‐step clustering features for image captioning. The two‐step clustering features can be obtained at a relatively low cost and have the presentation ability in objects or other salient image regions. To make the model perceive the image better, the authors introduce a novel local–global attention mechanism. The model will analyse the clustering features from local perspectives to global ones at each time step, making the model better understand the image contents. The authors evaluate the proposed method on the MSCOCO test server, achieving BLEU‐4/METEOR/ROUGE‐L scores of 36.8, 27.4, and 57.2, respectively. With the benefit of reducing training costs, the authors' model also achieves closing results compared with the models using detection features.", "Keywords": "", "DOI": "10.1049/cvi2.12087", "PubYear": 2022, "Volume": "16", "Issue": "3", "JournalId": 11350, "JournalTitle": "IET Computer Vision", "ISSN": "1751-9632", "EISSN": "1751-9640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China;Key Laboratory of Network Information System Technology (NIST) Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China;University of Chinese Academy of Sciences  Beijing China;School of Electronic, Electrical and Communication Engineering University of Chinese Academy of Sciences  Beijing China"}, {"AuthorId": 2, "Name": "Wen<PERSON> Zhang", "Affiliation": "Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China;Key Laboratory of Network Information System Technology (NIST) Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China;Key Laboratory of Network Information System Technology (NIST) Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China;University of Chinese Academy of Sciences  Beijing China;School of Electronic, Electrical and Communication Engineering University of Chinese Academy of Sciences  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China;Key Laboratory of Network Information System Technology (NIST) Aerospace Information Research Institute Chinese Academy of Sciences  Beijing China"}], "References": [{"Title": "Image captioning via semantic element embedding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "395", "Issue": "", "Page": "212", "JournalTitle": "Neurocomputing"}, {"Title": "Learning visual relationship and context-aware attention for image captioning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107075", "JournalTitle": "Pattern Recognition"}, {"Title": "Evolutionary recurrent neural network for image captioning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "401", "Issue": "", "Page": "249", "JournalTitle": "Neurocomputing"}, {"Title": "Reasoning like Humans: On Dynamic Attention Prior in Image Captioning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107313", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 92634946, "Title": "A magnetostrictive BaTiO3-Fe-Ga & PDMS magnetic field sensor: Research on magnetic detection performance", "Abstract": "Pizoelectric flexible magnetic field sensors have recently attracted much attention due to their high sensitivity and fast response time. A clear sensing mechanism will effectively improve the performance of such sensors. Here, we report a BaTiO<sub>3</sub>-Fe-Ga &amp; PDMS flexible magnetic sensor. The magnetostrictive and magnetic field detection performance of the sensor is simulated by the finite element software. The simulated results show that magnetic field detection can be achieved through magnetostrictive and piezoelectric effects. The flexible magnetic sensor is prepared and tested based on the simulation results. The distribution of the material in the composite film is demonstrated by SEM and EDS. The results show that the sensor with the BaTiO<sub>3</sub> concentration of 20% has the best performance and the sensitivity of 3.4 V/T.", "Keywords": "Piezoelectric effect ; Flexible magnetic sensor ; Magnetostrictive ; Magnetic field detection", "DOI": "10.1016/j.sna.2022.113383", "PubYear": 2022, "Volume": "335", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "MicroNano System Research Center, College of Information and Computer & Key Lab of Advanced Transducers and Intelligent Control System of the Ministry of Education, Taiyuan University of Technology, Taiyuan 030024, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "MicroNano System Research Center, College of Information and Computer & Key Lab of Advanced Transducers and Intelligent Control System of the Ministry of Education, Taiyuan University of Technology, Taiyuan 030024, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MicroNano System Research Center, College of Information and Computer & Key Lab of Advanced Transducers and Intelligent Control System of the Ministry of Education, Taiyuan University of Technology, Taiyuan 030024, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "MicroNano System Research Center, College of Information and Computer & Key Lab of Advanced Transducers and Intelligent Control System of the Ministry of Education, Taiyuan University of Technology, Taiyuan 030024, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Sang", "Affiliation": "MicroNano System Research Center, College of Information and Computer & Key Lab of Advanced Transducers and Intelligent Control System of the Ministry of Education, Taiyuan University of Technology, Taiyuan 030024, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "MicroNano System Research Center, College of Information and Computer & Key Lab of Advanced Transducers and Intelligent Control System of the Ministry of Education, Taiyuan University of Technology, Taiyuan 030024, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "MicroNano System Research Center, College of Information and Computer & Key Lab of Advanced Transducers and Intelligent Control System of the Ministry of Education, Taiyuan University of Technology, Taiyuan 030024, China;Corresponding author"}], "References": []}, {"ArticleId": 92634976, "Title": "Linguistically Driven Multi-Task Pre-Training for Low-Resource Neural Machine Translation", "Abstract": "<p> In the present study, we propose novel sequence-to-sequence pre-training objectives for low-resource machine translation (NMT): Japanese-specific sequence to sequence (JASS) for language pairs involving Japanese as the source or target language, and English-specific sequence to sequence (ENSS) for language pairs involving English. JASS focuses on masking and reordering Japanese linguistic units known as bunsetsu, whereas ENSS is proposed based on phrase structure masking and reordering tasks. Experiments on ASPEC Japanese–English & Japanese–Chinese, Wikipedia Japanese–Chinese, News English–Korean corpora demonstrate that JASS and ENSS outperform MASS and other existing language-agnostic pre-training methods by up to +2.9 BLEU points for the Japanese–English tasks, up to +7.0 BLEU points for the Japanese–Chinese tasks and up to +1.3 BLEU points for English–Korean tasks. Empirical analysis, which focuses on the relationship between individual parts in JASS and ENSS, reveals the complementary nature of the subtasks of JASS and ENSS. Adequacy evaluation using LASER, human evaluation, and case studies reveals that our proposed methods significantly outperform pre-training methods without injected linguistic knowledge and they have a larger positive impact on the adequacy as compared to the fluency. </p>", "Keywords": "Low-resource neural machine translation; pre-training; linguistically-driven", "DOI": "10.1145/3491065", "PubYear": 2022, "Volume": "21", "Issue": "4", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Japan"}], "References": []}, {"ArticleId": 92634980, "Title": "Arabic Word Sense Disambiguation for Information Retrieval", "Abstract": "<p>In the context of using semantic resources for information retrieval, the relationship and distance between concepts are considered important for word sense disambiguation. In this article, we experiment with Conceptual Density and Random Walk with graph methods to enhance the performance of the Arabic Information Retrieval System. To do this, a medium-sized corpus was used. The results proved that Random Walk can enhance the performance of the information retrieval system by achieving a mean improvement of 13%, 16%, and 12% in terms of recall, precision, and F-score, respectively.</p>", "Keywords": "IRS; conceptual density; PageRank; Arabic WordNet; WSD; information retrieval", "DOI": "10.1145/3510451", "PubYear": 2022, "Volume": "21", "Issue": "4", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire de Traitement Automatique de la Langue Arabe (LTALA), Tlemcen University, Tlemcen, Algeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratoire de Traitement Automatique de la Langue Arabe (LTALA), Tlemcen University, Tlemcen, Algeria"}], "References": []}, {"ArticleId": 92635007, "Title": "Transition Model–driven Unsupervised Localization Framework Based on Crowd-sensed Trajectory Data", "Abstract": "<p> The rapid popularization of mobile devices makes it more convenient and cost-efficient to collect synchronized WiFi received signal strength (RSS) and inertial measurement unit sequences by crowdsensing. The transition model has proven to be a promising unsupervised localization approach that captures the transition relationship between the change of RSS signal space and the change of physical space, alleviating the need of extra knowledge for creating radio map. However, it faces two essential challenges in real-world deployments. First, model coverage affects its locating performance, because a specific transition model only represents its local space. Second, the instability of RSS leads to a conflicting relationship between changes of two spaces because of the complex environment and the heterogeneous type of devices. To address these challenges, we propose Lightgbm-CTMM, a novel unsupervised localization framework. First, a clustering method is adopted to capture the expected relationship to ensure robust coverage. Second, direction filter is employed to guarantee that the change in signal space corresponds to the change in physical space. The feasibility and effectiveness of Lightgbm-CTMM are evaluated by extensive experiments, and the locating performance of Lightgbm-CTMM is better than that of conventional approaches. Moreover, Lightgbm-CTMM reduces the work on quality assessment of trajectories. </p>", "Keywords": "", "DOI": "10.1145/3499425", "PubYear": 2022, "Volume": "18", "Issue": "2", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Shen", "Affiliation": "Hangzhou Dianzi University, Hangzhou, Zhejiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, Zhejiang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, Zhejiang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Renmin University of China, Beijing, China"}, {"AuthorId": 5, "Name": "Quanbo Ge", "Affiliation": "Nanjing University of Information Science and Technology, Nanjing, Jiangsu, China"}], "References": []}, {"ArticleId": 92635375, "Title": "State-of-the-art survey of artificial intelligent techniques for IoT security", "Abstract": "The data protection problem concerning the Internet of Things (IoT) paradigm has drawn the innovation community’s considerable attention. Several surveys have covered different IoT-centered issues, namely vulnerability simulation, intrusion detection systems, and state-of-the-art techniques were put forward for this purpose. In comparison, we concentrate exclusively on the emerging IoT vulnerabilities and related Artificial Techniques in the current research. This paper initializes the detailed categorization of recent research works, which explore different Machine Learning and Deep Learning techniques for the IoT paradigm. Additionally, a novel taxonomy is included based on IoT vulnerabilities, corresponding attackers, and effects, threats that explore weak links, effective remedies, and organizational authentication technologies that are currently available to recognize and track such deficiencies. This seeks to offer a multidimensional analysis viewpoint on IoT vulnerabilities to the reader, including the technological specifics and effects, which are intended to be leveraged for remediation goals. Inspired by the lack of IoT paradigm-related scientific (and malicious) evidence, the current study provides an emphasis on IoT manipulation from passive measurements. The current research illustrates the seriousness of the IoT problem while offering organizational knowledge resources that will inevitably assist in the mitigating mission in general. In addition to open issues and research concerns, informative conclusions, inferences, and results are revealed in the current research, which will lead to future research initiatives to resolve scientific concerns relevant to IoT security.", "Keywords": "Security ; Internet of Things ; Authentication ; Protection", "DOI": "10.1016/j.comnet.2022.108771", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Engineering and Sciences, <PERSON>, Saudi Arabia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Engineering and Sciences, Prince <PERSON> University, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Oklahoma, United States"}], "References": [{"Title": "A survey of edge computing-based designs for IoT security", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "195", "JournalTitle": "Digital Communications and Networks"}, {"Title": "DL-Droid: Deep learning based android malware detection using real devices", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "101663", "JournalTitle": "Computers & Security"}, {"Title": "Energy Modeling for the Bluetooth Low Energy Protocol", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}]}, {"ArticleId": 92635426, "Title": "Abusive supervision and cyberloafing: A moderated moderation model of moral disengagement and negative reciprocity beliefs", "Abstract": "Cyberloafing is a major productivity killer in the workplace. Drawing on social cognitive theory and social exchange theory, we developed and tested a model that examined the underlying mechanisms through which abusive supervision influences cyberloafing. The results showed that abusive supervision is positively related to cyberloafing. This relationship is also moderated by negative reciprocity beliefs. In addition, support was found for the three-way interaction effect of abusive supervision, moral disengagement, and negative reciprocity beliefs on cyberloafing, such that the positive relationship between abusive supervision and cyberloafing is strongest at high levels of moral disengagement and negative reciprocity beliefs.", "Keywords": "Cyberloafing ; Abusive supervision ; Moral disengagement ; Negative reciprocity beliefs ; PLS-SEM", "DOI": "10.1016/j.im.2022.103600", "PubYear": 2022, "Volume": "59", "Issue": "2", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Lecturer Department of Marketing Strategy and Innovation, Sunway University, 47500 Bandar Sunway, Selangor, Malaysia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Professor Department of Management, National University of Singapore, 15 Kent Ridge Drive, 119245, Singapore"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Senior Lecturer Department of Management, Multimedia University, Persiaran Multimedia, 63100, Cyberjaya, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Associate Professor Department of Marketing Strategy and Innovation, Sunway University, 47500, Bandar Sunway, Selangor, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Associate Professor School of Social Science, Heriot-Watt University Malaysia, 1, Jalan Venna P5/2, Precinct 5, 62200, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Sessional lecturer Department of Accounting and Finance, Monash University Malaysia, Jalan Lagoon Selatan, Bandar Sunway, 47500, Subang Jaya, Selangor, Malaysia"}], "References": [{"Title": "Abusive supervisors and employees who cyberloaf", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "789", "JournalTitle": "Internet Research"}, {"Title": "The role of abusive supervision and organizational commitment on employees' information security policy noncompliance intention", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "5", "Page": "1383", "JournalTitle": "Internet Research"}, {"Title": "The effects of abusive supervision, emotional exhaustion and organizational commitment on cyberloafing: a moderated-mediation examination", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "497", "JournalTitle": "Internet Research"}]}, {"ArticleId": ********, "Title": "On the classification of foliations of degree three with one singularity", "Abstract": "With the help of the Axiom computer algebra system we give a computer aided proof of the classification of foliations of the projective plane with only one singularity and finite non-trivial isotropy group and study their algebraic solutions.", "Keywords": "Holomorphic foliation ; Algebraic solution ; Singularity", "DOI": "10.1016/j.jsc.2022.01.001", "PubYear": 2022, "Volume": "112", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "S.C. Coutinho", "Affiliation": "Instituto de Computação, Universidade Federal do Rio de Janeiro, Avenida Athos da Silveira Ramos, 274, Cidade Universitária - Ilha do Fundão, 21941-916 Rio de Janeiro, RJ, Brazil"}], "References": []}, {"ArticleId": 92635600, "Title": "Bidirectional difference locating and semantic consistency reasoning for change captioning", "Abstract": "<p>Change captioning is an emerging task to describe the changes between a pair of images. The difficulty in this task is to discover the differences between the two images. Recently, some methods have been proposed to address this problem. However, they all employ unidirectional difference localization to identify the changes. This can lead to ambiguity about the nature of the changes. Instead, we propose a framework with bidirectional difference localization and semantic consistency reasoning to describe the image changes. First, we locate the changes in the two images by capturing bidirectional differences. Then we design a decoder with spatial-channel attention to generate the change caption. Finally, we introduce semantic consistency reasoning to constrain our bidirectional difference localization module and spatial-channel attention module. Extensive experiments on three public data sets show that the performance of our proposed model outperforms the state-of-the-art change captioning models by a large margin.</p>", "Keywords": "change captioning;semantic consistency reasoning;spatial-channel attention", "DOI": "10.1002/int.22821", "PubYear": 2022, "Volume": "37", "Issue": "5", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Sun", "Affiliation": "School of Automation, College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Computing Technology, CAS, Beijing, China"}, {"AuthorId": 3, "Name": "Tingt<PERSON> Yao", "Affiliation": "School of Automation, College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "Tongyv Lu", "Affiliation": "School of Automation, College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 6, "Name": "Chenggang Yan", "Affiliation": "School of Automation, College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Data and Intelligence Department, JD.com, Beijing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Tsinghua University, Beijing, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Digital Environment Research Institute (DERI), Queen Mary University of London, London, UK"}], "References": [{"Title": "Fine-grained visual understanding and reasoning", "Authors": "Jun Yu; Yezhou Yang; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "398", "Issue": "", "Page": "408", "JournalTitle": "Neurocomputing"}, {"Title": "Learning visual relationship and context-aware attention for image captioning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107075", "JournalTitle": "Pattern Recognition"}, {"Title": "Fixed pattern noise reduction for infrared images based on cascade residual attention CNN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "377", "Issue": "", "Page": "301", "JournalTitle": "Neurocomputing"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "Video Captioning Based on Channel Soft Attention and Semantic Reconstructor", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "55", "JournalTitle": "Future Internet"}]}, {"ArticleId": 92635665, "Title": "Smart Agriculture and Food Industry with Blockchain and Artificial Intelligence", "Abstract": "Horticulture is one of the most major human exercises everywhere. Brilliant horticulture incorporates a bunch of advances, gadgets, conventions and computational ideal models to improve farming cycles. Blockchain, Big information, man-made consciousness, cloud and edge processing give capacities and answers for keeping, storing and investigating the huge information created by parts. The entry of a yield from the maker to the purchaser is followed by a cultivating store network. A reasonable stage that grants association individuals to the ranch together is a blockchain-based cultivating store network. This technique dispenses with the necessity for a unified confided in power, go-betweens and business narratives, developing creation and security through supporting outrageous trustworthiness, obligation and wellbeing. In, Industry 4.0 is one of the smart manufacturing methods in various industrial fields. It’s one to create the opportunity nowadays in mass production of the many industry fields. Such as Agricultural Industry, Food Industry, Automotive Industry, Textile Industry, Logistics, etc., In this 4.0 version, we have utilized the human workers more. even though such good technologies like Big Data, Cloud Computing, IoT, etc., To reduce the Human Work Efforts and Save their lives to enhance the technology, that is to turn Supply Chain Management 4.0 into Supply Chain Management 5.0. To, Testing and Creating the Driverless Vehicle for Transporting Using Distributed Ledger Technology. In, The Smart Contract we have to Implement the Face Recognition of Those who send the Agricultural Goods. When Consumer to Read the Smart Contract to satisfy the Face Recognition from the appropriate Producer. This one increases the trustworthiness of Relation between Producer and Consumer. Supply Chain Management 5.0 to Integrate the Emerging technologies in this study such as blockchain and Artificial Intelligence. In, this version to Collaborate the Human and Robot Working. Eventually, it create a Smart Manufacturing Technology in the agricultural food supply chain industry. © 2022 N. Nasurudeen Ahamed and R. Vignesh. This open access article is distributed under a Creative Commons Attribution (CC-BY) 4.0 licens", "Keywords": "Agriculture; Artificial Intelligence; Big Data; Blockchain; Cloud Computing; Distributed Ledger; Horticulture; Industry 4.0; Industry 5.0; IoT; Yield", "DOI": "10.3844/jcssp.2022.1.17", "PubYear": 2022, "Volume": "18", "Issue": "1", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, School of Engineering, Presidency University, Karnataka, Bengaluru, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, School of Engineering, Presidency University, Karnataka, Bengaluru, India"}], "References": []}, {"ArticleId": 92635790, "Title": "Sistem Informasi Pemasaran Dan Pemesanan Berbasis Android (Studi Kasus Pada Agriculture Mart SMKN 63 Jakarta)", "Abstract": "<p>Kegiatan pemasaran yang dilakukan oleh pemilik usaha merupakan suatu bentuk promosi produk atau layanan yang mereka punya kepada konsumen. Pemasaran dapat berupa sebuah iklan produk atau layanan, pen<PERSON><PERSON>, maupun pengiriman produk kepada konsumen. <PERSON><PERSON><PERSON> lain, pemesanan barang adalah kegiatan yang sangat penting dalam pengendalian persediaan barang dalam suatu usaha, baik barang tersebut adalah bahan baku mentah yang digunakan sebagai bahan produksi yang dapat diolah kembali atau sebagai barang yang digunakan untuk kegiatan sehari-hari. Dalam mewujudkan kegiatan pemasaran dan pemesanan produk yang baik maka perlu diimplementasikan pada sebuah aplikasi, sehingga bisa terciptanya kegiatan jual beli yang terukur, tepat, dan akurat dalam penyajian data produk. Maka dari itu penulis mengambil pembahasan Sistem Informasi Pemasaran dan Pemesanan Agriculture Mart Pada SMKN 63 Jakarta Berbasis Android, agar mampu mewujudkan sebuah aplikasi pemesanan yang memudahkan pelaku usaha maupun konsumen.</p>", "Keywords": "Android mobile;Pemasaran;Pemesanan;Sistem Informasi;Unified Modelling Language (UML)", "DOI": "10.30742/melekitjournal.v7i1.181", "PubYear": 2021, "Volume": "7", "Issue": "1", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92635945, "Title": "Efficient skin cancer diagnosis based on deep learning approach using lesions skeleton", "Abstract": "Skin cancer is one of the most threatening cancer all over the world. The early detection of this skin cancer could help dermatologists in saving patients' lives. For that, a computer-aided diagnosis is used for an early evaluation of this kind of cancer. Skeletons of the lesions are an effective representation making it possible to properly describe the shape and size of lesions and thus used to classify them effectively as melanoma or non-melanoma. Therefore, the proposed idea in this paper is to use the lesions skeleton as deep learning entry instead of the original images. Experimentation shows that this idea can both increase the classification rate, in comparison with recent approaches from the literature, and thus reduce the number of layers used to create the deep network. The accuracy of our proposed approach on the well-known ISIC challenge and dermoscopy datasets is 95%, showing the effectiveness of our system. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "CNN; Convolutional neural network; Deep learning; Melanoma; Skeleton; Skin cancer", "DOI": "10.1504/IJCC.2021.120395", "PubYear": 2021, "Volume": "10", "Issue": "5/6", "JournalId": 18415, "JournalTitle": "International Journal of Cloud Computing", "ISSN": "2043-9989", "EISSN": "2043-9997", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Sciences Dhar-Mahraz, USMBA, Fez, Morocco"}, {"AuthorId": 2, "Name": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Sciences Dhar-Mahraz, USMBA, Fez, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Faculty of Sciences Dhar-Mahraz, USMBA, Fez, Morocco"}], "References": []}, {"ArticleId": 92635964, "Title": "Predictive Analytics for Stock Prices using Sentiment Analysis", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022921888", "PubYear": 2022, "Volume": "183", "Issue": "48", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92635969, "Title": "Peer-to-peer storage engine for schemaless immutable data", "Abstract": "We present TaYo, a peer-to-peer storage engine explicitly designed for immutable data which bypasses the file system during the storage. TaYo uses content-addressable storage (CAS) and Cuckoo hashing to generate a hash of the content that then serves as its identity. In TaYo, we have two I/O operations: read and write. To write data to TaYo, we split it into eight chunks, record the structure in a separate index and assign the chunks to worker processes that write concurrently. Each chunk is replicated twice. To read data, the client has to provide the identifier which the index uses to locate and re-assemble the chunks. TaYo uses a semi-active replication technique, a blend of active and passive replication while storing the data. It uses a consensus protocol built on top of Raft to guarantee consistency among the replicas. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "CAS; Content-addressable storage; Data management; Immutable data; Peer-to-peer; Storage engines; Storage systems", "DOI": "10.1504/IJCC.2021.120401", "PubYear": 2021, "Volume": "10", "Issue": "5/6", "JournalId": 18415, "JournalTitle": "International Journal of Cloud Computing", "ISSN": "2043-9989", "EISSN": "2043-9997", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Namibia University of Science and Technology, Windhoek, Namibia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Motor Vehicle Accident Fund, Windhoek, Namibia"}], "References": []}, {"ArticleId": 92636082, "Title": "LightNet+: A dual-source lightning forecasting network with bi-direction spatiotemporal transformation", "Abstract": "<p>Lightning disaster causes a huge threat to human lives and industrial facilities. Data-driven lightning forecasting plays an effective role in alleviating such disaster losses. The forecasting process usually faces multi-source meteorological data characterized by spatiotemporal structure. However, established data-driven forecasting methods are mostly built on classic convolutional and recurrent neural blocks which processes one local neighborhood at a time, failing to capture long-range spatiotemporal dependencies within data. To address this issue, we propose a dual-source lightning forecasting network with bi-direction spatiotemporal transformation, referred to as LightNet (+) . The core of LightNet (+) is a novel module, namely bi-directional spatiotemporal propagator, which aims to model long-range connections among different spatiotemporal locations, going beyond the constraints of the receptive field of a local neighborhood. Moreover, a spatiotemporal encoder is introduced to extract historical trend information from recent observation data. Finally, all the obtained features are organically fused via a non-local spatiotemporal decoder, which then produces final forecasting results. We evaluate LightNet (+) on a real-world lightning dataset from North China and compare it with several state-of-the-art data-driven lightning forecasting methods. Experimental results show that the proposed LightNet (+) yields overall best performance.</p>", "Keywords": "Spatiotemporal sequence prediction; Lightning forecasting; Non-local mechanism; Deep learning; Data mining", "DOI": "10.1007/s10489-021-03089-5", "PubYear": 2022, "Volume": "52", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 4, "Name": "Qingyong Li", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Severe Weather, Chinese Academy of Meteorological Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Severe Weather, Chinese Academy of Meteorological Sciences, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Severe Weather, Chinese Academy of Meteorological Sciences, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Atmospheric and Oceanic Sciences, Fudan University, Shanghai, China"}], "References": [{"Title": "Spatio-temporal attention on manifold space for 3D human action recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "1", "Page": "560", "JournalTitle": "Applied Intelligence"}, {"Title": "AONet: Active Offset Network for crowd flow prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "104022", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Towards novel deep neuroevolution models: chaotic levy grasshopper optimization for short-term wind speed forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S3", "Page": "1787", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 92636408, "Title": "Study on Automatic 3D Facial Caricaturization: From Rules to Deep Learning", "Abstract": "<p>Facial caricature is the art of drawing faces in an exaggerated way to convey emotions such as humor or sarcasm. Automatic caricaturization has been explored both in the 2D and 3D domain. In this paper, we propose two novel approaches to automatically caricaturize input facial scans, filling gaps in the literature in terms of user-control, caricature style transfer, and exploring the use of deep learning for 3D mesh caricaturization. The first approach is a gradient-based differential deformation approach with data driven stylization. It is a combination of two deformation processes: facial curvature and proportions exaggeration. The second approach is a GAN for unpaired face-scan-to-3D-caricature translation. We leverage existing facial and caricature datasets, along with recent domain-to-domain translation methods and 3D convolutional operators, to learn to caricaturize 3D facial scans in an unsupervised way. To evaluate and compare these two novel approaches with the state of the art, we conducted the first user study of facial mesh caricaturization techniques, with 49 participants. It highlights the subjectivity of the caricature perception and the complementarity of the methods. Finally, we provide insights for automatically generating caricaturized 3D facial mesh.</p>", "Keywords": "Caricature; Style transfer; machine learning; Geometry processing; 3D mesh; perceptual study", "DOI": "10.3389/frvir.2021.785104", "PubYear": 2022, "Volume": "2", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "InterDigital, France;Inria, Univ Rennes, CNRS, IRISA, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "InterDigital, France;Institut national des sciences appliquées de Rennes, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inria, Univ Rennes, CNRS, IRISA, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "InterDigital, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "InterDigital, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "InterDigital, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inria, Univ Rennes, CNRS, IRISA, France"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inria, Univ Rennes, CNRS, IRISA, France;Laboratoire Mouvement, Sport, Santé, France"}], "References": [{"Title": "3DFaceGAN: Adversarial Nets for 3D Face Representation, Generation, and Translation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "10-11", "Page": "2534", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Landmark Detection and 3D Face Reconstruction for Caricature using a Nonlinear Parametric Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "101103", "JournalTitle": "Graphical Models"}]}, {"ArticleId": 92636443, "Title": "Better Scrum through Essence", "Abstract": "<p>We live at an exciting time where software has become a dominant aspect of our everyday life. Although software provides opportunities for improving various aspects of our society, it also presents many challenges. One of them is development, deployment, and sustaining of high quality software on a broad scale. While agile methods (Scrum being one of the most prominent examples) ease the process, their popularity deteriorates the clarity and simplicity they were once meant to bring into software development. This article explores the synergy of Scrum and Essence, a domain model of software engineering processes, intending to become a common ground for software development methods, bringing clarity into the composition of methods from individual practices. This short communication motivates the interplay of Scrum and Essence, being accompanied with a set of videotutorials and 21 Scrum Essential cards to further guide more effective team's way of working.</p>", "Keywords": "agile;Essence;methods;Scrum;software engineering practice", "DOI": "10.1002/spe.3070", "PubYear": 2022, "Volume": "52", "Issue": "6", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, Lausanne, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Scrum Inc., Cambridge, Massachusetts, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>, London, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Informatics, Masaryk University, Brno, Czech Republic"}], "References": [{"Title": "Scrum Essentials Cards", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "3", "Page": "83", "JournalTitle": "Queue"}]}, {"ArticleId": 92636447, "Title": "Hybrid particle swarm optimization\n ‐ \n grey wolf optimization based\n small‐signal \n modeling applied to\n GaN \n devices", "Abstract": "<p>In this research, four different equivalent circuit models that characterize the substrate/buffer loading effect for GaN high electron mobility transistor (HEMT) on Si substrate have been investigated. Y -parameter measurements of an open de-embedded structure have been used to characterize this effect in GaN HEMT on Si. An optimization technique based on hybridization between two meta-heuristic techniques, which are particle swarm optimization (PSO) and grey wolf optimization (GWO) has been developed to extract physical relevant model elements. The proposed optimization technique is applied on GaN HEMTs of different sizes and validated by means of S -parameters fitting. In addition, the extracted elements are evaluated in terms of their reliability and scalability. The proposed PSO-GWO technique shows a very good agreement with previous research results. It has been found that the circuit topology of both resistive and capacitive shunt branches is more accurate in characterizing conductive and displacement substrate/buffer leakage current, respectively. The results of this research validate the applicability of the proposed technique and the model topology for small and large-signal modeling of GaN transistors on Silicon substrate.</p>", "Keywords": "GaN HEMT;grey wolf optimization;particle swarm optimization;small-signal modeling;substrate/buffer loading effect", "DOI": "10.1002/mmce.23081", "PubYear": 2022, "Volume": "32", "Issue": "5", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Sharjah College of Engineering, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Sharjah College of Engineering, United Arab Emirates"}], "References": []}, {"ArticleId": 92636449, "Title": "Improved gradual change-based Harris Hawks optimization for real-world engineering design problems", "Abstract": "<p>Harris Hawks optimization (HHO) is a recently introduced meta-heuristic approach, which simulates the cooperative behavior of <PERSON> hawks in nature. In this paper, an improved variant of HHO is proposed, called HHSC, to relieve the main shortcomings of the conventional method that converges either fast or slow and falls in the local optima trap when dealing with complex problems. Two search strategies are added into the conventional HHO. First, the sine function is used to improve the convergence speed of the HHO algorithm. Second, the cosine function is used to enhance the ability of the exploration and exploitation searches during the early and later stages, respectively. The incorporated new two search methods significantly enhanced the convergence behavior and the searchability of the original algorithm. The performance of the proposed HHSC method is comprehensively investigated and analyzed using (1) twenty-three classical benchmark functions such as unimodal, multi-modal, and fixed multi-modal, (2) ten IEEE CEC2019 benchmark functions, and (3) five common engineering design problems. The experimental results proved that the search strategies of HHO and its convergence behavior are significantly developed. The proposed HHSC achieved promising results, and it got better effectiveness in comparisons with other well-known optimization methods.</p>", "Keywords": "Harris Hawks optimizer; Sine cosine algorithm; Benchmark functions; CEC2019; Engineering design problems", "DOI": "10.1007/s00366-021-01571-9", "PubYear": 2023, "Volume": "39", "Issue": "3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Sciences and Informatics, Amman Arab University, Amman, Jordan; School of Computer Sciences, Universiti Sains Malaysia, Pulau Pinang, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Engineering, New York University Abu Dhabi, Abu Dhabi, UAE; Department of Civil and Urban Engineering, Tandon School of Engineering, New York University, Brooklyn, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management Information System, College of Business Administration, Taif University, Taif, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Zagazig University, Zagazig, Egypt; Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates; Faculty of Computer Science & Engineering, Galala University, Suze, Egypt"}], "References": [{"Title": "A novel Harris hawks’ optimization and k-fold cross-validation predicting slope stability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "369", "JournalTitle": "Engineering with Computers"}, {"Title": "Equilibrium optimizer: A novel optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105190", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Boosting salp swarm algorithm by sine cosine algorithm and disrupt operator for feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113103", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel hybrid Harris hawks optimization and support vector machines for drug design and discovery", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "106656", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Salp swarm algorithm: a comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11195", "JournalTitle": "Neural Computing and Applications"}, {"Title": "On the application of Harris hawks optimization (HHO) algorithm to the design of microchannel heat sinks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Pol<PERSON> Sendur", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1409", "JournalTitle": "Engineering with Computers"}, {"Title": "An intensify Harris Hawks optimizer for numerical and engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106018", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-verse optimizer algorithm: a comprehensive survey of its results, variants, and applications", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12381", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Ant Lion Optimizer: A Comprehensive Survey of Its Variants and Applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1397", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Marine Predators Algorithm: A nature-inspired metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113377", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hybrid metaheuristic algorithm using butterfly and flower pollination base on mutualism mechanism for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3665", "JournalTitle": "Engineering with Computers"}, {"Title": "A competitive chain-based Harris Hawks Optimizer for global optimization and multi-level image thresholding problems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106347", "JournalTitle": "Applied Soft Computing"}, {"Title": "RETRACTED ARTICLE: Group search optimizer: a nature-inspired meta-heuristic optimization algorithm with its results, variants, and applications", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2949", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Selection scheme sensitivity for a hybrid Salp Swarm Algorithm: analysis and applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1149", "JournalTitle": "Engineering with Computers"}, {"Title": "A novel upgraded bat algorithm based on cuckoo search and Sugeno inertia weight for large scale and constrained engineering design optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1731", "JournalTitle": "Engineering with Computers"}, {"Title": "A highly efficient chain code for compression using an agent-based modeling simulation of territories in biological beavers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "1", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Improved Slime Mould Algorithm based on Firefly Algorithm for feature selection: A case study on QSAR model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S3", "Page": "2407", "JournalTitle": "Engineering with Computers"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Chaotic binary Group Search Optimizer for feature selection", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116368", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92636484, "Title": "Functionalized 3D H-SnS2-APTES-PTCA complexes with 3D hollow SnS2 as effective co-reaction accelerator for label-free electrochemiluminescence immunosensor", "Abstract": "In this work, a novel, label-free, and self-enhanced electrochemiluminescence (ECL) immunosensor, based on 3,4,9,10-perylenetetracarboxylic acid (PTCA) and three dimensional hollow SnS<sub>2</sub> (3D H-SnS<sub>2</sub>), was constructed to sensitively detect cardiac troponin I (cTnI). The 3D H-SnS<sub>2</sub> was employed as an effective co-reaction accelerator in an ECL system, with the 3D H-SnS<sub>2</sub> and PTCA cross-linked through (3-aminopropyl) triethoxysilane (APTES) to form a self-reinforcing ECL system. Functionalized 3D H-SnS<sub>2</sub>-APTES-PTCA complexes were then prepared and applied for the subsequent experiments. This system had plenty of active sites on the surface of insert-like SnS<sub>2</sub> nanosheets within the 3D H-SnS<sub>2</sub> and as such, it could strongly induce S<sub>2</sub>O<sub>8</sub><sup>2-</sup> to produce more SO<sub>4</sub><sup>•-</sup> radicals. Furthermore, since the luminescent substance and the co-reaction accelerator were connected together, the distance between the SO<sub>4</sub><sup>•-</sup> radicals and the luminescent PTCA could be effectively shortened, thus enabling SO<sub>4</sub><sup>•-</sup> to act immediately on PTCA for significantly enhancing the ECL intensity. By analyzing the mechanism involved, it was found that the conversion between the ion pairs of Sn<sup>4+/2+</sup> played a very important role in enhancing the signal. Based on the above work, the constructed biosensor was used to detect cTnI, a signaling molecule associated with acute myocardial infarction disease. And the sensor, with its linear response range of 16 fg mL<sup>−1</sup>-16 ng mL<sup>−1</sup> and its low detection limit of 1.19 fg mL<sup>−1</sup>, displayed potential application value in the field of clinical analysis.", "Keywords": "PTCA ; 3D H-SnS<sub>2</sub> ; Functionalized complexes ; Signal amplification ; Cardiac troponin I", "DOI": "10.1016/j.snb.2022.131439", "PubYear": 2022, "Volume": "357", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Peng<PERSON>i Li", "Affiliation": "Department of Chemistry and Laboratory for Preparation and Application of Ordered Structural Materials of Guangdong Province, Shantou University, Shantou, Guangdong 515063, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shantou Longhu Ecological Environment Monitoring Station, Shantou, Guangdong 515041, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Shantou Supervision Testing Institute of Quality & Measuring, Shantou, Guangdong 515041, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Shantou Supervision Testing Institute of Quality & Measuring, Shantou, Guangdong 515041, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry and Laboratory for Preparation and Application of Ordered Structural Materials of Guangdong Province, Shantou University, Shantou, Guangdong 515063, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry and Laboratory for Preparation and Application of Ordered Structural Materials of Guangdong Province, Shantou University, Shantou, Guangdong 515063, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Chemical Engineering, Guangdong Academy of Sciences, Guangzhou, Guangdong 510665, PR China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry and Laboratory for Preparation and Application of Ordered Structural Materials of Guangdong Province, Shantou University, Shantou, Guangdong 515063, PR China"}, {"AuthorId": 9, "Name": "Wenhua Gao", "Affiliation": "Department of Chemistry and Laboratory for Preparation and Application of Ordered Structural Materials of Guangdong Province, Shantou University, Shantou, Guangdong 515063, PR China;Corresponding author"}], "References": [{"Title": "2D hexagonal SnS2 nanoplates as novel co-reaction accelerator for construction of ultrasensitive g-C3N4-based electrochemiluminescent biosensor", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "319", "Issue": "", "Page": "128298", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 92636485, "Title": "A Survey on Machine Reading Comprehension Systems", "Abstract": "<p>Machine Reading Comprehension (MRC) is a challenging task and hot topic in Natural Language Processing. The goal of this field is to develop systems for answering the questions regarding a given context. In this paper, we present a comprehensive survey on diverse aspects of MRC systems, including their approaches, structures, input/outputs, and research novelties. We illustrate the recent trends in this field based on a review of 241 papers published during 2016–2020. Our investigation demonstrated that the focus of research has changed in recent years from answer extraction to answer generation, from single- to multi-document reading comprehension, and from learning from scratch to using pre-trained word vectors. Moreover, we discuss the popular datasets and the evaluation metrics in this field. The paper ends with an investigation of the most-cited papers and their contributions.</p>", "Keywords": "Natural Language Processing; question answering; Machine Reading Comprehension; deep learning; literature review", "DOI": "10.1017/S1351324921000395", "PubYear": 2022, "Volume": "28", "Issue": "6", "JournalId": 23332, "JournalTitle": "Natural Language Engineering", "ISSN": "1351-3249", "EISSN": "1469-8110", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer and Information Technology Department, University of Qom, Qom, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer and Information Technology Department, University of Qom, Qom, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer and Information Technology Department, University of Qom, Qom, Iran; Corresponding author."}], "References": [{"Title": "GF-Net: Improving machine reading comprehension with feature gates", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "8", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A Deep Neural Network Framework for English Hindi Question Answering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Improving the robustness of machine reading comprehension model with hierarchical knowledge and auxiliary unanswerability prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106075", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 92636647, "Title": "Quantitative Evaluation of Vitreous Opacities Using Motion Video Through Slit-Lamp Examination", "Abstract": "<p> This study proposed a quantitative evaluation method for vitreous opacities using motion video. The proposed method focused on moving turbidity in the vitreous. The moving turbidity appeared as an inter-frame difference, which was calculated from two consecutive frames. Therefore, the degree of vitreous opacity was estimated using this inter-frame difference. The proposed method was applied in the experiments to actual motion videos obtained using slit-lamp examination. The effectiveness of method was confirmed using the t -test and linear discriminant method. </p>", "Keywords": "vitreous opacities;waxy vision;slit-lamp examination;motion video;moving turbidity", "DOI": "10.20965/jaciii.2022.p0028", "PubYear": 2022, "Volume": "26", "Issue": "1", "JournalId": 13600, "JournalTitle": "Journal of Advanced Computational Intelligence and Intelligent Informatics", "ISSN": "1343-0130", "EISSN": "1883-8014", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology, Kagoshima College  1460-1 Shinko, Hayato, Kirishima, Kagoshima 899-5193, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Technology for Innovation, Yamaguchi University  1677-1 <PERSON><PERSON><PERSON>, Yamaguchi, Yamaguchi 753-8512, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fuzzy Logic Systems Institute  680-41 Kawazu, Iizuka, Fukuoka 820-0067, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ophthalmology, Graduate School of Medicine, Yamaguchi University 1-1-1 Minamikogushi, Ube, Yamaguchi 755-8505, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> 138-101 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Okayama 700-0951, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Takabatake Nishi Ganka"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Takabatake Nishi Ganka"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ophthalmology, Graduate School of Medicine, Yamaguchi University"}], "References": []}, {"ArticleId": 92636650, "Title": "Moving Object Grasping Method of Mechanical Arm Based on Deep Deterministic Policy Gradient and Hindsight Experience Replay", "Abstract": "<p>The mechanical arm is an important component in many types of robots; however, in certain production lines, the conventional grasp strategy cannot satisfy the demands of modern production because of several interference factors such as vibration, noise, and light pollution. This paper proposes a new grasping method for manipulators in stamping automatic production lines. Considering the factors that affect grasping in the production environment, the deep deterministic policy gradient (DDPG) method is selected in this study as the basic reinforcement-learning algorithm, and this algorithm is used to grasp moving objects in stamping automatic production lines. Owing to the low success rate of the conventional DDPG algorithm, the hindsight experience replay (HER) is used to improve the sample utilization efficiency of the agent and learn more effective tracking strategies. Simulation results show an 82% mean success rate of the optimized DDPG-HER algorithm, which is 31% better than that of the conventional DDPG algorithm. This method provides ideas for the research and design of the sorting system used in stamping automation production lines.</p>", "Keywords": "mechanical arm;moving object grabbing;DDPG-HER", "DOI": "10.20965/jaciii.2022.p0051", "PubYear": 2022, "Volume": "26", "Issue": "1", "JournalId": 13600, "JournalTitle": "Journal of Advanced Computational Intelligence and Intelligent Informatics", "ISSN": "1343-0130", "EISSN": "1883-8014", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, China University of Geosciences 388 Lumo Road, Hongshan District, Wuhan, Hubei 430074, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems 388 Lumo Road, Hongshan District, Wuhan, Hubei 430074, China"}], "References": [{"Title": "Suction-based Grasp Point Estimation in Cluttered Environment for Robotic Manipulator Using Deep Learning-based Affordance Map", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "2", "Page": "277", "JournalTitle": "International Journal of Automation and Computing"}, {"Title": "Tracking Control of Pneumatic Artificial Muscle-Activated Robot Arm Based on Sliding-Mode Control", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "3", "Page": "66", "JournalTitle": "Actuators"}]}, {"ArticleId": 92636787, "Title": "Hybrid meta-heuristics for the unrelated parallel machine scheduling problem with setup times", "Abstract": "This study focuses on an unrelated parallel machine scheduling problem with machine and job sequence-dependent setup times (UPMST) aiming to minimise the makespan. Recently, adaptive large neighbourhood search with learning automata (LA-ALNS) has been employed to tackle the UPMST with excellent performance. However, its search efficiency is heavily affected by re-visiting the recent solutions, which is known as the short-term cycle. Moreover, it remains challenging to balance the computational cost and quality of the solutions. To address these challenges, we propose a hybrid meta-heuristic method based on LA-ALNS and tabu search (LA-ALNS-TS). In the proposed algorithm, LA-ALNS is used to guide the exploration and TS is used to alleviate the short-term cycle experienced during the search process. In addition, we design two local search operators to achieve a balanced trade-off between the search efficiency and quality of the solutions. A dynamic perturbation scheme is proposed to improve the solution and help the algorithm escape from local optima. Comprehensive experiments were conducted to assess the performance of the algorithm on two exhaustive benchmarks. Experimental results demonstrate the effectiveness and efficiency of the proposed hybrid meta-heuristic method.", "Keywords": "Unrelated parallel machine scheduling ; Adaptive large neighbourhood search ; Tabu search ; Setup times ; Perturbation scheme", "DOI": "10.1016/j.knosys.2022.108193", "PubYear": 2022, "Volume": "241", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Jiangsu Provincial Engineering Laboratory of Pattern Recognition and Computational Intelligence, Jiangnan University, Wuxi, Jiangsu, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Provincial Engineering Laboratory of Pattern Recognition and Computational Intelligence, Jiangnan University, Wuxi, Jiangsu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Engineering and Computer Science, Victoria University of Wellington, Wellington, New Zealand"}], "References": [{"Title": "Time/sequence-dependent scheduling: the design and evaluation of a general purpose tabu-based adaptive large neighbourhood search algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "4", "Page": "1051", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Modeling and multi-neighborhood iterated greedy algorithm for distributed hybrid flow shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105527", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-objective optimization based on decomposition for flexible job shop scheduling under time-of-use electricity prices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106177", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 92636825, "Title": "Optimal prediction of user mobility based on spatio-temporal matching", "Abstract": "<p>Position tracking has become a critical key component for a huge variety of devices, ranging from mobile telephone location tracking to biodiversity monitoring. The majority of location-based services rely mostly on the user’s ongoing and prospective position, indicating a growing need of forecasting the user’s future location. Together with position prediction, forecasting the trajectories between two terminals is beneficial, because it enables to optimize the travel direction between them. This study tackles the problem of increasing prediction accuracy to its maximum level. The proposed work undergoes two major phases: feature extraction and prediction. Initially, antecedent and consequent features, spatio-temporal matching based features, and matching users based features can all be generated from the raw input data. For more precise prediction the most relevant features are extracted. The features will then be fed into the prediction algorithm, which will forecast user mobility. The prediction phase is constructed with an optimized convolutional neural network (CNN). Moreover, the weight of CNN is fine-tuned via a new improved butterfly optimization algorithm (IBOA), which is a conceptual improvement of standard BOA. At last, the supremacy of the presented approach is proved over other models with respect to varied measures. The accuracy of the proposed work is 18.33%, 26.67%, 33.33%, 55.2%, and 61.67% better than the existing models like HS–EH, GAF-WO, CNN, and GSTF.</p>", "Keywords": "", "DOI": "10.1142/S1793962322500465", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 12484, "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing", "ISSN": "1793-9623", "EISSN": "1793-9615", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "A.J. College of Science and Technology, Thonnakkal, Kerala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Noorul Islam Center for Higher Education, Kumarakovil, Tamil Nadu, India"}], "References": [{"Title": "Mobile crowd location prediction with hybrid features using ensemble learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "556", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Mobility aware autonomic approach for the migration of application modules in fog computing environment", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "5259", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Joint user association and resource allocation in HetNets based on user mobility prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "177", "Issue": "", "Page": "107312", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 92636911, "Title": "Observer design for continuous-time dynamical systems", "Abstract": "We review the main design techniques of state observer design for continuous-time dynamical systems, namely algorithms which reconstruct online the full information of a dynamical process on the basis of partially measured data. Starting from necessary conditions for the existence of such asymptotic observers, we classify the available methods depending on the detectability/observability assumptions they require. We show how each class of observer relies on transforming the system dynamics in a particular normal form which allows the design of an observer, and how each observability condition guarantees the invertibility of its associated transformation and the convergence of the observer. Finally, some implementation aspects and open problems are briefly discussed.", "Keywords": "Observer design ; Observability ; Detectability ; Normal forms ; Kalman observers ; Kalman-like observers ; Observability Gramian ; Extended <PERSON><PERSON> filter ; High-gain observers ; Homogeneous observers ; Triangular forms ; KKL observers ; Nonlinear Luenberger observers", "DOI": "10.1016/j.arcontrol.2021.11.002", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3642, "JournalTitle": "Annual Reviews in Control", "ISSN": "1367-5788", "EISSN": "1872-9088", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre Automatique et Systèmes, Mines Paris, Université PSL, Paris, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LAGEPP CNRS, Université Claude Bernard Lyon 1, Lyon, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "LAGEPP CNRS, Université Claude Bernard Lyon 1, Lyon, France"}], "References": [{"Title": "On the use of low-pass filters in high-gain observers", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "104856", "JournalTitle": "Systems & Control Letters"}, {"Title": "Dynamic Output Feedback Stabilization of Non-uniformly Observable Dissipative Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "4923", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Levant’s differentiator by interconnection of low order blocks", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "14", "Page": "319", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 92637031, "Title": "Arabic Fake News Detection: A Fact Checking Based Deep Learning Approach", "Abstract": "<p>Fake news stories can polarize society, particularly during political events. They undermine confidence in the media in general. Current NLP systems are still lacking the ability to properly interpret and classify Arabic fake news. Given the high stakes involved, determining truth in social media has recently become an emerging research that is attracting tremendous attention. Our literature review indicates that applying the state-of-the-art approaches on news content address some challenges in detecting fake news’ characteristics, which needs auxiliary information to make a clear determination. Moreover, the ‘Social-context-based’ and ‘propagation-based’ approaches can be either an alternative or complementary strategy to content-based approaches. The main goal of our research is to develop a model capable of automatically detecting truth given an Arabic news or claim. In particular, we propose a deep neural network approach that can classify fake and real news claims by exploiting ‘Convolutional Neuron Networks’. Our approach attempts to solve the problem from the fact checking perspective, where the fact-checking task involves predicting whether a given news text claim is factually authentic or fake. We opt to use an Arabic balanced corpus to build our model because it unifies stance detection, stance rationale, relevant document retrieval and fact-checking. The model is trained on different well selected attributes. An extensive evaluation has been conducted to demonstrate the ability of the fact-checking task in detecting the Arabic fake news. Our model outperforms the performance of the state-of-the-art approaches when applied to the same Arabic dataset with the highest accuracy of 91%.</p>", "Keywords": "Fake news detection; fact checking; social media; deep learning; Convolutional Neural Network; Arabic corpus", "DOI": "10.1145/3501401", "PubYear": 2022, "Volume": "21", "Issue": "4", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Sciences Department, College of Sciences, Ferhat Abbas University, Setif, Algeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Sciences Department, College of Sciences, Ferhat Abbas University, Setif, Algeria"}], "References": []}, {"ArticleId": 92637058, "Title": "Investigating the Effect of Preprocessing Arabic Text on Offensive Language and Hate Speech Detection", "Abstract": "<p>Preprocessing of input text can play a key role in text classification by reducing dimensionality and removing unnecessary content. This study aims to investigate the impact of preprocessing on Arabic offensive language classification. We explore six preprocessing techniques: conversion of emojis to Arabic textual labels, normalization of different forms of Arabic letters, normalization of selected nouns from dialectal Arabic to Modern Standard Arabic, conversion of selected hyponyms to hypernyms, hashtag segmentation, and basic cleaning such as removing numbers, kashidas, diacritics, and HTML tags. We also experiment with raw text and a combination of all six preprocessing techniques. We apply different types of classifiers in our experiments including traditional machine learning, ensemble machine learning, Artificial Neural Networks, and Bidirectional Encoder Representations from Transformers (BERT)-based models to analyze the impact of preprocessing. Our results demonstrate significant variations in the effects of preprocessing on each classifier type and on each dataset. Classifiers that are based on BERT do not benefit from preprocessing, while traditional machine learning classifiers do. However, these results can benefit from validation on larger datasets that cover broader domains and dialects.</p>", "Keywords": "Artificial neural networks; offensive language detection; natural language processing; Arabic language; machine learning", "DOI": "10.1145/3501398", "PubYear": 2022, "Volume": "21", "Issue": "4", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kuwait University, Safat, Kuwait"}, {"AuthorId": 2, "Name": "Ozlem Uzuner", "Affiliation": "<PERSON> University, Fairfax, VA, USA"}], "References": []}, {"ArticleId": 92637059, "Title": "Unlikely allies in preventing sexual misconduct: Student led prevention efforts in a technical communication classroom", "Abstract": "<p>Students' participation in relevant service learning can have a unique impact on their institution of higher education, if provided the opportunity. This article explores student-designed sexual misconduct prevention efforts taking place in an undergraduate project management course at one institution of higher education. We found that involving students in particular kinds of campus communication design and implementation simultaneously improved those efforts and offered students the opportunity to participate in impactful civic projects. In our article, we first examine the most common approach to sexual misconduct prevention, while considering its limitations. We then introduce a nontraditional collaboration---technical communication student involvement within prevention work---which resulted in new efforts. Finally, we illustrate how instructors can integrate similar collaborations.</p>", "Keywords": "pedagogy; service learning; student involvement; violence prevention", "DOI": "10.1145/3487213.3487214", "PubYear": 2021, "Volume": "9", "Issue": "4", "JournalId": 75910, "JournalTitle": "Communication Design Quarterly", "ISSN": "2166-1642", "EISSN": "2166-1642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Utah State University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Utah Department of Health"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Utah State University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Utah State University"}], "References": []}, {"ArticleId": 92637131, "Title": "Uncertain moving obstacles avoiding method in 3D arbitrary path planning for a spherical underwater robot", "Abstract": "In order to avoid the risk of obstacles collision during the spherical underwater robot (SUR) move to target points in 3D arbitrary path planning, an underwater obstacle avoiding method was studied. Considering the uncertainty of the movement of obstacles in the actual environment, we present an uncertain moving obstacle avoiding method based on the improved velocity obstacle method. In addition, to reduce the distance and time of obstacle avoidance, the concept of the time of obstacle avoiding was designed. First, the size and velocity information of obstacles are obtained through the camera, which can provide an accurate decision basis for obstacles avoidance in the next step. Then, according to the time when the robot collides with the obstacle, the time of start and end of the obstacle avoidance is determined. The movement direction and velocity of the robot are obtained based on the improved velocity obstacle method and the movement characteristics of SUR. Finally, a detailed 3D arbitrary path planning analysis based on an improved ant colony algorithm was conducted. A series of experiments were carried out in the pool that validates the proposed methods are also presented.", "Keywords": "Spherical underwater robot ; Uncertain dynamic obstacles avoiding ; 3D arbitrary path planning ; Inertial measurement unit and depth sensor localization", "DOI": "10.1016/j.robot.2021.104011", "PubYear": 2022, "Volume": "151", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Kagawa University, Takamatsu, Kagawa 760-0396, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Convergence Medical Engineering System and Healthcare Technology, The Ministry of Industry and Information Technology, School of Life Science and Technology, Beijing Institute of Technology, Beijing, China;Department of Intelligent Mechanical Systems Engineering, Kagawa University, Takamatsu, Kagawa 760-0396, Japan;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Graduate School of Engineering, Kagawa University, Takamatsu, Kagawa 760-0396, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Intelligent Mechanical Systems Engineering, Kagawa University, Takamatsu, Kagawa 760-0396, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Control Engineering, Chengdu University of Information Technology, Chengdu 610225, China"}], "References": [{"Title": "An improved ant colony optimization algorithm based on particle swarm optimization algorithm for path planning of autonomous underwater vehicle", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "8", "Page": "3349", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A highly stable and efficient spherical underwater robot with hybrid propulsion devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "5", "Page": "759", "JournalTitle": "Autonomous Robots"}, {"Title": "Obstacle avoidance in dynamic environments based on velocity space optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "103569", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Obstacle avoidance path planning of unmanned submarine vehicle in ocean current environment based on improved firework-ant colony algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106773", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 92637157, "Title": "A New Neuro-Optimal Nonlinear Tracking Control Method via Integral Reinforcement Learning with Applications to Nuclear Systems", "Abstract": "In this paper, a new infinite horizon optimal tracking control method for continuous-time nonlinear systems is given using an actor-critic structure. This present integral reinforcement learning (IRL) method is a novelty method in adaptive dynamic programming (ADP) algorithms and an online policy iteration algorithm. For the optimal tracking problem, the cost function is defined by tracking errors. Consequently, the goal is to minimize tracking errors toward desired trajectories. Since it is hard to solve the Hamilton-Jacobi-Bellman (HJB) equation for continuous-time nonlinear systems control problems, leveraging the actor-critic architecture with neural networks (NNs) to approximate the tracking error performance index and error control law is necessary. Instead of using conventional neural networks, we employ higher-order polynomials in the whole actor-critic architecture. Finally, we apply this new neuro-optimal tracking method to the 2500MW pressurized water reactor (PWR) nuclear power plant, and simulation results are given to demonstrate the effectiveness of the developed method.", "Keywords": "Integral reinforcement learning ; Nuclear power reactor ; Nonlinear system ; Optimal tracking control ; Neural networks", "DOI": "10.1016/j.neucom.2022.01.034", "PubYear": 2022, "Volume": "483", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin University of Science and Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Harbin University of Science and Technology, China;The State Key Laboratory for Management and Control of Complex Systems, Institute of Automation, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The State Key Laboratory for Management and Control of Complex Systems, Institute of Automation, Chinese Academy of Sciences, Beijing 100190, China;The School of Artificial Intelligence, University of Chinese Academy of Sciences, Beijing 100049, China;The Institute of Systems Engineering, Macau University of Science and Technology, Macau 999078, China;Corresponding author"}, {"AuthorId": 4, "Name": "Jingwei Lu", "Affiliation": "The State Key Laboratory for Management and Control of Complex Systems, Institute of Automation, Chinese Academy of Sciences, Beijing 100190, China;The School of Artificial Intelligence, University of Chinese Academy of Sciences, Beijing 100049, China"}], "References": []}, {"ArticleId": 92637161, "Title": "Power consumption analysis of access network in 5G mobile communication infrastructures — An analytical quantification model", "Abstract": "Energy consumption growth of the fifth-generation (5G) mobile network infrastructure can be significant due to the increased traffic demand for a massive number of end-users with increasing traffic volume, user density, and data rate. The emerging technologies of radio access networks (RAN), e.g., millimeter-wave (mm-wave) communication and large-scale antennas, make a considerable contribution to such an increase in energy consumption. The multiband 2-tier heterogeneous network (HetNet), cloud radio access network (C-RAN), and heterogeneous cloud radio access network (H-CRAN) are considered the prospective RAN architectures of the 5G mobile communication. This paper explores these novel architectures from the energy consumption and network power efficiency perspective considering the varying high volume traffic load, the number of antennas, varying bandwidth, and varying density of low power nodes (LPNs), integrated with mm-wave communication and large-scale multiple antennas. The architectural differences of these networks are highlighted and power consumption analytical models that characterize the energy consumption of radio resource heads (RRHs), base band unit (BBU) pool, fronthaul, macro base station (MBS), and small cell base stations (SCBs) in HetNet, C-RAN, and H-CRAN are developed. The network power efficiency with the consideration of propagation environment and network constraints is investigated to identify the energy-efficient architecture for the 5G mobile network. The simulation results reveal that the power consumption of all these architectures increases in all considered scenarios due to an increase in power consumption of radio frequency components and computation power. Moreover, CRAN is the most energy-efficient RAN architecture due to its cooperative processing and decreased cooling and site support devices and H-CRAN consumes most of the energy compared to other 5G RAN architectures mainly due to a high level of heterogeneity.", "Keywords": "5G mobile network ; Heterogeneous network ; Cloud radio access network ; Heterogeneous cloud radio access network ; Millimeter-wave communication ; Power consumption ; Network power efficiency ; Ultra-dense network", "DOI": "10.1016/j.pmcj.2022.101544", "PubYear": 2022, "Volume": "80", "Issue": "", "JournalId": 4135, "JournalTitle": "Pervasive and Mobile Computing", "ISSN": "1574-1192", "EISSN": "1873-1589", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Zhejiang University, 310027 Hangzhou, China;Faculty of Information and Communication Technology, Balochistan University of Information Technology, Engineering and Management Sciences, Quetta 87300, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Zhejiang University, 310027 Hangzhou, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Information and Communication Technology, Balochistan University of Information Technology, Engineering and Management Sciences, Quetta 87300, Pakistan"}], "References": []}, {"ArticleId": 92637202, "Title": "Deep plug-and-play prior for hyperspectral image restoration", "Abstract": "Deep-learning-based hyperspectral image (HSI) restoration methods have gained great popularity for their remarkable performance but often demand expensive network retraining whenever the specifics of task changes. In this paper, we propose to restore HSIs in a unified approach with an effective plug-and-play method, which can jointly retain the flexibility of optimization-based methods and utilize the powerful representation capability of deep neural networks. Specifically, we first develop a new deep HSI denoiser leveraging gated recurrent convolution units, short- and long-term skip connections, and an augmented noise level map to better exploit the abundant spatio-spectral information within HSIs. It, therefore, leads to the state-of-the-art performance on HSI denoising under both Gaussian and complex noise settings. Then, the proposed denoiser is inserted into the plug-and-play framework as a powerful implicit HSI prior to tackle various HSI restoration tasks. Through extensive experiments on HSI super-resolution, compressed sensing, and inpainting, we demonstrate that our approach often achieves superior performance, which is competitive with or even better than the state-of-the-art on each task, via a single model without any task-specific training.", "Keywords": "Hyperspectral image restoration ; Plug-and-play ; Deep denoising prior ; Inpainting ; Super resolution ; Compressed sensing", "DOI": "10.1016/j.neucom.2022.01.057", "PubYear": 2022, "Volume": "481", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}], "References": [{"Title": "Hyperspectral image classification based on sparse modeling of spectral blocks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "407", "Issue": "", "Page": "12", "JournalTitle": "Neurocomputing"}, {"Title": "Fast algorithm with theoretical guarantees for constrained low-tubal-rank tensor recovery in hyperspectral images denoising", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "413", "Issue": "", "Page": "397", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 92637271, "Title": "Application of Music Industry Based on the Deep Neural Network", "Abstract": "<p>After entering the digital era, digital music technology has prompted the rise of Internet companies. In the process, it seems that Internet music has made some breakthroughs in business models; yet essentially, it has not changed the way music content reaches users. In the past, different traditional and shallow machine learning techniques are used to extract features from musical signals and classify them. Such techniques were cost-effective and time-consuming. In this study, we use a novel deep convolutional neural network (CNN) to extract multiple features from music signals and classify them. First, the harmonic/percussive sound separation (HPSS) algorithm is used to separate the original music signal spectrogram into temporal and frequency components, and the original spectrogram is used as the input of the CNN. Finally, the network structure of the CNN is designed, and the effect of different parameters on the recognition rate is investigated. It will fundamentally change the way music content reaches music users and is a disruptive technology application for the industry. Experimental results show that the proposed recognition rate of the GTZAN dataset is about 73% with no data expansion.</p>", "Keywords": "", "DOI": "10.1155/2022/4068207", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhoukou Normal University, Music & Dance Deparment, Zhoukou, Henan 466000, China"}], "References": [{"Title": "Supervised machine learning for audio emotion recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "637", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Indigenous Food Recognition Model Based on Various Convolutional Neural Network Architectures for Gastronomic Tourism Business Analytics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "322", "JournalTitle": "Information"}]}, {"ArticleId": 92637760, "Title": "A composite framework of deep multiple view human joints feature extraction and selection strategy with hybrid adaptive sunflower optimization‐whale optimization algorithm for human action recognition in video sequences", "Abstract": "<p>In computer vision and pattern recognition field, video-based human action recognition (HAR) is the most predominant research area. Object recognition is needed to recognize the subjects regarding video contents, which allows reactive enquiry in a large number of camera contents, mainly in security based platforms where there is a prevalent growth of closed circuit television cameras. Generally, object detectors that have high performance are trained on a large collection of public benchmarks. Identifying human activities from unconstrained videos is the primary challenging task. Further, the feature extraction and feature selection from these unconstrained videos is also considered as a challenging issue. For that, in this article a new composite framework of HAR model is constructed by introducing an efficient feature extraction and selection strategy. The proposed feature extraction model extracts multiple view features, human joints features based on the domain knowledge of the action and fuses them with deep high level features extracted by an improved fully resolution convolutional neural networks. Also, it optimizes the feature selection strategy using the hybrid whale optimization algorithm and adaptive sun flower optimization that maximizes the feature entropy, correlation. It minimizes the error rate for improving the recognition accuracy of the proposed composite framework. The proposed model is validated on four different datasets, namely, Olympics sports, Virat Release 2.0, HMDB51, and UCF 50 sports action dataset to prove its effectiveness. The simulation results show that the proposed composite framework outperforms all the existing human recognition model in terms of classification accuracy and detection rate.</p>", "Keywords": "adaptive sun flower optimization;deep improved fully resolution convolutional neural networks (DIFR-CNN);feature selection;multiple view;video-based HAR", "DOI": "10.1111/coin.12499", "PubYear": 2022, "Volume": "38", "Issue": "2", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology RMK Engineering College  Chennai India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering RMD Engineering College, Anna University  Chennai India"}], "References": [{"Title": "Correlational Convolutional LSTM for human action recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "224", "JournalTitle": "Neurocomputing"}, {"Title": "Video spatiotemporal mapping for human action recognition by convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "265", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "Hand-crafted and deep convolutional neural network features fusion and selection strategy: An application to intelligent human action recognition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105986", "JournalTitle": "Applied Soft Computing"}, {"Title": "CGA: a new feature selection model for visual human action recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "5267", "JournalTitle": "Neural Computing and Applications"}, {"Title": "African vultures optimization algorithm: A new nature-inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107408", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Artificial gorilla troops optimizer: A new nature‐inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "10", "Page": "5887", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 92637897, "Title": "Review of \"Awful archives: Conspiracy theory, rhetoric, and acts of evidence by <PERSON>,\" <PERSON>, <PERSON><PERSON> (2020). The Ohio State University Press", "Abstract": "<p> Awful Archives presents a timely discussion of controversies and the line between what constitutes \"good\" versus \"bad\" evidence within empiricism and the scientific process. Calling attention to the fact that evidence is rhetorically constructed, <PERSON> implores us to interrogate the conception of bad evidence as equally constructed. Blurring the lines between \"good\" and \"bad\" evidence, <PERSON> moves away from rhetorical conceptions of evidence as imbued \"with a kind of thingfulness \" (p. 5), as this theory of evidence lends itself to clear demarcations between authentic and inauthentic distinctions. Contemporary conceptions of evidence seen through the thing/object binary deny opportunities for nuanced discussions about the evidentiary process and ultimately ignore evidence's ability to do something as a performative property. Ultimately, <PERSON> inquires into evidence as an act through which we attempt to \"figure out what the fuck is happening around us\" (p. 11) without the limiting characteristics of validity or empirical fidelity with which evidence is so often concerned. Alongside her analysis of the ways evidence is implemented, and often weaponized, by conspiracy theorists who frequently challenge the more empirical understandings of what evidence represents, <PERSON> makes the rhetorical move from whether evidence is \"good/bad\" or \"valid/invalid\" to an alternative foundational rhetorical theory of what is the evidence doing. </p>", "Keywords": "", "DOI": "10.1145/3487213.3487215", "PubYear": 2021, "Volume": "9", "Issue": "4", "JournalId": 75910, "JournalTitle": "Communication Design Quarterly", "ISSN": "2166-1642", "EISSN": "2166-1642", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Virginia Polytechnic Institute and State University"}], "References": []}, {"ArticleId": 92637899, "Title": "Improving Dialog System Grounded with Unstructured Knowledge by Domain Adaptive Pre-Training and Post-Ranking", "Abstract": "Linguistic intelligence and the ability to converse with human are important and indispensable parts of humanoid robots. One of the most challenging tasks in knowledge-grounded task-oriented dialog systems (KTDS) is the knowledge selection task, which aims to find the proper knowledge snippets to respond to user dialog requests. In this paper, we first propose domain adapted-BERT (DA-BERT) which employs pre-trained bidirectional encoder representations from transformers (BERT) with domain adaptive training and dynamic masking probability for knowledge selection in KTDS. Domain adaptive training can minimize the domain gap between the general text data that BERT is pre-trained on and the dialog–knowledge joint data while dynamic masking probability enhances the training in an easy-to-hard manner. After knowledge selection, the next task in KTDS is knowledge-grounded generation. To improve the performance in knowledge-grounded generation, we propose GPT-PR to employ post-ranking on the generator’s outputs. Post-ranking eliminates the possibility of generating hallucination response by a large portion during the sampling-based decoding process and thus can improve the quality of the generated response. Experimental results on the benchmark dataset show that our proposed pre-training and post-ranking methods, DA-BERT and GPT-PR, respectively, outperform the state-of-the-art models with large margins across all the evaluation metrics. Moreover, in the experiments, we also analyze the bad cases of DA-BERT and GPT-PR and do visualizations to facilitate further research in this direction.", "Keywords": "Domain adaptive training; dynamic masking probability; post-ranking; knowledge selection; knowledge-grounded generation", "DOI": "10.1142/S0219843621500195", "PubYear": 2021, "Volume": "18", "Issue": "6", "JournalId": 16624, "JournalTitle": "International Journal of Humanoid Robotics", "ISSN": "0219-8436", "EISSN": "1793-6942", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92637935, "Title": "Spatial extrapolation of statistically downscaled weather data over the Northwest Himalayas at major glacier sites", "Abstract": "Generating fine-resolution weather data over the Himalayan region is difficult due to complicated topography and harsh weather. In this study, we set up a Multiple-Point Statistics (MPS) based statistical model to spatially extrapolate fine-resolution rainfall and temperature data in five spatial domains and at ten-point locations of glaciers over the northwest Himalayas. The training domain consists of thirteen years (2001–2013) of daily rainfall and temperature data at 30 and 10-km spatial resolution which generates multiple realizations of fine-resolution information for the year 2014 in the larger domain. Small range of RMSE (2.46–6.68 mm) and MAE (1.12–4.15 mm) for rainfall suggests that MPS performs well in spatial domains. Furthermore, at the glacier sites, we observe that the training data of smaller training domains are insufficient while data of larger domains perform well. Overall, MPS shows encouraging results in extrapolating fine-resolution weather information over the complex region of northwest Himalayas.", "Keywords": "Himalayas ; Multiple-point statistics ; Rainfall extrapolation ; Glaciers ; Direct sampling ; Statistical downscaling", "DOI": "10.1016/j.envsoft.2022.105317", "PubYear": 2022, "Volume": "149", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Science Education and Research Bhopal, Madhya Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Science Education and Research Bhopal, Madhya Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Science Education and Research Bhopal, Madhya Pradesh, India;Corresponding author"}], "References": [{"Title": "A fast edge-based two-stage direct sampling method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "104742", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 92637962, "Title": "A cooperative coevolutionary algorithm approach to the no-wait job shop scheduling problem", "Abstract": "The no-wait job shop is an extension of the well-known job shop scheduling subject to the constraint that the operations of any job, once started, must be processed immediately, one after the other, until the completion of the job. The problem is NP-hard and exact methods can only solve small instances. In the last two decades of advances on this problem, one aspect became central for the success of metaheuristics to tackle it, namely the decomposition of solution into two parts: sequencing and timetabling. Most of these metaheuristics use a permutation to represent the sequencing part, while the timetabling part uses specific rules that differentiate one approach from the other. The main contribution of this work is the proposal of a cooperative coevolutionary algorithm where the sequencing and the timetabling parts interact with each other to evolve quasi-optimal sequencing and timetabling decisions. To this aim, the algorithm co-evolves a population of permutations with a population of binary chains. The permutation decides the sequencing while each bit in the binary chain decides whether or not a job is shifted to the left-most position on its corresponding machine. Therefore, the whole binary chain defines a timetabling rule that is automatically optimized during the evolution process. The algorithm also includes one-step perturbation mechanisms that help improve the solution quality. The proposed algorithm is tested on a set of benchmark instances to compare it with seven state-of-the-art methods. Computational experiments show that the proposed algorithm produces competitive results, furthermore, new best values for four instances are obtained.", "Keywords": "Scheduling ; No-wait ; Job shop ; Makespan ; Cooperative coevolutionary ; Genetic algorithm ; Timetabling", "DOI": "10.1016/j.eswa.2022.116498", "PubYear": 2022, "Volume": "194", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>Alcaraz", "Affiliation": "Universidad Autónoma de Baja California, Ensenada, B.C., Mexico"}, {"AuthorId": 2, "Name": "M.A. Cosío-León", "Affiliation": "Universidad Politécnica de Pachuca, Zempoala, Hidalgo, Mexico"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma de Baja California, Ensenada, B.C., Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CICESE Research Center, Ensenada, B.C., Mexico;Corresponding author"}], "References": [{"Title": "A population-based iterated greedy algorithm for no-wait job shop scheduling with total flow time criterion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "103369", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A population-based iterated greedy algorithm for no-wait job shop scheduling with total flow time criterion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "103369", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A hybrid discrete water wave optimization algorithm for the no-idle flowshop scheduling problem with total tardiness criterion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113166", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A cooperative coevolution algorithm for multi-objective fuzzy distributed hybrid flow shop", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105536", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A crossover operator for improving the efficiency of permutation-based genetic algorithms", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "113381", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Solving no-wait job-shop scheduling problems using a multi-start simulated annealing with bi-directional shift timetabling algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "106615", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Efficient scheduling of a stochastic no-wait job shop with controllable processing times", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "113879", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92638068, "Title": "Methods and tools for causal discovery and causal inference", "Abstract": "<p>Causality is a complex concept, which roots its developments across several fields, such as statistics, economics, epidemiology, computer science, and philosophy. In recent years, the study of causal relationships has become a crucial part of the Artificial Intelligence community, as causality can be a key tool for overcoming some limitations of correlation-based Machine Learning systems. Causality research can generally be divided into two main branches, that is, causal discovery and causal inference. The former focuses on obtaining causal knowledge directly from observational data. The latter aims to estimate the impact deriving from a change of a certain variable over an outcome of interest. This article aims at covering several methodologies that have been developed for both tasks. This survey does not only focus on theoretical aspects. But also provides a practical toolkit for interested researchers and practitioners, including software, datasets, and running examples.</p> <p>This article is categorized under: </p> Algorithmic Development > Causality Discovery Fundamental Concepts of Data and Knowledge > Explainable AI Technologies > Machine Learning <p></p> Abstract</h3> <p>In recent years, the study of causal relationships has become a crucial part of the AI community, as causality can be a key tool for overcoming some limitations of correlation-based machine learning systems. This survey does not only focus on theoretical aspects. It also provides a practical toolkit for interested researchers and practitioners, including software, datasets, and running examples. </p> <picture> </picture> <p ></p> <p></p>", "Keywords": "causal discovery;causal inference;causality", "DOI": "10.1002/widm.1449", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 3682, "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery", "ISSN": "1942-4787", "EISSN": "1942-4795", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LIAAD, INESC TEC, Porto, Portugal; PDCC, Faculdade de Ciências da Universidade do Porto, Porto, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Scuola Normale Superiore, Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Università di Pisa, Pisa, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Università di Pisa, Pisa, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "LIAAD, INESC TEC, Porto, Portugal"}], "References": [{"Title": "Causal Interpretability for Machine Learning - Problems, Methods and Evaluation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "1", "Page": "18", "JournalTitle": "ACM SIGKDD Explorations Newsletter"}, {"Title": "NlinTS: An R Package For Causality Detection in Time Series", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "1", "Page": "21", "JournalTitle": "The R Journal"}]}, {"ArticleId": 92638069, "Title": "Fatigue strength improvement due to alloying steel weld toes with WC tool constituent elements through friction stir processing", "Abstract": "Friction stir processing (FSP) enables surface modifications can likely be applied as a new post-weld treatment for improving fatigue strength. When applying FSP to high-strength materials, tool wear occurring at the interface between the tool tip and the topmost steel layer has been regarded as an unavoidable issue and is related to the tool rotational speed. The present study investigated the relationship between the tool rotational speed and fatigue strength of arc-welded high-strength low-alloy (HSLA) steel joints with weld toes subjected to FSP using a spherical-tip WC tool. FSP was conducted on the weld toe of HSLA steel joints with various tool rotational speeds. Tool wear increased with increase in tool rotational speed, and consequently contents of constituent elements of the WC tool increased in the topmost steel layer of weld toes, leading to large increase in fatigue strength. One reason for the increase with tool rotational speed is significant increase in solid solution hardening due to supersaturated W and C in the topmost steel layer consisting of martensite laths. The hardened topmost steel layer prevented fatigue crack initiation, and the increased fatigue strength depended on the contents of supersaturated W and C. Alloying of the topmost steel layer with tool constituent elements of W and C accompanied with WC tool wear during FSP is unique additive manufacturing to increase the fatigue strength of welded joints, and can be employed locally on structural components susceptible to fatigue.", "Keywords": "Friction stir processing; Tool wear; Alloying; Fatigue strength; Post-weld treatment; High-strength low-alloy steel", "DOI": "10.1007/s00170-022-08690-7", "PubYear": 2022, "Volume": "119", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Joining and Welding Research Institute, Osaka University, Ibaraki, Japan"}, {"AuthorId": 2, "Name": "Shodai <PERSON>", "Affiliation": "Joining and Welding Research Institute, Osaka University, Ibaraki, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Joining and Welding Research Institute, Osaka University, Ibaraki, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Joining and Welding Research Institute, Osaka University, Ibaraki, Japan"}], "References": []}, {"ArticleId": 92638072, "Title": "Recommendations for visual feedback about problems within BPMN process models", "Abstract": "<p>Business process modeling is a key task in business process management because, besides representing processes, the process models are used, for example, for communication purposes among stakeholders. When not correctly modeled, process models may diminish businesses’ profitability. In this work, we conducted a survey with 57 participants, where we gathered a list of modelers’ needs regarding the feedback they would like to get about problems in process models. For example, modelers would like to get feedback according to their level of experience and be able to activate/deactivate automatic validation. Then, we built a catalog of required features that represents a set of features that process modeling tools should address regarding feedback about problems in process models. Furthermore, we mapped the identified modelers’ needs to how a group of process modeling tools provides such kind of feedback and to the solutions found in the literature. Finally, based on the gaps found in the mapping, we provide a set of recommendations for visual feedback about problems in process models, which can guide the development of future process modeling tools. Our work focuses on the Business Process Model and Notation because it is an ISO standard, supported by several process modeling and execution tools. </p>", "Keywords": "Process modeling; Problems in process models; Survey; Recommendations; Visual feedback", "DOI": "10.1007/s10270-021-00972-0", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, Federal University of Rio Grande do Sul, Porto Alegre, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Informatics, Federal University of Rio Grande do Sul, Porto Alegre, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, Federal University of Rio Grande do Sul, Porto Alegre, Brazil"}], "References": []}, {"ArticleId": 92638075, "Title": "Promoting social diversity for the automated learning of complex MDE artifacts", "Abstract": "<p>Software modeling activities typically involve a tedious and time-consuming effort by specially trained personnel. This lack of automation hampers the adoption of model-driven engineering (MDE). Nevertheless, in the recent years, much research work has been dedicated to learn executable MDE artifacts instead of writing them manually. In this context, mono- and multi-objective genetic programming (GP) has proven being an efficient and reliable method to derive automation knowledge by using, as training data, a set of examples representing the expected behavior of an artifact. Generally, conformance to the training example set is the main objective to lead the learning process. Yet, single fitness peak, or local optima deadlock, a common challenge in GP, hinders the application of GP to MDE. In this paper, we propose a strategy to promote populations’ social diversity during the GP learning process. We evaluate our approach with an empirical study featuring the case of learning well-formedness rules in MDE with a multi-objective genetic programming algorithm. Our evaluation shows that integration of social diversity leads to more efficient search, faster convergence, and more generalizable results. Moreover, when the social diversity is used as crowding distance, this convergence is uniform through a hundred of runs despite the probabilistic nature of GP. It also shows that genotypic diversity strategies cannot achieve comparable results.</p>", "Keywords": "Genetic programming; Model-driven engineering; Social diversity", "DOI": "10.1007/s10270-021-00969-9", "PubYear": 2022, "Volume": "21", "Issue": "3", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "IN3-SOM - Universitat Oberta de Catalunya, Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Diro-Geodes - Université de Montréal, Montreal, Canada"}], "References": []}, {"ArticleId": 92638089, "Title": "A systematic literature review on wearable health data publishing under differential privacy", "Abstract": "<p>Wearable devices generate different types of physiological data about the individuals. These data can provide valuable insights for medical researchers and clinicians that cannot be availed through traditional measures. Researchers have historically relied on survey responses or observed behavior. Interestingly, physiological data can provide a richer amount of user cognition than that obtained from any other sources, including the user himself. Therefore, the inexpensive consumer-grade wearable devices have become a point of interest for the health researchers. In addition, they are also used in continuous remote health monitoring and sometimes by the insurance companies. However, the biggest concern for such kind of use cases is the privacy of the individuals. A few privacy mechanisms, such as abstraction and k -anonymity, are widely used in information systems. Recently, differential privacy (DP) has emerged as a proficient technique to publish privacy sensitive data, including data from wearable devices. In this paper, we have conducted a systematic literature review (SLR) to identify, select and critically appraise researches in DP as well as to understand different techniques and exiting use of DP in wearable data publishing. Based on our study, we have identified the limitations of proposed solutions and provided future directions.</p>", "Keywords": "Wearable; Health data; Real-time health data; Privacy; Differential privacy", "DOI": "10.1007/s10207-021-00576-1", "PubYear": 2022, "Volume": "21", "Issue": "4", "JournalId": 25892, "JournalTitle": "International Journal of Information Security", "ISSN": "1615-5262", "EISSN": "1615-5270", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shahjalal University of Science and Technology, Sylhet, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shahjalal University of Science and Technology, Sylhet, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "La Trobe University, Bundoora, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "BRAC University, Dhaka, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shahjalal University of Science and Technology, Sylhet, Bangladesh"}], "References": [{"Title": "Optimization of privacy-utility trade-offs under informational self-determination", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "488", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Differential privacy in blockchain technology: A futuristic approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "50", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "First human–robot archery competition: a new humanoid robot challenge", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "", "Page": "e22", "JournalTitle": "The Knowledge Engineering Review"}]}, {"ArticleId": 92638240, "Title": "Truth as social practice in a digital era: iteration as persuasion", "Abstract": "This article reflects on the problem of false belief produced by the integrated psychological and algorithmic landscape humans now inhabit. Following the work of scholars such as <PERSON> ( Post-Truth , MIT Press, 2018) or <PERSON><PERSON><PERSON> and <PERSON> ( The Misinformation Age: How False Beliefs Spread , Yale University Press, 2019) it combines recent discussions of fake news, post-truth, and science denialism across the disciplines of political science, computer science, sociology, psychology, and the history and philosophy of science that variously address the ineffectiveness, in a digital era, of countering individual falsehoods with facts. Truth and falsehood, it argues, rather than being seen as properties or conditions attached to individual instances of content, should now be seen as collective, performative, and above all persuasive phenomena. They should be practically evaluated as networked systems and mechanisms of sharing in which individually targeted actions are combining with structural tendencies (both human and mechanical) in unprecedented ways. For example, the persuasive agency of apparent consensus (clicks, likes, bots, trolls) is newly important in a fractured environment that only appears to be, but is no longer ‘public’; the control of narratives, labels, and associations is a live, time-sensitive issue, a continuous contest, or ongoing cusp. Taking a social approach to truth yields observations of new relevance; from how current strategies of negative cohesion, blame, and enemy-creation depend crucially on binary ways of constructing the world, to how the offer of identity/community powerfully cooperates with the structural tendencies of algorithm-driven advertiser platforms towards polarisation. Remedies for these machine-learned and psychological tendencies lie in end-user education. So the Arts and Humanities, whether via comparisons with previous historical periods, or via principles of critical thinking and active reading, offer crucial resources to help counter what since 1997 silicon valley executives and scholars have called ‘persuasive technology’ (Fogg in Persuasive Technology: Using Computers to Change What we Think and Do , Morgan Kaufmann, 2003; Hamari et al. (eds) in Persuasive Technology , Springer International Publishing, 2014; Harris in How a Handful of Tech Companies Control Billions of Minds Every Day , 2017; Lanier in Who Owns the Future? Simon & Schuster, 2014 and Ten Arguments for Deleting your Social Media Accounts Right Now , Picador, 2019). The article proposes a paradigm shift in public understandings of this new social environment: from a culture of discovery, where what matters is what exists or is in fact the case, to a culture of iteration, where what matters is what gets repeated.", "Keywords": "Misinformation; Post-truth; Fake news; Repetition; Persuasive technology; Digital public sphere; Social media; Recommender algorithms; Polarisation; Binary oppositions", "DOI": "10.1007/s00146-021-01306-w", "PubYear": 2023, "Volume": "38", "Issue": "5", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON> L. <PERSON>", "Affiliation": "Centre for Research in the Arts, Social Sciences and Humanities (CRASSH), University of Cambridge, Cambridge, UK"}], "References": [{"Title": "‘Pretending to favour the public’: how Facebook’s declared democratising ideals are reversed by its practices", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "5", "Page": "2105", "JournalTitle": "AI & SOCIETY"}]}, {"ArticleId": 92638293, "Title": "A Survey of Neural Network Hardware Accelerators in Machine Learning", "Abstract": "", "Keywords": "", "DOI": "10.5121/mlaij.2021.8402", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 20332, "JournalTitle": "Machine Learning and Applications: An International Journal", "ISSN": "", "EISSN": "2394-0840", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Manar AlSaraf", "Affiliation": ""}], "References": []}, {"ArticleId": 92638425, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0957-4174(22)00076-8", "PubYear": 2022, "Volume": "192", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [], "References": []}, {"ArticleId": 92638472, "Title": "A novel integration between service-oriented IoT-based monitoring with open architecture of CNC system monitoring", "Abstract": "<p>Smart factories are focusing on bridging the gap between physical and cyber-physical systems through software applications. This article proposed an efficient and sufficient data acquisition, storing and processing real-time monitoring information, response, and feedback in milling process monitoring. A methodology to enable integration between service-oriented IoT-based monitoring with interpreted information for open architecture CNC system was presented. The proposed system comprises four main layers: the perception layer, communication layer, application layer, and CNC machine. The developed system was validated through two case studies. First, the developed system successfully enabled data flow through the validation, from CNC machine back to CNC machine. Secondly, the reading of temperature, vibration, and electric current monitoring is higher for the worn cutting tool than the new cutting tool. Third, the percentage difference between new and worn cutting tools for temperature monitoring is up to 3.38%, and for vibration monitoring, it is up to 78.93%. Fourth, the electric current reading is proportional to cutting force as the reading of electric current on cutting insert is higher than reading before cutting tool insert with percentage differences more than 8.33% up to 20%. Based on the findings, it was summarized that the developed integrated monitoring system is feasible enough based on the performance and highly sensitive to any changes that occurred during the machining process, specifically on the cutting tool condition. In the future, this integrated monitoring system could be applied to other open CNC machine-based plug and play.</p>", "Keywords": "CNC machine monitoring; SOA; IoT; OAC; Interpreter; STEP-NC", "DOI": "10.1007/s00170-022-08675-6", "PubYear": 2024, "Volume": "131", "Issue": "11", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Manufacturing Engineering, Universiti Tun Hussein Onn Malaysia, UTHM, Batu Pahat, Malaysia; Department of Mechanical Engineering, Politeknik Sultan <PERSON>, Behrang Perak, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Manufacturing Engineering, Universiti Tun Hussein <PERSON>, UTHM, Batu <PERSON>, Malaysia; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Mechanical and Manufacturing Engineering, Universiti Tun Hussein Onn Malaysia, UTHM, Batu Pahat, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Politeknik Sultan <PERSON>, Shah <PERSON>, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Manufacturing Engineering Technology, Universiti Teknikal Malaysia Melaka Jalan Hang Tuah Jaya, Melaka, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Faculty of Engineering, Universiti Teknologi Malaysia UTM Skudai, Johor Bahru, Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Manufacturing Engineering, Universiti Tun Hussein Onn Malaysia, UTHM, Batu Pahat, Malaysia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Manufacturing Engineering, Universiti Tun Hussein Onn Malaysia, UTHM, Batu Pahat, Malaysia"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Manufacturing Engineering, Universiti Tun Hussein Onn Malaysia, UTHM, Batu Pahat, Malaysia; Department of Petroleum Engineering College of Engineering, University of Basrah, Basrah, Iran"}], "References": [{"Title": "A method of NC machine tools intelligent monitoring system in smart factories", "Authors": "<PERSON>; <PERSON><PERSON>in <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101842", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "STEP-NC feature-oriented high-efficient CNC machining simulation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2363", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Research and development of monitoring system and data monitoring system and data acquisition of CNC machine tool in intelligent manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "2", "Page": "172988141989801", "JournalTitle": "International Journal of Advanced Robotic Systems"}, {"Title": "A review of the application of component-based software development in open CNC systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "9-10", "Page": "3727", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Machine monitoring system: a decade in review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "11-12", "Page": "3645", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Optimization and execution of multiple holes-drilling operations based on STEP-NC", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "7-8", "Page": "2031", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A novel integrating between tool path optimization using an ACO algorithm and interpreter for open architecture CNC system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "114988", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92638476, "Title": "Recognition of abnormal patterns in industrial processes with variable window size via convolutional neural networks and AdaBoost", "Abstract": "<p>In industrial settings, it is inevitable to encounter abnormal patterns monitoring a process. These patterns point out manufacturing faults that can lead to significant internal and external failure costs unless treated promptly. Thus, detecting such abnormalities is of utmost importance. Machine learning algorithms have been widely applied to this problem. Nevertheless, the existing control chart pattern recognition (CCPR) method can only deal with a fixed input size rather than dealing with different input sizes according to the actual production needs. In order to tackle this problem, an original CCPR method relying on convolutional neural network (CNN) named as VIS-CNN is proposed. Signal resizing is performed using resampling methods, then CNN is used to extract the abnormal patterns in the dataset. Five different input sizes are generated for model training and testing. The optimal hyperparameters, as well as the best structure of the used CNN are obtained using Bayesian Optimization. Simulation results show that the correct recognition rate of the VIS-CNN is 99.78%, based on different window size control charts. Furthermore, we address the issue of the mixed CCP and provide a modified scheme to achieve high recognition ratio for 8 mixed patterns on top of 6 standard patterns. The modified scheme includes wavelet noise reduction and Adaptive Boosting. A case study on metal galvanization process is presented to show that the method has potential applications in the industrial environment.</p>", "Keywords": "CCPR; Mixed CCP; CNN; Deep learning; Bayesian optimization; Control chart pattern recognition", "DOI": "10.1007/s10845-021-01907-8", "PubYear": 2023, "Volume": "34", "Issue": "4", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Advanced Design and Systems Engineering, City University of Hong Kong, Kowloon, Hong Kong; Department of Mechanical Engineering, Benha Faculty of Engineering, Benha University, Benha, Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Advanced Design and Systems Engineering, City University of Hong Kong, Kowloon, Hong Kong; Shenzhen Research Institute, City University of Hong Kong, Shenzhen, China"}], "References": [{"Title": "Control chart pattern recognition using the convolutional neural network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "703", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Efficient mixture control chart pattern recognition using adaptive RBF neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1271", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A cost-sensitive convolution neural network learning for control chart pattern recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113275", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A condition monitoring approach for machining process based on control chart pattern recognition with dynamically-sized observation windows", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106360", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 92638554, "Title": "Organic Emitters Showing Excited-States Energy Inversion: An Assessment of MC-PDFT and Correlation Energy Functionals Beyond TD-DFT", "Abstract": "<p>The lowest-energy singlet (S1) and triplet (T1) excited states of organic conjugated chromophores are known to be accurately calculated by modern wavefunction and Time-Dependent Density Functional Theory (TD-DFT) methods, with the accuracy of the latter heavily relying on the exchange-correlation functional employed. However, there are challenging cases for which this cannot be the case, due to the fact that those excited states are not exclusively formed by single excitations and/or are affected by marked correlation effects, and thus TD-DFT might fall short. We will tackle here a set of molecules belonging to the azaphenalene family, for which research recently documented an inversion of the relative energy of S1 and T1 excited states giving rise to a negative energy difference (ΔEST) between them and, thereby, contrary to most of the systems thus far treated by TD-DFT. Since methods going beyond standard TD-DFT are not extensively applied to excited-state calculations and considering how challenging this case is for the molecules investigated, we will prospectively employ here a set of non-standard methods (Multi-Configurational Pair Density Functional Theory or MC-PDFT) and correlation functionals (i.e., <PERSON><PERSON> and <PERSON><PERSON>) relying not only on the electronic density but also on some modifications considering the intricate electronic structure of these systems.</p>", "Keywords": "TD-DFT; MC-PDFT; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; OLEDs TD-DFT ; MC-PDFT ; <PERSON><PERSON> ; <PERSON><PERSON><PERSON> ; OLEDs", "DOI": "10.3390/computation10020013", "PubYear": 2022, "Volume": "10", "Issue": "2", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Authors to whom correspondence should be addressed.↑These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Authors to whom correspondence should be addressed.↑These authors contributed equally to this work"}], "References": []}, {"ArticleId": 92638622, "Title": "Sales System Using Apriori Algorithm to Analyze Consumer Purchase Patterns", "Abstract": "<p>This study aims to create a sales system to get order data on time, not too late to result in days, and the data becomes structured. As well as develop solutions to process sales transaction data which will increasingly use a priori algorithms to find out consumer buying patterns so that they can be output for decision making or knowledge. This study uses a qualitative method to deepen understanding of the phenomena currently happening as profoundly as possible. This shows the importance of depth and detail of the data studied. The system development uses the waterfall method because it fits perfectly with the needs of the system to be built. From the results of the study, calculating a sample of transaction data with a total of 12 data on August 7-8, 2021, using the Tanagra tools resulted in a rule association that if you buy a vortex, you will buy a Caraco with a support value of 58% and a confidence value of 100%, having a lift ratio value of 1.3 stated that the two products have a solid attachment to each other. Followed by if you buy Faraco, you will purchase a vortex. If you believe in a crystal, you will buy an arco that meets the specified parameter criteria with a minimum support value of 20% and minimum confidence of 50%.</p>", "Keywords": "sales;Data mining;Apriori algorithms", "DOI": "10.36805/bit-cs.v3i1.2049", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 76190, "JournalTitle": "Buana Information Technology and Computer Sciences (BIT and CS)", "ISSN": "2715-2448", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Elfina Novalia", "Affiliation": "Universitas Buana Perjuangan Karawang"}, {"AuthorId": 2, "Name": "Apriade Voutama", "Affiliation": "Universitas Singaperbangsa <PERSON>wang"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AKAMIGAS Balongan"}], "References": []}, {"ArticleId": 92638750, "Title": "Outcomes of training in smart home technology adoption", "Abstract": "<p>While various forms of smart home technology have been available for decades, they have yet to achieve widespread adoption. Although they have risen in popularity during recent years, the general public continue to rate smart home devices as overly complex compared to their benefits. This article reports the results of an eight-month study into the effects of training on smart home technology adoption. Building upon the results of a previous study, and using the same living laboratory approach, we studied the effects of training on the attitudes of a group of residents toward use of smart home technology. Results show that training influences those attitudes toward smart home technology, including increased confidence in future use, and increased actual use of more complex smart home features. Results also indicate that users tended to seek out other users rather than training materials for advice, and that privacy concerns were not a deterrent to using smart home devices.</p>", "Keywords": "smart home technology; technology adoption; technology diffusion; training and technology; usability", "DOI": "10.1145/3468859.3468861", "PubYear": 2021, "Volume": "9", "Issue": "3", "JournalId": 75910, "JournalTitle": "Communication Design Quarterly", "ISSN": "2166-1642", "EISSN": "2166-1642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Missouri University of Science and Technology"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Missouri University of Science and Technology"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Missouri University of Science and Technology"}], "References": [{"Title": "Knowledge, Perceived Benefits, Adoption, and Use of Smart Home Products", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "10", "Page": "922", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 92638768, "Title": "Dimensionality reduction for multivariate time-series data mining", "Abstract": "<p>A multivariate time series is one of the most important objects of research in data mining. Time and variables are two of its distinctive characteristics that add the complication of the algorithms applied to data mining. Reduction in the dimensionality is often regarded as an effective way to address these issues. In this paper, we propose a method based on principal component analysis (PCA) to effectively reduce the dimensionality. We call it “piecewise representation based on PCA” (PPCA), which segments multivariate time series into several sequences, calculates the covariance matrix for each of them in terms of the variables, and employs PCA to obtain the principal components in an average covariance matrix. The results of the experiments, including retained information analysis, classification, and a comparison of the central processing unit time consumption, demonstrate that the PPCA method used to reduce the dimensionality in multivariate time series is superior to the prior methods.</p>", "Keywords": "Covariance matrix; Piecewise representation; Principal component analysis; Time-series data mining", "DOI": "10.1007/s11227-021-04303-4", "PubYear": 2022, "Volume": "78", "Issue": "7", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Business Administration, Huaqiao University, Quanzhou, China;Oriental Enterprise Management Research Center, Huaqiao University, Quanzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Business Administration, Huaqiao University, Quanzhou, China;Oriental Enterprise Management Research Center, Huaqiao University, Quanzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Business Administration, Huaqiao University, Quanzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Humanities and Arts, National Taipei University of Education, Taipei, Taiwan;Graduate Institute of Global Business and Strategy, National Taiwan Normal University, Taipei, Taiwan"}], "References": [{"Title": "Analysis of interpolation algorithms for the missing values in IoT time series: a case of air quality in Taiwan", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "8", "Page": "6475", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Grasshopper optimization algorithm with principal component analysis for global optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "7", "Page": "5609", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Efficient genetic algorithm for feature selection for early time series classification", "Authors": "<PERSON><PERSON><PERSON> Ahn; Sun Hur", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106345", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Clustering and classification of time series using topological data analysis with applications to finance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "113868", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Time works well: Dynamic time warping based on time weighting for time series data mining", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "592", "JournalTitle": "Information Sciences"}, {"Title": "Multivariate time-series clustering based on component relationship networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114649", "JournalTitle": "Expert Systems with Applications"}, {"Title": "RETRACTED ARTICLE: An improved privacy-preserving data mining technique using singular value decomposition with three-dimensional rotation data perturbation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "9", "Page": "10003", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A voxelized point clouds representation for object classification and segmentation on 3D data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "1", "Page": "1479", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 92638772, "Title": "Uniform scheduling of interruptible garbage collection and request IO to improve performance and wear-leveling of SSDs", "Abstract": "<p>In the past ten years, solid-state drive (SSD) has become one of the mainstream storage devices due to its performance advantages. However, how to reduce the impact of garbage collection operations on host-side IO is still a challenging issue in SSD firmware design. To solve this problem, this paper proposes a uniform scheduling of interruptible garbage collection and host IO (USIGC). The main contributions of USIGC include: First, USIGC sets up an interruptible garbage collection sub-request queue, and then uniformly schedules this sub-request queue with the host IO queue. In this way, the idle time of each channel is fully utilized to complete valid page migration and erase operation of interrupt garbage collection. Second, USIGC predicts the probability that the erase operation can be completed in the current idle time based on the historical idle interval and then makes the erase operation decision to reduce the probability of blocking the host IO. Third, by converting the amount of data that can be written in future to the present and taking the number of invalid pages of the current block as the selection basis of the victim block, the garbage collection and wear-leveling can be unified. Experimental results show that, compared with existing works, USIGC can reduce the average response time and max wait time and achieve the best wear-leveling performance.</p>", "Keywords": "Solid-state drive; Scheduling; Garbage collection; Wear-leveling", "DOI": "10.1007/s11227-021-04294-2", "PubYear": 2022, "Volume": "78", "Issue": "7", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "Xiaochong Kong", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "Jiecheng Bao", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>eng<PERSON>u", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}], "References": [{"Title": "A novel method for victim block selection for NAND flash-based solid state drives based on scoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "12", "Page": "10186", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 92638927, "Title": "Agents of Exploration and Discovery", "Abstract": "<p>Autonomous agents have many applications in familiar situations, but they also have great potential to help us understand novel settings. In this paper, I propose a new challenge for the AI research community: developing embodied systems that not only explore new environments but also characterize them in scientific terms. Illustrative examples include autonomous rovers on planetary surfaces and unmanned vehicles on undersea missions. I review two relevant paradigms: robotic agents that explore unknown areas and computational systems that discover scientific models. In each case, I specify the problem, identify component functions, describe current abilities, and note remaining limitations. Finally, I discuss obstacles that the community must overcome before it can develop integrated agents of exploration and discovery.</p>", "Keywords": "", "DOI": "10.1609/aimag.v42i4.15089", "PubYear": 2022, "Volume": "42", "Issue": "4", "JournalId": 18861, "JournalTitle": "AI Magazine", "ISSN": "0738-4602", "EISSN": "0738-4602", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Defense Analyses"}], "References": []}, {"ArticleId": 92638978, "Title": "Detection of Fake News using Machine Learning Models", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022921874", "PubYear": 2022, "Volume": "183", "Issue": "47", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92638980, "Title": "Data openness for efficient e-governance in the age of big data", "Abstract": "The data revolution in recent years has led governments around the world to realise the different benefits of communicating and opening data over their information and communication technologies (ICTs) in behalf of their citizens. Indeed, the need for data openness is vitally important for governments, research community and businesses, especially in the era of big data, which characterised by the increase in volume of structured and unstructured data, the speed at which data is generated and collected and the variety of data sources; this is known as the three Vs. Therefore, big data has changed the ways governments manage and support their policies towards their digital data and tend to make it more open and accessible. This 'open data' movement has been adopted by several countries thanks to its multiple benefits in different domains to uncover hidden patterns and improve e-governance effectiveness in terms of cost, productivity and innovation. Through using machine learning algorithms, this paper demonstrates that governments applying open policies are the same as those who get a high score in terms of Human Development Index. To fulfil paper's objectives, the powerful statistical tool named 'IBM SPSS Statistics' is used to accomplish the entire analytical process. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Big data; E-governance; Open data; Regression algorithms", "DOI": "10.1504/IJCC.2021.120391", "PubYear": 2021, "Volume": "10", "Issue": "5/6", "JournalId": 18415, "JournalTitle": "International Journal of Cloud Computing", "ISSN": "2043-9989", "EISSN": "2043-9997", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ASIA Team, M2I Laboratory, Department of Computer Science, Faculty of Sciences and Techniques, Moulay Ismail University of Meknes, BP 509 Boutalamine, Errachidia, 52000, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ASIA Team, M2I Laboratory, Department of Computer Science, Faculty of Sciences and Techniques, Moulay Ismail University of Meknes, BP 509 Boutalamine, Errachidia, 52000, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ASIA Team, M2I Laboratory, Department of Computer Science, Faculty of Sciences and Techniques, Moulay Ismail University of Meknes, BP 509 Boutalamine, Errachidia, 52000, Morocco"}], "References": []}, {"ArticleId": 92639068, "Title": "Internet addiction in young adults: A meta-analysis and systematic review", "Abstract": "Internet addiction, although not recognized by the WHO or the APA, is a serious and problematic pathology. This meta-analysis shows that the incidence of Internet addiction in adults was high in recent years (2017–2020). The effect size returned according to the random effects model is Z = 24.63; SE = 0.205; p = .001. In addition, high heterogeneity is evident in the research addressing this topic (Q = 1240.719, df = 36, p &lt; .001; I2 = 97.09%). On the other hand, the <PERSON><PERSON> test indicated an absence of publication bias. The sample consisted of 30 studies with k = 37 samples from Europe, Asia, America and Oceania. The total sample was constituted by 21,378 participants (51.22% male, 48.78% women; Mean age = 23.55 years). The statistical analyses of meta-regression and model comparison show a complex problem at the international level, explained by age and sex, and apparently also by geographical area. The results of the systematic review show the increase of internet addiction in the new generations, with other variables playing a relevant role, such as: Increase of individualism, lower sociability and enculturation. We conclude highlighting the need to address this problem from a public health approach.", "Keywords": "Internet addiction ; Young ; Meta-analysis ; FOMO", "DOI": "10.1016/j.chb.2022.107201", "PubYear": 2022, "Volume": "130", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "PhD Student in Department of Psychology and Sociology in University of Zaragoza (Faculty of Humanities and Science Education), Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "PhD Student in Department of Psychology and Sociology in University of Zaragoza (Faculty of Humanities and Science Education), Spain;Department of Science Education, in University of Zaragoza (Faculty of Humanities and Science Education), Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology in University of Valladolid and Vice-Dean of School Internships at the Faculty of Education (Duques de Soria Campus), Spain"}], "References": [{"Title": "Maximizing social outcomes? Social zapping and fear of missing out mediate the effects of maximization and procrastination on problematic social networks use", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "106296", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 92639291, "Title": "Garbage Classification and Detection for Urban Management", "Abstract": "", "Keywords": "", "DOI": "10.14445/22312803/IJCTT-V69I9P104", "PubYear": 2021, "Volume": "69", "Issue": "9", "JournalId": 32518, "JournalTitle": "International Journal of Computer Trends and Technology", "ISSN": "2349-0829", "EISSN": "2231-2803", "Authors": [{"AuthorId": 1, "Name": "Kishan PS", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92639315, "Title": "Time-Based Meticulous Analysis of Pandemic Spreading Ratio using Simpy Framework", "Abstract": "", "Keywords": "", "DOI": "10.14445/22312803/IJCTT-V69I9P106", "PubYear": 2021, "Volume": "69", "Issue": "9", "JournalId": 32518, "JournalTitle": "International Journal of Computer Trends and Technology", "ISSN": "2349-0829", "EISSN": "2231-2803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92639318, "Title": "A multiplayer game model to detect insiders in wireless sensor networks", "Abstract": "<p>Insiders might have incentives and objectives opposed to those of the belonging organization. It is hard to detect them because of their privileges that partially protect them. In Wireless Sensor Networks (WSNs), significant security issues arise, including compromised nodes by insiders that disrupt the normal network operation. Immediate defensive actions to isolate malicious nodes would mitigate any related impacts. A multiplayer game model is proposed as a solution to the problem of insider attacks in WSNs, the Game of Wireless Sensor Networks (GoWiSeN). It is an imperfect information game, formulated with the use of non-cooperative game theory, holding the assumption that all players are rational. The model consists of several Local Intrusion Detection Systems (LIDSs), which are located to different nodes and communicate with a Global Intrusion Detection System (GIDS). Each LIDS gives suggestions whether the monitoring node is trusted or not. The game is being played between a potential attacker, the nodes and the GIDS. The GIDS is responsible for making a final decision and for isolating a compromised node in case of an internal attack. The theoretical model represents these interactions in an extensive form game. The formal elements of the game are specified, the outcomes of the game are quantified by first specifying players’ preferences, and then, by using the von Neumann-<PERSON>rgenstern utility function, and payoffs are obtained. The game is constructed and solved, by locating NE in pure and mixed strategies. Experimental evaluations conducted on real network datasets, using IDSs of different capabilities, simulate special cases and compromised nodes in a WSN, verify the model efficiency, and show how the game should be played.</p>", "Keywords": "Game theory;Intrusion detection;Multiplayer game;Wireless sensor networks", "DOI": "10.7717/peerj-cs.791", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of West Attica, Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cyber Technology Institute, De Montfort University Leicester, Leicester, UK, United Kingdom"}, {"AuthorId": 3, "Name": "Panagiotis F<PERSON>", "Affiliation": "Banking and Financial Management, University of Piraeus, Piraeus, Greece"}, {"AuthorId": 4, "Name": "So<PERSON><PERSON>", "Affiliation": "Norwegian University of Science and Technology, Gjøvik, Norway"}], "References": [{"Title": "Classification model for accuracy and intrusion detection using machine learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A game theoretic power control and spectrum sharing approach using cost dominance in cognitive radio networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": ********, "Title": "Interference-adjusted power learning curve model with forgetting", "Abstract": "Researchers in production and operations management have studied the effect of worker learning and forgetting on system performance for decades. It remains an active research topic. Those studies have assumed that production interruptions (or production breaks) cause forgetting, which deteriorates performance. Research on human working memory provides enough evidence that continuous forgetting, precisely cognitive interference, results from overloading the memory with information. Despite the evidence, few studies have incorporated it into learning curve models. This paper presents an enhanced version of the power learning curve that accounts for a variable degree of interference when moving from a production cycle to the next. It adopts the concept of memory trace decay to measure the residual (interference-adjusted), not the nominal (maximum) cumulative experience. We test the developed model against learning data from manual assembly and inspection tasks, with varying numbers of repetitions and breaks. We also test three alternative power-form learning and forgetting curve models from the literature. The results show that the interference-adjusted model fits the data very well. The proposed learning and forgetting model and its individualized cumulative metrics can help identify struggling workers early and release precocious learners earlier than expected. As such, the model gives insights for managers on the occurrence of interference to enable individual learning support.", "Keywords": "Learning ; Power learning curve ; Cognitive interference ; Memory trace ; Forgetting ; Manual assembly", "DOI": "10.1016/j.ergon.2021.103257", "PubYear": 2022, "Volume": "88", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Aalto University School of Engineering, Puumiehenkuja 3, 02150, Espoo, Finland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Ryerson University, 350 Victoria Street, Toronto, Ontario, M5B 2K3, Canada"}], "References": [{"Title": "Cost Estimating Using a New Learning Curve Theory for Non-Constant Production Rates", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "429", "JournalTitle": "Forecasting"}]}, {"ArticleId": 92639498, "Title": "Sistem Informasi Manajemen Aset Untuk Monitoring Pemanfaatan Dan Pengadaan Aset Perusahaan Di Cv. <PERSON><PERSON><PERSON> Teknik", "Abstract": "<p>CV. Cahaya Utama Teknik merupakan sebuah perusahaan yang bergerak dalam bidang jasa. Aset pada perusahaan belum terdata secara detail sehingga beresiko terjadinya kehilangan aset dan aset yang tidak terpelihara dengan baik dapat menimbulkan kerusakan sehingga perusahaan mengeluarkan biaya lebih untuk penggantian aset baru. Sistem Informasi Manajemen Aset Untuk Monitoring Pemanfaatan Dan Pengadaan Aset Perusahaan Di CV. Cahaya Utama Teknik dirancang untuk membantu pihak perusahaan dalam Pengelolaan Aset. Sistem yang dibuat berbasis web dengan Bahasa pemrograman PHP dan database MySQL. Metodologi pengembangan perangkat lunak menggunakan Waterfall. Hasil yang dicapai adalah suatu sistem yang dapat membantu perusahaan dalam mengelola aset, dimana didalamnya terdapat pendataan aset per periode, pengingat perawatan aset yang dapat dilihat di web maupun email, History Perbaikan, <PERSON> Pengguna<PERSON>, History Penghapusan berdasarkan nilai dan kondisi, dan terdapat fitur prediksi kebutuhan aset.</p>", "Keywords": "History;Manajemen Aset;Monitoring;Pengingat;Sistem Informasi;Waterfall;Web", "DOI": "10.30742/melekitjournal.v7i1.182", "PubYear": 2021, "Volume": "7", "Issue": "1", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92639628, "Title": "On logical and extensional characterizations of attributed feature models", "Abstract": "Software-intensive systems can have thousands of interdependent configuration options across different subsystems. Feature models (FMs) allow designers to organize the configuration space by describing configuration options using interdependent features: a feature is a name representing some functionality and each software variant is identified by a set of features. Attributed feature models (AFMs) extend FMs to describe the, possibly constrained, choice of a value from domains such as integers or strings: each attribute is associated to one feature, and when the feature is selected then the attribute brings some additional information relative to the selected features. Different representations of FMs and AFMs have been proposed in the literature. In this paper we focus on the logical representation (which works well in practice) and the extensional representation (which has been recently shown well suited for theoretical investigations). We provide an algebraic and a logical characterization of operations and relations on FMs and AFMs, and we formalize the connection between the two characterizations as monomorphisms from lattices of logical FMs and AFMs to lattices of extensional FMs and AFMs, respectively. This formalization sheds new light on the correspondence between the algebraic and logical characterizations of operations and relations for FMs and AFMs. It aims to foster the development of a formal framework for supporting practical exploitation of future theoretical developments on FMs, AFMs and multi software product lines.", "Keywords": "Feature model ; Attributed feature model ; Boolean lattice ; Composition ; Configurable software ; Logic ; Software product line", "DOI": "10.1016/j.tcs.2022.01.016", "PubYear": 2022, "Volume": "912", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Turin, Turin, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ONERA, Palaiseau, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Turin, Turin, Italy;Corresponding author"}], "References": []}, {"ArticleId": 92639684, "Title": "Multi-category classification with label noise by robust binary loss", "Abstract": "In recent times, deep learning models have achieved state-of-the-art performances in image classification. However, the classification and generalization ability of most achievements have highly relied on the availability of large-scale accurate labeled training data, which are time-consuming, laborious, and expensive to collect. Moreover, in the generalizing process of deep learning models, the noisy labels are the challenges for multi-category classification. Hence, it is essential to explore effective methods that can efficiently and correctly train deep models under label noise to conduct multi-category classification. This paper proposes using robust binary loss functions to train deep models under label noise to address this problem. Specifically, we suggest handling the K -class classification task by using K binary classifiers, which can be completed by a joint adoption of multi-category large margin classification approaches, e.g., Pairwise-Comparison (PC) or One-versus-All (OVA). We also theoretically demonstrate that our method is inherently tolerant to label noise by using symmetric binary loss functions in multi-category classification tasks. Moreover, we designed a truncated CCE loss to combine with the proposed losses to improve the learning ability. Finally, we test our method on three different benchmark datasets with different types of label noise. The experimental results have clearly confirmed the effectiveness of our method, which can reduce the negative effect of noisy labels and improve the generalization ability.", "Keywords": "Deep learning ; Image recognition ; Label noise ; Robust loss function", "DOI": "10.1016/j.neucom.2022.01.031", "PubYear": 2022, "Volume": "482", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center of Statistical Research and School of Statistics, Southwestern University of Finance and Economics, Chengdu 611130, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Physics, Guangxi University for Nationalities, Nanning 530004, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China;Corresponding authors"}, {"AuthorId": 5, "Name": "Fengmao Lv", "Affiliation": "School of Computing and Artificial Intelligence & Manufacturing Industry Chains Collaboration and Information Support Technology Key Laboratory of Sichuan Province, Southwest Jiaotong University, Chengdu 611756, China;Corresponding authors"}], "References": [{"Title": "A PSO-based deep learning approach to classifying patients from emergency departments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "1939", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 92639685, "Title": "Modeling adaptive security-aware task allocation in mobile cloud computing", "Abstract": "Security is one of the most important criteria in the management of cloud resources. In Mobile Cloud Computing (MCC), secure allocation of tasks remains challenging due to the limited storage, battery life and computational power of mobile devices connected to the core cloud cluster infrastructure. Secure wireless communication channels and protocols for protecting the data and information sent to the cloud, and remote access to secure cloud services, are other important problems related to task scheduling and processing in dynamic MCC. In this paper, we developed a new security-aware task allocation model strategy in Mobile Cloud Computing. In this model, we define an allocation algorithm which generates an optimal and secure configuration of communication protocols in order to meet the specific data confidentiality requirements defined by end users. Resource utilization is predicted using Machine Learning methods, and the optimal secure service for task execution is selected. We developed a simulation environment (MocSecSim) for the evaluation of the algorithms proposed in several scenarios based on the users’ requirements. The results of simulations and experiments have demonstrated that the model proposed significantly improves the level of security of calculations in comparison with a configuration where processing time and energy consumption are the main criteria for optimizing task allocation.", "Keywords": "Security ; Mobile Cloud Computing ; Machine learning ; Simulation", "DOI": "10.1016/j.simpat.2022.102491", "PubYear": 2022, "Volume": "116", "Issue": "", "JournalId": 1087, "JournalTitle": "Simulation Modelling Practice and Theory", "ISSN": "1569-190X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science, AGH University of Science and Technology, al. <PERSON><PERSON> 30, 30-059 Krakow, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science, AGH University of Science and Technology, al. <PERSON><PERSON> 30, 30-059 Krakow, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science, AGH University of Science and Technology, al. <PERSON><PERSON> 30, 30-059 Krakow, Poland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Research and Technology Transfer Unit, Research and Academic Computer Network – NASK, Warsaw, Poland"}], "References": [{"Title": "Deep learning and big data technologies for IoT security", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "495", "JournalTitle": "Computer Communications"}, {"Title": "Adaptive resource planning for cloud-based services using machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Bartlomiej <PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "88", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 92639688, "Title": "BarMan: A run-time management framework in the resource continuum", "Abstract": "Over the last years, the number of IoT devices has grown exponentially, highlighting the current Cloud infrastructure limitations. In this regard, Fog and Edge computing began to move part of the computation closer to data sources by exploiting interconnected devices as part of a single heterogeneous and distributed system in a computing continuum viewpoint. Since these devices are typically heterogeneous in terms of performance, features, and capabilities, this perspective should encompass programming models and run-time management layers. This work presents and evaluates the BarMan open-source framework by implementing a Fog video surveillance use-case. BarMan leverages a task-based programming model combined with a run-time resource manager and the novel BeeR framework to deploy the application’s tasks transparently. This enables the possibility of considering aspects related to the energy and power dissipated by the devices and the single application. Moreover, we developed a task allocation policy to maximize application performance, considering run-time aspects, such as load and connectivity, of the time-varying available devices. Through an experimental evaluation performed on a real cluster equipped with heterogeneous embedded boards, we evaluated different execution scenarios to show the framework’s functionality and the benefit of a distributed approach, leading up to an improvement of 66% on the frame processing latency w.r.t. a monolithic solution.", "Keywords": "Fog computing ; Resource management ; Programming model ; Task allocation strategy", "DOI": "10.1016/j.suscom.2022.100663", "PubYear": 2022, "Volume": "35", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DEIB, Politecnico di Milano, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DEIB, Politecnico di Milano, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DEIB, Politecnico di Milano, Italy"}], "References": [{"Title": "A methodology for the design and deployment of distributed cyber–physical systems for smart environments", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "420", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 92639825, "Title": "Contributive Effects of Cloud Computing to Positive Changes of Students’ Academic Information Accessibility", "Abstract": "", "Keywords": "", "DOI": "10.14445/22312803/IJCTT-V69I10P109", "PubYear": 2021, "Volume": "69", "Issue": "10", "JournalId": 32518, "JournalTitle": "International Journal of Computer Trends and Technology", "ISSN": "2349-0829", "EISSN": "2231-2803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92639832, "Title": "The Requirements Elicitation Pyramid – A Theory of Requirements Evolution", "Abstract": "", "Keywords": "", "DOI": "10.14445/22312803/IJCTT-V69I11P106", "PubYear": 2021, "Volume": "69", "Issue": "11", "JournalId": 32518, "JournalTitle": "International Journal of Computer Trends and Technology", "ISSN": "2349-0829", "EISSN": "2231-2803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92639857, "Title": "Semantic Role Labeling Based on Highway-BiLSTM-CRF Model", "Abstract": "", "Keywords": "", "DOI": "10.14445/22312803/IJCTT-V69I10P103", "PubYear": 2021, "Volume": "69", "Issue": "10", "JournalId": 32518, "JournalTitle": "International Journal of Computer Trends and Technology", "ISSN": "2349-0829", "EISSN": "2231-2803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Xiangzhong Pu", "Affiliation": ""}], "References": []}, {"ArticleId": 92639858, "Title": "Towards an Ontology for Islamic Finance", "Abstract": "", "Keywords": "", "DOI": "10.14445/22312803/IJCTT-V69I12P103", "PubYear": 2021, "Volume": "69", "Issue": "12", "JournalId": 32518, "JournalTitle": "International Journal of Computer Trends and Technology", "ISSN": "2349-0829", "EISSN": "2231-2803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>car", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92639930, "Title": "X-vector anonymization using autoencoders and adversarial training for preserving speech privacy", "Abstract": "The rapid increase in web services and mobile apps, which collect personal data from users, has also increased the risk that their privacy may be severely compromised. In particular, the increasing variety of spoken language interfaces and voice assistants empowered by the vertiginous breakthroughs in deep learning have prompted important concerns in the European Union in terms of preserving the privacy of speech data. For instance, an attacker can record speech from users and impersonate them to obtain access to systems that require voice identification. By extracting speaker, linguistic (e.g., dialect), and paralinguistic features (e.g., age) from a speech signal, the speaker profiles can also be hacked from users through existing technology. To mitigate these weaknesses, in this study, we present a speech anonymization method based on autoencoders and adversarial training. Given an utterance, we first extract an x-vector, which is a powerful utterance-level embedding used in state-of-the-art speaker recognition. This original x-vector is transformed by an autoencoder producing a new x-vector, where speaker, gender, and accent information are suppressed through adversarial training. The anonymized speech is finally generated through a neural speech synthesizer driven by the anonymized x-vector, fundamental frequency, and phoneme information extracted from the input speech. For the evaluation, we followed the VoicePrivacy Challenge framework, where anonymization or privacy is measured using automatic speaker verification and the preservation of the intelligibility is evaluated through automatic speech recognition. Our experimental results show that the proposed method achieves higher privacy than the VoicePrivacy baseline (i.e., a higher speaker verification error) while preserving a similar intelligibility for the spoken content (i.e., a similar word error rate).", "Keywords": "Speaker anonymization ; Adversarial training ; Autoencoders ; Adversarial neural networks ; Automatic speech recognition ; Automatic speaker verification", "DOI": "10.1016/j.csl.2022.101351", "PubYear": 2022, "Volume": "74", "Issue": "", "JournalId": 6012, "JournalTitle": "Computer Speech & Language", "ISSN": "0885-2308", "EISSN": "1095-8363", "Authors": [{"AuthorId": 1, "Name": "<PERSON>Co<PERSON>", "Affiliation": "Sigma Technologies S.L.U., Madrid, Spain;GAPS Signal Processing Applications Group, Universidad Politécnica de Madrid, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Sigma Technologies S.L.U., Madrid, Spain;GAPS Signal Processing Applications Group, Universidad Politécnica de Madrid, Madrid, Spain;Corresponding author at: Sigma Technologies S.L.U., Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON>", "Affiliation": "GAPS Signal Processing Applications Group, Universidad Politécnica de Madrid, Madrid, Spain"}], "References": []}, {"ArticleId": 92640014, "Title": "Virtual Reality and EEG-Based Intelligent Agent in Older Adults With Subjective Cognitive Decline: A Feasibility Study for Effects on Emotion and Cognition", "Abstract": "<p> Objectives: Immersive virtual reality has tremendous potential to improve cognition in populations with cognitive impairment. We conducted a feasibility and proof-of-concept study to assess the potential of virtual reality and electroencephalography, with or without an intelligent agent, that adapts the presented material to the emotions elicited by the environment. </p><p> Method: Older adults with subjective cognitive decline recruited from the community received a virtual reality-based intervention taking place in one of two virtual environments, a train (Part 1, N = 19) or a music theatre, complemented by the intelligent agent (Part 2, N = 19). A comparative control group (N = 19) receiving no intervention was also included. All participants completed measures of affect and cognition before and after the intervention. The intervention groups completed measures of cybersickness and user experience after the intervention. </p><p> Results: Participants did not suffer from increased cybersickness following either intervention. They also reported a positive to highly positive user experience concerning the following aspects: attractivity, hedonic quality-identity and hedonic quality-stimulation. The measures of affect showed no pre-post change when comparing either intervention to the control condition. However, a reduction of negative affect was observed following the train intervention for participants with a high self-reported negative affect at baseline. Finally, there was a significant improvement in working memory when comparing either intervention group to the control condition. </p><p> Conclusion: Our results support the feasibility and tolerability of the technology, and a positive impact on cognition, paving the way for a larger-scale randomized clinical trial to confirm efficacy. </p>", "Keywords": "Cognition; Emotions; Subjective cognitive decline; Cybersickness; User Experience; Virtual Reality", "DOI": "10.3389/frvir.2021.807991", "PubYear": 2022, "Volume": "2", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Centre, Institut Universitaire de Gériatrie de Montréal, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research Centre, Institut Universitaire de Gériatrie de Montréal, Canada;Department of Psychology, Université de Montréal, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Operational Research, Université de Montréal, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Operational Research, Université de Montréal, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Research Centre, Institut Universitaire de Gériatrie de Montréal, Canada;Department of Psychiatry and Addictology, Université de Montréal, Canada"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Operational Research, Université de Montréal, Canada"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Centre, Institut Universitaire de Gériatrie de Montréal, Canada;Department of Psychology, Université de Montréal, Canada"}], "References": [{"Title": "Psychometric evaluation of Simulator Sickness Questionnaire and its variants as a measure of cybersickness in consumer virtual environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "", "Page": "102958", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 92640037, "Title": "Designing Persuasive Food Conversational Recommender Systems With Nudging and Socially-Aware Conversational Strategies", "Abstract": "<p>Unhealthy eating behavior is a major public health issue with serious repercussions on an individual’s health. One potential solution to overcome this problem, and help people change their eating behavior, is to develop conversational systems able to recommend healthy recipes. One challenge for such systems is to deliver personalized recommendations matching users’ needs and preferences. Beyond the intrinsic quality of the recommendation itself, various factors might also influence users’ perception of a recommendation. In this paper, we present <PERSON>, a conversational system that recommends recipes aligned with its users’ eating habits and current preferences. Users can interact with <PERSON> in two different ways. They can select pre-defined answers by clicking on buttons to talk to <PERSON> or write text in natural language. Additionally, <PERSON> can engage users through a social dialogue, or go straight to the point. <PERSON> is also able to propose different alternatives and to justify its recipes recommendation by explaining the trade-off between them. We conduct two experiments. In the first one, we evaluate the impact of <PERSON>’s conversational skills and users’ interaction mode on users’ perception and intention to cook the recommended recipes. Our results show that a conversational recommendation system that engages its users through a rapport-building dialogue improves users’ perception of the interaction as well as their perception of the system. In the second evaluation, we evaluate the influence of <PERSON>’s explanations and recommendation comparisons on users’ perception. Our results show that explanations positively influence users’ perception of a recommender system. However, comparing healthy recipes with a decoy is a double-edged sword. Although such comparison is perceived as significantly more useful compared to one single healthy recommendation, explaining the difference between the decoy and the healthy recipe would actually make people less likely to use the system.</p>", "Keywords": "health aware; Food recommender systems; conversational agents; nudging; Socially aware", "DOI": "10.3389/frobt.2021.733835", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Neuroscience and Psychology, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Neuroscience and Psychology, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Neuroscience and Psychology, United Kingdom;Khoury College of Computer Sciences, United States"}], "References": [{"Title": "An investigation on the user interaction modes of conversational recommender systems for the music domain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "251", "JournalTitle": "User Modeling and User-Adapted Interaction"}]}, {"ArticleId": 92640130, "Title": "Multi-Scale Fusion Networks with RGB Image Features for Depth Map Completion", "Abstract": "<p indent=0mm>Currently, researchers cannot directly train end-to-end model for depth image completion because of lacking paired “incomplete-complete” RGB-D datasets. To address this problem, a random mask-based method which is combined with “real-synthetic” data for joint training strategy is proposed to construct paired incomplete-complete RGB-D data. This method generates depth maps with different missing ratios based on random masks, and uses synthetic scene datasets to construct missing regions of depth map with high fidelity truth values. Based on this strategy, a multi-scale depth map completion network is constructed, which fuses the corresponding RGB image features. The proposed network extracts RGB image features and depth map features with different scales from RGB image branches and depth map branches. Then, in the feature fusion branch, the RGB image features and depth map features are fused at different scales, which makes that the rich semantic features of RGB images and the information of depth maps can be effectively integrated for depth map completion. Experiments on the NYU-Depth V2 dataset show that in depth completion tasks with different missing ratios, the average threshold accuracy of this method is 0.98, and the mean relative error is about 0.061. Compared with the existing methods based on neural networks and optimizing sparse equations, this method improves the threshold accuracy by an average of 0.02, and the mean relative error decreases by an average of 0.027.</p>\"; \t\tabstractP.html(escape2Html(text)); \t} \tUE.delEditor('cjad7d9u47kqxnT78'); \tUE.delEditor('M8nz5f9iMb75KBWTa'); //初始化文章全文富文本编辑器 \tfullAbstractEn = UE.getEditor('cjad7d9u47kqxnT78', { \t\tinitialFrameHeight : 500,//设置编辑器高度 \t\tscaleEnabled : true //设置不自动调整高度 \t}); \tfullAbstractZh = UE.getEditor('M8nz5f9iMb75KBWTa', { \t\tinitialFrameHeight : 500,//设置编辑器高度 \t\tscaleEnabled : true //设置不自动调整高度 \t}); }) function changeMore2(){ \t\tvar html = $(\"#moreClick2\").html(); \t\tif(html.indexOf('plus')>-1){ \t\t$(\".", "Keywords": "depth map completion,fusion                     networks,depth restoration", "DOI": "10.3724/SP.J.1089.2021.1886", "PubYear": 2021, "Volume": "33", "Issue": "9", "JournalId": 38257, "JournalTitle": "Journal of Computer-Aided Design & Computer Graphics", "ISSN": "1003-9775", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Chu<PERSON>an", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92640186, "Title": "Drug‐Loaded Mucoadhesive Patch with Active Delivery and Controlled Releasing Ability", "Abstract": "<p>Herein, a mucoadhesive patch for gastrointestinal tracts with active delivery, hyperthermia, and controlled drug release function using a magnetically actuated capsule is proposed to overcome the drug delivery and efficiency challenges of wireless structures. The proposed patch has excellent adhesion to the gastrointestinal tract and contains anticancer drug doxorubicin and magnetic nanoparticles. This enables hyperthermia and the controlled release of the loaded drug under an alternating magnetic field (AMF). In addition, it can be delivered to multiple intestinal target lesions using a magnetically actuated capsule. Characteristic analyses of the proposed patch are performed, such as morphology, adhesion force measurement with intestine, temperature change under an AMF, and drug release. The feasibility of the patch delivery into the gastrointestinal tract is verified through locomotion performance tests and ex vivo patch delivery experiments using a magnetically actuated capsule. Finally, through an in vitro therapeutic effect test, the death of tumor cells using the proposed patch is confirmed. As a result, the possibility that the multiple mucoadhesive patches can be delivered to target lesions in a digestive tract through a magnetically actuated capsule and can treat the lesions through hyperthermia and active drug release using an AMF stimulation is verified.</p>", "Keywords": "active delivery;gastrointestinal cancer treatment;hyperthermia;mucoadhesive patch;multiple delivery", "DOI": "10.1002/aisy.202100203", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Robotics Engineering, Daegu Gyeongbuk Institute of Science and Technology (DGIST), Daegu, 42988 Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Robotics Engineering, Daegu Gyeongbuk Institute of Science and Technology (DGIST), Daegu, 42988 Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Gastroenterology, Department of Internal Medicine and Yonsei Institute of Gastroenterology, Yonsei University College of Medicine, Seoul, 03722 Republic of Korea"}, {"AuthorId": 4, "Name": "Sukho Park", "Affiliation": "Department of Robotics Engineering, Daegu Gyeongbuk Institute of Science and Technology (DGIST), Daegu, 42988 Republic of Korea"}], "References": []}, {"ArticleId": 92640215, "Title": "Application of the neural network model of <PERSON><PERSON>’s associative memory in the task of processing radar information", "Abstract": "", "Keywords": "", "DOI": "10.18127/j20700970-202004-03", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 92874, "JournalTitle": "Neurocomputers", "ISSN": "1999-8554", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92640417, "Title": "Conformational change due to replacement of disulfide with selenosulfide and diselenide in dipeptide vicinal cysteine loop", "Abstract": "Replacement of disulfide bridges with diselenide bridges is increasingly common to improve the stability, foldability, and structural refinement of the cysteine-rich polypeptides. Even though the global structural features are similar due to the replacement of disulfide with diselenide, the local conformational differences have been reported in a few polypeptides. The current report has used the constrained vicinal cysteine disulfide as the model to access the influence of the replacement of disulfides with diselenide on the local conformations. Using the density functional theory (DFT), structures of dipeptide vicinal loops are optimized by systematically replacing sulfur with selenium. Conformations of the disulfide/selenosulfide/diselenide were identified using the side-chain torsional angle χ<sub>1,</sub> χ<sub>2,</sub> χ<sub>3,</sub> χ<sub>2</sub>′<sub>,</sub> χ<sub>1</sub>′ and mapped to one of the possible 32 conformations of the cysteine disulfide. Further, the influence of the change of configuration of Cα-atom of cysteine/selenocysteine from ‘R′ to ‘S′ configuration and peptide bond from cis to trans has also been accessed on the conformations of dipeptide vicinal loops. The results indicate that diselenide/selenosulfide explores additional conformational space apart from accommodating the conformations observed in the vicinal disulfide which is more amplified in the heterochiral system. Differences have been observed at the internal coordinates of the optimized structures of dipeptide vicinal disulfide, selenosulfide, and diselenide. The change in free energy (ΔG), spin density (Δs(r)), and electron density (Δρ(r)) was also calculated due to the replacement of disulfide with selenosulfide/diselenide. Conformational analysis of disulfides and that of the replaced diselenides in the crystal structures of the proteins retrieved from PDB have also indicated the retention as well as differences in the local conformations. The tendency of the diselenide loop to explore the additional conformational space may prompt for the local conformational differences in the corresponding disulfide to diselenide replaced polypeptides.", "Keywords": "Cysteine disulfide ; Selenocysteine diselenide ; Vicinal cysteine disulfide ; Density functional theory ; Protein Data Bank (PDB)", "DOI": "10.1016/j.compbiolchem.2022.107635", "PubYear": 2022, "Volume": "97", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, School of Chemical Sciences, Central University of Karnataka, Kalaburagi 585367, Karnataka, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, School of Chemical Sciences, Central University of Karnataka, Kalaburagi 585367, Karnataka, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, School of Chemical Sciences, Central University of Karnataka, Kalaburagi 585367, Karnataka, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, School of Chemical Sciences, Central University of Karnataka, Kalaburagi 585367, Karnataka, India;Corresponding author"}], "References": []}, {"ArticleId": 92640420, "Title": "SHA512 and MD5 Algorithm Vulnerability Testing Using Common Vulnerability Scoring System (CVSS)", "Abstract": "<p>This paper discusses the comparison of the results of testing the OTP (One Time Password) algorithm on two encryptions, namely SHA512 and MD5 which are applied to the Reconciliation Application of the Dinas Pemberdayaan Masyarakat dan Desa Kabupaten Sukabumi. This study uses the Vulnerability Assessment and Penetration Testing (VAPT) method, which combines two forms of vulnerability testing to achieve a much more complete vulnerability analysis by performing different tasks in the same focus area. The vulnerability assessment uses the Common Vulnerability Scoring System (CVSS) method. The results showed that the Vulnerability Assessment and Penetration Testing (VAPT) method was proven to be able to identify the level of security vulnerability in the Reconciliation Application at the Dinas Pemberdayaan Masyarakat dan Desa Kabupaten Sukabumi with a vulnerability level score of 5.3 in the SHA512 environment with a medium rating and 7.5 in the MD5 environment. with high ratings. So, it can be concluded that the best algorithm for implementing OTP is SHA512</p>", "Keywords": "OTP, SHA512, MD5, VAPT, CVSS", "DOI": "10.36805/bit-cs.v3i1.2046", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 76190, "JournalTitle": "Buana Information Technology and Computer Sciences (BIT and CS)", "ISSN": "2715-2448", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Budi Luhur"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Budi Luhur"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Budi Luhur"}], "References": []}, {"ArticleId": 92640439, "Title": "Context Diffusion in Fog Colonies: Exploring Autonomous Fog Node Operation Using ECTORAS", "Abstract": "<p>In Fog Computing, fog colonies are formed by nodes cooperating to provide services to end-users. To enable efficient operation and seamless scalability of fog colonies, decentralized control over participating nodes should be promoted. In such cases, autonomous Fog Nodes operate independently, sharing the context in which all colony members provide their services. In the paper, we explore different techniques of context diffusion and knowledge sharing between autonomous Fog Nodes within a fog colony, using ECTORAS, a publish/subscribe protocol. With ECTORAS, nodes become actively aware of their operating context, share contextual information and exchange operational policies to achieve self-configuration, self-adaptation and context awareness in an intelligent manner. Two different ECTORAS implementations are studied, one offering centralized control with the existence of a message broker, to manage colony participants and available topics, and one fully decentralized, catering to the erratic topology that Fog Computing may produce. The two schemes are tested as the Fog Colony size is expanding in terms of performance and energy consumption, in a prototype implementation based on Raspberry Pi nodes for smart building management.</p>", "Keywords": "Fog Colony Control; autonomous nodes; context awareness; publish/subscribe protocols Fog Colony Control ; autonomous nodes ; context awareness ; publish/subscribe protocols", "DOI": "10.3390/iot3010005", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 58626, "JournalTitle": "IoT", "ISSN": "", "EISSN": "2624-831X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 17778 Tavros, Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 17778 Tavros, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 17778 Tavros, Greece"}], "References": [{"Title": "An autonomous\n IoT\n service placement methodology in fog computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "5", "Page": "1097", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Process Automation in an IoT–Fog–Cloud Ecosystem: A Survey and Taxonomy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "92", "JournalTitle": "IoT"}, {"Title": "FONS: a fog orchestrator node selection model to improve application placement in fog computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "9", "Page": "10562", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Atmosphere: Context and situational-aware collaborative IoT architecture for edge-fog-cloud computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "79", "Issue": "", "Page": "103550", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Towards a Policy Management Framework for Managing Interaction Behaviors in IoT Collectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "4", "Page": "633", "JournalTitle": "IoT"}, {"Title": "Fog Node Self-Control Middleware: Enhancing context awareness towards autonomous decision making in Fog Colonies", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "100549", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 92640447, "Title": "Sistem Pendukung Keputusan Penilaian Pegawai Di Klinik Bunda Medical Center (BMC) Dengan Menggunakan Metode Simple Additive Weighting (SAW)", "Abstract": "<p>Penilaian kinerja merupakan suatu proses organisasi dalam menilai untuk kinerja pegawainya. Tujuan dilakukan penilaian kinerja untuk memberikan feedback kepada pegawai dalam upaya meningkatkan produktivitas organisasi, dan secara khusus dilakukan dalam kaitannya dengan berbagai kebijaksanaan terhadap pegawai diberikan bonus gaji dan mengevaluasi dari hasil kinerja pegawai. <PERSON><PERSON><PERSON> inilah yang membuat Klinik Bunda Medical Center (BMC)  mencari solusi agar penilaian kinerja pegawai dapat dilakukan secara adil, realistis valid dan relevan. Sistem Pendukung Keputusan (SPK) Penilaian Kinerja pegawai adalah sebuah sistem yang dapat membantu HRD dalam menentukan keputusan pegawai dengan kinerja yang baik untuk mendapatkan bonus gaji. Kriteria yang digunakan untuk mengukur kinerja pegawai ada 5 kriteria, ya<PERSON><PERSON>, Team Work,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>g Jawab,<PERSON><PERSON><PERSON>. Sistem ini menggunakan metode Simple Additive Weighting (SAW) yang merupakan metode penilaian yang diukur dari nilai suatu bobot kriteria yang dikelompokan dalam bobot nilai kriteria yang bersifat benefit. Dengan menggunakan sistem pendukung keputusan ini dapat membantu HRD untuk dalam melakukan penilaian kinerja pegawai di Klinik Bunda Medical Center (BMC). Hasil akhir penghitungan dari SAW, menunjukkan bahwa alternatif A5 atas nama Miftakhul Jannah, S.Kom, adalah pegawai terbaik dengan jumlah nilai tertinggi yaitu 1.</p>", "Keywords": "", "DOI": "10.37859/coscitech.v2i2.2961", "PubYear": 2021, "Volume": "2", "Issue": "2", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 2, "Name": " Januar Al Amien", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}], "References": []}, {"ArticleId": 92640576, "Title": "Circumferentially rotatable inspection robot with elastic suspensions for bridge cables", "Abstract": "Purpose \nPeriodic inspection of bridge cables is essential, and cable-climbing robots can replace human workers to perform risky tasks and improve inspection efficiency. However, cable inspection robots often fail to surmount large obstacles and cable clamps. The purpose of this paper is to develop a practical cable inspection robot with stronger obstacle-surmounting performance and circumferential rotation capability.\n \n \n Design/methodology/approa/ch \nA cable inspection robot with novel elastic suspension mechanisms and circumferential rotation mechanisms is designed and proposed in this study. The supporting force and spring deformation of the elastic suspension are investigated and calculated. Dynamic analysis of obstacle surmounting and circumferential rotation is performed. Experiments are conducted on vertical and inclined cables to test the obstacle-surmounting performance and cable-clamp passing of the robot. The practicality of the robot is then verified in field tests.\n \n \n Findings \nWith its elastic suspension mechanisms, the cable inspection robot can carry a 12.4 kg payload and stably climb a vertical cable. The maximum heights of obstacles surmounted by the driving wheels and the passive wheels of the robot are 15 mm and 13 mm, respectively. Equipped with circumferential rotation mechanisms, the robot can flexibly rotate and successfully pass cable clamps.\n \n \n Originality/value \nThe novel elastic suspension mechanism and circumferential rotation mechanism improve the performance of the cable inspection robot and solve the problem of surmounting obstacles and cable clamps. Application of the robot can promote the automation of bridge cable inspection.", "Keywords": "Bridge maintenance,Inspection robot,Circumferential rotation,Obstacle surmounting", "DOI": "10.1108/IR-11-2021-0261", "PubYear": 2022, "Volume": "49", "Issue": "5", "JournalId": 4481, "JournalTitle": "Industrial Robot: An International Journal", "ISSN": "0143-991X", "EISSN": "1758-5791", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electronic Engineering, School of Mechanical Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Yin", "Affiliation": "Department of Mechanical and Electronic Engineering, School of Mechanical Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electronic Engineering, School of Mechanical Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electronic Engineering, School of Mechanical Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electronic Engineering, School of Mechanical Engineering, Southeast University , Nanjing, China"}], "References": []}, {"ArticleId": 92640578, "Title": "Numerical simulation of the influence of mixed sand on erosion characteristics of centrifugal pump", "Abstract": "Purpose The purpose of the paper is to predict the erosion rate of the components of centrifugal pump under certain operating condition to identify the maximum erosion area and to discuss the factors affecting them. This helps to optimize design and estimate service life. Design/methodology/approach In the paper, the Eulerian–La<PERSON>ngian approach method coupled with the erosion model to investigate the mixed sand characteristics on erosion characteristics of centrifugal pump flow-through wall. The hydraulic performance and wear characteristics experiment of the pump is used to verify the accuracy of the numerical simulation. Findings The blade erosion area mainly occurs near the blade inlet and the trailing edge of the pressure surface, the main erosion area of the impeller back shroud is near the outlet of the flow passage and the main erosion area of the volute is near the tongue and the I section. With the change of the average diameter and density of sand particles, the average erosion rate on different flow-through walls is positively correlated with the average mass concentration to a certain extent. However, for different sand shape factors, there is little correlation between the average erosion rate and the average mass concentration. In addition, compared with other erosion areas, the increase of average sand particle diameter and density has the greatest impact on the total erosion rate of blade pressure surface, while the shape of sand particles has a greater impact on the total erosion rate of each flow-through wall of centrifugal pump. Originality/value In this work, according to the characteristics of the mixed distribution of different sand diameters in the Yellow River Basin, the erosion characteristics of centrifugal pumps used in the Yellow River Basin are studied. The numerical calculation method for predicting the wall erosion of centrifugal pump is established and compared with the experimental results. The results can provide reference for optimizing design and increasing service life.", "Keywords": "Centrifugal pump,Solid-liquid two-phase flow,Sand particles mixing,Numerical simulation,Erosion", "DOI": "10.1108/EC-10-2021-0602", "PubYear": 2022, "Volume": "39", "Issue": "6", "JournalId": 30507, "JournalTitle": "Engineering Computations", "ISSN": "0264-4401", "EISSN": "1758-7077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Center of Fluid Machinery Engineering and Technology , Jiangsu University , Zhenjiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center of Fluid Machinery Engineering and Technology , Jiangsu University , Zhenjiang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering , Beijing Institute of Technology , Beijing, China"}, {"AuthorId": 4, "Name": "Gangxiang Li", "Affiliation": "Research Center of Fluid Machinery Engineering and Technology , Jiangsu University , Zhenjiang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center of Fluid Machinery Engineering and Technology , Jiangsu University , Zhenjiang, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Research Center of Fluid Machinery Engineering and Technology , Jiangsu University , Zhenjiang, China"}], "References": []}, {"ArticleId": 92640706, "Title": "POI recommendation based on a multiple bipartite graph network model", "Abstract": "<p>In location-based social network platforms, the point-of-interest(POI) recommendation is an essential function to serve users. The existing POI recommendation algorithms are rarely able to fully integrate various factors affecting the POI recommendation and cannot make dynamic recommendations to users over time. To address this issue, POI recommendation based on a multiple bipartite graph network model (MBR) is proposed. To reduce the overall complexity of the recommendation algorithm, we propose a clustering algorithm based on graph model, which determines the center of user clustering in the established user graph. An algorithm for finding the sparsest subgraph is built to cluster users who have social friendships or check in at similar POI, which significantly excludes more dissimilar users and makes the final recommendation more effective, as well as reducing the algorithm’s complexity. To make more accurate recommendations to users, a large heterogeneous network of six weighted bipartite graphs is built based on the user clustering to describe the relations between users’ social relationships, geographical locations of POI, and temporal information. The original Large-scale Information Network Embedding (LINE) model is too complex to be adopted for the learning of vertex embedding, thus it is optimized by negative sampling and Alias sampling methods and are fused with bipartite graphs, which accelerates the training speed. Finally, simulation experiments are conducted with the Gowalla dataset to verify the MBR algorithm, and the results show that the algorithm outperforms another three recommendation algorithms in terms of time awareness.</p>", "Keywords": "Location-based social network; Point of Interest (POI); Clustering; Bipartite graph model", "DOI": "10.1007/s11227-021-04279-1", "PubYear": 2022, "Volume": "78", "Issue": "7", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Tiangong University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Tiangong University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Tiangong University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Tiangong University, Tianjin, China"}], "References": [{"Title": "WITHDRAWN: Collaborative filtering recommendation algorithm based on Bee Colony K-means clustering model", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "103424", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Non-negative matrix factorization via adaptive sparse graph regularization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "12507", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "ShortWalk: an approach to network embedding on directed graphs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "DEMLP: DeepWalk Embedding in MLP for miRNA-Disease Association Prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Sensors"}]}, {"ArticleId": 92640707, "Title": "Robust active disturbance rejection control for systems with internal uncertainties: Multirotor UAV application", "Abstract": "<p>Active Disturbance Rejection Control (ADRC) has recently stood out as a viable alternative to the proportional–integral–derivative controllers. An interesting field of application of this approach is the control of multirotor unmanned aerial vehicles (UAVs) which are inevitably subject to various force and torque disturbances. What makes ADRC attractive is the enhanced trajectory tracking and disturbance rejection capabilities that it allows while requiring minimal knowledge about the system. Although in theory, large uncertainties on the few required system parameters do not affect the stability of the ADRC's closed-loop, they cause performance deterioration and can lead to dangerous oscillations that result in instability in practice. In this study, we design a robust ADRC for multirotor UAVs that allows maintaining the performance despite large parameter variations, without changing the initial control gains tuning. Simulation and experimental results support the theoretical findings.</p>", "Keywords": "aerial robotics;extreme environments;multirotor;robust control", "DOI": "10.1002/rob.22058", "PubYear": 2022, "Volume": "39", "Issue": "4", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Vehicles Design and Control (DCAS), ISAE-SUPAERO, University of Toulouse, Toulouse, Occitanie, France; DONECLE (SAS), Labège, Occitanie, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Aerospace Vehicles Design and Control (DCAS), ISAE-SUPAERO, University of Toulouse, Toulouse, Occitanie, France"}], "References": []}, {"ArticleId": 92640712, "Title": "A discrete element framework for the numerical analysis of particle bed-based additive manufacturing processes", "Abstract": "This paper investigates the potential of the discrete element method to simulate the physics of particle bed-based additive manufacturing. This method naturally captures the discrete aspects of additive manufacturing processes, such as material addition. The proposed discrete element framework uses constitutive relations for loose powder, bonding kinematics and the thermo-mechanical behaviour of bonded particles. The mechanical bond interactions consist of beams that interconnect the particles. These beams are able to transfer forces as well as moments. The thermal conductive bond interactions assume an effective conductive area and density to account for the voids in the system. Simulated compression tests reveal that the macroscopic Young’s modulus and <PERSON><PERSON><PERSON>’s ratio of the bonded material are controlled by only two micro-scale parameters. Furthermore, a heat conducting rod of both powder and bonded material is simulated and compared to a continuum finite element simulation. The proposed discrete model is able to simulate a complete printing process, capturing the solid material behaviour accurately. A simulation of a printed sample shows various additive manufacturing aspects such as: the deposited powder layer, G-code input, heat source interaction, contact, bonding, thermal conduction and the accumulation of residual stresses and deformations.", "Keywords": "Discrete element method; Additive manufacturing; Powder bed printing; Thermo-mechanical analysis", "DOI": "10.1007/s00366-021-01590-6", "PubYear": 2022, "Volume": "38", "Issue": "6", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering, Mechanics of Materials, Eindhoven University of Technology, Eindhoven, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering, Mechanics of Materials, Eindhoven University of Technology, Eindhoven, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering, Mechanics of Materials, Eindhoven University of Technology, Eindhoven, The Netherlands"}], "References": []}, {"ArticleId": 92640780, "Title": "Evolution of Artificial Intelligence Programming Languages - a Systematic Literature Review", "Abstract": "Artificial Intelligence (AI) has received significant attention in recent years. It is being adopted to provide solutions to medicine, engineering, education, government and several other domains. To analyze the state-of-theart of research in AI, we present a systematic literature review focusing on the Evolution of AI programming languages. Our search returned 7,604 documents; after reviewing these documents, 78 which were relevant for this study were retained. Our research revealed that the prevalence of AI programming language by volume of publications had experienced peaks and valleys between 1963 and 2018; however, between 2015 to 2020, related publications have been experiencing peaks. During the review period, the PROLOG programming language received the most attention in about 49% of publications; this was followed by LISP, which received almost 22%. The remaining attention was shared between Logic and Object-Oriented Programming (LOOP), ARCHLOG, Epistemic Ontology Language with Constraints (EOLC), Python, C++, ADA and JAVA. However, the predominant AI programming language in recent AI software is C/C++, which takes 70% of the modern AI libraries analyzed in this study. Python is used in 60% of the modern AI libraries analyzed. Their prevalence is as a result of their speed, portability and ease of coding, making them effective in developing trending AI libraries such as TensorFlow and Keras. © 2021. <PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>eek Abayomi. This open access article is distributed under a Creative Commons Attribution (CC-BY) 4.0 license.", "Keywords": "ADA; AI; Artificial Intelligence; C++; EOLC; JAVA; LISP; Programming Language; PROLOG; Python", "DOI": "10.3844/jcssp.2021.1157.1171", "PubYear": 2021, "Volume": "17", "Issue": "11", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Information Engineering, College of Engineering, Covenant University, Ota, Nigeria; Covenant Applied Informatics and Communications African Center of Excellence, Covenant University, Ota, Nigeria; HRA, Institute for Systems Science, Durban University of Technology, P.O. Box 1334, Durban, South Africa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, College of Engineering, Covenant University, Ota, Nigeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Electrical and Information Engineering, College of Engineering, Covenant University, Ota, Nigeria"}, {"AuthorId": 4, "Name": "Funmilayo S. Moninuola", "Affiliation": "Department of Electrical and Information Engineering, College of Engineering, Covenant University, Ota, Nigeria"}, {"AuthorId": 5, "Name": "Olad<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, College of Engineering, Covenant University, Ota, Nigeria"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, College of Engineering, Covenant University, Ota, Nigeria; Covenant Applied Informatics and Communications African Center of Excellence, Covenant University, Ota, Nigeria"}, {"AuthorId": 7, "Name": "Obiseye O. <PERSON>mi", "Affiliation": "KZN e-Skills CoLab, Durban University of Technology, Durban, South Africa"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Osun State University, Osun State, Osogbo, Nigeria"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>of<PERSON>", "Affiliation": "Department of Information and Communication Technology, Mangosuthu University of Technology, P.O. Box 12363 Jacobs, Durban, 4026, South Africa"}], "References": []}, {"ArticleId": 92640934, "Title": "Wireless multipath video transmission: when IoT video applications meet networking—a survey", "Abstract": "<p>Advances in video camera and wireless communication technology have enabled a variety of video applications over the Internet. However, meeting these applications’ quality-of-service requirements poses significant challenges to the underlying network and has attracted significant attention from the networking research community. In particular, wireless multipath video transmission has been proposed as a viable alternative to deliver adequate performance to Internet video applications. This survey provides a thorough review of the current state-of-the-art in multipath video transmission focusing on IoT applications. We introduce a taxonomy to classify existing approaches based on their application-specific mechanisms (e.g., video coding techniques) as well as networking-specific techniques. In addition to describing existing approaches in light of the proposed taxonomy, we also discuss directions for future work.</p>", "Keywords": "Wireless video transmission; Internet of things; Wireless multimedia sensor network; Multipath routing; Video coding", "DOI": "10.1007/s00530-021-00885-4", "PubYear": 2022, "Volume": "28", "Issue": "3", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal Fluminense, Niterói, Brazil;Centro Federal de Educação Tecnológica de Minas Gerais, Leopoldina, Brazil"}, {"AuthorId": 2, "Name": "Diego <PERSON>", "Affiliation": "Universidade Federal Fluminense, Niterói, Brazil;Instituto Superior de Engenharia de Lisboa, Instituto Politécnico de Lisboa, Lisboa, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal Fluminense, Niterói, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of California Santa Cruz, Santa Cruz, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade Federal Fluminense, Niterói, Brazil"}], "References": []}, {"ArticleId": 92641051, "Title": "Light-driven carbon nitride microswimmers with propulsion in biological and ionic media and responsive on-demand drug delivery", "Abstract": "We propose two-dimensional poly(heptazine imide) (PHI) carbon nitride microparticles as light-driven microswimmers in various ionic and biological media. Their high-speed (15 to 23 micrometer per second; 9.5 ± 5.4 body lengths per second) swimming in multicomponent ionic solutions with concentrations up to 5 M and without dedicated fuels is demonstrated, overcoming one of the bottlenecks of previous light-driven microswimmers. Such high ion tolerance is attributed to a favorable interplay between the particle’s textural and structural nanoporosity and optoionic properties, facilitating ionic interactions in solutions with high salinity. Biocompatibility of these microswimmers is validated by cell viability tests with three different cell lines and primary cells. The nanopores of the swimmers are loaded with a model cancer drug, doxorubicin (DOX), resulting in a high (185%) loading efficiency without passive release. Controlled drug release is reported under different pH conditions and can be triggered on-demand by illumination. Light-triggered, boosted release of DOX and its active degradation products are demonstrated under oxygen-poor conditions using the intrinsic, environmentally sensitive and light-induced charge storage properties of PHI, which could enable future theranostic applications in oxygen-deprived tumor regions. These organic PHI microswimmers simultaneously address the current light-driven microswimmer challenges of high ion tolerance, fuel-free high-speed propulsion in biological media, biocompatibility, and controlled on-demand cargo release toward their biomedical, environmental, and other potential applications.", "Keywords": "", "DOI": "10.1126/scirobotics.abm1421", "PubYear": 2022, "Volume": "7", "Issue": "62", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Nanochemistry Department, Max Planck Institute for Solid State Research, 70569 Stuttgart, Germany."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Nanochemistry Department, Max Planck Institute for Solid State Research, 70569 Stuttgart, Germany.;Department of Chemistry, Ludwig-Maximilians-Universität München, 81377 Munich, Germany."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Nanochemistry Department, Max Planck Institute for Solid State Research, 70569 Stuttgart, Germany.;Department of Chemistry, Ludwig-Maximilians-Universität München, 81377 Munich, Germany."}, {"AuthorId": 6, "Name": "Vimal Kishore", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany.;Department of Physics, Banaras Hindu University, Varanasi 221005, India."}, {"AuthorId": 7, "Name": "<PERSON><PERSON> V. <PERSON>", "Affiliation": "Nanochemistry Department, Max Planck Institute for Solid State Research, 70569 Stuttgart, Germany.;Department of Chemistry, Ludwig-Maximilians-Universität München, 81377 Munich, Germany.;Cluster of Excellence e-conversion, Lichtenbergstrasse 4, 85748 Garching, Germany."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany.;Institute for Biomedical Engineering, ETH Zurich, 8092 Zurich, Switzerland.;School of Medicine and College of Engineering, Koç University, 34450 Istanbul, Turkey."}], "References": [{"Title": "Carbon‐Dot‐Induced Acceleration of Light‐Driven Micromotors with Inherent Fluorescence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "1900159", "JournalTitle": "Advanced Intelligent Systems"}]}]