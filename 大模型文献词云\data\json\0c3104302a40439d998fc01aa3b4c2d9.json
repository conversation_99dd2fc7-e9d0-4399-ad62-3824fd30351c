[{"ArticleId": 111053338, "Title": "Human Activity Recognition based on Local Linear Embedding and Geodesic Flow Kernel on Grassmann manifolds", "Abstract": "Human Activity Recognition (HAR) plays a crucial role in various applications(e.g., medical treatment, video surveillance and sports monitoring). Transfer learning is a promising solution to cross-domain identification problems in HAR. However, existing methods usually ignore the negative transfer caused by using the features of each source domain in equal proportions, as well as the distribution difference between the source and target domains. In this paper, an HAR method based on manifold learning is proposed. Firstly, the similarity between the domain and multiple source domains is calculated using the Multi-Kernel-Maximum Mean Difference (MK-MMD), and the source domain most similar to the target domain is selected as the optimal source domain in the transfer task. Secondly, Locally Linear Embedding (LLE) is leveraged to reduce the dimensionality of both optimal source domain and target domain data to remove redundant information, and the Geodesic Flow Kernel (GFK) is utilized to project low-dimensional data into the Grassmann manifold space and reduce the distribution difference between the two domains. Finally, the source domain action training model is applied to the target domain. Three public datasets (i.e., PAMAP2, OPPORTUNITY and UCI DSADS) are utilized to validate the effectiveness of the proposed approach. Experimental results are presented to demonstrate that the proposed HAR method can predict a large number of unlabeled samples in the target domain while preserving the original data structure .", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122696", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi’an University of Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China;Shaanxi Key Laboratory for Network Computing and Security Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi’an University of Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi’an University of Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi’an University of Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi’an University of Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China;Shaanxi Key Laboratory for Network Computing and Security Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China;Corresponding author at: School of Computer Science and Engineering, Xi’an University of Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi’an University of Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China;Shaanxi Key Laboratory for Network Computing and Security Technology, No. 5 South Jinhua Road, Xi’an, 710048, Shaanxi, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Computing, Engineering and Mathematical Sciences, La Trobe University, Melbourne 3086, Australia"}], "References": [{"Title": "Robust automated Parkinson disease detection based on voice signals with transfer learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "115013", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Smart, comfortable wearable system for recognizing Arabic Sign Language in real-time using IMUs and features-based fusion", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115448", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Human activity recognition using wearable sensors by heterogeneous convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116764", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Integration of deep adaptation transfer learning and online sequential extreme learning machine for cross-person and cross-position activity recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118807", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111053375, "Title": "Givens rotations for QR decomposition, SVD and PCA over database joins", "Abstract": "This article introduces FiGaRo , an algorithm for computing the upper-triangular matrix in the QR decomposition of the matrix defined by the natural join over relational data. FiGaRo ’s main novelty is that it pushes the QR decomposition past the join. This leads to several desirable properties. For acyclic joins, it takes time linear in the database size and independent of the join size. Its execution is equivalent to the application of a sequence of Givens rotations proportional to the join size. Its number of rounding errors relative to the classical QR decomposition algorithms is on par with the database size relative to the join output size. The QR decomposition lies at the core of many linear algebra computations including the singular value decomposition (SVD) and the principal component analysis (PCA). We show how FiGaRo can be used to compute the orthogonal matrix in the QR decomposition, the SVD and the PCA of the join output without the need to materialize the join output. A suite of experiments validate that FiGaRo can outperform both in runtime performance and numerical accuracy the LAPACK library Intel MKL by a factor proportional to the gap between the sizes of the join output and input.", "Keywords": "Givens rotations; QR decomposition; SVD; PCA; Relational databases; Joins", "DOI": "10.1007/s00778-023-00818-9", "PubYear": 2024, "Volume": "33", "Issue": "4", "JournalId": 4083, "JournalTitle": "The VLDB Journal", "ISSN": "1066-8888", "EISSN": "0949-877X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Zurich, Zurich, Switzerland; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ruhr University Bochum, Bochum, Germany"}, {"AuthorId": 3, "Name": "Ɖorđe <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}], "References": [{"Title": "Learning Models over Relational Data Using Sparse Tensors and Functional Dependencies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Database Systems"}, {"Title": "Scalable linear algebra on a relational database system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "8", "Page": "93", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 111053382, "Title": "Feature Engineering with Regularity Structures", "Abstract": "We investigate the use of models from the theory of regularity structures as features in machine learning tasks. A model is a polynomial function of a space–time signal designed to well-approximate solutions to partial differential equations (PDEs), even in low regularity regimes. Models can be seen as natural multi-dimensional generalisations of signatures of paths; our work therefore aims to extend the recent use of signatures in data science beyond the context of time-ordered data. We provide a flexible definition of a model feature vector associated to a space–time signal, along with two algorithms which illustrate ways in which these features can be combined with linear regression. We apply these algorithms in several numerical experiments designed to learn solutions to PDEs with a given forcing and boundary data. Our experiments include semi-linear parabolic and wave equations with forcing, and <PERSON><PERSON> equation with no forcing. We find an advantage in favour of our algorithms when compared to several alternative methods. Additionally, in the experiment with <PERSON><PERSON> equation, we find non-trivial predictive power when noise is added to the observations.", "Keywords": "Regularity structures; Path signatures; Regression; Supervised learning; Partial differential equations", "DOI": "10.1007/s10915-023-02401-4", "PubYear": 2024, "Volume": "98", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Bath, Bath, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Münster, Münster, Germany"}], "References": []}, {"ArticleId": 111053678, "Title": "FCIoU: A Targeted Approach for Improving Minority Class Detection in Semantic Segmentation Systems", "Abstract": "<p>In this paper, we present a comparative study of modern semantic segmentation loss functions and their resultant impact when applied with state-of-the-art off-road datasets. Class imbalance, inherent in these datasets, presents a significant challenge to off-road terrain semantic segmentation systems. With numerous environment classes being extremely sparse and underrepresented, model training becomes inefficient and struggles to comprehend the infrequent minority classes. As a solution to this problem, loss functions have been configured to take class imbalance into account and counteract this issue. To this end, we present a novel loss function, Focal Class-based Intersection over Union (FCIoU), which directly targets performance imbalance through the optimization of class-based Intersection over Union (IoU). The new loss function results in a general increase in class-based performance when compared to state-of-the-art targeted loss functions.</p>", "Keywords": "", "DOI": "10.3390/make5040085", "PubYear": 2023, "Volume": "5", "Issue": "4", "JournalId": 51955, "JournalTitle": "Machine Learning and Knowledge Extraction", "ISSN": "", "EISSN": "2504-4990", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronics (DOE), Carleton University, Ottawa, ON K1S 5B6, Canada; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Systems and Communication, Carleton University, Ottawa, ON K1S 5B6, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics (DOE), Carleton University, Ottawa, ON K1S 5B6, Canada"}], "References": []}, {"ArticleId": 111053748, "Title": "Fire identification based on improved multi feature fusion of YCbCr and regional growth", "Abstract": "Fire is one of the mutable hazards that damage properties and destroy forests. However, fire-like objects easily influence many existing image-based fire detection methods. In order to improve the performance of fire identification, this paper proposes a new fire identification algorithm by merging fire segmentation and multi feature fusion of fire. First, the improved YCbCr models in the reflection and non-reflection environment are constructed according to the color model. Simultaneously, the reflection and non-reflection conditions can be judged according to the segmented area. Second, the seed points are determined according to the weighted average of centroid of each connected region. Simultaneously, the fine segmentation of fire image is implemented according to the entropy of average contrast and uniformity within the connected region. Experiments show that the segmentation is not affected by image noises. Finally, the quantitative indicators of fire identification are given according to the coefficient of variation of area, the dispersion of centroid and the circularity. Cases show that the proposed identification method of fire not only accurately identifies fire, but also has a lower computation complexity than the deep learning method.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122661", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Xijiang Chen", "Affiliation": "School of Artificial Intelligence, Wuchang University of Technology, Wuhan, China;School of Safety Science and Emergency Management, Wuhan University of Technology, Wuhan, China;Corresponding author"}, {"AuthorId": 2, "Name": "Qing An", "Affiliation": "School of Artificial Intelligence, Wuchang University of Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Yu", "Affiliation": "School of Environment Science and Spatial Informatics, China University of Mining and Technology, Xuzhou, China"}], "References": [{"Title": "A Light-Weight Change Detection Method Using YCbCr-Based Texture Consensus Model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "9", "Page": "2050023", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}]}, {"ArticleId": 111053757, "Title": "Saturation-Based Boolean Conjunctive Query Answering and Rewriting for the Guarded Quantification Fragments", "Abstract": "Query answering is an important problem in AI, database and knowledge representation. In this paper, we develop saturation-based Boolean conjunctive query answering and rewriting procedures for the guarded, the loosely guarded and the clique-guarded fragments. Our query answering procedure improves existing resolution-based decision procedures for the guarded and the loosely guarded fragments and this procedure solves Boolean conjunctive query answering problems for the guarded, the loosely guarded and the clique-guarded fragments. Based on this query answering procedure, we also introduce a novel saturation-based query rewriting procedure for these guarded fragments. Unlike mainstream query answering and rewriting methods, our procedures derive a compact and reusable saturation, namely a closure of formulas, to handle the challenge of querying for distributed datasets. This paper lays the theoretical foundations for the first automated deduction decision procedures for Boolean conjunctive query answering and the first saturation-based Boolean conjunctive query rewriting in the guarded, the loosely guarded and the clique-guarded fragments.", "Keywords": "Saturation-based decision procedure; Saturation-based query rewriting; Boolean conjunctive query; Unskolemisation; Guarded fragment; Loosely guarded fragment; Clique-guarded fragment", "DOI": "10.1007/s10817-023-09687-x", "PubYear": 2023, "Volume": "67", "Issue": "4", "JournalId": 24991, "JournalTitle": "Journal of Automated Reasoning", "ISSN": "0168-7433", "EISSN": "1573-0670", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Manchester, Manchester, UK"}, {"AuthorId": 2, "Name": "Renate <PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Manchester, Manchester, UK"}], "References": [{"Title": "Combining Induction and Saturation-Based Theorem Proving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "2", "Page": "253", "JournalTitle": "Journal of Automated Reasoning"}, {"Title": "Formalizing <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>’s Ordered Resolution Prover", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "7", "Page": "1169", "JournalTitle": "Journal of Automated Reasoning"}]}, {"ArticleId": 111053847, "Title": "Editorial: Infrastructure sharing in broadband networks: impact on telecommunications operators and consumers", "Abstract": "", "Keywords": "Mobile sharing; digital economy; Network Sharing; communications networks; infrastructure", "DOI": "10.3389/fcomp.2023.1327140", "PubYear": 2023, "Volume": "5", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Oxford Martin School, University of Oxford, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hellenic Telecommunications and Post Commission (EETT), Greece; Department of Informatics and Telecommunications, University of Peloponnese, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hellenic Telecommunications and Post Commission (EETT), Greece; Department of Informatics and Telecommunications, University of Athens, Greece"}], "References": []}, {"ArticleId": *********, "Title": "Dynamic Adaptation Using Deep Reinforcement Learning for Digital Microfluidic Biochips", "Abstract": "<p>We describe an exciting new application domain for deep reinforcement learning (RL): droplet routing on digital microfluidic biochips (DMFBs). A DMFB consists of a two-dimensional electrode array, and it manipulates droplets of liquid to automatically execute biochemical protocols for clinical chemistry. However, a major problem with DMFBs is that electrodes can degrade over time. The transportation of droplet transportation over these degraded electrodes can fail, thereby adversely impacting the integrity of the bioassay outcome. We demonstrated that the fomulation of droplet transportation as an RL problem enables the training of deep neural network policies that can adapt to the underlying health conditions of electrodes and ensure reliable fluidic operations. We describe an RL-based droplet-routing solution that can be used for various sizes of DMFBs. We highlight the reliable execution of an epigenetic bioassay with the RL droplet router on a fabricated DMFB. We show that the use of the RL approach on a simple micro-computer (Raspberry Pi 4) leads to acceptable performance for time-critical bioassays. We present a simulation environment based on the OpenAI Gym Interface for RL-guided droplet routing problems on DMFBs. We present results on our study of electrode degradation using fabricated DMFBs. The study supports the degradation model used in the simulator.</p>", "Keywords": "", "DOI": "10.1145/3633458", "PubYear": 2024, "Volume": "29", "Issue": "2", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NVIDIA Corporation, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Duke University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Marvell Technology Inc., USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Duke University, USA"}, {"AuthorId": 5, "Name": "Tsung-<PERSON>", "Affiliation": "National Tsing Hua University, Taiwan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Duke University, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Duke University, USA"}], "References": [{"Title": "Deep Reinforcement Learning: A State-of-the-Art Walkthrough", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "69", "Issue": "", "Page": "1421", "JournalTitle": "Journal of Artificial Intelligence Research"}]}, {"ArticleId": 111053912, "Title": "Signal Processing and Machine Learning Algorithm to Classify Anaesthesia Depth", "Abstract": "<p>Volume 30, Issue 1 of the BMJ Health and Care Informatics Journal features a new piece of original research. <PERSON>, <PERSON> and Dr <PERSON> present the findings of a study into how information can impact on the monitoring of anaesthetic depth (AD) during surgical procedures.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad138", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111053976, "Title": "Locally linear attributes of ReLU neural networks", "Abstract": "<p>A ReLU neural network functions as a continuous piecewise linear map from an input space to an output space. The weights in the neural network determine a partitioning of the input space into convex polytopes, where each polytope is associated with a distinct affine mapping. The structure of this partitioning, together with the affine map attached to each polytope, can be analyzed to investigate the behavior of the associated neural network. We investigate simple problems to build intuition on how these regions act and both how they can potentially be reduced in number and how similar structures occur across different networks. To validate these intuitions, we apply them to networks trained on MNIST to demonstrate similarity between those networks and the potential for them to be reduced in complexity.</p>", "Keywords": "neural networks; RELU; Linearization; linear mapping; Polyhedral decomposition; Jacobian matrices", "DOI": "10.3389/frai.2023.1255192", "PubYear": 2023, "Volume": "6", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Colorado State University, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Colorado State University, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Colorado State University, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Colorado State University, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Colorado State University, United States"}], "References": []}, {"ArticleId": 111053981, "Title": "Perbandingan Kinerja k-Nearest Neighbor dan Local Mean Distance k-Nearest Neighbor Pada Data Citra Covid-19", "Abstract": "Corona Virus Disease 2019 (covid-19) merupakan pandemi dunia yang menimbulkan berbagai kerugian di dunia. Diagnosis covid-19 yang valid memerlukan waktu yang cukup lama dan hasil ini tidak sepenuhnya akurat. Salah satu cara untuk meningkatkan hasil akurasi adalah dengan menggunakan image classification. k-Nearest Neighbor (kNN) adalah salah satu Teknik klasifikasi yang paling banyak digunakan untuk melakukan pekerjaan tersebut, hanya saja kNN masih memiliki kelemahan. Untuk mengatasi kelemahan pada kNN, maka dilakukan modifikasi dengan menambahkan local mean dan distance weight, di mana varian kNN ini dikenal dengan nama Local Mean Distance Weight k-Nearest Neighbor (LMDWkNN). <PERSON>h sebab itu, penelitian kali mencoba membandingkan kinerja kedua algoritma ini untuk melakukan image classification pada citra covid-19. Kinerja diukur berdasarkan nilai akurasi, precision, dan recall, di mana dari hasil pengujian terbukti bahwa kinerja LMDWkNN lebih baik dari pada kinerja kNN. Algoritma LMDWkNN mengalami peningkatan rata-rata sebesar 3.5% untuk nilai akurasi, 2.89% untuk precision, dan 3.54% untuk recall. Meskipun begitu kNN tetap mampu menghasilkan kinerja yang sama baiknya yang mana kinerja kNN akan sangat bergantung dari nilai k yang digunakan", "Keywords": "Corona Virus Disease 2019 (covid-19);Image Classification;k-Nearest Neighbor (kNN);Local Mean Distance Weight k-Nearest Neighbor (LMDWkNN);Performance", "DOI": "10.35143/jkt.v9i1.6019", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institut Kesehatan Helvetia"}], "References": [{"Title": "Implementasi K-Nearest Neighbor untuk Klasifikasi Bunga Dengan Ekstraksi Fitur Warna RGB", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "6", "Page": "1129", "JournalTitle": "Jurnal Teknologi Informasi dan <PERSON>"}, {"Title": "Implementasi K-Nearest Neighbor untuk Klasifikasi Bunga Dengan Ekstraksi Fitur Warna RGB", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "6", "Page": "1129", "JournalTitle": "Jurnal Teknologi Informasi dan <PERSON>"}, {"Title": "Improving artificial bee colony algorithm using modified nearest neighbor sequence", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "8807", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "K-nearest neighbors rule combining prototype selection and local feature weighting for classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "243", "Issue": "", "Page": "108451", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A multi-voter multi-commission nearest neighbor classifier", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "6292", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "K Nearest Neighbor OveRsampling approach: An open source python package for data augmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "", "Page": "100272", "JournalTitle": "Software Impacts"}, {"Title": "Local-based k values for multi-label k -nearest neighbors rule", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105487", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 111054271, "Title": "Configuration optimization for heterogeneous time-sensitive networks", "Abstract": "<p>Time-Sensitive Networking (TSN) collectively defines a set of protocols and standard amendments that enhance IEEE 802.1Q Ethernet nodes with time-aware and fault-tolerant capabilities. Specifically, the IEEE 802.1Qbv amendment defines a timed-gate mechanism that governs the real-time transmission of critical traffic via a so-called Gate Control List (GCL) schedule encoded in each TSN-capable network device. Most TSN scheduling mechanisms are designed for homogeneous TSN networks in which all network devices must have at least the TSN capabilities related to scheduled gates and time synchronization. However, this assumption is often unrealistic since many distributed applications use heterogeneous TSN networks with legacy or off-the-shelf end systems that are unscheduled and/or unsynchronized. We propose a new scheduling paradigm for heterogeneous TSN networks that intertwines a network calculus worst-case interference analysis within the scheduling step. Through this, we compromise on the solution’s optimality to be able to support heterogeneous TSN networks featuring unscheduled and/or unsynchronized end-systems while guaranteeing the real-time properties of critical communication. Within this new paradigm, we propose two solutions to solve the problem, one based on a Constraint Programming formulation and one based on a Simulated Annealing metaheuristic, that provide different trade-offs and scalability properties. We compare and evaluate our flexible window-based scheduling methods using both synthetic and real-world test cases, validating the correctness and scalability of our implementation. Furthermore, we use OMNET++ to validate the generated GCL schedules.</p>", "Keywords": "Time-Sensitive Networking (TSN); Scheduled traffic; Metaheuristics; Constraint programming", "DOI": "10.1007/s11241-023-09414-0", "PubYear": 2023, "Volume": "59", "Issue": "4", "JournalId": 3371, "JournalTitle": "Real-Time Systems", "ISSN": "0922-6443", "EISSN": "1573-1383", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technical University of Denmark, Kongens Lyngby, Denmark"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univerity of California Irvine, Irvine, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "Silviu S. Craciunas", "Affiliation": "TTTech Computertechnik AG, Vienna, Austria"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Kongens Lyngby, Denmark"}], "References": [{"Title": "SAT modulo discrete event simulation applied to railway design capacity analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "2", "Page": "211", "JournalTitle": "Formal Methods in System Design"}, {"Title": "Time-Sensitive Networking in automotive embedded systems: State of the art and research opportunities", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "102137", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Constraint programming approaches to joint routing and scheduling in time-sensitive networks", "Authors": "<PERSON><PERSON>; Zdeněk Han<PERSON>álek; <PERSON><PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107317", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Reliability-aware Scheduling and Routing for Messages in Time-sensitive Networking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "Large-scale periodic scheduling in time-sensitive networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Zdeněk Hanzálek", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "105512", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": *********, "Title": "Mitigate Sensitive Data Risks With ChatGPT", "Abstract": "<p>As the use of generative AI tools becomes commonplace, <PERSON><PERSON>, distinguished VP <PERSON><PERSON><PERSON> at Gartner, looks at the key steps that organisations can take to help protect their sensitive data.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad120", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "Avivah Litan", "Affiliation": ""}], "References": []}, {"ArticleId": 111054315, "Title": "Solving human-robot collaborative mixed-model two-sided assembly line balancing using multi-objective discrete artificial bee colony algorithm", "Abstract": "Human-robot collaboration (HRC) is an emerging technology that aims to optimize production efficiency and reduce ergonomic risks by enabling humans and collaborative robots to work together in a shared workspace. This paper investigates HRC mixed-model two-sided assembly line balancing problem, which is a challenging real-world problem in manufacturing systems . The problem is formulated using a mixed-integer programming model with the objectives of minimizing ergonomic risk, energy consumption, and cycle time. To solve the problem efficiently, a multi-objective discrete artificial bee colony algorithm with specialist bees (MDABCSB) is proposed. MDABCSB utilizes a new five-vector encoding and decoding scheme and incorporates two types of neighbor structures for each vector. Additionally, the algorithm introduces specialist bees to search for the optimal solution for each objective, combines a tabu list to help them jump out of local optima, and employs variable neighborhood descent to enhance their exploration capabilities. Furthermore, the algorithm adopts a new temperature-based food sources update mechanism to probabilistically update the food sources of employed bees to reinforce exploitation. By comparing the proposed algorithm with other algorithms, the results demonstrate that MDABCSB performs better in terms of solution performance.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109776", "PubYear": 2024, "Volume": "187", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan, PR China;Department of Mechanical Engineering, Hubei University of Technology, Wuhan, PR China;Corresponding author at: Department of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan, PR China"}, {"AuthorId": 3, "Name": "Ruiping <PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan, PR China"}, {"AuthorId": 4, "Name": "Yingkang Lu", "Affiliation": "Department of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan, PR China"}, {"AuthorId": 5, "Name": "Gaocai Fu", "Affiliation": "Department of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Hubei University of Technology, Wuhan, PR China"}], "References": [{"Title": "Ergonomic risk and cycle time minimization for the U-shaped worker assignment assembly line balancing problem: A multi-objective approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "118", "Issue": "", "Page": "104905", "JournalTitle": "Computers & Operations Research"}, {"Title": "The Collaboration of Human-Robot in Mixed-Model Four-Sided Assembly Line Balancing Problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "1", "Page": "71", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Mathematical model and bee algorithms for mixed-model assembly line balancing problem with physical human–robot collaboration", "Authors": "<PERSON>ey<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106394", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-objective migrating bird optimization algorithm for cost-oriented assembly line balancing problem with collaborative robots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "14", "Page": "8575", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Job rotation and human–robot collaboration for enhancing ergonomics in assembly lines by a genetic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "9-10", "Page": "2901", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Energy-efficient project scheduling with supplier selection in manufacturing projects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116446", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Harmonizing ergonomics and economics of assembly lines using collaborative robots and exoskeletons", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "681", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Deliberative safety for industrial intelligent human–robot collaboration: Regulatory challenges and solutions for taking the next step towards industry 4.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "102386", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Robust assembly line balancing problem considering preventive maintenance scenarios with interval processing time", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105417", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multimanned partial disassembly line balancing optimization considering end-of-life states of products and skill differences of workers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "", "Page": "107", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Resource reconfiguration and optimization in brownfield constrained Robotic Assembly Line Balancing Problems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "67", "Issue": "", "Page": "132", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Models and algorithms for U-shaped assembly line balancing problem with collaborative robots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "14", "Page": "9639", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 111054328, "Title": "XLR-Net: Explainable AI-driven improved L 1 -regularized deep neural architecture for NSCLC biomarker identification", "Abstract": "<b  >Background and Objective:</b> Non-small cell lung cancer (NSCLC) exhibits intrinsic molecular heterogeneity, primarily driven by the mutation of specific biomarkers. Identification of these biomarkers would assist not only in distinguishing NSCLC into its major subtypes — Adenocarcinoma and Squamous Cell Carcinoma , but also in developing targeted therapy. Medical practitioners use one or more types of omic data to identify these biomarkers, copy number variation (CNV) being one such type. CNV provides a measure of genomic instability, which is considered a hallmark of carcinoma. However, the CNV data has not received much attention for biomarker identification. This paper aims to identify biomarkers for NSCLC using CNV data. <b  >Methods:</b> An eXplainable AI (XAI)-driven L 1 -regularized deep learning architecture, XL<sub>1</sub>R-Net , is proposed that introduces a novel modification of the standard L 1 -regularized gradient descent algorithm to arrive at an improved deep neural classifier for NSCLC subtyping. Further, XAI-based feature identification has been used to leverage the trained classifier to uncover a set of twenty NCSLC-relevant biomarkers. <b  >Results:</b> The identified biomarkers are evaluated based on their classification performance and clinical relevance. Using Multilayer Perceptron (MLP)-based model, a classification accuracy of 84.95% using 10-fold cross-validation is achieved. Moreover, the statistical significance test on the classification performance also revealed the superiority of the MLP model over the competitive machine learning models. Further, the publicly available Drug–Gene Interaction Database reveals twelve of the identified biomarkers as potentially druggable. The K–M Plotter tool was used to verify eighteen of the identified biomarkers with a high probability of predicting NSCLC patients’ likelihood of survival. While nine of the identified biomarkers confirm the recent literature, five find mention in the OncoKB Gene List. <b  >Conclusion:</b> A set of seven novel biomarkers that have not been reported in the literature could be investigated for their potential contribution towards NSCLC therapy. Given NSCLC’s genetic diversity, using only one omics data type may not adequately capture the tumor’s complexity. Multiomics data and its integration with other sources will be examined in the future to better understand NSCLC heterogeneity.", "Keywords": "Biomarker;Classification;Explainable AI;L(1)-regularization;Neural network;Non-small cell lung cancer", "DOI": "10.1016/j.compbiolchem.2023.107990", "PubYear": 2024, "Volume": "108", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Delhi, Delhi, India. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Delhi, Delhi, India. Electronic address:  ."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Dyal Singh College, Delhi, India. Electronic address:  ."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Nuclear Magnetic Resonance, All India Institute of Medical Sciences, New Delhi, India. Electronic address:  ."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Hans Raj College, University of Delhi, Delhi, India. Electronic address:  ."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Delhi, Delhi, India. Electronic address:  ."}], "References": [{"Title": "Improving performance of deep learning models with axiomatic attribution priors and expected gradients", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "7", "Page": "620", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Unbox the black-box for the medical explainable AI via multi-modal and multi-centre data fusion: A mini-review, two showcases and beyond", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "29", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 111054370, "Title": "Numerical Simulation Intra-Chamber of Unsteady Turbulent Flows Stimulate. Part 2", "Abstract": "The flow of gas in a solid-fuel rocket engine is determined by the peculiarities of the physico-chemical processes occurring in the combustion chamber and the process of gas outflow from the nozzle. The paper proposes a method for modeling internal unsteady turbulent flows in a rocket engine with a solid fuel charge of a telescopic type. A system of defining equations written in a cylindrical coordinate system describing the flow of a compressible viscous gas is given. A computational algorithm is proposed that belongs to the class of methods using the <PERSON><PERSON><PERSON> approach, developed on the basis of a modified flow vector splitting scheme. The obtained results of a numerical study of the flow in a model rocket engine show the dependence of the gas temperature on the engine wall on the combustion rate of a low-temperature external charge of a telescopic charge. © 2023 South Ural State University. All rights reserved.", "Keywords": "computational fluid dynamics; intra-chamber processes; turbulence; unsteady flow", "DOI": "10.14529/mmp230103", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111054379, "Title": "Development and assessment of MyAccessible Math: promoting self-learning for students with vision impairment", "Abstract": "<p>Human–computer interaction (HCI) research aims to make systems versatile, easy to use, and accessible for most people. The abundant information on the Internet and recent improvements in HCI technologies have improved how we acquire, share, and test our knowledge. While computer technologies have successfully remodeled and improved the learning process, the potential promised by these technologies has not become a reality for students with vision impairments. This paper presents MyAccessible Math, an open-source web platform designed with integrating user-centered design principles for SVIs to practice mathematics. Furthermore, we evaluate the voice and keyboard interaction on MyAccessible Math using experimental research designs. Experimental research designs investigated the effectiveness of the application with ten visually impaired elementary, middle, and high school students from the Alabama Institute for the Deaf and Blind and the Alabama School for the Blind. The experimental data show that voice and keyboard interaction helped students practice math questions.</p>", "Keywords": "Information and communication technologies (ICT); Human–computer interaction (HCI); Assistive technologies; Mathematics; Students with vision impairment (SVI)", "DOI": "10.1007/s10209-023-01068-w", "PubYear": 2025, "Volume": "24", "Issue": "1", "JournalId": 1397, "JournalTitle": "Universal Access in the Information Society", "ISSN": "1615-5289", "EISSN": "1615-5297", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Auburn University, Auburn, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Auburn University, Auburn, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Auburn University, Auburn, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Auburn University, Auburn, USA"}], "References": []}, {"ArticleId": 111054385, "Title": "Why AI Will Never Outsmart a Human", "Abstract": "<p>The worlds of cybersecurity and AI are set to become fused as technology continues to adapt and evolve. <PERSON> spoke to <PERSON><PERSON> to learn more.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad127", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111054438, "Title": "On the Effectiveness of Fog Offloading in a Mobility-Aware Healthcare Environment", "Abstract": "<p>The emergence of fog computing has significantly enhanced real-time data processing by bringing computation resources closer to data sources. This adoption is very beneficial in the healthcare sector, where abundant time-sensitive processing tasks exist. Although such adoption is very promising, there is a challenge with the limited computational capacity of fog nodes. This challenge becomes even more critical when mobile IoT nodes enter the network, potentially increasing the network load. To address this challenge, this paper presents a framework that leverages a Many-to-One offloading (M2One) policy designed for modelling the dynamic nature and time-critical aspect of processing tasks in the healthcare domain. The framework benefits the multi-tier structure of the fog layer, making efficient use of the computing capacity of mobile fog nodes to enhance the overall computing capability of the fog network. Moreover, this framework accounts for mobile IoT nodes that generate an unpredictable volume of tasks at unpredictable intervals. Under the proposed policy, a first-tier fog node, called the coordinator fog node, efficiently manages all requests offloaded by the IoT nodes and allocates them to the fog nodes. It considers factors like the limited energy in the mobile nodes, the communication channel status, and low-latency demands to distribute requests among fog nodes and meet the stringent latency requirements of healthcare applications. Through extensive simulations in a healthcare scenario, the policy’s effectiveness showed an improvement of approximately 30% in average delay compared to cloud computing and a significant reduction in network usage.</p>", "Keywords": "", "DOI": "10.3390/digital3040019", "PubYear": 2023, "Volume": "3", "Issue": "4", "JournalId": 82793, "JournalTitle": "Digital", "ISSN": "", "EISSN": "2673-6470", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Macquarie University, Sydney 2109, Australia↑Department of Computer Engineering, Sharif University of Technology, Tehran 11155-9517, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Sharif University of Technology, Tehran 11155-9517, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Macquarie University, Sydney 2109, Australia↑School of Systems & Computing, University of New South Wales, Canberra, ACT 2600, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Sharif University of Technology, Tehran 11155-9517, Iran"}, {"AuthorId": 5, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "School of Computing, Macquarie University, Sydney 2109, Australia; Corresponding author"}], "References": [{"Title": "An energy harvesting solution for computation offloading in Fog Computing networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "577", "JournalTitle": "Computer Communications"}, {"Title": "Resource provisioning for IoT services in the fog computing environment: An autonomic approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "109", "JournalTitle": "Computer Communications"}, {"Title": "A survey on computation offloading modeling for edge computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "169", "Issue": "", "Page": "102781", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "CODE-V: Multi-hop computation offloading in Vehicular Fog Computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "86", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Heuristic Computation Offloading Algorithms for Mobile Users in Fog Computing", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "Context‐aware computation offloading for mobile edge computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "5123", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Cooperative computation offloading and resource allocation for delay minimization in mobile edge computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "102167", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Computation offloading and service allocation in mobile edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "12", "Page": "13933", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Edge-computing-driven Internet of Things: A Survey", "Authors": "Linghe Kong; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111054479, "Title": "InterviewFinancial Regulation and Governance in the Age of AI", "Abstract": "<p><PERSON> spoke to <PERSON><PERSON><PERSON> and <PERSON> <PERSON><PERSON> from the Financial Conduct Authority about their work in AI and the challenges associated with regulating technically evolving markets.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad117", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111054732, "Title": "A benchmark test suite for evolutionary multi-objective multi-concept optimization", "Abstract": "For real-world design optimization, there may often exist multiple candidate concepts that may solve the problem at hand. The process of concurrently identifying the best concept and the corresponding variable values that optimize the design objective(s) is termed as a multi-concept optimization (MCO). While such problems are commonly encountered in practical domains such as engineering, transport, product design, there has been little focus on developing computationally efficient algorithms for MCO. One of the reasons contributing to this gap is the scarcity of benchmark problems with diverse challenges that could be used as a testbed for the development and systematic evaluation of advanced MCO algorithms. In this paper, we particularly focus on MCO problems with multiple conflicting objectives. We review the existing multi-objective MCO test problems and identify their shortcomings through preliminary numerical experiments. Then, we propose a methodology and provide a test problem generator for systematically constructing new multi-objective MCO instances with desired properties. We also propose 28 specific test problem instances using this generator to build a benchmark suite that poses a wide range of challenges to the prospective search strategies. Further, we conduct numerical experiments on the proposed test suite using multiple existing algorithmic strategies from the literature to demonstrate their performance. The results clearly highlight the challenges faced by the strategies for different types of problems. These observations emphasize the need for research efforts towards the development of more efficient and versatile MCO algorithms to tackle a wider range of MCO problems.", "Keywords": "Multi-concept optimization ; Multi-objective optimization", "DOI": "10.1016/j.swevo.2023.101429", "PubYear": 2024, "Volume": "84", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Technology, The University of New South Wales, Canberra, 2600, ACT, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Technology, The University of New South Wales, Canberra, 2600, ACT, Australia"}, {"AuthorId": 3, "Name": "Tapabrata Ray", "Affiliation": "School of Engineering and Technology, The University of New South Wales, Canberra, 2600, ACT, Australia"}], "References": [{"Title": "Evolutionary multiobjective optimization: open research areas and some challenges lying ahead", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "221", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Multifactorial optimization via explicit multipopulation evolutionary framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1555", "JournalTitle": "Information Sciences"}]}, {"ArticleId": *********, "Title": "The supply chain information sharing path based on the internet of things", "Abstract": "In the context of globalization and digitization, supply chain information sharing and circulation have become important links in supply chain management. The current difficulty in sharing supply chain information is significant, and there are issues of information asymmetry and incompleteness among co participants. In order to improve the integrity and efficiency of supply chain information sharing and enhance the competitiveness of the high supply chain, this article conducts in‐depth research on the path of supply chain information sharing using Internet of Things technology. This article first analyzes the level of information sharing, influencing factors, and existing problems, then explores the Internet of Things technology, and finally establishes specific methods and means for the supply chain information sharing path through the Internet of Things technology. To verify the effectiveness of the supply chain information sharing path based on the Internet of Things, in this article, a comparison was made between the development effects of enterprise supply chains before and after applying the supply chain information sharing path based on the Internet of Things. The results show that after applying the IoT based supply chain information sharing path, the market price, production capacity, and market forecasting ability of enterprises have all increased by more than 20%. The conclusion indicates that the Internet of Things can help optimize supply chain information sharing and provide a new perspective for enterprise supply chain management and development.", "Keywords": "cloud computing;information sharing;internet of things;supply chain", "DOI": "10.1002/itl2.491", "PubYear": 2024, "Volume": "7", "Issue": "4", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Kmitl Business School King Mongkut'S Institute of Technology Ladkrabang  Bangkok Thailand;Guiyang Institute of Humanities and Technology in china  Guiyang China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Economics and Management Guizhou Normal University  Guiyang China"}], "References": [{"Title": "Secure Timestamp-Based Mutual Authentication Protocol for IoT Devices Using RFID Tags: ", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "3", "Page": "20", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "Modelling Supply Chain Information Collaboration Empowered with Machine Learning Technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "243", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 111054839, "Title": "Investigating an amplitude amplification-based optimization algorithm for model predictive control", "Abstract": "The potential for greater algorithmic efficiency for some problems on quantum computers compared to classical computers is appealing in many fields including, for example, the process systems engineering field. While quantum algorithms have been studied for a variety of applications related to optimization, molecular modeling, and machine learning, there remain many applications in process systems engineering, including process control, where it is not clear how quantum computing algorithms would be beneficial. One idea for attempting to understand when a quantum algorithm might provide benefits for control is to start with algorithms that would be expected to benefit “similar” problems (e.g., optimization problems) and to see if controllers can be implemented within those algorithmic frameworks. Therefore, in this work, we study the use of a quantum computing algorithm related to <PERSON><PERSON>’s algorithm, which is an amplitude amplification strategy that can search an unordered list with improved efficiency compared to a classical algorithm for the task. It has been extended to perform a search for optimal paths over a graph. Given its potential utility for search and optimization, this is an example of an algorithm where we might wonder if it could be adjusted or used to provide speed-ups for large control problems if the controller could function within this algorithmic framework. This work provides the first steps toward attempting to address this question by investigating how optimization-based control problems would fit into this framework. A process described by x ̇ = x + u is considered as a test case. The modified <PERSON><PERSON>’s algorithm requires the optimization problem to be mapped into quantum gates. We discuss ideas for attempting to represent an optimization-based controller known as model predictive control (MPC) in the modified <PERSON><PERSON>’s algorithm framework. We test how various parameters of the control and quantum algorithm designs, including fundamental parameters in MPC such as the number of sampling periods and length of the sampling periods, impact the success of using the quantum algorithm for the MPC. We provide analyses regarding why the results are what they are to give perspective on how quantum computing algorithms work and intersect with engineering problems.", "Keywords": "Quantum Computing ; Control ; Optimization", "DOI": "10.1016/j.dche.2023.100134", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Wayne State University, Department of Chemical Engineering & Materials Science, Detroit, MI, 48202, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Wayne State University, Department of Chemical Engineering & Materials Science, Detroit, MI, 48202, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Air Force Research Laboratory, Information Directorate, Rome, NY, 13441, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Air Force Research Laboratory, Information Directorate, Rome, NY, 13441, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Air Force Research Laboratory, Information Directorate, Rome, NY, 13441, USA"}], "References": [{"Title": "Quantum computing assisted deep learning for fault detection and diagnosis in industrial process systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "107119", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Quantum Computing and Resilient Design Perspectives for Cybersecurity of Feedback Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "703", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 111054874, "Title": "Insights into software development approaches: mining Q &A repositories", "Abstract": "Context \n Software practitioners adopt approaches like DevOps, Scrum, and Waterfall for high-quality software development. However, limited research has been conducted on exploring software development approaches concerning practitioners’ discussions on Q &A forums.\n \n Objective \n We conducted an empirical study to analyze developers’ discussions on Q &A forums to gain insights into software development approaches in practice.\n \n Method \n We analyzed 13,903 developers’ posts across Stack Overflow (SO), Software Engineering Stack Exchange (SESE), and Project Management Stack Exchange (PMSE) forums. A mixed method approach, consisting of the topic modeling technique (i.e., Latent Dirichlet Allocation (LDA)) and qualitative analysis, is used to identify frequently discussed topics of software development approaches, trends (popular, difficult topics), and the challenges faced by practitioners in adopting different software development approaches.\n \n Findings \n We identified 15 frequently mentioned software development approaches topics on Q &A sites and observed an increase in trends for the top-3 most difficult topics requiring more attention. Finally, our study identified 49 challenges faced by practitioners while deploying various software development approaches, and we subsequently created a thematic map to represent these findings.\n \n Conclusions \n The study findings serve as a useful resource for practitioners to overcome challenges, stay informed about current trends, and ultimately improve the quality of software products they develop.", "Keywords": "Software development approaches; Q& A websites; Software process improvement; Software repositories mining", "DOI": "10.1007/s10664-023-10417-5", "PubYear": 2024, "Volume": "29", "Issue": "1", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "M3S Empirical Software Engineering Research Unit, University of Oulu, Oulu, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, School of Physics, engineering and computer science, University of Hertfordshire, Hatfield, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Software Engineering Department, Lappeenranta-Lahti University of Technology, Lappeenranta, Finland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, University of Southern Queensland, Springfield, Australia"}], "References": [{"Title": "Improving software bug-specific named entity recognition with deep neural network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "165", "Issue": "", "Page": "110572", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Agile Software Development: Methodologies and Trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "11", "Page": "246", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "Agile trends in Chinese global software development industry: Fuzzy AHP based conceptual mapping", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107090", "JournalTitle": "Applied Soft Computing"}, {"Title": "How do i refactor this? An empirical study on refactoring trends and topics in Stack Overflow", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "1", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "A systematic process for Mining Software Repositories: Results from a systematic literature review", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "144", "Issue": "", "Page": "106791", "JournalTitle": "Information and Software Technology"}, {"Title": "Valuating requirements arguments in the online user's forum for requirements decision‐making: The CrowdRE‐VArg framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "12", "Page": "2537", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Does agile methodology fit all characteristics of software projects? Review and analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "28", "Issue": "4", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "Exploring and mining rationale information for low-rating software applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 111054919, "Title": "Artificial consciousness: the missing ingredient for ethical AI?", "Abstract": "<p>Can we conceive machines that can formulate autonomous intentions and make conscious decisions? If so, how would this ability affect their ethical behavior? Some case studies help us understand how advances in understanding artificial consciousness can contribute to creating ethical AI systems.</p>", "Keywords": "Artificial Consciousness; Robot ethics framework; Ethical AI; Robot consciousness; Cognitive architectures", "DOI": "10.3389/frobt.2023.1270460", "PubYear": 2023, "Volume": "10", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "RoboticsLab, Italy"}], "References": [{"Title": "Developing Self-Awareness in Robots via Inner Speech", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "16", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Attention and Consciousness in Intentional Action: Steps Toward Rich Artificial Agency", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "15", "JournalTitle": "Journal of Artificial Intelligence and Consciousness"}, {"Title": "Functionally Effective Conscious AI Without Suffering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "39", "JournalTitle": "Journal of Artificial Intelligence and Consciousness"}, {"Title": "Rethinking Autonomy of Humans and Robots", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "141", "JournalTitle": "Journal of Artificial Intelligence and Consciousness"}, {"Title": "The Theory of Cognitive Consciousness, and Λ (Lambda)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "155", "JournalTitle": "Journal of Artificial Intelligence and Consciousness"}, {"Title": "Artificial Suffering: An Argument for a Global Moratorium on Synthetic Phenomenology", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "43", "JournalTitle": "Journal of Artificial Intelligence and Consciousness"}, {"Title": "Cognitive Robots and the Conscious Mind: A Review of the Global Workspace Theory", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "125", "JournalTitle": "Current Robotics Reports"}, {"Title": "Artificial virtuous agents: from theory to machine implementation", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "4", "Page": "1301", "JournalTitle": "AI & SOCIETY"}, {"Title": "Fully body visual self-modeling of robot morphologies", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "68", "Page": "eabn1944", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 111055052, "Title": "An Artificial Neural Network Autoencoder for Insider Cyber Security Threat Detection", "Abstract": "<p>The COVID-19 pandemic made all organizations and enterprises work on cloud platforms from home, which greatly facilitates cyberattacks. Employees who work remotely and use cloud-based platforms are chosen as targets for cyberattacks. For that reason, cyber security is a more concerning issue and is now incorporated into almost every smart gadget and has become a prerequisite in every software product and service. There are various mitigations for external cyber security attacks, but hardly any for insider security threats, as they are difficult to detect and mitigate. Thus, insider cyber security threat detection has become a serious concern in recent years. Hence, this paper proposes an unsupervised deep learning approach that employs an artificial neural network (ANN)-based autoencoder to detect anomalies in an insider cyber security attack scenario. The proposed approach analyzes the behavior of the patterns of users and machines for anomalies and sends an alert based on a set security threshold. The threshold value set for security detection is calculated based on reconstruction errors that are obtained through testing the normal data. When the proposed model reconstructs the user behavior without generating sufficient reconstruction errors, i.e., no more than the threshold, the user is flagged as normal; otherwise, it is flagged as a security intruder. The proposed approach performed well, with an accuracy of 94.3% for security threat detection, a false positive rate of 11.1%, and a precision of 89.1%. From the obtained experimental results, it was found that the proposed method for insider security threat detection outperforms the existing methods in terms of performance reliability, due to implementation of ANN-based autoencoder which uses a larger number of features in the process of security threat detection.</p>", "Keywords": "", "DOI": "10.3390/fi15120373", "PubYear": 2023, "Volume": "15", "Issue": "12", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering (AIML), KPR Institute of Engineering and Technology, Coimbatore 641407, Tamil Nadu, India↑These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science and Engineering, VIT-AP University, Amaravati 522241, Andhra Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Technology, Madras Institute of Technology, Anna University, Chennai 600044, Tamil Nadu, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, Centre for IoT and AI (CITI), KPR Institute of Engineering and Technology, Coimbatore 641407, Tamil Nadu, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mechanical Engineering and Naval Architecture (FESB), University of Split, Rudjera Boskovca 32, 21000 Split, Croatia↑These authors contributed equally to this work.; Corresponding author"}], "References": [{"Title": "BEHACOM - a dataset modelling users’ behaviour in computers", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "", "Page": "105767", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 111055072, "Title": "The Green Function Method in the Problem of Random Signal Transformation by a Linear Dynamic System", "Abstract": "A dynamic system is considered, which is described by a high order linear differential equation with constant coefficients. The <PERSON>’s function method established the relationship between the numerical characteristics of a random signal at the input and output of a dynamic system, namely between mathematical expectations and between correlation functions. In contrast to the known results, the stationarity of the input and output random signals is not assumed. © 2023 South Ural State University. All rights reserved.", "Keywords": "continuous random processes; correlation functions; dynamical systems with random functions; mathematical expectations", "DOI": "10.14529/mmp230110", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": [{"Title": "On Estimates of the Mean Queue Length for Single-Channel Queuing Systems in Terms of Statistical Unconditional Second-Order Moments of the Modified Arrival Flow", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "83", "Issue": "1", "Page": "92", "JournalTitle": "Automation and Remote Control"}, {"Title": "АНАЛИЗ ДИНАМИЧЕСКОГО РЕГУЛЯТОРА ПО ВЫХОДНОМУ СИГНАЛУ ДЛЯ СТОХАСТИЧЕСКИХ СИСТЕМ МУЛЬТИПЛИКАТИВНОГО ТИПА", "Authors": "", "PubYear": 2022, "Volume": "", "Issue": "3", "Page": "", "JournalTitle": "Автоматика и телемеханика"}]}, {"ArticleId": 111055127, "Title": "Stepping into <PERSON>'s Shoes", "Abstract": "<p><PERSON><PERSON><PERSON>, <PERSON><PERSON>, joined as the custodian of the .uk domain in November 2022, taking over a tech transformation project that was already in progress. He considers what questions those in his position should be asking themselves and others.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad133", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111055142, "Title": "Mutual information maximization for semi-supervised anomaly detection", "Abstract": "Anomaly detection is of considerable importance in areas ranging from industrial production over financial transaction to medical diagnosis. Due to the extreme imbalance of anomaly detection datasets, semi-supervised anomaly detection methods based on deep generative models that only use normal samples in the training stage are shining in various fields. However, since real-world training datasets are inevitably polluted by noise samples and abnormal samples, the deployment of semi-supervised anomaly detection methods is being greatly challenged, and the actual effect is not satisfactory. In our opinion, the most fundamental reason might be that the latent representation of normal samples and abnormal samples learned by such methods are entangled. To tackle these problems, we propose to regularize latent representation learned by deep generative model through mutual information maximization and provide theoretical justification that the latent representations learned by our method are far away from abnormal. In addition, we further proposed a technique named adaptive filter that can discard noise samples and empirically show the effects to stabilize and enhance the model. We extensively evaluate our proposed method on tabular, image, and real-world datasets to show excellent effectiveness and robustness.", "Keywords": "", "DOI": "10.1016/j.knosys.2023.111196", "PubYear": 2024, "Volume": "284", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Applied Statistics, School of Statistics, Renmin University of China, 59 Zhongguancun Street, Beijing, 100872, China"}, {"AuthorId": 2, "Name": "Maozai Tian", "Affiliation": "Center for Applied Statistics, School of Statistics, Renmin University of China, 59 Zhongguancun Street, Beijing, 100872, China;Corresponding author"}], "References": [{"Title": "adVAE: A self-adversarial variational autoencoder with Gaussian anomaly prior knowledge for anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "190", "Issue": "", "Page": "105187", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Outlier Detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep Learning for Anomaly Detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>bing Cao", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep Learning for Medical Anomaly Detection – A Survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111055150, "Title": "On a Lagrangian-Eulerian Method Calculation of Unsteady Flows Compressible Media", "Abstract": "In this paper, a numerical method for calculating two-dimensional flows in Eulerian coordinates is implemented, which is based on an explicit Lagrangian-Eulerian difference scheme. The calculation of each time step is carried out in two stages. At the Lagrangian stage, a difference scheme based on the <PERSON><PERSON><PERSON><PERSON> method is used, which has zero energy dissipation on smooth solutions and minimal distraction on strong discontinuities. At the Eulerian stage, the mesh is rebuilded and all the parameters of the substance are recalculated from the old mesh to the new one in accordance with the laws of conservation of mass, momentum and energy. The developed numerical algorithm has shown its operability when tested on tasks having an analytical or reference solution. © 2023 South Ural State University. All rights reserved.", "Keywords": "<PERSON><PERSON><PERSON><PERSON> method; Lagrangian-Eulerian method; shock wave calculation method", "DOI": "10.14529/mmp230208", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": [{"Title": "Adaptation of <PERSON><PERSON><PERSON><PERSON> Method for Calculating Shock Waves in Euler Coordinates", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "91", "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\""}]}, {"ArticleId": 111055158, "Title": "Note on Exact Factorization Algorithm for Matrix Polynomials", "Abstract": "There are two major obstacles for a wide utilisation of the <PERSON><PERSON><PERSON> factorization technique for matrix functions used to solve vectorial Riemann boundary problems. The first one reflects the absence of a general explicit factorization method in the matrix case, even though there are some explicit (constructive) factorizations available for specific classes of matrix functions. The second obstacle follows from the fact that the factorization of a matrix function is, generally speaking, not stable operation with respect to a small perturbation of the original function. As a result of the latter, a realisation of any constructive algorithm, even if it exists for the given matrix function, cannot be performed in practice. Moreover, developing explicit methods, authors do not often analyze its numerical implementation, implicitly assuming that all steps of the proposed constructive algorithm can be carried out exactly. In the proposed work, we continue studying a relation between the explicit and exact solutions of the factorization problem in the class of matrix polynomials. The main goal is to obtain an algorithm for the exact evaluation of the so-called indices and essential polynomials of a finite sequence of matrices. This is the cornerstone of the problem of exact factorization of matrix polynomials. © 2023 South Ural State University. All rights reserved.", "Keywords": "essential polynomials of sequence; <PERSON><PERSON><PERSON> matrices; <PERSON><PERSON><PERSON><PERSON> factorization", "DOI": "10.14529/mmp230104", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111055159, "Title": "Graph Convolutional Neural Network with Multi-Scale Attention Mechanism for EEG-Based Motion Imagery Classification", "Abstract": "<p>Recently, deep learning has been widely used in the classification of EEG signals and achieved satisfactory results. However, the correlation between EEG electrodes is rarely considered, which has been proved that there are indeed connections between different brain regions. After considering the connections between EEG electrodes, the graph convolutional neural network is applied to detect human motor intents from EEG signals, where EEG data are transformed into graph data through phase lag index, time-domain and frequency-domain features with different signal bands. Meanwhile, a multi-scale attention mechanism is proposed to the network to improve the accuracy of classification. By using the multi-scale attention-based graph convolutional neural network, the accuracy of 93.22% is achieved with 10-fold cross-validation, which is higher than the compared methods which ignore the spatial correlations of EEG signals.</p>", "Keywords": "", "DOI": "10.1142/S0218001423540204", "PubYear": 2023, "Volume": "37", "Issue": "14", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mathematics, Southeast University, Nanjing 210096, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Southeast University, Nanjing 210096, P. R. <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Southeast University, Nanjing 210096, P. R. China"}], "References": [{"Title": "Representation Learning for EEG-Based Biometrics Using <PERSON><PERSON><PERSON><PERSON> Transform", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "3", "Page": "47", "JournalTitle": "Computers"}, {"Title": "Transfer learning-based EEG analysis of visual attention and working memory on motor cortex for BCI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "22", "Page": "20179", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Mental Workload Artificial Intelligence Assessment of Pilots’ EEG Based on Multi-Dimensional Data Fusion and LSTM with Attention Mechanism Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "11", "Page": "", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}]}, {"ArticleId": 111055181, "Title": "Parameters Identification Algorithm for the Susuplume Air Pollution Propagation Model", "Abstract": "The article presents the method of identifying the parameters of a dynamic dispersion calculation model SUSUPLUME. It is supposed that the model parameters contain not only the characteristics of the atmosphere and the pollutant, but also information about the influence of other particular conditions such as terrain, building, background, etc. The model parameters are configured based on instrumental measurements of concentrations of pollutants in the atmospheric air in the surface layer (2 meters above ground level). Three identification strategies are considered: identification of parameters by all measurements, identification of parameters by measurements of a given source and identification of parameters using another approved model. A method for weighing these strategies is proposed in the issue. The article also provides objective functions for optimization criteria, an acceptable set of parameters, an algorithm for solving an optimization problem, a decision tree of a feasible set and a global optimization algorithm. © 2023 South Ural State University. All rights reserved.", "Keywords": "ecology; global optimization; propagation of pollutants in the atmosphere; SUSUPLUME model", "DOI": "10.14529/mmp230306", "PubYear": 2023, "Volume": "16", "Issue": "3", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": [{"Title": "Numerical Study of the Susuplume Air Pollution Model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "5", "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\""}]}, {"ArticleId": 111055234, "Title": "3D Human Pose Estimation from multi-view thermal vision sensors", "Abstract": "Human Pose Estimation from images allows the recognition of key daily activity patterns in Smart Environments. Current State-of-the-art (SOTA) 3D pose estimators are built on visible spectrum images, which can lead to privacy concerns in Ambient Assisted Living solutions. Thermal Vision sensors are being deployed in these environments, as they preserve privacy and operate in low brightness conditions. Furthermore, multi-view setups provide the most accurate 3D pose estimation, as the occlusion problem is overcome by having images from different perspectives. Nevertheless, no solutions in the literature use thermal vision sensors following a multi-view scheme. In this work, a multi-view setup consisting of low-cost devices is deployed in the Smart Home of the University of Almería. Thermal and visible images are paired using homography , and SOTA solutions such as YOLOv3 and Blazepose are used to annotate the bounding box and 2D pose in the thermal images. ThermalYOLO is built by fine-tuning YOLOv3 and outperforms YOLOv3 by 5% in bounding box recognition and by 1% in IoU value. Furthermore, InceptionResNetV2 is found as the most appropriate architecture for 2D pose estimation. Finally, a 3D pose estimator was built comparing input approaches and convolutional architectures. Results show that the most appropriate architecture is having three single-channel thermal images processed by independent convolutional backbones (ResNet50 in this case). After these, the output is fused with the 2D poses. The resulting convolutional neural network shows excellent behaviour when having occlusions, outperforming single-view SOTA approaches in the visible spectrum.", "Keywords": "", "DOI": "10.1016/j.inffus.2023.102154", "PubYear": 2024, "Volume": "104", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Informatics, University of Almería, ceIA3, Almería, 04120, Andalucía, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "Aurora Polo-Rodríguez", "Affiliation": "Department of Computer Science, Campus Las Lagunillas, University of Jaén, Jaén, 23071, Andalucía, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Automation and Robotics, Higher Technical School of Computer Engineering and Telecommunications, University of Granada, Granada, E-18071, Andalucía, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Informatics, University of Almería, ceIA3, Almería, 04120, Andalucía, Spain"}, {"AuthorId": 5, "Name": "<PERSON>lar <PERSON>", "Affiliation": "Department of Informatics, University of Almería, ceIA3, Almería, 04120, Andalucía, Spain"}], "References": [{"Title": "A generalizable approach for multi-view 3D human pose regression", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Deep 3D human pose estimation: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "210", "Issue": "", "Page": "103225", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Recent Advances of Monocular 2D and 3D Human Pose Estimation: A Deep Learning Perspective", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111055242, "Title": "A study of behavioral decay in design patterns", "Abstract": "Design patterns represent a means of communicating reusable solutions to common problems, provided they are implemented and maintained correctly. However, many design pattern instances erode as they age, sacrificing qualities they once provided. Identifying such instances of pattern decay is valuable because it allows for proactive attempts to extend the longevity and quality attributes of pattern components. Apart from structural decay, design patterns can exhibit symptoms of behavioral decay. We utilized a taxonomy that characterizes these negative behaviors and designed a case study wherein we measured structural and behavioral decay, hereafter referred to as pattern grime, as well as pattern quality and size, across pattern evolutions. We evaluated the relationships between structural and behavioral grime and found statistically significant cases of strong correlations between specific types of structural and behavioral grime. Furthermore, we extended the QATCH operational software quality model to incorporate design pattern evolution metrics and measured and correlated software quality to the presence of behavioral grime in software systems. Our results suggest a strong inverse relationship between software quality and behavioral grime.", "Keywords": "software design;software engineering;software evolution;software maintenance;software quality", "DOI": "10.1002/smr.2638", "PubYear": 2024, "Volume": "36", "Issue": "7", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Gianforte School of Computing Montana State University  Bozeman Montana USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Gianforte School of Computing Montana State University  Bozeman Montana USA;Joint Appointment with Idaho National Laboratories Idaho National Laboratories  Idaho Falls Idaho USA"}], "References": []}, {"ArticleId": 111055243, "Title": "Responsible Use of AI by Governments", "Abstract": "<p>While governments are eager to accelerate adoption of new technologies, including AI, they must be careful not to compromise trust along the way, writes <PERSON> MBE FBCS CITP.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad112", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111055286, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0925-2312(23)01195-5", "PubYear": 2024, "Volume": "565", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [], "References": []}, {"ArticleId": 111055331, "Title": "Augmented state estimation of urban settings using on-the-fly sequential Data Assimilation", "Abstract": "A data-driven investigation of the flow around a high-rise building is performed by combining heterogeneous experimental samples and numerical models based on the Reynolds-Averaged Navier–Stokes (RANS) equations. The experimental data , which include velocity and pressure measurements obtained by local and sparse sensors, replicate realistic conditions of future automated urban settings. The coupling between experiments and the numerical model is performed using techniques based on the Ensemble Kalman Filter (EnKF), including advanced manipulations such as localization and inflation. The augmented state estimation obtained via EnKF has also been employed to improve the predictive features of the RANS model via optimization of the free global model constants of two turbulence models used to close the equations, namely the K − ɛ and the K − ω SST turbulence models. The optimized inferred values are far from the classical values prescribed as general recommendations and implemented in codes, but also different from other data-driven analyses reported in the literature. The results obtained with this new optimized parametric description show a global improvement for both the velocity and pressure fields. In addition, some topological improvements for the flow organization are observed downstream, far from the location of the sensors.", "Keywords": "", "DOI": "10.1016/j.compfluid.2023.106118", "PubYear": 2024, "Volume": "269", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Pprime, CNRS - ISAE-ENSMA - Université de Poitiers, 11 B<PERSON><PERSON> <PERSON>, Site du Futuroscope, TSA 41123, 86073 Poitiers Cedex 9, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Univ. Lille, CNRS, ONERA, Arts et Métiers ParisTech, Centrale Lille, UMR 9014- LMFL- Laboratoire de Mécanique des fluides de Lille - Kampé de Feriet, F-59000 Lille, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Luxembourg, Interdisciplinary Centre for Security, Reliability and Trust (SnT), 6 Avenue de la Fonte, Esch-sur-Alzette, 4364 Luxembourg"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Univ. Lille, CNRS, ONERA, Arts et Métiers ParisTech, Centrale Lille, UMR 9014- LMFL- Laboratoire de Mécanique des fluides de Lille - Kampé de Feriet, F-59000 Lille, France"}], "References": [{"Title": "Evaluation of ensemble methods for quantifying uncertainties in steady-state CFD applications with small ensemble sizes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "104530", "JournalTitle": "Computers & Fluids"}, {"Title": "Digital twin paradigm: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "130", "Issue": "", "Page": "103469", "JournalTitle": "Computers in Industry"}, {"Title": "An open-source coupled method for aeroacoustics modelling", "Authors": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "278", "Issue": "", "Page": "108420", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 111055336, "Title": "Feature-based domain disentanglement and randomization: A generalized framework for rail surface defect segmentation in unseen scenarios", "Abstract": "Deep neural network has demonstrated high-level accuracy in rail surface defect segmentation. However, deploying these deep models in actual inspection situations results in generalizability deficits and accuracy degradation. This phenomenon is mainly caused by the appearance difference between training and test images. To alleviate this issue, we propose a feature-based domain disentanglement and randomization (FDDR) framework to improve the generalization of deep models in unseen datasets. Specifically, two encoders are introduced to decompose the defect image into domain-invariant structural features and domain-specific style features. Only the domain invariant features are used to identify the defects. Additionally, we design a shuffle whitening module to remove the style information from the domain-invariant features. Meanwhile, the extracted style features are used to train a style variational autoencoder to randomly generate novel defect styles. Then, the randomly generated style features are combined with the domain-invariant features to obtain new defect images, thus expanding the training sample. We validate the proposed FDDR framework in six defect segmentation datasets. Extensive experimental results show that FDDR demonstrates robust defect segmentation performance in unseen scenarios and outperforms other state-of-the-art domain generalization methods. The source code will be released at https://github.com/Rail-det/FDDR .", "Keywords": "", "DOI": "10.1016/j.aei.2023.102274", "PubYear": 2024, "Volume": "59", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, Liaoning 110819, China;National Frontiers Science Center for Industrial Intelligence and Systems Optimization, Northeastern University, Shenyang 110819, China;Key Laboratory of Data Analytics and Optimization for Smart Industry (Northeastern University), Ministry of Education, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, Liaoning 110819, China;National Frontiers Science Center for Industrial Intelligence and Systems Optimization, Northeastern University, Shenyang 110819, China;Key Laboratory of Data Analytics and Optimization for Smart Industry (Northeastern University), Ministry of Education, China;Corresponding authors at: School of Mechanical Engineering & Automation, Northeastern University, Shenyang, Liaoning 110819, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Control and Electronics Technology, Beijing 100045, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, Liaoning 110819, China;National Frontiers Science Center for Industrial Intelligence and Systems Optimization, Northeastern University, Shenyang 110819, China;Key Laboratory of Data Analytics and Optimization for Smart Industry (Northeastern University), Ministry of Education, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, Liaoning 110819, China;National Frontiers Science Center for Industrial Intelligence and Systems Optimization, Northeastern University, Shenyang 110819, China;Key Laboratory of Data Analytics and Optimization for Smart Industry (Northeastern University), Ministry of Education, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, Liaoning 110819, China;National Frontiers Science Center for Industrial Intelligence and Systems Optimization, Northeastern University, Shenyang 110819, China;Key Laboratory of Data Analytics and Optimization for Smart Industry (Northeastern University), Ministry of Education, China;Corresponding authors at: School of Mechanical Engineering & Automation, Northeastern University, Shenyang, Liaoning 110819, China"}], "References": [{"Title": "Soldering defect detection in automatic optical inspection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101004", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A new Feature-Fusion method based on training dataset prototype for surface defect recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101392", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "PVT v2: Improved baselines with Pyramid Vision Transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "415", "JournalTitle": "Computational Visual Media"}, {"Title": "Surface defect detection and classification of steel using an efficient Swin Transformer", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "", "Page": "102061", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A generalized well neural network for surface defect segmentation in Optical Communication Devices via Template-Testing comparison", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "151", "Issue": "", "Page": "103978", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 111055357, "Title": "An empirical study of deep learning-based feature extractor models for imbalanced image classification", "Abstract": "<p>Deep learning has played an important role in many real-life applications, especially in image classification. It is often found that some domain data are highly skewed, i.e., most of the data belongs to a handful of majority classes, and the minority classes only contain small amounts of information. It is important to acknowledge that skewed class distribution poses a significant challenge to machine learning algorithms. Due to which in case of imbalanced data distribution, the majority of machine and deep learning algorithms are not effective or may fail when it is highly imbalanced. In this study, a comprehensive analysis in case of imbalanced dataset is performed by considering deep learning based well known models. In particular, the best feature extractor model is identified and the current trend of latest feature extraction model is investigated. Moreover, to determine the global scientific research on the image classification of imbalanced mushroom dataset, a bibliometric analysis is conducted from 1991 to 2022. In summary, our findings may offer researchers a quick benchmarking reference and alternative approach to assessing trends in imbalanced data distributions in image classification research.</p>", "Keywords": "Deep learning models; Bibliometric analysis; Features extraction; Classification accuracy", "DOI": "10.1007/s43674-023-00067-x", "PubYear": 2023, "Volume": "3", "Issue": "6", "JournalId": 88401, "JournalTitle": "Advances in Computational Intelligence", "ISSN": "2730-7794", "EISSN": "2730-7808", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Botany, University of the Punjab, Lahore, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Shenzhen university, Shenzhen, China"}], "References": [{"Title": "Deep learning fault diagnosis method based on global optimization GAN for unbalanced data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104837", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Adaptive boost LS-SVM classification approach for time-series signal classification in epileptic seizure diagnosis applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113676", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Resampling imbalanced data for network intrusion detection datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "The improved AdaBoost algorithms for imbalanced data classification", "Authors": "<PERSON><PERSON> Wang; <PERSON><PERSON>", "PubYear": 2021, "Volume": "563", "Issue": "", "Page": "358", "JournalTitle": "Information Sciences"}, {"Title": "Missing value imputation through shorter interval selection driven by Fuzzy C-Means clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "93", "Issue": "", "Page": "107230", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Interval–valued fuzzy and intuitionistic fuzzy–KNN for imbalanced data classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115510", "JournalTitle": "Expert Systems with Applications"}, {"Title": "STL-HDL: A new hybrid network intrusion detection system for imbalanced dataset on big data environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102435", "JournalTitle": "Computers & Security"}, {"Title": "Missing label imputation through inception-based semi-supervised ensemble learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "1", "JournalTitle": "Advances in Computational Intelligence"}, {"Title": "Feature reduction for imbalanced data classification using similarity-based feature clustering with adaptive weighted K-nearest neighbors", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "593", "Issue": "", "Page": "591", "JournalTitle": "Information Sciences"}, {"Title": "Handling missing data through deep convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "595", "Issue": "", "Page": "278", "JournalTitle": "Information Sciences"}, {"Title": "LSR: Lightening super-resolution deep network for low-light image enhancement", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "505", "Issue": "", "Page": "263", "JournalTitle": "Neurocomputing"}, {"Title": "An efficient fraud detection framework with credit card imbalanced data in financial services", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "3", "Page": "4139", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A study on relationship between prediction uncertainty and robustness to noisy data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "54", "Issue": "6", "Page": "1243", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": *********, "Title": "Travelling Breaking Waves", "Abstract": "We study a mathematical model of coastal waves in the shallow water approximation. The model contains two empirical parameters. The first one controls turbulent dissipation. The second one is responsible for the turbulent viscosity and is determined by the turbulent Reynolds number. We study travelling waves solutions to this model. The existence of an analytical and numerical solution to the problem in the form of a traveling wave is shown. The singular points of the system are described. It is shown that there exists a critical value of the <PERSON>ln<PERSON> number corresponding to the transition from a monotonic profile to an oscillatory one. The paper is organized as follows. First, we present the governing system of ordinary differential equations (ODE) for travelling waves. Second, the L<PERSON><PERSON>nov function for the corresponding ODE system is derived. Finally, the behavior of the solution to the ODE system is discussed. © 2023 South Ural State University. All rights reserved.", "Keywords": "<PERSON><PERSON><PERSON>nov function; Reynolds number; shallow-water equation; travelling wave solution", "DOI": "10.14529/mmp230205", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "RETRACTED ARTICLE: The design of network security protection trust management system based on an improved hidden Markov model", "Abstract": "With the growth of the Internet, network security issues have become increasingly complex, and the importance of node interaction security is also gradually becoming prominent. At present, research on network security protection mainly starts from the overall perspective, and some studies also start from the interaction between nodes. However, the trust management mechanisms in these studies do not have a predictive function. Therefore, to predict trust levels and protect network security, this paper innovatively proposes a trust management system for network security protection based on the improved hidden Markov model. The research divides the trust level of inter-node interactions by calculating the threat level of inter-node interactions and predicts the trust level of inter-node interactions through an optimized hidden Markov model. In addition, the study designs an estimation of the types of interactive threats between nodes based on alarm data. The research results show that when inactive interaction tuples are not excluded, the average prediction accuracy of the combined model is 95.5%. In response time, the maximum values of the active and passive cluster management pages are 38 ms and 33 ms, respectively, while the minimum values are 16 ms and 14 ms, with an average of 26.2 ms and 24 ms, respectively. The trust management system designed by the research institute has good performance and can provide systematic support for network security protection, which has good practical significance.", "Keywords": "Signal;Image and Speech Processing;Systems and Data Security;Communications Engineering; Networks;Security Science and Technology", "DOI": "10.1186/s13635-023-00146-z", "PubYear": 2023, "Volume": "2023", "Issue": "1", "JournalId": 43752, "JournalTitle": "EURASIP Journal on Information Security", "ISSN": "", "EISSN": "2510-523X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Xijing University, Xi’an, China"}], "References": [{"Title": "A two-way trust management system for fog computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "206", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Hybrid intrusion detection system using machine learning", "Authors": "<PERSON>; Bouabid EL Ouahidi", "PubYear": 2020, "Volume": "2020", "Issue": "5", "Page": "8", "JournalTitle": "Network Security"}, {"Title": "Secure blockchain enabled Cyber–physical systems in healthcare using deep belief network with ResNet model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "150", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Adaptive and survivable trust management for Internet of Things systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "5", "Page": "375", "JournalTitle": "IET Information Security"}, {"Title": "Text segmentation for patent claim simplification via Bidirectional Long‐Short Term Memory and Conditional Random Field", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "205", "JournalTitle": "Computational Intelligence"}]}, {"ArticleId": 111055636, "Title": "Unconditionally Stable and Convergent Difference Scheme for Superdiffusion with Extrapolation", "Abstract": "Approximating the Hadamard finite-part integral by the quadratic interpolation polynomials, we obtain a scheme for approximating the Riemann-Liouville fractional derivative of order $$\\alpha \\in (1, 2)$$ \n \n α \n ∈ \n ( \n 1 \n , \n 2 \n ) \n \n and the error is shown to have the asymptotic expansion $$ \\big ( d_{3} \\tau ^{3- \\alpha } + d_{4} \\tau ^{4-\\alpha } + d_{5} \\tau ^{5-\\alpha } + \\cdots \\big ) + \\big ( d_{2}^{*} \\tau ^{4} + d_{3}^{*} \\tau ^{6} + d_{4}^{*} \\tau ^{8} + \\cdots \\big ) $$ \n \n \n ( \n \n \n d \n 3 \n \n \n τ \n \n 3 \n - \n α \n \n \n + \n \n d \n 4 \n \n \n τ \n \n 4 \n - \n α \n \n \n + \n \n d \n 5 \n \n \n τ \n \n 5 \n - \n α \n \n \n + \n ⋯ \n \n ) \n \n + \n \n ( \n \n \n d \n \n 2 \n \n \n \n ∗ \n \n \n \n τ \n 4 \n \n + \n \n d \n \n 3 \n \n \n \n ∗ \n \n \n \n τ \n 6 \n \n + \n \n d \n \n 4 \n \n \n \n ∗ \n \n \n \n τ \n 8 \n \n + \n ⋯ \n \n ) \n \n \n at any fixed time, where $$\\tau $$ \n τ \n denotes the step size and $$d_{l}, l=3, 4, \\dots $$ \n \n \n d \n l \n \n , \n l \n = \n 3 \n , \n 4 \n , \n ⋯ \n \n and $$d_{l}^{*}, l\\,=\\,2, 3, \\dots $$ \n \n \n d \n \n l \n \n \n \n ∗ \n \n \n , \n l \n \n = \n \n 2 \n , \n 3 \n , \n ⋯ \n \n are some suitable constants. Applying the proposed scheme in temporal direction and the central difference scheme in spatial direction, a new finite difference method is developed for approximating the time fractional wave equation. The proposed method is unconditionally stable, convergent with order $$O (\\tau ^{3- \\alpha }), \\alpha \\in (1, 2)$$ \n \n O \n \n ( \n \n τ \n \n 3 \n - \n α \n \n \n ) \n \n , \n α \n ∈ \n \n ( \n 1 \n , \n 2 \n ) \n \n \n and the error has the asymptotic expansion. Richardson extrapolation is applied to improve the accuracy of the numerical method. The convergence orders are $$O ( \\tau ^{4- \\alpha })$$ \n \n O \n ( \n \n τ \n \n 4 \n - \n α \n \n \n ) \n \n and $$O ( \\tau ^{2(3- \\alpha )}), \\alpha \\in (1, 2)$$ \n \n O \n \n ( \n \n τ \n \n 2 \n ( \n 3 \n - \n α \n ) \n \n \n ) \n \n , \n α \n ∈ \n \n ( \n 1 \n , \n 2 \n ) \n \n \n , respectively, after first two extrapolations. Numerical examples are presented to show that the numerical results are consistent with the theoretical findings.", "Keywords": "Time fractional wave equation; Higher order scheme; Stability; Error estimates; Asymptotic expansion; Extrapolation; 65M15; 65M60; 65M12; 45K05", "DOI": "10.1007/s10915-023-02395-z", "PubYear": 2024, "Volume": "98", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, LvLiang University, Lishi, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Physical, Mathematical and Engineering, University of Chester, Chester, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, BITS-Pilani, KK Birla Goa Campus, Zuarinagar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physical, Mathematical and Engineering, University of Chester, Chester, UK"}], "References": [{"Title": "An H2N2 Interpolation for Caputo Derivative with Order in (1, 2) and Its Application to Time-Fractional Wave Equations in More Than One Space Dimension", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "83", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 111055743, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0378-7206(23)00142-8", "PubYear": 2023, "Volume": "60", "Issue": "8", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [], "References": []}, {"ArticleId": 111055761, "Title": "Three-Dimensional Visualization of the Multiphase Submerged Jet Flow Model", "Abstract": "During production of oil products on the deep sea shelf, there is a danger of hydrocarbons being released into the water column in the form of a multiphase jet. The peculiarity of multiphase submerged jet flow determines the rate of water body pollution by oil and depends on the thermal and physical characteristics of the spill and the environment, including the nature of the underwater current. The conducted research has revealed the need for visualization of multiphase submerged jets on the basis of mathematical modeling data. The article considers the possibilities of multiphase submerged jet visualization in the 3D modeling tool Blender3D, the platform for the development of additional functionality, the purpose and functionality of the extension. Visual representation of the jet trajectory interprets temperature and concentration changes in color format. The program allows to display multiphase submerged jet in two modes: boundary and volumetric. The developed program gives an opportunity to expand the representations of the spill according to the initial and boundary conditions, which will reduce the time of its elimination in the long term. © 2023 South Ural State University. All rights reserved.", "Keywords": "", "DOI": "10.14529/mmp230106", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111055780, "Title": "Data- and management-driven metaverse research", "Abstract": "The metaverse has become a very important phenomenon in society because of the emergence of new technologies. The widespread adoption of the metaverse has generated significant discussions about the challenges and opportunities it presents. We invited three panelists to present their personal viewpoints on the metaverse in the 2022 AIS-SIG-ISAP Workshop on Information Systems in Asia-Pacific (ISAP). The discussion indicated that metaverse research is being conducted. Furthermore, it highlighted new research directions and offered research topics related to the advantages or disadvantages of the metaverse. The proposed research topics will offer new insights to academics and practitioners.", "Keywords": "Metaverse ; Virtual reality ; Management application ; Research topic", "DOI": "10.1016/j.dsm.2023.11.003", "PubYear": 2024, "Volume": "7", "Issue": "2", "JournalId": 84310, "JournalTitle": "Journal of Information Technology and Data Management", "ISSN": "2666-7649", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Zhigeng Pan", "Affiliation": "School of Artificial Intelligence, Nanjing University of Information Science & Technology, Nanjing, 210044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Management, Nanjing University, Nanjing, 210023, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, University of Southern Maine, Portland, 04104, USA;Maine Business School, University of Maine, Orono, 04469, USA;Système D'information et Organisationnels, Université Laval, Canada"}, {"AuthorId": 4, "Name": "Haibing Lu", "Affiliation": "Leavey School of Business, Santa Clara University, Santa Clara, 95053, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Management, Xi'an Jiaotong University, Xi'an, 710049, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Southern University of Science and Technology, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Pusan National University, Busan, 46241, South Korea;Corresponding author"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Information and Decision Sciences, University of Illinois Chicago, Chicago, 60607, USA"}], "References": [{"Title": "Blockchain and its derived technologies shape the future generation of digital businesses: a focus on decentralized finance and the Metaverse", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "3", "Page": "183", "JournalTitle": "Journal of Information Technology and Data Management"}, {"Title": "The impact of interaction on continuous use in online learning platforms: a metaverse perspective", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "34", "Issue": "1", "Page": "79", "JournalTitle": "Internet Research"}, {"Title": "Research on influencing factors and governance of disinformation dissemination on science and technology topics: an empirical study on the topic of “metaverse”", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "5", "Page": "1802", "JournalTitle": "Internet Research"}, {"Title": "A review of the literature on the metaverse: definition, technologies, and user behaviors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "34", "Issue": "1", "Page": "129", "JournalTitle": "Internet Research"}]}, {"ArticleId": 111055805, "Title": "Data-driven characterization of viscoelastic materials using time-harmonic hydroacoustic measurements", "Abstract": "Any numerical procedure in mechanics requires choosing an appropriate model for the constitutive law of the material under consideration. The most common assumptions regarding linear wave propagation in a viscoelastic material are the standard linear solid model, (generalized) Maxwell, Kelvin-Voigt models or the most recent fractional derivative models. Usually, once the frequency-dependent constitutive law is fixed, the intrinsic parameters of the mathematical model are estimated to fit the available experimental data with the mechanical response of that model. This modelling methodology potentially suffers from the epistemic uncertainty of an inadequate a priori model selection. However, in this work, the mathematical modelling of linear viscoelastic materials and the choice of their frequency-dependent constitutive laws is performed based only on the available experimental measurements without imposing any functional frequency dependence. This data-driven approach requires the numerical solution of an inverse problem for each frequency. The acoustic response of a viscoelastic material due to the time-harmonic excitations has been calculated numerically. In these numerical simulations, the non-planar directivity pattern of the transducer has been taken into account. Experimental measurements of insertion loss and fractional power dissipation in underwater acoustics have been used to illustrate the data-driven methodology that avoids selecting a parametric viscoelastic model.", "Keywords": "Data-driven material characterization ; Viscoelastic materials ; Hydroacoustics ; Young's modulus", "DOI": "10.1016/j.compstruc.2023.107229", "PubYear": 2024, "Volume": "292", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratory of Applied Mathematics, DICAM, University of Trento, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CITMAga, Departamento de Matemáticas, Universidade da Coruña, Spain;Corresponding author"}], "References": []}, {"ArticleId": *********, "Title": "An ensemble of CNNs with self-attention mechanism for DeepFake video detection", "Abstract": "The availability of large-scale facial datasets with the rapid progress of deep learning techniques, such as Generative Adversarial Networks, has enabled anyone to create realistic fake videos. These fake videos can potentially become harmful when used for fake news, hoaxes, and identity fraud. We propose a deep learning bagging ensemble classifier to detect manipulated faces in videos. The proposed bagging classifier uses the convolution and self-attention network (CoAtNet) model as a base learner. CoAtNet model is vertically stacking depthwise convolution layers and self-attention layers in such a way that generalization, capacity, and efficiency are improved. Depthwise convolution captures local features from faces extracted from video then pass these features to the attention layers to extract global information and efficiently capture long-range dependencies of spatial details. Each learner is trained on a different subset randomly taken of training data with a replacement then models’ predictions are combined to classify the video either as real or fake. We also use CutMix data augmentation on the extracted faces to enhance the generalization and localization performance of the base learner model. Our experimental results show that our proposed method achieves higher efficiency compared to state-of-the-art methods with AUC values of 99.70%, 97.49%, 98.90%, and 87.62% on the different manipulation techniques of the FaceForensics++ dataset (DeepFakes (DF), Face2Face (F2F), FaceSwap (FS), and NeuralTextures (NT)), respectively, and 99.74% on the Celeb-DF dataset.", "Keywords": "Generative Adversarial Networks; Bagging Ensemble; Self-Attention; CutMix; DeepFake Detection", "DOI": "10.1007/s00521-023-09196-3", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computer and Information Sciences, Mansoura University, Mansoura, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computer and Information Sciences, Mansoura University, Mansoura, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computer and Information Sciences, Mansoura University, Mansoura, Egypt"}], "References": [{"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "DeepFake detection algorithm based on improved vision transformer", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "7", "Page": "7512", "JournalTitle": "Applied Intelligence"}, {"Title": "DFGNN: An interpretable and generalized graph neural network for deepfakes detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "222", "Issue": "", "Page": "119843", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111055854, "Title": "Qudwa: STEM e-mentoring for young women in Saudi Arabia", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2023.2283196", "PubYear": 2024, "Volume": "43", "Issue": "11", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, College of Applied Studies and Community Services, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Open Lab, Newcastle University, Newcastle Upon Tyne, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, College of Applied Studies and Community Services, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Open Lab, Newcastle University, Newcastle Upon Tyne, UK"}], "References": []}, {"ArticleId": 111055937, "Title": "Challenges in starting to design and make together: Examining family engagement in Fab Labs", "Abstract": "Making in a digital fabrication laboratory (Fab Lab) using Fab Lab processes is generally challenging for novice makers, particularly for families with young children. We conducted a qualitative study on family engagement in open-ended digital fabrication activities in an informal Fab Lab setting, where families engaged in 2D/3D designing, laser cutting, and 3D printing. We observed challenging transformations when families moved from brainstorming to digital designing and, subsequently, from digital designing to machine-making a physical object. Fab Lab instructors’ work, parental approaches, and oral discussions, which were all intended to facilitate children&#x27;s participation in the activities, were central in and intertwined with these transformations, and led to the engagement or disengagement of children. Through this study, we shed light on the challenges families faced and provide insights into how to reduce the complexity of digital fabrication for families who have no prior experience of it as well as into how Fab Lab instructors can help enable young children&#x27;s participation in the related activities.", "Keywords": "Children ; Families ; Intergenerational design ; Intergenerational making ; Child-parent interaction ; Digital fabrication ; Fab Lab ; Makerspace ; Fab Lab instructors", "DOI": "10.1016/j.ijhcs.2023.103185", "PubYear": 2024, "Volume": "183", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INTERACT Research Unit, University of Oulu, Finland;Fab Lab Oulu, University of Oulu, Finland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "INTERACT Research Unit, University of Oulu, Finland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INTERACT Research Unit, University of Oulu, Finland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Fab Lab Oulu, University of Oulu, Finland;Center for Ubiquitous Computing, University of Oulu, Finland"}], "References": [{"Title": "Relating to materials in digital fabrication: Transform materials to transform yourself", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23-24", "Issue": "", "Page": "100166", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "STEAM in Oulu: Scaffolding the development of a Community of Practice for local educators around STEAM and digital fabrication", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "", "Page": "100197", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "Gender in the making: An empirical approach to understand gender relations in the maker movement", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "102548", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Exploring technology-oriented Fab Lab facilitators’ role as educators in K-12 education: Focus on scaffolding novice students’ learning in digital fabrication activities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "", "Page": "100207", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "Manifesto for children’s genuine participation in digital technology design and making", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "", "Page": "100244", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "Crafting, connecting, and commoning in everyday maker projects", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "156", "Issue": "", "Page": "102715", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Qi2He: A co-design framework inspired by eastern epistemology", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "160", "Issue": "", "Page": "102773", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 111055957, "Title": "Singularity structure simplification for hex mesh via integer linear program", "Abstract": "Topology optimization of hexahedral (hex) meshes has been a widely studied topic, with the primary goal of optimizing the singularity structure. Previous works have focused on simplifying complex singularity structures by collapsing sheets/chords. However, these works require a large number of checks during the process to prevent illegal operations. Moreover, the employed simplification strategies are not based on the topological characteristics of the structure, but rather on the rank of the components that can be simplified. To overcome these problems, we analyze how topology operations affect the degree of edges in hex meshes, and introduce a fast and automatic algorithm to simplify the singularity structure of hex meshes. The algorithm relies on sheet operations, using mesh volume as a metric to assess the degree of simplification. Moreover, it designs constraints to prevent illegal operations and employs integer linear program to plan the overall optimization strategy for a mesh. After that, we relax the singularity constraints to further simplify the structure, and handle unreasonable singularities via sheet inflation operation. Our algorithm can also improve singularity structure without merging singularities by adjusting the singularity constraint conditions. Numerous experiments demonstrate the effectiveness and efficiency of our algorithm.", "Keywords": "", "DOI": "10.1016/j.cad.2023.103654", "PubYear": 2024, "Volume": "168", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dalian University of Technology, School of Software Technology, Dalian, 116024, Liaoning, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian University of Technology, School of Software Technology, Dalian, 116024, Liaoning, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dalian University of Technology, International School of Information Science&Engineering, Dalian, 116024, Liaoning, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dalian University of Technology, School of Software Technology, Dalian, 116024, Liaoning, China"}], "References": [{"Title": "Topological operations for editing the singularity on a hex mesh", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1357", "JournalTitle": "Engineering with Computers"}, {"Title": "LoopyCuts", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Volume parametrization quantization for hexahedral meshing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Local Decomposition of Hexahedral Singular Nodes into Singular Curves", "Authors": "<PERSON>; <PERSON> (<PERSON><PERSON><PERSON><PERSON>) <PERSON>; <PERSON><PERSON><PERSON> (<PERSON>) Fan", "PubYear": 2023, "Volume": "158", "Issue": "", "Page": "103484", "JournalTitle": "Computer-Aided Design"}]}, {"ArticleId": 111055962, "Title": "Unified identity authentication scheme of system wide information management based on SAML-PKI-LDAP", "Abstract": "System wide information management (SWIM) is a platform to share and exchange information on the new air traffic management (ATM) services between different departments and systems in the civil aviation field. Through the connection of SWIM and various application services, a virtual information pool is formed to solve the interconnection issues of different systems. To ensure data security in the system and quick authentication of legitimate users, we propose a unified identity authentication scheme for SWIM. This scheme improves the security assertion markup language (SAML) cross-domain authentication model and integrates it with the public key infrastructure (PKI) authentication system and lightweight directory access protocol (LDAP). Experimental results show that this scheme realises the functions of user management, identity authentication, and cross-domain access, which can meet requirements of the SWIM gateway. © 2023 Inderscience Enterprises Ltd.", "Keywords": "digital certificate; directory access protocol; identity authentication; SAML; security assertion markup language; SWIM; system wide information management", "DOI": "10.1504/IJICS.2023.134959", "PubYear": 2023, "Volume": "22", "Issue": "2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Safety Science and Engineering, Civil Aviation University of China, Tianjin, 300300, China"}, {"AuthorId": 2, "Name": "Zhuoning Bai", "Affiliation": "School of Safety Science and Engineering, Civil Aviation University of China, Tianjin, 300300, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Safety Science and Engineering, Civil Aviation University of China, Tianjin, 300300, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Safety Science and Engineering, Civil Aviation University of China, Tianjin, 300300, China"}], "References": []}, {"ArticleId": 111055986, "Title": "Development of a Machine Learning Algorithm for the Searches of the New Bc+ Meson Decays with Charmonium and Multihadron Final States", "Abstract": "The paper describes the process of implementation of machine learning algorithm for the classification of the events in high energy physics. The results of testing a classifier based on gradient boosted decision tree to improve the selection efficiency of the rare Bc+ meson decays with charmonium and multihadron final states are presented. The development of the algorithm is performed using a toolkit for multivariate data analysis. The training of the classifier is based on the simulated data and experimental data, collected by the LHCb detector at the Large Hadron Collider in the period from 2011 to 2018. © 2023 South Ural State University. All rights reserved.", "Keywords": "beauty hadrons; charmonium; data analysis; decision tree; machine learning; multivariate analysis", "DOI": "10.14529/mmp230109", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111056015, "Title": "An extensible schema for capturing environmental model metadata: Implementation in the HydroShare online data repository", "Abstract": "Model metadata can be used to document the specifications, inputs, provenance, and other important attributes of an environmental modeling application. An open challenge in creating a general model metadata schema is addressing the diversity of environmental models in a way that provides flexibility and scalability while maintaining a standard core. This paper presents a new model metadata schema design aimed at addressing this challenge. Specifically, we present an extensible metadata schema for describing what we call a model instance: a specific application of an environmental model. We implement the schema in the HydroShare online data repository and demonstrate how the schema can be used for the Soil and Water Assessment (SWAT) model.", "Keywords": "", "DOI": "10.1016/j.envsoft.2023.105895", "PubYear": 2024, "Volume": "172", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Virginia, Charlottesville, VA, USA;Department of Civil and Construction Engineering, Brigham Young University, Provo, UT, USA;Corresponding author. University of Virginia, Department of Civil and Environmental Engineering, University of Virginia, 151 Engineers Way, P.O. Box 400747, Charlottesville, VA, 22904, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Irrigation and Hydraulics Engineering Department, Cairo University, Giza, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Biosystems & Agricultural Engineering, Oklahoma State University, Stillwater, OK, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Utah Water Research Laboratory, Utah State University, Logan, UT, USA"}, {"AuthorId": 5, "Name": "Pabitra K. Dash", "Affiliation": "Department of Civil and Environmental Engineering, Utah Water Research Laboratory, Utah State University, Logan, UT, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "AI Research Laboratory, R&D Management Department, K-water Research Institute, South Korea"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Virginia, Charlottesville, VA, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Consortium of Universities for the Advancement of Hydrologic Science, Inc, Arlington, MA, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Consortium of Universities for the Advancement of Hydrologic Science, Inc, Arlington, MA, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Utah Water Research Laboratory, Utah State University, Logan, UT, USA"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Virginia, Charlottesville, VA, USA"}], "References": [{"Title": "A model metadata schema for environmental hazard models and its implementation in the PURE portal", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "124", "Issue": "", "Page": "104597", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "A taxonomy for reproducible and replicable research in environmental modelling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "104753", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Toward open and reproducible environmental modeling by integrating online data repositories, computational environments, and model Application Programming Interfaces", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "135", "Issue": "", "Page": "104888", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "The role of metadata in reproducible computational research", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "9", "Page": "100322", "JournalTitle": "Patterns"}, {"Title": "Building cyberinfrastructure for the reuse and reproducibility of complex hydrologic modeling studies", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "164", "Issue": "", "Page": "105689", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Comparing containerization-based approaches for reproducible computational modeling of environmental systems", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "167", "Issue": "", "Page": "105760", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 111056016, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0743-7315(23)00177-6", "PubYear": 2024, "Volume": "184", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [], "References": []}, {"ArticleId": 111056030, "Title": "On the Qualitative Analysis of a Family of Differential Equations with First Integrals of Degree More Then 2", "Abstract": "We study a family of differential equations that arose as a result of a generalization of the classical integrable cases in rigid body dynamics. The system under study admits polynomial first integrals of the 4th and 6th degrees. Under appropriate constraints on the parameters of the family, the differential equations are interpreted as those of motion of a rigid body in a central force field and an ideal fluid, as well as the equations of motion of an electrically charged body. The qualitative analysis of the equations is done. We find special invariant sets of various dimensions and investigate their Lyapunov stability. For the analysis of the problem, generalizations of the Routh-L<PERSON><PERSON>nov method and software tools of computer algebra are applied. © 2023 South Ural State University. All rights reserved.", "Keywords": "computer algebra; first integrals; invariant sets; rigid body; stability; the equations of motion", "DOI": "10.14529/mmp230204", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111056037, "Title": "Fractional-order modified heterogeneous comprehensive learning particle swarm optimizer for intelligent disease detection in IoMT environment", "Abstract": "This paper presents an alternative early disease detection technique based on the Internet of Medical Things (IoMT) to improve the healthcare system. Since it is necessary to address different aspects to help healthcare organizations to improve their efficiency. Early disease detection using a fast and efficient test, such as chest X-ray images, is one of the most important issues. In this paper, we develop an efficient model to allocate abnormalities in medical chest X-ray images. The developed model consists of a set of stages; the first stage is to segment the images using a multilevel thresholding technique to determine the lung inside the image, then extracting the features from the segmented objects using different extractor methods. Then, we proposed a Fractional-order modified Heterogeneous comprehensive learning particle swarm optimizer (FMHCLPSO) as a feature selection method to determine the relevant features used to improve the detection process. To evaluate the performance of the developed IoMT model, a set of three medical datasets is used, called Covid-19 &amp; Pneumonia, X-raycovid, and Radiography. The results illustrate the high efficiency of the developed model to detect diseases based on performance measures. Furthermore, We compared the proposed method to several existing methods, and it showed significant performance.", "Keywords": "Internet of Medical Things (IoMT) ; Feature selection ; Fractional order ; Heterogeneous comprehensive learning particle swarm optimize", "DOI": "10.1016/j.swevo.2023.101430", "PubYear": 2024, "Volume": "84", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Zagazig University, Zagazig, Egypt;College of Information Technology, United Arab Emirates University, P.O. Box: 15551, Al Ain, United Arab Emirates;MEU Research Unit, Middle East University, Amman 11831, Jordan;Department of Electrical and Computer Engineering, Lebanese American University, Byblos 13-5053, Lebanon"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Power and Energy Center, EECS Department, Khalifa University, Abu Dhabi, United Arab Emirates;Department of Electrical Engineering, Faculty of Engineering, Fayoum University, Fayoum, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, College of Computer Engineering and Sciences, Prince <PERSON> Abdulaziz University, Al-Kharj 11942, Saudi Arabia;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Al al-Bayt University, Mafraq 25113, Jordan;Hourani Center for Applied Scientific Research, Al-Ahliyya Amman University, Amman 19328, Jordan;MEU Research Unit, Middle East University, Amman 11831, Jordan;Department of Electrical and Computer Engineering, Lebanese American University, Byblos 13-5053, Lebanon;School of Engineering and Technology, Sunway University Malaysia, Petaling Jaya 27500, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Physics and Electronic Information Engineering, Zhejiang Normal University, Jinhua 321004, China;Zhejiang Optoelectronics Research Institute, Jinhua, China, Jinhua 321004, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer, Damietta University, Damietta 34517, Egypt"}], "References": [{"Title": "Memetic improved cuckoo search algorithm for automatic B-spline border approximation of cutaneous melanoma from macroscopic medical images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101005", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Gaining-sharing knowledge based algorithm for solving optimization problems: a novel nature-inspired algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "7", "Page": "1501", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A deep learning based medical image segmentation technique in Internet-of-Medical-Things domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "135", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An efficient henry gas solubility optimization for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113364", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Slime mould algorithm: A new method for stochastic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "300", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Fractional-order cuckoo search algorithm for parameter identification of the fractional-order chaotic, chaotic with noise and hyper-chaotic financial systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103662", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Detection and diagnosis of pancreatic tumor using deep learning-based hierarchical convolutional neural network on the internet of medical things platform", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "132", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Heterogeneous comprehensive learning and dynamic multi-swarm particle swarm optimizer with two mutation operators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "175", "JournalTitle": "Information Sciences"}, {"Title": "A novel binary gaining–sharing knowledge-based optimization algorithm for feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "5989", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A Tutorial On the design, experimentation and application of metaheuristic algorithms to real-World optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "", "Page": "100888", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Chaotic gaining sharing knowledge-based optimization algorithm: an improved metaheuristic algorithm for feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "14", "Page": "9505", "JournalTitle": "Soft Computing"}, {"Title": "Performance assessment and exhaustive listing of 500+ nature-inspired metaheuristic algorithms", "Authors": "Zhongqiang Ma; Guohua Wu; Ponnuthurai Nagaratnam Suganthan", "PubYear": 2023, "Volume": "77", "Issue": "", "Page": "101248", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "ABOA-CNN: auction-based optimization algorithm with convolutional neural network for pulmonary disease prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "10", "Page": "7463", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 111056114, "Title": "Differential, Linear, and Meet-in-the-Middle Attacks on the Lightweight Block Cipher RBFK", "Abstract": "Randomized butterfly architecture of fast Fourier transform for key cipher (RBFK) is the lightweight block cipher for Internet of things devices in an edge computing environment. Although the authors claimed that RBFK is secure against differential cryptanalysis, linear cryptanalysis, impossible differential attack, and zero correlation linear cryptanalysis, the details were not explained in the literature. Therefore, we have evaluated the security of RBFK by application of differential cryptanalysis, linear cryptanalysis, and meet-in-the-middle (MITM) attack and have found that RBFK is not secure against these attacks. This paper introduces not only a distinguish attack but also key recovery attacks on full-round RBFK. In the distinguish attack scenario, data for differential cryptanalysis are two, and the time complexity is one for an exclusive-OR operation. In the key recovery attack scenario, the data for linear cryptanalysis are one pair of known plaintext–ciphertext. The time complexity is one operation for a linear sum. Data for an MITM attack are two. The time complexity is \n \n \n 2 \n 48 \n \n \n encryptions; the memory complexity is \n \n \n 2 \n 45 \n \n \n bytes. Because the vulnerabilities are identified in the round function and the key scheduling part, we propose some improvements for RBFK against these attacks.", "Keywords": "", "DOI": "10.1049/2023/6691726", "PubYear": 2023, "Volume": "2023", "Issue": "1", "JournalId": 17160, "JournalTitle": "IET Information Security", "ISSN": "1751-8709", "EISSN": "1751-8717", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hokkaido University of Science, 15-4-1, <PERSON><PERSON> 7-j<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hokkaido 006-8585, Japan"}], "References": [{"Title": "LBC-IoT: Lightweight Block Cipher for IoT Constraint Devices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "3563", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Towards Key-recovery-attack Friendly Distinguishers: Application to GIFT-128", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "156", "JournalTitle": "IACR Transactions on Symmetric Cryptology"}, {"Title": "A survey on software implementation of lightweight block ciphers for IoT devices", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "1801", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "SCENERY: a lightweight block cipher based on Feistel structure", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "3", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "New differential cryptanalysis results for the lightweight block cipher BORON", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "66", "Issue": "", "Page": "103129", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Differential cryptanalysis of WARP", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "", "Page": "103316", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "RBFK cipher: a randomized butterfly architecture-based lightweight block cipher for IoT devices in the edge computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}, {"Title": "Systematic literature review: Trend analysis on the design of lightweight block cipher", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "5", "Page": "101550", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Bit‐level evaluation of piccolo block cipher by satisfiability problem solver", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "4", "Page": "616", "JournalTitle": "IET Information Security"}]}, {"ArticleId": 111056173, "Title": "Interval forecasting for wind speed using a combination model based on multiobjective artificial hummingbird algorithm", "Abstract": "Short-term wind speed prediction is critical for enhancing the efficiency of wind power systems and assuring the stability and continuity of power generation and the host electricity markets. Various methods are available to improve the performance of wind speed prediction. However, these methods use traditional point forecasting and neglect the limitations of individual models, which cannot handle uncertainty in system operation. We propose a combined interval forecasting method that combines multiobjective artificial hummingbird algorithm, interval forecasting, and individual forecasting methods. As our proposal integrates various forecasting models including autoregressive integrated moving average, bidirectional long short-term memory, long short-term memory, and gated recurrent unit, it overcomes the limitations of single models and enhances the prediction accuracy. Experimental results show that the forecasting performance of the proposed combined interval forecasting model is considerably higher than that of similar models.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111090", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Peiqi Sun", "Affiliation": "School of Statistics, Southwestern University of Finance and Economics, Chengdu, Sichuan Province 611130, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Nanjing University of Posts and Telecommunications, Nanjing, Jiangsu Province 210023, China;Corresponding author"}, {"AuthorId": 3, "Name": "Jianzhou Wang", "Affiliation": "Institute of Systems Engineering, Macau University of Science and Technology, Taipa Street, Macau Special Administrative Region of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China;Center for Energy and Environmental Policy Research, Beijing Institute of Technology, Beijing 100081, China"}], "References": [{"Title": "A novel hybrid model based on multi-objective Harris hawks optimization algorithm for daily PM2.5 and PM10 forecasting", "Authors": "<PERSON><PERSON>; Ji<PERSON><PERSON> Wang; <PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106620", "JournalTitle": "Applied Soft Computing"}, {"Title": "Ensemble forecasting system for short-term wind speed forecasting based on optimal sub-model selection and multi-objective version of mayfly optimization algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; Jianzhou Wang", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114974", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Carbon price forecasting system based on error correction and divide-conquer strategies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "", "Page": "107935", "JournalTitle": "Applied Soft Computing"}, {"Title": "Tourism demand forecasting and tourists’ search behavior: evidence from segmented Baidu search volume", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "1", "JournalTitle": "Journal of Information Technology and Data Management"}, {"Title": "LSTM with particle Swam optimization for sales forecasting", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101118", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Wind speed forecasting based on model selection, fuzzy cluster, and multi-objective algorithm and wind energy simulation by <PERSON><PERSON>'s theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116509", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Advanced traffic congestion early warning system based on traffic flow forecasting and extenics evaluation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "", "Page": "108544", "JournalTitle": "Applied Soft Computing"}, {"Title": "New developments in wind energy forecasting with artificial intelligence and big data: a scientometric insight", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "2", "Page": "84", "JournalTitle": "Journal of Information Technology and Data Management"}, {"Title": "Hybrid model for profit-driven churn prediction based on cost minimization and return maximization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "228", "Issue": "", "Page": "120354", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A wind speed forecasting system for the construction of a smart grid with two-stage data processing based on improved ELM and deep learning strategies", "Authors": "<PERSON><PERSON><PERSON> Wang; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "122487", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111056206, "Title": "Quality effort strategies of video service supply chain considering fans preference and data-driven marketing under derived demand", "Abstract": "In the era of “Internet +”, video service as a new cultural industry model has attracted a multitude of attention. Especially under the influence of COVID-19, the importance of video service in people’s daily life becomes more prominent. In this paper, a video service supply chain (VSSC) composed of a video service platform and a video service supplier is researched. The video service supplier provides basic service and two-stages derived service to users through the video service platform. By analyzing the platform pricing scenario and supplier pricing scenario considering data-driven marketing (DDM), four models are established: No-DDM + platform pricing scenario, No-DDM + supplier pricing scenario, DDM + platform pricing scenario, and DDM + supplier pricing scenario. By using game theory, the optimal strategies, and optimal profits of VSSC members under four scenarios are obtained. Finally, the impact of basic market size, fans preference and data-driven marketing efficiency on the optimal strategies, optimal profits, profit growth rates, and model choice   of VSSC members are discussed, and relevant conclusions are drawn to give management enlightenment.", "Keywords": "", "DOI": "10.1016/j.elerap.2023.101338", "PubYear": 2023, "Volume": "62", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Economics Faculty, Liaoning University, Shenyang 110036, China;China Research Institute of Business Reform and Development, Shenyang 110036, China"}, {"AuthorId": 2, "Name": "Guanyu Jiang", "Affiliation": "Business School, Economics Faculty, Liaoning University, Shenyang 110036, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Economics Faculty, Liaoning University, Shenyang 110036, China;School of Business Administration, Northeastern University, Shenyang 110169, China;Corresponding author at: Business School, Economics Faculty, Liaoning University, Shenyang 110036, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Business School, Economics Faculty, Liaoning University, Shenyang 110036, China"}], "References": [{"Title": "Operation strategies for an omni-channel supply chain: Who is better off taking on the online channel and offline service?", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "", "Page": "100918", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Dynamic game strategies of a two-stage remanufacturing closed-loop supply chain considering Big Data marketing, technological innovation and overconfidence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "106538", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Cooperative strategy for a dual-channel supply chain with the influence of free-riding customers", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101001", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Not all qualities are equal: Moderating role of online shopper conscientiousness in quality evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101056", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Dynamic game analysis on pricing and service strategy in a retailer-led supply chain with risk attitudes and free-ride effect", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "3", "Page": "1199", "JournalTitle": "Kybernetes"}, {"Title": "The complementarity of a diverse range of deep learning features extracted from video content for video recommendation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116335", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improving the reliability of the information disclosure in supply chain based on blockchain technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101121", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Optimal financing strategies of a dual-channel closed-loop supply chain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101140", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Management of social selling and B2B customer-brand engagement: Is direct selling on social media good for your brand and relationships?", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101167", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Fuzzy closed-loop supply chain models with quality and marketing effort-dependent demand", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "118081", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Service quality evaluation and service improvement using online reviews: A framework combining deep learning with a hierarchical service quality model", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101174", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Coupon strategies for competitive products in an omnichannel supply chain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "", "Page": "101189", "JournalTitle": "Electronic Commerce Research and Applications"}]}, {"ArticleId": 111056220, "Title": "Diversity, Equity and Inclusion", "Abstract": "<p><PERSON>, Transformation Lead with Aviva, tells <PERSON> how the insurance, wealth and retirement firm is putting diversity and inclusion at the very heart of its digital products and services.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad125", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111056362, "Title": "Structural evaluation of management capability and the mediation role of cybersecurity awareness towards enterprise performance", "Abstract": "<p>Implementation of Cyber Security Awareness (CSA) can inspire both large and small manufacturing companies to change their internal business processes for improved performance. However, CSA is still relatively novel in developing countries like Kenya. Thus, small and medium-sized manufacturing firms in particular require a proper understanding of CSA. This study focused at how Management Capabilities (MC) affect CSA and Manufacturing Firm Performance (MFP) as well as the mediating role of CSA in the relationship between MC and MFP. A sample of thirty-three manufacturing enterprises participated in the survey, and out of 110 responses that were received, 97 were confirmed to be valid. Moreover, Partial Least Squares Structural Equation Modeling (PLS-SEM) was used for the measurement model to assess the constructs for convergent and discriminant validity. In addition, for the structural model, path coefficients and their corresponding t-values, R<sup>2</sup> were used. To obtain cross-validated redundancy measures for each endogenous construct, the study examined predictive relevance (Q<sup>2</sup>) using the blindfolding procedure. The results indicate that MFP and MC require the mediating support of CSA. The results demonstrate that both MFP and MC necessitate the intermediary support provided by CSA. Furthermore, this research presents empirical evidence highlighting the direct positive impact of cybersecurity awareness on the performance of manufacturing firms. This underscores the critical importance of cultivating a culture of cybersecurity awareness within an organization, elucidating its constructive influence on various dimensions of firm performance.</p>", "Keywords": "Cybersecurity awareness; Management capability; Manufacturing firm performance; Small ad medium sized enterprises; Structural modelling", "DOI": "10.1007/s42488-023-00108-7", "PubYear": 2023, "Volume": "5", "Issue": "4", "JournalId": 64686, "JournalTitle": "Journal of Data, Information and Management", "ISSN": "2524-6356", "EISSN": "2524-6364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime Univerty, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime Univerty, Dalian, China"}], "References": [{"Title": "Cybersecurity in Big Data Era: From Securing Big Data to Data-Driven Security", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "6", "Page": "2055", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Evaluating the cyber security readiness of organizations and its influence on performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "102726", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Enhancing employees information security awareness in private and public organisations: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "102267", "JournalTitle": "Computers & Security"}, {"Title": "Exploration of the Impact of Cybersecurity Awareness on Small and Medium Enterprises (SMEs) in Wales Using Intelligent Software to Combat Cybercrime", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "12", "Page": "174", "JournalTitle": "Computers"}]}, {"ArticleId": 111056422, "Title": "Development and Verification of a Simplified hp-Version of the Least-Squares Collocation Method for Irregular Domains", "Abstract": "A new high-precision hp-version of the least-squares collocation method (hp-LSCM) for the numerical solution of elliptic problems in irregular domains is proposed, implemented, and verified. We use boundary irregular cells (i-cells) cut off from the cells of a rectangular grid by a boundary domain and their external parts for writing the collocation and matching equations in constructing an approximate solution. A separate solution is not constructed in small and (or) elongated non-independent i-cells. The solution is continued from neighboring independent cells, in which the outer (and inner in a multiply-connected domain) part of the domain boundary contained in these non-independent i-cells is used to write the boundary conditions. This approach significantly simplifies the computer implementation of the developed hp-LSCM in comparison with the previous well-recommended version without losing its efficiency. We show reducing the overdetermination ratio of a system of linear algebraic equations in comparison with its values in the traditional versions of LSCM when solving a biharmonic equation. The results are compared with those of other papers with a demonstration of the advantages of the new technique. We present the results of bending calculations of annular plates of various thicknesses in the framework of the <PERSON><PERSON><PERSON>–<PERSON> and <PERSON>ner–<PERSON>lin theories using hp-LSCM without shear locking. © 2023 South Ural State University. All rights reserved.", "Keywords": "biharmonic equation; irregular domain; <PERSON><PERSON><PERSON>–<PERSON> theory; least-squares collocation method; <PERSON><PERSON><PERSON>–<PERSON> theory", "DOI": "10.14529/mmp230303", "PubYear": 2023, "Volume": "16", "Issue": "3", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111056430, "Title": "Construct and Evaluate a Phone Dialing System Leveraging SSVEP Brain-Computer Interface", "Abstract": "<p>This study presents a SSVEP based BCI system, designed for dialing a phone number through EEG signals. Our SSVEP system leverages a tablet-based stimulator and OpenBCI Cyton board, employing Canonical Correlation Analysis for EEG signal classification. Tested on 7 participants, the system demonstrated a high accuracy rate of 98.1% in identifying the observed keys. The use of a tablet-based SSVEP stimulator was found to reduce visual fatigue compared to traditional LED stimulators. Despite its initial success, further validation with a larger cohort and in varied real-world conditions is required. This work signifies a promising advancement in utilizing BCIs in practical applications.</p>", "Keywords": "BCI;EEG;SSVEP", "DOI": "10.24297/ijct.v23i.9539", "PubYear": 2023, "Volume": "23", "Issue": "", "JournalId": 55824, "JournalTitle": "INTERNATIONAL JOURNAL OF COMPUTERS & TECHNOLOGY", "ISSN": "", "EISSN": "2277-3061", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 2, "Name": "Boning Li", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>ng Cao", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}], "References": []}, {"ArticleId": *********, "Title": "An improved solution methodology for the urban transit routing problem", "Abstract": "An improved solution methodology is proposed in this paper for the urban transit routing problem (UTRP). This methodology includes a procedure for the generation of improved initial solutions as well as improved metaheuristic search approaches, involving the use of hyperheuristics to manage search operators in both trajectory-based and population-based metaheuristics. The UTRP variant considered in this paper is that of deciding upon efficient bus transit routes. The design criteria embedded in our UTRP model are the simultaneous minimisation of the expected average passenger travel time and minimisation of the system operator’s cost (measuring the latter as the sum total of all route lengths in the system). The model takes as input an origin–destination demand matrix for a pre-specified set of bus stops, along with an underlying road network structure, and returns as output a set of bus route trade-off solutions. The decision maker can then select one of these route sets subjectively, based on the desired degree of trade-off between the aforementioned transit system design criteria. This bi-objective minimisation problem is solved approximately in three distinct stages — a solution initialisation stage, an intermediate analysis stage, and an iterative metaheuristic search stage during which high-quality trade-off solutions are sought. A novel procedure is introduced for the solution initialisation stage, aimed at effectively generating high-quality initial feasible solutions. Two metaheuristics are implemented to solve instances of the problem, namely a dominance-based multi-objective simulated annealing algorithm and an improved non-dominated sorting genetic algorithm, each equipped with a hyperheuristic capable of managing the perturbation operators employed. Various novel operators are proposed for these metaheuristics, of which the most noteworthy take into account the demand of passengers.", "Keywords": "Transit network design problem ; Urban transit routing problem ; Dominance-based multi-objective simulated annealing ; Non-dominated sorting genetic algorithm II ; Metaheuristic ; Hyperheuristic", "DOI": "10.1016/j.cor.2023.106481", "PubYear": 2024, "Volume": "163", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Stellenbosch University, Private Bag X1, Matieland, 7602, South Africa;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Stellenbosch University, Private Bag X1, Matieland, 7602, South Africa"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Stellenbosch University, Private Bag X1, Matieland, 7602, South Africa"}], "References": []}, {"ArticleId": *********, "Title": "Data-driven product optimization capabilities to enhance sustainability and environmental compliance in a marine manufacturing context", "Abstract": "<p>This paper investigates concerns related to product data and digital data flow when aiming to automate company processes. Accurate data is necessary to create value by enabling improved decision-making in product development, including sustainability capabilities. The case analyzed is an engineer-to-order (ETO) company operating in a low-volume marine manufacturing context. A participatory research approach is used to study two projects that are part of the company’s digital business transformation, aiming to digitalize information and autogenerate downstream processes. Building on the strengths promised by digitalization requires precise and extensive product and process information. An important facilitation capability is to create a digital thread from design to finished product, including product documentation. This is necessary to establish capabilities both to autogenerate appropriate compliance reporting as part of the product development process and to conduct virtual testing and validation before the physical equipment is acquired, resulting in a manufacturing process that is ‘right first time’. In addition, data capabilities guide and enable sound-decision making for improved sustainable practices in the early phase of product development. It is found that the data quality required to utilize tools within the context of Industry 4.0 demands changes to existing product design practices and focus on the three pillars harmonization, integration and automation of data and systems.</p>", "Keywords": "", "DOI": "10.1177/1063293X231217543", "PubYear": 2023, "Volume": "31", "Issue": "3-4", "JournalId": 6257, "JournalTitle": "Concurrent Engineering", "ISSN": "1063-293X", "EISSN": "1531-2003", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Norwegian University of Science and Technology, Trondheim, Norway"}, {"AuthorId": 2, "Name": "Torgeir <PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Norwegian University of Science and Technology, Trondheim, Norway"}], "References": [{"Title": "Product development process for IoT-ready products", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "2", "Page": "87", "JournalTitle": "Concurrent Engineering"}, {"Title": "Towards next generation cyber-physical systems and digital twins for construction", "Authors": "Abiola <PERSON>; <PERSON><PERSON><PERSON>; Omobolanle O<PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "505", "JournalTitle": "Journal of Information Technology in Construction"}]}, {"ArticleId": 111056488, "Title": "Skills-Based Volunteering", "Abstract": "<p><PERSON> examines the potential for skills-based volunteering to help address the digital divide, and looks at ways for organisations and individuals to get involved.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad126", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111056522, "Title": "Computer-controlled ultra high voltage amplifier for dielectric elastomer actuators", "Abstract": "Soft robotics is a breakthrough technology to support human–robot interactions. The soft structure of a soft robot can increase safety during human and robot interactions. One of the promising soft actuators for soft robotics is dielectric elastomer actuators (DEAs). DEAs can operate silently and have an excellent energy density. The simple structure of DEAs leads to the easy fabrication of soft actuators. The simplicity combined with silent operation and high energy density make DEAs interesting for soft robotics researchers. DEAs actuation follows the Maxwell-pressure principle. The pressure produced in the DEAs actuation depends much on the voltage applied. Common DEAs requires high voltage to gain an actuation. Since the power consumption of DEAs is in the milli-Watt range, the current needed to operate the DEAs can be neglected. Several commercially available DC-DC converters can convert the volt range to the kV range. In order to get a voltage in the 2–3 kV range, the reliable DC-DC converter can be pricy for each device. This problem hinders the education of soft actuators, especially for a newcomer laboratory that works in soft electric actuators. This paper introduces an entirely do-it-yourself (DIY) Ultrahigh voltage amplifier (UHV-Amp) for education in soft robotics. UHV-Amp can amplify 12 V to at a maximum of 4 kV DC. As a demonstration, we used this UHV-Amp to test a single layer of powdered-based DEAs. The strategy to build this educational type UHV-Amp was utilizing a Cockcroft-Walton circuit structure to amplify the voltage range to the kV range. In its current state, the UHV-Amp has the potential to achieve approximately 4 kV. We created a simple platform to control the UHV-Amp from a personal computer. In near future, we expect this easy control of the UHV-Amp can contribute to the education of soft electric actuators.", "Keywords": "Dielectric elastomer actuators ; Electric amplifier ; Soft actuators ; Soft robotics ; Soft actuator education", "DOI": "10.1016/j.birob.2023.100139", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 85921, "JournalTitle": "Biomimetic Intelligence and Robotics", "ISSN": "2667-3797", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>rdi W<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Gadjah Mada, Yogyakarta 55281, Indonesia;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Tokyo Institute of Technology, Tokyo 152-8550, Japan;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Engineering and Science, Shibaura Institute of Technology, Tokyo 135-8548, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering and Science, Shibaura Institute of Technology, Tokyo 135-8548, Japan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Gadjah Mada, Yogyakarta 55281, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Shibaura Institute of Technology, Tokyo 135-8548, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering Science and Mechanics, Shibaura Institute of Technology, Tokyo 135-8548, Japan"}, {"AuthorId": 8, "Name": "Shingo <PERSON>", "Affiliation": "Department of Mechanical Engineering, Tokyo Institute of Technology, Tokyo 152-8550, Japan;Living Systems Materialogy (LiSM) Research Group, International Research Frontiers Initiative (IRFI), Tokyo Institute of Technology, Yokohama 226-8501, Japan"}], "References": [{"Title": "Open-source and do-it-yourself microfluidics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "347", "Issue": "", "Page": "130624", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Bidirectional electrohydrodynamic pump with high symmetrical performance and its application to a tube actuator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "332", "Issue": "", "Page": "113168", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "A DIY Fabrication Approach of Stretchable Sensors Using Carbon Nano Tube Powder for Wearable Device", "Authors": "<PERSON><PERSON> W<PERSON>a; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "346", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 111056548, "Title": "Mode standardization: A practical countermeasure against mode collapse of GAN-based signal synthesis", "Abstract": "Intelligent fault diagnosis , particularly via data-driven deep learning , is vital for industrial health management. However, it often faces challenges due to unbalanced datasets, with either insufficient fault data or redundant data representing the same condition. For signal-based diagnosis, generative adversarial networks (GANs) provide a countermeasure by generating new signals for data augmentation but can suffer from mode collapse, causing monotonous new signals and further imbalance. Inspired by the conditional mechanism, we propose mode standardization to mitigate mode collapse in GAN-based signal synthesis . This method utilizes part of the original signals as reference input to the generator. As a result, although mode collapse still occurs, and the new signals may still be monotonous, this monotony is confined to their references. In other words, as long as the references are diverse, the generated signals will maintain an acceptable diversity, similar to the original. The negative effects of mode collapse are diminished, decreasing harm to the generation process. Comparative experiments demonstrate mode standardization&#x27;s effectiveness in diversity and quality on various datasets, affirming it as a practical countermeasure against mode collapse and a suitable strategy for industrial practice.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111089", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Dai", "Affiliation": "University of Chinese Academy of Sciences, Beijing 100049, China;Key Laboratory of Space Utilization, Technology and Engineering Center for Space Utilization, Chinese Academy of Sciences, Beijing 100094, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing 100049, China;Key Laboratory of Space Utilization, Technology and Engineering Center for Space Utilization, Chinese Academy of Sciences, Beijing 100094, China;Correspondence to: No 9. Deng <PERSON>ang South Rd, Hai Dian Dist, Beijing 100094, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing 100049, China;Key Laboratory of Space Utilization, Technology and Engineering Center for Space Utilization, Chinese Academy of Sciences, Beijing 100094, China;Correspondence to: No 9. Deng <PERSON>ang South Rd, Hai Dian Dist, Beijing 100094, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Space Utilization, Technology and Engineering Center for Space Utilization, Chinese Academy of Sciences, Beijing 100094, China"}], "References": [{"Title": "Deep learning fault diagnosis method based on global optimization GAN for unbalanced data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104837", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Imbalanced sample fault diagnosis of rotating machinery using conditional variational auto-encoder generative adversarial network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106333", "JournalTitle": "Applied Soft Computing"}, {"Title": "A case study of conditional deep convolutional generative adversarial networks in machine fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "407", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Joint imbalanced classification and feature selection for hospital readmissions", "Authors": "<PERSON><PERSON> Du; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "106020", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Tackling mode collapse in multi-generator GANs with orthogonal vectors", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107646", "JournalTitle": "Pattern Recognition"}, {"Title": "Unsupervised transfer learning for anomaly detection: Application to complementary operating condition transfer", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106816", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep transfer learning with limited data for machinery fault diagnosis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107150", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel deep metric learning model for imbalanced fault diagnosis and toward open-set classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "220", "Issue": "", "Page": "106925", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Non-revisiting genetic cost-sensitive sparse autoencoder for imbalanced fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "108138", "JournalTitle": "Applied Soft Computing"}, {"Title": "A multi-module generative adversarial network augmented with adaptive decoupling strategy for intelligent fault diagnosis of machines with small sample", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "239", "Issue": "", "Page": "107980", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Method to enhance deep learning fault diagnosis by generating adversarial samples", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "108385", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel method based on deep transfer unsupervised learning network for bearing fault diagnosis under variable working condition of unequal quantity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108381", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A multi-level adaptation scheme for hierarchical bearing fault diagnosis under variable working conditions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "", "Page": "251", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A neural network compression method based on knowledge-distillation and parameter quantization for the bearing fault diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "109331", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 111056571, "Title": "Utilizing blockchains in opportunistic networks for integrity and confidentiality", "Abstract": "Opportunistic networks (OppNets) are usually a set of smart, wearable, and portable devices or entities with mobility that connect wirelessly without requiring infrastructure. Such a network is of great importance in data transmission, particularly in incidents and disasters, whether man-made or natural. However, message integrity and confidentiality are of concern when dealing with vital and physiological data transmission under strict privacy regulations. In this work, we propose a structure to classify messages based on their priority in different queues. Furthermore, due to the decentralized architecture of OppNets, we propose a blockchain-based structure for providing security for high-priority messages. It contains three sequences of functional blocks with a light and simplified implementation that make it suitable for battery-powered wearable devices that are limited in energy consumption and computational units. The simulation results show that by increasing the number of nodes in the network, the average of the changes in block sizes is neglectable, which addresses the computation bottleneck. Furthermore, we analyze the performance of the proposed structure in terms of message delivery and network overhead compared with the Epidemic and Prophet routing algorithms. These results indicate advancing the overall performance of the proposed algorithm.", "Keywords": "Opportunistic networks ; Blockchain ; Message integrity ; Message confidentiality", "DOI": "10.1016/j.bcra.2023.100167", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 83117, "JournalTitle": "Blockchain: Research and Applications", "ISSN": "2096-7209", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cyber Security, Department of Information Systems, University of Münster, 80539 Münster, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cyber Security, Department of Information Systems, University of Münster, 80539 Münster, Germany"}], "References": [{"Title": "Secure and privacy-preserving structure in opportunistic networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "102208", "JournalTitle": "Computers & Security"}, {"Title": "A blockchain-based secure routing protocol for opportunistic networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "2191", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A blockchain-orchestrated deep learning approach for secure data transmission in IoT-enabled healthcare system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "172", "Issue": "", "Page": "69", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": *********, "Title": "A New Visual Front-end Combining KLT with Descriptor Matching for Visual-inertial Odometry", "Abstract": "<p>Currently, feature-based visual-inertial odometry (VIO) predominantly employs descriptor-matching or Kanade-Lucas-<PERSON>i (KLT)-based methods for feature tracking. However, these methods are prone to short track lengths and large accumulative errors. In this study, we propose a novel approach that seamlessly integrates the advantages of KLT and descriptor-matching techniques through a tightly-coupled fusion for feature tracking. The proposed method effectively overcomes the limitations of both methods, resulting in longer tracking lengths and reducing accumulative errors. Consequently, the enhanced feature tracking module contributes to improving localization accuracy and stability in VIO. To validate the proposed approach, we incorporate it into the feature tracking module of mainstream VIO and evaluate its performance via open-source datasets. Experimental results reveal that our proposed feature tracking method outperforms the original method in accuracy and robustness.</p>", "Keywords": "VIO; Visual Front-end; Feature tracking; KLT; Descriptor matching", "DOI": "10.1007/s10846-023-02008-9", "PubYear": 2023, "Volume": "109", "Issue": "4", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Satellite Navigation System and Equipment Technology, Shijiazhuang, China; Department of Automation, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Satellite Navigation System and Equipment Technology, Shijiazhuang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 5, "Name": "Yingjing Shi", "Affiliation": "Department of Automation, University of Electronic Science and Technology of China, Chengdu, China"}], "References": [{"Title": "Robocentric visual–inertial odometry", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "7", "Page": "667", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 111056623, "Title": "Environmental cold chain distribution center location model in the semiconductor supply chain: A hybrid arithmetic whale optimization algorithm", "Abstract": "This study addresses the high cost and high carbon emissions associated with the transportation sector. An environmental cold chain logistics distribution center location model has been designed by considering various constraints such as demand, time windows, and cost factors associated with cargo damage, refrigeration of cold chain cargo, and carbon emissions. An environmental cold chain logistics distribution center location model is designed for the raw materials of low-temperature storage and transportation in the semiconductor supply chain to achieve sustainable development by reducing carbon emissions. This study effectively reduces transportation costs and carbon emissions. To address the issues of slow convergence speed and susceptibility to local optima in the conventional whale optimization algorithm (WOA), a hybrid arithmetic whale optimization algorithm (HAWOA) is proposed by combining arithmetic optimization and the honey badger algorithm. Simulation analysis shows that the HAWOA is capable of obtaining the location scheme with the minimum distribution cost under the same number of iterations, which provides a substantial advantage compared to other algorithms.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109773", "PubYear": 2024, "Volume": "187", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Tianjin University of Commerce, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Tianjin University of Commerce, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Tianjin University of Commerce, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Tianjin University of Commerce, China;Co-Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Innovation and Circular Economy, Asia University, Taiwan;Taiwan Department of Medical Research, China Medical University Hospital, China Medical University, Taichung, Taiwan;UKM-Graduate School of Business, Universiti Kebangsaan Malaysia, 43000 Bangi, Selangor, Malaysia;Department of Industrial Engineering Department, Khon Kaen University, Thailand;Corresponding author"}], "References": [{"Title": "A review on genetic algorithm: past, present, and future", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "8091", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Solving Logistics Distribution Center Location with Improved Cuckoo Search Algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "676", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Route optimization for cold chain logistics of front warehouses based on traffic congestion and carbon emission", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "161", "Issue": "", "Page": "107663", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A Chaotic Oppositional Whale Optimisation Algorithm with Firefly Search for Medical Diagnostics", "Authors": "<PERSON> Tair; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "1", "Page": "959", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Application of improved hybrid whale optimization algorithm to optimization problems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "17", "Page": "12433", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 111056635, "Title": "Self-supervised video distortion correction algorithm based on iterative optimization", "Abstract": "Wide-angle video frames typically contain more information, but they also exhibit distortion that degrades the visual quality, especially at the edges. To eliminate this distortion from videos, we propose a self-supervised iterative optimization method in this paper. Specifically, we construct a motion parameter estimation model utilizing two consecutive distorted frames, where motion parameters comprise affine transform and distortion parameters. We apply the Gauss–<PERSON> algorithm to minimize the sum-of-squares error between frames and update parameters. Treating inter-frame motion as undistort-affine-distort transformations, frame alignment is achieved by continuously adjusting transform parameters. Ultimately, frames are corrected using the converged parameters. We generated a synthetic dataset with various distortion parameters for evaluation. Experiments demonstrate superior performance versus state-of-the-art methods on synthetic and real wide-angle videos. Our algorithm also achieves higher parameter estimation accuracy without sacrificing efficiency.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.110114", "PubYear": 2024, "Volume": "148", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong University, Jinan, Shandong 250061, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shandong University, Jinan, Shandong 250061, PR China;Corresponding author"}], "References": [{"Title": "DQN-based gradual fisheye image rectification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "129", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 111056652, "Title": "Information and software technology: Special section on techdebt 2021", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.infsof.2023.107374", "PubYear": 2024, "Volume": "168", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [], "References": []}, {"ArticleId": 111056692, "Title": "Improving reflector antenna parameters via mesh technique: design analysis using CST studio", "Abstract": "<p>Parabolic reflector antennas overcome the challenge of their extensive use in satellite communication, radar, and military applications, despite their bulky nature. A mesh parabolic reflector antenna can be used in 5<sup>th</sup> Generation networks to provide high-gain directional coverage for point-to-point and point-to-multipoint communication links which also helps in increasing the optical transparency as well as can be used in Massive Multiple Input Multiple Output (MIMO) to improve the coverage and capacity of the network. This paper compares the conventional and meshed antennas for high-performance cabled network antenna reflectors. A mesh topology for cable networks was created using CST by designing two identical paraboloidal-shaped nets, one of which serves as the front net and the other, the rear net. The approach is different from the traditional method that requires constructing a pyramid. Characteristics like Radio Frequency Parameters and <PERSON>am Patterns of the reflector surface with triangular facets of the mesh antenna have been studied and evaluated with the conventional parabolic reflector. It is a definitive conclusion that the meshed antenna performs better, as evidenced by the examination of the far-field patterns of both antennas.</p>", "Keywords": "Parabolic reflector; Beam pattern; Radio frequency performance; Meshed antennas", "DOI": "10.1007/s41870-023-01618-8", "PubYear": 2024, "Volume": "16", "Issue": "1", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Electronics and Communication Engineering, Sharda School Of Engineering and Technology, Sharda University, Greater Noida, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Electronics and Communication Engineering, Sharda School Of Engineering and Technology, Sharda University, Greater Noida, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Electronics and Communication Engineering, Sharda School Of Engineering and Technology, Sharda University, Greater Noida, India; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Electronics and Communication Engineering, Sharda School Of Engineering and Technology, Sharda University, Greater Noida, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Electronics and Communication Engineering, Sharda School Of Engineering and Technology, Sharda University, Greater Noida, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Electronics and Communication Engineering, Sharda School Of Engineering and Technology, Sharda University, Greater Noida, India"}, {"AuthorId": 7, "Name": "Ka<PERSON><PERSON>na Sagar", "Affiliation": "Department of Electronics and Communication Engineering, KIET Group of Institutions, Ghaziabad, UP, India"}], "References": [{"Title": "5G: a new era of wireless communication", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "619", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Design and development of 4 × 4 MIMO antennas for smart 5G devices", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "4", "Page": "1693", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Interference reduction in hybrid beamforming using 2-D overlapped partially connected subarray structure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "1407", "JournalTitle": "International Journal of Information Technology"}, {"Title": "RF-MEMS capacitive switches: enabling transition towards 5G/B5G applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "7", "Page": "3889", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 111056702, "Title": "A verifiable and efficient cross-chain calculation model for charging pile reputation", "Abstract": "To solve the current situation of low vehicle-to-pile ratio, charging pile (CP) operators incorporate private CPs into the shared charging system. However, the introduction of private CP has brought about the problem of poor service quality. Reputation is a common service evaluation scheme, in which the third-party reputation scheme has the issue of single point of failure; although the blockchain-based reputation scheme solves the single point of failure issue, it also brings the challenges of storage and query efficiency. It is a feasible solution to classify and store information on multiple chains, and at this time, reputation needs to be calculated in a cross-chain mode. Crosschain reputation calculation faces the problems of correctness verification, integrity verification and efficiency. Therefore, this paper proposes a verifiable and efficient cross-chain calculation model for CP reputation. Specially, in this model, we propose a verifiable cross-chain contract calculation scheme that adopts polynomial commitment to solve the problems of polynomial damage and tampering that may be encountered in the crosschain process of outsourced polynomials, so as to ensure the integrity and correctness of polynomial calculations. In addition, the miner selection and incentive mechanism algorithm in this scheme ensures the correctness of extracted information when the outsourced polynomial is calculated on the blockchain. The security analysis and experimental results demonstrate that this scheme is feasible in practice.", "Keywords": "Blockchain; Shared charging; Reputation; Cross-chain; Verifiable", "DOI": "10.1016/j.hcc.2023.100180", "PubYear": 2024, "Volume": "4", "Issue": "2", "JournalId": 85789, "JournalTitle": "High-Confidence Computing", "ISSN": "2667-2952", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "North China University of Technology, School of Information Science and Technology, Beijing 100144, China;Beijing University of Posts and Telecommunications, School of Electronic Engineering, Beijing 100876, China"}, {"AuthorId": 2, "Name": "Yun<PERSON> He", "Affiliation": "North China University of Technology, School of Information Science and Technology, Beijing 100144, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing 100195, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, School of Electronic Engineering, Beijing 100876, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "North China University of Technology, School of Information Science and Technology, Beijing 100144, China"}, {"AuthorId": 6, "Name": "Hong Li", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing 100195, China"}], "References": [{"Title": "A trusted architecture for EV shared charging based on blockchain technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100001", "JournalTitle": "High-Confidence Computing"}]}, {"ArticleId": 111056733, "Title": "HAFE: A Hierarchical Awareness and Feature Enhancement Network for Scene Text Recognition", "Abstract": "Scene text recognition (STR) is to obtain text information from natural text images. Compared to the optical character recognition , STR has attracted much attentions due to the variability of scene images. However, most STR methods tend to loss some spatial information due to only simply adding Resnet-45 to the front end of the encoder. To solve this problem, we propose a Hierarchical Awareness and Feature Enhancement (HAFE) network. Our proposed Hierarchical Awareness (HA) module can effectively extract the depth information of scene text images from multiple dimensions and then improve the anti-interference ability of the network. Besides, the proposed Feature Enhancement (FE) module can enhance the stability of the model through strengthening the global features, which is helpful to improve the accuracy of text recognition. On the whole, our HAFE network tends to strengthen the information in encoder, better learn the feature maps from different levels, and then improve the recognition accuracy of non-horizontal texts. Extensive comparative experiments on seven benchmark datasets show that the proposed method has the most advanced performance and outperforms most state-of-the-art ones. Our code will be released at website: https://github.com/HAFE in the future.", "Keywords": "", "DOI": "10.1016/j.knosys.2023.111178", "PubYear": 2024, "Volume": "284", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Kai <PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China, 300072;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> TANG", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China, 300072"}, {"AuthorId": 3, "Name": "Zikang LIU", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China, 300072"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>i <PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China, 300072"}], "References": [{"Title": "A holistic representation guided attention network for scene text recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "414", "Issue": "", "Page": "67", "JournalTitle": "Neurocomputing"}, {"Title": "STAN: A sequential transformation attention-based network for scene text recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107692", "JournalTitle": "Pattern Recognition"}, {"Title": "STAN: A sequential transformation attention-based network for scene text recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107692", "JournalTitle": "Pattern Recognition"}, {"Title": "MASTER: Multi-aspect non-local network for scene text recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107980", "JournalTitle": "Pattern Recognition"}, {"Title": "An extended attention mechanism for scene text recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117377", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111056752, "Title": "Rancang <PERSON>un Alat Mobile Smart Trash Bin Menggunakan Raspberry Pi", "Abstract": "Standar hidup masyarakat semakin meningkat dengan adanya perkembangan ekonomi global, sehingga produksi sampah domestik menjadi semakin meningkat dari tahun ke tahun. <PERSON>un dalam pengolahan sampah masih terdapat masalah dimana masih tercampurnya sampah berbahaya sehingga dapat menyebabkan penyebaran covid saat ini. Saat ini tren mulai bergeser ke perangkat pintar yang menggunakan Internet of Things untuk mengatasi masalah umum seperti masalah pengolahan sampah. Dalam penelitian ini diusulkan sebuah mobile smart trash bin yang dapat mengurangi interaksi secara langsung karena memiliki fitur buka tutup otomatis, dapat memilah sampah menjadi berbahaya dan tidak berbahaya, dapat mengirimkan data berat sampah, dan dapat dikendalikan dari jarak jauh dengan dilengkapi&nbsp; livestream video. Sistem yang dikembangkan diuji dengan metode pengujian fungsionalitas, pengujian performa, dan pengujian konektifitas. Pengujian fungsionalitas memberikan hasil bahwa keseluruhan sistem dapat bekerja. Pengujian performa pada sensor ultrasonik memberikan hasil tingkat keakurasian sebesar 97,18%, servo motor MG995 berada di angka 100%, sensor berat memiliki tingkat keakurasian 98,65%, dan penggunaan baterai dalam keadaan bersiap dapat bekerja selama 13 Jam 3 Menit 40 Detik dan dalam keadaan bekerja dapat bekerja selama 1 Jam 56 Menit 42 Detik. Pengujian konektivitas dari rata-rata delay kontrol robot ketika diberikan perintah adalah 103,4ms, dan delay pada pengiriman data berat sampah adalah 112,8ms.", "Keywords": "HX711; internet of thing; load cell half bridge; mobile smart trash bin; raspberry pi; sensor ultrasonic", "DOI": "10.35143/jkt.v9i1.5749", "PubYear": 2023, "Volume": "", "Issue": "Vol. 9 No. 1 (2023)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Negeri Jakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Negeri Jakarta"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Jakarta"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Jakarta"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Politeknik Negeri Jakarta"}], "References": []}, {"ArticleId": 111056786, "Title": "Designing the color of electric motorcycle products emotionally based on the dynamic field theory and deep learning", "Abstract": "Current research in Product Color Emotional Design (PCED) often encounters limitations due to the ambiguity in users&#x27; emotional information expression, potentially compromising the effectiveness of the design scheme. This paper presents a novel PCED methodology that integrates dynamic field theory with deep learning, thus enabling one-stop generation from user feature labels to product color. Consequently, a product color design system is established, capable of generating color design schemes that align with users&#x27; emotional needs by solely incorporating user feature labels. This approach mitigates potential inaccuracies in generation results that could stem from users&#x27; direct expression of emotional information. The validity and applicability of the proposed method are demonstrated through a case study focusing on the color emotion design of an electric motorcycle.", "Keywords": "Product color emotional design (PCED) ; Dynamic field theory ; Fuzzy analytic hierarchy process (FAHP) ; GoogLeNet ; Conditional generative adversarial network (C-GAN)", "DOI": "10.1016/j.displa.2023.102584", "PubYear": 2024, "Volume": "81", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hebei University of Technology, College of Architecture and Art Design, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>eng Qin", "Affiliation": "Hebei University of Technology, College of Architecture and Art Design, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei University of Technology, College of Architecture and Art Design, Tianjin, China;Corresponding author at: Hebei University of Technology, College of Architecture and Art Design, 29 Guangrong Road, Hongqiao District, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei University of Technology, College of Architecture and Art Design, Tianjin, China"}], "References": [{"Title": "A Multi-Model Approach for User Portrait", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "6", "Page": "147", "JournalTitle": "Future Internet"}, {"Title": "SAR-to-optical image translation based on improved CGAN", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108208", "JournalTitle": "Pattern Recognition"}, {"Title": "Data-driven multi-objective affective product design integrating three-dimensional form and color", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "18", "Page": "15835", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 111056800, "Title": "PrivMaskFL: A private masking approach for heterogeneous federated learning in IoT", "Abstract": "With the arrival of the era of the Internet of Things (IoT), the rapid development of new technologies such as artificial intelligence , big data, and other advanced techniques, data is growing geometrically. These data are prone to form data silos, scattered in various places. Jointly decentralized data applications will accelerate the progress and development of the times, but also face the challenge of data privacy protection. Federated learning (FL), a new branch of distributed machine learning , is trained by collaborative training to obtain global model without direct exposure to local datasets. Some studies have shown that typically federated learning involves a larger number of participants, it can lead to a significant increase in communication overhead , resulting in issues such as higher latency and bandwidth consumption. We suggest masking a subset of diverse participants and allowing the remaining participants to proceed with the next communication round of updates. Our aim is for reducing the communication overhead and improving the convergence performance of the global model on the premise of heterogeneous data privacy protection. We design a private masking approach PrivMaskFL to address two problems. We firstly propose a dynamic participant aggregation masking approach, which adopts the greedy ideology to select the relatively important participants and mask the unimportant ones; secondly, we design an adaptive differential privacy approach, which adaptively stratifies privacy budget according to the characteristics of participant, allocates the budget in a fine-grained stratified level, and adds Gaussian noise reasonably. Specifically, in each communication round, the participant’s model needs to perform local differential privacy noise addition for uplink parameter transmission; the server aggregates to acquire global model, finds a candidate participant subset based on the smaller parameter divergence by using the greedy algorithm approximation for t + 1 -th communication round for downlink parameter transmission. Subsequently, the privacy budget sequence is divided and granted to the participants of the stratified level, and the Gaussian noise addition of adaptive differential privacy is completed to achieve privacy protection without compromising the model’s usability. In experiments, our approach reduces the communication overhead and improve the convergence performance. Furthermore, our approach achieves higher accuracy and robust variance on both FMNIST and FEMNIST datasets.", "Keywords": "", "DOI": "10.1016/j.comcom.2023.11.022", "PubYear": 2024, "Volume": "214", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer of Science and Technology, Huazhong University of Science and Technology, 1037 Luoyu Road, Hongshan District, Wuhan, China;Technology Promotion Division, CEPREI, 78 Zhucun Avenue West, Zengcheng District, Guangzhou, China;Corresponding author at: School of Computer of Science and Technology, Huazhong University of Science and Technology, 1037 Luoyu Road, Hongshan District, Wuhan, China"}, {"AuthorId": 2, "Name": "Hong <PERSON>", "Affiliation": "School of Computer of Science and Technology, Huazhong University of Science and Technology, 1037 Luoyu Road, Hongshan District, Wuhan, China"}], "References": [{"Title": "Real-time trajectory privacy protection based on improved differential privacy method and deep learning model", "Authors": "Jing <PERSON>; Hong Zhu", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}]}, {"ArticleId": 111056860, "Title": "Futurology", "Abstract": "<p><PERSON>, Senior Product Manager at IBM, and <PERSON>, Associate Professor of Information Systems at Warwick Business School, consider the skills required to thrive in an AI-enabled world.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad121", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111056883, "Title": "CFA-GAN: Cross fusion attention and frequency loss for image style transfer", "Abstract": "Image style transfer is a significant challenge in computer vision, where the goal is to transfer the style of a reference image to a source image while preserving its background and feature information. Despite previous progress in this area, many studies have overlooked the issues of local details and complexity, leading to unrealistic generated images and slow model inference speeds. This paper proposes an efficient image style transfer method based on cross-fusion attention (CFA), inspired by generative adversarial networks (GAN) and a new joint loss. Our novel CFA encodes local contextual information between low-level and high-level features, models remote dependencies of features, and embeds CFA into the encoder of our proposed model to increase the diversity of small features and focus on more prominent feature parts. Furthermore, we use frequency domain loss to improve image reconstruction quality and a joint loss to train the network. Our experiments on multiple datasets demonstrate that our proposed method offers significant advantages in terms of inference speed, background information preservation, and local details, making it a promising approach to image style transfer. The code for our method is available at https://github.com/berylxzhang/CFA-GAN .", "Keywords": "Image synthesis ; Style transfer ; Deep learning ; Generative adversarial network ; Image processing", "DOI": "10.1016/j.displa.2023.102588", "PubYear": 2024, "Volume": "81", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, Nanjing Institute of Technology, Nanjing, 211167, China;School of Geography and Marine Science, Nanjing University, Nanjing, 210023, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "China Academy of Safety Science and Technology, Beijing, 100012, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering, Nanjing Institute of Technology, Nanjing, 211167, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "China Academy of Safety Science and Technology, Beijing, 100012, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Earth Science and Engineering, Hohai University, Nanjing, 211100, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Surveying and Mapping, Beijing, 100038, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of computer science, University of Pennsylvania, 19104-6106, USA"}], "References": [{"Title": "T-GAN: A deep learning framework for prediction of temporal complex networks with adaptive graph convolution and attention mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "102023", "JournalTitle": "Displays"}, {"Title": "A new VAE-GAN model to synthesize arterial spin labeling images from structural MRI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "102079", "JournalTitle": "Displays"}, {"Title": "GAN computers generate arts? A survey on visual arts, music, and literary text generation using generative adversarial network", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102237", "JournalTitle": "Displays"}, {"Title": "HCFNN: High-order coverage function neural network for image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Yu", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108873", "JournalTitle": "Pattern Recognition"}, {"Title": "Neural style transfer for image steganography and destylization with supervised image to image translation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "4", "Page": "6271", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Continuous transfer of neural network representational similarity for incremental learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "545", "Issue": "", "Page": "126300", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111056916, "Title": "Driving Business Innovation and Growth", "Abstract": "<p>To help organisations transform and flourish, the BCS IT Leaders Forum believes a new approach is needed. It believes people who have been developed to combine both IT leadership and entrepreneurship are ideally placed to enable change.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad135", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [], "References": []}, {"ArticleId": 111056959, "Title": "A practically implementable reinforcement learning control approach by leveraging offset-free model predictive control", "Abstract": "This work addresses the problem of designing an offset-free implementable reinforcement learning (RL) controller for nonlinear processes . RL-based controllers can update the control policy using observed data obtained online based on the controller-process interactions. This allows to alleviate the regular model maintenance step that is essential in advanced control techniques such as model predictive control (MPC). However, random explorations are required for an RL agent to find optimal state–action regions so as to finally reach the optimal policy . This is not implementable in practical situations due to safety concerns and economic objectives. To address this issue, a pre-training strategy is proposed to provide a secure platform for online implementations of the RL controllers. To this end, an offset-free MPC (representative industrial MPC) optimization problem is leveraged to train the RL agent offline. Having obtained similar performance to the offset-free MPC, the RL agent is utilized for online control to interact with the actual process. The efficacy of the proposed approach to handle nonlinearity and changes in plant operating conditions (due to unmeasured disturbances) are demonstrated through simulations on a chemical reactor example for a pH neutralization process. The results show that the proposed RL controller can significantly improve the oscillatory closed-loop responses, obtained by running the offset-free MPC due to the plant-model mismatch and unmeasured disturbances.", "Keywords": "", "DOI": "10.1016/j.compchemeng.2023.108511", "PubYear": 2024, "Volume": "181", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, McMaster University, Hamilton, Ontario L8S 4L7, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, McMaster University, Hamilton, Ontario L8S 4L7, Canada;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sartorius, Oakville, Ontario, Canada"}], "References": [{"Title": "Reinforcement learning for batch bioprocess optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "106649", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "A review On reinforcement learning: Introduction and applications in industrial process control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106886", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Real-time optimization using reinforcement learning", "Authors": "<PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "107077", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Model-based reinforcement learning and predictive control for two-stage optimal control of fed-batch bioreactor", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "107465", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Deep reinforcement learning control of hydraulic fracturing", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "107489", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Twin actor twin delayed deep deterministic policy gradient (TATD3) learning for batch process control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "107527", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Reinforcement learning for batch process control: Review and perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "", "Page": "108", "JournalTitle": "Annual Reviews in Control"}, {"Title": "Safe chance constrained reinforcement learning for batch process control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; E.A. del Rio-Chanona", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "107630", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Reinforcement learning for online adaptation of model predictive controllers: Application to a selective catalytic reduction unit", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "160", "Issue": "", "Page": "107727", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Model predictive control of nonlinear processes using neural ordinary differential equation models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Panagiot<PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "178", "Issue": "", "Page": "108367", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 111057010, "Title": "Towards more accurate and interpretable model: Fusing multiple knowledge relations into deep knowledge tracing", "Abstract": "With the rapid growth of online education , Knowledge tracing (KT) has become a well established problem, which evaluates the knowledge states of students and predicts their performance on new exercises. Recently, more and more works have noticed the importance of relations among knowledge points and proposed to introduce the knowledge relations into KT. However, how to precisely learn the representation of different types of knowledge relations and effectively fuse multiple relations into KT is still challenging. To address this issue, we propose a novel KT model, called Deep Knowledge Tracing with Multiple Relations (DKTMR), which can simultaneously fuse the directed relation and undirected relation into KT. More specifically, casting the knowledge relations as a graph, DKTMR designs to utilize two types of Generative Adversarial Networks (GANs) to learn the representation of knowledge point with different relations via graph representation learning . Then, the Gated Recurrent Unit (GRU) is used to update the students’ knowledge states. Furthermore, to consider the different contribution for each type of relation to the final prediction, an attention-based fusion method is proposed to learn the coefficients for different relations. Compared with several state-of-the-art baselines, the extensive experiments on four real-world datasets demonstrate the effectiveness and interpretability of DKTMR.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122573", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, Jilin 130117, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, Jilin 130117, China"}, {"AuthorId": 3, "Name": "Hengnian Gu", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, Jilin 130117, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, Jilin 130117, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, Jilin 130117, China"}, {"AuthorId": 6, "Name": "Dongdai Zhou", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, Jilin 130117, China;Corresponding author"}], "References": [{"Title": "Efficient heuristics for learning Bayesian network from labeled and unlabeled data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "385", "JournalTitle": "Intelligent Data Analysis"}, {"Title": "Instance-based weighting filter for superparent one-dependence estimators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106085", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Ability boosted knowledge tracing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "596", "Issue": "", "Page": "567", "JournalTitle": "Information Sciences"}, {"Title": "Knowledge Tracing: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "11", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "HHSKT: A learner–question interactions based heterogeneous graph neural network model for knowledge tracing", "Authors": "Qin Ni; Tingjiang Wei; Jiabao Zhao", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119334", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111057050, "Title": "EtherShield: Time-interval Analysis for Detection of Malicious Behavior on Ethereum", "Abstract": "<p>Advances in blockchain technology have attracted significant attention across the world. The practical blockchain applications emerging in various domains ranging from finance, healthcare, and entertainment, have quickly become attractive targets for adversaries. The novelty of the technology coupled with the high degree of anonymity it provides made malicious activities even less visible in the blockchain environment. This made their robust detection challenging.</p><p>This paper presents EtherShield, an novel approach for identifying malicious activity on the Ethereum blockchain. By combining temporal transaction information and contract code characteristics, EtherShield can detect various types of threats and provide insight into the behavior of contracts. The time-interval based analysis used by EtherShield enables expedited detection, achieving comparable accuracy to other approaches with significantly less data. Our validation analysis, which involved over 15,000 Ethereum accounts, demonstrated that EtherShield can significantly expedite the detection of malicious activity while maintaining high accuracy levels (86.52% accuracy with 1 hour of transaction history data and 91.33% accuracy with 1 year of transaction history data).</p>", "Keywords": "", "DOI": "10.1145/3633514", "PubYear": 2024, "Volume": "24", "Issue": "1", "JournalId": 23139, "JournalTitle": "ACM Transactions on Internet Technology", "ISSN": "1533-5399", "EISSN": "1557-6051", "Authors": [{"AuthorId": 1, "Name": "Bofeng Pan", "Affiliation": "Department of Computer Science, University of Saskatchewan, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Saskatchewan, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, Canada"}], "References": [{"Title": "Dissecting Ponzi schemes on Ethereum: Identification, analysis, and impact", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "259", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Detection of illicit accounts over the Ethereum blockchain", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113318", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Phishing Scams Detection in Ethereum Transaction Network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "SADPonzi: Detecting and Characterizing Ponzi Schemes in Ethereum Smart Contracts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems"}]}, {"ArticleId": *********, "Title": "Method for Calculating the Parameters of Superplasticity of Titanium Alloys Based on the Results of Test Forming Into a Rectangular Matrix at Constant Pressure", "Abstract": "We propose a procedure to determine the strain rate sensitivity index of a superplastic material from the results of bulge forming of a long thin rectangular membrane under constant pressure of inertia gas. In contrast to other known procedures, the method suggested takes into consideration the presence of entry radius in the matrix set used. The mathematical model of the technological process is developed based on the main assumptions of the thin shell theory. To validate the procedure suggested the finite element analysis is fulfilled using the software package ANSYS 10 ED. Experimental approbation of the method suggested is carried out on the titanium alloys Ti-6Al-4V, a good agreement is achieved. It is shown that taking into account the influence of the entry radius allows to improve considerably the accuracy for finite element modelling. We draw the conclusion that the procedure developed may be recommended for practical usage to determine the superplastic parameters of thin sheet superplastic materials. © 2023 South Ural State University. All rights reserved.", "Keywords": "ANSYS; entry radius; rectangular matrix; rheological parameters; superplastic forming", "DOI": "10.14529/mmp230108", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "Pellet image segmentation model of superpixel feature-based support vector machine in digital twin", "Abstract": "A digital twin model based on superpixel features is established to solve the problem of noise and similar gray values between foreground and background of pellet images. With superpixel as the basic unit of segmentation, the influence of single pixel on segmentation results is reduced, and allows for higher segmentation accuracy . The gray-level co-occurrence matrix is used to represent the superpixel characteristic information, and the color moment and gray level distribution are combined to comprehensively characterize the superpixel. Through principal component analysis and correlation analysis, The feature compression of superpixel is realized, and the computational efficiency is improved. The superpixel binary classification data set is built, and the multi-dimensional feature information of superpixel is extracted as input vector to train the binary classification model of SVM , and the image segmentation problem is transformed into foreground and background classification problem. A multi-scale superpixel segmentation boundary optimization method is proposed to further refine the boundary region of foreground and background. A four-neighborhood search algorithm is proposed to reduce the missegmentation rate of edge superpixels. Experimental results show that the accuracy of the proposed method can reach 95.87%, the precision of image edge segmentation is high, and the foreground and background of granular image are accurately segmented. The digital twin model is established which can provide basis for the subsequent visual analysis and decision.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111083", "PubYear": 2024, "Volume": "151", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Engineering Research Center for the Intelligentization of Iron Ore Optimization and Ironmaking Raw Materials Preparation Processes, North China University of Science and Technology, Tangshan 063210, China;Tangshan Intelligent Industry and Image Processing Technology Innovation Center, North China University of Science and Technology, Tangshan 063210, China;College of Metallurgy and Energy, North China University of Science and Technology, Tangshan 063210, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei Engineering Research Center for the Intelligentization of Iron Ore Optimization and Ironmaking Raw Materials Preparation Processes, North China University of Science and Technology, Tangshan 063210, China;Tangshan Intelligent Industry and Image Processing Technology Innovation Center, North China University of Science and Technology, Tangshan 063210, China;College of Metallurgy and Energy, North China University of Science and Technology, Tangshan 063210, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei Engineering Research Center for the Intelligentization of Iron Ore Optimization and Ironmaking Raw Materials Preparation Processes, North China University of Science and Technology, Tangshan 063210, China;Tangshan Intelligent Industry and Image Processing Technology Innovation Center, North China University of Science and Technology, Tangshan 063210, China;College of Metallurgy and Energy, North China University of Science and Technology, Tangshan 063210, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hebei Engineering Research Center for the Intelligentization of Iron Ore Optimization and Ironmaking Raw Materials Preparation Processes, North China University of Science and Technology, Tangshan 063210, China;Tangshan Intelligent Industry and Image Processing Technology Innovation Center, North China University of Science and Technology, Tangshan 063210, China;College of Metallurgy and Energy, North China University of Science and Technology, Tangshan 063210, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Engineering Research Center for the Intelligentization of Iron Ore Optimization and Ironmaking Raw Materials Preparation Processes, North China University of Science and Technology, Tangshan 063210, China;Tangshan Intelligent Industry and Image Processing Technology Innovation Center, North China University of Science and Technology, Tangshan 063210, China;College of Metallurgy and Energy, North China University of Science and Technology, Tangshan 063210, China;Corresponding author at: Hebei Engineering Research Center for the Intelligentization of Iron Ore Optimization and Ironmaking Raw Materials Preparation Processes, North China University of Science and Technology, Tangshan 063210, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Metallurgy and Energy, North China University of Science and Technology, Tangshan 063210, China;Corresponding author"}], "References": [{"Title": "Spatiotemporal road scene reconstruction using superpixel-based Markov random field", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "124", "JournalTitle": "Information Sciences"}, {"Title": "Predicting stock price trends based on financial news articles and using a novel twin support vector machine with fuzzy hyperplane", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106806", "JournalTitle": "Applied Soft Computing"}, {"Title": "Memory‐augmented neural networks based dynamic complex image segmentation in digital twins for self‐driving vehicle", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "132", "Issue": "", "Page": "108956", "JournalTitle": "Pattern Recognition"}, {"Title": "Integrating digital twins and deep learning for medical image analysis in the era of COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "4", "Page": "292", "JournalTitle": "Virtual Reality & Intelligent Hardware"}, {"Title": "Fuzzy Superpixel-based Image Segmentation", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109045", "JournalTitle": "Pattern Recognition"}, {"Title": "Innovative soft computing-enabled cloud optimization for next-generation IoT in digital twins", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>v", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "110082", "JournalTitle": "Applied Soft Computing"}, {"Title": "Real-time prediction of grinding surface roughness based on multi-sensor signal fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "127", "Issue": "11-12", "Page": "5847", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 111057233, "Title": "The fuzzy human-robot collaboration assembly line balancing problem", "Abstract": "Consideration is given to the assembly line balancing problem (ALBP) in production lines employed collaborative robots. Human–robot collaboration promises high advantages in manufacturing regarding automation, productivity, accuracy as well as flexibility in production. This technology is really challenging but requires redesigning of assembly lines to effectively involve the available human- and robot-resources. As robots can collaborate with humans sharing the assembly tasks in the same workstation, production managers experience an increasingly complex ALBP setting. Moreover, a crucial issue raised within this context is the variance and uncertainty in processing times for manual (human) work. Processing times for human work are subject to the nature and skills of the human worker. We explore the heuristic solution of this NP-hard problem using a problem-specific metaheuristic. Our aim is to investigate the effects of utilizing collaborate robots in manual assembly lines with respect to both production rate and workload smoothing. Simulation experiments on a real-life case study borrowed from the literature show how to tackle the problem under uncertainty (fuzziness) in task processing times.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109774", "PubYear": 2024, "Volume": "187", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Paraskevi Th. Zacharia", "Affiliation": "Department of Business Administration, Management Information Systems & Business Intelligence Laboratory, University of Patras, Greece;Department of Industrial Design & Production Engineering, University of West Attica, Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Management Information Systems & Business Intelligence Laboratory, University of Patras, Greece;Department of Product and Systems Design Engineering, University of the Aegean, Syros, Greece"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Management Information Systems & Business Intelligence Laboratory, University of Patras, Greece;Corresponding author"}], "References": [{"Title": "A conceptual framework to evaluate human-robot collaboration", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "3", "Page": "841", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A comprehensive review of robotic assembly line balancing problem", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Multi-objective migrating bird optimization algorithm for cost-oriented assembly line balancing problem with collaborative robots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "14", "Page": "8575", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Evaluating quality in human-robot interaction: A systematic search and classification of performance and human-centered factors, measures and metrics towards an industry 5.0", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "392", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Collaborative robots’ assembly system in the manufacturing area, assembly system 4.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "2", "Page": "1069", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 111057260, "Title": "The PolitiFact-Oslo Corpus: A New Dataset for Fake News Analysis and Detection", "Abstract": "<p>This study presents a new dataset for fake news analysis and detection, namely, the PolitiFact-Oslo Corpus. The corpus contains samples of both fake and real news in English, collected from the fact-checking website PolitiFact.com. It grew out of a need for a more controlled and effective dataset for fake news analysis and detection model development based on recent events. Three features make it uniquely placed for this: (i) the texts have been individually labelled for veracity by experts, (ii) they are complete texts that strictly correspond to the claims in question, and (iii) they are accompanied by important metadata such as text type (e.g., social media, news and blog). In relation to this, we present a pipeline for collecting quality data from major fact-checking websites, a procedure which can be replicated in future corpus building efforts. An exploratory analysis based on sentiment and part-of-speech information reveals interesting differences between fake and real news as well as between text types, thus highlighting the importance of adding contextual information to fake news corpora. Since the main application of the PolitiFact-Oslo Corpus is in automatic fake news detection, we critically examine the applicability of the corpus and another PolitiFact dataset built based on less strict criteria for various deep learning-based efficient approaches, such as Bidirectional Long Short-Term Memory (Bi-LSTM), LSTM fine-tuned transformers such as Bidirectional Encoder Representations from Transformers (BERT) and RoBERTa, and XLNet.</p>", "Keywords": "", "DOI": "10.3390/info14120627", "PubYear": 2023, "Volume": "14", "Issue": "12", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Literature, Area Studies and European Languages, University of Oslo, 0315 Oslo, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sintef <PERSON>, 0373 Oslo, Norway; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sintef Digital, 0373 Oslo, Norway"}], "References": [{"Title": "A body sensor data fusion and deep recurrent neural network-based behavior recognition approach for robust healthcare", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "105", "JournalTitle": "Information Fusion"}, {"Title": "A heterogeneous stacking ensemble based sentiment analysis framework using multiple word embeddings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "530", "JournalTitle": "Computational Intelligence"}, {"Title": "CB-Fake: A multimodal deep learning framework for automatic fake news detection using capsule neural network and BERT", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "4", "Page": "5587", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Beyond word embeddings: A survey", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "89", "Issue": "", "Page": "418", "JournalTitle": "Information Fusion"}, {"Title": "Fake News Spreaders Detection: Sometimes Attention Is Not All You Need", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "9", "Page": "426", "JournalTitle": "Information"}, {"Title": "A Comparative Study of Machine Learning and Deep Learning Techniques for Fake News Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "12", "Page": "576", "JournalTitle": "Information"}, {"Title": "Detection of Hate Speech using BERT and Hate Speech Word Embedding with Deep Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "1", "Page": "2166719", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Content-Based Fake News Detection With Machine and Deep Learning: a Systematic Review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "530", "Issue": "", "Page": "91", "JournalTitle": "Neurocomputing"}, {"Title": "Transformers in the Real World: A Survey on NLP Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "4", "Page": "242", "JournalTitle": "Information"}]}, {"ArticleId": 111057262, "Title": "Sistem Peramalan Stok Kaos Sablon dengan Weight Moving Average", "Abstract": "Salah satu permasalahan yang dihadapi oleh pemiliki usaha kaos sablon adalah kesulitan dalam melakukan pemesanan bahan baku kaos polos. Selama ini pemesanan bahan baku kaos&nbsp;tidak mengambil perhitungan konkrit atau mengacu pada jumlah bahan yang dipesan untuk kaos polos.&nbsp;Sehingga diperlukan sebuah sistem peramalan sebagai pendukung pengambilan keputusan pemesanaan kaos polos bagi usaha kaos sablon. Berdasarkan permasalahan tersebut, digunakan metode&nbsp;Weight Moving Average&nbsp;(WMA)&nbsp;untuk meramalkan stok kaos sablon. Metode ini cocok digunakan untuk peramalan dengan dengan pola data horizontal, disesuaikan dengan pola data penjualan sejak Januari â€“ Desember&nbsp;2021. Hasil dari peramalan menghasilkan nilai error&nbsp;terkecil untuk kaos hitam dengan bobot WMA 5 dengan rata-rata Mean Absolute Percentage Error&nbsp;(MAPE) 9,97% . Sedangkan kaos putih menghasilkan nilai error&nbsp;terkecil untuk bobot WMA 3 dengan rata-rata MAPE 9,95%. Hal ini menunjukkan bahwa metode WMA memiliki tingkat akurasi sangat tinggi karena memiliki nilai MAPE kurang dari 10%.", "Keywords": "Time Series Analysis;Forecasting System;Weight Moving Average", "DOI": "10.35143/jkt.v9i1.5834", "PubYear": 2023, "Volume": "", "Issue": "Vol. 9 No. 1 (2023)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Jember"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Jember"}, {"AuthorId": 3, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "Universitas Jember"}], "References": []}, {"ArticleId": 111057292, "Title": "Do developer perceptions have borders? Comparing C code responses across continents", "Abstract": "<p>Recent studies have empirically validated the existence of small patterns in C code, named atoms of confusion (or atoms for short), that can interfere with program comprehension. The focus of this research is an attempt to see if these patterns in C would have a similar impact on a second group of participants who have similar levels of experience with C, but come from different places. We report on studies conducted with students from the USA and China. Both sets of participants were shown snippets of code and asked to predict the output. While performance measures (accuracy and speed) showed little difference in aggregate, a few individual atoms yielded surprising results. For example, we found examples where the clarified versions of code, with the atoms removed, were more confusing to the Chinese participants, despite the presence of atoms having much less impact on this group in general. These findings suggest that both the atoms themselves, and the processes used to remove them, may be viewed differently by individuals from different parts of the world. As such, developing insights on the “cross-border” applicability of coding practices could help create better pedagogical practices to prepare students for today’s globally-integrated approach to software development. </p>", "Keywords": "Code comprehension; Empirical studies; C code snippets; Programming languages", "DOI": "10.1007/s11219-023-09654-0", "PubYear": 2024, "Volume": "32", "Issue": "2", "JournalId": 27487, "JournalTitle": "Software Quality Journal", "ISSN": "0963-9314", "EISSN": "1573-1367", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Colorado Colorado Springs, Colorado Springs, USA; Corresponding author."}, {"AuthorId": 2, "Name": "Yu <PERSON>", "Affiliation": "Teaching + Learning Commons, University of California San Diego, La Jolla, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, New York University, Brooklyn, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information Sciences and Technology, Pennsylvania State University-Brandywine, Media, USA"}], "References": [{"Title": "Studying programming in the neuroage", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "6", "Page": "30", "JournalTitle": "Communications of the ACM"}, {"Title": "Identifying program confusion using electroencephalogram measurements", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "12", "Page": "2528", "JournalTitle": "Behaviour & Information Technology"}]}, {"ArticleId": 111057332, "Title": "Leading Cyber Security in the UK Civil Nuclear Sector", "Abstract": "<p>Dr <PERSON> MBCS believes, when it comes to cyber security leadership in the civil nuclear sector, that strategy scope should cover the entire cyber environment.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad129", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111057363, "Title": "Mathematical Modelling Economy", "Abstract": "The article presents an overview of the main methods of economic modelling used in scientific research over the past twenty years. This overview does not claim to cover all areas, methods and models used in scientific research in the field of economics, since it is impossible to do within a single article. We consider mathematical modelling of only two branches of economic theory: macroeconomics and microeconomics. At the same time, we present no literature review of methods and models of research in the section of microeconomics, which take place in the tools of scientific research, but were described in the section of macroeconomics. We believe that this review is useful to scientists engaged in the indirect study of economic phenomena and processes. © 2023 South Ural State University. All rights reserved.", "Keywords": "macroeconomics; microeconomics; model; modelling; review; sources", "DOI": "10.14529/mmp230101", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111057388, "Title": "Remote Parkinson's disease severity prediction based on causal game feature selection", "Abstract": "Telemonitoring of Parkinson&#x27;s disease has important implications for early diagnosis and treatment of patients. Most of the existing feature selection methods for remote prediction of PD severity are based on correlation and rarely consider causality, thus compromising the robustness of the model. Therefore, a causal game-based feature selection (CGFS) model is proposed for remote PD symptom severity assessment. Firstly, to address the challenge of small data size, the similar patient transfer strategy is designed to find data from source domain patients with conditions similar to those of the target patient. Secondly, the undirected equivalent greedy search method is employed to construct the causal graph between features and PD severity scores , and the robustness of the model is improved by selecting causal features. Then, to enhance the prediction accuracy, this paper utilizes the cooperative game approach Shapley value to evaluate the contribution of neighborhood nodes of the target value, and selects the features with causality and high contribution to form the final feature subset. Finally, the subset is input into the random forest to further enhance robustness and performance of the model. Experiments on <PERSON>’s telemonitoring dataset and the tapping dataset with different biomarkers show that the robustness of the feature subset selected by the CGFS model, and the prediction performance is better than advanced models compared. Therefore, the validity and universality of the CGFS method is demonstrated in remote PD severity prediction.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122690", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Yanshan University, Qinhuangdao, China;Hebei Key Laboratory of Information Transmission and Signal Processing, Qinhuangdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Yanshan University, Qinhuangdao, China;Hebei Key Laboratory of Information Transmission and Signal Processing, Qinhuangdao, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Yanshan University, Qinhuangdao, China;Hebei Key Laboratory of Information Transmission and Signal Processing, Qinhuangdao, China;Corresponding author at: School of Information Science and Engineering, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Yanshan University, Qinhuangdao, China;Hebei Key Laboratory of Information Transmission and Signal Processing, Qinhuangdao, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Yanshan University, Qinhuangdao, China;Hebei Key Laboratory of Information Transmission and Signal Processing, Qinhuangdao, China"}], "References": [{"Title": "Speech Based Estimation of Parkinson’s Disease Using Gaussian Processes and Automatic Relevance Determination", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "401", "Issue": "", "Page": "173", "JournalTitle": "Neurocomputing"}, {"Title": "A novel multi-task linear mixed model for smartphone-based telemonitoring", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113809", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Efficient and accurate structural fusion of Bayesian networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "155", "JournalTitle": "Information Fusion"}, {"Title": "Causality-based Feature Selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Separation and recovery Markov boundary discovery and its application in EEG-based emotion recognition", "Authors": "Xingyu Wu; Bingbing Jiang; <PERSON><PERSON>", "PubYear": 2021, "Volume": "571", "Issue": "", "Page": "262", "JournalTitle": "Information Sciences"}, {"Title": "Adaptive feature selection with shapley and hypothetical testing: Case study of EEG feature engineering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "586", "Issue": "", "Page": "374", "JournalTitle": "Information Sciences"}, {"Title": "Error-aware <PERSON><PERSON> blanket learning for causal feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "849", "JournalTitle": "Information Sciences"}, {"Title": "Causal GraphSAGE: A robust graph method for classification based on causal sampling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "108696", "JournalTitle": "Pattern Recognition"}, {"Title": "Temporal causality-based feature selection for fault prediction in rotorcraft flight controls", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "235", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Progress prediction of Parkinson's disease based on graph wavelet transform and attention weighted random forest", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117483", "JournalTitle": "Expert Systems with Applications"}, {"Title": "The Choquet integral-based <PERSON><PERSON><PERSON>y function for n-person cooperative games with probabilistic hesitant fuzzy coalitions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118882", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Energy management in residential microgrid using model predictive control-based reinforcement learning and Shapley value", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105793", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A voice feature extraction method based on fractional attribute topology for Parkinson’s disease detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "219", "Issue": "", "Page": "119650", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Research on the multi-source causal feature selection method based on multiple causal relevance", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "265", "Issue": "", "Page": "110334", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Voice feature description of <PERSON>’s disease based on co-occurrence direction attribute topology", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106097", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A local dynamic feature selection fusion method for voice diagnosis of Parkinson's disease", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "", "Page": "101536", "JournalTitle": "Computer Speech & Language"}]}, {"ArticleId": 111057447, "Title": "Comparative Analysis of Some Mathematical Models Ignition of Hydrogen-Oxygen Mixtures", "Abstract": "This paper presents a comparative analysis of three mathematical models of chemical transformation describing the ignition of hydrogen-oxygen mixtures. Using the example of solving the problem of determining the ignition induction period of a hydrogen-oxygen mixture in an adiabatic reactor, three kinetic schemes of hydrogen combustion were tested, consisting of sixteen, forty-four and sixty reactions, respectively.<PERSON><PERSON><PERSON> In order to select the optimal kinetic scheme of hydrogen ignition and combustion, as well as the rate constants of chemical reactions included in the kinetic scheme, the calculation results were compared with known experimental data from different authors.Gorenje The calculations have shown that the most accurate description of experimental data on the delay times of an adiabatic explosion at high initial temperatures can be obtained using the kinetics of hydrogen oxidation, consisting of sixteen reactions. All three kinetic models of hydrogen oxidation give similar values of the mixture temperatures when the process enters the stationary mode. © 2023 South Ural State University. All rights reserved.", "Keywords": "hydrogen; ignition; induction period; kinetic models", "DOI": "10.14529/mmp230203", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111057544, "Title": "AI, Business and Digital Evolution", "Abstract": "<p><PERSON><PERSON><PERSON>, co-founder and CEO of Geeks, spoke to <PERSON> about her passion for helping today's businesses get the most from AI and her thoughts about its continuing evolution.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad114", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "So<PERSON>yeh <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111057694, "Title": "Design of a 180 nm CMOS Neuron Circuit with Soft‐Reset and Underflow Allowing for Loss‐Less Hardware Spiking Neural Networks", "Abstract": "Spiking neural networks (SNNs) have been researched as an alternative to reduce the gap with the human brain in terms of energy efficiency, due to their inherent spare event‐driven characteristics from a hardware implementation perspective. However, they still face significant challenges in learning, compared to artificial neural networks (ANNs). Recently, several algorithms have been developed to narrow the performance gap between SNNs and ANNs, including features in spiking neurons that can reduce information loss in the membrane potential. Inspired by these advancements, the current study designs and measures a neuron circuit using 180 nm complementary metal‐oxide‐semiconductor (CMOS) technology to address this information loss. The proposed circuit successfully implements these features, and their performance is validated through simulation based on the measured data.", "Keywords": "CMOS integrate-and-fire neuron circuits;overthreshold potential retaining;spiking neural networks;underflow allowing", "DOI": "10.1002/aisy.202300460", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering University of Seoul  Seoul 02504 South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering University of Seoul  Seoul 02504 South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering University of Seoul  Seoul 02504 South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of AI Semiconductor Engineering Korea University  Sejong 30019 South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Incheon National University  Incheon 22012 South Korea"}], "References": [{"Title": "A low-cost and high-speed hardware implementation of spiking neural network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "382", "Issue": "", "Page": "106", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111057790, "Title": "An APE1-mediated isothermal target cycling amplification for label-free and rapid detection of miRNA", "Abstract": "Developing simple, rapid and sensitive strategies for miRNA analysis is extremely important for diseases early diagnosis. Herein, an APE1-mediated isothermal target cycling amplification system combined with magnetic separation was established for label-free and rapid detection of miRNA. As a proof-of-concept, miR-1246 was selected as research model. In the detection system, when the miR-1246 is present, it hybridizes with AP probes of AP-MBs to form a double-stranded, in which the APE1 recognizes specific AP site of double-stranded and cleaves the AP probes, releasing a sequence containing a G-quadruplex (G4). After the cleavage, target miR-1246 can be released to hybridize with another AP of AP-MBs, triggering a continues cleavage reaction. After magnetic separation, label-free analysis of miRNA was achieved by measuring the fluorescence of G4/ThT. Due to the high efficiency and specificity of APE1 toward AP sites in dsDNAs, this method exhibits high specificity and sensitivity with the capability of distinguishing miRNAs with single-base mismatch. The system could detect miRNA with high sensitivity within 2 h, and the limit of detection (LOD) was calculated as 25.6 fM. Furthermore, high accuracy has been achieved through recovery experiments and successful attempts has been made in applying the approach to detect miR-1246 in serum samples from breast cancer patients and normal people. This method is expected to be an effective tool for miRNA-related research and clinical early diagnosis.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135029", "PubYear": 2024, "Volume": "401", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center of Clinical Laboratory, The First Affiliated Hospital of Jinan University, Guangzhou 510632, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center of Clinical Laboratory, The First Affiliated Hospital of Jinan University, Guangzhou 510632, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Clinical Laboratory, The First Affiliated Hospital of Jinan University, Guangzhou 510632, PR China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, PR China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Sensing Technology and Biomedical Instrument, School of Biomedical Engineering, Sun Yat-Sen University, Shenzhen 518107, PR China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China;Corresponding author at: NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, PR China"}], "References": []}, {"ArticleId": 111057827, "Title": "<PERSON><PERSON><PERSON> Aplikasi Food Waste Management pada Usaha Food and Beverages", "Abstract": "Pengelolaan sampah makanan pada banyak industri makanan dan minuman (Food and Beverages) seharusnya menjadi sebuah perhatian penting bagi perusahaan. FAO mencatat bahwa bisnis F&amp;B menjadi salah satu penyebab timbulnya sampah makanan baik dari tingkat produksi hingga konsumsi konsumen. Penelitian ini bertujuan untuk membantu bisnis F&amp;B memiliki sebuah aplikasi pengelolaan sampah makanan (Food Waste Management), sekaligus membantu merekomendasikan menu yang memiliki probabilitas munculnya sampah makanan lebih sedikit. Penelitian menggunakan teknik antrian dalam menangani stok bahan, melakukan audit sampah makanan dengan mendokumentasikan pengelompokan sampah yang dihasilkan. Hasil dari kedua fungsi sebelumnya menjadi input bagi proses rekomendasi menu dengan minimal sampah berdasarkan pendekatan Model Contextual dan Multi Criteria Decision Making (MCDM). Hasil menunjukkan mekanisme antrian dalam menangani stok bahan berhasil menurunkan COGS &nbsp;sebesar 24,8% . &nbsp;Audit sampah &nbsp;berhasil menghitung persentase food loss dan food waste yang muncul, dan rekomendasi menu berhasil memberikan alternatif terbaik yang meminimalisasi munculnya sampah.", "Keywords": "Food Waste Management; Queue; Waste Audit; Menu Recommendation", "DOI": "10.35143/jkt.v9i1.5840", "PubYear": 2023, "Volume": "", "Issue": "Vol. 9 No. 1 (2023)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}]