{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["this is a test\n"]}], "source": ["print(\"this is a test\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["开始使用VScode写python代码"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}