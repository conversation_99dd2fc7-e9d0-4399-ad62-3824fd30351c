[{"ArticleId": 114359139, "Title": "Erratum: A 1.8 V 115.52 dB Third-Order Discrete-Time Sigma-Delta Modulator Using Nested Chopper Technology", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218126624920014", "PubYear": 2024, "Volume": "33", "Issue": "9", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Henan University of Science and Technology, Luoyang 471023, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Henan University of Science and Technology, Luoyang 471023, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Henan University of Science and Technology, Luoyang 471023, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Henan University of Science and Technology, Luoyang 471023, P. R. China"}], "References": []}, {"ArticleId": 114359220, "Title": "Sustainable machining practices: a comparative study of MQL and MQL with wheel cleaning jet applied to different abrasives", "Abstract": "<p>With the increased use of polymeric products and, consequently, steel molds, the growth in grinding applications and the use of lubricoolant fluids is inevitable. As a result, studying different process configurations is of great interest to both the industry and society in general, by making it more economical and sustainable. This work focuses on evaluating the application of different lubricooling methods (conventional, MQL, and MQL + WCJ), cutting tools (green silicon carbide, black silicon carbide, and alumina), and cutting speeds (0.25 mm/min, 0.5 mm/min, and 0.75 mm/min) in the grinding of VP50IM. The goal is to identify the combination with the lowest cost and CO<sub>2</sub> production, while assessing its performance through output parameters such as surface roughness, roundness error, G ratio, grinding power, cost analysis, and CO<sub>2</sub> emission. Furthermore, SEM images were taken to analyze the machined surface of the workpiece. Based on this data, it was determined that the conventional method still achieved the highest machining efficiency, even though it was more expensive and more polluting. However, the MQL + WCJ technique significantly reduced costs, up to 45%, and CO<sub>2</sub> emissions by 67.5%, even though it presented slightly lower performance compared to the conventional method. On the other hand, the MQL system obtained the worst results due to its deficiency in wheel cleaning and heat removal from the cutting zone. It was also observed that increasing the process speed reduces costs and gas emissions, but directly affects performance since machining intensity increases, resulting in lower quality. Finally, the tool with the best heat conduction capacity, green silicon carbide, led to the best process performance, while the alumina tool, with lower thermal conductivity, achieved the poorest results.</p>", "Keywords": "Grinding; Abrasives; VP50IM steel; Cutting fluid; MQL; Wheel cleaning jet; Environment", "DOI": "10.1007/s00170-024-13521-y", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, São Paulo, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, São Paulo, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, São Paulo, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Control and Industrial Processes, Federal Institute of Education, Science and Technology of Paraná, Paraná, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, São Paulo, Brazil"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, São Paulo, Brazil"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, São Paulo, Brazil"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, São Paulo, Brazil; Corresponding author."}], "References": [{"Title": "Grinding process applied to workpieces with different geometries interrupted using CBN wheel", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1265", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Grinding assessment of workpieces with different interrupted geometries using aluminum oxide wheel with vitrified bond", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "3", "Page": "931", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Grinding performance of hardened steel: a study about the application of different cutting fluids with corrosion inhibitor", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "9-10", "Page": "2741", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "New knowledge about grinding using MQL simultaneous to cooled air and MQL combined to wheel cleaning jet technique", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "Page": "905", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Evaluation of a cooled wheel cleaning jet in minimum quantity lubrication grinding process", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "5-6", "Page": "1303", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Grinding comparative analysis between different proportions of water-oil applied to MQL technique and industrial production cost towards a green manufacturing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "5-6", "Page": "1281", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Eco-friendly manufacturing towards the industry of the future with a focus on less cutting fluid and high workpiece quality applied to the grinding process", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "3-4", "Page": "1163", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Grinding behavior of VP50IM steel using green and black silicon carbide compared to aluminum oxide wheel under different feed rates", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "9-10", "Page": "2639", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Manufacturing process linked to the MQL compared to flood lubrication applied to the grinding of VP50IM steel using black silicon carbide wheel", "Authors": "<PERSON>; <PERSON>; Stasys <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "5-6", "Page": "4179", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Evaluating the effect of MQL technique in grinding VP50IM steel with green carbide wheel", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "11-12", "Page": "7287", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Green manufacturing concept applied to the grinding process of advanced ceramics using an alternative lubri-refrigeration technique", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "7-8", "Page": "2771", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Wheel cleaning jet (WCJ) strategy for green grinding: mitigating greenhouse impact in VP50IM steel machining with green silicon carbide wheel", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "129", "Issue": "5-6", "Page": "2125", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Wheel cleaning jet (WCJ) strategy for green grinding: mitigating greenhouse impact in VP50IM steel machining with green silicon carbide wheel", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "129", "Issue": "5-6", "Page": "2125", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Grinding effect of thermoplastic mold steel using green manufacturing concepts combined with various conventional wheels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "129", "Issue": "5-6", "Page": "2443", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "An experimental evaluation between pure and diluted MQL versus flood lubri-cooling focused on cost and environmental impact", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "129", "Issue": "5-6", "Page": "2691", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Sustainable grinding: mitigating CO2 emissions through MQL+WCJ technique in AISI VP50 steel processing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "129", "Issue": "11-12", "Page": "5421", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Reducing carbon footprint in grinding: exploring green manufacturing to mitigate CO2 emission from cutting fluids", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "129", "Issue": "11-12", "Page": "5691", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114359228, "Title": "Hidden <PERSON><PERSON> guided Deep Learning models for forecasting highly volatile agricultural commodity prices", "Abstract": "Predicting agricultural commodity prices accurately is of utmost importance due to various factors such as perishability, seasonality, production uncertainty etc . Moreover, the substantial volatility that may be exhibited in time series further adds to the complexity and constitutes a significant challenge. In this paper, a Hidden Markov (HM) guided Deep Learning (DL) models has been developed on nonlinear and nonstationary price data of agricultural commodities for forecasting by considering technical indicators viz ., Moving Average (MA), Bollinger Bands (BB), Moving Average Convergence Divergence (MACD), Exponential MA (EMA) and Fast Fourier Transformation (FFT). HM Models (HMMs) can effectively handle the sequential dependencies and hidden states, while DL approach can learn complex patterns and relationships within the price series and thus the drawback of lack of generalization capability in the DL model has been overcome by HMM. In this study, the Potato price data of the Champadanga district of West Bengal, India has been utilized to assess the performance of the proposed technique. HMM has been combined with six baseline DL models viz ., Recurrent Neural Networks (RNN), Convolutional Neural Networks (CNN), Long Short-Term Memory (LSTM), Gated Recurrent Units (GRU), Bidirectional LSTM (BiLSTM) and Bidirectional GRU (BiGRU) for forecast modeling. Performance evaluation metrics viz ., Root Mean Squared Error (RMSE), Mean Absolute Percentage Error (MAPE), Mean Absolute Error (MAE) and the insightful Diebold–Mariano (DM) test revealed that Hidden Markov hybridized with DL models surpassed baseline DL models in forecasting accuracy for 1-week, 4-week, 8-week and 12-week ahead DL predictions. The proposed approach holds significant promise for enhancing the precision of agricultural commodity price forecasting with far-reaching implications for various stakeholders such as farmers and planners.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111557", "PubYear": 2024, "Volume": "158", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The Graduate School, ICAR - Indian Agricultural Research Institute, New Delhi, India;ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ICAR - National Academy of Agricultural Research Management, Hyderabad, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The Graduate School, ICAR - Indian Agricultural Research Institute, New Delhi, India;ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "The Graduate School, ICAR - Indian Agricultural Research Institute, New Delhi, India;ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}, {"AuthorId": 10, "Name": "<PERSON> <PERSON>", "Affiliation": "ICAR - Indian Agricultural Statistics Research Institute, New Delhi, India"}], "References": [{"Title": "A novel system for multi-step electricity price forecasting for electricity market management", "Authors": "Wen<PERSON> Yang; Jianzhou Wang; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106029", "JournalTitle": "Applied Soft Computing"}, {"Title": "Financial time series forecasting with deep learning : A systematic literature review: 2005–2019", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106181", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning for financial applications : A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106384", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Neural network enhanced hidden Markov model for tourism demand forecasting", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106465", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep Learning for Time Series Forecasting: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "3", "JournalTitle": "Big Data"}, {"Title": "Constructing a stock-price forecast CNN model with gold and crude oil indicators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107760", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep long short-term memory based model for agricultural price forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "4661", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 114359302, "Title": "Retraction Note: Customer centric hybrid recommendation system for E-Commerce applications by integrating hybrid sentiment analysis", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10660-024-09846-1", "PubYear": 2024, "Volume": "24", "Issue": "S1", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Financial and Actuarial Mathematics, School of Mathematics and Physics, Xi’an Jiaotong-Liverpool University, Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Studies, Institute of Public Enterprise, Hyderabad, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SBM’s <PERSON><PERSON><PERSON> School of Entrepreneurship and Family Business Management, SVKM’s NMIMS University, Mumbai, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Logistics, State University of Management, Moscow, Russia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Entrepreneurship and Logistics, Plekhanov Russian University of Economics, Moscow, Russia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, PSN College of Engineering and Technology, Tirunelveli, India; Corresponding author."}], "References": []}, {"ArticleId": 114359311, "Title": "Toward fast belief propagation for distributed constraint optimization problems via heuristic search", "Abstract": "<p>Belief propagation (BP) approaches, such as Max-sum and its variants, are important methods to solve large-scale Distributed Constraint Optimization Problems. However, these algorithms face a huge challenge since their computational complexity scales exponentially with the arity of each constraint function. Current accelerating techniques for BP use sorting or branch-and-bound (BnB) strategy to reduce the search space. However, the existing BnB-based methods are mainly designed for specific problems, which limits their applicability. On the other hand, though several generic sorting-based methods have been proposed, they require significantly high preprocessing as well as memory overhead, which prohibits their adoption in some realistic scenarios. In this paper, we aim to propose a series of generic and memory-efficient heuristic search techniques to accelerate belief propagation. Specifically, by leveraging dynamic programming, we efficiently build function estimations for every partial assignment scoped in a constraint function in the preprocessing phase. Then, by using these estimations to build upper bounds and employing a branch-and-bound in a depth-first fashion to reduce the search space, we propose our first method called FDSP. Next, we enhance FDSP by adapting a concurrent-search strategy and leveraging the upper bounds as guiding information and propose its first heuristic variant framework called CONC-FDSP. Finally, by choosing to expand the partial assignment with the highest upper bound in each step of exploration, we propose the second heuristic variant of FDSP, called BFS-FDSP. We prove the correctness of our methods theoretically, and our empirical evaluations indicate their superiority for accelerating Max-sum in terms of both time and memory, compared with the state-of-the-art.</p>", "Keywords": "DCOPs; Belief propagation; Max-sum; Heuristic search", "DOI": "10.1007/s10458-024-09643-y", "PubYear": 2024, "Volume": "38", "Issue": "1", "JournalId": 21187, "JournalTitle": "Autonomous Agents and Multi-Agent Systems", "ISSN": "1387-2532", "EISSN": "1573-7454", "Authors": [{"AuthorId": 1, "Name": "Junsong Gao", "Affiliation": "College of Computer Science, Chongqing University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science, Chongqing University, Chongqing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science, Chongqing University, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Chongqing University, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Chongqing University, Chongqing, China"}], "References": [{"Title": "Governing convergence of Max-sum on DCOPs through damping and splitting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "279", "Issue": "", "Page": "103212", "JournalTitle": "Artificial Intelligence"}, {"Title": "Utility distribution matters: enabling fast belief propagation for multi-agent optimization with dense local utility function", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "2", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}]}, {"ArticleId": 114359314, "Title": "Mellin transform-based D2D power optimization in 5G-enabled social IoT network", "Abstract": "<p>One of the main sources of information dissemination is social networks. Changes in social and Internet of Things (IoT) relationships can affect the quality of service (QoS) of device-to-device (D2D) communication in fifth-generation (5G) networks. Integration of social networks and IoT networks will allow users to improve their communication abilities with proximity users. The most challenging issues are interference and channel uncertainty due to massive connectivity and random movement of users in an urban dynamic environment. In this paper, D2D-based social Internet of Things (D2D–social IoT) in a 5G network is modeled for urban regions. To address these issues, we formulate a throughput maximization problem under the maximum power and QoS constraints. The problem is divided into multiple subproblems to reduce interference and enhance throughput. First, we leverage the interference channel link for intelligent resource sharing with better QoS by the spatiotemporal method. The second one involves bisection-based power search optimization by utilizing the <PERSON><PERSON> transformation method for latency and power minimization. Simulation results demonstrate that the proposed method significantly improves the network performance in terms of throughput, sum rate, and reduction in network latency by up to 19.91%, 26.24%, and 31.57%, respectively. It is expected that an inclusive implementation of the said method can enable resource allocation in 5G-enabled social IoT networks for the development of smart city.</p>", "Keywords": "Device-to-device (D2D) communication; Internet of things (IoT); Social IoT (SIoT); Mellin transform; Power consumption; 5G", "DOI": "10.1007/s11227-024-06061-5", "PubYear": 2024, "Volume": "80", "Issue": "11", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Wireless Sensor Network Lab, Department of Electronics and Communication Engineering, National Institute of Technology, Patna, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wireless Sensor Network Lab, Department of Electronics and Communication Engineering, National Institute of Technology, Patna, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Patna, India"}], "References": [{"Title": "ST-EUA: Spatio-temporal Edge User Allocation with Task Decomposition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Smart object recommendation based on topic learning and joint features in the social internet of things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "22", "JournalTitle": "Digital Communications and Networks"}]}, {"ArticleId": 114359327, "Title": "Electro-Thermopneumatically Actuated, Adhesion-Force Controllable Octopus-Like Suction Cup Actuator", "Abstract": "<p>A light-weight actuator developed in this work belongs to a class of soft robots, and in a sense, resembles an octopus. Its main function is in the attachment or detachment to a solid surface driven by an electro-thermopneumatic mechanism. In this study, a suction cup similar to that of an octopus is manufactured from an elastomer, which is actuated by an electro-thermopneumatic system, mimicking the movement of the octopus' acetabular muscle. Accordingly, the adhesion force generated by such an actuator is regulated by releasing the inner air or adjusting the cup's elasticity. This actuator is designed to be an assistive device that facilitates the individual's physical strength in case of conditions related to aging or cerebellar disease, or a person who lost limbs. In this study, the actuator capabilities are demonstrated in the form of a grip-assisting glove and prosthetic attacher. Moreover, the adhesion mechanism is quantified by numerical simulations and verified experimentally.</p>", "Keywords": "acetabulum;electro-thermopneumatic;octopus;soft actuator;suction cup", "DOI": "10.1089/soro.2023.0172", "PubYear": 2024, "Volume": "11", "Issue": "5", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Illinois at Chicago, Chicago, Illinois, USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Korea University, Seoul, Republic of Korea."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Korea University, Seoul, Republic of Korea."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, College of Science, King Sa<PERSON> University, Riyadh, Saudi Arabia."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, College of Science, King Sa<PERSON> University, Riyadh, Saudi Arabia."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON> An", "Affiliation": "SKKU Advanced Institute of Nanotechnology (SAINT), Department of Nano Engineering, and Department of Nano Science and Technology, Sungkyunkwan University, Suwon, Republic of Korea."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Illinois at Chicago, Chicago, Illinois, USA.;School of Mechanical Engineering, Korea University, Seoul, Republic of Korea."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Korea University, Seoul, Republic of Korea."}], "References": [{"Title": "Octopus Arm-Inspired Tapered Soft Actuators with <PERSON><PERSON> for Improved Grasping", "Authors": "<PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "5", "Page": "639", "JournalTitle": "Soft Robotics"}, {"Title": "Nanotextured Soft Electrothermo-Pneumatic Actuator for Constructing Lightweight, Integrated, and Untethered Soft Robotics", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; Chanwoo Park", "PubYear": 2022, "Volume": "9", "Issue": "5", "Page": "960", "JournalTitle": "Soft Robotics"}]}, {"ArticleId": 114359347, "Title": "Intelligent code search aids edge software development", "Abstract": "<p>The growth of multimedia applications poses new challenges to software facilities in edge computing. Developers must effectively develop edge computing software to accommodate the rapid expansion of multimedia applications. Code search has become a prevalent practice to enhance the efficiency of the construction of edge software infrastructure. Researchers have proposed lots of approaches for code search, and employed deep learning technology to extract features from program representations, such as token, AST, graphs, method name, and API. Nevertheless, two prominent issues remain: 1) there are only a few studies on the effective use of graph representation for code search (especially in Java language), and 2) there is a lack of empirical study on the contributions of different program representations. To address these issues, we conduct an empirical study to explore program representations, especially program graphs. To the best of our knowledge, this is the first attempt to conduct code search with mixed graphs representation for Java language, containing the control flow graph and the program dependence graph. We also present a hybrid approach to capture and fuse the features of a program with representations of T oken, A ST, and M ixed G raphs (TAMG) . The results of our experiment show that our approach possesses the best ability (R@1 with 37% and R@10 with 67.1%). Our graph representation exhibits a positive effect, and the token and AST also have a significant contribution to the code search. Our findings can aid developers in efficiently searching for the desired code while constructing the software infrastructure for edge computing, which is crucial for the rapid expansion of multimedia applications.</p>", "Keywords": "Cloud computing;Code retrieval;Multi-modal;Attention mechanism;Deep learning", "DOI": "10.1186/s13677-024-00629-5", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "Mengcheng Li", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Corps Hospital of the Chinese People’s Armed Police Forces, Guangzhou, China; Corresponding author."}], "References": [{"Title": "Intelligent software engineering in the context of agile software development: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "119", "Issue": "", "Page": "106241", "JournalTitle": "Information and Software Technology"}, {"Title": "Neural reverse engineering of stripped binaries using augmented control flow graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Opportunities and Challenges in Code Search Tools", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "deGraphCS : Embedding Variable-based Flow Graph for Neural Code Search", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Code Search: A Survey of Techniques for Finding Code", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "11", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 114359372, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0965-9978(24)00046-2", "PubYear": 2024, "Volume": "191", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [], "References": []}, {"ArticleId": 114359388, "Title": "Secure symbol-level precoding for reconfigurable intelligent surface-aided cell-free networks", "Abstract": "<p>With the improvement in communication network density, inter-cell interference has become severe. Due to the blurring of boundaries, cell-free networks are considered as a solution. However, it faces some challenges, such as high energy consumption due to the deployment of a large number of base stations, and security issues in complex communication scenarios. To tackle these issues, we propose a novel modeling scheme involving symbol-level precoding and reconfigurable intelligent surfaces (RIS). This solution can reduce the base station transmission power while ensuring communication layer security. Then, we decompose the non-convex problem of modeling into two sub-problems and solve them iteratively. The first sub-problem is to design symbol-level precoding which can be realized by an efficient gradient descent algorithm. The second sub-problem is about solving the reflection coefficients of RIS, which can be obtained by a Riemann conjugate gradient algorithm. In simulations, our proposed method outperforms benchmark methods. While ensuring physical layer security, the power consumption of cell-free networks has been reduced by 6 dBm.</p>", "Keywords": "Cell-free; Symbol-level precoding; Physical layer security; Reconfigurable intelligent surface; Multiple-input-multiple-outputs", "DOI": "10.1007/s11227-024-06059-z", "PubYear": 2024, "Volume": "80", "Issue": "11", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "Hongtai Yao", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China"}], "References": []}, {"ArticleId": 114359397, "Title": "Feature selection based on dynamic crow search algorithm for high-dimensional data classification", "Abstract": "High-dimensional biomedical data plays an important role in disease diagnosis and classification. To analyze the high-dimensional biomedical data, machine learning algorithms are widely used. However, a great amount of redundant, irrelevant or weak correlation features are prevalent in these datasets, which significantly deteriorate the capability of machine learning techniques. It is necessary to select the most informative features and discard the useless ones for enhancing the classification accuracy and reducing the dimensionality. To address this issue, an improved Crow Search Algorithm (CSA) which is known as the Dynamic CSA (DCSA) is proposed to find the optimal feature subset in high-dimensional classification tasks. In DCSA, three modifications are investigated. Firstly, we propose a dynamic bi-level awareness probability to guide the transition between global search and local search. Consequently, the balance between exploration and exploitation is improved. Secondly, levy flight with a unfixed step length control parameter is employed as the global search and the random search mechanism of the original CSA is abandoned. Thirdly, a dynamic flight length strategy is adopted to enhance the local search and speed up convergence. All in all, DCSA is developed as a wrapper feature selection model, where KNN classifier is applied as the feature subset evaluator. The performance of DCSA is measured on seven high-dimensional biomedical datasets. Experimental results show that DCSA outperforms other state-of-the-art methods. Specifically, DCSA achieved the highest average accuracy in six data. In terms of numbers of selected features, DCSA gained the smallest subset size in a seven data. And for SRBCT data, the prediction accuracy of DCSA is at 100%.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.123871", "PubYear": 2024, "Volume": "250", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Finance, Xi’an Jiaotong University, Xi’an Shannxi 710061, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Economics and Business Administration, Central China Normal University, Wuhan Hubei 430079, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Statistics and Data Science, JiangXi University of Finance and Economics, Nanchang JiangXi 330013, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Statistics and Data Science, JiangXi University of Finance and Economics, Nanchang JiangXi 330013, China"}], "References": [{"Title": "Feature selection strategy based on hybrid crow search optimization algorithm integrated with chaos theory and fuzzy c-means algorithm for medical diagnosis problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1565", "JournalTitle": "Soft Computing"}, {"Title": "An improved sine cosine algorithm to select features for text categorization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "4", "Page": "454", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Gene selection for cancer types classification using novel hybrid metaheuristics approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100661", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Island-based Crow Search Algorithm for solving optimal control problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106170", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Binary Crow Search Algorithm for Solving Two-dimensional Bin Packing Problem with Fixed Orientation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "809", "JournalTitle": "Procedia Computer Science"}, {"Title": "Enhanced Crow Search Algorithm for Feature Selection", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113572", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved grey wolf optimizer for solving engineering problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "113917", "JournalTitle": "Expert Systems with Applications"}, {"Title": "MTDE: An effective multi-trial vector-based differential evolution algorithm and its applications for engineering design problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106761", "JournalTitle": "Applied Soft Computing"}, {"Title": "A hybrid feature selection method based on information theory and binary butterfly optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "104079", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Feature selection using Binary Crow Search Algorithm with time varying flight length", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114288", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Bacterial colony algorithm with adaptive attribute learning strategy for feature selection in classification of customers for personalized recommendation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "747", "JournalTitle": "Neurocomputing"}, {"Title": "A novel wrapper-based feature subset selection method using modified binary differential evolution algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "565", "Issue": "", "Page": "278", "JournalTitle": "Information Sciences"}, {"Title": "B-MFO: A Binary Moth-Flame Optimization for Feature Selection from Medical Datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "11", "Page": "136", "JournalTitle": "Computers"}, {"Title": "Advanced strategies on update mechanism of Sine Cosine Optimization Algorithm for feature selection in classification problems", "Authors": "Gizem <PERSON>; Uğur Yüzgeç", "PubYear": 2022, "Volume": "107", "Issue": "", "Page": "104506", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An efficient harmonic estimator design based on Augmented Crow Search Algorithm in noisy environment", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "194", "Issue": "", "Page": "116470", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Novel Enhanced-Grey Wolf Optimization hybrid machine learning technique for biomedical data computation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>.P.<PERSON>", "PubYear": 2022, "Volume": "99", "Issue": "", "Page": "107778", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A hybrid feature selection approach for Microarray datasets using graph theoretic-based method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "615", "Issue": "", "Page": "449", "JournalTitle": "Information Sciences"}, {"Title": "Boosted crow search algorithm for handling multi-threshold image problems with application to X-ray images of COVID-19", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119095", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An exploitation-boosted sine cosine algorithm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105620", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Effective Feature Selection Strategy for Supervised Classification based on an Improved Binary Aquila Optimization Algorithm", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "181", "Issue": "", "Page": "109300", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A greedy-based crow search algorithm for semiconductor final testing scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "183", "Issue": "", "Page": "109423", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 114359429, "Title": "Spatiotemporal analysis of powder bed fusion melt pool monitoring videos using deep learning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10845-024-02355-w", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A convolutional approach to quality monitoring for laser manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "789", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Detecting voids in 3D printing using melt pool time series data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3", "Page": "845", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Towards real-time in-situ monitoring of hot-spot defects in L-PBF: a new classification-based method for fast video-imaging data analysis", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "293", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Perspectives of using machine learning in laser powder bed fusion for metal additive manufacturing", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "3", "Page": "372", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Deep semi-supervised learning of dynamics for anomaly detection in laser powder bed fusion", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "2", "Page": "457", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Additive Manufacturing In Situ and Ex Situ Geometric Data Registration", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "6", "Page": "", "JournalTitle": "Journal of Computing and Information Science in Engineering"}]}, {"ArticleId": 114359460, "Title": "Mining association between different emotion classes present in users posts of social media", "Abstract": "<p>Emotion analysis is a specialized aspect of sentiment analysis that involves assessing and comprehending the emotions conveyed within textual data. This analytical process, applied to users’ emotions, serves various practical purposes, including healthcare, public opinion analysis on diverse subjects, criminal detection, and personalized recommendations. In this work, two distinct approaches have been presented-a quantitative approach and a fuzzy approach-to extract meaningful rules that reveal associations between various emotion classes present in users’ posts on social media platforms. The proposed methodology is evaluated using a dataset of Twitter users, categorizing emotions according to <PERSON><PERSON>’s six classes. Several experiments have been conducted, considering different minimum support and minimum confidence thresholds. Promising results are obtained, as both methods effectively identify associations among different emotion classes present in users’ tweets. Identifying such associations can provide valuable insights, such as how emotion words present from any emotion class determines the presence of emotion words from other emotion class in the tweets of a user. Such findings will definitely be useful for the psychologist to study the emotion psychology of people and in the study of personality of people. </p>", "Keywords": "Sentiment analysis; Emotion analysis; Social media data mining; Fuzzy association rule", "DOI": "10.1007/s13278-024-01241-w", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Gauhati University, Jalukbari, Guwahati, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Gauhati University, Jalukbari, Guwahati, India; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Gauhati University, Jalukbari, Guwahati, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Gauhati University, Jalukbari, Guwahati, India"}], "References": [{"Title": "Opinion mining and emotion recognition applied to learning environments", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113265", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep learning based sentiment analysis of public perception of working from home through tweets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "60", "Issue": "1", "Page": "255", "JournalTitle": "Journal of Intelligent Information Systems"}]}, {"ArticleId": 114359951, "Title": "Evaluation of high quality and full employment based on CRITIC-entropy-TOPSIS multi-criteria framework", "Abstract": "Purpose The main aim of this paper is to establish a reasonable and scientific evaluation index system to assess the high quality and full employment (HQaFE). Design/methodology/approach This paper uses a novel Technique for Order of Preference by Similarity to Ideal Solution (TOPSIS) multi-criteria framework to evaluate the quality and quantity of employment, wherein the integrated weights of attributes are determined by the combined the Criteria Importance Through Inter-criteria Correlation (CRITIC) and entropy approaches. Findings Firstly, the gap in the Yangtze River Delta in employment quality is narrowing year by year; secondly, employment skills as well as employment supply and demand are the primary indicators that determine the HQaFE; finally, the evaluation scores are clearly hierarchical, in the order of Shanghai, Jiangsu, Zhejiang and Anhui. Originality/value A scientific and reasonable evaluation index system is constructed. A novel CRITIC-entropy-TOPSIS evaluation is proposed to make the results more objective. Some policy recommendations that can promote the achievement of HQaFE are proposed.", "Keywords": "Multi-criteria decision-making;Employment quality;Full employment;Yangtze River delta;Integrated weights;TOPSIS", "DOI": "10.1108/IJICC-11-2023-0342", "PubYear": 2024, "Volume": "17", "Issue": "3", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Wuxi Vocational College of Science and Technology , Wuxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ningbo University , Ningbo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ningbo University , Ningbo, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Wuxi Vocational College of Science and Technology , Wuxi, China"}], "References": [{"Title": "A hesitant fuzzy linguistic terms set-based AHP-TOPSIS approach to evaluate ERP software packages", "Authors": "Zeki <PERSON>; Funda Samanlioglu", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "54", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}, {"Title": "A property perceived service quality evaluation method for public buildings based on multisource heterogeneous information fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106070", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Evaluation of employment quality of college graduates based on interval MULTIMOORA with combination weights", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "4", "Page": "800", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}]}, {"ArticleId": 114360103, "Title": "Multi-constraint non-negative matrix factorization for community detection: orthogonal regular sparse constraint non-negative matrix factorization", "Abstract": "Community detection is an important method to analyze the characteristics and structure of community networks, which can excavate the potential links between nodes and further discover subgroups from complex networks. However, most of the existing methods only unilaterally consider the direct link topology without comprehensively considering the internal and external characteristics of the community as well as the result itself, which fails to maximize the access to the network information, thus affecting the effectiveness of community detection. To compensate for this deficiency, we propose a new community detection method based on multi-constraint non-negative matrix factorization, named orthogonal regular sparse constraint non-negative matrix factorization (ORSNMF). Based on the network topology, the ORSNMF algorithm models the differences of the outside of the community, the similarities of the nodes inside the community, and the sparseness of the community membership matrices at the same time, which together guides the iterative learning process to better reflect the underlying information and inherent attributes of the community structure in order to improve the correct rate of dividing subgroups. An algorithm with convergence guarantee is also proposed to solve the model, and finally a large number of comparative experiments are conducted, and the results show that the algorithm has good results.", "Keywords": "Community detection; Non-negative matrix factorization (NMF); Community dissimilarity; Node similarity", "DOI": "10.1007/s40747-024-01404-4", "PubYear": 2024, "Volume": "10", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Security and Information Law, Chongqing University of Posts and Telecommunications, Chongqing, China; Intelligent Policing Key Laboratory of Sichuan Province, Sichuan Police College, Sichuan, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Cyber Security and Information Law, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Intelligent Policing Key Laboratory of Sichuan Province, Sichuan Police College, Sichuan, China; Corresponding author."}, {"AuthorId": 4, "Name": "Zhen<PERSON> Zhang", "Affiliation": "School of Cyber Security and Information Law, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON>g <PERSON>", "Affiliation": "School of Cyber Security and Information Law, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Santa Clara University, Santa Clara, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyberspace Security, Beijing University of Posts and Telecommunications, Beijing, China"}], "References": [{"Title": "Collaborative filtering recommendation algorithm based on user correlation and evolutionary clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "147", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Community detection in node-attributed social networks: A survey", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "", "Page": "100286", "JournalTitle": "Computer Science Review"}, {"Title": "Community detection method using improved density peak clustering and nonnegative matrix factorization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "247", "JournalTitle": "Neurocomputing"}, {"Title": "Similarity preserving overlapping community detection in signed networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "275", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "<PERSON>ust semi-supervised non-negative matrix factorization for binary subspace learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "2", "Page": "753", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A novel density deviation multi-peaks automatic clustering algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "177", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Constraint-Induced Symmetric Nonnegative Matrix Factorization for Accurate Community Detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "89", "Issue": "", "Page": "588", "JournalTitle": "Information Fusion"}, {"Title": "Differential evolution-based transfer rough clustering algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "5", "Page": "5033", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": *********, "Title": "Book review", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.envsoft.2024.106023", "PubYear": 2024, "Volume": "176", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Capability Systems Centre, School of Systems and Computing, University of New South Wales Canberra, Australia"}], "References": [{"Title": "Model credibility revisited: Concepts and considerations for appropriate trust", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "3", "Page": "312", "JournalTitle": "Journal of Simulation"}, {"Title": "Fit-for-purpose environmental modeling: Targeting the intersection of usability, reliability and feasibility", "Authors": "<PERSON>; <PERSON>; Danial <PERSON><PERSON>", "PubYear": 2022, "Volume": "148", "Issue": "", "Page": "105278", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": *********, "Title": "An analytical model for predicting the depth of subsurface plastic deformation during cutting titanium alloy", "Abstract": "<p>The cutting subsurface plastic deformation layer of titanium alloys has a serious influence on fatigue performance. Hence, it is necessary to establish a predicting model of the depth of subsurface plastic deformation. However, empirical models are difficult to be applied for different cutting methods and are controlled by various cutting parameters. This paper establishes an analytical model for predicting the depth of subsurface plastic deformation based on cutting force. In this case, as long as the cutting force is known, the analytical model can be used to predict the depth of subsurface plastic deformation layer for various cutting conditions. In experiments, the depth of subsurface plastic deformation was measured by using a scanning electron microscope (SEM) and electron back-scatter diffraction (EBSD). The measured and predicted values are closed, and the average prediction error is only 16.01%. Therefore, the analytical model is reliable and useful to predict the depth of subsurface plastic deformation during cutting titanium alloys. This study will have an important application value to control the depth of subsurface plastic deformation to improve fatigue performance.</p>", "Keywords": "Prediction model; The depth of subsurface plastic deformation; Titanium alloy; Cutting", "DOI": "10.1007/s00170-024-13449-3", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University, Shenyang, China; School of Mechanical Engineering, Shenyang Ligong University, Shenyang, China; KeJi Development Corporation of Shenyang Ligong University, Shenyang, China; Corresponding author."}, {"AuthorId": 2, "Name": "Lidong Bai", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University, Shenyang, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "AECC Shenyang Engine Research Institute, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "AECC Shenyang Engine Research Institute, Shenyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University, Shenyang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shenyang Ligong University, Shenyang, China; KeJi Development Corporation of Shenyang Ligong University, Shenyang, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University, Shenyang, China"}], "References": [{"Title": "Formation mechanism of surface metamorphic layer and influence rule on milling TC17 titanium alloy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "7-8", "Page": "2259", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Insights into the fatigue property of titanium alloy Ti-6Al-4V in aero-engine from the subsurface damages induced by milling: state of the art", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "5-6", "Page": "1229", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Microstructural and mechanical property investigation of machined surface layer in high-speed milling of Ti-6Al-4V alloy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "5-6", "Page": "1707", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114360279, "Title": "Mast<PERSON>", "Abstract": "", "Keywords": "", "DOI": "10.1109/MAHC.2023.3343581", "PubYear": 2024, "Volume": "46", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [], "References": []}, {"ArticleId": 114360300, "Title": "Encoder-decoder networks with guided transmission map for effective image dehazing", "Abstract": "<p>A plain-architecture and effective image dehazing scheme, called Encoder-Decoder Network with Guided Transmission Map (EDN-GTM), is proposed in this paper. Nowadays, neural networks are often built based on complex architectures and modules, which inherently prevent them from being efficiently deployed on general mobile platforms that are not integrated with latest deep learning operators. Hence, from a practical point of view, plain-architecture networks would be more appropriate for implementation. To this end, we aim to develop non-sophisticated networks with effective dehazing performance. A vanilla U-Net is adopted as a starting baseline, then extensive analyses have been conducted to derive appropriate training settings and architectural features that can optimize dehazing effectiveness. As a result, several modifications are applied to the baseline such as plugging spatial pyramid pooling to the bottleneck and replacing ReLU activation with Swish activation. Moreover, we found that the transmission feature estimated by Dark Channel Prior (DCP) can be utilized as an additional prior for a generative network to recover appealing haze-free images. Experimental results on various benchmark datasets have shown that the proposed EDN-GTM scheme can achieve state-of-the-art dehazing results as compared to prevailing dehazing methods which are built upon complex architectures. In addition, the proposed EDN-GTM model can be combined with YOLOv4 to witness an improvement in object detection performance in hazy weather conditions. The code of this work is publicly available at https://github.com/tranleanh/edn-gtm .</p>", "Keywords": "Image dehazing; Dark channel prior; Spatial pyramid pooling; U-Net; Generative adversarial networks", "DOI": "10.1007/s00371-024-03330-5", "PubYear": 2025, "Volume": "41", "Issue": "1", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "Le<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Myongji University, Yongin, South Korea"}, {"AuthorId": 2, "Name": "Dong-Chul Park", "Affiliation": "Department of Electronics Engineering, Myongji University, Yongin, South Korea; Corresponding author."}], "References": [{"Title": "Single Image Dehazing via Multi-scale Convolutional Neural Networks with Holistic Edges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "1", "Page": "240", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "You Only Look Yourself: Unsupervised and Untrained Single Image Dehazing Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "5", "Page": "1754", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "A novel encoder-decoder network with guided transmission map for single image dehazing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Se<PERSON>yong <PERSON>; Dong-Chul Park", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "682", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 114360353, "Title": "Automating Feature Extraction from Entity-Relation Models: Experimental Evaluation of Machine Learning Methods for Relational Learning", "Abstract": "<p>With the exponential growth of data, extracting actionable insights becomes resource-intensive. In many organizations, normalized relational databases store a significant portion of this data, where tables are interconnected through some relations. This paper explores relational learning, which involves joining and merging database tables, often normalized in the third normal form. The subsequent processing includes extracting features and utilizing them in machine learning (ML) models. In this paper, we experiment with the propositionalization algorithm (i.e., Wordification) for feature engineering. Next, we compare the algorithms PropDRM and PropStar, which are designed explicitly for multi-relational data mining, to traditional machine learning algorithms. Based on the performed experiments, we concluded that G<PERSON><PERSON> <PERSON><PERSON>, compared to PropDRM, achieves similar performance (F1 score, accuracy, and AUC) on multiple datasets. PropStar consistently underperformed on some datasets while being comparable to the other algorithms on others. In summary, the propositionalization algorithm for feature extraction makes it feasible to apply traditional ML algorithms for relational learning directly. In contrast, approaches tailored specifically for relational learning still face challenges in scalability, interpretability, and efficiency. These findings have a practical impact that can help speed up the adoption of machine learning in business contexts where data is stored in relational format without requiring domain-specific feature extraction.</p>", "Keywords": "", "DOI": "10.3390/bdcc8040039", "PubYear": 2024, "Volume": "8", "Issue": "4", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Ss Cyril and Methodius University, 1000 Skopje, North Macedonia; Magix.AI, 1000 Skopje, North Macedonia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Ss Cyril and Methodius University, 1000 Skopje, North Macedonia; Magix.AI, 1000 Skopje, North Macedonia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Ss Cyril and Methodius University, 1000 Skopje, North Macedonia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Ss Cyril and Methodius University, 1000 Skopje, North Macedonia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Ss Cyril and Methodius University, 1000 Skopje, North Macedonia; Magix.AI, 1000 Skopje, North Macedonia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Ss Cyril and Methodius University, 1000 Skopje, North Macedonia; Magix.AI, 1000 Skopje, North Macedonia"}], "References": [{"Title": "Propositionalization and embeddings: two sides of the same coin", "Authors": "Nada Lavrač; <PERSON><PERSON><PERSON>; <PERSON><PERSON>-Šikonja", "PubYear": 2020, "Volume": "109", "Issue": "7", "Page": "1465", "JournalTitle": "Machine Learning"}, {"Title": "Cost Optimization for Big Data Workloads Based on Dynamic Scheduling and Cluster-Size Tuning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "", "Page": "100203", "JournalTitle": "Big Data Research"}, {"Title": "Beyond graph neural networks with lifted relational neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "7", "Page": "1695", "JournalTitle": "Machine Learning"}, {"Title": "Relational data embeddings for feature enrichment with background information", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "112", "Issue": "2", "Page": "687", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 114360440, "Title": "IEEE Computer Society Has You Covered!", "Abstract": "", "Keywords": "", "DOI": "10.1109/MAHC.2024.3374486", "PubYear": 2024, "Volume": "46", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [], "References": []}, {"ArticleId": 114360460, "Title": "Estimated camera trajectory-based integration among local 3D models sequentially generated from image sequences by SfM–MVS", "Abstract": "<p>This paper describes a three-dimensional (3D) modeling method for sequentially and spatially understanding situations in unknown environments from an image sequence acquired from a camera. The proposed method chronologically divides the image sequence into sub-image sequences by the number of images, generates local 3D models from the sub-image sequences by the Structure from Motion and Multi-View Stereo (SfM–MVS), and integrates the models. Images in each sub-image sequence partially overlap with previous and subsequent sub-image sequences. The local 3D models are integrated into a 3D model using transformation parameters computed from camera trajectories estimated by the SfM–MVS. In our experiment, we quantitatively compared the quality of integrated models with a 3D model generated from all images in a batch and the computational time to obtain these models using three real data sets acquired from a camera. Consequently, the proposed method can generate a quality integrated model that is compared with a 3D model using all images in a batch by the SfM–MVS and reduce the computational time.</p>", "Keywords": "Photogrammetry; Structure from motion; Multi-view stereo; Integration; Decommissioning", "DOI": "10.1007/s10015-024-00949-4", "PubYear": 2024, "Volume": "29", "Issue": "2", "JournalId": 4137, "JournalTitle": "Artificial Life and Robotics", "ISSN": "1433-5298", "EISSN": "1614-7456", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Japan Atomic Energy Agency, Futaba, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Japan Atomic Energy Agency, Futaba, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Japan Atomic Energy Agency, Futaba, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Sapporo University, Sapporo, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Japan Atomic Energy Agency, Futaba, Japan"}], "References": [{"Title": "3D reconstruction considering calculation time reduction for linear trajectory shooting and accuracy verification with simulator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "28", "Issue": "2", "Page": "352", "JournalTitle": "Artificial Life and Robotics"}, {"Title": "3D Gaussian Splatting for Real-Time Radiance Field Rendering", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Integration of 3D environment models generated from the sections of the image sequence based on the consistency of the estimated camera trajectories", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "2", "Page": "11281", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 114360483, "Title": "Combating Misinformation and Polarization in the Corporate Sphere: Integrating Social, Technological and AI Strategies", "Abstract": "", "Keywords": "", "DOI": "10.9781/ijimai.2024.03.003", "PubYear": 2024, "Volume": "In press", "Issue": "In press", "JournalId": 20387, "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence", "ISSN": "", "EISSN": "1989-1660", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Politécnica de Madrid"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "York St. John University"}, {"AuthorId": 3, "Name": "Ziba <PERSON>", "Affiliation": "Universidad Politécnica de Madrid"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universidad Politécnica de Madrid"}], "References": []}, {"ArticleId": 114360607, "Title": "Medical image security by crypto watermarking using enhanced chaos and fruit fly optimization algorithm with SWT and SVD", "Abstract": "<p>Telemedicine is the field that uses medical images for diagnosing various diseases. The transmitting and storing of medical images via the cloud-based network must meet several stringent criteria, including confidentiality, validity, and security, and are viewed as very sensitive, irrespective of the image processing context. Medical image copyright protection has been essential since little alterations can even put the lives of patients in danger. Hence numerous significant watermarking methods need to be developed. Watermarking conceals sensitive data by embedding it in a harmless medium, such as a cover. The challenging issue of quick and extremely safe image encryption can be solved more effectively with chaos-based cryptography. Thus the image is encrypted by using enhanced chaos with the Fruit Fly Optimization Algorithm (FFOA). The work proposes an efficient medical image watermarking approach using the Two-level Stationary Wavelet Transform (SWT) and Singular Value Decomposition (SVD) technique on chaotic encrypted medical images. Finally, the proposed method is compared with existing methods to demonstrate higher performance. To check robustness and imperceptibility results, the work is examined under various attacks and produces good results in Peak Signal-to-Noise Ratio (PSNR) and Normalized Cross Correlation (NCC) measures. For different kinds of medical images, the PSNR of the suggested methodology is greater than 40 dB, and NCC values are close to 1 illustrating the technique's superior efficacy.</p>", "Keywords": "Watermarking; Chaos; Encryption; FFOA; SWT; SVD", "DOI": "10.1007/s11042-024-19019-9", "PubYear": 2024, "Volume": "83", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Abirami R", "Affiliation": "Department of Networking and Communications, School of Computing, SRM Institute of Science and Technology, Kattankulathur, India; Corresponding author."}, {"AuthorId": 2, "Name": "Malathy C", "Affiliation": "Department of Networking and Communications, School of Computing, SRM Institute of Science and Technology, Kattankulathur, India"}], "References": [{"Title": "EMOTE – Multilayered encryption system for protecting medical images based on binary curve", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "676", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Secure Watermarking Scheme for Color DICOM Images in Telemedicine Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "2", "Page": "2525", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Robust zero-watermarking algorithm for medical images based on SIFT and Bandelet-DCT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "12", "Page": "16863", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust, imperceptible and optimized watermarking of DICOM image using Schur decomposition, LWT-DCT-SVD and its authentication using SURF", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "11", "Page": "16555", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A robust encryption watermarking algorithm for medical images based on ridgelet-DCT and THM double chaos", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "A robust and secured fusion based hybrid medical image watermarking approach using RDWT-DWT-MSVD with Hyperchaotic system-Fibonacci Q Matrix encryption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "24", "Page": "37479", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": *********, "Title": "Phishing Detection Using Machine Learning Algorithm", "Abstract": "Phishing is a criminal scheme to steal the user’s personal data and other credential information. It is a fraud that acquires victim’s confidential information such as password, bank account detail, credit card number, financial username and password etc. and later it can be misuse by attacker. The use of machine learning algorithms in phishing detection has gained significant attention in recent years. This research paper aims to evaluate the effectiveness of various machine learning algorithms in detecting phishing URL’s/website. The algorithms tested in this study are Decision Tree, Random Forest, Multilayer Perceptron, XGBoost, Autoencoder Neural Network, and Support Vector Machines. A dataset of phishing URLs is used to train and test the algorithms, and their performance is evaluated based on metrics such as accuracy, precision, recall, and F1 Score. The paper takes in data of phished URL from Phishtank and legitimate URL from University of New Brunswick. The results of this study demonstrate that the Random Forest and XGBoost algorithms outperforms other algorithms in terms of accuracy and other performance metrics and the system has an overall accuracy of 98 %.", "Keywords": "Phishing Detection;Feature Collection;Feature Selection;Classification;Machine Learning;Explainable AI;Data Sets", "DOI": "10.32628/CSEIT2410228", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Information Technology, Dwarkadas <PERSON> College of Engineering, Mumbai, Maharashtra, India"}, {"AuthorId": 2, "Name": " <PERSON>", "Affiliation": "Information Technology, Dwarkadas <PERSON> College of Engineering, Mumbai, Maharashtra, India"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Technology, Dwarkadas <PERSON> College of Engineering, Mumbai, Maharashtra, India"}], "References": []}, {"ArticleId": 114360762, "Title": "AI Literacy for the top management: An upper echelons perspective on corporate AI orientation and implementation ability", "Abstract": "<p>We draw on upper echelons theory to examine whether the AI literacy of a firm’s top management team (i.e., TMT AI literacy) has an effect on two firm characteristics paramount for value generation with AI—a firm’s AI orientation, enabling it to identify AI value potentials, and a firm’s AI implementation ability, empowering it to realize these value potentials. Building on the notion that TMT effects are contingent upon firm contexts, we consider the moderating influence of a firm’s type (i.e., startups vs. incumbents). To investigate these relationships, we leverage observational literacy data of 6986 executives from a professional social network (LinkedIn.com) and firm data from 10-K statements. Our findings indicate that TMT AI literacy positively affects AI orientation as well as AI implementation ability and that AI orientation mediates the effect of TMT AI literacy on AI implementation ability. Further, we show that the effect of TMT AI literacy on AI implementation ability is stronger in startups than in incumbent firms. We contribute to upper echelons literature by introducing AI literacy as a skill-oriented perspective on TMTs, which complements prior role-oriented TMT research, and by detailing AI literacy’s role for the upper echelons-based mechanism that explains value generation with AI.</p>", "Keywords": "AI orientation; AI implementation; AI literacy; Attention-based view; Upper echelons theory; M15; O30; L22", "DOI": "10.1007/s12525-024-00707-1", "PubYear": 2024, "Volume": "34", "Issue": "1", "JournalId": 16578, "JournalTitle": "Electronic Markets", "ISSN": "1019-6781", "EISSN": "1422-8890", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Information Systems & Electronic Services, Technical University of Darmstadt, Darmstadt, Germany; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Information Systems & Electronic Services, Technical University of Darmstadt, Darmstadt, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Information Systems & Electronic Services, Technical University of Darmstadt, Darmstadt, Germany"}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "An Exploration into Future Business Process Management Capabilities in View of Digitalization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "2", "Page": "83", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "The strategic impacts of Intelligent Automation for knowledge and service work: An interdisciplinary review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "4", "Page": "101600", "JournalTitle": "The Journal of Strategic Information Systems"}, {"Title": "Explainable Artificial Intelligence: Objectives, Stakeholders, and Future Research Opportunities", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "53", "JournalTitle": "Information Systems Management"}, {"Title": "Ready or Not, AI Comes— An Interview Study of Organizational AI Readiness Factors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "1", "Page": "5", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Artificial intelligence capability: Conceptualization, measurement calibration, and empirical study on its impact on organizational creativity and firm performance", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "3", "Page": "103434", "JournalTitle": "Information & Management"}, {"Title": "Machine learning and deep learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "3", "Page": "685", "JournalTitle": "Electronic Markets"}, {"Title": "A Differentiated Discussion About AI Education K-12", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "2", "Page": "131", "JournalTitle": "KI - Künstliche Intelligenz"}, {"Title": "Understanding dark side of artificial intelligence (AI) integrated business analytics: assessing firm’s operational inefficiency and competitiveness", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "3", "Page": "364", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Sprint Zeal or Sprint Fatigue? The Benefits and Burdens of Agile ISD Practices Use for Developer Well-Being", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "2", "Page": "557", "JournalTitle": "Information Systems Research"}, {"Title": "Conceptualizing AI literacy: An exploratory review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "100041", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Artificial Intelligence and its Impact on Leaders and Leadership", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "1024", "JournalTitle": "Procedia Computer Science"}, {"Title": "Measuring user competence in using artificial intelligence: validity and reliability of artificial intelligence literacy scale", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "9", "Page": "1324", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Managing paradoxes in b\n i‐modal \n information technology functions: A\n multi‐case \n study", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "6", "Page": "1177", "JournalTitle": "Information Systems Journal"}, {"Title": "Shifting ML value creation mechanisms: A process model of ML value creation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "3", "Page": "101734", "JournalTitle": "The Journal of Strategic Information Systems"}, {"Title": "Algorithmic Management", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "6", "Page": "825", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Human vs. Automated Sales Agents: How and Why Customer Responses Shift Across Sales Stages", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "3", "Page": "1148", "JournalTitle": "Information Systems Research"}]}, {"ArticleId": 114360784, "Title": "Synthesis and characteristics study of epoxy composites made with various stacking of jute fiber through hand layup route", "Abstract": "<p>Polymer-based advanced composite materials fulfill the industry’s needs. Attention to technological growth in different industry sectors is required for advanced materials with desired mechanical and thermal characteristics. The research aims to locate the consequence of jute fiber orientations (0, 10, 30, 50, 70, and 90°) on polymer composite’s mechanical and thermal adsorption properties developed by epoxy resin via hand layup technique with manual stirrer action. The mechanical characteristics like tensile, impact, and flexural strength of advanced composites are evaluated by ASTM test standards, and its experimental results are compared to 0° orientation. The thermal adsorption performance of jute fiber reinforced polymer composite is predicted by a thermo-gravimetric analyzer (TGA) and differential type scanning calorimeter (DSC). According to the test results, the epoxy composite made with jute fiber as 30° stacking orientation is exposed to maximum mechanical properties such as higher tensile, impact, and flexural strength of 265 ± 1.11 MPa, 0.84 ± 0.14 J, and 46.12 ± 2.09 MPa; this is better than the other epoxy composite samples. Likewise, the epoxy composite with 30–50° jute fiber stacking orientation is exposed to better thermal stability. An 80.19% thermal weight loss is spotted at 360 °C, leading to netter exothermal and endothermal effects.</p>", "Keywords": "Epoxy polymer resin; Jute fiber; Orientation; Tensile; impact; and flexural; Thermal adsorption properties", "DOI": "10.1007/s00170-024-13497-9", "PubYear": 2025, "Volume": "136", "Issue": "1", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, <PERSON><PERSON> College of Engineering, Trichy, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Sanjivani College of Engineering, Kopargaon, Ahmednagar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Karpaga Vinayaga College of Engineering and Technology, Chengalpattu, India"}, {"AuthorId": 4, "Name": "Priya Chathapuram Balasubramanian", "Affiliation": "Department of Mechanical Engineering, OASYS Institute of Technology, Trichy, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences (SIMATS), Saveetha University, Chennai, India; Corresponding author."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Kongunadu College of Engineering and Technology, Trichy, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Graphic Era (Deemed to be University), Dehradun, India; Department of Mechanical Engineering, Amity University Dubai, Dubai, United Arab Emirates"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Botany and Microbiology, College of Science, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Botany and Microbiology, College of Science, King Saud University, Riyadh, Saudi Arabia"}], "References": []}, {"ArticleId": 114360820, "Title": "An efficient Dense-Resnet for multimodal image fusion using medical image", "Abstract": "<p>Nowadays, the visual content of various medical images is increased through multimodal image fusion to gather more information from medical images. The complementary information available in various modalities is merged to increase the visual content of the image for quick medical diagnosis. However, the resultant fused multi-modality images suffered from different issues, like texture distortion and gradient, mainly for the affected region. Thus, a hybrid deep learning model, Dense-ResNet is designed for the fusing of multimodal medical images from different modalities in this research work. The images from three different modalities are collected initially, which are individually pre-processed by using a median filter. The pre-processed image of the spatial domain is transformed into a spectral domain by applying Dual-Tree Complex Wavelet Transform (DTCWT), and the transformed image is segmented using Edge-Attention Guidance Network (ET-Net). Finally, the multimodal fusion of medical images is performed on the segmented images using the designed Dense-ResNet model. Moreover, the superiority of the designed model is validated, which shows that the designed Dense-ResNet model outperforms as compared with other existing multimodal medical image fusion approaches. The Dense-ResNet model achieved 0.402 Mean Square Error (MSE), 0.634 Root Mean Square Error (RMSE), and 47.136 dB Peak Signal to Noise Ratio (PSNR).</p>", "Keywords": "Dual-Tree Complex Wavelet Transform; Edge-Attention Guidance Network; ResidualNet; DenseNet; Median filter", "DOI": "10.1007/s11042-024-18974-7", "PubYear": 2024, "Volume": "83", "Issue": "26", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering Department, Delhi Technological University, Delhi, India; Bhagwan Parshuram Institute of Technology, Delhi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering, Delhi Technological University, Delhi, India"}], "References": [{"Title": "Multi-modality medical image fusion technique using multi-objective differential evolution based deep neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2483", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 114360836, "Title": "Branch architecture quantification of large-scale coniferous forest plots using UAV-LiDAR data", "Abstract": "Branch architecture plays an important role in forest physical and ecological processes, greatly affecting forest spatial structure and the accumulation, production and distribution of organic carbon. Very few studies have quantified the branch architecture of large-scale forest plots, due to the lack of high-resolution large-scale three-dimensional data. The emergence of unmanned aerial vehicle-light detecting and ranging (UAV-LiDAR) provides great potential to overcome this limitation. However, due to insufficient quality of UAV-LiDAR data for branch reconstruction, existing algorithms remain tremendously challenging. We propose an effective branch reconstruction algorithm for UAV-LiDAR data to quantify branch architecture. First, individual branches are extracted using a region growth method that is guided by the branch growth direction and local smoothness constraint. Second, incorrect branches are eliminated based on three pieces of branch features, i.e., specific angles between branches at the same whorl, specific height differences between branches at different whorls, and the growth pattern of branches from the stem outward, reaching maximum distances at tips. Finally, branches are reconstructed using polynomial fitting, and branch architecture parameters are extracted based on branch models. The proposed algorithm simplifies branch reconstruction by skipping the separation of photosynthetic and non-photosynthetic components. It also adapts well to UAV-LiDAR point clouds, generating more realistic reconstructions. The algorithm successfully identified non-photosynthetic components in experiments involving 240 trees, including Scots pine, Norway spruce, and Radiata pine. The average overall accuracy for these three species was 0.88, 0.76, and 0.74, respectively. The proposed algorithm was tested for branch reconstruction using UAV-LiDAR data from a two-hectare Larix principis-rupprechtii plot. The accuracy of branch identification and parameter extraction accuracy were evaluated branch by branch based on the individual branches manually identified in terrestrial laser scanning data. Results showed the F- score of branch and stem identification was 0.58 and 1. The relative RMSE of branch length and angle was 36.87% and 18.3%, and ones of stem length and diameter were 1.46% and 6.16%. The proposed algorithm outperformed the well-known reconstruction algorithms, including TreeQSM, AdTree and Laplacian-based algorithms.", "Keywords": "", "DOI": "10.1016/j.rse.2024.114121", "PubYear": 2024, "Volume": "306", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Forest Resource Information Techniques, Chinese Academy of Forestry, Beijing 100091, China;Key Laboratory of Forestry Remote Sensing and Information System, National Forestry and Grassland Administration, Beijing 100091, China;State Key Laboratory of Information Engineering in Surveying, Mapping and Remote Sensing, Wuhan University, Wuhan 430079, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-Sen University, Guangzhou, China;Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Guangdong, China;Corresponding author at: School of Geospatial Engineering and Science, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-Sen University, Guangzhou, China;Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Guangdong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Forest Ecology-Environment and Nature Conservation, Chinese Academy of Forestry, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping and Remote Sensing, Wuhan University, Wuhan 430079, China;Corresponding author"}], "References": [{"Title": "Incorporating LiDAR metrics into a structure-based habitat model for a canopy-dwelling species", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "236", "Issue": "", "Page": "111499", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Virtual laser scanning with HELIOS++: A novel take on ray tracing-based simulation of topographic full-waveform 3D laser scanning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "269", "Issue": "", "Page": "112772", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Quantifying tropical forest structure through terrestrial and UAV laser scanning fusion in Australian rainforests", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "112912", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Modelling internal tree attributes for breeding applications in Douglas-fir progeny trials using RPAS-ALS", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "", "Page": "100072", "JournalTitle": "Science of Remote Sensing"}, {"Title": "A study of annual tree-wise LiDAR intensity patterns of boreal species observed using a hyper-temporal laser scanning time series", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "305", "Issue": "", "Page": "114083", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": *********, "Title": "Research on Multi-objective Optimization Model of Power Storage Materials Based on NSGA-II Algorithm", "Abstract": "<p>Aiming at the problems of slow convergence speed and low precision probability of multi-objective optimization of energy storage materials, a multi-objective optimization model of energy storage materials based on NSGA-II algorithm was proposed. The association rule set of storage materials in the joint supply chain operation performance management system is extracted, and the rough vector feature distribution set multi-objective optimization method is used to decompose and optimize the characteristics of storage materials in the joint supply chain operation performance management system. Using NSGA-II optimization analysis method, this paper summarizes the power storage materials under the joint supply chain operation performance management system, and summarizes three kinds of inventory control: periodic inventory, inventory coding, and computerized inventory. Combined with the positive regression learning method of organizational operational performance, the multi-objective optimization decision of electric storage materials under the joint supply chain operational performance management system is realized. The simulation results show that under the joint supply chain operation performance management system, the proposed method reaches the optimal convergence after 65 iterations, the convergence speed is fast, and the accuracy probability reaches 1.000 after 80 iterations, which solves the problems of slow convergence speed and low accuracy probability, and has a good scheduling ability of energy storage materials.</p>", "Keywords": "NSGA-II algorithm; Electric power storage materials; Multi-objective optimization; Rough vector; Joint supply chain; Operational performance management", "DOI": "10.1007/s44196-024-00454-3", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Hebei Electric Power Co., Ltd., Shijiazhuang Power Supply Branch, Hebei, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Hebei Electric Power Co., Ltd., Shijiazhuang Power Supply Branch, Hebei, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Grid Hebei Electric Power Co., Ltd., Hebei, China"}, {"AuthorId": 4, "Name": "Xiaodong Geng", "Affiliation": "State Grid Hebei Electric Power Co., Ltd., Shijiazhuang Power Supply Branch, Hebei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shijiazhuang Luancheng District Electric Power Supply Company, Hebei, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Pingshan Electric Power Supply Company, Hebei, China"}], "References": [{"Title": "A dynamic material distribution scheduling of automotive assembly line considering material-handling errors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "5", "Page": "1101", "JournalTitle": "Engineering Computations"}]}, {"ArticleId": *********, "Title": "Efficient and secure confidential transaction scheme based on commitment and aggregated zero-knowledge proofs", "Abstract": "", "Keywords": "", "DOI": "10.1080/23742917.2024.2336634", "PubYear": 2024, "Volume": "8", "Issue": "4", "JournalId": 16753, "JournalTitle": "Journal of Cyber Security Technology", "ISSN": "2374-2917", "EISSN": "2374-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, China;Guangxi Key Laboratory of Cryptography and Information Security, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, China;Guangxi Key Laboratory of Cryptography and Information Security, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, China;Guangxi Key Laboratory of Cryptography and Information Security, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, China;Guangxi Key Laboratory of Cryptography and Information Security, Guilin, China"}], "References": []}, {"ArticleId": 114361111, "Title": "Location-aware multi-code generator for remote sensing scene classification", "Abstract": "Many types of objects are crowdedly distributed on the surface of remote sensing scenes. Global semantics with object mixture and small training samples potentially cause difficulties in the classification of remote sensing scenes. The description of them often requires crucial parts of the scenes and corresponding discriminative features, especially as the convolutional neural network (CNN) goes deeper. In this paper, we propose a novel Location-Aware Multi-code Generator network (LAM-GAN) that incorporates multiple latent codes as the input to a generator network. This network is designed to train from scratch and recover most details of the input (real) image in a principled way. Meanwhile, multiple latent codes are reversely updated using K cluster centres located by the subsequently proposed part co-location module. By doing so, the global features of the real-fake image pair and part-level features are stacked and fed to a joint part classification network for discriminative classification. This approach makes it easier to induce the semantic concepts in a remote sensing scene. With this formulation, our approach generates an internal compact representation of the scene and enables weakly supervised part co-localization. The proposed method provides a unified framework for not only generating high-quality fake images but also facilitating the remote sensing scene classification task. We evaluated LAM-GAN on several benchmark datasets, and the experiment results demonstrate that the proposed method is more effective than previous state-of-the-art methods.", "Keywords": "Scene classification ; convolutional neural network ; location-aware ; multi-code generator ; part co-localization", "DOI": "10.1080/01431161.2024.2334773", "PubYear": 2024, "Volume": "45", "Issue": "8", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Wuhan Technology and Business University, Wuhan, China;School of Computer Science, Wuhan University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Wuhan Technology and Business University, Wuhan, China"}, {"AuthorId": 3, "Name": "Chengsong Hu", "Affiliation": "School of Artificial Intelligence, Wuhan Technology and Business University, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Wuhan Technology and Business University, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Health Administration and Policy, George Mason University, Fairfax, VA, USA"}], "References": [{"Title": "Adaptive Capsule Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "218", "Issue": "", "Page": "103405", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "SPARE: Self-supervised part erasing for ultra-fine-grained visual categorization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "108691", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 114361113, "Title": "Static video summarization with multi-objective constrained optimization", "Abstract": "<p>Video summarization is an emerging research field. In particular, static video summarization plays a major role in abstraction and indexing of video repositories. It extracts the vital events in a video such that it covers the entire content of the video. Frames having those important events are called keyframes which are eventually used in video indexing. It also helps in giving an abstract view of the video content such that the internet users are aware of the events present in the video before watching it completely. The proposed research work is focused on efficient static video summarization by extracting various visual features namely color, texture and shape features. These features are aggregated and clustered using a Density-Based Spatial Clustering of Applications with Noise (DBSCAN) algorithm. In order to produce good video summary by clustering, the parameters of DBSCAN algorithm are optimized by using a meta heuristic population based optimization called Artificial Algae Algorithm (AAA). The experimental results on two public datasets namely VSUMM and OVP dataset show that the proposed Static Video Summarization with Multi-objective Constrained Optimization (SVS_MCO) achieves better results when compared to existing methods.</p>", "Keywords": "Static video summarization; Keyframes; DBSCAN algorithm; Artificial Algae Algorithm; Multi-objective optimization", "DOI": "10.1007/s12652-024-04777-z", "PubYear": 2024, "Volume": "15", "Issue": "4", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Annamalai University, Chidambaram, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Annamalai University, Chidambaram, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Annamalai University, Chidambaram, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Annamalai University, Chidambaram, India"}], "References": [{"Title": "OPFSumm: on the video summarization using Optimum-Path Forest", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> G<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "15-16", "Page": "11195", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "GVSUM: generic video summarization using deep visual features", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "9", "Page": "14459", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Key moment extraction for designing an agglomerative clustering algorithm-based video summarization framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "7", "Page": "4881", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Graph-based structural difference analysis for video summarization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "483", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 114361144, "Title": "<PERSON><PERSON>bituary", "Abstract": "", "Keywords": "", "DOI": "10.1109/MAHC.2024.3366628", "PubYear": 2024, "Volume": "46", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer History Museum, Mountain View, CA, USA"}], "References": []}, {"ArticleId": 114361146, "Title": "A self‐learning human‐machine cooperative control method based on driver intention recognition", "Abstract": "Human‐machine cooperative control has become an important area of intelligent driving, where driver intention recognition and dynamic control authority allocation are key factors for improving the performance of cooperative decision‐making and control. In this paper, an online learning method is proposed for human‐machine cooperative control, which introduces a priority control parameter in the reward function to achieve optimal allocation of control authority under different driver intentions and driving safety conditions. Firstly, a two‐layer LSTM‐based sequence prediction algorithm is proposed to recognise the driver's lane change (LC) intention for human‐machine cooperative steering control. Secondly, an online reinforcement learning method is developed for optimising the steering authority to reduce driver workload and improve driving safety. The driver‐in‐the‐loop simulation results show that our method can accurately predict the driver's LC intention in cooperative driving and effectively compensate for the driver's non‐optimal driving actions. The experimental results on a real intelligent vehicle further demonstrate the online optimisation capability of the proposed RL‐based control authority allocation algorithm and its effectiveness in improving driving safety.", "Keywords": "", "DOI": "10.1049/cit2.12313", "PubYear": 2024, "Volume": "9", "Issue": "5", "JournalId": 26444, "JournalTitle": "CAAI Transactions on Intelligence Technology", "ISSN": "2468-6557", "EISSN": "2468-2322", "Authors": [{"AuthorId": 1, "Name": "Yan <PERSON>", "Affiliation": "College of Intelligence Science and Technology National University of Defense Technology  Changsha China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology National University of Defense Technology  Changsha China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology National University of Defense Technology  Changsha China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology National University of Defense Technology  Changsha China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Technical University of Munich  Garching Germany"}], "References": [{"Title": "Deep reinforcement learning for pedestrian collision avoidance and human-machine cooperative driving", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "532", "Issue": "", "Page": "110", "JournalTitle": "Information Sciences"}, {"Title": "A graph-based reinforcement learning-enabled approach for adaptive human-robot collaborative assembly operations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "491", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 114361182, "Title": "A comprehensive bibliometric and content analysis of artificial intelligence in language learning: tracing between the years 2017 and 2023", "Abstract": "The rising pervasiveness of Artificial Intelligence (AI) has led applied linguists to combine it with language teaching and learning processes. In many cases, such implementation has significantly contributed to the field. The retrospective amount of literature dedicated on the use of AI in language learning (LL) is overwhelming. Thus, the objective of this paper is to map the existing literature on Artificial Intelligence in language learning through bibliometric and content analysis. From the Scopus database, we systematically explored, after keyword refinement, the prevailing literature of AI in LL. After excluding irrelevant articles, we conducted our study with 606 documents published between 2017 and 2023 for further investigation. This review reinforces our understanding by identifying and distilling the relationships between the content, the contributions, and the contributors. The findings of the study show a rising pattern of AI in LL. Along with the metrics of performance analysis, through VOSviewer and R studio (Biblioshiny), our findings uncovered the influential authors, institutions, countries, and the most influential documents in the field. Moreover, we identified 7 clusters and potential areas of related research through keyword analysis. In addition to the bibliographic details, this review aims to elucidate the content of the field. NVivo 14 and Atlas AI were used to perform content analysis to categorize and present the type of AI used in language learning, Language learning factors, and its participants.", "Keywords": "Artificial intelligence; Language learning; Bibliometric analysis; Natural language processing; Review; Content analysis", "DOI": "10.1007/s10462-023-10643-9", "PubYear": 2024, "Volume": "57", "Issue": "4", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of English, School of Social Sciences and Languages, Vellore Institute of Technology, Vellore, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of English, School of Social Sciences and Languages, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of English, School of Social Sciences and Languages, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of English, School of Social Sciences and Languages, Vellore Institute of Technology, Vellore, India"}], "References": [{"Title": "The rise of chatbots – new personal assistants in foreign language learning", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "169", "Issue": "", "Page": "542", "JournalTitle": "Procedia Computer Science"}, {"Title": "Artificial intelligence within the interplay between natural and artificial computation: Advances in data science, trends and applications", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "410", "Issue": "", "Page": "237", "JournalTitle": "Neurocomputing"}, {"Title": "Effects of artificial intelligence on English speaking anxiety and speaking performance: A case study", "Authors": "Reham El Shazly", "PubYear": 2021, "Volume": "38", "Issue": "3", "Page": "e12667", "JournalTitle": "Expert Systems"}, {"Title": "Chatbots for language learning—Are they really useful? A systematic review of chatbot‐supported language learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "237", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Exploring an AI-based writing Assistant's impact on English language learners", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100055", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "A case study of using Alexa for out-of-class, self-directed Japanese language learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100088", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Review of research on applications of speech recognition technology to assist language learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "74", "JournalTitle": "ReCALL"}, {"Title": "A systematic review of artificial intelligence techniques for collaborative learning over the past two decades", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100097", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Instructional design and learning outcomes of intelligent computer assisted language learning: Systematic review in the field", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "", "Page": "100117", "JournalTitle": "Computers and Education: Artificial Intelligence"}]}, {"ArticleId": 114361277, "Title": "Unraveling the intricacies of EEG seizure detection: A comprehensive exploration of machine learning model performance, interpretability, and clinical insights", "Abstract": "<p>In neurology, it is critical to promptly and precisely identify epileptic episodes using EEG data. Interpretability and thorough model evaluation are still crucial to guarantee reliability, even though machine learning provides sophisticated tools for automated EEG analysis. We undertook a thorough investigation of EEG seizure classification in this work, with equal emphasis on statistical validation, interpretability, and model performance. Following thorough preprocessing, we used statistical significance tests in conjunction with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>ya Bo<PERSON> to select features. Top performers were found to be the Random Forest which had accuracies of 96%. A paired t-test and bootstrapped 95% confidence intervals were used to statistically validate the performance robustness, demonstrating the large variation in model performances. Going beyond simple performance measures, we set out to decipher the nuances of model choices by utilizing methods devoted to model interpretability and shedding light on the importance of distinct aspects. This helped neurologists grasp the primary EEG indicators predictive of seizures and opened the door for future clinical insights. It also provided transparency in diagnostic predictions. Our efforts highlight the mutually reinforcing importance of predictability and model interpretability in the context of medical machine-learning applications, hence intensifying the demand for the creation and use of trustworthy and transparent neurology diagnostic instruments.</p>", "Keywords": "EEG analysis; Machine learning; Model interpretability; Feature selection; Bootstrapping; Diagnostic tools; Neurology", "DOI": "10.1007/s11042-024-18900-x", "PubYear": 2024, "Volume": "83", "Issue": "41", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Marwadi University, Rajkot, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Software Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Artificial Intelligence and Big Data Analytics, Marwadi University, Rajkot, India"}], "References": [{"Title": "Feature extraction from EEG spectrograms for epileptic seizure detection", "Authors": "<PERSON>; <PERSON><PERSON>-<PERSON>; <PERSON>-Pine<PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "202", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Machine learning-based EEG signals classification model for epileptic seizure detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "12", "Page": "17849", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Machine Learning Approach for Brain Tumor Detection and Segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "3", "Page": "68", "JournalTitle": "International Journal of Organizational and Collective Intelligence"}, {"Title": "Study and analysis of different segmentation methods for brain tumor MRI application", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "5", "Page": "7117", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "The role of artificial neural network and machine learning in utilizing spatial information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "3", "Page": "275", "JournalTitle": "Spatial Information Research"}]}, {"ArticleId": 114361349, "Title": "AMAA-GMM: Adaptive Mexican Axolotl Algorithm based Enhanced Gaussian Mixture Model to Segment the Cervigram Images", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJRIS.2024.10063302", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 10882, "JournalTitle": "International Journal of Reasoning-based Intelligent Systems", "ISSN": "1755-0556", "EISSN": "1755-0564", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Lalasa Mu<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114361370, "Title": "Hermetic microfluidic device for point-of-care viral nucleic acid testing", "Abstract": "There is an unmet clinical and public health need for rapid and accurate point-of-care (POC) diagnosis and management of infectious pathogens, especially in resource-limited environments. In this work, we present a microfluidic deoxyribonucleic acid (DNA) and ribonucleic acid (RNA) rapid test (μDART) device for POC detection of various viral pathogens. This platform hermetically integrates the modules of extraction-free sample lysis, loop-mediated isothermal amplification (LAMP) with lyophilized reagent beads, and real-time colorimetric signal sensing to realize &quot;sample-in and result-out&quot; in 15–30 min. Test results can be sent to a mobile phone for remote monitoring. Automated high-precision liquid handling is achieved by a combination of negative-pressure-driven microchambers and self-sealing valves, thus simplifying manual operations and eliminating the leakage of nucleic acid aerosols. The limit of detection (LOD) of the device is as low as ∼53 copies/mL or ∼2 copies/reaction for SARS-CoV-2, which is as sensitive as the gold standard of nucleic acid testing. To validate our device’s clinical applications, we tested both DNA and RNA viruses as representative pathogens including 537 clinical nasal swab samples for SARS-CoV-2, 74 cervical swab samples for human papillomavirus 16/18 (HPV16/18) and 82 pet swab samples for feline cupripoxvirus (FCV) and feline herpesvirus (FHV). Across these tests, the clinical sensitivity and specificity were 92.5% - 97.7% and 99.7%-100%, respectively. We demonstrate that the μDART is able to provide a quick and accurate result for POCT and self-testing, and can connect to health care wirelessly to achieve remote monitoring.", "Keywords": "", "DOI": "10.1016/j.snb.2024.135740", "PubYear": 2024, "Volume": "411", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 2, "Name": "<PERSON>o Bai", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "One-Chip Biotechnology Co. Ltd, Chengdu, 610041, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "One-Chip Biotechnology Co. Ltd, Chengdu, 610041, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China"}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": "One-Chip Biotechnology Co. Ltd, Chengdu, 610041, China"}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "One-Chip Biotechnology Co. Ltd, Chengdu, 610041, China"}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China;Corresponding authors"}, {"AuthorId": 16, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China;Corresponding authors"}, {"AuthorId": 17, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China;Corresponding authors"}, {"AuthorId": 18, "Name": "<PERSON><PERSON><PERSON> (<PERSON>) Hu", "Affiliation": "Department of Laboratory Medicine, Precision Medicine Translational Research Center (PMTRC), State Key Laboratory of Respiratory Health and Multimorbidity, Med-X Center for Manufacturing, National Industrial Innovation Center of Precision Medicine, Department of Integrated Care Management Center, Frontier Science Center of Disease Molecular Network, Clinical Trial Center, West China Hospital, Sichuan University, Chengdu 610041, China;Corresponding authors"}], "References": [{"Title": "A portable microfluidic analyzer for integrated bacterial detection using visible loop-mediated amplification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "310", "Issue": "", "Page": "127834", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Diagnosis of pathogen infection via a multiple-wavelength colorimetric sensor platform with loop-mediated isothermal amplification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "370", "Issue": "", "Page": "132449", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 114361486, "Title": "Input attribute optimization for thermal deformation of machine-tool spindles using artificial intelligence", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10845-024-02350-1", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "Swami <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Win-<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Bivas Panigrahi", "Affiliation": ""}, {"AuthorId": 4, "Name": "Prateek Negi", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Cooling of motor spindles—a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "11-12", "Page": "3273", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Increase in Accuracy of a Built-in Spindle by Adaptive Cooling Control with Varied Coolant Volume and Temperature", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "11", "Page": "3689", "JournalTitle": "Sensors and Materials"}, {"Title": "Real-time thermal modelling approach of a machine tool spindle based on bond graph method", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "1-2", "Page": "99", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Thermal network model and experimental validation for a motorized spindle including thermal–mechanical coupling effect", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "1-2", "Page": "487", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Robust modeling method for thermal error of CNC machine tools based on random forest algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "4", "Page": "2013", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Thermal error modeling of motorized spindle based on Elman neural network optimized by sparrow search algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "1-2", "Page": "349", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Coolant Volume Prediction for Spindle Cooler with Adaptive Neuro-fuzzy Inference System Control Method", "Authors": "<PERSON><PERSON><PERSON>; Swami <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "2447", "JournalTitle": "Sensors and Materials"}, {"Title": "Linear Axial Error Signal Measurement and Processing Method of a Machine Tool for Accuracy Compensation Improvement", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Swami <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "11", "Page": "4137", "JournalTitle": "Sensors and Materials"}, {"Title": "Thermal deformation and economic analysis of a multiobject cooling system for spindles with varied coolant volume control", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Swami <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "3-4", "Page": "1807", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114361499, "Title": "DTA: distribution transform-based attack for query-limited scenario", "Abstract": "<p>In generating adversarial examples, the conventional black-box attack methods rely on sufficient feedback from the to-be-attacked models by repeatedly querying until the attack is successful, which usually results in thousands of trials during an attack. This may be unacceptable in real applications since Machine Learning as a Service Platform (MLaaS) usually only returns the final result (i.e., hard-label) to the client and a system equipped with certain defense mechanisms could easily detect malicious queries. By contrast, a feasible way is a hard-label attack that simulates an attacked action being permitted to conduct a limited number of queries. To implement this idea, in this paper, we bypass the dependency on the to-be-attacked model and benefit from the characteristics of the distributions of adversarial examples to reformulate the attack problem in a distribution transform manner and propose a distribution transform-based attack (DTA). DTA builds a statistical mapping from the benign example to its adversarial counterparts by tackling the conditional likelihood under the hard-label black-box settings. In this way, it is no longer necessary to query the target model frequently. A well-trained DTA model can directly and efficiently generate a batch of adversarial examples for a certain input, which can be used to attack un-seen models based on the assumed transferability. Furthermore, we surprisingly find that the well-trained DTA model is not sensitive to the semantic spaces of the training dataset, meaning that the model yields acceptable attack performance on other datasets. Extensive experiments validate the effectiveness of the proposed idea and the superiority of DTA over the state-of-the-art.</p>", "Keywords": "Distribution transform-based attack;Query-limited adversarial attack;Adversarial examples;Conditional normalizing flow", "DOI": "10.1186/s42400-023-00197-2", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 5427, "JournalTitle": "Cybersecurity", "ISSN": "", "EISSN": "2523-3246", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Yunnan University, Kunming, China; Nanyang Technological University, Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Yunnan University, Kunming, China"}, {"AuthorId": 3, "Name": "Xi<PERSON>", "Affiliation": "Yunnan University, Kunming, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Yunnan University, Kunming, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Kunming Institute of Physics, Kunming, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Alibaba Group, Beijing, China; Corresponding author."}], "References": [{"Title": "LSGAN-AT: enhancing malware detector robustness against adversarial examples", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}, {"Title": "IPatch: a remote adversarial patch", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}, {"Title": "Towards the universal defense for query-based audio adversarial attacks on speech recognition system", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}, {"Title": "Threats, attacks and defenses to federated learning: issues, taxonomy and perspectives", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}]}, {"ArticleId": *********, "Title": "Call for Papers: IEEE Computer Architecture", "Abstract": "", "Keywords": "", "DOI": "10.1109/MAHC.2024.3377853", "PubYear": 2024, "Volume": "46", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "Rule-based Matching and Hidden Markov Model-based Warning for Brushing Behavior", "Abstract": "This paper proposes a warning system for brushing behavior based on the tobacco industry's reward activities by combining rule-based matching and Hidden Markov Models. Targeting behaviors where customers make large-scale purchases during promotional events that deviate from their individual historical consumption patterns, the system employs rule-based matching for initial assessment, followed by a more in-depth behavioral analysis using Hidden Markov Models. Experimental results demonstrate the significant effectiveness of the system in warning against brushing behavior, providing an effective monitoring tool for the tobacco industry and similar sectors.", "Keywords": "", "DOI": "10.23977/jaip.2024.070119", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 52855, "JournalTitle": "Journal of Artificial Intelligence Practice", "ISSN": "2371-8315", "EISSN": "2371-8412", "Authors": [], "References": []}, {"ArticleId": 114361629, "Title": "Collision Avoidance Flight Trajectory Tracking Method of UAV Based on Multi sensor Fusion", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJRIS.2024.10063304", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 10882, "JournalTitle": "International Journal of Reasoning-based Intelligent Systems", "ISSN": "1755-0556", "EISSN": "1755-0564", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114361667, "Title": "Sentiment Analysis of Indonesian Youtube Reviews About <PERSON><PERSON>, Guy, Bisexual and Transgender (LGBT) using IndoBERT Fine Tuning", "Abstract": "<p>Lesbian, gay, Bisexual, and Transgender (LGBT) is an individual who has a sexual orientation or gender identity that is different from the heterosexual majority. The LGBT community now dares to appear openly on social media; nowadays, social media is used as a source of information and a place to provide comments. The Indonesian state generally still views the LGBT community as deviant behavior. This research was conducted to understand Indonesian society's views on LGBT through YouTube and social media. The text mining method analyzes and classifies the counter or pro sentences expressed in the comments. The model used in this research is IndoBERT because the research object studied is Indonesian. IndoBERT is part of the Bidirectional Encoder Representation From Transformers (BERT) model. The data sources used were 1,493 data. The stages carried out in this research included the preprocessing stage, which included case folding, data cleaning, tokenization, stopword removal, stemming, and normalization, then the data labeling stage, and finally, the model building stage with IndoBERT Fine Tuning. The level of accuracy achieved using IndoBERT is 74%.  \r  </p>", "Keywords": "", "DOI": "10.24843//LKJITI.2024.v15.i01.p03", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 46965, "JournalTitle": "Lontar Komputer : <PERSON><PERSON> Ilmiah Teknologi Informasi", "ISSN": "2088-1541", "EISSN": "2541-5832", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Gunadarma University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Gunadarma"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Udayana"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gunadarma University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Udayana"}], "References": []}, {"ArticleId": 114361688, "Title": "Comparison of Gain Ratio and Chi-Square Feature Selection Methods in Improving SVM Performance on IDS", "Abstract": "<p>An intrusion detection system (IDS) is a security technology designed to identify and monitor suspicious activity in a computer network or system and detect potential attacks or security breaches. The importance of accuracy in IDS must be addressed, given that the response to any alert or activity generated by the system must be precise and measurable. However, achieving high accuracy in IDS requires a process that takes work. The complex network environment and the diversity of attacks led to significant challenges in developing IDS. The application of algorithms and optimization techniques needs to be considered to improve the accuracy of IDS. Support vector machine (SVM) is one data mining method with a high accuracy level in classifying network data packet patterns. A feature selection stage is needed for an optimal classification process, which can also be applied to SVM. Feature selection is an essential step in the data preprocessing phase; optimization of data input can improve the performance of the SVM algorithm, so this study compares the performance between feature selection algorithms, namely Information Gain Ratio and Chi-Square, and then classifies IDS data using the SVM algorithm. This outcome implies the importance of selecting the right features to develop an effective IDS.</p>", "Keywords": "", "DOI": "10.24843/LKJITI.2024.v15.i01.p06", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 46965, "JournalTitle": "Lontar Komputer : <PERSON><PERSON> Ilmiah Teknologi Informasi", "ISSN": "2088-1541", "EISSN": "2541-5832", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Udayana"}, {"AuthorId": 2, "Name": "I Ketut Gede Darma Putra", "Affiliation": "Information Technology Department, Udayana University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Faculty of Engineering, Udayana University"}, {"AuthorId": 4, "Name": "I <PERSON>", "Affiliation": "Information Technology Department, Udayana University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, The University of Warwick"}], "References": []}, {"ArticleId": 114361715, "Title": "A Fine-Tuned RetinaNet for Real-Time Lettuce Detection", "Abstract": "<p>The agricultural industry plays a vital role in the global demand for food production. Along with population growth, there is an increasing need for efficient farming practices that can maximize crop yields. Conventional methods of harvesting lettuce often rely on manual labor, which can be time-consuming, labor-intensive, and prone to human error. These challenges lead to research into automation technology, such as robotics, to improve harvest efficiency and reduce reliance on human intervention. Deep learning-based object detection models have shown impressive success in various computer vision tasks, such as object recognition. RetinaNet model can be trained to identify and localize lettuce accurately. However, the pre-trained models must be fine-tuned to adapt to the specific characteristics of lettuce, such as shape, size, and occlusion, to deploy object recognition models in real-world agricultural scenarios. Fine-tuning the models using lettuce-specific datasets can improve their accuracy and robustness for detecting and localizing lettuce. The data acquired for RetinaNet has the highest accuracy of 0.782, recall of 0.844, f1-score of 0.875, and mAP of 0,962. Metrics evaluate that the higher the score, the better the model performs.</p>", "Keywords": "", "DOI": "10.24843/LKJITI.2024.v15.i01.p02", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 46965, "JournalTitle": "Lontar Komputer : <PERSON><PERSON> Ilmiah Teknologi Informasi", "ISSN": "2088-1541", "EISSN": "2541-5832", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Merdeka Malang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kakumamachi, Kanazawa, Ishikawa, Jepang"}], "References": []}, {"ArticleId": 114361775, "Title": "Vision transformer models for mobile/edge devices: a survey", "Abstract": "<p>With the rapidly growing demand for high-performance deep learning vision models on mobile and edge devices, this paper emphasizes the importance of compact deep learning-based vision models that can provide high accuracy while maintaining a small model size. In particular, based on the success of transformer models in natural language processing and computer vision tasks, this paper offers a comprehensive examination of the latest research in redesigning the Vision Transformer (ViT) model into a compact architecture suitable for mobile/edge devices. The paper classifies compact ViT models into three major categories: (1) architecture and hierarchy restructuring, (2) encoder block enhancements, and (3) integrated approaches, and provides a detailed overview of each category. This paper also analyzes the contribution of each method to model performance and computational efficiency, providing a deeper understanding of how to efficiently implement ViT models on edge devices. As a result, this paper can offer new insights into the design and implementation of compact ViT models for researchers in this field and provide guidelines for optimizing the performance and improving the efficiency of deep learning vision models on edge devices.</p>", "Keywords": "Vision transformer; Mobile/edge devices; Survey", "DOI": "10.1007/s00530-024-01312-0", "PubYear": 2024, "Volume": "30", "Issue": "2", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Research Center for Electrical and Information Technology, Seoul National University of Science and Technology, Seoul, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Research Center for Electrical and Information Technology, Seoul National University of Science and Technology, Seoul, Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Research Center for Electrical and Information Technology, Seoul National University of Science and Technology, Seoul, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Research Center for Electrical and Information Technology, Seoul National University of Science and Technology, Seoul, Korea"}, {"AuthorId": 5, "Name": "Sang<PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Research Center for Electrical and Information Technology, Seoul National University of Science and Technology, Seoul, Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Research Center for Electrical and Information Technology, Seoul National University of Science and Technology, Seoul, Korea"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Research Center for Electrical and Information Technology, Seoul National University of Science and Technology, Seoul, Korea; Corresponding author."}], "References": [{"Title": "A deeply coupled ConvNet for human activity recognition using dynamic and RGB images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "469", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 114361973, "Title": "Efficient Data Sensing Algorithm with Generalized Cascaded Chaotic Maps for Secure Cognitive Radio in 5G Networks", "Abstract": "<p>Sparse code multiple access (SCMA) has emerged as a significant technology to satisfy the essential criteria for 5G wireless networks, including high data rates, widespread connection, reliability, and greater spectrum efficiency. Under the restriction of scarce and limited spectrum utilization in 5G networks, the integrated cognitive radio (CR) with SCMA is a promising solution to handle these crucial needs. A reliable and secure connection network should be built in addition to this integrated network to deal with densely deployed networks. A secure transmission sensing approach for CR-SCMA systems employing chaos theory has been presented in the paper. Different chaotic maps, including the logistic map, tent map, Chebyshev map, sine map, and the generalized cascaded chaotic map (CCS), have been employed for sensing with secure communication. Bit error rate (BER) and probability of detection with effective simulation are two of the metrics used for performance evaluation. Instead of using random sequences, the chaos-based signal transmission provides a viable solution for 5G generation networks with a secure wireless communication link. Using chaotic map signals, which are extremely sensitive to their initial conditions and have non-periodic properties, the eavesdropper or the unauthorized user cannot simply grab the encrypted and secured vital data.</p>", "Keywords": "Sparse code multiple access (SCMA); Chaotic maps; Cognitive radio; Spectrum sensing; Cascaded chaotic signal (CCS)", "DOI": "10.1007/s42979-024-02699-3", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, Malaviya National Institute of Technology, Jaipur, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of ECE, Malaviya National Institute of Technology, Jaipur, India"}], "References": [{"Title": "A Review on Applications of Chaotic Maps in Pseudo-Random Number Generators and Encryption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "25", "JournalTitle": "Annals of Data Science"}, {"Title": "Secure collaborative cognitive radio based on chaotic modulation and compressive sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101671", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 114361978, "Title": "High Performance Methyl Salicylate Gas Sensor based on Noble Metal (Au, Pt) Decorated WO3 Nanofibers", "Abstract": "Methyl salicylate (MeSa) is known as a volatile plant pathogen biomarker and a chemical warfare agent simulant. Herein, high performance MeSa gas sensors were developed by optimizing morphology and composition of noble metal decorated tungsten trioxide (WO<sub>3</sub>) nanofibers. Using optimized nanofibers, 100 ppb of MeSa were readily sensed in dry air whereas the calculated low limit of detection (LLOD) is expected to be in a few tens of ppb. Pt-WO<sub>3</sub> (1.1 at% Pt) nanofibers also show an excellent selectivity over other volatile organic compounds including aromatic compounds ( i.e., benzene, toluene, ethyl benzene, p-xylene). Systematic characterization of sensors in environments with different oxygen contents indicated that surface carrier concentration significantly altered as a function of Au and Pt content. Although 1.7 at% Au-WO<sub>3</sub> nanofibers based sensor has higher sensing response toward MeSa, 1.1 at% Pt-WO<sub>3</sub> nanofibers were found to be a more promising sensing material for MeSa detection because of higher selectivity over other VOCs and lower calculated LLOD ( ∼41 ppb).", "Keywords": "", "DOI": "10.1016/j.snb.2024.135741", "PubYear": 2024, "Volume": "413", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, University of Notre Dame, Notre Dame 46556, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, University of Notre Dame, Notre Dame 46556, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, University of Notre Dame, Notre Dame 46556, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, University of Notre Dame, Notre Dame 46556, United States"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, University of Notre Dame, Notre Dame 46556, United States;Corresponding author"}], "References": [{"Title": "Au@ZnO functionalized three–dimensional macroporous WO3: A application of selective H2S gas sensor for exhaled breath biomarker detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "324", "Issue": "", "Page": "128725", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Enhanced n-butanol sensing performance of SnO2-based gas sensors by doping In2O3 via co-precipitation method", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "340", "Issue": "", "Page": "129944", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 114361997, "Title": "When will the blind be able to take their first steps with GDR guidance under artificial intelligence?", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-01912-4", "PubYear": 2025, "Volume": "40", "Issue": "2", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114362077, "Title": "A machine learning-based approach for estimation of deflection and contact area characteristics of tubeless and tube-type agricultural tyres", "Abstract": "Support Vector Regression (SVR) models with different kernel functions (radial basis and polynomial) were developed for predicting deflection and contact area of tubeless and tube-type tractor tyres. Data were collected from experimental trials on 13.6–28 tubeless and tube-type tyres under varying loads (750–1400 kg) and inflation pressures (69–179 kPa). A displacement transducer comprising a rack-pinion arrangement and a rotary potentiometer was developed for measuring tyre deflection. A total of 168 data points were split into 70% for training and 30% for testing the SVR models. An additional 42 new data points were collected at a normal load of 900 kg for validation purpose. As compared to tube-type tyre, tubeless tyre had higher contact area at higher normal loads due to higher tyre deflection. Two SVR models were constructed by considering normal load, inflation pressure, and tyre-type as input parameters. The well-trained SVR model could predict tyre deflection with a maximum deviation of 2% from the measured values as compared to −21% in case of <PERSON><PERSON><PERSON>hn model. Similarly, the predicted contact area had a maximum variation of 1% from the measured values as compared to 9.99% in case of Komandi model and 31% in case of Diserens model. The sensitivity analysis indicated that the proposed models ranked normal load as the highest priority followed by inflation pressure and tyre-type for estimating both tyre deflection and contact area.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108357", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Agricultural and Food Engineering Department, Indian Institute of Technology Kharagpur, West Bengal, 721302, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Agricultural and Food Engineering Department, Indian Institute of Technology Kharagpur, West Bengal, 721302, India"}], "References": [{"Title": "Transport infrastructure connectivity and conflict resolution: a machine learning analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "6585", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Using deep belief network to construct the agricultural information system based on Internet of Things", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "1", "Page": "379", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Prediction of electron beam weld quality from weld bead surface using clustering and support vector regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118677", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Application of asymmetric proximal support vector regression based on multitask learning in the stock market", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "227", "Issue": "", "Page": "120208", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114362078, "Title": "CONSUMER CHOICES IN THE FOOD MARKET: HEALTHY AND SUSTAIN<PERSON>LE CONSUMPTION", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIMA.2024.10063308", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 16124, "JournalTitle": "International Journal of Internet Marketing and Advertising", "ISSN": "1477-5212", "EISSN": "1741-8100", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114362140, "Title": "ChatGPT for Educational Purposes: Investigating the Impact of Knowledge Management Factors on Student Satisfaction and Continuous Usage", "Abstract": "The growing prevalence of advanced generative artificial intelligence chatbots, such as ChatGPT, in the educational sector has raised considerable interest in understanding their impact on student knowledge and exploring effective and sustainable implementation strategies. This research investigates the influence of knowledge management factors on the continuous usage of ChatGPT for educational purposes while concurrently evaluating student satisfaction with its use in learning. Using a quantitative approach, a structured questionnaire was administered to 513 Vietnamese university students via Google Forms for data collection. The partial least squares structural equation modeling statistical technique was employed to examine the relationships between identified factors and evaluate the research model. The results provided strong support for several hypotheses, revealing significant positive effects of expectation confirmation on perceived usefulness and satisfaction, as well as perceived usefulness on user satisfaction and continuous usage of ChatGPT. These findings suggest that when students recognize the usefulness of ChatGPT for their learnings, they experience higher satisfaction and are more likely to continue using it. In addition, knowledge acquisition significantly impacts both satisfaction and continuous usage of ChatGPT, while knowledge sharing and application influence satisfaction exclusively. This indicates that students prioritize knowledge acquisition over sharing and applying knowledge through ChatGPT. The study has theoretical and practical implications for ChatGPT developers, educators, and future research. Theoretically, it contributes to understanding satisfaction and continuous usage in educational settings, utilizing the expectation confirmation model and integrating knowledge management factors. Practically, it provides insights into comprehension and suggestions for enhancing user satisfaction and continuous usage of ChatGPT in education.", "Keywords": "", "DOI": "10.1109/TLT.2024.3383773", "PubYear": 2024, "Volume": "17", "Issue": "", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business, FPT University, Can Tho, Vietnam"}, {"AuthorId": 2, "Name": "Thanh Tu Tran", "Affiliation": "Department of Business, FPT University, Can Tho, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business, FPT University, Can Tho, Vietnam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business, FPT University, Can Tho, Vietnam"}], "References": [{"Title": "Perceived user satisfaction and intention to use massive open online courses (MOOCs)", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON>-<PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "85", "JournalTitle": "Journal of Computing in Higher Education"}, {"Title": "What makes you continuously use chatbot services? Evidence from chinese online travel agencies", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "3", "Page": "575", "JournalTitle": "Electronic Markets"}, {"Title": "Directions of the 100 most cited chatbot-related human behavior research: A review of academic publications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "100023", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "The Chatbot Usability Scale: the Design and Pilot of a Usability Scale for Interaction with AI-Based Conversational Agents", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "95", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Are We There Yet? - A Systematic Literature Review on Chatbots in Education", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "89", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Chatbots applications in education: A systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "100033", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "NEU-chatbot: <PERSON><PERSON><PERSON> for admission of National Economics University", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "100036", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "A literature review on users' behavioral intention toward chatbots' adoption", "Authors": "Paraskevi Gatzioufa; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Applied Computing and Informatics"}, {"Title": "ChatGPT in Education: Partner or <PERSON><PERSON><PERSON>?", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "48", "JournalTitle": "XRDS: Crossroads, The ACM Magazine for Students"}]}, {"ArticleId": 114362315, "Title": "Call for Papers: Get Published in the New IEEE Transactions on Privacy", "Abstract": "", "Keywords": "", "DOI": "10.1109/MAHC.2024.3374490", "PubYear": 2024, "Volume": "46", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [], "References": []}, {"ArticleId": 114362344, "Title": "Modelling for Efficient Scientific Data Storage Using Simple Graphs in DNA", "Abstract": "<p>With an annual exponential growth rate, the demand for massive data archives is almost outpacing the capabilities of currently available world storage media. Alternatively, data archives could be maintained remarkably using DNA storage, making simple graph-aware data archives essential for efficiency rather than raw data. Ideally, graph-aware data archiving has a significant advantage over raw data. That helps to reduce the related data size for DNA storage in terms of nucleotides. As a result, we take advantage of lower database operational costs. Based on the Re-Pair compression technique, we provide a theoretical model for efficient scientific data storage using simple graphs in DNA. Herewith, two storage strategies—individual and composite graphs—have been investigated to discover the preferable. Some simple graph-based datasets, particularly from the biological domain, have been used to analyze experimental results. The compression ratios ranged from 1.18 and 1.53 and the exploitation of graph-aware data ultimately helped us save a substantial amount of money.</p>", "Keywords": "DNA storage; Simple graph; Data compression; Data modelling", "DOI": "10.1007/s42979-024-02672-0", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Goethe University, Frankfurt, Germany; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Goethe University, Frankfurt, Germany"}], "References": [{"Title": "The future is big graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "9", "Page": "62", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 114362379, "Title": "Adversarial Machine Learning for Social Good: Reframing the Adversary as an Ally", "Abstract": "", "Keywords": "", "DOI": "10.1109/TAI.2024.3383407", "PubYear": 2024, "Volume": "5", "Issue": "9", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information and Computing Technology (ICT) Division, College of Science and Engineering, Hamad Bin Khalifa University, Doha, Qatar"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology University, Lahore, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of New South Wales Sydney, Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Information and Computing Technology (ICT) Division, College of Science and Engineering, Hamad Bin Khalifa University, Doha, Qatar"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, College of Engineering, Qatar University, Doha, Qatar"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Data Engineering, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computing and Data Science, Nanyang Technological University, Singapore"}, {"AuthorId": 8, "Name": "Ala Al-Fuqaha", "Affiliation": "Information and Computing Technology (ICT) Division, College of Science and Engineering, Hamad Bin Khalifa University, Doha, Qatar"}], "References": [{"Title": "A definition, benchmark and database of AI for social good initiatives", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "2", "Page": "111", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Socially Responsible AI Algorithms: Issues, Purposes, and Challenges", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "1137", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Ulixes: Facial Recognition Privacy with Adversarial Machine Learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "1", "Page": "148", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "Optimizing molecules using efficient queries from property evaluations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "1", "Page": "21", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Tamp-X: Attacking explainable natural language classifiers through tampered activations", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102791", "JournalTitle": "Computers & Security"}, {"Title": "An Overview of Artificial Intelligence Ethics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "4", "Page": "799", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Stateful detection of adversarial reprogramming", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "642", "Issue": "", "Page": "119093", "JournalTitle": "Information Sciences"}, {"Title": "Improve individual fairness in federated learning via adversarial training", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "103336", "JournalTitle": "Computers & Security"}, {"Title": "Con-Detect: Detecting adversarially perturbed natural language inputs to deep classifiers through holistic analysis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "103367", "JournalTitle": "Computers & Security"}, {"Title": "Counterfactual Explanations and Algorithmic Recourses for Machine Learning: A Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "12", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 114362408, "Title": "Multi-dataset learning with channel modulation loss for blind image quality assessment", "Abstract": "<p>In Blind Image Quality Assessment (BIQA), due to the problem of laborious labeling, it is perceived as the intractability of collecting a new large-scale dataset that has plentiful images with a large diversity in distortion and scene. Therefore, to develop a general model, training with data from diverse datasets could be a viable solution and hold significant value. A straightforward solution is to mix multiple datasets to train a robust model. However, as IQA datasets vary in terms of distortion types and labeling mechanisms, models fail to adapt well across various datasets. To solve the multi-dataset adaption problem, we propose a Channel Modulation Loss that encourages each channel to adaptively enhance/diminish dataset-corresponding features. In detail, when training a CNN with data from all the datasets, certain channels acquire knowledge that can be applied across some datasets, whereas others are only effective for a specific dataset. Therefore, we propose a dataset-specific kernel as supervision, by which channel attention can be explicitly optimized to emphasize the channels corresponding to the dataset of an input image. Finally, extensive experiments on six IQA databases show the promise of the learned method in blindly assessing image quality.</p>", "Keywords": "Blind image quality assessment; Multi-dataset learning; Channel attention mechanism; Channel modulation loss", "DOI": "10.1007/s11042-024-18542-z", "PubYear": 2024, "Volume": "83", "Issue": "40", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computing Science, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Network Intelligence, Pengcheng Laboratory, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing Science, Harbin Institute of Technology, Harbin, China; Corresponding author."}], "References": [{"Title": "Unified Quality Assessment of in-the-Wild Videos with Mixed Datasets Training", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "4", "Page": "1238", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 114362436, "Title": "An experimental study on mixed reality-based user interface for collaborative operation of high-precision process equipment", "Abstract": "<p>Recent experiments have shown that improper encoding in MR user interfaces exacerbates the individual cognitive symbol effect, leading to issues of excessive cognitive workload and elevated psychological stress levels in individual cognitive task methods. In light of this, this paper proposes a novel MR user interface information encoding method. By assessing the improvement level of object clues and action clues on information encoding quality, it identifies the optimal encoding forms for combining these two types of cues, thereby establishing a more intuitive and natural communication channel for information. The paper presents several human–machine interface usability test results and compares the proposed encoding forms with the MR interface encoding information currently used in significant factories like AVIC (Aviation Industry Corporation of China) to analyze the advantages and disadvantages of the proposed encoding forms.</p>", "Keywords": "Mixed reality; User interface; Collaborative operation; High precision", "DOI": "10.1007/s00170-024-13517-8", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, University of Shanghai for Science and Technology, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Shanghai for Science and Technology, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Shanghai for Science and Technology, Shanghai, China"}, {"AuthorId": 4, "Name": "Yan <PERSON>", "Affiliation": "School of Mechanical Engineering, University of Shanghai for Science and Technology, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Civil Aircraft Engineering Research and Development Center, AVIC Airborne Systems Co., Ltd., Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Dai", "Affiliation": "School of Environment and Architecture, University of Shanghai for Science and Technology, Shanghai, China; Corresponding author."}], "References": [{"Title": "Applications of virtual reality in maintenance during the industrial product lifecycle: A systematic review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "525", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Mobile augmented reality to support fuselage assembly", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "106712", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "AED: a novel visual representation based on AR and empathy computing in manual assembly", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "1", "JournalTitle": "Revista Internacional de Métodos Numéricos para Cálculo y Diseño en Ingeniería"}, {"Title": "M-AR: A Visual Representation of Manual Operation Precision in AR Assembly", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "19", "Page": "1799", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "An experimental study on augmented reality assisted manual assembly with occluded components", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "685", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "AR/MR Remote Collaboration on Physical Tasks: A Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "72", "Issue": "", "Page": "102071", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "SHARIDEAS: a smart collaborative assembly platform based on augmented reality supporting assembly intention recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "1-2", "Page": "475", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A futuristic perspective on human-centric assembly", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "199", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Effects of Augmented Reality-, Virtual Reality-, and Mixed Reality–Based Training on Objective Performance Measures and Subjective Evaluations in Manual Assembly Tasks: A Scoping Review", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "2", "Page": "589", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "A comprehensive review of augmented reality-based instruction in manual assembly, training and repair", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "102407", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "ARCoA: Using the AR-Assisted Cooperative Assembly System to Visualize Key Information about the Occluded Partner", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "18", "Page": "3556", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "BeHere: a VR/SAR remote collaboration system based on virtual replicas sharing gesture and avatar in a procedural task", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "2", "Page": "1409", "JournalTitle": "Virtual Reality"}]}, {"ArticleId": 114362587, "Title": "A Fast Monte Carlo Algorithm for Evaluating Matrix Functions with Application in Complex Networks", "Abstract": "We propose a novel stochastic algorithm that randomly samples entire rows and columns of the matrix as a way to approximate an arbitrary matrix function using the power series expansion. This contrasts with existing Monte Carlo methods, which only work with one entry at a time, resulting in a significantly better convergence rate than the original approach. To assess the applicability of our method, we compute the subgraph centrality and total communicability of several large networks. In all benchmarks analyzed so far, the performance of our method was significantly superior to the competition, being able to scale up to 64 CPU cores with remarkable efficiency.", "Keywords": "Monte Carlo methods; Randomized algorithms; Matrix functions; Network analysis; 65C05; 68W20; 65F50; 05C90; 68W10", "DOI": "10.1007/s10915-024-02500-w", "PubYear": 2024, "Volume": "99", "Issue": "2", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "INESC-ID, Instituto Superior Técnico, Universidade de Lisboa, Lisbon, Portugal; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "INESC-ID, Instituto Superior Técnico, Universidade de Lisboa, Lisbon, Portugal; Department of Mathematics, Carlos III University of Madrid, Leganés, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INESC-ID, Instituto Superior Técnico, Universidade de Lisboa, Lisbon, Portugal"}], "References": [{"Title": "A highly parallel algorithm for computing the action of a matrix exponential on a vector based on a multilevel Monte Carlo method", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "12", "Page": "3495", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 114362843, "Title": "ANDez: An open-source tool for author name disambiguation using machine learning", "Abstract": "Author name disambiguation in bibliographic data is challenging due to the same names of different authors and name variations of authors. Various machine learning (ML) methods address this, but a unified framework for comparing them is lacking. This study introduces ANDez, an open-source tool that integrates top-performing ML techniques for author name disambiguation. Developed in Python using popular ML libraries, ANDez provides a transparent system, merging complex procedures from different ML approaches. This promotes the assessment, modification, and benchmarking of ML techniques in author name disambiguation. ANDez&#x27;s user-friendly design also helps researchers analyze ambiguous bibliographic data without needing advanced ML coding expertise.", "Keywords": "Author name disambiguation ; Authority control ; Machine learning ; Science of science ; Scientometrics ; Bibliometrics", "DOI": "10.1016/j.softx.2024.101719", "PubYear": 2024, "Volume": "26", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Social Research & School of Information, University of Michigan, 330 Packard Street, Ann Arbor, MI 48104, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Sciences, University of Illinois at Urbana-Champaign, 501 E. Daniel Street, Champaign, IL 61820, USA"}], "References": [{"Title": "CluEval: A Python tool for evaluating clustering performance in named entity disambiguation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "", "Page": "100510", "JournalTitle": "Software Impacts"}]}, {"ArticleId": 114362927, "Title": "Correction to: Experimental investigation on cross wedge rolling of composite 42CrMo/Q235 laminated shaft", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-024-13358-5", "PubYear": 2024, "Volume": "132", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>osh<PERSON>", "Affiliation": "Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, People’s Republic of China; Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, People’s Republic of China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, People’s Republic of China; Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, People’s Republic of China; Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, People’s Republic of China; Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, People’s Republic of China; Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, People’s Republic of China; Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, People’s Republic of China"}], "References": []}, {"ArticleId": 114362945, "Title": "Training flexible spatial-cognitive estimation strategies using augmented reality", "Abstract": "<p>We investigated the potential for augmented reality (AR) as a training aid for spatial estimation skills. Though there are many tools to support spatial judgments, from measuring cups to rulers, not much is known about training spatial skills for retention and transfer. Display of AR was manipulated to train the spatial skill of portion estimation. In Experiment 1, an AR-aided strategy of creating smaller portions out of a larger example amount was compared to a no-AR control condition. This manipulation was based on previous non-AR experiments where amorphous foods were better estimated when divided into smaller portions. There was a significant benefit of estimating using a solid AR shape. In Experiment 2, cognitive anchoring was manipulated. Using meaningful AR anchors resulted in the best performance and most learning. We conclude that spatial estimation skills can be combined with mental strategies and trained via AR.</p>", "Keywords": "augmented reality;cognitive anchoring;skill acquisition;training;transfer", "DOI": "10.1080/00140139.2024.2332768", "PubYear": 2025, "Volume": "68", "Issue": "3", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, North Carolina State University, Raleigh, NC, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Psychology, North Carolina State University, Raleigh, NC, USA"}], "References": [{"Title": "A Fundamental Cognitive Taxonomy for Cognition Aids", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "6", "Page": "865", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "An evaluation of a virtual atlas of portion sizes (VAPS) mobile augmented reality for portion size estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "695", "JournalTitle": "Virtual Reality"}]}, {"ArticleId": *********, "Title": "An Edge Cloud Based Coordination Platform for Multi-user AR Applications", "Abstract": "Augmented Reality (AR) applications can reshape our society enabling novel ways of interactions and immersive experiences in many fields. However, multi-user and collaborative AR applications pose several challenges. The expected user experience requires accurate position and orientation information for each device and precise synchronization of the respective coordinate systems in real-time. Unlike mobile phones or AR glasses running on battery with constrained resource capacity, cloud and edge platforms can provide the computing power for the core functions under the hood. In this paper, we propose a novel edge cloud based platform for multi-user AR applications realizing an essential coordination service among the users. The latency critical, computation intensive Simultaneous Localization And Mapping (SLAM) function is offloaded from the device to the edge cloud infrastructure. Our solution is built on open-source SLAM libraries and the Robot Operating System (ROS). Our contribution is threefold. First, we propose an extensible, edge cloud based AR architecture. Second, we develop a proof-of-concept prototype supporting multiple devices and building on an AI-based SLAM selection component. Third, a dedicated measurement methodology is described, including energy consumption aspects as well, and the overall performance of the system is evaluated via real experiments.", "Keywords": "Augmented reality; Edge computing; SLAM", "DOI": "10.1007/s10922-024-09809-9", "PubYear": 2024, "Volume": "32", "Issue": "2", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "HSN Lab, Department of Telecommunications and Media Informatics, Faculty of Electrical Engineering and Informatics, Budapest University of Technology and Economics, Budapest, Hungary; MTA-BME Network Softwarization Research Group, Budapest, Hungary; HUN-REN-BME Cloud Applications Research Group, Budapest, Hungary; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HSN Lab, Department of Telecommunications and Media Informatics, Faculty of Electrical Engineering and Informatics, Budapest University of Technology and Economics, Budapest, Hungary; MTA-BME Network Softwarization Research Group, Budapest, Hungary; HUN-REN-BME Cloud Applications Research Group, Budapest, Hungary"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HSN Lab, Department of Telecommunications and Media Informatics, Faculty of Electrical Engineering and Informatics, Budapest University of Technology and Economics, Budapest, Hungary; MTA-BME Network Softwarization Research Group, Budapest, Hungary; HUN-REN-BME Cloud Applications Research Group, Budapest, Hungary"}, {"AuthorId": 4, "Name": "Zsófia <PERSON>és-Solymosi", "Affiliation": "HSN Lab, Department of Telecommunications and Media Informatics, Faculty of Electrical Engineering and Informatics, Budapest University of Technology and Economics, Budapest, Hungary; MTA-BME Network Softwarization Research Group, Budapest, Hungary"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HSN Lab, Department of Telecommunications and Media Informatics, Faculty of Electrical Engineering and Informatics, Budapest University of Technology and Economics, Budapest, Hungary; MTA-BME Network Softwarization Research Group, Budapest, Hungary; HUN-REN-BME Cloud Applications Research Group, Budapest, Hungary"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Ericsson Research, Budapest, Hungary"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ericsson Research, Budapest, Hungary"}, {"AuthorId": 8, "Name": "Balá<PERSON>s <PERSON>", "Affiliation": "Ericsson Research, Budapest, Hungary"}], "References": [{"Title": "Visual and Visual-Inertial SLAM: State of the Art, Classification, and Experimental Benchmarking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Sensors"}, {"Title": "LIFT-SLAM: A deep-learning feature-based monocular visual SLAM method", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "455", "Issue": "", "Page": "97", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114363042, "Title": "Burr formation mechanism and experimental research in longitudinal-torsional ultrasonic-assisted milling Ti-6Al-4 V", "Abstract": "<p>Titanium alloy milling is prone to burrs at the edges of the workpiece, which can negatively affect surface integrity and dimensional accuracy, and even lead to part scrap. Ultrasonic vibration–assisted milling technology can effectively inhibit burr generation and improve machining quality. However, the research of ultrasonic vibration–assisted milling on burr inhibition is not clear, so this paper establishes a mathematical model of ultrasonic vibration vertical milling titanium alloy top burr size based on the chip deformation process and specifically analyses the effect of ultrasonic machining parameters on burr through experiments. The experimental results show that the depth of cut has the greatest influence on the burr size, and the ultrasonic vibration has the second greatest influence on the burr. The cutting force and the burr size on both sides of the groove show a trend of “decrease and then increase” with the increase of ultrasonic amplitude. When the ultrasonic amplitude was 3 µm, the cutting forces F <sub> x </sub> and F <sub> y </sub> were reduced by 34.42% and 31.36%, respectively, and the heights and widths of the burrs on the up milling side and on the down milling side were reduced by 75.49%, 44.33% and 89.16%, 47.82%, respectively, when comparing with no ultrasonic machining. The longitudinal-torsional ultrasonic vibration converted the large piled-up, rolled-up, and serrated burrs into intermittent, small-sized flocculent burrs, which significantly improved the burr morphology and weakened the serrated characteristics of the chips.</p>", "Keywords": "Longitudinal-torsional ultrasonic vibration; Chip deformation force; Burr size; Milling Ti-6Al-4 V", "DOI": "10.1007/s00170-024-13494-y", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, China"}], "References": [{"Title": "Effect of ultrasound-assisted vibration on Ti-6Al-4V/Al2024-T351 laminated material processing with geometric tools", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "1-2", "Page": "219", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Burr formation and its treatments—a review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2189", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental study of tool wear and its effects on cutting process of ultrasonic-assisted milling of Ti6Al4V", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Huadong Yu", "PubYear": 2020, "Volume": "108", "Issue": "9-10", "Page": "2917", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Modeling of burr height in ultrasonic-assisted drilling of DD6 superalloy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "3-4", "Page": "2167", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Burr formation mechanism and machining parameter effect in slot micro-milling titanium alloy Ti6Al4V", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "5-6", "Page": "2073", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Influence of machining parameters in longitudinal-torsional ultrasonic vibration milling titanium alloy for milling force", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "9-10", "Page": "3587", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Optimization model for ultrasonic-assisted dry helical milling of CFRP based on genetic algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "131", "Issue": "5-6", "Page": "2133", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Analytical modeling of Poisson burr formation in the machining of Al6061 with interface constraint", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "129", "Issue": "1-2", "Page": "353", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114363060, "Title": "Call for Papers: IEEE Computer Society", "Abstract": "", "Keywords": "", "DOI": "10.1109/MAHC.2024.3374488", "PubYear": 2024, "Volume": "46", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [], "References": []}, {"ArticleId": 114363230, "Title": "The Computer History Museum's 2023 in Review", "Abstract": "Presents a summary of the events, exhibits, and programs sponsored by the The Computer History Museum in 2023.", "Keywords": "", "DOI": "10.1109/MAHC.2024.3369258", "PubYear": 2024, "Volume": "46", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer History Museum, Mountain View, CA, USA"}], "References": []}, {"ArticleId": 114363245, "Title": "Enhancing business by procuring and dispensing items among supply chain members using multi-objective approach", "Abstract": "", "Keywords": "", "DOI": "10.1080/02286203.2024.2334977", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4075, "JournalTitle": "International Journal of Modelling and Simulation", "ISSN": "0228-6203", "EISSN": "1925-7082", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Technology, Pandit Deendayal Energy University, Raisan, Gandhinagar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Technology, Pandit Deendayal Energy University, Raisan, Gandhinagar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Management, Nirma University, Ahmedabad, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Technology, Nirma University, Ahmedabad, India"}, {"AuthorId": 5, "Name": "N R N V <PERSON><PERSON>", "Affiliation": "Department of Agricultural Engineering, Faculty of Agricultural Sciences, Rajiv Gandhi University, Papumpare, Arunachal Pradesh, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Manipal University Jaipur, Jaipur, Rajasthan, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Manipal University Jaipur, Jaipur, Rajasthan, India"}], "References": [{"Title": "Real-time large-scale supplier order assignments across two-tiers of a supply chain with penalty and dual-sourcing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "176", "Issue": "", "Page": "108928", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Hybrid MCDM and simulation-optimization for strategic supplier selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "219", "Issue": "", "Page": "119624", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114363325, "Title": "USIR-Net: sand-dust image restoration based on unsupervised learning", "Abstract": "<p>In sand-dust weather, the influence of sand-dust particles on imaging equipment often results in images with color deviation, blurring, and low contrast, among other issues. These problems making many traditional image restoration methods unable to accurately estimate the semantic information of the images and consequently resulting in poor restoration of clear images. Most current image restoration methods in the field of deep learning are based on supervised learning, which requires pairing and labeling a large amount of data, and the possibility of manual annotation errors. In light of this, we propose an unsupervised sand-dust image restoration network. The overall model adopts an improved CycleGAN to fit unpaired sand-dust images. Firstly, multiscale skip connections in the multiscale cascaded attention module are used to enhance the feature fusion effect after downsampling. Secondly, multi-head convolutional attention with multiple input concatenations is employed, with each head using different kernel sizes to improve the ability to restore detail information. Finally, the adaptive decoder-encoder module is used to achieve adaptive fitting of the model and output the restored image. According to the experiments conducted on the dataset, the qualitative and quantitative indicators of USIR-Net are superior to the selected comparison algorithms, furthermore, in additional experiments conducted on haze removal and underwater image enhancement, we have demonstrated the wide applicability of our model.</p>", "Keywords": "Image denoising; Image enhancement; Unsupervised adversarial learning; Adaptive learning; Sand-dust image restoration", "DOI": "10.1007/s00138-024-01528-0", "PubYear": 2024, "Volume": "35", "Issue": "3", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electronics and Information Engineering, Lanzhou Jiaotong University, Lanzhou, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Lanzhou Jiaotong University, Lanzhou, China"}], "References": [{"Title": "Single image dehazing by approximating and eliminating the additional airlight component", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "400", "Issue": "", "Page": "294", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114363343, "Title": "Ensemble of deep learning techniques to human activity recognition using smart phone signals", "Abstract": "<p>Human Activity Recognition (HAR) has become a significant area of study in the fields of health, human behavior analysis, the Internet of Things, and human–machine interaction in recent years. Smartphones are a popular choice for HAR as they are common devices used in daily life. However, most available HAR datasets are gathered in laboratory settings, which do not reflect real-world scenarios. To address this issue, a real-world dataset using smartphone inertial sensors, involving 62 individuals, is collected. The collected dataset is noisy, small, and has variable frequency. On the other hand, in the context of HAR, algorithms face additional challenges due to intra-class diversity (which refers to differences in the characteristics of performing an activity by different people or by the same individual under different conditions) and inter-class similarity (which refers to different activities that are highly similar). Consequently, it is essential to extract features accurately from the dataset. Ensemble learning, which combines multiple models, is an effective approach to improve generalization performance. In this paper, a weighted ensemble of hybrid deep models for HAR using smartphone sensors is proposed. The proposed ensemble approach demonstrates superior performance compared to current methods, achieving impressive results across multiple evaluation metrics. Specifically, the experimental analysis demonstrates an accuracy of 97.15%, precision of 96.41%, recall of 95.62%, and an F1-score of 96.01%. These results demonstrate the effectiveness of our ensemble approach in addressing the challenges of HAR in real-world scenarios.</p>", "Keywords": "Human Activity Recognition; Ensemble learning; Deep Learning; Time series classification; Real-world dataset; Smartphone inertial sensors", "DOI": "10.1007/s11042-024-18935-0", "PubYear": 2024, "Volume": "83", "Issue": "42", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, University of Tabriz, Tabriz, Iran; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, RMIT University, Melbourne, Australia"}], "References": [{"Title": "Multi-user activity recognition: Challenges and opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "121", "JournalTitle": "Information Fusion"}, {"Title": "A review and categorization of techniques on device-free human activity recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "102738", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Syntax-type-aware graph convolutional networks for natural language understanding", "Authors": "Chunning Du; <PERSON><PERSON>; Haifeng Sun", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107080", "JournalTitle": "Applied Soft Computing"}, {"Title": "Device-free single-user activity recognition using diversified deep ensemble learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107066", "JournalTitle": "Applied Soft Computing"}, {"Title": "An ensemble of autonomous auto-encoders for human activity recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "439", "Issue": "", "Page": "271", "JournalTitle": "Neurocomputing"}, {"Title": "Data augmentation techniques in natural language processing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109803", "JournalTitle": "Applied Soft Computing"}, {"Title": "Smart farming using artificial intelligence: A review", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105899", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114363543, "Title": "Lightweight group authentication protocol for secure RFID system", "Abstract": "<p>Nowadays, wireless technology has been widely used in healthcare and communication systems. It makes our life easier in all respects. A radiofrequency identification device (RFID) has been deployed as a wireless and identity communication device. RFID is a low-resource device that requires cryptography with limited energy regarding the minimum key size. Security and authentication between the server and the tags are key challenges for an RFID system to maintain data privacy. This article presents the security vulnerabilities of recent existing RFID authentication schemes. Keeping the focus on stringent security, privacy, and low cost, we have designed a new lightweight group authentication protocol for the robust RFID system. A single server controls multiple tags by using the proposed lightweight protocol in the RFID system. Formal and informal security analysis is performed compared to other lightweight group authentication articles in which only informal security analysis is carried out. The formal security strength of our proposed protocol is analyzed using the AVISPA (Automated Verification of Internet Security Protocol Analysis) tool, confirming that it is safe from different security threats. The newly designed protocol’s performance analysis results are measured in terms of computational cost, storage space, and communication cost. Finally, the combined consequence of security and lightweight of the proposed group authentication protocol is superior and outperforms compared to the existing scheme.</p>", "Keywords": "Radiofrequency identification device; Lagrange interpolation; AVISPA tool; Security; Authentication", "DOI": "10.1007/s11042-024-19013-1", "PubYear": 2024, "Volume": "83", "Issue": "41", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Scholar, Computer Science and Engineering, I<PERSON><PERSON><PERSON>. (I.S.M.), Dhanbad, India; Corresponding author."}, {"AuthorId": 2, "Name": "Haider Banka", "Affiliation": "Associate Professor, Computer Science and Engineering, I<PERSON>I<PERSON>T. (I.S.M.), Dhanbad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Associate Professor, Computer Science and Engineering, SMVDU, Katra, India"}], "References": [{"Title": "Quadratic residue-based unilateral authentication protocol for RFID system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "11", "Page": "16533", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": *********, "Title": "An RML-FNML module for Python user-defined functions in Morph-KGC", "Abstract": "The RML mapping language declares schema transformations to map heterogeneous data into knowledge graphs. Although the schema transformations provided by RML are sufficient for simple use cases, large real-world ones typically involve diverse data and require complex computations. User-defined functions provide the flexibility to enable the creation and application of knowledge graphs for these use cases and also allow reusing existing software packages for data processing in multiple domains. In this work, we present an implementation for data transformations in Morph-KGC. This implementation has the following benefits: (i) it conforms to RML-FNML, the standard module for data transformations in RML, (ii) it can handle user-defined functions written in Python, a widely used programming language for data processing, (iii) it includes support for YARRRML, a user-friendly syntax of RML to maximize usability. The implementation is currently being used by BASF, a large multinational chemical company, to semantically integrate its industrial data in a maintainable and reproducible manner.", "Keywords": "Knowledge graphs ; Python user-defined functions ; RML-FNML ; Chemical industry", "DOI": "10.1016/j.softx.2024.101709", "PubYear": 2024, "Volume": "26", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-Guerrero", "Affiliation": "Universidad Politécnica de Madrid, Madrid, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>-Arias", "Affiliation": "BASF Digital Solutions, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "BASF Digital Solutions, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "BASF, Ludwigshafen am Rhein, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "BASF Digital Solutions, Madrid, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Universidad Politécnica de Madrid, Madrid, Spain"}], "References": [{"Title": "Implementation-independent function reuse", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "946", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Knowledge Graphs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Morph-KGC: Scalable knowledge graph materialization with mapping partitions", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "1", "Page": "1", "JournalTitle": "Semantic Web"}, {"Title": "Declarative RDF graph generation from heterogeneous (semi-)structured data: A systematic literature review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "75", "Issue": "", "Page": "100753", "JournalTitle": "Journal of Web Semantics"}, {"Title": "Scaling up knowledge graph creation to large and heterogeneous data sources", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "75", "Issue": "", "Page": "100755", "JournalTitle": "Journal of Web Semantics"}, {"Title": "Knowledge Graph Construction with a\n Façade \n : A Unified Method to Access Heterogeneous Data Sources on the Web", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Systematic Construction of Knowledge Graphs for Research-Performing Organizations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "12", "Page": "562", "JournalTitle": "Information"}]}, {"ArticleId": 114363585, "Title": "Research on theoretical prediction method of rivet-forming quality considering different riveted structure parameters", "Abstract": "<p>Rivet connection is one of the most important connection technologies. The main factors affecting the rivet-forming quality of riveted structures are squeezing force, squeezing velocity, and materials of rivets and plates, which significantly impact the damage and failure of riveted holes in riveted structures. This paper provides a detailed study of the rivet-forming quality of riveted structures under different dynamic loads. A theoretical analysis method for the nonlinear dynamic plastic behavior of riveted structures is proposed to predict and investigate the rivet-forming quality of riveted structures under different squeezing forces, velocities, and materials. The proposed theoretical analysis method for the rivet-forming quality of riveted structures is conducted using the explicit dynamics method. Furthermore, the nonlinear dynamic plasticity theory method and nonlinear explicit dynamics model calculation results of riveted structures are compared with measured and Ultra Plus field scanning electron microscope scanning values. The results confirm the accuracy of the nonlinear dynamic plasticity theory method for riveted structures. In addition, the effects of different squeezing forces, velocities, rivet material, and plate material properties on the rivet-forming quality of riveted structures are also studied. This paper proposes a theoretical analysis method for the rivet-forming quality of riveted structures, providing an optimal squeezing force and velocity for rivet hole damage and rivet failure under different rivet and plate materials.</p>", "Keywords": "Rivet-forming; Riveted structures; Squeezing force; Squeezing velocity; Nonlinear dynamic", "DOI": "10.1007/s00170-024-13441-x", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Guocheng Lv", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "AVIC Shenyang Aircraft Company Limited, Shenyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}], "References": []}, {"ArticleId": *********, "Title": "Identification and control of systems with hysteresis: concepts and tools", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJMIC.2024.10063332", "PubYear": 2024, "Volume": "44", "Issue": "1", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "Petrus E. O. G. B. Abreu", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114363888, "Title": "K1K2NN: A novel multi-label classification approach based on neighbors for predicting COVID-19 drug side effects", "Abstract": "COVID-19, a novel ailment, has received comparatively fewer drugs for its treatment. Side Effects (SE) of a COVID-19 drug could cause long-term health issues. Hence, SE prediction is essential in COVID-19 drug development. Efficient models are also needed to predict COVID-19 drug SE since most existing research has proposed many classifiers to predict SE for diseases other than COVID-19. This work proposes a novel classifier based on neighbors named K<sub>1</sub> K<sub>2</sub> Nearest Neighbors (K<sub>1</sub>K<sub>2</sub>NN) to predict the SE of the COVID-19 drug from 17 molecules&#x27; descriptors and the chemical 1D structure of the drugs. The model is implemented based on the proposition that chemically similar drugs may be assigned similar drug SE, and co-occurring SE may be assigned to chemically similar drugs. The K<sub>1</sub>K<sub>2</sub>NN model chooses the first K<sub>1</sub> neighbors to the test drug sample by calculating its similarity with the train drug samples. It then assigns the test sample with the SE label having the majority count on the SE labels of these K<sub>1</sub> neighbor drugs obtained through a voting mechanism. The model then calculates the SE-SE similarity using the Jaccard similarity measure from the SE co-occurrence values. Finally, the model chooses the most similar K<sub>2</sub> SE neighbors for those SE determined by the K<sub>1</sub> neighbor drugs and assigns these SE to that test drug sample. The proposed K<sub>1</sub>K<sub>2</sub>NN model has showcased promising performance with the highest accuracy of 97.53% on chemical 1D drug structure and outperforms the state-of-the-art multi-label classifiers. In addition, we demonstrate the successful application of the proposed model on gene expression signature datasets, which aided in evaluating its performance and confirming its accuracy and robustness.", "Keywords": "COVID-19;Chemical Properties;Drug Development;Machine Learning;Multi-Label;Side Effects", "DOI": "10.1016/j.compbiolchem.2024.108066", "PubYear": 2024, "Volume": "110", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, National Institute of Technology Nagaland, Chumukedima, Dimapur, Nagaland 797103, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, National Institute of Technology Nagaland, Chumukedima, Dimapur, Nagaland 797103, India"}], "References": [{"Title": "Kernelized Supervised Laplacian Eigenmap for Visualization and Classification of Multi-Label Data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108399", "JournalTitle": "Pattern Recognition"}, {"Title": "A global report on the dynamics of COVID-19 with quarantine and hospitalization: A fractional order model with non-local kernel", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "107645", "JournalTitle": "Computational Biology and Chemistry"}, {"Title": "Design and various in silico studies of the novel curcumin derivatives as potential candidates against COVID-19 -associated main enzymes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "107657", "JournalTitle": "Computational Biology and Chemistry"}, {"Title": "Mutation in Eth A protein of Mycobacterium tuberculosis conferred drug tolerance against enthinoamide in Mycobacterium smegmatis mc2155", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "107677", "JournalTitle": "Computational Biology and Chemistry"}, {"Title": "Mathematical modeling and analysis of the SARS-Cov-2 disease with reinfection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "107678", "JournalTitle": "Computational Biology and Chemistry"}, {"Title": "Semi-supervised partial multi-label classification via consistency learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108839", "JournalTitle": "Pattern Recognition"}, {"Title": "Noise-robust oversampling for imbalanced data classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109008", "JournalTitle": "Pattern Recognition"}, {"Title": "Dual computational and biological assessment of some promising nucleoside analogs against the COVID-19-Omicron variant", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "104", "Issue": "", "Page": "107768", "JournalTitle": "Computational Biology and Chemistry"}, {"Title": "Neural network for ordinal classification of imbalanced data by minimizing a Bayesian cost", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>-Vidal", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "109303", "JournalTitle": "Pattern Recognition"}, {"Title": "Physicochemical properties, drug likeness, ADMET, DFT studies, and in vitro antioxidant activity of oxindole derivatives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "104", "Issue": "", "Page": "107861", "JournalTitle": "Computational Biology and Chemistry"}]}, {"ArticleId": 114363894, "Title": "Online continual streaming learning for embedded space applications", "Abstract": "<p>This paper proposes an online continual learning (OCL) methodology tested on hardware and validated for space applications using an object detection close-proximity operations task. The proposed OCL algorithm simulates a streaming scenario and uses experience replay to enable the model to update its knowledge without suffering catastrophic forgetting by saving past inputs in an onboard reservoir that will be sampled during updates. A stream buffer is introduced to enable online training, i.e., the ability to update the model as data is streamed, one sample at a time, rather than being available in batches. Hyperparameters such as buffer sizes, update rate, batch size, batch concatenation parameters and number of iterations per batch are all investigated to find an optimized approach for the incremental domain and streaming learning task. The algorithm is tested on a customized dataset for space applications simulating changes in visual environments that significantly impact the deployed model’s performance. Our OCL methodology uses Weighted Sampling, a novel approach which allows the system to analytically choose more useful input samples during training, the results show that a model can be updated online achieving up to 60% Average Learning while Average Forgetting can be as low as 13% all with a Model Size Efficiency of 1, meaning the model size does not increase. An additional contribution is an implementation of On-Device Continual Training for embedded applications, a hardware experiment is carried out on the Zynq 7100 FPGA where a pre-trained CNN model is updated online using our FPGA backpropagation pipeline and OCL methodology to take into account new data and satisfactorily complete the planned task in less than 5 min achieving 90 FPS.</p>", "Keywords": "Continual learning; Automated deep learning; Convolutional neural networks; Streaming learning; FPGA; Space", "DOI": "10.1007/s11554-024-01438-4", "PubYear": 2024, "Volume": "21", "Issue": "3", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "Alaa Eddine Mazouz", "Affiliation": "COMELEC, Télécom Paris, Palaiseau, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LTCI, Telecom Paris, Institut Polytechnique de Pari, Palaiseau, France"}], "References": [{"Title": "Automated CNN back-propagation pipeline generation for FPGA online training", "Authors": "<PERSON><PERSON>; C. P<PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "6", "Page": "2583", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Online continual learning in image classification: An empirical survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "469", "Issue": "", "Page": "28", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114364007, "Title": "Cloud‐based provenance framework for duplicates identification and data quality enhancement", "Abstract": "Cloud computing revolutionizes data management by offering centralized repositories or services accessible over the Internet. These services, hosted by a single provider or distributed across multiple entities, facilitate seamless access for users and applications. Additionally, cloud technology enables federated search capabilities, allowing organizations to amalgamate data from diverse sources and perform comprehensive searches. However, such integration often leads to challenges in data quality and duplication due to structural disparities among datasets, including variations in metadata. This research presents a novel provenance‐based search model designed to enhance data quality within cloud environments. The model expands the traditional concept of a single canonical URL by incorporating provenance data, thus providing users with diverse search options. Leveraging this model, the study conducts inferential analyses to improve data accuracy and identify duplicate entries effectively. To verify the proposed model, two research paper datasets from Kaggle and DBLP repositories are utilized, and the model effectively identifies duplicates, even with partial queries. Tests demonstrate the system's ability to remove duplicates based on title or author, in both single and distributed dataset scenarios. Traditional search engines struggle with duplicate content, resulting in biased results or inefficient crawling. In contrast, this research uses provenance data to improve search capabilities, overcoming these limitations.", "Keywords": "", "DOI": "10.1111/exsy.13600", "PubYear": 2025, "Volume": "42", "Issue": "1", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Computer Science King Fahd University of Petroleum & Minerals  Dhahran Saudi Arabia;Interdisciplinary Research Centre for Intelligent Secure Systems King Fahd University of Petroleum & Minerals  Dhahran Saudi Arabia;SDAIA‐KFUPM Joint Research Center for Artificial Intelligence, KFUPM  Dhahran Saudi Arabia"}], "References": [{"Title": "Pipeline provenance for cloud‐based big data analytics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "5", "Page": "658", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Provenance documentation to enable explainable and trustworthy AI: A literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "1", "Page": "139", "JournalTitle": "Data Intelligence"}, {"Title": "A novel semantic-aware search scheme based on BCI-tree index over encrypted cloud data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "5", "Page": "3055", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 114364063, "Title": "Euclidean embedding with preference relation for recommender systems", "Abstract": "<p>Recommender systems (RS) help users pick the relevant items among numerous items that are available on the internet. The items may be movies, food, books, etc. The Recommender systems utilize the data that is fetched from the users to generate recommendations. Usually, these ratings may be explicit or implicit. Explicit ratings are absolute ratings that are generally in the range of 1 to 5. While implicit ratings are derived from information like purchase history, click-through rate, viewing history, etc. Preference relations are an alternative way to represent the users’ interest in the items. Few recent research works show that preference relations yield better results compared to absolute ratings. Besides, in RS, the latent factor models like Matrix Factorization (MF) give accurate results especially when the data is sparse. Euclidean Embedding (EE) is an alternative latent factor model that yields similar results as MF. In this work, we propose a Euclidean embedding with preference relation for the recommender system. Instead of using the inner product of items and users’ latent factors, Euclidean distances between them are used to predict the rating. Preference Relations with Matrix Factorization (MFPR) produced better recommendations compared to that of traditional matrix factorization. We present a collaborative model termed EEPR in this work. The proposed framework is implemented and tested on two real-world datasets, MovieLens-100K and Netflix-1M to demonstrate the effectiveness of the proposed method. We utilize popular evaluation metric for recommender systems as precision@K. The experimental outcomes show that the proposed model outperforms certain state-of-the-art existing models such as MF, EE, and MFPR.</p>", "Keywords": "Recommender system (RS); Collaborative filtering (CF); Cold start problem; Preference relation; Matrix factorization (MF); Euclidean Embedding (EE)", "DOI": "10.1007/s11042-024-18885-7", "PubYear": 2024, "Volume": "83", "Issue": "42", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "V Ramanjaneyulu Yannam", "Affiliation": "National Institute of Technology Rourkela, Rourkela, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Rourkela, Rourkela, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology, Design and Manufacturing, Kurnool, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Rourkela, Rourkela, India"}], "References": [{"Title": "A collaborative filtering recommendation system with dynamic time decay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "1", "Page": "244", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Presentation of a recommender system with ensemble learning and graph embedding: a case on MovieLens", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "7805", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A deep learning based algorithm for multi-criteria recommender systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106545", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel context-aware recommender system based on a deep sequential learning approach (CReS)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "17", "Page": "11067", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Embedding metadata using deep collaborative filtering to address the cold start problem for the rating prediction task", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "12", "Page": "18553", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comprehensive analysis on movie recommendation system employing collaborative filtering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "19", "Page": "28647", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A two-stage embedding model for recommendation with multimodal auxiliary information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "582", "Issue": "", "Page": "22", "JournalTitle": "Information Sciences"}, {"Title": "A trust-worthy approach to recommend movies for communities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "14", "Page": "19655", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An Adaptive Temporal-Concept Drift Model for Sequential Recommendation", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "2", "Page": "222", "JournalTitle": "ECTI Transactions on Computer and Information Technology (ECTI-CIT)"}, {"Title": "An improved deep sequential model for context-aware POI recommendation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "1", "Page": "1643", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Collaborative filtering and kNN based recommendation to overcome cold start and sparsity issues: A comparative analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "25", "Page": "35693", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114364120, "Title": "Performance evaluation of all intra Kvazaar and x265 HEVC encoders on embedded system Nvidia Jetson platform", "Abstract": "<p>The growing demand for high-quality video requires complex coding techniques that cost resource consumption and increase encoding time which represents a challenge for real-time processing on Embedded Systems. Kvazaar and x265 encoders are two efficient implementations of the High-Efficient Video Coding (HEVC) standard. In this paper, the performance of All Intra Kvazaar and x265 encoders on the Nvidia Jetson platform was evaluated using two coding configurations; highspeed preset and high-quality preset. In our work, we used two scenarios, first, the two encoders were run on the CPU, and based on the average encoding time Kvazaar proved to be 65.44% and 69.4% faster than x265 with 1.88% and 0.6% BD-rate improvement over x265 at high-speed and high-quality preset, respectively. In the second scenario, the two encoders were run on the GPU of the Nvidia Jetson, and the results show the average encoding time under each preset is reduced by half of the CPU-based scenario. In addition, Kvazaar is 54.5% and 56.70% faster with 1.93% and 0.45% BD-rate improvement over x265 at high-speed and high-quality preset, respectively. Regarding the scalability, the two encoders on the CPU are linearly scaled up to four threads and speed remains constant afterward. On the GPU, the two encoders are scaled linearly with the number of threads. The obtained results confirmed that, Kvazaar is more efficient and that it can be used on Embedded Systems for real-time video applications due to its high speed and performance over the x265 HEVC encoder</p>", "Keywords": "All intra; Computational time complexity; Kvazaar HEVC encoder; Jetson TX1; NVIDIA GPU; x265 HEVC encoder", "DOI": "10.1007/s11554-024-01429-5", "PubYear": 2024, "Volume": "21", "Issue": "3", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, Egypt-Japan University of Science and Technology, Alexandria, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, Egypt-Japan University of Science and Technology, Alexandria, Egypt; Department of Electrical and Electronics Engineering, Assiut University, Assiut, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Science and Electrical Engineering, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, Egypt-Japan University of Science and Technology, Alexandria, Egypt; Department of Electronics and Communications Engineering, Zagazig University, Zagazig, Egypt"}], "References": []}, {"ArticleId": 114364161, "Title": "Clustering Algorithms in Sentiment Analysis Techniques in Social Media – A Rapid Literature Review", "Abstract": "Based on the high dynamic of Sentiment Analysis (SA) topic among the latest publication landscape, the current review attempts to fill a research gap. Consequently, the paper elaborates on the most recent body of literature to extract and analyze the papers that elaborate on the clustering algorithms applied on social media datasets for performing SA. The current rapid review attempts to answer the research questions by analyzing a pool of 46 articles published in between Dec 2020 – Dec 2023. The manuscripts were thoroughly selected from Scopus (Sco) and WebOf-Science (WoS) databases and, after filtering the initial pool of 164 articles, the final results (46) were extracted and read in full. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "Clustering algorithms; DBSCAN; HAC; K-means; natural language processing techniques; sentiment analysis; social media datasets; Twitter/X", "DOI": "10.14569/IJACSA.2024.0150314", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Accounting, Information Systems and Statistics Department, <PERSON><PERSON>ru <PERSON> University of Iasi, Iasi, Romania"}], "References": []}, {"ArticleId": *********, "Title": "Model-aided and vision-based navigation for an aerial robot in real-time application", "Abstract": "<p>In this paper, a novel navigation method with the assistance of a vehicle dynamic model (VDM), known as the VDM-aided navigation method, is introduced. This method is specifically designed for a subset of fixed-wing aerial robots within the broader category of unmanned aerial vehicles. Vision-based navigation (VBN) is employed to increase accuracy while maintaining reliability in Global Navigation Satellite System (GNSS) outages. In addition, an unscented Kalman filter (UKF) is used to estimate navigation parameters, including speed, position and attitude. This method uses the dynamic system as a process model and employs VBN, barometric altitude and vertical gyro as measurement inputs. In VBN, the method of scale-invariant feature transform is used as a method for image matching. To ensure the real-time capability of this method with the existing microprocessor, a hardware-in-the-loop (HIL) laboratory has been utilized. According to nonlinear observability methods, one can show the proposed integrated nonlinear navigation is observable under all conditions. Finally, the results of the HIL laboratory demonstrate that the proposed approach can estimate the robot navigation parameters with an acceptable level of precision even in the absence of an Inertial Navigation System (INS) and GNSS. It was validated even when there was an error of up to 20% in VDM parameters. Furthermore, an investigation was carried out regarding the use of Extended Kalman Filter instead of the UKF for the integrated navigation output. In GNSS outage conditions, considering both accuracy and cost, this method can serve as a valuable alternative for aerial robots. In addition, this approach can be recommended for INS fault detection with or without GNSS. Additionally, the integrated navigation provided can substitute the GNSS/INS system during fault conditions.</p>", "Keywords": "Aerial robot; Vehicle dynamic model-aided (VDM-aided); Vision-based navigation (VBN); Invariant feature transform (SIFT); Hardware in the loop (HIL)", "DOI": "10.1007/s11370-024-00532-7", "PubYear": 2024, "Volume": "17", "Issue": "4", "JournalId": 2152, "JournalTitle": "Intelligent Service Robotics", "ISSN": "1861-2776", "EISSN": "1861-2784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Engineering Department, UAV Lab., K. N. Toosi University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Aerospace Engineering Department, UAV Lab., K. N. Toosi University of Technology, Tehran, Iran; Corresponding author."}], "References": [{"Title": "Hardware-in-the-loop real-time validation of micro-satellite attitude control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "85", "Issue": "", "Page": "106679", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Autonomous drone race: A computationally efficient vision-based navigation and control strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "103621", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "2D object recognition: a comparative analysis of SIFT, SURF and ORB feature descriptors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "12", "Page": "18839", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "What if there was no revisit? Large-scale graph-based SLAM with traffic sign detection in an HD map using LiDAR inertial odometry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "2", "Page": "161", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "An improved multi-object classification algorithm for visual SLAM under dynamic environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "39", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "A tightly coupled monocular visual lidar odometry with loop closure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "129", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Object-aware data association for the semantically constrained visual SLAM", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "2", "Page": "155", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "IQ-VIO: adaptive visual inertial odometry via interference quantization under dynamic environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "5", "Page": "565", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Lmapping: tightly-coupled LiDAR-inertial odometry and mapping for degraded environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "5", "Page": "583", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Autonomous Vision-Based Algorithm for Interplanetary Navigation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "47", "Issue": "9", "Page": "1792", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 114364227, "Title": "Multi-Objective Reinforcement Learning for Virtual Machines Placement in Cloud Computing", "Abstract": "The rapid demand for cloud services has provoked cloud providers to efficiently resolve the problem of Virtual Machines Placement in the cloud. This paper presents a VM Placement using Reinforcement Learning that aims to provide optimal resource and energy management for cloud data centers. Reinforcement Learning provides better decision-making as it solves the complexity of VM Placement problem caused due to tradeoff among the objectives and hence is useful for mapping requested VM on the minimum number of Physical Machines. An enhanced Tournament-based selection strategy along with Roulette Wheel sampling has been applied to ensure that the optimization goes through balanced exploration and exploitation, thereby giving better solution quality. Two heuristics have been used for the ordering of VM, considering the impact of CPU and memory utilizations over the VM placement. Moreover, the concept of the Pareto approximate set has been considered to ensure that both objectives are prioritized according to the perspective of the users. The proposed technique has been implemented on MATLAB 2020b. Simulation analysis showed that the VMRL performed preferably well and has shown improvement of 17%, 20% and 18% in terms of energy consumption, resource utilization and fragmentation respectively in comparison to other multi-objective algorithms. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "cloud computing; energy consumption; reinforcement learning; resource utilization; Virtual machines placement", "DOI": "10.14569/IJACSA.2024.01503105", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Manipal University Jaipur, Rajasthan, Jaipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Manipal University Jaipur, Rajasthan, Jaipur, India"}], "References": []}, {"ArticleId": 114364261, "Title": "Method for Disaster Area Detection with Just One SAR Data Acquired on the Day After Earthquake Based on YOLOv8", "Abstract": "Method for earthquake disaster area detection with just a single satellite-based SAR data which is acquired on the day after earthquake based on object detection method of YOLOv8 and Detectron2 is proposed. Through experiments with several SAR data derived from the different SAR satellites which observed Noto Peninsula earthquake occurred on the first of January 2024, it is found that the proposed method works well to detect several types of damages effectively. Also, it is found that the proposed method based on “Roboflow” and YOLOv8 as well as Detectron2 for annotation and object detection is appropriate for disaster area detection. Furthermore, it is possible to detect disaster areas even if just one single SAR data which acquired on the day after the disaster occurred because the trained learning model for disaster area detection is created through experiments. © (2024) Science and Information Organization.", "Keywords": "Detectron2; disaster; disaster area detection; earthquake; noto peninsula earthquake; SAR; YOLOv8", "DOI": "10.14569/IJACSA.2024.0150344", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science Dept., Saga University, Saga City, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Science Dept., Saga University, Saga City, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science Dept., Saga University, Saga City, Japan"}], "References": []}, {"ArticleId": 114364277, "Title": "An Integrated CNN-BiLSTM Approach for Facial Expressions", "Abstract": "Deep learning algorithms have demonstrated good performance in many sectors and applications. Facial expression recognition (FER) is recognizing the emotions through images. FER is an integral part of many applications. With the help of the CNN-BiLSTM integrated approach, higher accuracy can be achieved in identification of the facial expressions. Convolutional neural networks (CNN) consist of a Conv2D layer, dividing the given images into batches, performing normalization and if required flattening the data i.e. converting the data in a 1D array and achieving a higher accuracy. BiLSTM works on two LSTMs i.e. one in the forward direction and the other in a backward direction. One can use LSTM to process the images (datasets) however, it is suggested with the help of BiLSTM can predict the expressions with more accuracy. Input data is available in both the direction (forward and backward) which helps maintaining the context. Using LSTM CNN and BiLSTM always helps increasing the prediction accuracy. Application areas where a BiLSTM can give more prediction accuracy are the forecasting models, text recognition, speech recognition, classifying the large data and the proposed facial expression recognition. The integrated approach (CNN and BiLSTM) increases the accuracy significantly as discussed in the results and discussion section. This approach could be categorized as a fusion technique where two methods (approaches) are integrated to get higher accuracy. The results and discussion section elaborates the effectiveness of the integrated approach compared to HERO: human emotions recognition for realizing the intelligent internet of things. As compared to the HERO approach CNN-BiLSTM gives good results in terms of precision and recall. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "BiLSTM (Bi Directional Long Short Term Memory); CNN (Convolutional Neural Network); deep learning; facial expression recognition; flattening", "DOI": "10.14569/IJACSA.2024.0150398", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "B. H. Pansambal", "Affiliation": "Department of Electronics and Telecommunications Engineering, <PERSON><PERSON><PERSON><PERSON>kar Technological University, Lonere, 402103, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electronics and Telecommunications Engineering, <PERSON><PERSON><PERSON><PERSON>kar Technological University, Lonere, 402103, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electronics and Telecommunications Engineering, <PERSON><PERSON><PERSON><PERSON>kar Technological University, Lonere, 402103, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technical Education, Maharashtra, India"}], "References": []}, {"ArticleId": 114364285, "Title": "Utilizing the Metaverse in Astrosociology: Examine Students' Perspectives of Space Science Education", "Abstract": "Big economic countries must invest in space skills to create a favorable business environment, particularly in KSA considering the present mindset in outer space. KSA's vast landmass is a tremendous asset that makes it the perfect position to provide space services throughout the Middle East and the world. Space science education is becoming increasingly important, requiring advanced technology and computational skills to benefit early-career scientists. The Ministry of Education in KSA has declared that students will take Earth and Space Sciences to prepare them for global competition. Traditional learning experiences seem to have little to no impact on students' conceptual understandings of the space science courses. The sociological interests of Generation Z serve as the foundation for modern Metaverse approaches. Students' comprehension and interest in studying space and the galaxy are increased by provided a simulation of space travel using metaverse technology. The major goal of this study is to underline the significance and usefulness of employing metaverse technology while creating a new space science curriculum to advance knowledge in the field of space scientific education. Another goal is to introduce the value of astrosociology in understanding how people might interact with one another in space. A voluntary survey was completed by 39 students prior to their training in the metaverse space simulation as part of this study. They then used the space simulation with careful observation. After that, they reply to a follow-up survey. The findings supported the suggestion that the metaverse should be included in space science curricula. A number of comments and interests also arise on the viability of space travel, social interaction, and the advantages of using the metaverse to research these issues. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "astrosociology; Metaverse; space science education; space simulation; virtual reality", "DOI": "10.14569/IJACSA.2024.0150373", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Metaverse Lab, Faculty of Computers and Information Technology, University of Tabuk, Saudi Arabia"}], "References": []}, {"ArticleId": *********, "Title": "Enhance Telecommunication Security Through the Integration of Support Vector Machines", "Abstract": "This research investigates the escalating issue of telephone-based fraud in Indonesia, a consequence of enhanced connectivity and technological advancements. As the telecommunications sector expands, it faces increased threats from sophisticated criminal activities, notably voice call fraud, which leads to significant financial losses and diminishes trust in digital systems. This study presents a novel security system that leverages the capabilities of Support Vector Machines (SVM) for the advanced classification of complex patterns inherent in fraudulent activities. By integrating SVM algorithms, this system aims to effectively process and analyze large volumes of data to identify and prevent fraudulent acts. The utilization of SVM in our proposed framework represents a significant strategy to combat the adaptive and evolving tactics of cybercriminals, thereby bolstering the resilience of telecommunications infrastructure. Upon further refinement, the system exhibited a substantial improvement in identifying fraudulent activities, with accuracy rates increasing from 81% to 86%. This enhancement underscores the system's efficacy in real-world scenarios. Our research underscores the critical need to marry technological innovations with ethical and privacy considerations, highlighting the role of public awareness and education in augmenting security measures. The development of this SVM-based security system constitutes a pivotal step towards reinforcing Indonesia's telecommunications infrastructure, contributing to the national objective of securing the digital economy and fostering a robust digital ecosystem. By addressing current and future cyber threats, this approach exemplifies Indonesia's commitment to leveraging technology for societal welfare, ensuring a secure and prosperous digital future for its citizens. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "artificial intelligence; Call security system; data analysis; fraud detection system; support vector machine", "DOI": "10.14569/IJACSA.2024.0150364", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatic Engineering, Politeknik Negeri Bengkalis, Riau, Bengkalis, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Utara Malaysia, Kedah, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Utara Malaysia, Kedah, Malaysia; Institute for Advanced and Smart Digital Opportunities (IASDO), Universiti Utara Malaysia, Kedah, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computing, Universiti Utara Malaysia, Kedah, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Creative Industry Management and Performing Arts, Universiti Utara Malaysia, Kedah, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Utara Malaysia, Kedah, Malaysia; Institute for Advanced and Smart Digital Opportunities (IASDO), Universiti Utara Malaysia, Kedah, Malaysia"}, {"AuthorId": 7, "Name": "Jaroji -", "Affiliation": "Department of Informatic Engineering, Politeknik Negeri Bengkalis, Riau, Bengkalis, Indonesia"}, {"AuthorId": 8, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "School of Multimedia Technology and Communication, Universiti Utara Malaysia, Kedah, Malaysia"}], "References": []}, {"ArticleId": 114364415, "Title": "Data-Driven Rice Yield Predictions and Prescriptive Analytics for Sustainable Agriculture in Malaysia", "Abstract": "Maximizing rice yield is critical for ensuring food security and sustainable agriculture in Malaysia. This research investigates the impact of environmental conditions and management methods on crop yields, focusing on accurate predictions to inform decision-making by farmers. Utilizing machine learning algorithms as decision-support tools, the study analyses commonly used models—Linear Regression, Support Vector Machines, Random Forest, and Artificial Neural Networks—alongside key environmental factors such as temperature, rainfall, and historical yield data. A comprehensive dataset for rice yield prediction in Malaysia was constructed, encompassing yield data from 2014 to 2018. To elucidate the influence of climatic factors, long-term rainfall records spanning 1981 to 2018 were incorporated into the analysis. This extensive dataset facilitates the exploration of recent agricultural trends in Malaysia and their relationship to rice yield. The study specifically evaluates the performance of Random Forest, Support Vector Machine (SVM), and Neural Network (NN) models using metrics like Correlation Coefficient, Mean Absolute Error (MAE), Root Mean Squared Error (RMSE), Mean Squared Error (MSE), and Mean Absolute Percentage Error (MAPE). Results reveal Random Forest as the standout performer with a Correlation Coefficient of 0.954, indicating a robust positive linear relationship between predictions and actual yield data. SVM and NN also exhibit respectable Correlation Coefficients of 0.767 and 0.791, respectively, making them effective tools for rice yield prediction in Malaysia. By integrating diverse environmental and management factors, the proposed methodology enhances prediction accuracy, enabling farmers to optimize practices for better economic outcomes. This approach holds significant potential for contributing to sustainable agriculture, improved food security, and enhanced economic efficiency in Malaysia's rice farming sector. Leveraging machine learning, the research aims to transform rice yield prediction into a proactive decision-making tool, fostering a resilient and productive agrarian ecosystem in Malaysia. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "artificial neural network; linear regression; predictive analytics; Rice yield prediction; support vector machine; sustainable agriculture", "DOI": "10.14569/IJACSA.2024.0150337", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Department of Computer Science, University Putra Malaysia, Selangor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Department of Computer Science, University Putra Malaysia, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Department of Computer Science, University Putra Malaysia, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Department of Computer Science, University Putra Malaysia, Selangor, Malaysia"}], "References": []}, {"ArticleId": 114364467, "Title": "Assessing the impact of heat vulnerability on urban public spaces using a fuzzy-based unified computational technique", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-01904-4", "PubYear": 2025, "Volume": "40", "Issue": "2", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Saswat Kishore Mishra", "Affiliation": ""}], "References": [{"Title": "Analyzing the Implications of Healthcare Data Breaches through Computational Technique", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "3", "Page": "1763", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 114364468, "Title": "The Critical Success Factors Influencing the Use of Mobile Learning and its Perceived Impacts in Students’ Education: A Systematic Literature Review", "Abstract": "Mobile Learning (M-learning) adoption and success in supporting students' learning engagement mainly depend on many factors. Therefore, this study systematically reviews the literature, synthesizes and analyzes the predictors of M-learning adoption, and uses success for students learning engagement. Literature from 2016 to 2023 in various databases is covered in this study. Based on the review's findings, the factors that influence students learning engagement when it comes to M-learning usage and adoption, can be divided into technical, pedagogical, and social factors. More specifically, technical factors include mobile devices availability and quality, connectivity to the internet, and user-friendly interfaces, pedagogical factors include effective instructional design, teaching methods, and assessment strategies, and social factors include motivation of students, social interaction and perceived enjoyment all these factors have a significant impact on the M-learning adoption and use success. The findings of the review also indicated that M-learning has a key role in enhancing the learning engagement of students through different ways, like increasing their motivation, attention, and participation in their process of learning, paving the way for interaction and building relationships opportunities with peers and instructors, which in turn, can lead to strengthening the learning environment. The implications of these findings extend beyond immediate educational contexts, offering vital insights for future educational technology strategies and policy decisions, particularly in addressing global educational challenges and embracing technological advancements in learning. © 2024 Korean Society for Internet Information. All rights reserved.", "Keywords": "Mobile Learning; Students Engagement; Technology Adoption and Use; Technology and Education", "DOI": "10.3837/tiis.2024.03.005", "PubYear": 2024, "Volume": "18", "Issue": "3", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 114364496, "Title": "Efficient Classification of Prostate Cancer Using Artificial Intelligence Techniques", "Abstract": "<p>As the primary cause of illness and death for men, Prostate Cancer (PCa) is a serious worldwide health problem. To maximise treatment results and raise patient survival rates, prostate cancer diagnosis and timing are essential. The rising death rate of PCa could be reduced with better treatment planning and diagnosis, both of which depend on early and accurate detection. In this paper, a comprehensive computer-aided diagnostic (CAD) system that transforms prostate cancer diagnosis is introduced. The system includes crucial steps like feature extraction, feature classification, prostate segmentation, and image denoising, providing an all-encompassing strategy for early and precise detection. A careful evaluation of the denoising filters’ effectiveness on 300 mp-Magnetic Resonance Imaging images focuses on preserving key structural features. Notably, anisotropic and Non-Local Means filters are shown to be the best options for reducing noise while maintaining image quality. The quality of diagnostic images is improved as a result of the standardization of denoising techniques in medical imaging. A remarkable 7.8% improvement in accuracy is achieved by the study’s integration of Particle Swarm Optimization (PSO) into the segmentation process. The ability of PSO-based segmentation algorithms to manage a variety of data sources and medical imaging modalities effectively is highlighted by the flexibility and robustness of these algorithms. The study uses support vector machine, multilayer perceptron, and linear discriminant analysis machine learning algorithms to accurately classify benign and malignant prostate cancer cases. A thorough evaluation framework that includes a variety of assessment metrics, including the Peak Signal Noise Ratio, Structural Similarity Index, Feature Similarity Index, Mean Opinion Score, and a Final Score, guarantees a thorough examination of the performance of the suggested method.</p>", "Keywords": "Magnetic resonance imaging (MRI); Support vector machine (SVM); Multilayer perceptron (MLP); Linear discriminant analysis (LDA)", "DOI": "10.1007/s42979-024-02745-0", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>azee<PERSON>", "Affiliation": "MIS Department, College of Business, University of Jeddah, Jeddah, Saudi Arabia; Corresponding author."}], "References": [{"Title": "Adaptive Switching Weight Mean Filter for Salt and Pepper Image Denoising", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "292", "JournalTitle": "Procedia Computer Science"}, {"Title": "DESN: An unsupervised MR image denoising network with deep image prior", "Authors": "Yazhou Zhu; Xiang Pan; Tianxu Lv", "PubYear": 2021, "Volume": "880", "Issue": "", "Page": "97", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Prostate cancer classification from ultrasound and MRI images using deep learning based Explainable Artificial Intelligence", "Authors": "Md. <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; Md. <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "462", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Patch-Based Weighted SCAD Prior for Rician Noise Removal", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "90", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Application of support vector machine algorithm for early differential diagnosis of prostate cancer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Information Technology and Data Management"}]}, {"ArticleId": 114364516, "Title": "DeepSL: Deep Neural Network-based Similarity Learning", "Abstract": "The quest for a top-rated similarity metric is inherently mission-specific, with no universally ”great” metric relevant across all domain names. Notably, the efficacy of a similarity metric is regularly contingent on the character of the challenge and the characteristics of the records at hand. This paper introduces an innovative mathematical model called MCESTA, a versatile and effective technique designed to enhance similarity learning via the combination of multiple similarity functions. Each characteristic within it is assigned a selected weight, tailor-made to the necessities of the given project and data type. This adaptive weighting mechanism enables it to outperform conventional methods by providing an extra nuanced approach to measuring similarity. The technique demonstrates significant enhancements in numerous machine learning tasks, highlighting the adaptability and effectiveness of our model in diverse applications. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "MCESTA; Siamese networks; Similarity learning; similarity metrics; triplet loss", "DOI": "10.14569/IJACSA.2024.01503136", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CSIDS, FST, University of Nouakchott, Mauritania"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CISIEV, FSTG, Cadi Ayyad University, Morocco"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CSIDS, FST, University of Nouakchott, Mauritania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CSIDS, FST, University of Nouakchott, Mauritania"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "LTI, ENSA, Chouaib Doukkali University, El Jadida, Morocco"}], "References": []}, {"ArticleId": 114364577, "Title": "An AutoML-based approach for automatic traffic incident detection in smart cities", "Abstract": "<p>In the realm of modern urban mobility, automatic incident detection is a critical element of intelligent transportation systems (ITS), since the ability to promptly identify unexpected events allows for quick implementation of preventive measures and efficient response to the situations as they arise. With the growing availability of traffic data, Machine Learning (ML) has become a vital tool for enhancing traditional incident detection methods. Automated machine-learning (AutoML) techniques present a promising solution by streamlining the machine-learning process; however the application of AutoML for incident detection has not been widely explored in scientific research In this paper, we propose and apply an AutoML-based methodology for traffic incident detection and compare it with state-ofthe-art ML approaches. Our approach integrates data preprocessing with AutoML, and uses Tree-based Pipeline Optimization Tool (TPOT) to refine the process from raw data to prediction. We have tested the efficiency of our approach in two major European cities, Athens and Antwerp. Finally, we present the limitations of our work and outline recommendations for application of AutoML in the incident detection task and potentially in other domains.</p>", "Keywords": "", "DOI": "10.3233/IDT-240231", "PubYear": 2024, "Volume": "18", "Issue": "2", "JournalId": 36670, "JournalTitle": "Intelligent Decision Technologies", "ISSN": "1872-4981", "EISSN": "1875-8843", "Authors": [{"AuthorId": 1, "Name": "Georgia Gkioka", "Affiliation": "Information Management Unit, Institute of Communication and Computer Systems (ICCS), National Technical University of Athens (NTUA), Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Scientific Researcher, Aimsun, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Information Management Unit, Institute of Communication and Computer Systems (ICCS), National Technical University of Athens (NTUA), Greece"}], "References": [{"Title": "Scaling tree-based automated machine learning to biomedical big data with a feature set selector", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "250", "JournalTitle": "Bioinformatics"}, {"Title": "Snorkel: rapid training data creation with weak supervision", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "2-3", "Page": "709", "JournalTitle": "The VLDB Journal"}, {"Title": "Deep spatio-temporal graph convolutional network for traffic accident prediction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "135", "JournalTitle": "Neurocomputing"}, {"Title": "AutoML: A survey of the state-of-the-art", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106622", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "AutoML: A survey of the state-of-the-art", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106622", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Benchmark and Survey of Automated Machine Learning Frameworks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "409", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Automated evolutionary approach for the design of composite machine learning pipelines", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "109", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "AutoML to Date and Beyond: Challenges and Opportunities", "Authors": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>); M<PERSON>. <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Intelligent algorithms for incident detection and management in smart transportation systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "110", "Issue": "", "Page": "108839", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 114364589, "Title": "Melt pool width measurement in a multi-track, multi-layer laser powder bed fusion print using single-camera two-wavelength imaging pyrometry", "Abstract": "<p>In laser powder bed fusion (LPBF) additive manufacturing, melt pool characterization is one of the potential approaches toward rapid process qualification and efficient non-destructive evaluation of printed parts. Especially melt pool width measurement is crucial for understanding the print process regimes, estimating the solidified melt pool depth, and identifying any process anomalies, among other attributes of interest. While existing works focus on monitoring melt pools of single scan tracks or single layer prints, melt pool characterization for a multi-track multi-layer (MTML) LPBF print has not been extensively studied. In this work, we employ our lab-designed coaxial single-camera two-wavelength imaging pyrometry (STWIP) system to monitor in-situ melt pool properties during a MTML LPBF process. The STWIP-measured melt pool widths are validated using a serial sectioning machine (Robo-Met, UES). The in-situ STWIP and ex-situ Robo-Met measurement data are in close agreement with each other, having a mean absolute error and root mean squared error of 9.83 μm and 16.53 μm, respectively. Furthermore, we demonstrate the successful mapping of melt pool location and melt pool size on the printed MTML part. In sum, this work demonstrates the capability and the applicability of STWIP for accurate large-scale melt pool monitoring during LPBF processing of practical parts, thereby facilitating the development of LPBF process models and control strategies.</p>", "Keywords": "Additive manufacturing; Powder bed fusion; Melt pool; Imaging pyrometry; Multi-layer print", "DOI": "10.1007/s00170-024-13486-y", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Stevens Institute of Technology, Hoboken, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ZIP-AM Lab, Department of Mechanical Engineering and Materials Science, University of Pittsburgh, Pittsburgh, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering and Materials Science, University of Pittsburgh, Pittsburgh, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering and Materials Science, University of Pittsburgh, Pittsburgh, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ZIP-AM Lab, Department of Mechanical Engineering and Materials Science, University of Pittsburgh, Pittsburgh, USA; Corresponding author."}], "References": []}, {"ArticleId": 114364622, "Title": "Profiling and Classification of Users Through a Customer Feedback-based Machine Learning Model", "Abstract": "The systems aimed at predicting user preferences and providing recommendations are now commonly used in many systems such as online shops, social websites, and tourist guide websites. These systems typically rely on collecting user data and learning from it in order to improve their performance. In the context of urban mobility, user Profiling and Classification represent a crucial step in the continuous enhancement of services provided by our multi-agent system for multimodal transportation. In this paper, our goal is to implement and compare some machine learning (ML) algorithms. We will address the technical aspect of this implementation, demonstrating this model leverages customer feedback to develop a thorough understanding of individual preferences and travel behaviors. Through this approach, we can categorize users into distinct groups, enabling a finer personalization of route recommendations and transportation preferences. The ML model analyzes customer feedback, identifies recurring patterns, and continuously adjusts user profiles based on their evolution. This innovative approach aims to optimize the user experience by offering more precise and tailored recommendations, while fostering dynamic adaptation of the system to the changing needs of urban users. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "Machine learning; multi-agent systems; multimodal transportation; urban mobility", "DOI": "10.14569/IJACSA.2024.01503102", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>hane <PERSON>", "Affiliation": "Laboratory of Computer Science and Systems, HASSAN 2 University Faculty of Science, Ain Chock, Casablanca, Morocco"}, {"AuthorId": 2, "Name": "Abdeltif EL BYED", "Affiliation": "Laboratory of Computer Science and Systems, HASSAN 2 University Faculty of Science, Ain Chock, Casablanca, Morocco"}], "References": []}, {"ArticleId": 114364744, "Title": "Video anomaly detection based on a multi-layer reconstruction autoencoder with a variance attention strategy", "Abstract": "In this paper, we propose a comprehensive framework for detecting anomalies in videos based on autoencoder (AE). Traditional AE models solely rely on input and final reconstruction, potentially limiting their capacity to fully utilize the intermediate neural network layers. To mitigate this limitation, we introduce a novel approach that concurrently trains the model using corresponding intermediate layers from both the encoder and decoder. This allows the model to capture more intricate features, thus enhancing its anomaly detection capabilities. Furthermore, we introduce a motion loss function that exclusively relies on original video frames rather than optical flow, rendering it more efficient and capable of extracting motion features. Additionally, we have devised a variance attention strategy that is parameter-free and can automatically directs our model&#x27;s focus towards moving objects, further boosting the performance of our approach. Our experiments on three public datasets demonstrate the effectiveness and efficiency of our method in identifying abnormal events in complex scenarios. The code is publicly available at https://github.com/lsf2008/multRecLossAEPub .", "Keywords": "", "DOI": "10.1016/j.imavis.2024.105011", "PubYear": 2024, "Volume": "146", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Li", "Affiliation": "School of Information Science and Technology, Bohai University, Jinzhou 121000, Liaoning, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Bohai University, Jinzhou 121000, Liaoning, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Bohai University, Jinzhou 121000, Liaoning, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Bohai University, Jinzhou 121000, Liaoning, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hikvision Research Institute, Hikvision Digital Technology Co., Ltd, Hangzhou, 310051 Zhejiang, China"}], "References": [{"Title": "A comprehensive review on deep learning-based methods for video anomaly detection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104078", "JournalTitle": "Image and Vision Computing"}, {"Title": "NM-GAN: Noise-modulated generative adversarial network for video anomaly detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107969", "JournalTitle": "Pattern Recognition"}, {"Title": "Video anomaly detection with spatio-temporal dissociation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108213", "JournalTitle": "Pattern Recognition"}, {"Title": "Residual Spatiotemporal Autoencoder with Skip Connected and Memory Guided Network for Detecting Video Anomalies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "4677", "JournalTitle": "Neural Processing Letters"}, {"Title": "Video anomaly detection using deep residual-spatiotemporal translation network", "Authors": "Thitta<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "143", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Fast Anomaly Detection Based on 3D Integral Images", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1465", "JournalTitle": "Neural Processing Letters"}, {"Title": "Anomaly detection based on superpixels in videos", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "15", "Page": "12617", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 114364908, "Title": "A Hybrid Cybersecurity Algorithm for Digital Image Transmission over Advanced Communication Channel Models", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.046757", "PubYear": 2024, "Volume": "79", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Naglaa F. Soliman", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A novel blind color image watermarking based on Walsh Hadamard Transform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "9-10", "Page": "6845", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Secure color image cryptosystem based on chaotic logistic in the FrFT domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "Page": "2495", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient Apriori algorithm for frequent pattern in human intoxication data", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "19", "Issue": "1", "Page": "61", "JournalTitle": "Innovations in Systems and Software Engineering"}, {"Title": "Dual Image Cryptosystem Using Henon Map and Discrete Fourier Transform", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "36", "Issue": "3", "Page": "2933", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "An Apriori Algorithm-Based Association Rule Analysis to detect Human Suicidal Behaviour", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "219", "Issue": "", "Page": "1279", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 114364973, "Title": "From space to place: mapping poverty in Turkish regions with NASA’s global gridded relative deprivation index", "Abstract": "<p>This study examines the spatial distribution of poverty in Turkish states using zonal statistics techniques. The recently released Global Gridded Relative Deprivation Index (GRDIv1) dataset by NASA has been utilized. The GRDIv1 index contains six pivotal components: Child Dependency Ratio, Subnational Development Index, Infant Mortality Rate, Built-Up to Non-Built-Up Ratios, VIIRS nighttime lights, and VIIRS Nighttime Lights (VNL) Slope Component. All the components capture various aspects of regional poverty differences. The results show the eastern regions have significantly higher levels of deprivation than the western regions. This disparity is attributed to conflicts, unemployment, and illiteracy in the East, while the West benefits from higher development. The analysis of the ratio of Built-Up Areas to Non-Built-Up Areas reveals a complex distribution of urbanization and industrialization, with the western Marmara region emerging as a center of development and industrial activity. Moreover, the analysis of Nocturnal Illumination Patterns, based on VIIRS nighttime light data, further confirms the higher levels of development in the west and the deprivation in the east. This study objectively proves that the Eastern region of Turkiye contains areas with much higher deprivation than does central and western regions.</p>", "Keywords": "Spatial distribution; Geospatial analysis; Poverty; GRDIv1; Turkish states", "DOI": "10.1007/s41324-024-00579-9", "PubYear": 2024, "Volume": "32", "Issue": "5", "JournalId": 2564, "JournalTitle": "Spatial Information Research", "ISSN": "2366-3286", "EISSN": "2366-3294", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "Ibn <PERSON>, Istanbul, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ibn <PERSON> University, Istanbul, Turkey"}], "References": []}, {"ArticleId": 114364978, "Title": "An integrated attention-guided deep convolutional neural network for facial expression recognition in the wild", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19012-2", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Deep learning-based face analysis system for monitoring customer interest", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Serap Kazan", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "237", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Facial expression recognition with convolutional neural networks via a new face cropping and rotation strategy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "2", "Page": "391", "JournalTitle": "The Visual Computer"}, {"Title": "Using CNN for facial expression recognition: a study of the effects of kernel size and number of filters on accuracy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "2", "Page": "405", "JournalTitle": "The Visual Computer"}, {"Title": "Improved curriculum learning using SSM for facial expression recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "8", "Page": "1635", "JournalTitle": "The Visual Computer"}, {"Title": "Attention-based convolutional neural network for deep face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "9-10", "Page": "5595", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Deep convolution network based emotion analysis towards mental health care", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "388", "Issue": "", "Page": "212", "JournalTitle": "Neurocomputing"}, {"Title": "RETRACTED ARTICLE: Real-time facial expression recognition for affect identification using multi-dimensional SVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "6355", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "E-FCNN for tiny facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "1", "Page": "549", "JournalTitle": "Applied Intelligence"}, {"Title": "LBAN-IL: A novel method of high discriminative representation for facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "432", "Issue": "", "Page": "159", "JournalTitle": "Neurocomputing"}, {"Title": "EmNet: a deep integrated convolutional neural network for facial emotion recognition in the wild", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "8", "Page": "5543", "JournalTitle": "Applied Intelligence"}, {"Title": "EmotionNet Nano: An Efficient Deep Convolutional Neural Network Design for Real-Time Facial Expression Recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "", "Page": "105", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Dual integrated convolutional neural network for real-time facial expression recognition in the wild", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "1083", "JournalTitle": "The Visual Computer"}, {"Title": "Landmark guidance independent spatio-channel attention and complementary context information based facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON> Balasubramanian", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "58", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Residual multi-task learning for facial landmark localization and expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "107893", "JournalTitle": "Pattern Recognition"}, {"Title": "Efficient convolutional neural network with multi-kernel enhancement features for real-time facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "6", "Page": "2111", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Robustness comparison between the capsule network and the convolutional network for facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2269", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi-level spatial and semantic enhancement network for expression recognition", "Authors": "<PERSON><PERSON> Ma; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "12", "Page": "8565", "JournalTitle": "Applied Intelligence"}, {"Title": "FaceCaps for facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3-4", "Page": "e2021", "JournalTitle": "Computer Animation and Virtual Worlds"}, {"Title": "A new multi-feature fusion based convolutional neural network for facial expression recognition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "3", "Page": "2918", "JournalTitle": "Applied Intelligence"}, {"Title": "SG-DSN: A Semantic Graph-based Dual-Stream Network for facial expression recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "462", "Issue": "", "Page": "320", "JournalTitle": "Neurocomputing"}, {"Title": "Two-pathway attention network for real-time facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "4", "Page": "1173", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Convolutional relation network for facial expression recognition in the wild with few-shot learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "116046", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep learning inspired intelligent embedded system for haptic rendering of facial emotions to the blind", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "4595", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Co-attentive multi-task convolutional neural network for facial expression recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108401", "JournalTitle": "Pattern Recognition"}, {"Title": "CERN: Compact facial expression recognition net", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "9", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "CERN: Compact facial expression recognition net", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "9", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Adaptive weight based on overlapping blocks network for facial expression recognition", "Authors": "<PERSON><PERSON> Tong; <PERSON><PERSON> Sun; <PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "104399", "JournalTitle": "Image and Vision Computing"}, {"Title": "Effective attention feature reconstruction loss for facial expression recognition in the wild", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "12", "Page": "10175", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An attention-guided convolutional neural network for automated classification of brain tumor from MRI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "2541", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Patch attention convolutional vision transformer for facial expression recognition with occlusion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "781", "JournalTitle": "Information Sciences"}, {"Title": "MPCSAN: multi-head parallel channel-spatial attention network for facial expression recognition in the wild", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "9", "Page": "6529", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Enhanced discriminative global-local feature learning with priority for facial expression recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "630", "Issue": "", "Page": "370", "JournalTitle": "Information Sciences"}, {"Title": "Facial expression recognition based on regional adaptive correlation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "4", "Page": "445", "JournalTitle": "IET Computer Vision"}, {"Title": "CFNet: Facial expression recognition via constraint fusion under multi-task joint learning network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "110312", "JournalTitle": "Applied Soft Computing"}, {"Title": "A framework for facial expression recognition using deep self-attention network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "7", "Page": "9543", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 114365085, "Title": "An End-to-End Model of ArVi-MoCoGAN and C3D with Attention Unit for Arbitrary-view Dynamic Gesture Recognition", "Abstract": "Human gesture recognition is an attractive research area in computer vision with many applications such as humanmachine interaction, virtual reality, etc. Recent deep learning techniques have been efficiently applied for gesture recognition, but they require a large and diverse amount of training data. In fact, the available gesture datasets contain mostly static gestures and/or certain fixed viewpoints. Some contain dynamic gestures, but they are not diverse in poses and viewpoints. In this paper, we propose a novel end-to-end framework for dynamic gesture recognition from unknown viewpoints. It has two main components: (1) an efficient GAN-based architecture, named ArVi-MoCoGAN; (2) the gesture recognition component, which contains C3D backbones and an attention unit. ArVi-MoCoGAN aims at generating videos at multiple fixed viewpoints from a real dynamic gesture at an arbitrary viewpoint. It also returns the probability that a real arbitrary view gesture belongs to which of the fixed-viewpoint gestures. These outputs of ArViMoCoGAN will be processed in the next component to improve the arbitrary view recognition performance through multi-view synthetic gestures. The proposed system is extensively analyzed and evaluated on four standard dynamic gesture datasets. The experimental results of our proposed method are better than the current solutions, from 1% to 13.58% for arbitrary view gesture recognition and from 1.2% to 7.8% for single view gesture recognition. © (2024), (Science and Information Organization). All Rights Reserved.", "Keywords": "attention unit; Dynamic gesture recognition; generative adversarial network", "DOI": "10.14569/IJACSA.2024.01503122", "PubYear": 2024, "Volume": "15", "Issue": "3", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Control and Automation, Electric Power University, Ha Noi, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "MQ Information and Communication Technology Solutions JSC, Ha Noi, Viet Nam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Faculty of Information Security, Academy of People Security, Ha Noi, Viet Nam"}], "References": []}, {"ArticleId": 114365211, "Title": "Advancing Sentiment Analysis during the Era of Data-Driven Exploration via the Implementation of Machine Learning Principles", "Abstract": "<p xml:lang=\"en\">Information technology has seamlessly woven into the fabric of our daily existence, making it nearly inconceivable to envision life without the influence of social media platforms. Communication networks, encompassing mediums like television and radio broadcasts, have transcended their role as mere sources of entertainment, evolving into contemporary vehicles for disseminating significant information, viewpoints, and concepts among users. Certain subsets of this data hold pivotal importance, serving as valuable reservoirs for analysis and subsequent extraction of crucial insights, destined to inform future decision-making processes. Within the scope of this undertaking, we delve into the intricacies of sentiment analysis, leveraging the power of machine learning to prognosticate and dissect data derived from external origins. A prime focal point of this endeavor revolves around the implementation of the Naive Bayes technique, a supervised approach that imparts knowledge to the system, enabling it to forecast the emotional undercurrents of forthcoming input data. Empirical findings stemming from this venture substantiate the prowess of the Naive Bayes method, positioning it as a formidable and highly efficient tool in the arsenal of sentiment analysis methodologies. Its remarkable accuracy in discerning the positive and negative polarity of data reinforces its merit. Furthermore, this approach expedites the generation of high-caliber results within an abbreviated timeframe, setting it apart from alternative techniques and processes inherent in the realm of machine learning.</p>", "Keywords": "", "DOI": "10.17694/bajece.1340321", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 27857, "JournalTitle": "Balkan Journal of Electrical and Computer Engineering", "ISSN": "2147-284X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "GAZİANTEP ÜNİVERSİTESİ, FEN BİLİMLERİ ENSTİTÜSÜ, ELEKTRİK, ELEKTRONİK MÜHENDİSLİĞİ (DR)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "GAZIANTEP UNIVERSITY"}], "References": [{"Title": "Multi-Tier Sentiment Analysis of Social Media Text Using Supervised Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "74", "Issue": "3", "Page": "5527", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Machine learning and deep learning for sentiment analysis across languages: A survey", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "531", "Issue": "", "Page": "195", "JournalTitle": "Neurocomputing"}, {"Title": "A survey of sentiment analysis from film critics based on machine learning, lexicon and hybridization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "13", "Page": "9437", "JournalTitle": "Neural Computing and Applications"}]}]