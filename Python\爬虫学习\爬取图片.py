"""
python爬虫练习
url：http://pic.netbian.com/4kmeinv/
爬取上面链接的4k美女图片，并保存到本地
注意：该网址进入时会有真人验证
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time

# 配置浏览器驱动
options = webdriver.ChromeOptions()
options.add_argument('--headless')  # 无头模式（无界面）
options.add_argument('--disable-gpu')
options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

# 自动下载并配置Chrome驱动
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

try:
    driver.get("http://pic.netbian.com/4kmeinv/")
    time.sleep(5)  # 等待页面加载（可能需要手动完成验证）
    
    # 获取页面源码
    page_source = driver.page_source
    soup = BeautifulSoup(page_source, 'html.parser')
    
    # 解析图片链接（同上）
    img_tags = soup.find_all('img', {'class': 'img-responsive'})
    img_urls = [img['src'] for img in img_tags if img.get('src')]
    
    # 下载图片（需要单独处理）
    for idx, img_url in enumerate(img_urls):
        # 补全域名并下载
        if not img_url.startswith('http'):
            img_url = f'http://pic.netbian.com{img_url}'
        driver.get(img_url)
        with open(f'image_{idx}.jpg', 'wb') as f:
            f.write(driver.find_element('tag name', 'img').screenshot_as_png)
        print(f"下载完成：image_{idx}.jpg")
        time.sleep(1)

finally:
    driver.quit()