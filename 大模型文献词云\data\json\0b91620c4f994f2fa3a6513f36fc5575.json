[{"ArticleId": 115490534, "Title": "Meta-learning for heterogeneous treatment effect estimation with closed-form solvers", "Abstract": "<p>This article proposes a meta-learning method for estimating the conditional average treatment effect (CATE) from a few observational data. The proposed method learns how to estimate CATEs from multiple tasks and uses the knowledge for unseen tasks. In the proposed method, based on the meta-learner framework, we decompose the CATE estimation problem into sub-problems. For each sub-problem, we formulate our estimation models using neural networks with task-shared and task-specific parameters. With our formulation, we can obtain optimal task-specific parameters in a closed form that are differentiable with respect to task-shared parameters, making it possible to perform effective meta-learning. The task-shared parameters are trained such that the expected CATE estimation performance in few-shot settings is improved by minimizing the difference between a CATE estimated with a large amount of data and one estimated with just a few data. Our experimental results demonstrate that our method outperforms the existing meta-learning approaches and CATE estimation methods.</p>", "Keywords": "Meta-learning; Treatment effect estimation; Deep learning", "DOI": "10.1007/s10994-024-06546-7", "PubYear": 2024, "Volume": "113", "Issue": "9", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NTT Communication Science Laboratories, NTT Corporation, Kyoto, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NTT Communication Science Laboratories, NTT Corporation, Kyoto, Japan"}], "References": [{"Title": "A survey of deep meta-learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "4483", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Few-shot learning for spatial regression via neural embedding-based Gaussian processes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "111", "Issue": "4", "Page": "1239", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 115490617, "Title": "A compliance-based ranking of certificate authorities using probabilistic approaches", "Abstract": "<p>The security of the global Certification Authority (CA) system has recently been compromised as a result of attacks on the Public Key Infrastructure (PKI). Although the CA/Browser (CA/B) Forum publishes compliance requirements for CAs, there are no guarantees that even a commercially successful CA is complying with these recommendations. In this paper, we propose the first systematic CA ranking mechanism that ranks CAs in terms of their adherence to the CA/B Forum and X.509 certificate standards. Unfortunately, there is no consolidated and widely accepted parameter to rank the CAs so we have proposed formula-based rating models and introduced different ranking techniques like Direct, Bayesian, and MarkovChain Ranking. These rankings are applied to a comprehensive dataset of X.509 trust chains gathered during the time period of 2020 to 2023. Our proposed ranking scheme can serve as a criterion for both consumers and enterprises for selecting and prioritizing CAs based on performance as well as adherence to the certificate standards. </p>", "Keywords": "X.509 Certificates; CA/B Forum; Web PKI; Certificate authority; Ranking", "DOI": "10.1007/s10207-024-00867-3", "PubYear": 2024, "Volume": "23", "Issue": "4", "JournalId": 25892, "JournalTitle": "International Journal of Information Security", "ISSN": "1615-5262", "EISSN": "1615-5270", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Technology University of the Punjab, Punjab, Pakistan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Technology University of the Punjab, Punjab, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Qatar University, Doha, Qatar"}], "References": [{"Title": "Identifying vulnerabilities of SSL/TLS certificate verification in Android apps with static and dynamic analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "110609", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 115490767, "Title": "Time-resolved subcycle and intercycle interference influenced momentum shift in nonadiabatic tunnelling ionization", "Abstract": "", "Keywords": "", "DOI": "10.23967/j.rimni.2024.05.015", "PubYear": 2024, "Volume": "40", "Issue": "2", "JournalId": 21056, "JournalTitle": "Revista Internacional de Métodos Numéricos para Cálculo y Diseño en Ingeniería", "ISSN": "0213-1315", "EISSN": "1886-158X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115490801, "Title": "Multigranulation Variable-scale Fuzzy Neighborhood Measures and Corresponding Choquet-like Integrals for Feature Selection", "Abstract": "Choquet-like integrals, a nonlinear fuzzy aggregation function, are diffusely applied in several real problems. However, the corresponding fuzzy measures in them are provided by human intervention and not by data-driven methods. As an effective knowledge representation tool, rough set models in different approximation spaces are established for data processing. Particularly, the multigranulation fuzzy β -covering approximation space has been concerned with only a single scale β ∈ ( 0 , 1 ] . However, different granulation structures should have different scales. Therefore, establishing a multigranulation approximation space with a variable scale by inducing fuzzy measures through data-driven methods and using corresponding Choquet-like integrals in practical problems is a significant challenge. To address this challenge, the notion of multigranulation variable-scale fuzzy Θ-covering group approximation space is presented here, as well as multigranulation variable-scale fuzzy neighborhood measures in it. Furthermore, Choquet-like integrals with the presented measures are constructed to solve the issue of feature selection (i.e., attribute reduction) under the new approximation spaces. Firstly, the concept of multigranulation variable-scale fuzzy Θ-covering group approximation space is presented, where different fuzzy β -coverings have different scales β ∈ Θ . Moreover, multigranulation variable-scale fuzzy neighborhood measures in it, as fuzzy measures, are presented. Subsequently, Choquet-like integrals with multigranulation variable-scale fuzzy neighborhood measures are constructed. A novel attribute reduction method under Choquet-like integrals with multigranulation variable-scale fuzzy neighborhood measures is proposed for any decision information table. Finally, the presented method is selected for solving problems of classification and diagnosis of rolling bearing faults. Several public data sets are used to demonstrate the effectiveness and feasibility of the presented methods mentioned above.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120789", "PubYear": 2024, "Volume": "676", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Data Science, Shaanxi University of Science and Technology, Xi'an 710021, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Data Science, Shaanxi University of Science and Technology, Xi'an 710021, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Data Science, Shaanxi University of Science and Technology, Xi'an 710021, PR China;Corresponding author"}], "References": [{"Title": "A novel fuzzy rough set model with fuzzy neighborhood operators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "266", "JournalTitle": "Information Sciences"}, {"Title": "A new distance-based total uncertainty measure in Dempster-Shafer evidence theory", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "2", "Page": "1209", "JournalTitle": "Applied Intelligence"}, {"Title": "Constructing multi-layer classifier ensembles using the Choquet integral based on overlap and quasi-overlap functions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "500", "Issue": "", "Page": "413", "JournalTitle": "Neurocomputing"}, {"Title": "Novel fuzzy β -covering rough set models and their applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "286", "JournalTitle": "Information Sciences"}, {"Title": "Rough Pythagorean fuzzy approximations with neighborhood systems and information granulation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "119603", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Novel variable precision fuzzy rough sets and three-way decision model with three strategies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "629", "Issue": "", "Page": "222", "JournalTitle": "Information Sciences"}, {"Title": "Attribute reduction and information granulation in Pythagorean fuzzy formal contexts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "222", "Issue": "", "Page": "119794", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Self-triggered finite-time control for discrete-time Markov jump systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "634", "Issue": "", "Page": "101", "JournalTitle": "Information Sciences"}, {"Title": "Improved Dynamic Event-Triggered Security Control for T–S Fuzzy LPV-PDE Systems via Pointwise Measurements and Point Control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "8", "Page": "3177", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Online group streaming feature selection based on fuzzy neighborhood granular ball rough sets", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123778", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Variable scale fuzzy β -covering group approximation space and variable scale multi-granulation FCRSs with applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "164", "Issue": "", "Page": "111982", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 115490857, "Title": "Voronoi Tessellations based Simple Optimizer", "Abstract": "Population diversity holds significant importance in determining the success of any evolutionary algorithm. It helps the algorithm in efficiently exploring the search space and identifying the promising region(s) containing global optimal solution(s). However, during the optimization procedure the population may lose its diversity, causing premature convergence in the algorithm and resulting in approximations to the sub-optimal solution(s). This paper proposes a Voronoi Tessellations based Simple Optimizer (VTSO) algorithm that utilizes a niche concept of Voronoi Tessellations (VTs) from the field of computational geometry to ensure a well-distributed population throughout the optimization procedure. It proposed an elite sampling mechanism that utilizes Lévy flights to aggressively explore the search space for locating potential optimal region(s), and the Differential Evolution (DE) algorithm to exploit these regions in order to approximate the global optimal solution(s). In addition, a population diameter-based switch is devised which activates itself when the algorithm detects premature convergence in the algorithm. Experiments are conducted on CEC14 and CEC17 benchmark test suit, and the proposed algorithm is compared with the existing state-of-the-art evolutionary algorithms. The results are competitive to recommend the VTSO algorithm as a new efficient and accurate optimizer for handling complex optimization problems.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120795", "PubYear": 2024, "Volume": "676", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Computer Sciences, South Asian University, Rajpur road, New Delhi, 110068, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Computer Sciences, South Asian University, Rajpur road, New Delhi, 110068, Delhi, India;Corresponding author"}], "References": [{"Title": "A better balance in metaheuristic algorithms: Does it exist?", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100671", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Influence of initialization on the performance of metaheuristic optimizers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106193", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evolving Multimodal Robot Behavior via Many Stepping Stones with the Combinatorial Multiobjective Evolutionary Algorithm", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "131", "JournalTitle": "Evolutionary Computation"}, {"Title": "Multi-surrogate assisted multi-objective evolutionary algorithms for feature selection in regression and classification problems with time series data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "1064", "JournalTitle": "Information Sciences"}, {"Title": "Survey on Evolutionary Deep Learning: Principles, Algorithms, Applications, and Open Issues", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 115490925, "Title": "A Closer Look into IPFS: Accessibility, Content, and Performance", "Abstract": "<p>The InterPlanetary File System (IPFS) has recently gained considerable attention. While prior research has focused on understanding its performance characterization and application support, it remains unclear: (1) what kind of files/content are stored in IPFS, (2) who are providing these files, (3) are these files always accessible, and (4) what affects the file access performance.</p><p>To answer these questions, in this paper, we perform measurement and analysis on over 4 million files associated with CIDs (content IDs) that appeared in publicly available IPFS datasets. Our results reveal the following key findings: (1) Mixed file accessibility: while IPFS is not designed for a permanent storage, accessing a non-trivial portion of files, such as those of NFTs and video streams, often requires multiple retrieval attempts, potentially blocking NFT transactions and negatively affecting the user experience. (2) Dominance of NFT (non-fungible token) and video files: about 50% of stored files are NFT-related, followed by a large portion of video files, among which about half are pirated movies and adult content. (3) Centralization of content providers: a small number of peers (top-50), mostly cloud nodes hosted by tech companies, serve a large portion (95%) of files, deviating from IPFS's intended design goal. (4) High variation of downloading throughput and lookup time: large file retrievals experience lower average throughput due to more overhead for resolving file chunk CIDs, and looking up files hosted by non-cloud nodes takes longer. We hope that our findings can offer valuable insights for (1) IPFS application developers to take into consideration these characteristics when building applications on top of IPFS, and (2) IPFS system developers to improve IPFS and similar systems to be developed for Web3.</p>", "Keywords": "", "DOI": "10.1145/3656015", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> University, Fairfax, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> University, Fairfax, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON> University, Fairfax, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Virginia, Charlottesville, USA"}, {"AuthorId": 5, "Name": "Song<PERSON> Chen", "Affiliation": "<PERSON> University, Fairfax, USA"}], "References": []}, {"ArticleId": 115490962, "Title": "Spatial Datasets of 30-year (1991-2020) Average Monthly Total Precipitation and Minimum/Maximum Temperature for Canada and the United States", "Abstract": "<p>Thin plate smoothing spline models, covering Canada and the continental United States, were developed using ANUSPLIN for 30-year (1991-2020) monthly mean maximum and minimum temperature and precipitation. These models employed monthly weather station values from the North American dataset published by National Oceanic and Atmospheric Administration's (NOAA's) National Centers for Environmental Information (NCEI). Maximum temperature mean absolute errors (MAEs) ranged between 0.54 °C and 0.64 °C (approaching measurement error), while minimum temperature MAEs were slightly higher, varying from 0.87 °C to 1.0 °C. On average, thirty-year precipitation estimates were accurate to within approximately 10 % of total precipitation levels, ranging from 9.0 % in the summer to 12.2 % in the winter. Error rates were higher in Canada compared to estimates in the United States, consistent with a less dense station network in Canada relative to the United States. Precipitation estimates in Canada exhibited MAEs representing 14.7 % of mean total precipitation compared to 9.7 % in the United States. The datasets exhibited minimal bias overall; 0.004 °C for maximum temperature, 0.01 °C for minimum temperature, and 0.5 % for precipitation. Winter months showed a greater dry bias (0.8 % of total winter precipitation) compared to other seasons (-0.4 % of precipitation). These 30-year gridded datasets are available at ∼2 km resolution.</p><p>Crown Copyright © 2024 Published by Elsevier Inc.</p>", "Keywords": "Grids;Historical;Precipitation;Raster;Spatial datasets;Spline;Temperature", "DOI": "10.1016/j.dib.2024.110561", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Great Lakes Forestry Centre, Canadian Forest Service, Natural Resources Canada, P6A 2E5 1219 Queen Street East, Sault Ste. Marie, Ontario, Canada."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Great Lakes Forestry Centre, Canadian Forest Service, Natural Resources Canada, P6A 2E5 1219 Queen Street East, Sault Ste. Marie, Ontario, Canada."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Great Lakes Forestry Centre, Canadian Forest Service, Natural Resources Canada, P6A 2E5 1219 Queen Street East, Sault Ste. Marie, Ontario, Canada."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Great Lakes Forestry Centre, Canadian Forest Service, Natural Resources Canada, P6A 2E5 1219 Queen Street East, Sault Ste. Marie, Ontario, Canada."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Great Lakes Forestry Centre, Canadian Forest Service, Natural Resources Canada, P6A 2E5 1219 Queen Street East, Sault Ste. Marie, Ontario, Canada."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Fenner School of Environment and Society, Australian National University, Australia."}], "References": []}, {"ArticleId": *********, "Title": "Improved lion swarm optimization algorithm to solve the multi-objective rescheduling of hybrid flowshop with limited buffer", "Abstract": "As the realities of production and operation in green and intelligent workshops become more variable, the adverse risks arising from disruptions to modernized workshop energy consumption schedules and customer churn caused by dynamic events are increasing. In order to solve those problems, we take the intelligent hybrid flow shop as the research subject, use buffer capacity and automated guided vehicles (AGVs) transport devices as resource constraints, construct a multi-objective rescheduling model that considers both energy consumption and customer satisfaction. According to the model characteristics, an improved lion swarm optimization algorithm (ILSO) is designed to solve the above model. To improve the initial solution quality and global search capability of the algorithm, ILSO is improved by combining the reverse learning initialization strategy of Logistic chaotic mapping with the tabu search strategy. The results of experiments on the proposed algorithm with different sizes of arithmetic cases and real cases in the workshop indicate that ILSO can effectively solve the bi-objective rescheduling problem oriented to inserting orders, and the proposed model can provide green dynamic scheduling solutions for manufacturing enterprises to achieve the purpose of transformation to green intelligent manufacturing.", "Keywords": "Rescheduling; Energy consumption; Satisfaction; Lion swarm optimization algorithm; Limited buffer", "DOI": "10.1016/j.jksuci.2024.102077", "PubYear": 2024, "Volume": "36", "Issue": "5", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Tingyu Guan", "Affiliation": "School of Business Administration, Liaoning Technical University, Huludao 125105, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Liaoning Technical University, Huludao 125105, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Liaoning Technical University, Huludao 125105, China"}], "References": [{"Title": "Digital Twin Enhanced Dynamic Job-Shop Scheduling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "146", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Efficient multiobjective optimization for an AGV energy-efficient scheduling problem with release time", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108334", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Echo state network with logistic mapping and bias dropout for time series prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "489", "Issue": "", "Page": "196", "JournalTitle": "Neurocomputing"}, {"Title": "Dynamic shop-floor scheduling using real-time information: A case study from the thermoplastic industry", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "152", "Issue": "", "Page": "106134", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 115491123, "Title": "Machine learning algorithms for predicting the risk of chronic kidney disease in type 1 diabetes patients: a retrospective longitudinal study", "Abstract": "<p>Chronic kidney disease (CKD) is a significant concern for individuals with type 1 diabetes (T1D), impacting their quality of life and healthcare costs. Identifying T1D patients at greater risk of developing CKD is crucial for preventive measures. However, it is challenging due to the asymptomatic progression of CKD and limited nephrologist availability in many countries. This study explores machine learning algorithms to predict CKD risk in T1D patients using ten years of retrospective data from the Epidemiology of Diabetes Interventions and Complications clinical trial. Eleven machine learning algorithms were applied to twenty-two readily available features from T1D patients’ routine check-ups and self-assessments to develop 10-year CKD risk prediction models. In addition, we also proposed a heterogeneous ensemble model (STK) using a stacking generalization approach. The models’ performance was evaluated using different evaluation metrics and repeated stratified k-fold cross-validation. Several predictive models showed reliable performance in CKD risk prediction, with the proposed ensemble model being the best performing with an average accuracy of 0.97, specificity of 0.98, sensitivity/recall of 0.96, precision of 0.98, F1 score of 0.97, Kappa and MCC score of 0.94, AUROC of 0.99, and Precision-Recall curve of 0.99. The proposed machine learning approach could be applicable for CKD risk prediction in T1D patients to ensure the necessary precautions to overcome the risk.</p>", "Keywords": "Chronic kidney disease; Prognosis model; Risk prediction; Machine learning; Type 1 diabetes", "DOI": "10.1007/s00521-024-09959-6", "PubYear": 2024, "Volume": "36", "Issue": "26", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Systems Engineering, Universiti Kebangsaan Malaysia, UKM, Bangi, Malaysia; Department of Computer Science and Engineering, Bangladesh Army University of Science and Technology (BAUST), Saidpur, Nilphamari, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Independent University, Bangladesh, Bashundhara, Bangladesh; Department of Electrical, Electronic and Systems Engineering, Centre of Advanced Electronic and Communication Engineering, Universiti Kebangsaan Malaysia, UKM, Bangi, Malaysia; Malaysia-Japan International Institute of Technology, Universiti Teknologi Malaysia, Kuala Lumpur, Malaysia; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Independent University, Bangladesh, Bashundhara, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON>us <PERSON>am International Centre for Theoretical Physics (ICTP), Trieste, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "<PERSON>us <PERSON>am International Centre for Theoretical Physics (ICTP), Trieste, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Rajshahi, Rajshahi, Bangladesh"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence Resource, Molecular Imaging Branch, National Cancer Institute, Bethesda, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Independent University, Bangladesh, Bashundhara, Bangladesh"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Malaysia-Japan International Institute of Technology, Universiti Teknologi Malaysia, Kuala Lumpur, Malaysia"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Xiamen University Malaysia, Bandar Sunsuria, Sepang, Malaysia"}], "References": []}, {"ArticleId": 115491147, "Title": "Dried Fish dataset for Indian Seafood: A machine learning application", "Abstract": "<p>Dryingfish is a simple and economical way to process the catch. It creates a profitable business for coastal communities by providing a market for their catches, even during periods of abundance. It's a traditional method to preserve fish, especially valuable in regions where fresh fish isn't readily available or affordable throughout the year. This dataset provides a rich resource of <b>8290 images</b> specifically designed for machine learning applications. It focuses on the five most popular types of dried seafood in India: prawns (shrimp), small anchovies (tingali), golden anchovies (mandeli), mackerel (bangada), and Bombay duck (bombil). To ensure high-quality data for machine learning applications for Identification and classification of different dried fish varieties, the dataset features a diverse set of images in singles and in bulk for each category. The dataset utilizes standardized lighting, background, and object pose for optimal machine learning performance. This rich dataset empowers researchers and data scientists to leverage machine learning for various applications in the Indian dried fish industry.Overall, the Dried Fish Dataset for Indian Seafood aims to leverage machine learning to improve the standardization, quality control, safety, and efficiency of the Indian dried fish industry.</p><p>© 2024 The Authors.</p>", "Keywords": "Dried fish classification;Dried fish dataset;Dried fish detection;Machine learning", "DOI": "10.1016/j.dib.2024.110563", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> (Deemed to be University) College of Engineering, Pune, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> (Deemed to be University) College of Engineering, Pune, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering and Technology, Dr <PERSON><PERSON><PERSON><PERSON>, MIT World Peace University, Pune, India."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "MIT School of Computing, MIT Art Design and Technology University, Pune, India."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MIT School of Computing, MIT Art Design and Technology University, Pune, India."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Symbiosis Institute of Technology, Symbiosis International (Deemed University), Pune, India."}], "References": [{"Title": "Sugarcane leaf dataset: A dataset for disease detection and classification for machine learning applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "53", "Issue": "", "Page": "110268", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 115491361, "Title": "Observer-based stabilization of discrete time-delay systems", "Abstract": "The objective of this paper is to design an observer-based stabilizing controller for a discrete time-delay system with unmeasurable states. This study proposes a novel Lyapunov-K<PERSON>skii (LK) functional to establish delay-dependent stabilization of a discrete time-delay system and the observer-based design is exploited to deal with the unmeasurable states of the system. To establish less conservative stabilization conditions in terms of LMIs, the reciprocally convex inequality and summation inequality are exploited. The relevance of the proposed stabilizing controller is illustrated with numerical examples.", "Keywords": "Discrete time-delay systems; Lyapunov-Krasovskii functionals (LKF); Linear Matrix Inequality (LMI)", "DOI": "10.1016/j.ifacol.2024.05.008", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology, Calicut, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology, Calicut, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology, Calicut, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology, Calicut, India"}], "References": [{"Title": "Observer-based controller design for linear time-varying delay systems using a new Lyapunov-Krasovskii functional", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "1", "Page": "99", "JournalTitle": "International Journal of Automation and Control"}]}, {"ArticleId": 115491393, "Title": "Lyapunov Characterization of Fold Change Detection and Adaptation in Biomolecular Systems", "Abstract": "Biomolecular systems, whether natural or synthetic, exhibit diverse systems-level behaviors attributed to the inherent nonlinear dynamics of gene regulatory networks. This study investigates two closely intertwined dynamical phenomena: Fold Change Detection (FCD) and Adaptation. Despite their widespread occurrence in genetic networks, a notable deficiency exists in terms of a comprehensive system-theoretic analysis and design framework, particularly in the context of design. In response to this gap, we have developed a control theory-based framework for characterizing and designing both fold change detection and adaptation in biomolecular systems. Our approach reframes FCD and Adaptation as stabilization issues of an augmented dynamics comprising parametric state sensitivities. Leveraging <PERSON><PERSON><PERSON><PERSON> theory, we establish the stability of the sensitivity dynamics, providing a foundation for characterizing FCD and Adaptation. These findings contribute a systematic framework for the analysis and design of nonlinear dynamics in the domain of systems and synthetic biology.", "Keywords": "biomolecular systems; systems biology; fold change detection; adaptation; sensitivity dynamics", "DOI": "10.1016/j.ifacol.2024.05.012", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology Kanpur"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology Kanpur"}], "References": []}, {"ArticleId": 115491628, "Title": "An ontology‐based digital test and evaluation master plan (dTEMP) compliant with DoD policy", "Abstract": "Test and evaluation (TE) planning is a critical part of systems engineering. However, it has not received as much attention from digital engineering efforts as early‐stage design and analysis. Digital engineering has the potential to reduce the risk and effort associated with TE, while leveraging existing digital capabilities to add value. One aspect in particular that may benefit from such attention is the Department of Defense's Test and Evaluation Master Plan (TEMP). The purpose of the TEMP is to identify the key processes with respect to the TE of a product, and to specify the roles and responsibilities of key personnel and organizations. Concerns have been raised regarding the document‐based nature of the TEMP and the increased risk and reduced reward that this entails. In this paper, we investigate the potential benefits of digitalizing the TEMP and outline an incremental approach for achieving this. We also present a set of ontologies, collectively known as the Digital TEMP (dTEMP), and investigate potential benefits and limitations by applying the dTEMP to an example test program.", "Keywords": "", "DOI": "10.1002/sys.21769", "PubYear": 2024, "Volume": "27", "Issue": "6", "JournalId": 2331, "JournalTitle": "Systems Engineering", "ISSN": "1098-1241", "EISSN": "1520-6858", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Systems and Industrial Engineering The University of Arizona  Tucson USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Systems and Industrial Engineering The University of Arizona  Tucson USA"}], "References": [{"Title": "Present and future of semantic web technologies: a research statement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "5", "Page": "413", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "Evaluating Domain Ontologies", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Ontology-based test generation for automated and autonomous driving functions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "117", "Issue": "", "Page": "106200", "JournalTitle": "Information and Software Technology"}, {"Title": "The long and winding road: MBSE adoption for functional avionics of spacecraft", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "110453", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Driving digital engineering integration and interoperability through semantic integration of models with ontologies", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "4", "Page": "365", "JournalTitle": "Systems Engineering"}]}, {"ArticleId": 115491646, "Title": "FONIC: an energy-conscious fuzzy-based optimized nature-inspired clustering technique for IoT networks", "Abstract": "<p>The Internet of Things (IoT) has developed into a new area of study that promises to elevate human culture to a higher level of sophistication. The network is essential in IoT since it is responsible for relaying information from sensors to the sink. In the IoT, where many devices share finite resources, extending the lifespan of the network is a difficult challenge. The lifespan of a network can be prolonged by the use of clustering. However, initial network nodes’ energy might be quickly depleted by incorrectly selecting cluster heads (CHs). This research aims to provide a solution by suggesting a fuzzy-based optimized nature-inspired clustering technique (FONIC) to choose the best CH to sustain the network over time. When dealing with unreliable network conditions, the precise solution provided by fuzzy logic (FL) is invaluable. Therefore, in order to calculate a fitness value, FL is used on network metrics such as energy, distance, degree, and centrality. In the end, the right CH is chosen with the help of the Penguin Search Optimization Algorithm (PeSOA). Python is utilized to do extensive simulations that confirm the effectiveness of the suggested FONIC protocol. Other protocols, including FIGWO, HMGWO, LEACH-PRO, FGWSTERP, and SSMOECHS, are contrasted with the proposed FONIC protocol. Compared to other top-tier protocols, the suggested FONIC protocol was shown to perform better than any of them, improving the ratio of packet transmission by 10% and network lifespans by 10–15%.</p>", "Keywords": "Cluster head; Network lifetime; Fuzzy logic; Improve energy efficiency; PeSOA; IoT", "DOI": "10.1007/s11227-024-06229-z", "PubYear": 2024, "Volume": "80", "Issue": "13", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Networks, College of Information Technology, University of Babylon, Babylon, Iraq"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Cyber Security, College of Sciences, Al-Mustaqbal University, Babylon, Hillah, Iraq; Department of Computer Science, College of Science for Women, University of Babylon, Babylon, Iraq; Corresponding author."}], "References": [{"Title": "An efficient double adaptive random spare reinforced whale optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "113018", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An energy-efficient fuzzy-based scheme for unequal multihop clustering in wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "873", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Two-level energy-efficient data reduction strategies based on SAX-LZW and hierarchical clustering for minimizing the huge data conveyed on the internet of things networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "16", "Page": "17844", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An Energy-Efficient Protocol for Internet of Things Based Wireless Sensor Networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "75", "Issue": "2", "Page": "2397", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A comprehensive review of clustering approaches for energy efficiency in wireless sensor networks", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "72", "Issue": "2", "Page": "139", "JournalTitle": "International Journal of Computer Applications in Technology"}]}, {"ArticleId": 115491682, "Title": "Efficient magnetic shielding by designing layered composite with nanocrystalline and permalloy for weak magnetic measurement", "Abstract": "Magnetic field shields are important for ultrahigh-sensitivity sensors and electrical instruments. Traditional single soft magnetic material structures struggle to meet the increasing demands for shielding performance. Here, we develop a multi-layered interfacial composite magnetic shield that further amplifies the interaction by introducing an additional multi-layer nanocrystalline structure on the outer layer of the permalloy to shield low-frequency magnetic fields, including the geomagnetic field, effectively. A theoretical calculation method for the composite magnetic shielding performance that integrates the material properties and structural dimensions is proposed. Using the shielding factor and material thickness as the target, the structure of a three-layer magnetic shielding box is optimized to achieve the expected results. The experimental part was validated using a single-layer N-P composite magnetic shielding box of 400×300×300 mm in length, width, and height with a total thickness of 2.3 mm. The experimental results show that at static magnetic field and AC 1000 Hz, the SF (shielding factor) reaches 593.5 and 652.8, and the measured center remanence is 57.2nT and 52.1nT, respectively. It is hoped that this work can provide an innovative strategy for effective magnetic shielding design.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115538", "PubYear": 2024, "Volume": "375", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Instrumentation and Optoelectronic Engineering, Beihang University, Beijing 100191, China;Institute of Large-scale Scientific Facility and Centre for Zero Magnetic Field Science, Beihang University, Beijing, China;National Institute of Extremely-Weak Magnetic Field Infrastructure, Hangzhou, China;Corresponding author at: School of Instrumentation and Optoelectronic Engineering, Beihang University, Beijing 100191, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Instrumentation and Optoelectronic Engineering, Beihang University, Beijing 100191, China;Institute of Large-scale Scientific Facility and Centre for Zero Magnetic Field Science, Beihang University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Zhao", "Affiliation": "School of Instrumentation and Optoelectronic Engineering, Beihang University, Beijing 100191, China;Institute of Large-scale Scientific Facility and Centre for Zero Magnetic Field Science, Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Instrumentation and Optoelectronic Engineering, Beihang University, Beijing 100191, China;Institute of Large-scale Scientific Facility and Centre for Zero Magnetic Field Science, Beihang University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Instrumentation and Optoelectronic Engineering, Beihang University, Beijing 100191, China;Institute of Large-scale Scientific Facility and Centre for Zero Magnetic Field Science, Beihang University, Beijing, China"}], "References": [{"Title": "A nanocrystalline shield for high precision co-magnetometer operated in spin-exchange relaxation-free regime", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "339", "Issue": "", "Page": "113487", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "A novel low-noise Mu-metal magnetic shield with winding shape", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "346", "Issue": "", "Page": "113884", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "A low-noise multilayer mu-metal thin shell magnetic shield for ultra-highly sensitive atomic sensors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "352", "Issue": "", "Page": "114207", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Magnetic noise modeling and calculation of Fe-based nanocrystalline in the SERF magnetometer considering temperature effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "373", "Issue": "", "Page": "115431", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 115491736, "Title": "On Game Theory-based Self-triggered Consensus Tracking", "Abstract": "This paper presents a consensus tracking control scheme for multi-agent system in leader-follower configuration, communicating over a directed spanning tree rooted at the leader. The agents are assumed to have double-integrator dynamics with bounded control inputs and subjected to bounded external disturbances. The consensus problem of disturbance-affected multi-agent system is solved by considering a pursuit-evasion game within each directed branch of the communication tree. Unlike general pursuit-evasion games that rely on continuous state information, the pursuer employs a self-triggered pursuit strategy that updates the control law at specific sampling instants determined by the pursuer. Simulation results verify that, with the self-triggered control for the followers, consensus tracking of the multi-agent system to the leader agent is achieved in a finite time, with a finite number of information updates.", "Keywords": "Multi-agent system; Self-triggered control; Pursuit-Evasion games; Game theory", "DOI": "10.1016/j.ifacol.2024.05.056", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology Dharwad, Karnataka, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology Dharwad, Karnataka, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology Dharwad, Karnataka, India"}], "References": [{"Title": "Group consensus via pinning control for a class of heterogeneous multi-agent systems with input constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "542", "Issue": "", "Page": "247", "JournalTitle": "Information Sciences"}, {"Title": "Self-Triggered Finite Time Pursuit Strategy for a Two-Player Game", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "2759", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 115491746, "Title": "Addressing multi-attribute large-scale group decision making with overlapping subgroups: A bidirectional adjustment consensus reaching method for heterogeneous decision makers", "Abstract": "Due to the rapid development of internet technology, multi-attribute large-scale group decision making (MALSGDM) becomes a research hotspot of multi-attribute group decision making. An interesting phenomenon is that decision makers (DMs) may assume multiple roles across various interest groups, thereby naturally forming overlapping subgroups. Aiming at the MALSGDM problem with overlapping subgroups, this paper proposes an overlapping subgroup-based consensus reaching method considering heterogeneous DMs. Firstly, a novel overlapping clustering method is proposed to decompose the large-scale group into several overlapping subgroups characterized by low evaluation conflict and stable trust relationship, thereby identify ordinary and key DMs. Secondly, a two-criteria consensus reaching judgment method is devised as the termination condition for the consensus reaching process. Thirdly, a novel bidirectional adjustment method considering heterogeneous DMs is constructed, which focuses on key DMs belonging to multiple subgroups simultaneously. The adjusted evaluation values are obtained through linear uncertain minimum adjustment consensus models under two different cases. Finally, a numerical example is provided to demonstrate the feasibility of the proposed method, and the comparative and sensitivity analyses are carried out to verify the superiority of the proposed method.", "Keywords": "", "DOI": "10.1016/j.cie.2024.110260", "PubYear": 2024, "Volume": "193", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "Yanbing Ju", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "International College, China Agricultural University, Beijing 100091, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Economics, Faculty of Informatics and Management, University of Hradec Králové, Rokitanskeho 62, 50003, Hradec Kralove 3, Czech Republic"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Andalusian Research Institute in Data Science and Computational Intelligence, Department of Computer Science and AI, University of Granada, 18071 Granada, Spain"}], "References": [{"Title": "Measuring trust in social networks based on linear uncertainty theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "154", "JournalTitle": "Information Sciences"}, {"Title": "Social network community analysis based large-scale group decision making approach with incomplete fuzzy preference relations", "Authors": "<PERSON><PERSON> Chu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "98", "JournalTitle": "Information Fusion"}, {"Title": "A cyclic dynamic trust-based consensus model for large-scale group decision making with probabilistic linguistic information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106937", "JournalTitle": "Applied Soft Computing"}, {"Title": "An overlap graph model for large‐scale group decision making with social trust information considering the multiple roles of experts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "3", "Page": "e12659", "JournalTitle": "Expert Systems"}, {"Title": "Linear uncertain extensions of the minimum cost consensus model based on uncertain distance and consensus utility", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "12", "JournalTitle": "Information Fusion"}, {"Title": "A cohesion-driven consensus reaching process for large scale group decision making under a hesitant fuzzy linguistic term sets environment", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "107158", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A multi-stage hybrid consensus reaching model for multi-attribute large group decision-making: Integrating cardinal consensus and ordinal consensus", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107443", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A non-threshold consensus model based on the minimum cost and maximum consensus-increasing for multi-attribute large group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "90", "JournalTitle": "Information Fusion"}, {"Title": "Democratic consensus reaching process for multi-person multi-criteria large scale decision making considering participants’ individual attributes and concerns", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "220", "JournalTitle": "Information Fusion"}, {"Title": "On consensus reaching process based on social network analysis in uncertain linguistic group decision making: Exploring limited trust propagation and preference modification attitudes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "180", "JournalTitle": "Information Fusion"}, {"Title": "Trust propagation and trust network evaluation in social networks based on uncertainty theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "234", "Issue": "", "Page": "107610", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A dynamic hybrid trust network-based dual-path feedback consensus model for multi-attribute group decision-making in intuitionistic fuzzy environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "266", "JournalTitle": "Information Fusion"}, {"Title": "Social network utility consensus model with empathic and fuzzy interactions", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "175", "Issue": "", "Page": "108904", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Managing multi-granular probabilistic linguistic information in large-scale group decision making: A personalized individual semantics-based consensus model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "230", "Issue": "", "Page": "120645", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A robust ordinal regression feedback consensus model with dynamic trust propagation in social network group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101952", "JournalTitle": "Information Fusion"}, {"Title": "Consensus reaching process for group decision-making based on trust network and ordinal consensus measure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "101", "Issue": "", "Page": "101969", "JournalTitle": "Information Fusion"}, {"Title": "A minimum cost-maximum consensus jointly driven feedback mechanism under harmonious structure in social network group decision making", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122358", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A large-scale group decision-making method based on improved affinity propagation algorithm and adjustable minimum-cost consensus model in social networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "187", "Issue": "", "Page": "109819", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A personalized bidirectional feedback mechanism by combining cooperation and trust to improve group consensus in social network", "Authors": "<PERSON><PERSON> Jin; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "188", "Issue": "", "Page": "109888", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A maximum satisfaction consensus-based large-scale group decision-making in social network considering limited compromise behavior", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "670", "Issue": "", "Page": "120606", "JournalTitle": "Information Sciences"}, {"Title": "Information consistent degree‐based clustering method for large‐scale group decision‐making with linear uncertainty distributions information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "6", "Page": "3394", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 115491831, "Title": "An Android Application For Attendance Using Geofencing", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2024.13585", "PubYear": 2024, "Volume": "13", "Issue": "5", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> ringe", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Prof.<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115491911, "Title": "Modelling and Simulation of Interval Type-2 Fuzzy PID Controllers using Mamdani Minimum Inference", "Abstract": "This paper presents two different mathematical models (also called analytical structures) of the simplest Mamdani type Interval Type-2 (IT2) Fuzzy Proportional Integral Derivative (FPID) controller by considering Average and Nie-Tan (NT) defuzzification methods. IT2 Fuzzy Sets (FSs) with triangular Membership Functions (MFs) on both input and output variables, Mamdani Minimum (MM) inference, and One-Dimensional (1D) input space are used to obtain the analytical structures of the controller. The models of the proposed controller are thoroughly analysed for their properties. Validity and applicability of the proposed controller models are demonstrated through simulation study", "Keywords": "IT2 fuzzy control; Mathematical modelling; Mamdani minimum inference; Average defuzzification; Nie-<PERSON> defuzzification", "DOI": "10.1016/j.ifacol.2024.05.049", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology Kharagpur, Kharagpur 721302, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology Kharagpur, Kharagpur 721302, India"}], "References": [{"Title": "Development, experimental validation, and comparison of interval type-2 Mamdani fuzzy PID controllers with different footprints of uncertainty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "601", "Issue": "", "Page": "374", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 115491920, "Title": "Sequential metamodel‐based approaches to level‐set estimation under heteroscedasticity", "Abstract": "This paper proposes two sequential metamodel‐based methods for level‐set estimation (LSE) that leverage the uniform bound built on stochastic kriging: predictive variance reduction (PVR) and expected classification improvement (ECI). We show that PVR and ECI possess desirable theoretical performance guarantees and provide closed‐form expressions for their respective sequential sampling criteria to seek the next design point for performing simulation runs, allowing computationally efficient one‐iteration look‐ahead updates. To enhance understanding, we reveal the connection between PVR and ECI's sequential sampling criteria. Additionally, we propose integrating a budget allocation feature with PVR and ECI, which improves computational efficiency and potentially enhances robustness to the impacts of heteroscedasticity. Numerical studies demonstrate the superior performance of the proposed methods compared to state‐of‐the‐art benchmarking approaches when given a fixed simulation budget, highlighting their effectiveness in addressing LSE problems.", "Keywords": "heteroscedastic Gaussian process metamodeling;level-set estimation;sequential sampling;uniform bounds", "DOI": "10.1002/sam.11697", "PubYear": 2024, "Volume": "17", "Issue": "3", "JournalId": 5556, "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal", "ISSN": "1932-1864", "EISSN": "1932-1872", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Grado Department of Industrial and Systems Engineering Virginia Tech  Blacksburg Virginia USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Grado Department of Industrial and Systems Engineering Virginia Tech  Blacksburg Virginia USA"}], "References": [{"Title": "Active Learning for Level Set Estimation Under Input Uncertainty and Its Extensions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "12", "Page": "2436", "JournalTitle": "Neural Computation"}, {"Title": "Dynamic Sampling Allocation Under Finite Simulation Budget for Feasibility Determination", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Leyuan Shi", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "557", "JournalTitle": "INFORMS Journal on Computing"}, {"Title": "Statistical Tests for Cross-Validation of Kriging Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "607", "JournalTitle": "INFORMS Journal on Computing"}, {"Title": "Adaptive batching for Gaussian process surrogates with application in noisy level set estimation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "2", "Page": "225", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}]}, {"ArticleId": 115491934, "Title": "Analytical Structures of Some Simplest Fuzzy PD Controllers using Bounded Sum Aggregation", "Abstract": "Bounded Sum (BS) was hardly used as an aggregation operator in the modeling of fuzzy controllers. In this work, the utilization of the Bounded Sum (BS) as an aggregation operator has been explored in the modeling of the simplest fuzzy PD controller. Mathematical models of two simplest fuzzy PD controllers utilizing Mamdani Minimum (MM) inference and Center of Area (CoA) defuzzification have been derived in this paper. In addition to modeling, the properties of newly developed controllers are examined. A simulation study is carried out on a single link manipulator system to show the effectiveness of the proposed controllers over the linear PD controller. An improvement in numerical accuracy of results is clearly shown. These newly designed fuzzy PD controllers have the potential to be beneficial for a wide range of control applications where most traditional control algorithms might not yield satisfactory results.", "Keywords": "Modeling; Simplest fuzzy PD controller; Center of Area defuzzification; Bounded Sum aggregation; Mamdani Minimum t-norm", "DOI": "10.1016/j.ifacol.2024.05.047", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology, Kharagpur, Kharagpur 721302, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology, Kharagpur, Kharagpur 721302, India"}], "References": [{"Title": "General structure of Interval Type-2 fuzzy PI/PD controller of Takagi–Sugeno type", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103273", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Modeling, analysis and real-time implementation of five new simplest fuzzy nonlinear PI/PD controllers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "11", "Page": "7435", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 115491947, "Title": "Online industrial fault prognosis in dynamic environments via task-free continual learning", "Abstract": "Online industrial fault prognosis based on real-time condition monitoring plays a significant role in the predictive maintenance of the interested system in service. Despite the recent advances in deep learning-based fault prognosis, most existing works focus on offline tasks using static deep models, which cannot flexibly adapt their learning behavior to the real-time health conditions of the interested machines. To meet more realistic scenarios, it is essential but challenging to develop an online prognostic method that is capable of incrementally learning and flexibly adapting to different tasks in dynamic environments. Meanwhile, continual learning as a new learning paradigm aims at learning adaptively like humans. This paper proposes a novel online fault prognosis paradigm via task-free continual learning towards more practical industrial scenarios. Correspondingly, an intelligent fault prognosis method based on continual neural Dirichlet process mixture is developed for this problem. It dynamically allocates new resources to learn new tasks by integrating the mixture of experts with the Bayesian nonparametric framework, wherein each expert with two components is designed to perform remaining useful life prediction and task identification respectively. The comprehensive experimental results on two turbofan engine datasets validate the proposed method, which offers a promising solution for online fault prognosis under more open settings.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127930", "PubYear": 2024, "Volume": "598", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Liu", "Affiliation": "State CIMS Engineering Research Center, Department of Automation, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State CIMS Engineering Research Center, Department of Automation, Tsinghua University, Beijing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State CIMS Engineering Research Center, Department of Automation, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State CIMS Engineering Research Center, Department of Automation, Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State CIMS Engineering Research Center, Department of Automation, Tsinghua University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State CIMS Engineering Research Center, Department of Automation, Tsinghua University, Beijing, China"}], "References": [{"Title": "Potential, challenges and future directions for deep learning in prognostics and health management applications", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103678", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Aircraft Engine Run-to-Failure Dataset under Real Flight Conditions for Prognostics and Diagnostics", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "5", "JournalTitle": "Data"}]}, {"ArticleId": 115491997, "Title": "Limit cycle based identification of two-input two-output systems with time delay", "Abstract": "This paper presents an identification algorithm based on relay feedback approach for modelling and identification of a class of multivariable systems with time delay. Owing to nonlinear characteristics exhibited by relay, an unknown system under the asymmetrical relay feedback control yields the sustained oscillatory responses also known as limit cycle oscillations around the reference input. From the obtained system responses, a set of analytical expressions for frequency domain based parameter estimation of unknown system model parameters is proposed. To show the robustness of the proposed identification method when subjected to adverse effects of measurement noise due to presence of faulty sensors at the system output, a noise-free limit cycle measurements are recovered from a Fourier series based curve fitting method and further substituted in the derived set of analytical expressions for modelling and identification of two-input two-output systems in terms of second order plus time delay dynamics. Typical examples from the literature are included to validate the effectiveness and merit of the proposed identification algorithm.", "Keywords": "Identification; Sensor Noise; Time Delay; Two-Input Two-Output Systems", "DOI": "10.1016/j.ifacol.2024.05.072", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Institute of Petroleum Technology, Jais, Amethi, Amethi, 229304, Uttar Pradesh, India"}], "References": []}, {"ArticleId": 115492019, "Title": "METHOD FOR SEARCHING FOR <PERSON>P<PERSON><PERSON><PERSON> PARAMETER VALUES OF THE ENTITY RESOLUTION ALGORITHM FOR CONCRETE HISTORICAL DATA", "Abstract": "", "Keywords": "", "DOI": "10.14357/08696527240209", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 41662, "JournalTitle": "Systems and Means of Informatics", "ISSN": "0869-6527", "EISSN": "2311-0325", "Authors": [], "References": []}, {"ArticleId": 115492086, "Title": "Conglomerate Multi-fidelity Gaussian Process Modeling, with Application to Heavy-Ion Collisions", "Abstract": "", "Keywords": "Bayesian nonparametrics; computer experiments; multi-fidelity modeling; surrogate modeling; quark-gluon plasma; 62G08; 60G15", "DOI": "10.1137/22M1525004", "PubYear": 2024, "Volume": "12", "Issue": "2", "JournalId": 11089, "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification", "ISSN": "", "EISSN": "2166-2525", "Authors": [{"AuthorId": 1, "Name": "Yi Ji", "Affiliation": "Department of Statistical Science, Duke University, Durham, NC 27708-0251 USA.;Joint first authors."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> School of Industrial & Systems Engineering, Georgia Institute of Technology, Atlanta, GA 30308 USA.;Joint first authors."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Physics, Duke University, Durham, NC 27708 USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Duke University, Durham, NC 27708 USA.;Department of Physics and Astronomy & Department of Mathematics, Vanderbilt University, Nashville, TN 37240 USA."}, {"AuthorId": 5, "Name": "Steffen A. Bass", "Affiliation": "Department of Physics, Duke University, Durham, NC 27708 USA."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> School of Industrial & Systems Engineering, Georgia Institute of Technology, Atlanta, GA 30308 USA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> School of Industrial & Systems Engineering, Georgia Institute of Technology, Atlanta, GA 30308 USA."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Corresponding author. Department of Statistical Science, Duke University, Durham, NC 27708-0251 USA."}], "References": []}, {"ArticleId": 115492108, "Title": "A novel penalty function-based interval constrained multi-objective optimization algorithm for uncertain problems", "Abstract": "To directly address problems involving uncertain objectives and constraints, a novel penalty function-based interval constrained multi-objective optimization algorithm (PF-ICMOA) was developed. It comprises three main components: an uncertainty propagation-based interval constraint solver, an interval constraint violation function, and a two-stage penalty function. The uncertainty violation of interval constraints can be accurately evaluated by the proposed advanced multi-objective optimization algorithm using a segmentation function without requiring multiple interval analyses. Additionally, by applying penalties to infeasible solutions in the two-stage process, partially feasible and superior infeasible solutions are retained in the early stages of evolution, while superior feasible solutions are favored in the later stages. A novel benchmark was designed and the effect of the imprecision factor on the Pareto front was examined in details. Comparisons with seven deterministic constraint handling techniques demonstrated the effectiveness of the proposed method for solving interval constrained multi-objective optimization problems, demonstrating that PF-ICMOA is a highly competitive method for balancing the feasibility, convergence, and diversity performance characteristics.", "Keywords": "", "DOI": "10.1016/j.swevo.2024.101584", "PubYear": 2024, "Volume": "88", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, China University of Geosciences, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Astronautics, Beihang University, Beijing 100191, China;Corresponding author"}, {"AuthorId": 3, "Name": "Guangming Dai", "Affiliation": "School of Computer, China University of Geosciences, Wuhan 430074, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, China University of Geosciences, Wuhan 430074, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "China Astronautics Standards Institute, Beijing 100081, China"}], "References": [{"Title": "A Benchmark-Suite of real-World constrained multi-objective optimization problems and some baseline results", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "100961", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An archive-based two-stage evolutionary algorithm for constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101161", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A dual-population based bidirectional coevolution algorithm for constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119258", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 115492153, "Title": "Optimal consistency adjustment strategy and benevolent multiplicative data envelopment analysis for group decision-making with interval-probabilistic linguistic preference relations", "Abstract": "Due to the complexity of the actual group decision-making environment, the probabilities with real values provided by multiplicative probabilistic linguistic term sets may not accurately reflect the decision maker's perspective on practical issues. To solve this issue, we first propose the multiplicative interval-probabilistic linguistic term set and multiplicative interval-probabilistic linguistic preference relation. It is widely acknowledged that group decision-making results derived from preference relations lacking an acceptable level of consistency are often considered unreasonable. Therefore, an optimal consistency adjustment strategy based on the prioritization algorithm is proposed to obtain the acceptable consistent multiplicative interval-probabilistic linguistic preference relations. Then, to derive the objective weights of decision makers, we design a nonlinear programming model based on the novel trust relationship. Furthermore, considering the benevolent mentality of decision makers, the benevolent multiplicative data envelopment analysis cross-efficiency model, which aims to minimize the sum of deviations between other decision-making units and the ideal point, is constructed to obtain the ranking of all alternatives. Finally, we give a case study on the branch location selection of a new energy vehicle company to demonstrate the validity of our suggested method. The merits of our proposed group decision-making method are validated through comparative analysis and discussion.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108629", "PubYear": 2024, "Volume": "134", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Anhui University, Hefei, Anhui, 230601, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Anhui University, Hefei, Anhui, 230601, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Anhui University, Hefei, Anhui, 230601, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Big Data and Statistics, Anhui University, Hefei, Anhui, 230601, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Anhui University, Hefei, Anhui, 230601, China"}], "References": [{"Title": "A comprehensive model for fuzzy multi-objective portfolio selection based on DEA cross-efficiency model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "2515", "JournalTitle": "Soft Computing"}, {"Title": "Multiplicative data envelopment analysis cross-efficiency and stochastic weight space acceptability analysis for group decision making with interval multiplicative preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "319", "JournalTitle": "Information Sciences"}, {"Title": "Distribution linguistic preference relations with incomplete symbolic proportions for group decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106005", "JournalTitle": "Applied Soft Computing"}, {"Title": "Decision making with probabilistic hesitant fuzzy information based on multiplicative consistency", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "8", "Page": "1233", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multiplicative Consistency Adjustment Model and Data Envelopment Analysis-Driven Decision-Making Process with Probabilistic Hesitant Fuzzy Preference Relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "7", "Page": "2319", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A nondominated selection procedure with partially consistent non-reciprocal probabilistic linguistic preference relations and its application in social donation channel selection under the COVID-19 outbreaks", "Authors": "<PERSON>sheng Jiang; <PERSON><PERSON>", "PubYear": 2021, "Volume": "564", "Issue": "", "Page": "416", "JournalTitle": "Information Sciences"}, {"Title": "Developing a new integrated artificial immune system and fuzzy non-discretionary DEA approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "13", "Page": "8109", "JournalTitle": "Soft Computing"}, {"Title": "Consistency and consensus reaching process for group decision making based on complete interval distributed preference relations under social network analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "88", "Issue": "", "Page": "126", "JournalTitle": "Information Fusion"}, {"Title": "A bargaining game based feedback mechanism to support consensus in dynamic social network group decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "93", "Issue": "", "Page": "363", "JournalTitle": "Information Fusion"}, {"Title": "On the environmental performance analysis: A combined fuzzy data envelopment analysis and artificial intelligence algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "224", "Issue": "", "Page": "119953", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Efficiency evaluation of two-stage parallel-series structures with fixed-sum outputs: An approach based on SMAA and DEA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "227", "Issue": "", "Page": "120264", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel Aczel-Alsina triangular norm-based group decision-making approach under dual hesitant q-rung orthopair fuzzy context for parcel lockers’ location selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "106846", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Group decision making based on relative projection between fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "184", "Issue": "", "Page": "109573", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Global correlation coordination model for ranking decision-making units based on cross-efficiency game", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "185", "Issue": "", "Page": "109649", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Dual consistency-driven group decision making method based on fuzzy preference relation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122228", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel parsimonious spherical fuzzy analytic hierarchy process for sustainable urban transport solutions", "Authors": "Sarbast Moslem", "PubYear": 2024, "Volume": "128", "Issue": "", "Page": "107447", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 115492155, "Title": "State estimation of Two-Phase Reactor Condenser System with Recycle using multi-rate Unscented Kalman Filter", "Abstract": "This paper delves into the pivotal role played by state estimation in advancing control strategies tailored for nonlinear models. While existing methods primarily focus on systems governed by Ordinary Differential Equations, this study assesses the effectiveness of the multi-rate Differential Algebraic Equation-based Unscented Kalman Filter for a Two-Phase Reactor Condenser System with Recycle. Differential algebraic equations incorporate a fusion of differential and algebraic equations, making them well-suited for a broader range of systems characterized by constraints or inter-variable dependencies. The performance of the multi-rate differential algebraic equation-based unscented Kalman filter is examined under various scenarios involving regularly sampled multi-rate data, encompassing measurements taken at varying intervals. The Mean Squared Error metric is employed to gauge the efficacy of state estimation using the multi-rate differential algebraic equation-based unscented Kalman filter on the two-phase reactor condenser system.", "Keywords": "State Estimation; Differential Algebraic Systems; Multi-rate Systems; Unscented Kalman Filter", "DOI": "10.1016/j.ifacol.2024.05.070", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Chemical Engineering Department, Dharmsinh Desai University, Nadiad, Gujarat, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chemical Engineering Department, Dharmsinh Desai University, Nadiad, Gujarat, India"}], "References": []}, {"ArticleId": 115492244, "Title": "Distributed Denial of Service Attack Detection Using Machine Learning Classifiers", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2024.10064418", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115492279, "Title": "INTELLIGENT DECISION SUPPORT SYSTEMS IN MEDICINE: CONCEPT, PROBLEMS, AND APPROACHES TO THE DEVELOPMENT", "Abstract": "", "Keywords": "", "DOI": "10.14357/08696527240208", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 41662, "JournalTitle": "Systems and Means of Informatics", "ISSN": "0869-6527", "EISSN": "2311-0325", "Authors": [], "References": []}, {"ArticleId": 115492304, "Title": "Real-time Validation of Learning-Based Control Schemes for Smart Deep Brain Stimulator using Controller Hardware-in-the-Loop", "Abstract": "Precision medicine and personalized treatment approaches are imperative for effectively managing neurological disorders such as Epilepsy, Parkinson's disease, and Mental Depression. Deep brain stimulation is one of the important treatment techniques to suppress epilepsy for drug-resistant epilepsy. Advances in computational modeling and control schemes have received more attention for the development of patient-specific treatment of these diseases. For the effective treatment of these diseases, the design of smart deep brain stimulators (SDBS) with patient-specific models based on individual pathological conditions becomes essential. This paper focuses on developing learning-based control schemes for SDBS to suppress epilepsy using the Hodgkin–Huxley (HH) conductance-based single-neuron model of the brain and real-time validation of the control algorithms using Controller Hardware-in-the-Loop (CHIL). Firstly, patient-specific linear models are developed using a single-neuron cellular model. Secondly Reinforcement Learning (RL) based control schemes namely RL, RL-based Proportional Integral (RL-PI) controller, and RL-based Model Predictive Controller (RL-MPC) are developed for SDBS using Deep Deterministic Policy Gradient (DDPG) algorithm for epilepsy suppression. Finally, the developed control schemes are validated using CHIL with OP4200 real-time simulator and Arduino Mega 2560. The results show that RL-MPC outperforms other controllers and suppresses epilepsy effectively by providing optimal stimulation based on individualized pathological conditions.", "Keywords": "Patient-Specific; SDBS; HH; CHIL; DDPG; RL; RL-PI; RL-MPC", "DOI": "10.1016/j.ifacol.2024.05.050", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": "Research Scholar, Department of Instrumentation Engineering, Madras Institute of Technology, Anna University, Chennai, 600044"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Professor, Department of Instrumentation Engineering, Madras Institute of Technology, Anna University, Chennai, 600044"}], "References": [{"Title": "Reinforcement learning based adaptive PID controller design for control of linear/nonlinear unstable processes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "109450", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 115492404, "Title": "Constrained multi-objective optimization problems: Methodologies, algorithms and applications", "Abstract": "Constrained multi-objective optimization problems (CMOPs) are widespread in practical applications such as engineering design, resource allocation, and scheduling optimization. It is high challenging for CMOPs to balance the convergence and diversity due to conflicting objectives and complex constraints. Researchers have developed a variety of constrained multi-objective optimization algorithms (CMOAs) to find a set of optimal solutions, including evolutionary algorithms and machine learning-based methods. These algorithms exhibit distinct advantages in solving different categories of CMOPs. Recently, constrained multi-objective evolutionary algorithms (CMOEAs) have emerged as a popular approach, with several literature reviews available. However, there is a lack of comprehensive-view survey on the methods of CMOAs, limiting researchers to track the cutting-edge investigations in this research direction. Therefore, this paper reviews the latest algorithms for handling CMOPs. A new classification method is proposed to divide literature, containing classical mathematical methods, evolutionary algorithms and machine learning methods. Subsequently, it reviews the modeling and algorithms of CMOPs in the context of practical applications. Lastly, the paper gives potential research directions with respect to CMOPs. This paper is able to provide guidance and inspiration for scholars studying CMOPs.", "Keywords": "Constrained multi-objective optimization problems; Evolutionary algorithms; Machine learning; Applications", "DOI": "10.1016/j.knosys.2024.111998", "PubYear": 2024, "Volume": "299", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Computer Science, Fernuniversität in Hagen, Hagen, 58097, Germany;School of Systems Science, Beijing Jiaotong University, No. 3, Shangyuan Village, Beijing, 100028, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science, Qingdao University of Science and Technology, No. 99, Songling Road, Qingdao, 266061, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science, Qingdao University of Science and Technology, No. 99, Songling Road, Qingdao, 266061, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science, Qingdao University of Science and Technology, No. 99, Songling Road, Qingdao, 266061, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Minnan Normal University, China;Corresponding authors"}], "References": [{"Title": "Difficulty Adjustable and Scalable Constrained Multiobjective Test Problem Toolkit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "3", "Page": "339", "JournalTitle": "Evolutionary Computation"}, {"Title": "Push and pull search embedded in an M2M framework for solving constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100651", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A test-suite of non-convex constrained optimization problems from the real-world and some baseline results", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "100693", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Purpose-directed two-phase multiobjective differential evolution for constrained multiobjective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100799", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An efficient surrogate-assisted hybrid optimization algorithm for expensive optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "561", "Issue": "", "Page": "304", "JournalTitle": "Information Sciences"}, {"Title": "A multi-objective evolutionary algorithm for steady-state constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107042", "JournalTitle": "Applied Soft Computing"}, {"Title": "A multi-stage evolutionary algorithm for multi-objective optimization with complex constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "68", "JournalTitle": "Information Sciences"}, {"Title": "A surrogate-assisted multi-objective particle swarm optimization of expensive constrained combinatorial optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "223", "Issue": "", "Page": "107049", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A simple two-stage evolutionary algorithm for constrained multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107263", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A partition-based constrained multi-objective evolutionary algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "100940", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Two-type weight adjustments in MOEA/D for highly constrained many-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "592", "JournalTitle": "Information Sciences"}, {"Title": "A dual-population algorithm based on alternative evolution and degeneration for solving constrained multi-objective optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "89", "JournalTitle": "Information Sciences"}, {"Title": "A multi-objective differential evolution algorithm based on domination and constraint-handling switching", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "796", "JournalTitle": "Information Sciences"}, {"Title": "A Benchmark-Suite of real-World constrained multi-objective optimization problems and some baseline results", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "100961", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A novel two-archive evolutionary algorithm for constrained multi-objective optimization with small feasible regions", "Authors": "Mingming Xia; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "237", "Issue": "", "Page": "107693", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hybrid driven strategy for constrained evolutionary multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "585", "Issue": "", "Page": "344", "JournalTitle": "Information Sciences"}, {"Title": "A constrained multi-objective evolutionary algorithm using valuable infeasible solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "68", "Issue": "", "Page": "101020", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Constrained multi-objective optimization of short-term crude oil scheduling with dual pipelines and charging tank maintenance requirement", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "588", "Issue": "", "Page": "381", "JournalTitle": "Information Sciences"}, {"Title": "A decomposition-based constrained multi-objective evolutionary algorithm with a local infeasibility utilization mechanism for UAV path planning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "", "Page": "108495", "JournalTitle": "Applied Soft Computing"}, {"Title": "A tri-population based co-evolutionary framework for constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "", "Page": "101055", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A fuzzy constraint handling technique for decomposition-based constrained multi- and many-objective optimization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "597", "Issue": "", "Page": "318", "JournalTitle": "Information Sciences"}, {"Title": "Constrained multi-objective evolutionary algorithm with an improved two-archive strategy", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "246", "Issue": "", "Page": "108732", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A constrained multi-item EOQ inventory model for reusable items: Reinforcement learning-based differential evolution and particle swarm optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "118018", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A constrained multi-objective evolutionary algorithm based on decomposition with improved constrained dominance principle", "Authors": "Qinghua Gu; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101162", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An archive-based two-stage evolutionary algorithm for constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101161", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Constrained multi-objective optimization via two archives assisted push–pull evolutionary algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101178", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A constrained multiobjective evolutionary algorithm with the two-archive weak cooperation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "615", "Issue": "", "Page": "415", "JournalTitle": "Information Sciences"}, {"Title": "A constrained multi-objective evolutionary algorithm assisted by an additional objective function", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Xiangsong Kong", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109904", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi population-based chaotic differential evolution for multi-modal and multi-objective optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109909", "JournalTitle": "Applied Soft Computing"}, {"Title": "Fuzzy constraint prioritization to solve heavily constrained problems with the genetic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105768", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A review of surrogate-assisted evolutionary algorithms for expensive optimization problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "217", "Issue": "", "Page": "119495", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Solving a multi-objective chance constrained hierarchical optimization problem under intuitionistic fuzzy environment with its application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "217", "Issue": "", "Page": "119595", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A flexible two-stage constrained multi-objective evolutionary algorithm based on automatic regulation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "634", "Issue": "", "Page": "227", "JournalTitle": "Information Sciences"}, {"Title": "A multi-objective fuzzy facility location problem with congestion and priority for drone-based emergency deliveries", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "179", "Issue": "", "Page": "109167", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An ε ‐Constraint Method for Multiobjective Linear Programming in Intuitionistic Fuzzy Environment", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A constrained multi-objective evolutionary algorithm with two-stage resources allocation", "Authors": "Mingming Xia; <PERSON>; Minggang <PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "", "Page": "101313", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A dynamic dual-population co-evolution multi-objective evolutionary algorithm for constrained multi-objective optimization problems", "Authors": "Xiangsong Kong; <PERSON><PERSON><PERSON>; Zhisheng Lv", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "110311", "JournalTitle": "Applied Soft Computing"}, {"Title": "Surrogate-assisted MOEA/D for expensive constrained multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "639", "Issue": "", "Page": "119016", "JournalTitle": "Information Sciences"}, {"Title": "A new boredom-aware dual-resource constrained flexible job shop scheduling problem using a two-stage multi-objective particle swarm optimization algorithm", "Authors": "Jiaxuan Shi; Mingzhou Chen; Yumin <PERSON>", "PubYear": 2023, "Volume": "643", "Issue": "", "Page": "119141", "JournalTitle": "Information Sciences"}, {"Title": "Handling constrained many-objective optimization problems via determinantal point processes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "643", "Issue": "", "Page": "119260", "JournalTitle": "Information Sciences"}, {"Title": "A multi-objective Chaos Game Optimization algorithm based on decomposition and random learning mechanisms for numerical optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "110525", "JournalTitle": "Applied Soft Computing"}, {"Title": "Global and local feasible solution search for solving constrained multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "649", "Issue": "", "Page": "119467", "JournalTitle": "Information Sciences"}, {"Title": "An adaptive tradeoff evolutionary algorithm with composite differential evolution for constrained multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "83", "Issue": "", "Page": "101386", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Deep reinforcement learning assisted co-evolutionary differential evolution for constrained optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "83", "Issue": "", "Page": "101387", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Migration-based algorithm library enrichment for constrained multi-objective optimization and applications in algorithm selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "649", "Issue": "", "Page": "119593", "JournalTitle": "Information Sciences"}, {"Title": "A coevolutionary constrained multi-objective algorithm with a learning constraint boundary", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110845", "JournalTitle": "Applied Soft Computing"}, {"Title": "High-dimensional interactive adaptive RVEA for multi-objective optimization of polyester polymerization process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "650", "Issue": "", "Page": "119707", "JournalTitle": "Information Sciences"}, {"Title": "A constrained multi-objective evolutionary algorithm with Pareto estimation via neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121718", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A dual-population algorithm based on self-adaptive epsilon method for constrained multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "655", "Issue": "", "Page": "119906", "JournalTitle": "Information Sciences"}, {"Title": "Reinforcement learning-based differential evolution algorithm for constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "131", "Issue": "", "Page": "107817", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 115492417, "Title": "日本人間工学会第62回大会特別講演開催・聴講報告", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.220", "PubYear": 2021, "Volume": "57", "Issue": "4", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "美恵子 大須賀", "Affiliation": "大阪工業大学 ロボティクス＆デザイン工学部"}], "References": []}, {"ArticleId": 115492423, "Title": "日本人間工学会関東支部2020年度講演会開催報告", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.143", "PubYear": 2021, "Volume": "57", "Issue": "3", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "憲司 武藤", "Affiliation": "芝浦工業大学工学部情報通信工学科　講演会担当"}], "References": []}, {"ArticleId": 115492465, "Title": "DSME-FOTA: Firmware over-the-air update framework for IEEE 802.15.4 DSME MAC to enable large-scale multi-hop industrial IoT networks", "Abstract": "The Internet of Things (IoT) is rapidly expanding in applications ranging from smart homes and smart cities to various other domains, including agriculture, transportation, industrial automation, and environmental monitoring. Regardless of the application domain, firmware update over-the-air (FOTA) capability is critical for any IoT network, and its importance is particularly evident in the field of industrial IoT. This paper presents a comprehensive solution designed for large-scale industrial IoT networks, with a special focus on Deterministic Synchronous Multi-channel Extension (DSME)-based multi-hop networks, called DSME-FOTA. The paper provides a detailed FOTA protocol design, a numerical analysis, and proof-of-concept experiments. The analytical and experimental results show that the total firmware update time of DSME-FOTA exhibits a first-order dependence on both the number of hops and the firmware binary size.", "Keywords": "Firmware over-the-air; DSME; DSME-FOTA; Internet of Things; Industrial IoT; Multi-hop network; IEEE 802.15.4; Wireless sensor network; LPWA; LoRa", "DOI": "10.1016/j.iot.2024.101239", "PubYear": 2024, "Volume": "27", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Telecommunications Research Institute, 218 Gajeong-ro, Daejeon, 34129, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Telecommunications Research Institute, 218 Gajeong-ro, Daejeon, 34129, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Telecommunications Research Institute, 218 Gajeong-ro, Daejeon, 34129, Republic of Korea;Corresponding author"}], "References": [{"Title": "Secure firmware Over-The-Air updates for IoT: Survey, challenges, and discussions", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "", "Page": "100508", "JournalTitle": "Internet of Things"}, {"Title": "Over-the-air firmware update for IoT devices on the wild", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "100578", "JournalTitle": "Internet of Things"}, {"Title": "DSME-LoRa: Seamless Long-range Communication between Arbitrary Nodes in the Constrained IoT", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}, {"Title": "i-DSME: An industrial-DSME MAC protocol for smart factory automation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "", "Page": "100859", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 115492479, "Title": "Experimental study of cutting forces and surface topography in three-dimensional vibration-assisted milling for fabricating structured surfaces", "Abstract": "<p>In the vibration-assisted milling process, the cutting force as a very important parameter can reflect the cutting characteristics of this machining method. In this study, a three-dimensional vibration-assisted milling system was utilized to focus on the changing law of the cutting forces and the topography of the structured surface during machining. Firstly, the kinematic analysis proves that the trajectory of the tool relative to the workpiece under the excitation of the vibration device is a complex spatial spiral curve. Then, many vibration-assisted milling experiments were carried out to investigate the effect of the process variables, such as the spindle speed, feed rate, amplitude of vibration, vibration frequency, and tool radius, on the cutting forces (average and maximum cutting forces) and the topographical characteristics of the structured surfaces. The experimental results sufficiently confirmed that the amplitude of vibration is the most important process variable associated with the cutting forces, followed by spindle speed and vibration frequency; for the topographic features of the structured surface, the amplitude of vibration also has an important effect on the three-dimensional dimensions of each structural unit; by contrast, the vibration frequency and the feed rate do have a significant effect on the topography of structured surfaces. The results of the research provide critical guidance for 3D vibration-assisted milling to fabricate a variety of structured surfaces.</p>", "Keywords": "Vibration-assisted milling; Structured surfaces; Cutting forces; Surface topography", "DOI": "10.1007/s00170-024-13804-4", "PubYear": 2024, "Volume": "133", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory for Advanced Mechatronic System Design and Intelligent Control, School of Mechanical Engineering, Tianjin University of Technology, Tianjin, China; National Demonstration Center for Experimental Mechanical and Electrical Engineering Education, Tianjin University of Technology, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory for Advanced Mechatronic System Design and Intelligent Control, School of Mechanical Engineering, Tianjin University of Technology, Tianjin, China; National Demonstration Center for Experimental Mechanical and Electrical Engineering Education, Tianjin University of Technology, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory for Advanced Mechatronic System Design and Intelligent Control, School of Mechanical Engineering, Tianjin University of Technology, Tianjin, China; National Demonstration Center for Experimental Mechanical and Electrical Engineering Education, Tianjin University of Technology, Tianjin, China"}, {"AuthorId": 4, "Name": "Bingrui Lv", "Affiliation": "Key Laboratory of Advanced Ceramics and Machining Technology, Ministry of Education, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory for Advanced Mechatronic System Design and Intelligent Control, School of Mechanical Engineering, Tianjin University of Technology, Tianjin, China; National Demonstration Center for Experimental Mechanical and Electrical Engineering Education, Tianjin University of Technology, Tianjin, China; Corresponding author."}], "References": []}, {"ArticleId": 115492528, "Title": "Personal Data Protection Model in IOMT-Blockchain on Secured Bit-Count Transmutation Data Encryption Approach", "Abstract": "", "Keywords": "", "DOI": "10.54216/FPA.160111", "PubYear": 2024, "Volume": "16", "Issue": "1", "JournalId": 91518, "JournalTitle": "Fusion: Practice and Applications", "ISSN": "2770-0070", "EISSN": "2692-4048", "Authors": [{"AuthorId": 1, "Name": "admin admin", "Affiliation": "Department of Computer Science, College of Computer and Information Sciences, Majmaah University, Majmaah, 11952, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, University of the Cumberlands, Williamsburg, KY, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Computer and Information Sciences, Majmaah University, Majmaah, 11952, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computer and Information Systems, Islamic University of Madinah, Medinah, 42351, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "IT Programs Center, Faculty of IT Department, Institute of Public Administration, Riyadh, 11141, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer Science and Engineering, Taibah University, Medina 42353, Saudi Arabia"}], "References": []}, {"ArticleId": 115492545, "Title": "Machine learning meets Ke<PERSON>: inverting <PERSON><PERSON>’s equation for All vs All conjunction analysis", "Abstract": "<p>The number of satellites in orbit around Earth is increasing rapidly, with&#xD;the risk of collision rising accordingly. Trends of the global population of satellites&#xD;need to be analyzed to test the viability and impact of proposed rules and laws&#xD;affecting the satellite population and collision avoidance strategies. This requires&#xD;large scale simulations of satellites that are propagated on long timescales to compute&#xD;the large amounts of actionable close encounters (called conjunctions), which could&#xD;lead to collisions. Rigorously checking for conjunctions by computing future states of&#xD;orbits is computationally expensive due to the large amount of objects involved and&#xD;conjunction filters are thus used to remove non-conjuncting orbit pairs from the list&#xD;of possible conjunctions. In this work, we explore the possibility of machine learning&#xD;based conjunction filters using several algorithms such as eXtreme Gradient Boosting,&#xD;TabNet and (physics-informed) neural networks and deep operator networks. To show&#xD;the viability and the potential of machine learning based filters, these algorithms are&#xD;trained to predict the future state of orbits. For the physics-informed approaches,&#xD;multiple partial differential equations are set up using the Kepler equation as a basis.&#xD;The empirical results demonstrate that physics-informed deep operator networks are&#xD;capable of predicting the future state of orbits using these equations (RMSE: 0.136) and&#xD;outperform eXtreme Gradient Boosting (RMSE: 0.568) and TabNet (RMSE: 0.459).&#xD;We also propose a filter based on the trained deep operator network which is shown&#xD;to outperforms the filter capability of the commonly used perigee-apogee test and the&#xD;orbit path filter on a synthetic dataset, while being on average 3.2 times faster to&#xD;compute than a rigorous conjunction check.</p>", "Keywords": "", "DOI": "10.1088/2632-2153/ad51cc", "PubYear": 2024, "Volume": "5", "Issue": "2", "JournalId": 72803, "JournalTitle": "Machine Learning: Science and Technology", "ISSN": "", "EISSN": "2632-2153", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Tabular data: Deep learning is not all you need", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "", "Page": "84", "JournalTitle": "Information Fusion"}, {"Title": "Enhancing computational fluid dynamics with machine learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "6", "Page": "358", "JournalTitle": "Nature Computational Science"}]}, {"ArticleId": 115492569, "Title": "ErfReLU: adaptive activation function for deep neural network", "Abstract": "<p>Recent research has found that the activation function (AF) plays a significant role in introducing non-linearity to enhance the performance of deep learning networks. Researchers recently started developing activation functions that can be trained throughout the learning process, known as trainable, or adaptive activation functions (AAF). Research on AAF that enhances the outcomes is still in its early stages. In this paper, a novel activation function ‘ErfReLU’ has been developed based on the erf function and ReLU. This function leverages the advantages of both the Rectified Linear Unit (ReLU) and the error function (erf). A comprehensive overview of activation functions like Sigmoid, ReLU, Tanh, and their properties have been briefly explained. Adaptive activation functions like Tanhsoft1, Tanhsoft2, Tanhsoft3, TanhLU, SAAF, ErfAct, Pserf, Smish, and Serf is also presented. Lastly, comparative performance analysis of 9 trainable activation functions namely Tanhsoft1, Tanhsoft2, Tanhsoft3, TanhLU, SAAF, ErfAct, Pserf, Smish, and Serf with the proposed one has been performed. These activation functions are used in MobileNet, VGG16, and ResNet models and their performance is evaluated on benchmark datasets such as CIFAR-10, MNIST, and FMNIST.</p>", "Keywords": "Activation function; Deep learning; Image classification", "DOI": "10.1007/s10044-024-01277-w", "PubYear": 2024, "Volume": "27", "Issue": "2", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, National Institute of Technology Raipur, Raipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Raipur, Raipur, India; Corresponding author."}], "References": [{"Title": "“SPOCU”: scaled polynomial constant unit activation function", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "8", "Page": "3385", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Shape autotuning activation function", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114534", "JournalTitle": "Expert Systems with Applications"}, {"Title": "RSigELU: A nonlinear activation function for deep neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114805", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Learnable Leaky ReLU (LeLeLU): An Alternative Accuracy-Optimized Activation Function", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "12", "Page": "513", "JournalTitle": "Information"}, {"Title": "Enhancement of neural networks with an alternative activation function tanhLU", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "117181", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Activation functions in deep learning: A comprehensive survey and benchmark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "503", "Issue": "", "Page": "92", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 115492584, "Title": "2021年度人間工学専門資格認定試験実施のお知らせ（A方式）", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.3.Notice3", "PubYear": 2021, "Volume": "57", "Issue": "3", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [], "References": []}, {"ArticleId": 115492646, "Title": "Splines on Manifolds: a Survey", "Abstract": "Splines in the manifold setting have been defined as extensions from the standard Euclidean setting, but they are far more complicated. Alternative approaches, which are equivalent in the Euclidean case, lead to different results in the manifold case; the existence conditions are often quite restrictive; and the necessary computations are rather involved. All difficulties stem from the peculiar nature of the geodesic distance: in general, shortest geodesics may be not unique and the dependence on their endpoints may not be smooth; and distances cannot be computed in closed form. The former issue may impose strong limitations on the placement of control points. While the latter may greatly complicate the computations. Nevertheless, some recent results suggest that splines on surfaces may have practical impact on CAGD applications. We review the literature on this topic, accounting for both theoretical results and practical implementations.", "Keywords": "Geodesic splines; Subdivision curves; Manifold setting", "DOI": "10.1016/j.cagd.2024.102349", "PubYear": 2024, "Volume": "112", "Issue": "", "JournalId": 6580, "JournalTitle": "Computer Aided Geometric Design", "ISSN": "0167-8396", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DIBRIS - University of Genoa, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "DIBRIS - University of Genoa, Italy"}], "References": [{"Title": "You can find geodesic paths in triangle meshes by just flipping edges", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Practical Computation of the Cut Locus on Discrete Surfaces", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "5", "Page": "261", "JournalTitle": "Computer Graphics Forum"}, {"Title": "geoTangle \n : Interactive Design of Geodesic Tangle Patterns on Surfaces", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Computing the Riemannian center of mass on meshes", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "103", "Issue": "", "Page": "102203", "JournalTitle": "Computer Aided Geometric Design"}, {"Title": "<PERSON>'s Algorithm in Geometric Data Analysis: Theory and Application", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "110", "Issue": "", "Page": "102288", "JournalTitle": "Computer Aided Geometric Design"}, {"Title": "CPoser: An Optimization-after-Parsing Approach for Text-to-Pose Generation Using Large Language Models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 115492665, "Title": "IEA2021　PIE企画セッション報告：“Neuroergonomics Symposium”参加報告", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.214", "PubYear": 2021, "Volume": "57", "Issue": "4", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "快之 鎌倉", "Affiliation": "大阪工業大学　情報科学部"}], "References": []}, {"ArticleId": 115492683, "Title": "学会だより・編集後記・奥付", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.150", "PubYear": 2021, "Volume": "57", "Issue": "3", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [], "References": []}, {"ArticleId": 115492693, "Title": "表紙・目次", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.4.Cover1", "PubYear": 2021, "Volume": "57", "Issue": "4", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [], "References": []}, {"ArticleId": 115492699, "Title": "日本人間工学会関西支部 2021年度大会のご案内（第1報）", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.4.Notice8", "PubYear": 2021, "Volume": "57", "Issue": "4", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [], "References": []}, {"ArticleId": 115492717, "Title": "裏表紙・目次", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.1.Cover2", "PubYear": 2021, "Volume": "57", "Issue": "1", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [], "References": []}, {"ArticleId": 115492726, "Title": "SDGs検討委員会　委員募集のお知らせ", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.57.3.Notice6", "PubYear": 2021, "Volume": "57", "Issue": "3", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [], "References": []}, {"ArticleId": 115492766, "Title": "Determinants of Institutionalized Political Participation in Elderly Women: Insights From a Chinese Empirical Study", "Abstract": "<p>This study explores the factors affecting elderly women's institutional political participation. The inverse relationship between economic status and participation may be attributed to poverty constraining the political engagement capabilities of elderly women, while traditional media, especially television, enhances it by providing easily accessible information resources. In contrast, new media shows minimal influence, hinting at a generational digital divide or preference for traditional information channels. Surprisingly, close social circles, notably neighbors and friends, deter participation, possibly reflecting deep-rooted norms or localized echo chambers. These findings highlight the complex interplay of factors shaping political engagement among elderly women and suggest a need for tailored strategies to promote their active involvement in institutional politics.</p>", "Keywords": "", "DOI": "10.4018/IJEGR.345398", "PubYear": 2024, "Volume": "20", "Issue": "1", "JournalId": 8342, "JournalTitle": "International Journal of Electronic Government Research", "ISSN": "1548-3886", "EISSN": "1548-3894", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong University of Finance and Economics, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Guo", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University, China"}], "References": [{"Title": "Factors Affecting the Institutionalized Political Participation of Chinese Women", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Electronic Government Research"}]}, {"ArticleId": 115492819, "Title": "An intelligent surgical video retrieval for computer vision enhancement in medical diagnosis using deep learning techniques", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-18813-9", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Support vector regression and extended nearest neighbor for video object retrieval", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "2", "Page": "837", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Research on local feature indexing of multimedia video based on intelligent soft computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "15", "Page": "22757", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Improved Fuzzy-Based SVM Classification System Using Feature Extraction for Video Indexing and Retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "5", "Page": "1716", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Multimedia image and video retrieval based on an improved HMM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "6", "Page": "2093", "JournalTitle": "Multimedia Systems"}, {"Title": "Multimedia image and video retrieval based on an improved HMM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "6", "Page": "2093", "JournalTitle": "Multimedia Systems"}, {"Title": "Research on sports video retrieval algorithm based on semantic feature extraction", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "14", "Page": "21941", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Fine-grained correlation analysis for medical image retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "106992", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "The use of Big Data Analytics in healthcare", "Authors": "Kornelia Batko; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "A new design of multimedia big data retrieval enabled by deep feature learning and Adaptive Semantic Similarity Function", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "3", "Page": "1039", "JournalTitle": "Multimedia Systems"}, {"Title": "Content based video retrieval using dynamic textures", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "1", "Page": "59", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Content based video retrieval using deep learning feature extraction by modified VGG_16", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "9", "Page": "4235", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "DnS: Distill-and-Select for Efficient and Accurate Video Indexing and Retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "10", "Page": "2385", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "An Overview of Data Warehouse and Data Lake in Modern Enterprise Data Management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "132", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Content based video retrieval system using two stream convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "16", "Page": "24465", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 115492928, "Title": "Contribution of Residual Signals to the Detection of Face Swapping in Deepfake Videos", "Abstract": "The impressive rise of Deep Learning and, more specifically, the discovery of generative adversarial networks has revolutionised the world of Deepfake. The forgeries are becoming more and more realistic, and consequently harder to detect. Attesting whether a video content is authentic is increasingly sensitive. Furthermore, free access to forgery technologies is dramatically increasing and very worrying. Numerous methods have been proposed to detect these deepfakes and it is difficult to know which detection methods are still accurate regarding the recent advances. Therefore, an approach for face swapping detection in videos, based on residual signal analysis is presented in this paper.", "Keywords": "Deepfake videos;Face swapping;Residual signals;Digital forensics;Deep Learning", "DOI": "10.2352/EI.2024.36.4.MWSF-334", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115492949, "Title": "Safety of Human–Artificial Intelligence Systems: Applying Safety Science to Analyze Loopholes in Interactions between Human Organizations, Artificial Intelligence, and Individual People", "Abstract": "<p>Loopholes involve misalignments between rules about what should be done and what is actually done in practice. The focus of this paper is loopholes in interactions between human organizations’ implementations of task-specific artificial intelligence and individual people. The importance of identifying and addressing loopholes is recognized in safety science and in applications of AI. Here, an examination is provided of loophole sources in interactions between human organizations and individual people. Then, it is explained how the introduction of task-specific AI applications can introduce new sources of loopholes. Next, an analytical framework, which is well-established in safety science, is applied to analyses of loopholes in interactions between human organizations, artificial intelligence, and individual people. The example used in the analysis is human–artificial intelligence systems in gig economy delivery driving work.</p>", "Keywords": "", "DOI": "10.3390/informatics11020036", "PubYear": 2024, "Volume": "11", "Issue": "2", "JournalId": 40001, "JournalTitle": "Informatics", "ISSN": "", "EISSN": "2227-9709", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "VTT Technical Research Centre of Finland, FI-02150 Espoo, Finland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "RoboticsLab, University Carlos III of Madrid, 28911 Leganés, Spain"}], "References": [{"Title": "Human factors risk assessment: An integrated method for improving safety in clinical use of medical devices", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105918", "JournalTitle": "Applied Soft Computing"}, {"Title": "Artificial Intelligence, Values, and Alignment", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "411", "JournalTitle": "Minds and Machines"}, {"Title": "Data and its (dis)contents: A survey of dataset development and use in machine learning research", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "11", "Page": "100336", "JournalTitle": "Patterns"}, {"Title": "Real‐time out‐of‐distribution detection in cyber‐physical systems with learning‐enabled components", "Authors": "<PERSON><PERSON>ng <PERSON>; Xenofon <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "4", "Page": "212", "JournalTitle": "IET Cyber-Physical Systems: Theory & Applications"}, {"Title": "Reward (Mis)design for autonomous driving", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "316", "Issue": "", "Page": "103829", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 115493002, "Title": "Rethinking Data Management Planning: Introducing Research Output Management Planning (ROMPi) Approach", "Abstract": "", "Keywords": "", "DOI": "10.5334/dsj-2024-034", "PubYear": 2024, "Volume": "23", "Issue": "", "JournalId": 11331, "JournalTitle": "Data Science Journal", "ISSN": "", "EISSN": "1683-1470", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Connected Research: The Potential of the PID Graph", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "100180", "JournalTitle": "Patterns"}, {"Title": "Reproducible Research Publication Workflow: A Canonical Workflow\n Framework and FAIR Digital Object Approach to Quality Research\n Output", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "2", "Page": "306", "JournalTitle": "Data Intelligence"}, {"Title": "Curriculum Development for FAIR Data Stewardship", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> O<PERSON>po", "PubYear": 2022, "Volume": "4", "Issue": "4", "Page": "991", "JournalTitle": "Data Intelligence"}]}, {"ArticleId": 115493105, "Title": "Microsaccade-inspired event camera for robotics", "Abstract": "Neuromorphic vision sensors or event cameras have made the visual perception of extremely low reaction time possible, opening new avenues for high-dynamic robotics applications. These event cameras’ output is dependent on both motion and texture. However, the event camera fails to capture object edges that are parallel to the camera motion. This is a problem intrinsic to the sensor and therefore challenging to solve algorithmically. Human vision deals with perceptual fading using the active mechanism of small involuntary eye movements, the most prominent ones called microsaccades. By moving the eyes constantly and slightly during fixation, microsaccades can substantially maintain texture stability and persistence. Inspired by microsaccades, we designed an event-based perception system capable of simultaneously maintaining low reaction time and stable texture. In this design, a rotating wedge prism was mounted in front of the aperture of an event camera to redirect light and trigger events. The geometrical optics of the rotating wedge prism allows for algorithmic compensation of the additional rotational motion, resulting in a stable texture appearance and high informational output independent of external motion. The hardware device and software solution are integrated into a system, which we call artificial microsaccade–enhanced event camera (AMI-EV). Benchmark comparisons validated the superior data quality of AMI-EV recordings in scenarios where both standard cameras and event cameras fail to deliver. Various real-world experiments demonstrated the potential of the system to facilitate robotics perception both for low-level and high-level vision tasks.", "Keywords": "", "DOI": "10.1126/scirobotics.adj8124", "PubYear": 2024, "Volume": "9", "Issue": "90", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Maryland, College Park, MD 20742, USA.;College of Control Science and Engineering, Zhejiang University, Hangzhou, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Huzhou Institute of Zhejiang University, Huzhou, China.;College of Optical Science and Engineering, Zhejiang University, Hangzhou, China."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Control Science and Engineering, Zhejiang University, Hangzhou, China.;Huzhou Institute of Zhejiang University, Huzhou, China."}, {"AuthorId": 4, "Name": "Jing<PERSON> Chen", "Affiliation": "Department of Computer Science, University of Maryland, College Park, MD 20742, USA."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Maryland, College Park, MD 20742, USA."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, Zhejiang University, Hangzhou, China.;Huzhou Institute of Zhejiang University, Huzhou, China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong, China."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "College of Optical Science and Engineering, Zhejiang University, Hangzhou, China."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Huzhou Institute of Zhejiang University, Huzhou, China."}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "College of Control Science and Engineering, Zhejiang University, Hangzhou, China.;Huzhou Institute of Zhejiang University, Huzhou, China."}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Maryland, College Park, MD 20742, USA.;Institute for Advance Computer Studies, University of Maryland, College Park, MD 20742, USA.;Institute for Systems Research, University of Maryland, College Park, MD 20742, USA."}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, Zhejiang University, Hangzhou, China.;Huzhou Institute of Zhejiang University, Huzhou, China."}, {"AuthorId": 14, "Name": "Cornelia <PERSON>ü<PERSON>", "Affiliation": "Department of Computer Science, University of Maryland, College Park, MD 20742, USA.;Institute for Advance Computer Studies, University of Maryland, College Park, MD 20742, USA.;Institute for Systems Research, University of Maryland, College Park, MD 20742, USA."}], "References": [{"Title": "Dynamic obstacle avoidance for quadrotors with event cameras", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "40", "Page": "eaaz9712", "JournalTitle": "Science Robotics"}, {"Title": "Towards uniform point distribution in feature-preserving point cloud filtering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "2", "Page": "249", "JournalTitle": "Computational Visual Media"}, {"Title": "Neuromorphic high-frequency 3D dancing pose estimation in dynamic environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "547", "Issue": "", "Page": "126388", "JournalTitle": "Neurocomputing"}, {"Title": "Microsaccade-inspired event camera for robotics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "9", "Issue": "90", "Page": "eadj8124", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 115493116, "Title": "Bundle Protocol Security Models and Policies for Safeguarding Space Information Networks", "Abstract": "", "Keywords": "", "DOI": "10.1109/JRFID.2024.3406890", "PubYear": 2024, "Volume": "8", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Nacional de Córdoba, Argentina"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Nacional de Córdoba, Argentina"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Osmium Solutions, Spain -Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Osmium Solutions, Spain -Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universidad Nacional de Córdoba, Argentina"}], "References": [{"Title": "Cyber security in New Space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "3", "Page": "287", "JournalTitle": "International Journal of Information Security"}, {"Title": "Routing in the Space Internet: A contact graph routing tutorial", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "102884", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Securing the Inter-Spacecraft Links: Physical Layer Key Generation From Doppler Frequency Shift", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "232", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}, {"Title": "Threat analysis for space information network based on network security attributes: a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "3", "Page": "3429", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Routing Heterogeneous Traffic in Delay-Tolerant Satellite Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "", "Page": "390", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}]}, {"ArticleId": *********, "Title": "Evaluating the effectiveness of sonification in science education using Edukoi", "Abstract": "Science, Technology, Engineering, and Mathematics classes are mainly taught using visual supports. However, the advancement of technology and the increasing efforts to equip schools with digital instrumentation have opened up the possibility of exploring new teaching avenues, such as sonification. We explored the efficacy of sonification in education using a novel interactive tool, Edukoi, in the context of astronomy, which is predominantly disseminated through spectacular images, animations, and visuals. Edukoi is a motion-sensing sonification tool that converts images to sound in real-time for educational applications. Our study, conducted with nearly 150 middle-school students, included a preliminary questionnaire investigating the perception, engagement, and motivation of students towards science; two sessions dedicated to testing Edukoi and assessing the potentiality of the software for the recognition of the colour and the shape of real and sketchy images; and a final second administration of the questionnaire to capture a possible beneficial effect of the use of the tool in the engagement towards science. Results showed the effectiveness of <PERSON><PERSON><PERSON> in colour recognition and reasonable efficacy in shape identification. Although the questionnaire did not reveal an increment in science engagement over the time of the study, oral feedback from the students was positive. Edukoi presents a possible alternative teaching aid, potentially benefiting diverse learners, including the visually impaired. Further developments of the software are needed to enhance its effectiveness in conveying more complex features such as composite colours or shapes.", "Keywords": "", "DOI": "10.1007/s00779-024-01809-5", "PubYear": 2024, "Volume": "28", "Issue": "5", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of General Psychology, University of Padova, Padua, Italy; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Istituto Nazionale Di Astrofisica, Padua, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Astronomy Unit, Department of Physics, University of Trieste, Trieste, Italy; INAF - Osservatorio Astronomico Di Trieste, Trieste, Italy; IFPU - Institute for Fundamental Physics of the Universe, Trieste, Italy; Laboratoire Lagrange, Université Côte d’Azur, Observatoire de La Côte d’Azur, CNRS, Nice, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Dipartimento Di Fisica E Astronomia, Università Di Firenze, Florence, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of General Psychology, University of Padova, Padua, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of General Psychology, University of Padova, Padua, Italy"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of General Psychology, University of Padova, Padua, Italy"}], "References": [{"Title": "Evaluating the effectiveness of sonification in science education using Edukoi", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "5", "Page": "693", "JournalTitle": "Personal and Ubiquitous Computing"}]}, {"ArticleId": 115493324, "Title": "CPDP – regulatory sandboxes for trustworthy artificial intelligence – global and Latin American experiences", "Abstract": "", "Keywords": "", "DOI": "10.1080/13600869.2024.2351674", "PubYear": 2025, "Volume": "39", "Issue": "1", "JournalId": 25543, "JournalTitle": "International Review of Law, Computers & Technology", "ISSN": "1360-0869", "EISSN": "1364-6885", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Law School, Universidade de Brasilia (UnB), Brasília, Brazil;Faculty of Law and Criminology, Vrije Universiteit Brussels (VUB), Brussels, Belgium"}], "References": []}, {"ArticleId": 115493472, "Title": "Whole genome sequence data of 516 F2 plants of tomato (Solanum lycopersicum)", "Abstract": "<p>The large-fruited fresh-market tomato cultivated in the U.S. represents a unique fruit market class of contemporary (modern) tomatoes for direct consumption. The genomes of F<sub>2</sub> plants from crosses between inbred contemporary U.S. large-fruited fresh-market tomatoes were sequenced. 516 F<sub>2</sub> individual plants randomly selected from five different biparental segregating populations were used for DNA extraction. The polymerase chain reaction (PCR)-free, paired-end (2 × 150 bp) sequencing libraries (350 bp DNA fragment length) were prepared, and sequenced on average 5 Gb for each plant using the Illumina next-generation sequencing technologies [1,2]. Raw Illumina reads with adapter contamination and/or uncertain nucleotides constitute (Ns, >10 % of either read; Q-score 5 or lower, >50 % of either read) were removed. This data article will contribute to improving our knowledge of the genetic recombination and variation in tomato.</p><p>© 2024 The Author(s).</p>", "Keywords": "Fresh-market tomato;Genetic heterogeneity;Genetic recombination;Segregating population;Trait", "DOI": "10.1016/j.dib.2024.110567", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Horticultural Sciences Department, University of Florida, Gainesville, FL, USA."}], "References": []}, {"ArticleId": 115493477, "Title": "Screening and selection of cellulase-secreting yeast single cells using integrated double emulsion droplet and flow cytometry techniques", "Abstract": "The capability of yeast cell factories to secrete industrially important enzymes into the extracellular environment can significantly reduce overall production costs. This impacts a wide range of sectors, including pharmaceuticals, food and feed, textiles, detergents, materials, and energy. Although recent advances in molecular biology and laboratory automation have expanded our capability to construct enormous strain variant libraries, it calls for technologies to effectively screen thousands of strains with phenotypes that are spatially removed from the original cell. In this work, we demonstrate the application of integrated double emulsion (DE) droplets and fluorescence-activated cell sorting (FACS) technology for the screening and selection of yeast single cells based on extracellular enzyme activity. Single Saccharomyces cerevisiae cells secreting cellulase enzyme, β-glucosidase, are compartmentalized within highly monodisperse DE droplets with a fluorogenic substrate. After single-cell encapsulation, cells are cultivated within the DE droplets to allow enzyme production. Single cell-laden DE droplets are sorted by FACS to identity strains with high β-glucosidase secretion. Moreover, encapsulated S. cerevisiae cells are successfully recovered from DEs after sorting. We expect that this user-friendly method will be combined with a range of synthetic biology techniques to optimize value-added product yield and stability.", "Keywords": "Double emulsion droplets; Fluorescence-activated cell sorting; Saccharomyces cerevisiae; β-glucosidase; Single-cell enzyme assays", "DOI": "10.1016/j.snb.2024.136038", "PubYear": 2024, "Volume": "416", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Macquarie University, Sydney, NSW 2109, Australia;Department of Bioengineering and Therapeutic Sciences, University of California San Francisco, San Francisco, CA 94158, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering, Macquarie University, Sydney, NSW 2109, Australia;School of Mechanical and Manufacturing Engineering, University of New South Wales, Sydney, NSW 2052, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ARC Centre of Excellence in Synthetic Biology, Australia;School of Natural Sciences, Macquarie University, Sydney, NSW 2109, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ARC Centre of Excellence in Synthetic Biology, Australia;School of Natural Sciences, Macquarie University, Sydney, NSW 2109, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "ARC Centre of Excellence in Synthetic Biology, Australia;School of Natural Sciences, Macquarie University, Sydney, NSW 2109, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "ARC Centre of Excellence in Synthetic Biology, Australia;School of Natural Sciences, Macquarie University, Sydney, NSW 2109, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "ARC Centre of Excellence for Nanoscale BioPhotonics, Australia;School of Mathematical and Physical Sciences, Macquarie University, Sydney, NSW 2109, Australia"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Engineering, Macquarie University, Sydney, NSW 2109, Australia;School of Mechanical and Manufacturing Engineering, University of New South Wales, Sydney, NSW 2052, Australia;Corresponding author at: School of Mechanical and Manufacturing Engineering, University of New South Wales, Sydney, NSW 2052, Australia"}], "References": []}, {"ArticleId": *********, "Title": "Fire Detection Systems Using Feature Entropy Guided Neural Network", "Abstract": "Fire detection from video has become possible and more feasible in prevention of fire disaster due to deep convolutional neural networks (CNNs) and embedded processing hardware. Artificial intelligence (AI) methods generally require more computational time and hardware with powerful graphical processing unit (GPU). In this paper, we propose cost-effective deep CNN architecture for fire detection from video with respect to computational performance of Jetson Nano from NVIDIA. In our paper we compare CNN networks (AlexNet and SqueezeNet) with our proposed CNN architecture. The proposed CNN architecture finds equilibrium between efficiency and accuracy for target system (Jetson Nano). We used CNNs which show high accuracy and low loss.", "Keywords": "Fire Detection;Convolutional Neural Network;Surveillance", "DOI": "10.32628/CSEIT2410287", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON> <PERSON><PERSON>", "Affiliation": "Associate Professor, Department of CSE, Sri Vasavi Institute of Engineering & Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 2, "Name": " I T V V S N S Pravallica", "Affiliation": "Department of CSE, Sri Vasavi Institute of Engineering & Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "Department of CSE, Sri Vasavi Institute of Engineering & Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 4, "Name": " <PERSON><PERSON>", "Affiliation": "Department of CSE, Sri Vasavi Institute of Engineering & Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 5, "Name": " <PERSON><PERSON>", "Affiliation": "Department of CSE, Sri Vasavi Institute of Engineering & Technology, Nandamuru, Andhra Pradesh, India"}], "References": []}, {"ArticleId": 115493523, "Title": "Characterization of SSR markers from draft genome assembly and genotypic data in Hedychium spicatum (Zingiberaceae).", "Abstract": "<p>The plant family Zingiberaceae consists of many medicinally important tropical herbs. Here, we provide a contig level genome assembly for <i>Hedychium spicatum</i>, one of the medicinally utilized species in this family<i>.</i> We used genome assembly to identify candidate Simple Sequence Repeat (SSR) markers in the nuclear, chloroplast and mitochondrial compartments. We identified a total of 60,695 SSRs, which consisted of di-, tri-, tetra-, penta- and complex repeat types, and primers were designed for 14,851 SSR loci from both coding and non-coding parts of the genome. A total of 62 sets of candidate SSR primers were tested, out of which a final set of 20 SSR markers were characterized and they met the criteria of amplification success and retention of the repeat motif and homology. Out of these 20 markers, we genotyped 11 markers by amplifying and sizing 99 accessions of <i>H. spicatum</i> from 13 different geographic locations. The 11 markers were also characterised for four congeneric species, <i>H. ellipticum, H. go<PERSON>zianum, H. venustum</i>, and <i>H. yunnanense</i>. All 11 SSR markers were found to be polymorphic and showed cross-species amplification. The total number of alleles per locus varied from 5 to 25. SSR markers continue to be a valuable tool for researchers because of their cost-effectiveness and simplicity. The cross-species amplification and variability of the SSR markers generated here further extend the utility of the markers to other <i>Hedychium</i> spp. The markers presented in this dataset can be used for a variety of studies, such as population genetics of invasive <i>Hedychium</i> species, QTL mapping, DNA fingerprinting, parentage analysis and genetic diversity assessments.</p><p>© 2024 Published by Elsevier Inc.</p>", "Keywords": "Allelic diversity;Asian tropics;Fragment analysis;Genotyping;Microsatellites;North East India;Rhizomatous herb;Spiked ginger lily", "DOI": "10.1016/j.dib.2024.110568", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tropical Ecology and Evolution (TrEE) Lab, Department of Biological Sciences, Indian Institute of Science Education and Research Bhopal (IISER Bhopal), Madhya Pradesh 462066, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tropical Ecology and Evolution (TrEE) Lab, Department of Biological Sciences, Indian Institute of Science Education and Research Bhopal (IISER Bhopal), Madhya Pradesh 462066, India."}], "References": []}, {"ArticleId": 115493542, "Title": "Fine-Grained Complexity-Driven Latency Predictor in Hardware-Aware Neural Architecture Search using Composite Loss", "Abstract": "An efficient hardware-aware neural architecture search is crucial for automating the creation of network architectures that are optimized for resource-limited platforms. However, challenges arise owing to inaccuracies in key hardware performance metrics, notably in latency estimation. This study introduces a composite loss-based complexity-driven latency predictor, which is an innovative approach that achieves remarkable evaluation accuracy with limited training data. This reveals a robust correlation between the layer-based complexity features and network inference latency. This groundbreaking insight leverages these complex features as network architecture encodings for latency predictors, substantially enhancing the precision of latency assessments. In addition, a composite loss function is proposed that seamlessly integrates ranking and absolute performance losses. This novel approach addresses the limitations of rank-based loss methods, which often lack broader context. Incorporating a global perspective through absolute performance metrics significantly improves the generalization capabilities of the predictor across various benchmarks. Experimental results on the NAS-Bench-201, NAS-Bench-101, and MobileNetV3 benchmarks underscore the effectiveness of the predictor. For instance, in the NAS-Bench-201 evaluation, the predictor demonstrates a notable increase in <PERSON>'s tau correlation, from 0.738 to 0.9733. These findings highlight the enhanced accuracy of the proposed approach with far-reaching implications for optimizing network structures on resource-limited platforms.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120783", "PubYear": 2024, "Volume": "676", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi'an, 710071, China;The Key Laboratory of Smart Human-Computer Interaction and Wearable Technology of Shaanxi Province, Xi'an, 710071, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi'an, 710071, China;The Key Laboratory of Smart Human-Computer Interaction and Wearable Technology of Shaanxi Province, Xi'an, 710071, China;Corresponding authors at: School of Computer Science and Technology, Xidian University, Xi'an, 710071, China"}, {"AuthorId": 3, "Name": "Chengcheng Li", "Affiliation": "The 54th Research Institute of CETC, Shijiazhuang, 050050, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi'an, 710071, China"}, {"AuthorId": 5, "Name": "Wenkai Lv", "Affiliation": "Tencent AI Lab, Shenzhen, 518000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi'an, 710071, China;The Key Laboratory of Smart Human-Computer Interaction and Wearable Technology of Shaanxi Province, Xi'an, 710071, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi'an, 710071, China;The Key Laboratory of Smart Human-Computer Interaction and Wearable Technology of Shaanxi Province, Xi'an, 710071, China;Corresponding authors at: School of Computer Science and Technology, Xidian University, Xi'an, 710071, China"}], "References": [{"Title": "Neural Architecture Search Survey: A Hardware Perspective", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "MTLP-JR: Multi-task learning-based prediction for joint ranking in neural architecture search", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "105", "Issue": "", "Page": "108474", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A resource-efficient ECG diagnosis model for mobile health devices", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "648", "Issue": "", "Page": "119628", "JournalTitle": "Information Sciences"}, {"Title": "Neural architecture search with interpretable meta-features and fast predictors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Luís P.F. Garcia", "PubYear": 2023, "Volume": "649", "Issue": "", "Page": "119642", "JournalTitle": "Information Sciences"}, {"Title": "OnceNAS: Discovering efficient on-device inference neural networks for edge devices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "669", "Issue": "", "Page": "120567", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 115493584, "Title": "Strongly Tail-Optimal Scheduling in the Light-Tailed M/G/1", "Abstract": "<p>We study the problem of scheduling jobs in a queueing system, specifically an M/G/1 with light-tailed job sizes, to asymptotically optimize the response time tail. This means scheduling to make \\mathbfP [T > t], the chance a job's response time exceeds t, decay as quickly as possible in the t \\to \\infty limit. For some time, the best known policy was First-Come First-Served (FCFS), which has an asymptotically exponential tail: \\mathbfP [T > t] \\sim C e^-γ t . FCFS achieves the optimal *decay rate* γ, but its *tail constant* C is suboptimal. Only recently have policies that improve upon FCFS's tail constant been discovered. But it is unknown what the optimal tail constant is, let alone what policy might achieve it. In this paper, we derive a closed-form expression for the optimal tail constant C, and we introduce *γ-Boost*, a new policy that achieves this optimal tail constant. Roughly speaking, γ-Boost operates similarly to FCFS, but it pretends that small jobs arrive earlier than their true arrival times. This significantly reduces the response time of small jobs without unduly delaying large jobs, improving upon FCFS's tail constant by up to 50% with only moderate job size variability, with even larger improvements for higher variability. While these results are for systems with full job size information, we also introduce and analyze a version of γ-Boost that works in settings with partial job size information, showing it too achieves significant gains over FCFS. Finally, we show via simulation that γ-Boost has excellent practical performance.</p>", "Keywords": "", "DOI": "10.1145/3656011", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, USA"}], "References": [{"Title": "Size-based scheduling for TCP flows: Implementation and performance evaluation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "183", "Issue": "", "Page": "107574", "JournalTitle": "Computer Networks"}, {"Title": "The Gittins Policy is Nearly Optimal in the M/G/k under Extremely General Conditions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems"}, {"Title": "On the Stochastic and Asymptotic Improvement of First-Come First-Ser<PERSON> and <PERSON><PERSON>g", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems"}, {"Title": "Optimal Scheduling in the Multiserver-job Model under Heavy Traffic", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems"}, {"Title": "Performance of the Gittins policy in the G/G/1 and G/G/k, with and without setup times", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "163", "Issue": "", "Page": "102377", "JournalTitle": "Performance Evaluation"}]}, {"ArticleId": 115493623, "Title": "Fingerprint Recognition and Verification using Fourier Domain Filtering and Histogram Equalization Techniques", "Abstract": "Fingerprint Recognition is a vital method in biometric identification and verification of human beings in various domains like Security, Digital Forensics, Internet of Things (IoT), and many more. Each individual human is having distinct fingerprint pattern than others, hence it is one of the most prominent and widely used method to distinguish individuals. Many research studies and solutions have been developed in biometric domain since a decade, which influences now in making the process of fingerprint recognition more optimized, faster and efficient. However, present fingerprint acquisition/recognition systems have some limitations, mainly longer computation time for fingerprint matching and evaluating the results. This paper presents a procedure for fingerprint matching that takes into account minutiae features in finger print images and the process of creating an OpenCV structure for minutiae extraction and matching of fingerprints.", "Keywords": "Fingerprint;OpenCV;Biometric;Security and Authentication;Minutiae", "DOI": "10.32628/CSEIT24102100", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of CSE, Sri Vasavi Institute of Engineering and Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": "UG Student, Department of CSE, Sri Vasavi Institute of Engineering and Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "UG Student, Department of CSE, Sri Vasavi Institute of Engineering and Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 4, "Name": " <PERSON><PERSON>", "Affiliation": "UG Student, Department of CSE, Sri Vasavi Institute of Engineering and Technology, Nandamuru, Andhra Pradesh, India"}, {"AuthorId": 5, "Name": " <PERSON><PERSON>", "Affiliation": "UG Student, Department of CSE, Sri Vasavi Institute of Engineering and Technology, Nandamuru, Andhra Pradesh, India"}], "References": []}, {"ArticleId": 115493651, "Title": "Enhancing Qur'anic Recitation Experience with CNN and MFCC Features for Emotion Identification", "Abstract": "<p>In this study, MFCC feature extraction and CNN algorithms are used to examine the identification of emotions in the murottal sounds of the Qur'an. A CNN model with labelled emotions is trained and tested, as well as data collection of Qur'anic murottal voices from a variety of readers using MFCC feature extraction to capture acoustic properties. The outcomes show that MFCC and CNN work together to significantly improve emotion identification. The CNN model attains an accuracy rate of 56 percent with the Adam optimizer (batch size 8) and a minimum of 45 percent with the RMSprop optimizer (batch size 16). Notably, accuracy is improved by using fewer emotional parameters, and the Adam optimizer is stable across a range of batch sizes. With its insightful analysis of emotional expression and user-specific recommendations, this work advances the field of emotion identification technology in the context of multitonal music.</p>", "Keywords": "Emotion identification;;Qur'an murottal sound;;MelFrequency Cepstral Coefficients (MFCC);;Convolutional Neural Network (CNN)", "DOI": "10.22219/kinetik.v9i2.2007", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 31102, "JournalTitle": "KINETIK", "ISSN": "2503-2259", "EISSN": "2503-2267", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>"}], "References": []}, {"ArticleId": 115493734, "Title": "Developing a lumped rainfall-runoff model in daily timestep for the Central European regions: A case study of the Czech Republic", "Abstract": "The negative impact of climate change mainly affects the delicate balance of the water cycle between precipitation, evaporation, discharge, and other interactions between the atmosphere and the surface of the earth. Thus, the adaptation of the environment to these changing hydrological conditions is crucial. One of the crucial tasks for the assessment of the environment adaptation is hydrological modeling. Hence, we propose a conceptual lumped water balance hydrological model which was developed for the Central European moderate climatic conditions. Based on previous works that aimed for a monthly timestep, this model's concept has been innovated to a daily timestep. The model was tested by modeling six catchments with a wide range of surface areas in the Czech Republic and using performance ranking system. Additionally, a comprehensive multi-model comparison was conducted across these catchments, further validating the robustness and superior performance of the proposed model.", "Keywords": "Hydrological modeling; Conceptual model; Water balance; Snowmelt; Unit hydrographs; Performance ranking system", "DOI": "10.1016/j.envsoft.2024.106092", "PubYear": 2024, "Volume": "179", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Landscape Water Management, Faculty of Civil Engineering, Brno University of Technology, Veveri 331/95, 602 00, Brno, Czech Republic;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Landscape Water Management, Faculty of Civil Engineering, Brno University of Technology, Veveri 331/95, 602 00, Brno, Czech Republic"}], "References": []}, {"ArticleId": 115493772, "Title": "A Robust Nonlinear Control Strategy for a Polytopic AUV System by Employing an NMI Approach", "Abstract": "This study outlines a robust control strategy for an autonomous underwater vehicle (AUV) operating in the steering plane. The nonlinear affine form represents the AUV behavior used in the control algorithm design. Besides, dissipativity and L<sub>2</sub> gain analysis are considered to design the control algorithm. The L<sub>2</sub> gain formulation is transformed into a Hamilton-<PERSON><PERSON><PERSON><PERSON> inequality which is further represented in terms of Nonlinear Matrix Inequalities (NMI). Furthermore, the pointwise polytopes are considered to formulate the optimization problem. Utilizing the concept of convex optimization principles, the control law is derived. Additionally, a backstepping approach addresses the yaw orientation, reducing the computing workload imposed on the system. The main objective is to ensure effective tracking of the desired yaw in the steering plane which further enhances the path-following task by exhibiting robust behavior and internal stability.", "Keywords": "Backstepping; AUV; NMI; Nonlinear H∞ Control; Steering Control", "DOI": "10.1016/j.ifacol.2024.05.018", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, VIT-AP University, Amaravati, Vijayawada, 522237, Andhra Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>a", "Affiliation": "School of Electronics Engineering, VIT-AP University, Amaravati, Vijayawada, 522237, Andhra Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Sciences, Indian Institute of Technology, Goa, 403401, India"}], "References": [{"Title": "Design of a backstepping technique-based robust state feedback optimal control law for autonomous underwater vehicle in depth plane using semi-definite programming", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "5", "Page": "551", "JournalTitle": "International Journal of Automation and Control"}]}, {"ArticleId": 115493890, "Title": "Evaluation of Biomechanical and Mental Workload During Human–Robot Collaborative Pollination Task", "Abstract": "Objective <p>The purpose of this study is to identify the potential biomechanical and cognitive workload effects induced by human robot collaborative pollination task, how additional cues and reliability of the robot influence these effects and whether interacting with the robot influences the participant’s anxiety and attitude towards robots.</p> Background <p>Human–Robot Collaboration (HRC) could be used to alleviate pollinator shortages and robot performance issues. However, the effects of HRC for this setting have not been investigated.</p> Methods <p>Sixteen participants were recruited. Four HRC modes, no cue, with cue, unreliable, and manual control were included. Three categories of dependent variables were measured: (1) spine kinematics (L5/S1, L1/T12, and T1/C7), (2) pupillary activation data, and (3) subjective measures such as perceived workload, robot-related anxiety, and negative attitudes towards robotics.</p> Results <p>HRC reduced anxiety towards the cobot, decreased joint angles and angular velocity for the L5/S1 and L1/T12 joints, and reduced pupil dilation, with the “with cue” mode producing the lowest values. However, unreliability was detrimental to these gains. In addition, HRC resulted in a higher flexion angle for the neck (i.e., T1/C7).</p> Conclusion <p>HRC reduced the physical and mental workload during the simulated pollination task. Benefits of the additional cue were minimal compared to no cues. The increased joint angle in the neck and unreliability affecting lower and mid back joint angles and workload requires further investigation.</p> Application <p>These findings could be used to inform design decisions for HRC frameworks for agricultural applications that are cognizant of the different effects induced by HRC.</p>", "Keywords": "biomechanics;human–robot interaction;indoor pollination;kinematics", "DOI": "10.1177/00187208241254696", "PubYear": 2025, "Volume": "67", "Issue": "2", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Florida, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "West Virginia University, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "West Virginia University, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Florida, USA"}], "References": [{"Title": "Human Factors Considerations and Metrics in Shared Space Human-Robot Collaboration: A Systematic Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "", "Page": "6", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "A system to improve the physical ergonomics in Human-Robot Collaboration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "689", "JournalTitle": "Procedia Computer Science"}, {"Title": "Distracted worker: Using pupil size and blink rate to detect cognitive load during manufacturing tasks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "106", "Issue": "", "Page": "103867", "JournalTitle": "Applied Ergonomics"}, {"Title": "Physiological and perceptual consequences of trust in collaborative robots: An empirical investigation of human and robot factors", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "106", "Issue": "", "Page": "103863", "JournalTitle": "Applied Ergonomics"}, {"Title": "Improving Workers’ Musculoskeletal Health During Human-Robot Collaboration Through Reinforcement Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "6", "Page": "1754", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}]}, {"ArticleId": 115494003, "Title": "An Efficient Corpus Indexer for dynamic corpora retrieval", "Abstract": "As a new paradigm for information retrieval, generative retrieval (GR) has achieved solid performance on various retrieval tasks. Despite its promising progress, this line of research cannot generalize on a dynamic corpora, where new documents are continually added to it. There are already some continual learning-based pioneering works focusing on this issue, yet the continual learning framework requires retraining after model deployment and may suffer from catastrophic forgetting issues. Hence, we propose a new retrieval framework noted as ECI (an Efficient Corpus Indexer for dynamic corpora retrieval). The ECI is a hybrid index framework containing generative and deep hashing indexes. We design a complementary training objective noted as Prefix-Sensitive Similarity Alignment, which can further improve the performance of generative retrieval. Besides, ECI enables incremental deep hashing learning and provides a deep hashing index-based retrieval scheme for new documents, thus solving the generalization problem on dynamic corpora. Furthermore, ECI utilizes techniques like whitening and query-generated data augmentation to enhance retrieval performance. In a dynamic corpus retrieval task built on the commonly used academic benchmark Natural Question, the ECI outperforms various baselines, including the state-of-the-art GR baseline and its variants.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124306", "PubYear": 2024, "Volume": "254", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing, People’s Republic of China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing, People’s Republic of China;Jinling Institute of Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing, People’s Republic of China"}], "References": []}, {"ArticleId": 115494204, "Title": "Synthetic Data Generation for AI-based Machine Vision Applications", "Abstract": "This paper presents a method for synthesizing 2D and 3D sensor data for various machine vision tasks. Depending on the task, different processing steps can be applied to a 3D model of an object. For object detection, segmentation and pose estimation, random object arrangements are generated automatically. In addition, objects can be virtually deformed in order to create realistic images of non-rigid objects. For automatic visual inspection, synthetic defects are introduced into the objects. Thus sensor-realistic datasets with typical object defects for quality control applications can be created, even in the absence of defective parts. The simulation of realistic images uses physically based rendering techniques. Material properties and different lighting situations are taken into account in the 3D models. The resulting tuples of 2D images and their ground truth annotations can be used to train a machine learning model, which is subsequently applied to real data. In order to minimize the reality gap, a random parameter set is selected for each image, resulting in images with high variety. Considering the use cases damage detection and object detection, it has been shown that a machine learning model trained only on synthetic data can also achieve very good results on real data.", "Keywords": "3D Modeling;Machine Learning;Machine Vision;Synthetic Data", "DOI": "10.2352/EI.2024.36.6.IRIACV-276", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115494221, "Title": "Detecting drifts in data streams using Kullback-Leibler (KL) divergence measure for data engineering applications", "Abstract": "The exponential growth of data coupled with the widespread application of artificial intelligence(AI) presents organizations with challenges in upholding data accuracy, especially within data engineering functions. While the Extraction, Transformation, and Loading process addresses error-free data ingestion, validating the content within data streams remains a challenge. Prompt detection and remediation of data issues are crucial, especially in automated analytical environments driven by AI. To address these issues, this study focuses on detecting drifts in data distributions and divergence within data fields processed from different sample populations. Using a hypothetical banking scenario, we illustrate the impact of data drift on automated decision-making processes. We propose a scalable method leveraging the Kullback-Leibler (KL) divergence measure, specifically the Population Stability Index (PSI), to detect and quantify data drift. Through comprehensive simulations, we demonstrate the effectiveness of PSI in identifying and mitigating data drift issues. This study contributes to enhancing data engineering functions in organizations by offering a scalable solution for early drift detection in data ingestion pipelines. We discuss related research works, identify gaps, and present the methodology and experiment results, underscoring the importance of robust data governance practices in mitigating risks associated with data drift and improving data observability.", "Keywords": "Kullback-Leibler divergence(KL); Data drift; Population Stability Index(PSI); Real-time data validation; Explainable AI; Concept drift; Data observability", "DOI": "10.1007/s42488-024-00119-y", "PubYear": 2024, "Volume": "6", "Issue": "3", "JournalId": 64686, "JournalTitle": "Journal of Data, Information and Management", "ISSN": "2524-6356", "EISSN": "2524-6364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Researcher, Computational and Data Sciences, Chapman University, Orange, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Associate Professor, Fowler School of Engineering, Chapman University, Orange, USA"}], "References": [{"Title": "Automating model management: a survey on metaheuristics for concept-drift adaptation", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "3-4", "Page": "211", "JournalTitle": "Journal of Data, Information and Management"}]}, {"ArticleId": 115494302, "Title": "A Novel Feed-Forward Control Scheme to Meet Transient Response Specifications of MIL-STD-704 for High-Power Aircraft Generators", "Abstract": "The three-stage BLDC aircraft generating system is one of the most prominent architectures in the novel generation aircraft. This system produces a regulated DC power from multi-phase AC power through an N-Pulse rectifier and converts variable frequency input to constant DC using static diodes. The key factor in developing military and civil aircraft is designing of power generating system and meeting the transient response requirements imposed by relevant standards is an important one among these. The military standards are applied in civil and military aircraft for the safe operation of the machinery. Especially in airborne applications, the transient characteristics become crucially important to meet the mission requirements formalized by MIL-STD-704F. The mathematical models of the BLDC generator and controlling systems are realized. This paper presents a novel feed-forward technique that substantially improves the transient characteristics of the generating system by this technique both OFF and ON transient characteristics are achieved within the specifications of MIL-STD-704F envelop of normal voltage transient for 28V DC system. The transient levels are within the limits during the no-load to full-load conditions and vice versa. The Simulink models are developed with various filter designs under different controlling techniques to achieve faster transient response using the MATLAB/Simulink software.", "Keywords": "BLDC generator; MATLAB/Simulink; mathematical modeling; MIL-STD-704F; OFF transient and ON transient", "DOI": "10.1016/j.ifacol.2024.05.010", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ajinky<PERSON><PERSON>", "Affiliation": "Combat Vehicles Research and Development Establishment, DRDO, AVADI, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Combat Vehicles Research and Development Establishment, DRDO, AVADI, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON> Rajaraman", "Affiliation": "Combat Vehicles Research and Development Establishment, DRDO, AVADI, Chennai, India"}, {"AuthorId": 4, "Name": "S Shamala", "Affiliation": "Combat Vehicles Research and Development Establishment, DRDO, AVADI, Chennai, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Combat Vehicles Research and Development Establishment, DRDO, AVADI, Chennai, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Combat Vehicles Research and Development Establishment, DRDO, AVADI, Chennai, India"}], "References": []}, {"ArticleId": 115494320, "Title": "Coordinated torque control for enhanced steering and stability of independently driven mobile robots", "Abstract": "Purpose \nMobile robots with independent wheel control face challenges in steering precision, motion stability and robustness across various wheel and steering system types. This paper aims to propose a coordinated torque distribution control approach that compensates for tracking deviations using the longitudinal moment generated by active steering.\n \n Design/methodology/approach \nBuilding upon a two-degree-of-freedom robot model, an adaptive robust controller is used to compute the total longitudinal moment, while the robot actuator is regulated based on the difference between autonomous steering and the longitudinal moment. An adaptive robust control scheme is developed to achieve accurate and stable generation of the desired total moment value. Furthermore, quadratic programming is used for torque allocation, optimizing maneuverability and tracking precision by considering the robot’s dynamic model, tire load rate and maximum motor torque output.\n \n Findings \nComparative evaluations with autonomous steering Ackermann speed control and the average torque method validate the superior performance of the proposed control strategy, demonstrating improved tracking accuracy and robot stability under diverse driving conditions.\n \n Research limitations/implications \nWhen designing adaptive algorithms, using models with higher degrees of freedom can enhance accuracy. Furthermore, incorporating additional objective functions in moment distribution can be explored to enhance adaptability, particularly in extreme environments.\n \n Originality/value \nBy combining this method with the path-tracking algorithm, the robot’s structural path-tracking capabilities and ability to navigate a variety of difficult terrains can be optimized and improved.", "Keywords": "Coordinated torque control;Adaptive robust controller;Tracking deviation compensation;Robot stability;Longitudinal moment", "DOI": "10.1108/IR-12-2023-0344", "PubYear": 2024, "Volume": "51", "Issue": "4", "JournalId": 4481, "JournalTitle": "Industrial Robot: An International Journal", "ISSN": "0143-991X", "EISSN": "1758-5791", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology , Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Technology , Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology , Beijing, China"}], "References": [{"Title": "Vehicle direct yaw moment control system based on the improved linear quadratic regulator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "3", "Page": "378", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Fuzzy adaptive sliding mode controller for electrically driven wheeled mobile robot for trajectory tracking task", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "71", "JournalTitle": "Journal of Control and Decision"}, {"Title": "Spatial positioning robotic system for autonomous inspection of LPG tanks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "1", "Page": "70", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Design and locomotion analysis of a close-chain leg-wheel mobile platform", "Authors": "Xiang<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "1", "Page": "122", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "The first half century of industrial robot: 50 years of robotic developments", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "1", "Page": "1", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Reconfigurable wheel-crawler-integrated walking mechanism design and kinetic analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "4", "Page": "633", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Adaptive Control for Simultaneous Tracking and Stabilization of Wheeled Mobile Robot with Uncertainties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "108", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "AGV robot for laser-SLAM based method testing in automated container terminal", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "6", "Page": "969", "JournalTitle": "Industrial Robot: An International Journal"}]}, {"ArticleId": 115494321, "Title": "Infrared Human Pose Estimation for Embedded System", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.145121", "PubYear": 2024, "Volume": "14", "Issue": "5", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "雨琪 侯", "Affiliation": ""}], "References": [{"Title": "TensorRT-Based Framework and Optimization Methodology for Deep Learning Inference on Jetson Boards", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}]}, {"ArticleId": 115494329, "Title": "Improving few-shot named entity recognition via Semantics induced Optimal Transport", "Abstract": "Named entity recognition (NER) is to identify and categorize entities in unstructured text, which serves as a fundamental task for a variety of natural language processing (NLP) applications. In particular, emerging few-shot NER methods aim to learn model parameters well with few samples and have received considerable attention. The dominant few-shot NER methods usually employ pre-trained language models (PLMs) as their basic architecture and fine-tune model parameters with few NER samples. Since the sample size is small and there are a large number of parameters in PLMs, fine-tuning may result in the parameters of PLMs being highly biased. To address this issue, this study introduces the semantic distribution distance constraints to optimize the fine-tuning process of few-shot NER models and develops a framework named Semantic Constraints on few-shot Named Entity Recognition (SCNER). Specifically, the framework formulates the general knowledge transfer of PLMs as an optimal transport procedure with a semantic prior. And, a Semantics-induced Optimal Transport (SOT) regularizer is developed to utilize the importance and similarities of tokens within sentences. SOT builds the semantic distribution of the sentence and defines the transport costs between tokens to achieve the token-level optimal transport procedures. Finally, SOT is employed as a regularization term of few-shot NER to introduce the semantic distribution distance constraint for effectively transferring general knowledge from PLMs. The experiments on four public datasets demonstrate that the proposed method significantly improves the performance of NER models in both few-shot and fully supervised scenarios. SCNER is a common framework that can be applied to a variety of models without adding additional learning parameters, and can be used to enhance the generalization ability and adaptability of various few-shot NER models.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127938", "PubYear": 2024, "Volume": "597", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan 430078, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Biogeology and Environmental Geology, China University of Geosciences, Wuhan 430078, China;School of Computer Science, China University of Geosciences, Wuhan 430078, China;Hubei Key Laboratory of Intelligent Geo-Information Processing, China University of Geosciences, Wuhan 430078, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan 430078, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Biogeology and Environmental Geology, China University of Geosciences, Wuhan 430078, China;School of Computer Science, China University of Geosciences, Wuhan 430078, China;Hubei Key Laboratory of Intelligent Geo-Information Processing, China University of Geosciences, Wuhan 430078, China;Corresponding author at: School of Computer Science, China University of Geosciences, Wuhan 430078, China"}], "References": [{"Title": "Decomposed Two-Stage Prompt Learning for Few-Shot Named Entity Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "262", "JournalTitle": "Information"}, {"Title": "<PERSON><PERSON><PERSON><PERSON> named entity recognition by using contextualized word embeddings", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "229", "Issue": "", "Page": "120489", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Naming entity recognition of citrus pests and diseases based on the BERT-BiLSTM-CRF model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "121103", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 115494462, "Title": "Improving electricity demand forecasting accuracy: a novel grey-genetic programming approach using GMC(1,N) and residual sign estimation", "Abstract": "Purpose This paper addresses the challenges associated with forecasting electricity consumption using limited data without making prior assumptions on normality. The study aims to enhance the predictive performance of grey models by proposing a novel grey multivariate convolution model incorporating residual modification and residual genetic programming sign estimation. Design/methodology/approach The research begins by constructing a novel grey multivariate convolution model and demonstrates the utilization of genetic programming to enhance prediction accuracy by exploiting the signs of forecast residuals. Various statistical criteria are employed to assess the predictive performance of the proposed model. The validation process involves applying the model to real datasets spanning from 2001 to 2019 for forecasting annual electricity consumption in Cameroon. Findings The novel hybrid model outperforms both grey and non-grey models in forecasting annual electricity consumption. The model's performance is evaluated using MAE, MSD, RMSE, and R 2 , yielding values of 0.014, 101.01, 10.05, and 99% respectively. Results from validation cases and real-world scenarios demonstrate the feasibility and effectiveness of the proposed model. The combination of genetic programming and grey convolution model offers a significant improvement over competing models. Notably, the dynamic adaptability of genetic programming enhances the model's accuracy by mimicking expert systems' knowledge and decision-making, allowing for the identification of subtle changes in electricity demand patterns. Originality/value This paper introduces a novel grey multivariate convolution model that incorporates residual modification and genetic programming sign estimation. The application of genetic programming to enhance prediction accuracy by leveraging forecast residuals represents a unique approach. The study showcases the superiority of the proposed model over existing grey and non-grey models, emphasizing its adaptability and expert-like ability to learn and refine forecasting rules dynamically. The potential extension of the model to other forecasting fields is also highlighted, indicating its versatility and applicability beyond electricity consumption prediction in Cameroon.", "Keywords": "Hybrid forecasting;Residual modification;GP sign estimation;Electricity demand;Cameroon", "DOI": "10.1108/GS-01-2024-0011", "PubYear": 2024, "Volume": "14", "Issue": "4", "JournalId": 12304, "JournalTitle": "Grey Systems: Theory and Application", "ISSN": "2043-9377", "EISSN": "2043-9385", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Technologies and Applied Science , University Institute of Technology , Douala, Cameroon"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Higher Institute of Transport, Logistics and Commerce , Ambam, Cameroon"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Agricultural Engineering , University of Tehran , Tehran, Cameroon"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Maths, Faculty of Science and Technology , Ziane <PERSON>ur University of Djelfa , Djelfa, Algeria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Technologies and Applied Sciences , Douala, Cameroon"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Sciences, Southwest Petroleum University , Chengdu, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Laboratory of Technologies and Applied Science , University Institute of Technology , University of Douala , Douala, Cameroon"}], "References": [{"Title": "Forecasting cocoa production of six major producers through ARIMA and grey models", "Authors": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "3", "Page": "434", "JournalTitle": "Grey Systems: Theory and Application"}, {"Title": "A novel structural adaptive discrete grey prediction model and its application in forecasting renewable energy generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "115761", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Review on Deep Learning with Focus on Deep Recurrent Neural Network for Electricity Forecasting in Residential Building", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "193", "Issue": "", "Page": "141", "JournalTitle": "Procedia Computer Science"}, {"Title": "Root mean square error or mean absolute error? Use their ratio as well", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "585", "Issue": "", "Page": "609", "JournalTitle": "Information Sciences"}, {"Title": "Petroleum products consumption forecasting based on a new structural auto-adaptive intelligent grey prediction model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117579", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Self-paced ARIMA for robust time series prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "269", "Issue": "", "Page": "110489", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Learning latent dynamics with a grey neural ODE prediction model and its application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "3", "Page": "488", "JournalTitle": "Grey Systems: Theory and Application"}, {"Title": "Application of a novel hybrid accumulation grey model to forecast total energy consumption of Southwest Provinces in China", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "4", "Page": "629", "JournalTitle": "Grey Systems: Theory and Application"}, {"Title": "Surrogate model for memetic genetic programming with application to the one machine scheduling problem with time-varying capacity", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "120916", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel multivariate time-lag discrete grey model based on action time and intensities for predicting the productions in food industry", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121627", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel time-delay neural grey model and its applications", "Authors": "<PERSON><PERSON> Le<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121673", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An optimal wavelet transform grey multivariate convolution model to forecast electricity demand: a novel approach", "Authors": "Flav<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "14", "Issue": "2", "Page": "233", "JournalTitle": "Grey Systems: Theory and Application"}]}, {"ArticleId": 115494612, "Title": "Empowering Diagnosis: Cutting-Edge Segmentation and Classification in Lung Cancer Analysis", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.050204", "PubYear": 2024, "Volume": "79", "Issue": "3", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "HRDEL: High ranking deep ensemble learning-based lung cancer diagnosis model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118956", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Developing lung cancer post-diagnosis system using pervasive data analytic framework", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "105", "Issue": "", "Page": "108528", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Classification Of Lung Cancer Using Lightweight Deep Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON> <PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "1869", "JournalTitle": "Procedia Computer Science"}, {"Title": "Lung nodule detection of CT images based on combining 3D-CNN and squeeze-and-excitation networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "17", "Page": "25747", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Classification of computerized tomography images to diagnose non-small cell lung cancer using a hybrid model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "21", "Page": "33379", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Automatic classification of pulmonary nodules in computed tomography images using pre-trained networks and bag of features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "27", "Page": "42977", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 115494629, "Title": "Factors that influence adoption intentions toward smart city services among users", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10799-024-00429-y", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 20680, "JournalTitle": "Information Technology and Management", "ISSN": "1385-951X", "EISSN": "1573-7667", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Exploring the conditions of success in e-libraries in the higher education context through the lens of the social learning theory", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "4", "Page": "103208", "JournalTitle": "Information & Management"}, {"Title": "Medical practitioner's adoption of intelligent clinical diagnostic decision support systems: A mixed-methods study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "7", "Page": "103524", "JournalTitle": "Information & Management"}]}, {"ArticleId": 115494635, "Title": "Design of QCA Based Memory Cell Using a Novel Majority Voter with Physical Validation", "Abstract": "Quantum Dot Cellular Automata (QCA) is a unique transistor less paradigm which effectively uses change in cell polarization to perform logical operations with high speed, low power and high intricacy. In recent years, the need of high performance memory cell is increased for improving the system performance. This paper presents the design of a QCA based memory cell with read write capabilities. In recent past, most of the QCA circuits are designed using the conventional three input majority voter. The conventional three input majority gate is not fault tolerant. Thus, we need an alternative design which can serve as the majority voter and also shows the fault tolerance. Moreover, the design of three input majority voter is not much addressed . In this paper, an alternative, simple structure of three input majority voter is presented which is better than the conventional one in terms of fault tolerance. In addition to this, the proposed three input majority voter is power aware and efficient to realize various digital circuits. The correctness of the proposed majority voter is validated through the physical proof. Moreover, the proposed gate is subjected to cell displacement defect to investigate the testability. The proposed gate is further used to implement rudimentary elements such as XOR gate, multiplexer and D latch. Finally, the design of Random Access Memory (RAM) cell with read, write, set and reset capabilities is proposed using the presented majority voter. The proposed circuits are further subjected to comprehensive analyses for estimation of cost functions and energy dissipation. The investigation of presented circuits manifests the use of proposed majority voter for next generation computing circuits.", "Keywords": "", "DOI": "10.1016/j.nancom.2024.100513", "PubYear": 2024, "Volume": "41", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SRM Institute of Science and Technology, Delhi-NCR Campus, Ghaziabad, U.P., India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, School of Electronics and Communication Engineering, IIMT University, Meerut, UP, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information and Communication Technology, Mawlana Bhashani Science and Technology University, Santosh, Tangail, Bangladesh"}], "References": [{"Title": "An Ultra-Low-Power Five-Input Majority Gate in Quantum-Dot Cellular Automata", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "11", "Page": "2050176", "JournalTitle": "Journal of Circuits, Systems and Computers"}]}, {"ArticleId": 115494835, "Title": "Artificial Potential Fields based Formation Control for Fixed Wing UAVs with Obstacle Avoidance", "Abstract": "This paper introduces an approach for generating a trajectory for the formation flying of unmanned aerial vehicles while avoiding obstacles. The UAV formation is established using an improved artificial field approach where the member UAVs fly around a sphere with a predefined radius. A virtual spherical structured formation is generated with a virtual leader UAV at the center of the sphere which can track a mobile target with minimum tracking error. The member UAVs follow the virtual leader maintaining the spherical formation, with the help of attractive and repulsive potential fields which are created using artificial potential field functions. The formation achieves obstacle avoidance with the help of the rotational potential field concept, thereby avoiding the chance of formation getting stuck at the local minimum. A control law is then generated that fuses the formational control forces with obstacle avoidance force so that the formation can traverse around an obstacle without colliding with each other. The effectiveness and utility of the proposed approach are substantiated using extensive numerical simulations.", "Keywords": "Unmanned aerial vehicles; formation flight; artificial potential field; collision avoidance; obstacle avoidance", "DOI": "10.1016/j.ifacol.2024.05.004", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, IIT Bombay, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, IIT Bombay, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, IIT Bombay, India"}], "References": [{"Title": "Formation Control Algorithms for Multiple-UAVs: A Comprehensive Survey", "Authors": "Hai Do; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "27", "Page": "170230", "JournalTitle": "EAI Endorsed Transactions on Industrial Networks and Intelligent Systems"}]}, {"ArticleId": 115494841, "Title": "Improving robustness with image filtering", "Abstract": "Adversarial robustness is one of the most challenging problems in Deep Learning and Computer Vision research. State-of-the-art techniques to enforce robustness are based on Adversarial Training, a computationally costly optimization procedure. For this reason, many alternative solutions have been proposed, but none proved effective under stronger or adaptive attacks. This paper presents Image-Graph Extractor (IGE), a new image filtering scheme that extracts the fundamental nodes of an image and their connections through a graph structure. By utilizing the IGE representation, we have developed a new defense technique, Filtering as a Defense, which prevents attackers from creating malicious patterns that can deceive image classifiers. Moreover, we show that data augmentation with filtered images effectively improves the model’s robustness to data corruptions. We validate our techniques on Convolutional Neural Networks on CIFAR-10, CIFAR-100, and ImageNet.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127927", "PubYear": 2024, "Volume": "596", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering, University of Padua, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Human Inspired Technology Research Centre, University of Padua, Italy;Department of Information Engineering, University of Padua, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Human Inspired Technology Research Centre, University of Padua, Italy;Department of Information Engineering, University of Padua, Italy"}], "References": []}, {"ArticleId": 115495148, "Title": "Unveiling and mitigating bias in ride-hailing pricing for equitable policy making", "Abstract": "Ride-hailing services have skyrocketed in popularity due to their convenience. However, recent research has shown that their pricing strategies can have a disparate impact on some riders, such as those living in disadvantaged neighborhoods with a greater share of residents of color or residents below the poverty line. Analyzing real-world data, we additionally show that these communities tend to be more dependent on ride-hailing services (e.g., for work commutes) due to a lack of adequate public transportation infrastructure. To this end, we present the first thorough study on fair pricing for ride-hailing services by first devising applicable fairness measures to quantify this bias and then proposing novel fair pricing mechanisms to alleviate this bias. We present two pricing mechanisms to provide flexibility and account for different platform needs. By taking affordability into account and potentially providing discounts that may be government-subsidized, our approaches result in an increased number and more affordable rides for the disadvantaged community. Experiments on real-world Chicago ride-hailing data demonstrate worse scores for the proposed fairness metrics for rides corresponding to disadvantaged neighborhoods than those of a control group (random mix of neighborhoods). Subsequently, the results show that our fair pricing mechanisms eliminate this inequality gap. Our mechanisms provide a basis for the government and the ride-hailing platforms to implement fair ride-hailing policies.", "Keywords": "", "DOI": "10.1007/s43681-024-00498-3", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 79924, "JournalTitle": "AI and Ethics", "ISSN": "2730-5953", "EISSN": "2730-5961", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Advanced social media sentiment analysis for short‐term cryptocurrency price prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "", "JournalTitle": "Expert Systems"}, {"Title": "A Survey on Bias and Fairness in Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep Reinforcement Learning-based Trajectory Pricing on Ride-hailing Platforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 115495156, "Title": "Introduction to the special section on recent advances in multimedia forensics for cyber security and data tampering (VSI-forens)", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.compeleceng.2024.109348", "PubYear": 2024, "Volume": "118", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "Dr. <PERSON><PERSON><PERSON>", "Affiliation": "Amrita School of Computing, Amrita Vishwa Vidyapeetham, Amaravati, Andhra Pradesh, 522503, India;Corresponding author"}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, National University of Sciences and Technology (NUST), Islamabad 44000, Pakistan"}], "References": []}, {"ArticleId": 115495249, "Title": "Pure reaction automata", "Abstract": "This work introduces the new class of pure reaction automata, as well as a new update manner, called maximal reactive manner, that can also be applied to standard reaction automata. Pure reaction automata differ from the standard model in that they don’t have permanence: the entities that are not consumed by the reactions happening at a certain state are not conserved in the result states. We prove that the set of languages accepted by the new class under the maximal reactive manner contains the set of languages accepted by standard reaction automata under the same manner or under the maximal parallel manner. We also prove that a strict subclass of pure reaction automata can compute any partial recursive function.", "Keywords": "Reaction systems; Reaction automata; Formal languages; Computability", "DOI": "10.1007/s11047-024-09980-7", "PubYear": 2024, "Volume": "23", "Issue": "2", "JournalId": 1251, "JournalTitle": "Natural Computing", "ISSN": "1567-7818", "EISSN": "1572-9796", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Trieste, Trieste, Italy; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Trieste, Trieste, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Université Côte d’Azur, CNRS, I3S, Nice, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Trieste, Trieste, Italy; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Trieste, Trieste, Italy"}], "References": [{"Title": "Simulation of reaction systems by the strictly minimal ones", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "162", "JournalTitle": "Journal of Membrane Computing"}, {"Title": "Facilitation in reaction systems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "149", "JournalTitle": "Journal of Membrane Computing"}, {"Title": "Theory of reaction automata: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "63", "JournalTitle": "Journal of Membrane Computing"}, {"Title": "Encoding Threshold Boolean Networks into Reaction Systems for the Analysis of Gene Regulatory Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "2", "Page": "205", "JournalTitle": "Fundamenta Informaticae"}, {"Title": "Chemical Reaction Regular Grammars", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "2", "Page": "659", "JournalTitle": "New Generation Computing"}, {"Title": "Evolvability of reaction systems and the invisibility theorem", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "924", "Issue": "", "Page": "17", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Fixed points and attractors of reactantless and inhibitorless reaction systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "984", "Issue": "", "Page": "114322", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 115495260, "Title": "Single-valued neutrosophic fuzzy Sombor numbers and their applications in trade flows between different countries via sea route", "Abstract": "<p>Numerous domains, including all branches of science and technology, database theory, data mining, neural networks, expert systems, cluster analysis, control theory, and image capture, have employed graph theory, which can be used to represent interactions among multiple individuals. An extension of fuzzy sets, specifically intuitionistic fuzzy sets, is the concept of single-valued neutrosophic sets. We extend to graphs the notion of single-valued neutrosophic sets, which is an instance of neutrosophic sets. Neutrosophic fuzzy graphs are generalizations of fuzzy graphs, intuitionistic fuzzy graphs, and interval-valued fuzzy graphs. When in the case of fuzzy or intuitionistic fuzzy graphs relation between atoms in problems is indeterminate, then the neutrosophic fuzzy graph serves as a potent tool for dealing with partial, ambiguous, and inconsistent information in the actual world. In single-valued neutrosophic fuzzy graphs, each vertex is allocated a triplet in which the first element represents membership value, second and third elements as indeterminacy and non-membership value, respectively. In this work, we generalize the conclusions relating crisp graphs and fuzzy graphs by applying graph theory to single-valued neutrosophic sets and exploring a novel type of graph structure known as single-valued neutrosophic graphs. Sombor numbers are an effective and efficient tool in describing the topology of the graph in a numerical value. The main aim of this article is to define the first six Sombor numbers for single-valued neutrosophic fuzzy graphs. Then, degree of vertices of different graph families is determined in single-valued neutrosophic fuzzy framework and calculate the six Sombor numbers for these fundamental graph families for single-valued neutrosophic fuzzy graphs. In the end, an application to show the trade flows between different countries via sea route is presented to prove that single-valued neutrosophic fuzzy graphs are more generalized and efficient. First, we calculated the six Sombor numbers for crisp graph and then in SVNS fuzzy framework. Illustrated cases are then provided to show the applicability, viability, efficacy, and benefits of the suggested ways.</p>", "Keywords": "Neutrosophic fuzzy graph; Single-valued neutrosophic fuzzy graphs; Application of fuzzy graph-theoretic parameters; Sea route", "DOI": "10.1007/s11227-024-06169-8", "PubYear": 2024, "Volume": "80", "Issue": "14", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Riphah International University, Lahore, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Riphah International University, Lahore, Pakistan; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Riphah International University, Lahore, Pakistan; Corresponding author."}, {"AuthorId": 4, "Name": "Bandar Almohsen", "Affiliation": "Department of Mathematics, College of Science, King Saud University, Riyadh, Saudi Arabia; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Sciences, Northumbria University, Newcastle, UK"}], "References": [{"Title": "Quantifying the spatial homogeneity of urban road networks via graph neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "3", "Page": "246", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Complex relationship graph abstraction for autonomous air combat collaboration: A learning and expert knowledge hybrid approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119285", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fuzzy topological indices with application to cybercrime problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "5", "Page": "967", "JournalTitle": "Granular Computing"}, {"Title": "Some new product operations of T-spherical fuzzy graphs and an application of T-spherical fuzzy graphs in MCGDM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "11", "Page": "13663", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 115495321, "Title": "Insilico Evaluation on Potential Mt-Sp1/Matriptase Inhibitors Data: DFT and Molecular Modelling Approaches", "Abstract": "<p>Nine heterocyclic compounds were investigated using density functional theory, molecular operating environment software, material studio, swissparam (Swiss drug design) software. In this work, the descriptors generated from the optimized compounds proved to be efficient and explain the level of reactivity of the investigated compound. The developed quantitative structure activity relationship (QSAR) model was predictive and reliable. Also, compound 9 proved to be capable of inhibiting Mt-Sp1/Matriptase (pdb id: 1eax) than other examined heterocyclic compounds. Target prediction analysis was carried out on the compound with highest binding affinity (Compound 9) and the results were reported.</p><p>© 2024 The Author(s).</p>", "Keywords": "DFT;Docking;GFA;Inhibitors;Mt-Sp1/Matriptase;Target prediction", "DOI": "10.1016/j.dib.2024.110565", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Industrial Chemistry Programme, Bowen University, PMB 284, Iwo, Osun State, Nigeria."}, {"AuthorId": 2, "Name": "Sunday Adewale Akintelu", "Affiliation": "Department of pure and Applied Chemistry, Ladoke Akintola University of Technology, PMB 4000, Ogbomoso, Oyo State, Nigeria."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Industrial Chemistry Programme, Bowen University, PMB 284, Iwo, Osun State, Nigeria."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Industrial Chemistry Programme, Bowen University, PMB 284, Iwo, Osun State, Nigeria."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Industrial Chemistry Programme, Bowen University, PMB 284, Iwo, Osun State, Nigeria."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Microbiology Programme, Bowen University, PMB 284 Iwo, Osun State, Nigeria."}], "References": []}, {"ArticleId": 115495336, "Title": "FedQV: Leveraging Quadratic Voting in Federated Learning", "Abstract": "<p>Federated Learning (FL) permits different parties to collaboratively train a global model without disclosing their respective local labels. A crucial step of FL, that of aggregating local models to produce the global one, shares many similarities with public decision-making, and elections in particular. In that context, a major weakness of FL, namely its vulnerability to poisoning attacks, can be interpreted as a consequence of the one person one vote (henceforth 1p1v) principle that underpins most contemporary aggregation rules. In this paper, we introduce FedQV, a novel aggregation algorithm built upon the quadratic voting scheme, recently proposed as a better alternative to 1p1v-based elections. Our theoretical analysis establishes that FedQV is a truthful mechanism in which bidding according to one's true valuation is a dominant strategy that achieves a convergence rate matching that of state-of-the-art methods. Furthermore, our empirical analysis using multiple real-world datasets validates the superior performance of FedQV against poisoning attacks. It also shows that combining FedQV with unequal voting \"budgets'' according to a reputation score increases its performance benefits even further. Finally, we show that FedQV can be easily combined with Byzantine-robust privacy-preserving mechanisms to enhance its robustness against both poisoning and privacy attacks.</p>", "Keywords": "", "DOI": "10.1145/3656006", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IMDEA Networks Institute & Universidad Carlos III of Madrid, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IMDEA Networks Institute, Madrid, Spain"}], "References": [{"Title": "Privacy-preserving Byzantine-robust federated learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "103561", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "SoK: Secure Aggregation Based on Cryptographic Schemes for Federated Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "1", "Page": "140", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}]}, {"ArticleId": 115495340, "Title": "Peptide-based Electrochemical Detection of Prostate cancer-derived exosomes Using a dual signal amplification strategy", "Abstract": "Prostate cancer-derived exosomes have important potential as biomarkers for diagnosis and treatment of prostate cancer. But such specific exosomes towards clinical application remains problematic due to their comparatively low concentration in relation to other constituents of blood. Additionally, the presence of particles in blood that share a similar size with exosomes adds to the complexity of their selective and sensitive detection. Consequently, the detection of exosomes derived from prostate cancer in intricate biological settings necessitates the implementation of highly sensitive and specific biosensors. Herein, we report an electrochemical biosensor for prostate cancer-derived exosomes detection, with two-level selectivity achieved through a sandwich structure involving specific peptides and two-level amplification utilizing the combination of biotin-streptavidin linkage and G-quadruplex hemin mimetic peroxidase to enhance the sensitivity. Evaluation of PSMA positive exosomes at various concentrations demonstrates a remarkable limit of detection as low as 26 particles/μL, as well as an excellent linear sensor response spanning from 1.0 × 10<sup>2</sup> to 1.0 × 10<sup>7</sup> particles/μL. Compared to the enzymatic biosensor, this biosensor proves more versatile without a label or enzyme, and may be more promising for clinical applications.", "Keywords": "Prostate cancer-derived exosomes; Peptide; Dual signal amplification strategy; Electrochemical biosensor", "DOI": "10.1016/j.snr.2024.100202", "PubYear": 2024, "Volume": "7", "Issue": "", "JournalId": 72042, "JournalTitle": "Sensors and Actuators Reports", "ISSN": "2666-0539", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Laboratory, The Second Hospital of Nanjing, Nanjing University of Chinese Medicine, Nanjing 210003, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Laboratory, Gaochun People's Hospital, Nanjing 211300, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Laboratory, The Second Hospital of Nanjing, Nanjing University of Chinese Medicine, Nanjing 210003, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Laboratory, The Second Hospital of Nanjing, Nanjing University of Chinese Medicine, Nanjing 210003, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Laboratory, The Second Hospital of Nanjing, Nanjing University of Chinese Medicine, Nanjing 210003, China"}], "References": []}, {"ArticleId": 115495391, "Title": "A Novel Sparse-Based Approach for Joint Radio Frequency Fingerprint and Channel Estimation", "Abstract": "Radio Frequency Fingerprint (RFF) plays a pivotal role in specific emitter identification (SEI) and enhancing the physical layer security of wireless networks. However, past research on RFF extraction has encountered significant challenges, including algorithm instability in the presence of multi-path channels, disparities between actual RFF and proposed models, and the need to compensate for receiver RFF. In this study, we introduce an innovative algorithm designed to simultaneously estimate the transmitter's RFF and the non-line-of-sight (NLOS) channel state in the presence of receivers’ antenna effect. Moreover, we introduce the transmitter antenna feeding current as a modified RFF, accounting for transimtter components such as power amplifier (PA) nonlinearity, in-phase quadrature (IQ) modulator imbalance. Our approach commences with an exploration of wave propagation theory fundamentals, followed by a transformation of the inverse scattering equations into linear equations for joint channel and RFF estimation. Due to the non-convex nature of the derived equation, we employ the sparse lift method to render the problem convex. Leveraging the sparsity of channel coefficients, we devise a compressive sensing linear solution, denoted as the sparse-based approach for joint (SBJ) RFF and channel estimator. Additionally, we propose a calibration method to mitigate the effect of the receiver antennas on the estimated RFF of the transmitter. Through simulation, we demonstrate a 10 dB enhancement in RFF estimation accuracy compared to existing methods. Furthermore, our results indicate that the SBJ algorithm remains robust when faced with the combined effect of transmitter components, whereas previous models exhibit diminished precision as additional component effects are considered.", "Keywords": "", "DOI": "10.1016/j.phycom.2024.102399", "PubYear": 2024, "Volume": "66", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PhD Candidate of Communication Engineering, Imam <PERSON> Comprehensive University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Amirkabir University of Technology (Tehran Polytechnic), Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Imam <PERSON> Comprehensive University, Tehran, Iran;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Imam <PERSON> Comprehensive University, Tehran, Iran"}], "References": [{"Title": "A Review of Radio Frequency Fingerprinting Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "222", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}, {"Title": "Radio frequency fingerprinting identification for Zigbee via lightweight CNN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "", "Page": "101250", "JournalTitle": "Physical Communication"}, {"Title": "A comprehensive survey on radio frequency (RF) fingerprinting: Traditional approaches, deep learning, and open challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "219", "Issue": "", "Page": "109455", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 115495438, "Title": "Teaching CCI in computer science and computer science education", "Abstract": "This paper elaborates on a series of Child–Computer Interaction courses conducted at three universities in Germany. It emphasizes the integration of hands-on activities and a significant project development component, allowing students to practically apply and enhance their CCI knowledge. The article details the course structure, students’ engagement, and the evolution of content, underlining the importance of experiential learning in CCI education. Additionally, it offers reflections on the outcomes and lessons learned, serving as a resource for university lecturers looking to incorporate similar courses in their curriculum.", "Keywords": "Child–Computer Interaction; Teaching; Higher education", "DOI": "10.1016/j.ijcci.2024.100657", "PubYear": 2024, "Volume": "41", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>ky Universität Oldenburg, Department of Computing Science, Computer Science Education, Ammerländer Heerstraße 114-118, Oldenburg, 26129, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>ky Universität Oldenburg, Department of Computing Science, Computer Science Education, Ammerländer Heerstraße 114-118, Oldenburg, 26129, Germany"}], "References": []}, {"ArticleId": 115495485, "Title": "Evaluating the barriers affecting cybersecurity behavior in the Metaverse using PLS-SEM and fuzzy sets (fsQCA)", "Abstract": "While offering novel user experiences, the Metaverse introduces complex cybersecurity challenges due to the sophisticated interaction of augmented reality (AR), virtual reality (VR), and web technologies. Addressing the barriers to cybersecurity behavior is essential to protect users against risks such as identity theft and loss of digital assets. Therefore, this research aims to investigate these barriers by developing a theoretical model that draws factors from the Technology Threat Avoidance Theory (TTAT) and considers variables such as privacy concerns, perceived risks, and response costs. The data were collected from 395 Metaverse users and were analyzed using the Partial Least Squares-Structural Equation Modeling (PLS-SEM) and fuzzy-set Qualitative Comparative Analysis (fsQCA). The PLS-SEM findings showed that perceived threats, privacy concerns, and response costs have a significant negative impact on cybersecurity behavior, while perceived risks have an insignificant negative influence. The fsQCA results revealed that there is not a single pathway leading to robust cybersecurity behavior. Instead, eight configurations that include the presence and absence of certain conditions can lead to this desirable outcome. The findings not only advance the academic conversation on Metaverse security but also offer actionable strategies for stakeholders to reinforce user protection in this dynamic virtual environment.", "Keywords": "", "DOI": "10.1016/j.chb.2024.108315", "PubYear": 2024, "Volume": "159", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "Mostafa Al-Emran", "Affiliation": "Faculty of Engineering & IT, The British University in Dubai, Dubai, United Arab Emirates;Institute of Informatics and Computing in Energy, Universiti Tenaga Nasional, Selangor, Malaysia;Department of Computer Techniques Engineering, Dijlah University College, Baghdad, Iraq;Corresponding author.: Faculty of Engineering & IT, The British University in Dubai, Dubai, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Informatics and Computing in Energy, Universiti Tenaga Nasional, Selangor, Malaysia;Department of Informatics, College of Computing & Informatics, Universiti Tenaga Nasional, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program in International Business Administration, I-Shou University, Kaohsiung, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "La Trobe Business School, La Trobe University, Melbourne, Victoria, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computing, Universiti Teknologi Malaysia, Jo<PERSON>, 81310, Malaysia;Computer Science Department, College of Computer Science and Mathematics, Tikrit University, Tikrit, 34001, Iraq"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Informatics and Computing in Energy, Universiti Tenaga Nasional, Selangor, Malaysia;Information and Communication Technology Research Group, Scientific Research Center, Al-Ayen University, Thi-Qar, Iraq"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics and Computing in Energy, Universiti Tenaga Nasional, Selangor, Malaysia"}], "References": [{"Title": "Design and Evaluation of an Augmented Reality Game for Cybersecurity Awareness (CybAR)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "121", "JournalTitle": "Information"}, {"Title": "Understanding cybersecurity behavioral habits: Insights from situational support", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "", "Page": "102710", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Evaluating the cyber security readiness of organizations and its influence on performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "102726", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Maladaptive behaviour in response to email phishing threats: The roles of rewards and response costs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "102278", "JournalTitle": "Computers & Security"}, {"Title": "The effects of antecedents and mediating factors on cybersecurity protection behavior", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "", "Page": "100165", "JournalTitle": "Computers in Human Behavior Reports"}, {"Title": "The mediating role of perceived risks and benefits when self-disclosing: A study of social media trust and FoMO", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "103071", "JournalTitle": "Computers & Security"}, {"Title": "A systematic threat analysis and defense strategies for the metaverse and extended reality systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "128", "Issue": "", "Page": "103127", "JournalTitle": "Computers & Security"}, {"Title": "Is the digital security act 2018 sufficient to avoid cyberbullying in Bangladesh? A quantitative study on young women from generation-z of Dhaka city", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "100289", "JournalTitle": "Computers in Human Behavior Reports"}, {"Title": "Metaverse Chronicles: A Bibliometric Analysis of Its Evolving Landscape", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "17", "Page": "4873", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Determinants of Intention to Use ChatGPT for Educational Purposes: Findings from PLS-SEM and fsQCA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "17", "Page": "4501", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "The mediating role of security anxiety in internet threat avoidance behavior", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "103429", "JournalTitle": "Computers & Security"}, {"Title": "Drivers and Barriers Affecting Metaverse Adoption: A Systematic Review, Theoretical Framework, and Avenues for Future Research", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "22", "Page": "7043", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 115495506, "Title": "A non-uniform low-light image enhancement method with multi-scale attention transformer and luminance consistency loss", "Abstract": "<p>Low-light image enhancement aims to improve the perception of images collected in dim environments and provide high-quality data support for image recognition tasks. When dealing with photographs captured under non-uniform illumination, existing methods cannot adaptively extract the differentiated luminance information, which will easily cause overexposure and underexposure. From the perspective of unsupervised learning, we propose a multi-scale attention Transformer named MSATr, which sufficiently extracts local and global features for light balance to improve the visual quality. Specifically, we present a multi-scale window division scheme, which uses exponential sequences to adjust the window size of each layer. Within different-sized windows, the self-attention computation can be refined, ensuring the pixel-level feature processing capability of the model. For feature interaction across windows, a global transformer branch is constructed to provide comprehensive brightness perception and alleviate exposure problems. Furthermore, we propose a loop training strategy, in which diverse images generated by weighted mixing and a luminance consistency loss are used to effectively improve the model’s generalization ability. Extensive experiments on several benchmark datasets quantitatively and qualitatively prove that our MSATr is superior to state-of-the-art low-light image enhancement methods. The enhanced images have more natural brightness and outstanding details. The code is released at https://github.com/fang001021/MSATr . </p>", "Keywords": "Low-light image enhancement; Non-uniform illumination; Unsupervised learning; Multi-scale attention network; Consistency loop training; Luminance consistency loss", "DOI": "10.1007/s00371-024-03452-w", "PubYear": 2025, "Volume": "41", "Issue": "3", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China; Corresponding author."}, {"AuthorId": 3, "Name": "Baofeng Li", "Affiliation": "China Electric Power Research Institute Company Limited, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "China Electric Power Research Institute Company Limited, Beijing, China; School of Electrical and Information Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "Yu <PERSON>", "Affiliation": "China Electric Power Research Institute Company Limited, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 7, "Name": "Jiansheng Lu", "Affiliation": "State Grid Shanxi Marketing Service Center, Taiyuan, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "State Grid Shanxi Marketing Service Center, Taiyuan, China"}], "References": [{"Title": "Multi-exposure high dynamic range imaging with informative content enhanced network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "386", "Issue": "", "Page": "147", "JournalTitle": "Neurocomputing"}, {"Title": "Lightweight image super-resolution with enhanced CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106235", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "GPSD: generative parking spot detection using multi-clue recovery model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "9-11", "Page": "2657", "JournalTitle": "The Visual Computer"}, {"Title": "LE-GAN: Unsupervised low-light image enhancement network using attention module and identity invariant loss", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "240", "Issue": "", "Page": "108010", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Detecting color boundaries on 3D surfaces by applying edge‐detection image filters on a quad‐remeshing", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "2", "Page": "e2051", "JournalTitle": "Computer Animation and Virtual Worlds"}, {"Title": "Low-light Image Enhancement via Breaking Down the Darkness", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "1", "Page": "48", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "LACN: A lightweight attention-guided ConvNeXt network for low-light image enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105632", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Fusing surveillance videos and three‐dimensional scene: A mixed reality system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "1", "Page": "e2129", "JournalTitle": "Computer Animation and Virtual Worlds"}, {"Title": "Enhanced image classification using edge CNN (E-CNN)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "1", "Page": "319", "JournalTitle": "The Visual Computer"}, {"Title": "Trajectory‐BERT: Pre‐training and fine‐tuning bidirectional transformers for crowd trajectory enhancement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "3-4", "Page": "e2190", "JournalTitle": "Computer Animation and Virtual Worlds"}]}, {"ArticleId": 115495561, "Title": "Decoding Characteristics of Key Physical Properties in Silver Nanoparticles by Attaining Centroids for Cytotoxicity Prediction through Data Cleansing", "Abstract": "<p>This research underscores the profound impact of data cleansing, ensuring dataset integrity and providing a structured foundation for unravelling unraveling convoluted connections between diverse physical properties and cytotoxicity. As the scientific community delves deeper into this interplay, it becomes clear that precise data purification is a fundamental aspect of investigating parameters within datasets. The study presents the need for data filtration in the background of Artificial Intelligence and Machine Learning (AI/ (ML) that has widened its horizon into the field of biological application through the amalgamation of predictive systems and algorithms that delve into the intricate characteristics of cytotoxicity of nanoparticles. The reliability and accuracy of models in the ML AI/ML landscape hinge on the quality of input data, making data cleansing a critical component of the pre-processing pipeline. The main encounter faced here is the lengthy, broad and complex datasets that has have to be toned down for further studies. Through a thorough data cleansing process, this study addresses the complexities arising from diverse sources, resulting in a refined dataset. The filtration process employs K-meansK-means clustering to derive centroids, revealing the correlation between the physical properties of nanoparticles, viz, concentration, zeta potential, hydrodynamic diameter, morphology, and absorbance andwavelength, and cytotoxicity outcomes measured in terms of cell viability. The cell lines considered for determining the centroid values that predicts the cytotoxicity of silver nanoparticles are human and animal cell lines which were categorized as normal and carcinoma type. The objective of the study is to simplify the high-dimensional data for accurate analysis of the parameters that affect the cytotoxicity of silver NPs through finding out the centroids. &#xD;</p>", "Keywords": "", "DOI": "10.1088/2632-2153/ad51cb", "PubYear": 2024, "Volume": "5", "Issue": "2", "JournalId": 72803, "JournalTitle": "Machine Learning: Science and Technology", "ISSN": "", "EISSN": "2632-2153", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Condensed Silhouette: An Optimized Filtering Process for Cluster Selection in K-Means", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "205", "JournalTitle": "Procedia Computer Science"}, {"Title": "Contrastive analysis for scatterplot-based representations of dimensionality reduction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "46", "JournalTitle": "Computers & Graphics"}, {"Title": "K-means clustering algorithms: A comprehensive review, variants analysis, and advances in the era of big data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "178", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 115495566, "Title": "An Integrated and Fully Automated Test Solution for Automotive Cameras of Ultrawide Angle of View", "Abstract": "Vehicle-borne cameras vary greatly in imaging properties, e.g., angle of view, working distance and pixel count, to meet the diverse requirements of various applications. In addition, auto parts must tolerate dramatic variations in ambient temperature. These pose considerable challenges to the automotive industry when it comes to the evaluation of automotive cameras in terms of imaging performance. In this paper, an integrated and fully automated system, developed specifically to address these issues, is described. The key components include a collimator unit incorporating a LED light source and a transmissive test target, a mechanical structure that holds and moves the collimator and the camera under test, and a software suite that communicates with the controllers and computes the images captured by the camera. With the multifunctional system, imaging performance of cameras can be conveniently measured at a high degree of accuracy, precision and compatibility. The results are consistent with those obtained from tests conducted with conventional methods. Preliminary results demonstrate the potential of the system in terms of functionality and flexibility with continuing development.", "Keywords": "automated testing;automotive camera;collimator;image quality;slanted edge;spatial frequency response;through focus;ultrawide angle of view", "DOI": "10.2352/EI.2024.36.17.AVM-110", "PubYear": 2024, "Volume": "36", "Issue": "17", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115495727, "Title": "Decentralized medical image classification system using dual-input CNN enhanced by spatial attention and heuristic support", "Abstract": "The Internet of Medical Things (IoMT) enables the construction of expert systems that can use shared resources or data. In this work, we present a new IoMT operation model based on decentralized federated learning, which has been extended with a blockchain module. To ensure the security of medical data, we are introducing local and global blockchains with committee consensus mechanisms. Moreover, for data classification, we propose the use of a dual-input network with a spatial attention module which allows for feature fusion of processed images. The processed images are the original sample and one modified by a heuristic algorithm to extract global features in the form of superpixels. The use of this network model allows for the extraction of other features from the image in separate branches, which are processed by the attention module and subsequent fusion. The proposed system model solution with a newly modeled network was analyzed and tested on publicly available databases to verify its operation. Based on the conducted experiments, the effectiveness of the proposal in comparison to state-of-art is significant and the classifier reached 0.9862 of accuracy using a decentralized proposed approach. Moreover, the region of superpixel can be a valuable asset to image processing tools.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124343", "PubYear": 2024, "Volume": "253", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Applied Mathematics, Silesian University of Technology, Kaszubska 23, 44-100 Gliwice, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Applied Mathematics, Silesian University of Technology, Kaszubska 23, 44-100 Gliwice, Poland"}], "References": [{"Title": "Dragonfly algorithm: a comprehensive review and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "21", "Page": "16625", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Red fox optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114107", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A conceptual and practical comparison of PSO-style optimization algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114430", "JournalTitle": "Expert Systems with Applications"}, {"Title": "CoVNet-19: A Deep Learning model for the detection and analysis of COVID-19 patients", "Authors": "<PERSON><PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "107184", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multimodal medical image fusion based on joint bilateral filter and local gradient energy", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "569", "Issue": "", "Page": "302", "JournalTitle": "Information Sciences"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fairness, integrity, and privacy in a scalable blockchain-based federated learning system", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "108621", "JournalTitle": "Computer Networks"}, {"Title": "Federated Learning for Smart Healthcare: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; Pubudu <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Snake Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108320", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "BSM-ether: Bribery selfish mining in blockchain-based healthcare systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "601", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "Federated learning based on Stackelberg game in unmanned-aerial-vehicle-enabled mobile edge computing", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "235", "Issue": "", "Page": "121023", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A blockchain-enabled privacy-preserving authentication management protocol for Internet of Medical Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121329", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fedcs: Efficient communication scheduling in decentralized federated learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "102", "Issue": "", "Page": "102028", "JournalTitle": "Information Fusion"}, {"Title": "Backdoor attacks and defenses in federated learning: Survey, challenges and future research directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "127", "Issue": "", "Page": "107166", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Federated Deep Learning for Wireless Capsule Endoscopy Analysis: Enabling Collaboration Across Multiple Data Centers for Robust Learning of Diverse Pathologies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "361", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Personalized federated learning-based intrusion detection system: Poisoning attack and defense", "Authors": "<PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "182", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Cov-Fed: Federated learning-based framework for COVID-19 diagnosis using chest X-ray scans", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "128", "Issue": "", "Page": "107448", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Blockchain meets machine learning: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}]}, {"ArticleId": 115495765, "Title": "Fashion Finder : Ai-Powered Image Analysis and Online Shopping Integration App", "Abstract": "The proposed mobile application aims to revolutionize the fashion industry by providing users with a seamless way to discover and purchase clothing items through image recognition technology. The app allows users to either capture images of clothing items through their device's camera or upload images from their stored files. Leveraging machine learning and computer vision algorithms, the app identifies the corresponding clothing items within the images. Upon identification, the app interfaces with e-commerce platforms such as Amazon and Flipkart to search for similar items available for purchase. The development process involves extensive research, design, and implementation of features including image recognition, camera/file upload functionality, API integration with e-commerce platforms, user authentication, and additional user-friendly features. Through rigorous testing, deployment on major app stores, and continuous maintenance, the app aims to offer a seamless and engaging experience for fashion enthusiasts while adhering to user privacy and data security standards.", "Keywords": "Fashion;AI;Imaging;Object Detection", "DOI": "10.32628/CSEIT2410234", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " Dr. <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Associate Professor, <PERSON> College of Arts & Science, Peelamedu, Tamil Nadu, India"}, {"AuthorId": 2, "Name": " Prasannaa RT", "Affiliation": "UG Scholar, <PERSON> College of Arts & Science, Tamil Nadu, India"}, {"AuthorId": 3, "Name": " Prasannaa RT", "Affiliation": "UG Scholar, <PERSON> College of Arts & Science, Tamil Nadu, India"}], "References": [{"Title": "Detecting and tracking using 2D laser range finders and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "415", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Accurate fashion and accessories detection for mobile application based on deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "4", "Page": "4347", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "Humans as path-finders for mobile robots using teach-by-showing navigation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "8", "Page": "1255", "JournalTitle": "Autonomous Robots"}]}, {"ArticleId": 115495783, "Title": "Exploiting residual errors in nonlinear online prediction", "Abstract": "We introduce a novel online (or sequential) nonlinear prediction approach that incorporates the residuals, i.e., prediction errors in the past observations, as additional features for the current data. Including the past error terms in an online prediction algorithm naturally improves prediction performance significantly since this information is essential for an algorithm to adjust itself based on its past errors. These terms are well exploited in many linear statistical models such as ARMA, SES, and Holts-Winters models. However, the past error terms are rarely or in a certain sense not optimally exploited in nonlinear prediction models since training them requires complex nonlinear state-space modeling. To this end, for the first time in the literature, we introduce a nonlinear prediction framework that utilizes not only the current features but also the past error terms as additional features, thereby exploiting the residual state information in the error terms, i.e., the model’s performance on the past samples. Since the new feature vectors contain error terms that change with every update, our algorithm jointly optimizes the model parameters and the feature vectors simultaneously. We achieve this by introducing new update equations that handle the effects resulting from the changes in the feature vectors in an online manner. We use soft decision trees and neural networks as the nonlinear prediction algorithms since these are the most widely used methods in highly publicized competitions. However, as we show, our methods are generic and any algorithm supporting gradient calculations can be straightforwardly used. We show through our experiments on the well-known real-life competition datasets that our method significantly outperforms the state-of-the-art. We also provide the implementation of our approach including the source code to facilitate reproducibility ( https://github.com/ahmetberkerkoc/SDT-ARMA ).", "Keywords": "Online/sequential Regression/prediction; Residual errors; Soft Decision trees; Neural networks", "DOI": "10.1007/s10994-024-06554-7", "PubYear": 2024, "Volume": "113", "Issue": "9", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Bilkent University, Ankara, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "Ahmet B. Ko<PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Bilkent University, Ankara, Turkey"}, {"AuthorId": 3, "Name": "Suleyman S<PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Bilkent University, Ankara, Turkey"}], "References": [{"Title": "Explainable online ensemble of deep neural network pruning for time series forecasting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "111", "Issue": "9", "Page": "3459", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 115495827, "Title": "SADNet: Generating immersive virtual reality avatars by real‐time monocular pose estimation", "Abstract": "Generating immersive virtual reality avatars is a challenging task in VR/AR applications, which maps physical human body poses to avatars in virtual scenes for an immersive user experience. However, most existing work is time‐consuming and limited by datasets, which does not satisfy immersive and real‐time requirements of VR systems. In this paper, we aim to generate 3D real‐time virtual reality avatars based on a monocular camera to solve these problems. Specifically, we first design a self‐attention distillation network (SADNet) for effective human pose estimation, which is guided by a pre‐trained teacher. Secondly, we propose a lightweight pose mapping method for human avatars that utilizes the camera model to map 2D poses to 3D avatar keypoints, generating real‐time human avatars with pose consistency. Finally, we integrate our framework into a VR system, displaying generated 3D pose‐driven avatars on Helmet‐Mounted Display devices for an immersive user experience. We evaluate SADNet on two publicly available datasets. Experimental results show that SADNet achieves a state‐of‐the‐art trade‐off between speed and accuracy. In addition, we conducted a user experience study on the performance and immersion of virtual reality avatars. Results show that pose‐driven 3D human avatars generated by our method are smooth and attractive.", "Keywords": "3D avatar;computer animation;human pose estimation", "DOI": "10.1002/cav.2233", "PubYear": 2024, "Volume": "35", "Issue": "3", "JournalId": 17200, "JournalTitle": "Computer Animation and Virtual Worlds", "ISSN": "1546-4261", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Virtual Reality Technology and Systems Beihang University  Beijing China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Virtual Reality Technology and Systems Beihang University  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Virtual Reality Technology and Systems Beihang University  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Virtual Reality Technology and Systems Beihang University  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Virtual Reality Technology and Systems Beihang University  Beijing China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Virtual Reality Technology and Systems Beihang University  Beijing China;Zhongguancun Laboratory  Beijing China"}], "References": [{"Title": "Constraining dense hand surface tracking with elasticity", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Mirror world: creating digital twins of the space and persons from video streamings", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "9", "Page": "6689", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 115495841, "Title": "Pola Pembelian Konsumen Dengan Metode Market Basket Analysis pada Perishable Product di Toko Roti Ikobana Bakery", "Abstract": "<p>Industri makanan dan minuman berperan besar dalam ekonomi Indonesia. Roti sebagai salah satu produk makanan termasuk kategori fast mover consumer goods dan perishable product yang dijual cepat, harga relatif murah, namun cepat kadaluarsa dengan siklus hidup yang Terkhusus bagi Ikobana Bakery, yang memproduksi roti Jepang dengan tekstur lembut berbahan premium dan varian banyak perlu strategi pemasaran untuk meningkatkan penjualan. Penelitian ini bertujuan untuk mengetahui pola pembelian konsumen yang dibeli secara bersamaan baik saat promosi maupun penjualan reguler, sehingga diketahui produk yang paling sering dibeli dan akhirnya dapat memberikan strategi pemasaran berdasarkan perilaku pembelian konsumen. Metode yang digunakan adalah market basket analysis algoritma apriori menggunakan data transaksi dari Januari 2022 sampai dengan Agustus 2023, sejumlah 1000 data penjualan promosi dan 4593 data penjualan reguler. Bahasa pemrograman Python digunakan untuk mengolah dan menganalisis data. Dari penelitian ini dihasilkan 17 aturan asosiasi untuk penjualan promosi dengan minimum support 40% dan minimum confidence 60%, serta 11 aturan untuk penjualan reguler dengan minimum support 20% dan minimum confidence 55%. Roti sosis mayonaise menonjol sebagai produk penting dengan nilai consequent support tinggi yaitu sebesar 36,2% pada penjualan promosi dan 29,3% pada penjualan reguler. Pada produk rekomendasi yaitu roti melon pan terdapat perilaku konsumen yang menginginkan produk ini pada saat penjualan promosi 2 kali lipat lebih besar aturan yang terbentuk dibandingkan pada saat penjualan regular. Rekomendasi pemasaran termasuk strategi product bundling dan price bundling untuk meningkatkan penjualan.</p>", "Keywords": "Market Basket Analysis; Apriori; Perilaku Konsumen; Bakery", "DOI": "10.25077/TEKNOSI.v10i1.2024.82-91", "PubYear": 2024, "Volume": "10", "Issue": "1", "JournalId": 32016, "JournalTitle": "Jurnal Teknologi dan Sistem Informasi", "ISSN": "2460-3465", "EISSN": "2476-8812", "Authors": [{"AuthorId": 1, "Name": "Ardhian Agung <PERSON>", "Affiliation": "Program Studi Pendidikan <PERSON>, Sekolah Pasca Sarjana Universitas Andalas"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Program <PERSON><PERSON>, Fakultas Ekonomi dan Bisnis Universitas Dharma Andalas"}], "References": []}, {"ArticleId": 115496016, "Title": "Kalman Filter Innovation based Estimation for Detection of Unmodeled Dynamics of Radio Frequency Cavity", "Abstract": "Behavior of Radio Frequency (RF) cavity as a dynamical system has been well explained by its state space model especially in case of normal conducting cavity. In case of superconducting cavity, one needs to also model Time Varying Detuning (TVD) of cavity by a additional non-linear equations. The TVD forms one of parameter of system matrix of cavity state space model and is function of number of mechanical modes of cavities. Often one misses out or fails to model one or more of these modes. Given, that system designer has modeled system to maximum possible extent, there is a need to detect if any dynamics are unmodeled. Kalman filter (KF) and its many variants have been at forefront as estimators for states and parameters of system governed by dynamical equation in presence of uncertainty. It estimates states of system by running in parallel as a system with model similar to that of plant and with its inputs and outputs fetched from plant/system. Under such a situation, KF, while its operation, outputs an innovation sequence. If all dynamics of process are well captured by KF model, the innovation sequence is white noise and zero mean. Any model mismatch due to unmodeled or wrongly modeled dynamics are indicated by innovation sequence. In this work, authors attempt to detect such dynamics using superconducting RF cavity model as a plant in a case study. This paper must be viewed as a novel usage of KF. The proposed method may find potential use in field of dynamics detection or system identification. This paper describes RF cavity model and a discrete KF followed by a mechanism to detect unmodelled dynamics by modeling and simulations studies.", "Keywords": "Kalman; detection; model; dynamics; unmodelled", "DOI": "10.1016/j.ifacol.2024.05.065", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bhabha Atomic Research Centre, Trombay, Mumbai, 400085, India;Research Scholar, <PERSON><PERSON> Bhabha National Institute, Mumbai, 400094"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bhabha Atomic Research Centre, Trombay, Mumbai, 400085, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Mumbai, 400076, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Bhabha Atomic Research Centre, Trombay, Mumbai, 400085, India"}], "References": []}, {"ArticleId": 115496060, "Title": "Stability and Performance Analysis of Interfered Digital Systems with Generalized Overflow Nonlinearities", "Abstract": "This paper investigates the problem of local stability for fixed-point interfered digital filters with generalized overflow nonlinearities. First, a familiar form of overflow nonlinearity function, covering the nonlinearities like zeroing, saturation, two's complement, and triangular is established. Second, the asymptotic stability condition for the digital filters is formulated in local context without external interference. Third, with external interference the work is extended to investigate the local stability and to attain H ∞ performance of the digital filter using generalized nonlinearity function. Further, the conventional global stability results can be established as special cases of presented local approach. Finally, sufficient numerical examples are presented to highlight the merit of proposed approach.", "Keywords": "Asymptotic stability; External interference; Generalized overflow nonlinearity; Limit cycle oscillations; Local stability", "DOI": "10.1016/j.ifacol.2024.05.058", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Tiruchirappalli, Tiruchirappalli, Tamil Nadu, 620015, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Advanced Signal and Image Processing (ASIP) Lab, Department of Electronics and Communication Engineering, Indian Institute of Information Technology, Design and Manufacturing, Kancheepuram, Chennai, 600127, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Advanced Signal and Image Processing (ASIP) Lab, Department of Electronics and Communication Engineering, Indian Institute of Information Technology, Design and Manufacturing, Kancheepuram, Chennai, 600127, India"}], "References": [{"Title": "H<SUB align=\"right\">∞ model reduction of discrete-time 2D T-S fuzzy systems in finite frequency ranges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "22", "JournalTitle": "International Journal of Modelling, Identification and Control"}]}]