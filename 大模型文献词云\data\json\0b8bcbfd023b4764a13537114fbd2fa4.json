[{"ArticleId": 83109608, "Title": "Conforming and nonconforming laminated finite element Kirchhoff nanoplates in bending using strain gradient theory", "Abstract": "This paper presents a comprehensive numerical finite element implementation of the nonlocal strain gradient theory applied to thin laminated composite nanoplates using Kirchhoff theory (known as Classical Laminated Plate Theory or CLPT). Hermite interpolation functions are used to approximate membrane and bending degrees of freedom according to the conforming and nonconforming approaches. To the best of the authors’ knowledge, there is no finite element formulation in the literature able to deal with laminated Kirchhoff plates including the strain gradient theory, which allows to consider general stacking sequences and boundary conditions. A simple and effective matrix notation is employed to facilitate the computer implementation. Benchmarks reported prove the accuracy of the implementation. Novel applications are provided for further developments in the subject.", "Keywords": "Finite element method ; Laminates ; Nanostructures ; Nonlocal elastic theory ; Conforming and nonconforming formulations", "DOI": "10.1016/j.compstruc.2020.106322", "PubYear": 2020, "Volume": "239", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "DESD Department, University of San Marino, San Marino;DICAM Department, University of Bologna, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "DICAM Department, University of Bologna, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, University of Porto, Portugal"}], "References": []}, {"ArticleId": 83109705, "Title": "A general model for prediction of deformation from initial residual stress", "Abstract": "<p>This paper studied the influence of the initial residual stress on the dimensional stability of manufactured components. Although residual stresses are assumed to be in self-equilibrium within a solid that is not subjected to an external load, most manufactured products will be affected by external forces during service. Therefore, this study allowed for the combined effects of the initial residual stress and external forces on the excessive deformation of a prestressed body. On the basis of the virtual work principle, we developed a new analytical model for predicting the excessive deformation of a prestressed body under an applied load. The proposed model was validated by means of the finite difference method, and we made a comparison with an existing model from the literature, which was developed to predict the excessive deformation of a thin aluminum plates with the machining-induced residual stress. The computational results showed that the proposed method had 10% greater predictive accuracy than previous models available in the work.</p>", "Keywords": "Deformation; Residual stress; Analytical model; Finite difference method", "DOI": "10.1007/s00170-020-05683-2", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan, China"}], "References": []}, {"ArticleId": 83109713, "Title": "Dataset on blood flow and instantaneous wave-free ratio in normal and stenosed coronary arteries", "Abstract": "Instantaneous wave-free ratio (iFR) has been proposed as a hemodynamic parameter that can reliably reflect the blood flow in stenosed coronary arteries. Currently, there are few investigations on the quantitative analysis of iFR in the patients regarding the variation of microcirculatory resistance (MR). The data aim to provide geometric (cross-section area of branches) and hemodynamic (flow rate and iFR of branches) parameters of normal and stenosed coronary arteries derived from CFD simulation. The CFD simulation was performed on the three-dimensional artery models reconstructed from computed tomography (CT) images of four subjects. The hemodynamic parameters were obtained in six situations of MR to simulate coronary microvascular dysfunction (CMD). This dataset could be used as the reference to estimate the iFR and flow rate in patients with CMD and stenosis in coronary arteries. The geometric parameters could be used in the modelling of coronary arteries.", "Keywords": "Computational fluid dynamics (CFD);Coronary artery disease (CAD);Coronary microvascular dysfunction (CMD);Instantaneous wave-free ratio (iFR)", "DOI": "10.1016/j.dib.2020.106011", "PubYear": 2020, "Volume": "32", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Centre of Intelligent Healthcare, Faculty of Health and Life Science, Coventry University, Coventry CV1 5FB, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Ou", "Affiliation": "Department of Radiology, General Hospital of Southern Theater Command, PLA, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiology, Guangzhou First People's Hospital, Nansha Hospital, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Research Centre of Intelligent Healthcare, Faculty of Health and Life Science, Coventry University, Coventry CV1 5FB, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Zhejiang University, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, Zhejiang University, Hangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medicine and Therapeutics, The Chinese University of Hong Kong, Prince of Wales Hospital, Shatin, Hong Kong, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Medicine and Therapeutics, The Chinese University of Hong Kong, Prince of Wales Hospital, Shatin, Hong Kong, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Imaging and Interventional Radiology, The Chinese University of Hong Kong, Prince of Wales Hospital, Shatin, Hong Kong, China;Corresponding authors"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Centre of Intelligent Healthcare, Faculty of Health and Life Science, Coventry University, Coventry CV1 5FB, UK;Corresponding authors"}], "References": []}, {"ArticleId": 83109725, "Title": "Design and development of a friendly user interface for building social network traceability system", "Abstract": "<p>A social networking site (SNS) is a platform for building social networks or social relations with those who share similar profiles, career, interests, activities, backgrounds, etc. Social networking site serves for communication purposes to specific interest groups, but they do not have a searching option where we can search for individuals or groups with obvious features. A user can be found on social network only if we know the name of the person. With the help of this paper, we aim at presenting a searching method using natural language processing and query optimization and classification for searching people by name or any distinct characteristic. These concepts can be together used to constitute and process social network information so that searching user through their name or some distinguished characteristics is feasible. This paper handles some of the present limitations of accessing social network data by natural language processing technique. We have used PHP for programming and algorithm implementation and MySQL for storing sample data.</p>", "Keywords": "SNS; Natural language processing; Web technologies; MySQL; PHP", "DOI": "10.1007/s13278-020-00675-2", "PubYear": 2020, "Volume": "10", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON>na <PERSON>zad College of Engineering and Technology, Patna, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Birla Institute of Technology, Mesra, India"}], "References": [{"Title": "Development and evaluation of an emotional lexicon system for young children", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "1535", "JournalTitle": "Microsystem Technologies"}, {"Title": "Symmetric searchable encryption scheme that supports phrase search", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "1721", "JournalTitle": "Microsystem Technologies"}, {"Title": "Augmented reality system for tourism using image-based recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "1811", "JournalTitle": "Microsystem Technologies"}, {"Title": "Novel features for intensive human activity recognition based on wearable and smartphone sensors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "6", "Page": "1889", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 83109806, "Title": "Point-cloud avatars to improve spatial communication in immersive collaborative virtual environments", "Abstract": "<p>Collaborative virtual environments allow remote users to work together in a shared 3D space. To take advantage of the possibilities offered by such systems, their design must allow the users to interact and communicate efficiently. One open question in this field concerns the avatar fidelity of remote partners. This can impact communication between the remote users, more particularly when performing collaborative spatial tasks. In this paper, we present an experimental study comparing the effects of two partner’s avatars on collaboration during spatial tasks. The first avatar was based on a 2.5D streamed point-cloud and the second avatar was based on a 3D preconstructed avatar replicating the remote user movements. These avatars differ in their fidelity levels described through two components: visual and kinematic fidelity. The collaborative performance was evaluated through the efficacy of completing two spatial communication tasks, a pointing task and spatial guidance task. The results indicate that the streamed point-cloud avatar permitted a significant improvement of the collaborative performance for both tasks. The subjective evaluation suggests that these differences in performance can mainly be attributed to the higher kinematic fidelity of this representation as compared with the 3D preconstructed avatar representation. We conclude that, when designing spatial collaborative virtual environments, it is important to reach a high kinematic fidelity of the partner’s representation while a moderate visual fidelity of this representation can suffice.</p>", "Keywords": "Partner’s avatar; Kinematic fidelity; Collaborative virtual environments; Spatial communication; Immersive virtual reality", "DOI": "10.1007/s00779-020-01431-1", "PubYear": 2021, "Volume": "25", "Issue": "3", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IBISC Laboratory, Université d’Evry, Université Paris Saclay, Evry Cédex, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IBISC Laboratory, Université d’Evry, Université Paris Saclay, Evry Cédex, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IBISC Laboratory, Université d’Evry, Université Paris Saclay, Evry Cédex, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBISC Laboratory, Université d’Evry, Université Paris Saclay, Evry Cédex, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "LS2N, Institut Mines Telecom Atlantique, Nantes, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "IBISC Laboratory, Université d’Evry, Université Paris Saclay, Evry Cédex, France"}], "References": []}, {"ArticleId": 83109817, "Title": "Boring chatter identification by multi-sensor feature fusion and manifold learning", "Abstract": "<p>In the boring process, chatter is easy to occur because of the large overhang of the boring bar and the poor structural stiffness of the system. The key technique to reduce the chatter effect in boring process is to identify the chatter state accurately. In this paper, a method of chatter identification based on multi-sensor feature fusion and manifold learning is proposed. Displacement sensor, acceleration sensor, and acoustic pressure sensor are used to acquire processing signals synchronously, and then triple signals are decomposed by empirical mode decomposition (EMD). The multi-indicators are used to construct high-dimensional space, and then different manifold learning algorithms are used to reduce feature dimensionality. Support vector machine chatter identification models are obtained to verify the effect of boring chatter identification. Multi-sensor feature fusion realizes the complementary of different features and achieves better recognition results. The results show that the proposed method can identify boring chatter effectively. And the best result is obtained by the combination of the displacement sensor and acceleration sensor.</p>", "Keywords": "Boring; Chatter identification; Multi-sensor; Feature fusion; Empirical mode decomposition; Support vector machine", "DOI": "10.1007/s00170-020-05611-4", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, People’s Republic of China;Key Laboratory of Fundamental Science for Advanced Machining, Beijing Institute of Technology, Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, People’s Republic of China;Key Laboratory of Fundamental Science for Advanced Machining, Beijing Institute of Technology, Beijing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, People’s Republic of China"}], "References": []}, {"ArticleId": 83109893, "Title": "k-Mnv-Rep: A k-type clustering algorithm for matrix-object data", "Abstract": "In matrix-object data, an object (or a sample) is described by more than one feature vector (record) and all of those feature vectors are responsible for the observed classification of the object. A task for matrix-object data is to cluster it into a set of groups by analyzing and utilizing the information of feature vectors. Matrix-object data are widespread in many real applications. Previous studies typically address data sets that an object is generally represented by a feature vector, which may be violated in many real-world tasks. In this paper, we propose a k -multi-numeric-values-representatives (abbr. k -Mnv-Rep) algorithm to cluster numeric matrix-object data. In this algorithm, a new dissimilarity measure between two numeric matrix-objects is defined and a new heuristic method of updating cluster centers is given. Furthermore, we also propose a k -multi-values-representatives (abbr. k -Mv-Rep) algorithm to cluster hybrid matrix-object data. The two proposed algorithms break the limitations of the previous studies, and can be applied to address matrix-object data sets that exist widely in many real-world tasks. The benefits and effectiveness of the two algorithms are shown by some experiments on real and synthetic data sets.", "Keywords": "Matrix-object ; Clustering ; Numeric ; Hybrid", "DOI": "10.1016/j.ins.2020.06.071", "PubYear": 2021, "Volume": "542", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, Taiyuan 030006, China;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, Taiyuan 030006, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, University of Eastern Finland, Kuopio 70211, Finland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, Taiyuan 030006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, Taiyuan 030006, China"}], "References": []}, {"ArticleId": 83109913, "Title": "Learning robust affinity graph representation for multi-view clustering", "Abstract": "Recently, an increasingly pervasive trend in real-word applications is that the data are collected from multiple sources or represented by multiple views. Owing to the powerful ability of affinity graph in capturing the structural relationships among samples, constructing a robust and meaningful affinity graph has been extensively studied, especially in spectral clustering tasks. However, conventional spectral clustering extended to multi-view scenarios cannot obtain the satisfactory performance due to the presence of noise and the heterogeneity among different views. In this paper, we propose a robust affinity graph learning framework to deal with this issue. First, we employ an improved feature selection algorithm that integrates the advantages of hypergraph embedding and sparse regression to select significant features such that more robust graph Laplacian matrices for various views on this basis can be constructed. Second, we model hypergraph Laplacians as points on a Grassmann manifold and propose a Consistent Affinity Graph Learning (CAGL) algorithm to fuse all views. CAGL aims to learn a latent common affinity matrix shared by all Laplacian matrices by taking both the clustering quality evaluation criterion and the view consistency loss into account. We also develop an alternating descent algorithm to optimize the objective function of CAGL. Experiments on five publicly available datasets demonstrate that our proposed method obtains promising results compared with state-of-the-art methods.", "Keywords": "Multi-view clustering ; Feature selection ; Graph representation ; Grassmann manifold", "DOI": "10.1016/j.ins.2020.06.068", "PubYear": 2021, "Volume": "544", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 30072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 30072, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Li", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 30072, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong Univeristy, Shandong 250101, China"}], "References": []}, {"ArticleId": 83109928, "Title": "Double-level adversarial domain adaptation network for intelligent fault diagnosis", "Abstract": "Deep neural networks have been widely studied in the field of mechanical fault diagnosis with the rapidity of intelligent manufacturing and industrial big data, however, attractive performance gains usually come from a premise that source training data and target test data have the same distribution. Unfortunately, this assumption is generally untenable in practice due to changeable working conditions and complex industrial environment. To address this issue, a double-level adversarial domain adaptation network (DL-ADAN) is presented for cross-domain fault diagnosis, which is able to bridge the divergences between the source and target domains. Specifically, the proposed diagnostic framework is composed of a feature extractor based on deep convolutional network, a domain discriminator and two label classifiers, which conducts two minimax adversarial games. In the first adversarial stream, the feature extractor and domain discriminator game with each other to achieve domain-level alignment from a global perspective. On the other line, the extractor and two classifiers are against each other to conduct class-level alignment, in which <PERSON><PERSON><PERSON> discrepancy is used to detect outlier target samples. As a result, the extractor can learn transferable discriminative features for accurate fault diagnosis. Extensive diagnostic experiments are constructed for performance analysis and several state of the art diagnostic methods are selected for comparative study. The comprehensive results demonstrate the effectiveness and superiority of the proposed method.", "Keywords": "Domain adaptation ; Intelligent diagnosis ; Domain-level alignment ; Class-level alignment ; Machine", "DOI": "10.1016/j.knosys.2020.106236", "PubYear": 2020, "Volume": "205", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory for Manufacturing Systems Engineering, School of Mechanical Engineering, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beihang University, Beijing 100083, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Manufacturing Systems Engineering, School of Mechanical Engineering, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Manufacturing Systems Engineering, School of Mechanical Engineering, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, China"}], "References": [{"Title": "A renewable fusion fault diagnosis network for the variable speed conditions under unbalanced samples", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Xingxing Jiang", "PubYear": 2020, "Volume": "379", "Issue": "", "Page": "12", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 83109931, "Title": "Target recognition and network pharmacology for revealing anti-diabetes mechanisms of natural product", "Abstract": "Diabetes is a metabolic disease characterized by persistent hyperglycemia, which results in serious complications such as cardiovascular disorder, neuropathy, renal failure, and retinopathy. Various anti-diabetic drugs have been developed, however, their therapeutic effect is far from satisfactory. Therefore, increasing attention has been paid to natural products, whose multi-targets characteristics might contribute to treating polygenetic disease like diabetes. Indeed, valuable implications for anti-diabetic drug discovery can be obtained through exploring the molecular mechanism of these natural products, however, it could be a formidable task with traditional experimental approaches. As such, computational approaches are applied in predicting the compound-target interactions, including molecular docking, molecular dynamics (MD) simulations, and other computer aided drug design (CADD) methods. Furthermore, network pharmacology strategy is introduced for exploring natural products with anti-diabetic activities, including the general workflow of network pharmacology, its application in anti-diabetic traditional Chinese medicine formula research, and the application of natural products in diabetic complications. In particular, a promising prospective insight of combining computational methods and network pharmacology in anti-diabetic drug discovery is discussed. In summary, the combination of computational approaches and network pharmacology has already produced fruitful natural products with anti-diabetic effect, and will hopefully initiate novel anti-diabetic drug development with advanced therapeutic effect and less side effect.", "Keywords": "Diabetes ; Natural products ; Network pharmacology ; Computational approaches", "DOI": "10.1016/j.jocs.2020.101186", "PubYear": 2020, "Volume": "45", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Wuya College of Innovation, Shenyang Pharmaceutical University, Shenyang, Liaoning, 110016, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Structure-Based Drug Design & Discovery, Ministry of Education, Shenyang Pharmaceutical University, Shenyang, Liaoning, 110016, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wuya College of Innovation, Shenyang Pharmaceutical University, Shenyang, Liaoning, 110016, China;School of Pharmaceutical Engineering, Shenyang Pharmaceutical University, Shenyang, Liaoning, 110016, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Structure-Based Drug Design & Discovery, Ministry of Education, Shenyang Pharmaceutical University, Shenyang, Liaoning, 110016, China;School of Pharmaceutical Engineering, Shenyang Pharmaceutical University, Shenyang, Liaoning, 110016, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wuya College of Innovation, Shenyang Pharmaceutical University, Shenyang, Liaoning, 110016, China;Corresponding author at: Wuya College of Innovation, Shenyang Pharmaceutical University, Shenyang, 110016, China"}], "References": []}, {"ArticleId": 83109967, "Title": "A hierarchical approach for solving an integrated packing and sequence-optimization problem in production of glued laminated timber", "Abstract": "Abstract \nIntegrated packing and sequence-optimization problems appear in many industrial applications. As an example of this type of problem, we consider the production of glued laminated timber (glulam) in sawmills: Wood beams must be packed into a sequence of pressing steps subject to packing constraints of the press and subject to sequencing constraints. In this paper, we present a three-stage approach for solving this hard optimization problem: Firstly, we identify alternative packings for small parts of an instance. Secondly, we choose an optimal subset of these packings by solving a set cover problem. Finally, we apply a sequencing algorithm in order to find an optimal order of the selected subsequences. For every level of the hierarchy, we present tailored algorithms, analyze their performance and illustrate the efficiency of the overall approach by a comprehensive numerical study.", "Keywords": "Production planning; Packing problems; Scheduling; Heuristics; 90B30; 90B90; 90C59; 90B35", "DOI": "10.1007/s13675-020-00127-8", "PubYear": 2020, "Volume": "8", "Issue": "3-4", "JournalId": 294, "JournalTitle": "EURO Journal on Computational Optimization", "ISSN": "2192-4406", "EISSN": "2192-4414", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer ITWM, Institute for Industrial Mathematics, Kaiserslautern, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Fraunhofer ITWM, Institute for Industrial Mathematics, Kaiserslautern, Germany"}], "References": []}, {"ArticleId": 83109971, "Title": "RETRACTED ARTICLE: Sentimental analysis of transliterated text in Malayalam using recurrent neural networks", "Abstract": "<p>Usage of mobile phones, access to internet in the fingertips and increasing number of mobile applications has accelerated the generation of online content. Freedom of expression has waived the barriers of online interaction. Curiosity in knowing others viewpoint through their reviews in each and everything starting from a purchase of product to watching a movie has become a common scenario. Decision on success and failure of one’s business is in the hands of public now. Humans are always comfortable, sticking to their native Language, when it comes to expressions whether it is interest, emotions, feeling or opinion. Usage of Natural Language and the trend to analyze the subjective sentiments is increasing day by day. Transliterated text of a language is the English version of spoken native language. For example, Malayalam in English we call as Manglish. Transliterated text has become the language of social media websites like WhatsApp, Facebook, Twitter. It’s a kind of boon to the young generation who know to speak their native language but not to read or write in its own nominal scripts. In this paper we consider the Sentimental Analysis of Transliterated text. RNN-LSTM technique is used to derive the sentiments of transliterated text.</p>", "Keywords": "Neural network; RNN-LSTM technique; Natural language processing (NLP); Waikato environment for knowledge analysis (Weka)", "DOI": "10.1007/s12652-020-02305-3", "PubYear": 2021, "Volume": "12", "Issue": "6", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "AMCEC Research Center, Visvesvaraya Technological University, Belgaum, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "AMC Engineering College, Visvesvaraya Technological University, Belgaum, India"}], "References": [{"Title": "Sentiment analysis and text categorization of cancer medical records with LSTM", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "5309", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Design of recommendation system for tourist spot using sentiment analysis based on CNN-LSTM", "Authors": "<PERSON><PERSON><PERSON><PERSON>woo <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "1653", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 83109995, "Title": "A derived least square fast learning network model", "Abstract": "<p>The extreme learning machine (ELM) requires a large number of hidden layer nodes in the training process. Thus, random parameters will exponentially increase and affect network stability. Moreover, the single activation function affects the generalization capability of the network. This paper proposes a derived least square fast learning network (DLSFLN) to solve the aforementioned problems. DLSFLN uses the inheritance of some functions to obtain various activation functions through continuous differentiation of functions. The types of activation functions were increased and the mapping capability of hidden layer neurons was enhanced when the random parameter dimension was maintained. DLSFLN randomly generates the input weights and hidden layer thresholds and uses the least square method to determine the connection weights between the output and the input layers and that between the output and the input nodes. The regression and classification experiments show that DLSFLN has a faster training speed and better training accuracy, generalization capability, and stability compared with other neural network algorithms, such as fast learning network(FLN).</p>", "Keywords": "Neural network; Extreme learning machine; Fast learning network; Least square method", "DOI": "10.1007/s10489-020-01773-6", "PubYear": 2020, "Volume": "50", "Issue": "12", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical Behavior and System Safety of Traffic Engineering Structures, Shijiazhuang Tiedao University, Shijiazhuang, China;School of Mechanical Engineering, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical Behavior and System Safety of Traffic Engineering Structures, Shijiazhuang Tiedao University, Shijiazhuang, China;School of Mechanical Engineering, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical Behavior and System Safety of Traffic Engineering Structures, Shijiazhuang Tiedao University, Shijiazhuang, China;School of Mechanical Engineering, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical Behavior and System Safety of Traffic Engineering Structures, Shijiazhuang Tiedao University, Shijiazhuang, China;School of Mechanical Engineering, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical Behavior and System Safety of Traffic Engineering Structures, Shijiazhuang Tiedao University, Shijiazhuang, China;School of Mechanical Engineering, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Qi", "Affiliation": "State Key Laboratory of Mechanical Behavior and System Safety of Traffic Engineering Structures, Shijiazhuang Tiedao University, Shijiazhuang, China;School of Mechanical Engineering, Shijiazhuang Tiedao University, Shijiazhuang, China"}], "References": []}, {"ArticleId": 83110009, "Title": "Accurate language achievement prediction method based on multi-model ensemble using personality factors", "Abstract": "<p>To overcome the noise in personality factors and precisely predict language achievement, we propose a robust regression algorithm based on the maximum correntropy criterion (MCC) and the coarse-to-fine method. Firstly, as there are many samples while few personality factors correlate to the language achievement in the data set, we propose a regression method based on Pearson feature selection to eliminate the noise and redundant features for solving the overfitting problem. Secondly, as the learning ability of each traditional regression model is different and limited, we introduce the model ensemble method based on MCC to predict language achievement via personality factors. Thirdly, owing to the fact that the language achievement data is unevenly distributed and the same model parameter cannot fit all the data effectively, we propose a coarse-to-fine prediction method to reduce prediction errors, which divides the range of the language achievement into multiple intervals and then establishes different regression models at each interval to obtain more accurate results. The experimental results on the data set of the personality factors and English achievement demonstrate the high precision and robustness of the proposed algorithm compared with the traditional single regression models.</p>", "Keywords": "Personality factors; Regression; Correntropy; Multi-model ensemble", "DOI": "10.1007/s11042-020-09297-4", "PubYear": 2021, "Volume": "80", "Issue": "11", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Foreign Studies, Xi’an Jiaotong University, Xi’an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, People’s Republic of China"}], "References": []}, {"ArticleId": 83110010, "Title": "Improving Steganographic capacity using distributed steganography over BMP", "Abstract": "<p>Our research area tackles the improvement of private data security using our proposed Steganographic method called DSoBMP-I (Distributed Steganography over BMP faze I) to improve the issues of low capacity, high detectability and distortion. The methodology consists of a new distributed steganographic approach to minimise the main weaknesses of today’s methods, including Discrete Cosine Transform, where the capacity, detectability and distortion needs an upgrade to accommodate securer steganography for our data protection. The proposed prototype approach that evolved after a few experiments using our distributed steganographic method, where secrete data is secure into a set of BMP files (as it is proven more reliable), originates from a raw file that is not necessarily a BMP at the start. After applying a layer of encryption for extra security using two different methods such as RC4 & RSA, comparing the two encryption techniques for its agility and extra security to address the issue of low capacity and better security using the DSoBMP-I method, all deriving from the supplied image. The overall achievement was improved capacity that doubles as the set of BMP images increases, less distortion and detectability as secrete data stays among different files.</p>", "Keywords": "BMP; Cryptography; DCT; Encryption; Security; Steganography", "DOI": "10.1007/s11042-020-09298-3", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Systems Research Centre from the School of Computing and Digital Media, London Metropolitan University, London, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Intelligent Systems Research Centre from the School of Computing and Digital Media, London Metropolitan University, London, UK"}], "References": []}, {"ArticleId": 83110026, "Title": "Assessing flood severity from crowdsourced social media photos with deep neural networks", "Abstract": "<p>The use of social media data in disaster and crisis management is increasing rapidly. Particularly in connection to flooding events, geo-referenced images shared by citizens can provide situational awareness to emergency responders, as well as assistance to financial loss assessment, giving information that would otherwise be very hard to collect through conventional sensors or remote sensing. Moreover, recent advances in computer vision and deep learning can perhaps support the automated analysis of these data. In this paper, focusing on ground-level images taken by humans during flooding events, we evaluate the use of deep convolutional neural networks for (a) discriminating images showing direct evidence of a flood, and (b) estimating the severity of the flooding event. Considering distinct datasets (i.e., the European Flood 2013 dataset, and data from different editions of the MediaEval Multimedia Satellite Task), we specifically evaluated models based on the DenseNet and EfficientNet neural network architectures, concluding that these classification models can achieve a very high accuracy on this task, thus having a clear potential to complement other sources of information (e.g., satellite imagery) related to flooding events.</p>", "Keywords": "Flood detection and severity estimation; Crowdsourced images; Image classification; Deep learning; Convolutional neural networks", "DOI": "10.1007/s11042-020-09196-8", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidade de Lisboa, IST / INESC-ID, Lisboa, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidade de Lisboa, IST / INESC-ID, Porto Salvo, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INESC-ID, Lisboa, Portugal"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "INESC-ID, Lisboa, Portugal;Universidade Europeia, Lisboa, Portugal"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universidade de Lisboa, IST / INESC-ID, Lisboa, Portugal"}], "References": []}, {"ArticleId": 83110297, "Title": "Unsupervised online prediction of tool wear values using force model coefficients in milling", "Abstract": "<p>Tool wear prediction is an important research in metal cutting, which aims to improve machining accuracy and production efficiency, maximize tool utilization, and reduce machining cost. However, due to high complexity and nonlinearity of tool wear process, it is difficult to establish a general tool wear prediction model, which limits its application in industrial production. To solve this problem, an unsupervised online prediction method for tool wear values is proposed. In the method, a milling force model considering tool wear is established by using analytical method, and parameters varying with tool wear in the force model are integrated into five force model coefficients. The coefficients are solved and updated continuously using the least square estimation method according to the cutting force signals collected in real time. Based on study of relationship between the coefficients and tool wear, a tool flank wear value estimation model is constructed, combined with a time series analysis model, to achieve prediction of tool flank wear values. Experiments are conducted to test the prediction accuracy of tool wear values using the proposed method, and the results show that the average online prediction accuracy reached 72.0%, without supervision. The method has the advantages of low cost and strong adaptability, and can be used for online prediction of tool wear in machining industry.</p>", "Keywords": "Tool wear; Online monitoring; Quantitative prediction; Force model coefficients", "DOI": "10.1007/s00170-020-05684-1", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Construction Machinery, Chang’an University, Xi’an, China;Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Construction Machinery, Chang’an University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical Engineering, Xi’an Shiyou University, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Construction Machinery, Chang’an University, Xi’an, China"}], "References": [{"Title": "An unsupervised online monitoring method for tool wear using a sparse auto-encoder", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2493", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Investigated iterative convergences of neural network for prediction turning tool wear", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "7-8", "Page": "2939", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A hybrid information model based on long short-term memory network for tool condition monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "6", "Page": "1497", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 83110299, "Title": "Applications of texturization techniques on cutting tools surfaces—a survey", "Abstract": "<p>The development of manufacturing processes advances in productivity and cost reduction. Industry 4.0 increasingly allows production to be automated, eliminating production errors and allowing the manufacture of mechanical components with complex geometries. However, the chip formation mechanisms are still the same, generating friction and heat during machining aided by the machining resistant materials and higher cutting speeds. The evolution in materials for cutting tools and hard coatings, as well as efficient techniques for the application of cutting fluid, has been the focus of numerous researches in the field of machining. To obtain better machining conditions, the functionalization of surfaces, through the texturing of micro and nano-geometries on the rake and flank faces of cutting tools, has proven to be effective and promising. This technique increases the entry and retention of the cutting fluid in the contact zones. It reduces the contact area between chip and tool in dry cutting, allowing less friction at the chip-tool interface, reduction in machining temperatures, lower adhesion, and longer tool life. The present work aims to relate the application of textured cutting tools according to the machining materials and cutting tools found in scientific articles of high relevance. Thus, the review refers to the implementation of textured cutting tools in nickel-based alloys, titanium alloys, medium carbon steels, and aluminum alloys, as well as pointing out recent research on cutting tool materials, hard coatings, and solid lubricants applied on textured cutting tools.</p>", "Keywords": "Textured cutting tools; Tool life; Friction; Temperature; Cutting forces", "DOI": "10.1007/s00170-020-05669-0", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, Bauru Campus, Bauru, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, Bauru Campus, Bauru, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, Bauru Campus, Bauru, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, São Paulo State University “Júlio de Mesquita Filho”, Bauru Campus, Bauru, Brazil"}], "References": [{"Title": "Application of MQL technique using TiO2 nanoparticles compared to MQL simultaneous to the grinding wheel cleaning jet", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2205", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Analysis of the surface roughness obtained by the abrasive flow machining process using an abrasive paste with oiticica oil", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "5061", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Depth of dressing optimization in CBN wheels of different friabilities using acoustic emission (AE) technique", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "5225", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Performance of austempered ductile iron (ADI) grinding using diluted oil in MQL combined with wheel cleaning jet and different CBN grains friability", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1805", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Grinding hardened steel using MQL associated with cleaning system and cBN wheel", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2065", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Grinding performance by applying MQL technique: an approach of the wheel cleaning jet compared with wheel cleaning Teflon and Alumina block", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "11-12", "Page": "4415", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Behavior of austempered ductile iron (ADI) grinding using different MQL dilutions and CBN wheels with low and high friability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "11-12", "Page": "4373", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 83110341, "Title": "Synchronization of Stochastic Complex Dynamical Networks with Mixed Time-Varying Coupling Delays", "Abstract": "<p>Synchronization of complex networks with mixed time-varying coupling delays and stochastic perturbation. We constructed a novel Lyapunov functional with triple integral terms. By applying <PERSON>’s inequality and Lyapunov stability theory stability conditions are derived to check the asymptotical stability of the concerned system. By employing the stochastic evaluation and <PERSON>ronecker product delay-dependent synchronization criteria of stochastic complex dynamical networks are derived. By using the derived conditions control gain matrix is obtained. Finally, numerical results are presented to demonstrate the effectiveness and usefulness of the proposed results.</p>", "Keywords": "Complex dynamical networks (CDNs); Synchronization control; Linear matrix inequality; <PERSON><PERSON><PERSON><PERSON><PERSON> functional; Time-varying coupling delays; 34D20; 34K20; 34K40", "DOI": "10.1007/s11063-020-10301-z", "PubYear": 2020, "Volume": "52", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Thiruvalluvar University, Vellore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Thiruvalluvar University, Vellore, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Nonlinear Analysis and Applied Mathematics (NAAM)-Research Group, Department of Mathematics Faculty of Science, King Abdulaziz University, Jeddah, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nonlinear Analysis and Applied Mathematics (NAAM)-Research Group, Department of Mathematics Faculty of Science, King Abdulaziz University, Jeddah, Saudi Arabia"}], "References": []}, {"ArticleId": 83110460, "Title": "On the probabilistic modeling of fake news (hoax) persistency in online social networks and the role of debunking and filtering", "Abstract": "<p>Understanding the dynamics of information diffusion, including spreading of fake news, hoaxes, and generally, misinformation/disinformation, has become crucial in post‐truth societies. The paper focuses on the probability that a hoax originated at a given time will continue to spread indefinitely in online social networks; a minimalistic model based on the theory of branching processes is devised, which only considers the basic possible reactions that users can have after reading a post whose content is a hoax, that is, to share it further, to ignore it, or to try to debunk it. The analysis of the resulting dynamics shows that ignoring is indeed not sufficient to stop the spreading, not even if most people do so. More active counter‐measures are needed; in particular, the proposed model formally describes the ways in which retractions and debunking posts, cultural/educational initiatives, and content moderation policies (including filtering) by Internet companies, can impact on the persistency probability of hoaxes and generally fake news.</p>", "Keywords": "diffusion models;hoax/fake news spreading;online social networks;opinion dynamics", "DOI": "10.1002/itl2.204", "PubYear": 2020, "Volume": "3", "Issue": "5", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dipartimento di Ingegneria dell'Innovazione, University of Salento, Lecce, Italy"}], "References": []}, {"ArticleId": 83110501, "Title": "Unveiling the implicit knowledge, one scenario at a time", "Abstract": "<p>When defining virtual reality applications with complex procedures, such as medical operations or mechanical assembly or maintenance procedures, the complexity and the variability of the procedures make the definition of the scenario difficult and time-consuming. Indeed, the variability complicates the definition of the scenario by the experts, and its combinatorics demand a comprehension effort for the developer, which is often out of reach. Additionally, the experts have a hard time explaining the procedures with a sufficient level of details, as they usually forget to mention some actions that are, in fact, important for the application. To ease the creation of scenario, we propose a complete methodology, based on (1) an iterative process composed of: (2) the recording of actions in virtual reality to create sequences of actions and (3) the use of mathematical tools that can generate a complete scenario from a few of those sequences, with (4) graphical visualization of the scenarios and complexity indicators. This process helps the expert to determine the sequences that must be recorded to obtain a scenario with the required variability. </p>", "Keywords": "Virtual reality; Scenario; Generalization; Authoring", "DOI": "10.1007/s00371-020-01904-7", "PubYear": 2020, "Volume": "36", "Issue": "10-12", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INSA Rennes, Univ Rennes, Inria, CNRS, IRISA, Rennes, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "INSA Rennes, Univ Rennes, Inria, CNRS, IRISA, Rennes, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IRISA, Univ Rennes, Inria, CNRS, IRISA, Rennes, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "INSA Rennes, Univ Rennes, Inria, CNRS, IRISA, Rennes, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, Rennes, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "INSA Rennes, Univ Rennes, Inria, CNRS, IRISA, Rennes, France"}], "References": []}, {"ArticleId": 83110543, "Title": "Accurate physiological monitoring using lab-on-a-chip platform for aquatic micro-organisms growth and optimized culture", "Abstract": "The present work was dedicated to the development of a lab-on-chip microsystem for the monitoring of microalgal photosynthetic activity. Thanks to integrated electrochemical microcells, dissolved oxygen O<sub>2</sub> concentrations due to photosynthetic microalgal activity were measured in a continuous way and oxygen production rates of microalgae cultures were finally determined in the frame of artificial night/day cycles. Application was performed by studying the physiological metabolisms of the green microalga Chlamydomonas reinhardtii . First, the different growth dynamics of microalgal cultures were characterized, enabling the determination of the induction, \"exponential growth\", \"declining growth rate\" and stationary phases. Then, the influences of carbon-based nutrients, such as sodium bicarbonate HCO<sub>3</sub>Na and methanol CH<sub>3</sub>OH, as well as of pollutants, such as silver nitrate AgNO<sub>3</sub>, were studied, evidencing contradictory behaviors according to the competition between nutritional properties, toxicity effects and acclimation phenomena. This paves the way to the development of analysis microsystems for the understanding of microalgal metabolisms as well as for the improvement of microalgae growth processes and associated industrial production.", "Keywords": "Biosensor ; Electrochemical sensor ; Lab-on-chip ; Green microalgae culture ; Algal metabolisms", "DOI": "10.1016/j.snb.2020.128492", "PubYear": 2020, "Volume": "321", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France;Corresponding author at: LAAS-CNRS, 7 avenue du colonel <PERSON>, F-31400, Toulouse Cedex 4, France"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "CNRS, LAAS, 7 avenue du colonel <PERSON>, F-31400, Toulouse, France;Université de Toulouse, UPS, LAAS, F-31400, Toulouse, France"}], "References": []}, {"ArticleId": 83110552, "Title": "SalientGaze: Saliency-based gaze correction in virtual reality", "Abstract": "Eye-tracking with gaze estimation is a key element in many applications, ranging from foveated rendering and user interaction to behavioural analysis and usage metrics. For virtual reality, eye-tracking typically relies on near-eye cameras that are mounted in the VR headset. Such methods usually involve an initial calibration to create a mapping from eye features to a gaze position. However, the accuracy based on the initial calibration degrades when the position of the headset relative to the users’ head changes; this is especially noticeable when users readjust the headset for comfort or even completely remove it for a short while. We show that a correction of such shifts can be achieved via 2D drift vectors in eye space. Our method estimates these drifts by extracting salient cues from the shown virtual environment to determine potential gaze directions. Our solution can compensate for HMD shifts, even those arising from taking off the headset, which enables us to eliminate reinitialization steps.", "Keywords": "Virtual reality ; Eye-tracking ; Headsets shifts ; Saliency ; Stereo ; Drift estimation", "DOI": "10.1016/j.cag.2020.06.007", "PubYear": 2020, "Volume": "91", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Graphics and Visualization Group, Delft University of Technology, Delft, 2628 XE, the Netherlands;Science and Technology on Information Systems Engineering Laboratory, National University of Defense Technology, Changsha 410073, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Graphics and Visualization Group, Delft University of Technology, Delft, 2628 XE, the Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Graphics and Visualization Group, Delft University of Technology, Delft, 2628 XE, the Netherlands"}], "References": []}, {"ArticleId": 83110628, "Title": "Parallel finite-element codes for the simulation of two-dimensional and three-dimensional solid–liquid phase-change systems with natural convection", "Abstract": "We present and distribute a FreeFem++ Toolbox for the parallel computing of two- or three-dimensional liquid–solid phase-change systems involving natural convection. FreeFem++ ( www.freefem.org ) is a free finite-element software available for all existing operating systems. We use the recent library ffddm that makes available in FreeFem++ state-of-the-art scalable Schwarz domain decomposition methods (DDM). The single domain approach used in our previous contribution (<PERSON><PERSON> et al., 2020) is adapted for the use of the DDM method. As a result, the computational time is considerably reduced for 2D configurations and furthermore 3D problems become affordable. The numerical method is based on an enthalpy-porosity model. The same set of equations is solved in both liquid and solid phases: the incompressible Navier–Stokes equations with Boussinesq approximation for thermal effects. A Carman–Kozeny-type penalty term is added to the momentum equations to bring progressively the velocity to zero into the solid. Model equations are discretized using Galerkin triangular or tetrahedral finite elements. The coupled system of equations is integrated in time using a second-order Gear implicit scheme. The resulting discrete equations are solved using a Newton algorithm. The DDM approach is based on an overlapping Schwarz method. The mesh is first split in subdomains using Scotch or Metis libraries. The final linear system is then solved in parallel using a GMRES Krylov method, with a Restricted Additive Schwarz (RAS) preconditioner. The mesh is adapted during the computation using metrics control. The 3D-mesh adaptivity uses the mmg ( www.mmgtools.org ) open source library. Parallel 2D and 3D computations of benchmark cases of increasing difficulty are presented: natural convection of air, natural convection of water, melting or solidification of a phase-change material, and, finally, a water freezing case. For each case, careful validations are provided and the performance of the code is assessed. The robustness of the Toolbox in 3D is also demonstrated by adapting the number of processors to the number of tetrahedra, which can considerably vary after the mesh adaptation. Program summary Program Title: PCM_Toolbox_DDM_2D and PCM_Toolbox_DDM_3D CPC Library link to program files: http://dx.doi.org/10.17632/dk49rfrz9y.1 Licensing provisions: Apache License, 2.0 Programming language: FreeFem+ +( www.freefem.org ), mmg ( www.mmgtools.org ) Nature of problem: The software is scoped to parallel computations of 2D or 3D configurations of liquid–solid phase-change problems with convection in the liquid phase. Natural convection, melting and solidification processes are illustrated in the paper. The software can be easily modified to take into account different related physical models. Solution method: We use a single domain approach, solving the incompressible Navier–Stokes equations with Boussinesq approximation in both liquid and solid phases. A Carman–Kozeny-type penalty term is added to the momentum equations to bring the velocity to zero into the solid phase. An enthalpy model is used in the energy equation to take into account the phase change. Discontinuous variables (latent heat, material properties) are regularized through an intermediate (mushy) region. Space discretization is based on Galerkin triangular/tetrahedral finite elements. A second order Gear implicit scheme is used for the time integration of the coupled system of equations. The resulting discrete equations are solved using a Newton algorithm. Piecewise quadratic (P2) finite-elements are used for the velocity and piecewise linear (P1) for the pressure. For the temperature both P2 or P1 discretization are possible. The mesh is first split in subdomains using Scotch or Metis libraries, which are interfaced with FreeFem++. Then, a Schwarz domain decomposition method is used through the FreeFem+ +library ffddm . The final linear system is solved in parallel using a GMRES Krylov method, with a Restricted Additive Schwarz (RAS) preconditioner. Mesh adaptivity using metrics control makes possible the optimization of the distribution of mesh elements. For 3D case, the mmg open source library is used to adapt the mesh.", "Keywords": "Phase change ; PCM ; Parallel computing ; Finite element ; 3D melting ; Mesh adaptivity ; Na<PERSON><PERSON>–Boussinesq ; FreeFem", "DOI": "10.1016/j.cpc.2020.107492", "PubYear": 2020, "Volume": "257", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire de Mathématiques Raphaël Salem, Université de Rouen Normandie, CNRS UMR 6085, Avenue de l’Université, BP 12, F-76801 Saint-Étienne-<PERSON>-Rouvray, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire de Mathématiques Raphaël Salem, Université de Rouen Normandie, CNRS UMR 6085, Avenue de l’Université, BP 12, F-76801 Saint-Étienne-<PERSON>-Rouvray, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sorbonne Université, CNRS UMR 7598, Laboratoire Jacques-Louis <PERSON>, F-75005, Paris, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire de Mathématiques Raphaël Salem, Université de Rouen Normandie, CNRS UMR 6085, Avenue de l’Université, BP 12, F-76801 Saint-Étienne-<PERSON>-Rouvray, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire de Mathématiques Raphaël Salem, Université de Rouen Normandie, CNRS UMR 6085, Avenue de l’Université, BP 12, F-76801 Saint-Étienne-<PERSON>-Rouvray, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire de Mathématiques Raphaël Salem, Université de Rouen Normandie, CNRS UMR 6085, Avenue de l’Université, BP 12, F-76801 Saint<PERSON>, France;Corresponding author"}], "References": [{"Title": "A finite-element toolbox for the simulation of solid–liquid phase-change systems with natural convection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "253", "Issue": "", "Page": "107188", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 83110633, "Title": "Blockchain-based public auditing and secure deduplication with fair arbitration", "Abstract": "Data auditing enables data owners to verify the integrity of their sensitive data stored at an untrusted cloud without retrieving them. This feature has been widely adopted by commercial cloud storage. However, the existing approaches still have some drawbacks. On the one hand, the existing schemes have a defect of fair arbitration, i.e., existing auditing schemes lack an effective method to punish the malicious cloud service provider (CSP) and compensate users whose data integrity is destroyed. On the other hand, a CSP may store redundant and repetitive data. These redundant data inevitably increase management overhead and computational cost during the whole data life cycle. To address these challenges, we propose a blockchain-based public auditing and secure deduplication scheme with fair arbitration. By using a smart contract, our scheme supports automatic penalization of the malicious CSP and compensates users whose data integrity is damaged. Moreover, our scheme introduces a message-locked encryption algorithm and removes the random masking in data auditing. Compared with the existing schemes, our scheme can effectively reduce the computational cost of tag verification and data storage costs. We give a comprehensive analysis to demonstrate the correctness of the proposed scheme in terms of storage, batch auditing, and data consistency. Also, extensive experiments conducted on the platform of Ethereum blockchain demonstrate the efficiency and effectiveness of our scheme.", "Keywords": "Blockchain ; Data auditing ; Fair arbitration ; Data deduplication", "DOI": "10.1016/j.ins.2020.07.005", "PubYear": 2020, "Volume": "541", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Service Networks (ISN), Xidian University, Xi’an, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Chen", "Affiliation": "State Key Laboratory of Integrated Service Networks (ISN), Xidian University, Xi’an, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Service Networks (ISN), Xidian University, Xi’an, PR China"}, {"AuthorId": 4, "Name": "Jiaming Yuan", "Affiliation": "School of Information Systems, Singapore Management University, Singapore"}, {"AuthorId": 5, "Name": "Hongyang Yan", "Affiliation": "School of Computer Science, Guangzhou University, Guangzhou, PR China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computing and Information Technology, University of Wollongong, Australia"}], "References": [{"Title": "Secure Cloud Data Deduplication with Efficient Re-Encryption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "442", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 83110905, "Title": "Optimal local dimming based on an improved greedy algorithm", "Abstract": "<p>As a new technology appeared in recent years, the local dimming can effectively reduce the power consumption of a display system and improve its display effect. A suitable local dimming algorithm should have efficient performance and can make the displayed images have higher visual quality. However, most of the existing local dimming methods can not have both of the above advantages. In this paper, the local dimming is taken as an optimization problem. On the basis of our previous work which focuses on reducing the image distortion and power consumption, image contrast ratio which is another important factor of visual quality is also considered. To improve the running efficiency of local dimming, the Greedy Algorithm (GRA) which is one of the simplest heuristic algorithms is used to design the local dimming algorithm. In order to improve the global optimization ability of the GRA, an Improved Greedy Algorithm(IGRA) based on the strategies of Taking out-Putting in and variable search step size is proposed. Experienced in four different types of images and compared with five parameter-based algorithms, the IGRA can obtain a higher visual quality under the same or lower power consumption. It is also proved that the IGRA has more powerful search ability and higher running efficiency by the comparisons with the Improved Shuffled Frog Leaping Algorithm (ISFLA) proposed in our previous work, and two recent algorithms including the Modified Genetic Algorithm (MGA) and the Improved Particle Swarm Optimization (IPSO).</p>", "Keywords": "Local dimming; Optimization; Visual quality; IGRA", "DOI": "10.1007/s10489-020-01769-2", "PubYear": 2020, "Volume": "50", "Issue": "12", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China;Texas Instruments DSP Joint Lab, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China;Texas Instruments DSP Joint Lab, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China;Texas Instruments DSP Joint Lab, Tianjin University, Tianjin, China"}], "References": [{"Title": "Moth–flame optimization algorithm: variants and applications", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "14", "Page": "9859", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Salp swarm algorithm: a comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11195", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 83110938, "Title": "Sensitive and reproducible surface-enhanced raman spectroscopy (SERS) with arrays of dimer-nanopillars", "Abstract": "It is challenge for most metallic nanostructures to produce both reproducible and high sensitive SERS enhancement, which hinder practical applications of SERS technology. In this work, we report that gold-coated silicon ( [email protected] ) dimer-nanopillar arrays can be used as SERS substrates with both high-sensitivity and reproducibility. The substrates consist of dimer-nanopillars with height of 480 nm and gaps of 8−10 nm fabricated with electron-beam lithography and followed plasma etching. The dimer-nanopillars have demonstrated SERS enhancement factors (EFs) of 4.3−8.0 × 10<sup>9</sup> with SERS signals relative standard deviation (RSD) of 3.2−5.6 % (reproducibility). The EFs are 2–3 orders of magnitude higher in comparison with that of other types of ordered nanostructures and are comparable with those of chemically synthesized nanoparticle aggregates. Meanwhile, the reproducibility is among the best ever reported. The dimer-nanopillar arrays were applied for detection of component of rapeseed oil. Cooked and uncooked oil can be clearly identified from the SERS spectra. Our study demonstrated that the dimer-nanopillars are promising to develop highly enhancing and reproducible SERS substrates for SERS sensing applications.", "Keywords": "Surface-enhanced raman scattering (SERS) ; Dimer-nanopillars ; Surface plasmon ; Nanofabrication", "DOI": "10.1016/j.snb.2020.128563", "PubYear": 2020, "Volume": "322", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Weisheng Yue", "Affiliation": "State Key Laboratory of Optical Technologies on Nano-Fabrication and Micro-Engineering, Institute of Optics and Electronics, Chinese Academy of Sciences, P.O. Box 350, Chengdu 610209, China;Corresponding author"}, {"AuthorId": 2, "Name": "Tiancheng Gong", "Affiliation": "State Key Laboratory of Optical Technologies on Nano-Fabrication and Micro-Engineering, Institute of Optics and Electronics, Chinese Academy of Sciences, P.O. Box 350, Chengdu 610209, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Optical Technologies on Nano-Fabrication and Micro-Engineering, Institute of Optics and Electronics, Chinese Academy of Sciences, P.O. Box 350, Chengdu 610209, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Astronomy, University of Manchester, Oxford Road, Manchester M13 9PL, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Optical Technologies on Nano-Fabrication and Micro-Engineering, Institute of Optics and Electronics, Chinese Academy of Sciences, P.O. Box 350, Chengdu 610209, China"}, {"AuthorId": 6, "Name": "Ming<PERSON>", "Affiliation": "State Key Laboratory of Optical Technologies on Nano-Fabrication and Micro-Engineering, Institute of Optics and Electronics, Chinese Academy of Sciences, P.O. Box 350, Chengdu 610209, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Optical Technologies on Nano-Fabrication and Micro-Engineering, Institute of Optics and Electronics, Chinese Academy of Sciences, P.O. Box 350, Chengdu 610209, China"}], "References": []}, {"ArticleId": 83110939, "Title": "Electric potential and surface oxygen ion density for planar, spherical and cylindrical metal oxide grains", "Abstract": "In this article, the mathematical model presented by <PERSON><PERSON> and <PERSON> (Sensors and Actuators B: Chemical 293 (2019) 31–40) for metal oxide grains is discussed. The nonlinear governing model, which is a Poisson-Boltzmann-type equation, is solved by a simple and efficient method. Analytical expressions for the electrical potential and oxygen ion density within the planar, cylindrical and spherical metal oxide grains are expressed in terms of the absolute value of the electric potential and grain thickness. In addition, an investigation of the effects of parameters such as grain diameter, grain thickness, and uniform distribution of single donors on the performance. The validity of the derived analytical expression is established by direct comparison with computer-generated numerical simulations in addition to previously available limiting cases’ results (low and high oxygen adsorption on the grain).", "Keywords": "Poisson-<PERSON><PERSON><PERSON> equation ; Electric potential ; Surface oxygen ion ; Metal oxide ; Mathematical modelling", "DOI": "10.1016/j.snb.2020.128576", "PubYear": 2020, "Volume": "321", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Academy of Maritime Education and Training (AMET), Deemed to be University, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Academy of Maritime Education and Training (AMET), Deemed to be University, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Academy of Maritime Education and Training (AMET), Deemed to be University, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, American University of Sharjah, United Arab Emirates;Corresponding author"}], "References": []}, {"ArticleId": 83110966, "Title": "E-nose for the monitoring of plastics catalytic degradation through the released Volatile Organic Compounds (VOCs) detection", "Abstract": "The work presents the results of an artificial olfaction E-nose system application for detection of Volatile Organic Compounds (VOCs) – destruction products of OXO-biodegradable polyethylene films when exposed to Ultraviolet (UV) irradiation and heating. The E-nose system was based on eight piezoelectric quartz resonator sensors coated with polymeric sensing materials with various selectivity to analytes. The effect of prooxidants, materials based on variable valence metals, such as D<sub>2</sub>w, ferric stearate and ferric carboxylate, addition on polyethylene films (PE) photo destruction was studied. The dependence of the composition of VOCs mixtures emitted by PE films on the processing time, the power of the UV irradiation and on the nature of the modifying additive was established. In addition, the main emitted volatile compounds were identified and the dynamics of their formation for different catalysts during the plastics destruction were studied. The proposed E-nose system has proved to be an effective tool for assessing catalyst-prooxidants properties.", "Keywords": "Biodegradable plastics ; Polyethylene ; Catalyst ; UV-irradiation ; VOCs release ; Piezoelectric E-nose", "DOI": "10.1016/j.snb.2020.128585", "PubYear": 2020, "Volume": "322", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical and Analytical Chemistry, Voronezh State University of Engineering Technologies (VSUET), Revolutsii Avenue 19, Voronezh, 394036, Russia;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical and Analytical Chemistry, Voronezh State University of Engineering Technologies (VSUET), Revolutsii Avenue 19, Voronezh, 394036, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Sciences and Technology, University \"Tor Vergata\", Via della Ricerca Scientifica 1, Rome, Italy;Corresponding authors"}], "References": []}, {"ArticleId": 83110968, "Title": "Proximity ligation-responsive catalytic hairpin assembly-guided DNA dendrimers for synergistically amplified electrochemical biosensing", "Abstract": "Herein, we report a specific and sensitive electrochemical aptasensor through the proximity ligation-responsive catalytic hairpin assembly (PLCHA) to guide the construction of functional DNA dendrimers (DNADS), which remarkably facilitates the anchoring and entrapping of synergistic amplifiers, Pt nanoparticles (PtNPs) and manganese(III) meso -tetrakis ( N -methyl-pyridinium-2-yl)-porphyrin (MnTMPyP). Using thrombin (TB) as analyte model, the proximity ligation with its specific aptamers tethered in two affinity probes ( A1 and A2 ) dependently output a sequence-specific fuel strand ( F* ) to activate PLCHA of two DNA hairpins ( H1 and H2 ) through F* -mediated strand displacement. With unpaired toeholds of the formed duplexes, two PtNPs-functionalized single strands ( S1 @PtNPs) and S2 @PtNPs) sequentially hybridize one another, allowing the “bottom-up” self-assembly of this DNADS to entrap mimicking peroxidase MnTMPyP in numerous duplex scaffolds. Upon treated with hydrogen peroxide (H<sub>2</sub>O<sub>2</sub>), the oxidation of 4-chloro-1-naphthol (4-CN) was greatly promoted by the synergistic catalysis of PtNPs and MnTMPyP, generating significantly enhanced electrochemical signal for reliable quantitation of TB in a linear range of 1 fM to 100 nM with a high sensitivity down to 10.7 aM. Based on PLCHA-directed DNADS for the stable loading of signal amplifiers, this rational detection route would pave a new avenue to monitor various disease-related proteins, enzymes or small molecules.", "Keywords": "Electrochemical aptasensor ; Thrombin ; Protein-proximity ligation ; Catalytic hairpin assembly ; DNA dendrimers ; Mimicking peroxidase enzyme", "DOI": "10.1016/j.snb.2020.128566", "PubYear": 2020, "Volume": "322", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Engineering Laboratory of Nanomaterials & Sensor Technologies, School of Chemistry and Chemical Engineering, Southwest University, Chongqing, 400715, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Engineering Laboratory of Nanomaterials & Sensor Technologies, School of Chemistry and Chemical Engineering, Southwest University, Chongqing, 400715, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Engineering Laboratory of Nanomaterials & Sensor Technologies, School of Chemistry and Chemical Engineering, Southwest University, Chongqing, 400715, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Engineering Laboratory of Nanomaterials & Sensor Technologies, School of Chemistry and Chemical Engineering, Southwest University, Chongqing, 400715, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Engineering Laboratory of Nanomaterials & Sensor Technologies, School of Chemistry and Chemical Engineering, Southwest University, Chongqing, 400715, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Engineering Laboratory of Nanomaterials & Sensor Technologies, School of Chemistry and Chemical Engineering, Southwest University, Chongqing, 400715, PR China;Corresponding author"}], "References": []}, {"ArticleId": 83110971, "Title": "A reinforcement learning approach for optimizing multiple traveling salesman problems over graphs", "Abstract": "This paper proposes a learning-based approach to optimize the multiple traveling salesman problem (MTSP), which is one classic representative of cooperative combinatorial optimization problems. The MTSP is interesting to study, because the problem arises from numerous practical applications and efficient approaches to optimize the MTSP can potentially be adapted for other cooperative optimization problems. However, the MTSP is rarely researched in the deep learning domain because of certain difficulties, including the huge search space, the lack of training data that is labeled with optimal solutions and the lack of architectures that extract interactive behaviors among agents. This paper constructs an architecture consisting of a shared graph neural network and distributed policy networks to learn a common policy representation to produce near-optimal solutions for the MTSP. We use a reinforcement learning approach to train the model, overcoming the requirement data labeled with ground truth. We use a two-stage approach, where reinforcement learning is used to learn an allocation of agents to vertices, and a regular optimization method is used to solve the single-agent traveling salesman problems associated with each agent. We introduce a S -samples batch training method to reduce the variance of the gradient, improving the performance significantly. Experiments demonstrate our approach successfully learns a strong policy representation that outperforms integer linear programming and heuristic algorithms, especially on large scale problems.", "Keywords": "Multi-agent reinforcement learning ; Combinatorial optimization problems ; Multiple traveling salesman problems ; Graph neural networks ; Policy networks", "DOI": "10.1016/j.knosys.2020.106244", "PubYear": 2020, "Volume": "204", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, China;National University of Singapore, Singapore;Corresponding author at: Northwestern Polytechnical University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northwestern Polytechnical University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Singapore, Singapore"}], "References": []}, {"ArticleId": 83110991, "Title": "Security in smart cities: A brief review of digital forensic schemes for biometric data", "Abstract": "A smart city is engineered to be a self-sustained ecosystem driven by Internet-of-Things (IoT) devices. Smooth functioning of smart cities is conditioned on seamless communication between users and devices. Smart devices equipped with biometric authentication can offer security as well as personalized experience to the end users. Currently, a number of smart devices employ face, fingerprint, and voice modalities for user verification. However, the biometric data acquired by these devices can be digitally manipulated or tampered with, that can compromise the security of the smart environment. Further, the preponderance of biometric data such as face and voice in social media applications, necessitates the validation of their integrity. In this work, we review state-of-the-art digital forensic schemes for audio-visual biometric data that can be leveraged by applications designed for smart cities.", "Keywords": "Smart technology ; Biometric data ; Digital forensics ; 41A05 ; 41A10 ; 65D05 ; 65D17", "DOI": "10.1016/j.patrec.2020.07.009", "PubYear": 2020, "Volume": "138", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Michigan State University, East Lansing, MI 48824, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Michigan State University, East Lansing, MI 48824, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Michigan State University, East Lansing, MI 48824, USA"}], "References": [{"Title": "Deepfakes and beyond: A Survey of face manipulation and fake detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "131", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 83111033, "Title": "Sediment descriptions and geochemical analyses of radiocarbon-dated deposits from the vicinity of Göbekli Tepe—A dataset", "Abstract": "This dataset comprises the detailed descriptions and laboratory measurements of sediment profiles from the semi-arid environs of the Pre-Pottery Neolithic site Göbekli Tepe in southeastern Turkey—one of the oldest monumental structures of humankind dating to c. 11.5–10 ka BP. Focus of the descriptions are the architectural elements of the deposits allowing to conduct facies interpretations and the reconstruction of different depositional environments. This is supported by bulk geochemical sediment analyses (pH, electrical conductivity, magnetic susceptibility, and loss on ignition) and the determination of total and inorganic carbon contents and chemical element concentrations. The Late Holocene chronology is based on radiocarbon dating of charcoal pieces and bulk samples containing organic matter from buried organic-rich topsoil horizons and soil sediments. Lithic artifacts from the Pre-Pottery Neolithic provide additional age estimates. <PERSON><PERSON><PERSON> et al. [1] provide the synthesis that is based on the presented datasets.", "Keywords": "Sediment sequences ; Depositional architecture ; Geochemical bulk parameters ; Facies interpretation ; Late Holocene geomorphodynamics", "DOI": "10.1016/j.dib.2020.106012", "PubYear": 2020, "Volume": "31", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Freie Universität Berlin, Institute of Geographical Sciences, Malteserstr. 74-100, 12249 Berlin, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Geography, Physical Geography, Christian-Albrechts-Universität zu Kiel, Ludewig-Meyn-Str. 14, 24118 Kiel, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Freie Universität Berlin, Institute of Geographical Sciences, Malteserstr. 74-100, 12249 Berlin, Germany"}], "References": []}, {"ArticleId": 83111060, "Title": "SAFFO: A SIFT based approach for digital anastylosis for fresco recOnstruction", "Abstract": "Anastylosis is an archaeological technique which focuses on the reconstruction of collapsed building and destroyed artworks, starting from the original pieces. Many digital approaches have been developed in the last decade, mainly based on 2D and 3D analysis of the structure of the fragments. These techniques aim at supporting the priceless work of the involved operators, mainly in the decision processes and in the resolution of positioning ambiguities. Techniques acting with this scope lie in the field of the digital anastylosis. In this paper we present SAFFO, a digital approach to 2D reconstruction of frescoes, based on the extraction of SIFT features from a painting. The approach appears to be very robust to false positives, resulting optimal in scenarios involving fragment sets containing spurious elements. The experiments have been performed on the DAFNE (Digital Anastylosis for Fresco challeNgE) dataset, which gathers more than 30 2D artworks and provides several tessellation for each. For its robustness against spurious fragments, SAFFO won the third place in the rank list of DAFNE Challenge 2019.", "Keywords": "SIFT features ; Fresco Reconstruction ; DAFNE challenge", "DOI": "10.1016/j.patrec.2020.07.008", "PubYear": 2020, "Volume": "138", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitá degli Studi di Salerno - <PERSON>, 132 - 84084 <PERSON>s<PERSON><PERSON> (SA) Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Technology Engineering (DIETI), Università degli Studi di Napoli, Federico II Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitá degli Studi di Salerno - Via <PERSON>, 132 - 84084 <PERSON>sciano (SA) Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitá degli Studi di Salerno - Via <PERSON>, 132 - 84084 <PERSON>sciano (SA) Italy"}], "References": [{"Title": "SAFFO: A SIFT based approach for digital anastylosis for fresco recOnstruction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "123", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 83111069, "Title": "Feature selection considering Uncertainty Change Ratio of the class label", "Abstract": "The topic of feature selection in high-dimensional data sets has attracted considerable attention. Feature selection can reduce the dimension of feature and improve the prediction accuracy of the classification model. Information-theoretical-based feature selection methods intend to obtain classification information regarding class labels from the already-selected feature subset as much as possible. Existing methods focus on the reduced uncertainty of class labels while ignoring the change of the remained uncertainty of class labels. In the process of feature selection, the large reduced uncertainty of class labels does not signify the few remained uncertainty of class labels when different candidate features are given. In this paper, we analyze the difference between the reduced uncertainty of class labels and the remained uncertainty of class labels and propose a new term named Uncertainty Change Ratio that considers the change of uncertainty of class labels. Finally, a novel method named Feature Selection considering Uncertainty Change Ratio (UCRFS) is proposed. To prove the classification superiority of the proposed method, UCRFS is compared to three traditional methods and four state-of-the-art methods on fourteen benchmark data sets. The experimental results demonstrate that UCRFS outperforms seven other methods in terms of average classification accuracy, AUC and F1 score.", "Keywords": "Classification ; Feature selection ; Information theory ; Uncertainty Change Ratio", "DOI": "10.1016/j.asoc.2020.106537", "PubYear": 2020, "Volume": "95", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130012, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, Changchun 130012, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130012, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, Changchun 130012, China;College of Chemistry, Jilin University, Changchun 130012, China;Corresponding author at: College of Computer Science and Technology, Jilin University, Changchun 130012, China"}], "References": [{"Title": "Binary differential evolution with self-learning for multi-objective feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}, {"Title": "Multi-objective feature selection based on artificial bee colony: An acceleration approach with variable sample size", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106041", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 83111100, "Title": "Transcoding across 3D shape representations for unsupervised learning of 3D shape feature", "Abstract": "Unsupervised learning of 3D shape feature is a challenging yet important problem for organizing a large collection of 3D shape models that do not have annotations. Recently proposed neural network-based approaches attempt to learn meaningful 3D shape feature by autoencoding a single 3D shape representation such as voxel, 3D point set, or multiview 2D images. However, using single shape representation isn't sufficient in training an effective 3D shape feature extractor, as none of existing shape representation can fully describe geometry of 3D shapes by itself. In this paper, we propose to use transcoding across multiple 3D shape representations as the unsupervised method to obtain expressive 3D shape feature. A neural network called Shape Auto-Transcoder (SAT) learns to extract 3D shape features via cross-prediction of multiple heterogeneous 3D shape representations. Architecture and training objective of SAT are carefully designed to obtain effective feature embedding. Experimental evaluation using 3D model retrieval and 3D model classification scenarios demonstrates high accuracy as well as compactness of the proposed 3D shape feature. The code of SAT is available at https://github.com/takahikof/ShapeAutoTranscoder .", "Keywords": "Unsupervised feature learning ; 3D shape retrieval and classification ; Deep learning ; Autoencoder", "DOI": "10.1016/j.patrec.2020.07.012", "PubYear": 2020, "Volume": "138", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Yamanashi, 4-3-11 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>-<PERSON>en 400-8511, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Yamanashi, 4-3-11 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>-<PERSON><PERSON> 400-8511, Japan"}], "References": []}, {"ArticleId": 83111187, "Title": "Information processing in Internet of Things using big data analytics", "Abstract": "With innovation in persistent technologies, such as wearable sensor gadgets, sensor devices, and wireless ad-hoc communication networks connect everyday life things to the Internet, normally referred to as Internet of Things (IoT). IoT is observed as an active entity for design and development of smart and context awareness services and applications in the area of business, science and engineering discipline. These applications and services could vigorously respond to the surroundings transformation and users’ preference. Developing a scalable system for data analysis, processing and mining of enormous real world based datasets has turned into one of the demanding problems that faces both system research scholars and data management research scholars. Employing big data analytics with IoT technologies is one of the ways for handling the timely analyzing information (i.e., data, events) streams. In this paper, we propose an integrated approach that coalesce IoT systems with big data tools into a holistic platform for real-time and continuous data monitoring and processing. We propose Fog assisted IoT based Smart and real time healthcare information processing (SRHIP) system in which large amounts of data generated by IoT sensor devices are offloaded at Fog cloud form data analytics and processing with minimum delay. The processed data is then transferred to a centralized cloud system for further analysis and storage. In this work, we introduce a Fog-assisted model with big data environment for data analytic of real time data with remote monitoring and discuss our plan for evaluating its efficacy in terms of several performance metrics such as transmission cost, storage cost, accuracy, specificity, sensitivity and F-measure. The proposed SRHIP system needs less transmission cost of 40.10% in comparison to SPPDA, 100% fewer bytes are compromised in comparison to GCEDA. Our proposed system data size reduction of 60% reduction due to proposed compression scheme in comparison to other benchmark strategies that offer 40% of reduction.", "Keywords": "Internet of Things (IoT) ; Fog computing ; Cloud computing ; Naïve Bayes classifier ; Smart and real-time healthcare information processing (SRHIP)", "DOI": "10.1016/j.comcom.2020.06.020", "PubYear": 2020, "Volume": "160", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Moral Culture Research Center of Hunan Normal University, Changsha, 410081, China"}], "References": [{"Title": "Towards secure big data analytic for cloud-enabled applications with fully homomorphic encryption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "192", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "The construction of smart city information system based on the Internet of Things and cloud computing", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "158", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 83111225, "Title": "Linear strain maximization in MEMS-elastomer hybrid configurations for isotropic electromagnetic modulations in stretchable electronics", "Abstract": "We analyzed several model designs that have been subjected to incremental increase in the isotropically distributed strain for an elastomer component which is integrated into a four-actuator microelectromechanical system (MEMS) with a hybrid configuration in this study. Based on the fundamental configuration in which a cross-shaped polydimethylsiloxane (PDMS) structure was placed above the symmetrically designed MEMS with a limited 40.96% degree of isotropy (DOI), our analysis showed that retreating the location of the applied forces away from the center induced free-form PDMS deformation and increased the DOI by 36.48%. Continuous expansion of the DOI was realized by either connecting the geometrically dicontinuous PDMS (for an additional 7.20% improvement) or removing constraints from its right-angled shape (for an additional 7.52% improvement). Further analysis showed that an integrated configuration based on an eight-actuator MEMS exhibited a 100% DOI in the active area, which can be applied to support various electromagnetic modulations that require linear and isotropic strain operations as stretchable electronics. In addition to mechanical analyses, color filtering based on surface plasmon resonance was performed to evaluate potential improvements in terms of the positive relationship between the DOI and color purity. An example of further enhancement of the strain by increasing the forces applied to the MEMS is provided, which proves that a doubled linear and isotropic strain could be induced in the hybrid configuration compared to that generated by the existing configuration.", "Keywords": "Microelectromechanical system ; Polydimethylsiloxane ; Strain ; Surface plasmon resonance", "DOI": "10.1016/j.displa.2020.101963", "PubYear": 2020, "Volume": "64", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Power Mechanical Engineering, National Tsing Hua University 101, Section 2, Kuang Fu Road, Hsinchu 30013, Taiwan, ROC"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut National des Sciences Appliquées de Lyon, 20 Avenue Albert Einstein, 69100, Villeurbanne, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of NanoEngineering and MicroSystems, National Tsing Hua University 101, Section 2, Kuang Fu Road, Hsinchu 30013, Taiwan, ROC"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Power Mechanical Engineering, National Tsing Hua University 101, Section 2, Kuang Fu Road, Hsinchu 30013, Taiwan, ROC;Institute of NanoEngineering and MicroSystems, National Tsing Hua University 101, Section 2, Kuang Fu Road, Hsinchu 30013, Taiwan, ROC;Corresponding author"}], "References": []}, {"ArticleId": 83111241, "Title": "Partial order resolution of event logs for process conformance checking", "Abstract": "While supporting the execution of business processes, information systems record event logs. Conformance checking relies on these logs to analyze whether the recorded behavior of a process conforms to the behavior of a normative specification. A key assumption of existing conformance checking techniques, however, is that all events are associated with timestamps that allow to infer a total order of events per process instance. Unfortunately, this assumption is often violated in practice. Due to synchronization issues, manual event recordings, or data corruption, events are only partially ordered. In this paper, we put forward the problem of partial order resolution of event logs to close this gap. It refers to the construction of a probability distribution over all possible total orders of events of an instance. To cope with the order uncertainty in real-world data, we present several estimators for this task, incorporating different notions of behavioral abstraction. Moreover, to reduce the runtime of conformance checking based on partial order resolution, we introduce an approximation method that comes with a bounded error in terms of accuracy. Our experiments with real-world and synthetic data reveal that our approach improves accuracy over the state-of-the-art considerably.", "Keywords": "Process mining ; Conformance checking ; Partial order resolution ; Data uncertainty", "DOI": "10.1016/j.dss.2020.113347", "PubYear": 2020, "Volume": "136", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Data and Web Science Group, University of Mannheim, Mannheim, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Kühne Logistics University, Hamburg, Germany;Hasso Plattner Institute, University of Potsdam, Potsdam, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Humboldt-Universität zu Berlin, Berlin, Germany"}], "References": []}, {"ArticleId": 83111243, "Title": "Application of the best evacuation model of deep learning in the design of public structures", "Abstract": "Evacuation behavior is an important factor which must be considered in the design of public structures. With the continuous complexity of structure, more and more factors should be considered in evacuation. The traditional design based on experience may have some limitations in practice. Based on the deep neural network model, the evacuation design simulation for subway station buildings is implemented. VR video tracking technologies such as auxiliary image data pre-training algorithm, tracking sequence pre-training algorithm, and recursive neural network model based on You Only Look Once (YOLO) are introduced. Compared with the convolutional neural network (CNN) model, the classified data set pre-training model, and YOLO algorithm, the accuracy and training speed of the model algorithm are verified. In simulation, the Zhujiang New Town Station in Guangzhou is taken as the object. The initial evacuation test point is selected according to the structure of the subway platform, and the test personnel are selected according to the test requirements. The average evacuation time and the average satisfaction score of the testers under the influence factors such as gender, age, subway frequency, and familiarity with VR equipment, as well as under the initial starting points of different evacuation tests. The results show that the accuracy of the algorithm is lower than that of the CNN, but the training speed is faster. The accuracy of the model based on YOLO recurrent neural network is the highest. Although the training speed is 19 ms, which is higher than other models, the overall performance is the best. Differences in factors such as gender, age, frequency of subway ride, and familiarity with VR devices will result in different differences in average evacuation time and average satisfaction score. When the platform center is used as the initial evacuation point, the average evacuation time is the shortest, and the average satisfaction score of the testers is the highest. In conclusion, through VR video tracking technology, the actual situation of subway station buildings can be well simulated, and further design schemes can be made according to the simulated situation, which has practical reference significance.", "Keywords": "Evacuation ; Deep learning ; VR video tracking method ; YOLO-based recursive neural network model ; Simulation", "DOI": "10.1016/j.imavis.2020.103975", "PubYear": 2020, "Volume": "102", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Architecture and Fine Art, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Architecture and Fine Art, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Art, Suzhou University of Science and Technology, Suzhou 116011, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Architecture and Fine Art, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Architecture and Fine Art, Dalian University of Technology, Dalian 116024, China;Corrseponding author"}], "References": []}, {"ArticleId": 83111246, "Title": "Improved many-objective particle swarm optimization algorithm for scientific workflow scheduling in cloud computing", "Abstract": "Optimized scientific workflow scheduling can greatly improve the overall performance of cloud computing. As workflow scheduling belongs to NP-complete problem, so, meta-heuristic approaches are more preferred option. Most studies on workflow scheduling in cloud mostly consider at most two or three objectives and there is a lack of effective studies and approaches on problems with more than three objectives remains; because the efficiency of multi-objective evolutionary algorithms (MOEAs) will seriously degrade when the number of objectives is more than three, which are often known as many-objective optimization problems (MaOPs). In this paper, an approach to solve workflow scheduling problem using Improved Many Objective Particle Swarm Optimization algorithm named I_MaOPSO is proposed considering four conflicting objectives namely maximization of reliability and minimization of cost, makespan and energy consumption. Specifically, we use four improvements to enhance the ability of MaOPSO to converge to the non-dominated solutions that apply a proper equilibrium between exploration and exploitation in scheduling process. The experimental results show that the proposed approach can improve up to 71%, 182%, 262% the HyperVolume (HV) criterion compared with the LEAF, MaOPSO, and EMS-C algorithms respectively. I_MaOPSO opens the way to develop a scheduler to deliver results with improved convergence and uniform spacing among the answers in compared with other counterparts and presents results that are more effective closer to non-dominated solutions.", "Keywords": "Cloud computing ; Many-objective PSO ; Workflow scheduling", "DOI": "10.1016/j.cie.2020.106649", "PubYear": 2020, "Volume": "147", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dolatabad Branch, Islamic Azad University, Isfahan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dolatabad Branch, Islamic Azad University, Isfahan, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Faculty of Engineering, University of Kashan, Kashan, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Mobarakeh Branch, Islamic Azad University, Mobarakeh, Isfahan, Iran"}], "References": [{"Title": "An adaptive scheduling approach based on integrated best-worst and VIKOR for cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106272", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Q-learning based dynamic task scheduling for energy-efficient cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "361", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Pareto dominance based Multiobjective Cohort Intelligence algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "538", "Issue": "", "Page": "69", "JournalTitle": "Information Sciences"}]}, {"ArticleId": ********, "Title": "Network prominence and e-store performance in social marketplace: A nuanced typology and empirical evidence", "Abstract": "This research provides a nuanced typology on seller’s network prominence in social marketplace, and then studies its effect on seller’s sales performance. Accounting for both directionality and the business role of the corresponding partners of seller’s social ties, we classify four types of seller’s network prominence, i.e. indegree centrality among buyers, indegree centrality among sellers, outdegree centrality among buyers, and outdegree centrality among sellers. By studying sales performance of 91,903 e-stores on etsy.com over two years, we report that indegree centralities positively affect seller’s sales performance with the effect of the indegree centrality among buyers much stronger than that of the indegree centrality among sellers; outdegree centralities negatively affect seller’s sales performance with comparable impacts.", "Keywords": "Network prominence ; Degree centrality ; Social marketplace ; Social network service (SNS) ; Social commerce performance", "DOI": "10.1016/j.elerap.2020.100991", "PubYear": 2020, "Volume": "43", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Finance and Management Science, University of Saskatchewan, 25 Campus Drive, Saskatoon, SK, S7N 5A7, Canada Lazaridis School of Business & Economics, Wilfrid Laurier University, 75 University Avenue West, Waterloo, Ontario, N2L 3C5, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Finance and Management Science, University of Saskatchewan, 25 Campus Drive, Saskatoon, SK, S7N 5A7, Canada Lazaridis School of Business & Economics, Wilfrid Laurier University, 75 University Avenue West, Waterloo, Ontario, N2L 3C5, Canada"}], "References": [{"Title": "How behaviors on social network sites and online social capital influence social commerce intentions", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "2", "Page": "103176", "JournalTitle": "Information & Management"}]}, {"ArticleId": 83111339, "Title": "QR Code based Indoor Navigation system for Attender Robot", "Abstract": "In this paper, we elicit a cost-effective design of a robot that is used to perform the tasks of the attender, a wheeled mobile robot. The two main challenging aspects of mobile robotics are to achieve localization and navigation. Localization determines the location of the robot with respect to its environment. Navigation is the movement of the robot from one location to the other. Here, the unique design and representation of the QR code helped us to localize and navigate the robot. A raspberry pi mounted with a camera is used to interface with the robot. The triangulation method is used to localize the robot. <PERSON><PERSON><PERSON>’s algorithm is used to compute the shortest path from source to destination. The system is monitored tested based on the feedback system established by the Hologram cellular USB modem. Experimental results show that this approach has good feasibility and effectiveness. The robot navigates through the shortest path, performs specified tasks, and returns to its source.", "Keywords": "", "DOI": "10.4108/eai.13-7-2018.165519", "PubYear": 2022, "Volume": "6", "Issue": "21", "JournalId": 46302, "JournalTitle": "EAI Endorsed Transactions on Internet of Things", "ISSN": "", "EISSN": "2414-1399", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Kuppili N. Sa<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83111651, "Title": "The Construction of Effective Multi-Dimensional Computer Designs of Experiments Based on a Quasi-Random Additive Recursive R<sub>d</sub>-sequence", "Abstract": "Abstract <p>Uniform multi-dimensional designs of experiments for effective research in computer modelling are highly demanded. The combinations of several one-dimensional quasi-random sequences with a uniform distribution are used to create designs with high homogeneity, but their optimal choice is a separate problem, the solution of which is not trivial. It is believed that now the best results are achieved using <PERSON><PERSON>’s LPτ-sequences, but this is not observed in all cases of their combinations. The authors proposed the creation of effective uniform designs with guaranteed acceptably low discrepancy using recursive Rd-sequences and not requiring additional research to find successful combinations of vectors set distributed in a single hypercube. The authors performed a comparative analysis of both approaches using indicators of centred and wrap-around discrepancies, graphical visualization based on <PERSON>oro<PERSON><PERSON> diagrams. The conclusion was drawn on the practical use of the proposed approach in cases where the requirements for the designs allowed restricting to its not ideal but close to it variant with low discrepancy, which was obtained automatically without additional research.</p>", "Keywords": "Computer designs of the experiment ; generalized discrepancy ; LP-sequence ; parameterless additive recursive Rd-sequences ; quasi-random expanding sequences ; uniformity of distribution ; V<PERSON><PERSON>i diagrams", "DOI": "10.2478/acss-2020-0009", "PubYear": 2020, "Volume": "25", "Issue": "1", "JournalId": 26179, "JournalTitle": "Applied Computer Systems", "ISSN": "2255-8683", "EISSN": "2255-8691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cherkasy State Technological University, Cherkasy, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cherkasy State Technological University, Cherkasy, Ukraine"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cherkasy State Technological University, Cherkasy, Ukraine"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cherkasy State Technological University, Cherkasy, Ukraine"}], "References": []}, {"ArticleId": 83111659, "Title": "Hand Gesture Recognition in Video Sequences Using Deep Convolutional and Recurrent Neural Networks", "Abstract": "Abstract <p>Deep learning is a new branch of machine learning, which is widely used by researchers in a lot of artificial intelligence applications, including signal processing and computer vision. The present research investigates the use of deep learning to solve the hand gesture recognition (HGR) problem and proposes two models using deep learning architecture. The first model comprises a convolutional neural network (CNN) and a recurrent neural network with a long short-term memory (RNN-LSTM). The accuracy of model achieves up to 82 % when fed by colour channel, and 89 % when fed by depth channel. The second model comprises two parallel convolutional neural networks, which are merged by a merge layer, and a recurrent neural network with a long short-term memory fed by RGB-D. The accuracy of the latest model achieves up to 93 %.</p>", "Keywords": "Computer Vision (CV) ; Convolutional Neural Network (CNN) ; Deep Learning ; Hand Gesture Recognition (HGR) ; Recurrent Neural Network with Long Short-Term Memory (RNN-LSTM)", "DOI": "10.2478/acss-2020-0007", "PubYear": 2020, "Volume": "25", "Issue": "1", "JournalId": 26179, "JournalTitle": "Applied Computer Systems", "ISSN": "2255-8683", "EISSN": "2255-8691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, University of Kashan, Kashan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Aalto University, Helsinki, Finland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering Department, University of Kashan, Kashan, Iran"}], "References": []}, {"ArticleId": 83111684, "Title": "A Prototype Model for Semantic Segmentation of Curvilinear Meandering Regions by Deconvolutional Neural Networks", "Abstract": "Abstract <p>Deconvolutional neural networks are a very accurate tool for semantic image segmentation. Segmenting curvilinear meandering regions is a typical task in computer vision applied to navigational, civil engineering, and defence problems. In the study, such regions of interest are modelled as meandering transparent stripes whose width is not constant. The stripe on the white background is formed by the upper and lower non-parallel black curves so that the upper and lower image parts are completely separated. An algorithm of generating datasets of such regions is developed. It is revealed that deeper networks segment the regions more accurately. However, the segmentation is harder when the regions become bigger. This is why an alternative method of the region segmentation consisting in segmenting the upper and lower image parts by subsequently unifying the results is not effective. If the region of interest becomes bigger, it must be squeezed in order to avoid segmenting the empty image. Once the squeezed region is segmented, the image is conversely rescaled to the original view. To control the accuracy, the mean BF score having the least value among the other accuracy indicators should be maximised first.</p>", "Keywords": "Curvilinear meandering region ; deconvolutional layer ; empty image segmentation ; mean BF score ; neural network ; overfitting ; semantic segmentation ; toy dataset", "DOI": "10.2478/acss-2020-0008", "PubYear": 2020, "Volume": "25", "Issue": "1", "JournalId": 26179, "JournalTitle": "Applied Computer Systems", "ISSN": "2255-8683", "EISSN": "2255-8691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Polish Naval Academy, Gdynia, Poland"}], "References": []}, {"ArticleId": 83111696, "Title": "Anemic Domain Model vs Rich Domain Model to Improve the Two-Hemisphere Model-Driven Approach", "Abstract": "Abstract <p>Evolution of software development process and increasing complexity of software systems calls for developers to pay great attention to the evolution of CASE tools for software development. This, in turn, causes explosion for appearance of a new wave (or new generation) of such CASE tools. The authors of the paper have been working on the development of the so-called two-hemisphere model-driven approach and its supporting BrainTool for the past 10 years. This paper is a step forward in the research on the ability to use the two-hemisphere model driven approach for system modelling at the problem domain level and to generate UML diagrams and software code from the two-hemisphere model. The paper discusses the usage of anemic domain model instead of rich domain model and offers the main principle of transformation of the two-hemisphere model into the first one.</p>", "Keywords": "Anemic domain model ; code generation ; model transformation ; rich domain model ; system modelling ; two-hemisphere model", "DOI": "10.2478/acss-2020-0006", "PubYear": 2020, "Volume": "25", "Issue": "1", "JournalId": 26179, "JournalTitle": "Applied Computer Systems", "ISSN": "2255-8683", "EISSN": "2255-8691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Riga Technical University, Riga, Latvia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Riga Technical University, Riga, Latvia"}], "References": []}, {"ArticleId": 83111876, "Title": "Sarcasm Detection Beyond using Lexical Features", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijdms.2020.12304", "PubYear": 2020, "Volume": "12", "Issue": "3", "JournalId": 16205, "JournalTitle": "International Journal of Database Management Systems", "ISSN": "0975-5985", "EISSN": "0975-5705", "Authors": [{"AuthorId": 1, "Name": " ADEWUYI", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": " OLADEJI", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83112027, "Title": "Mobile edge computation induced caching strategy for huge online education with college teachers and students", "Abstract": "<p>Via wireless network, mobile edge computation (MEC) provides computation ability to make user feel better. However, caching strategy restricts the performance of MEC. For instance, MEC requires to decide which course should be cached in huge asynchronous online education with college teachers and students. In order to solve the caching problem in asynchronous online education, we put the computation ability to network edge, then make the access points can analysis independently. In access points, we use a reduced support vector regression (rSVR) model to predict the popularity of local data. The popularity is used to improve the hit rate of the caching files. The rSVR model is learnt offline. Different SVR, rSVR is learnt on a small retained subset. Thus, learning rSVR is much faster and the rSVR's model is smaller than SVR's, which is helpful to improve the caching process. A real‐world asynchronous online learning dataset is used to verify effectiveness of our framework. The experimental results show that rSVR predicts popularity for local data is better than previous ones and our caching framework only requires near 50% storage space to maintain the same cache hit rate.</p>", "Keywords": "mobile edge computation;online education;reduced SVR", "DOI": "10.1002/itl2.208", "PubYear": 2021, "Volume": "4", "Issue": "1", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Changchun University of Science and Technology, Jilin, China"}], "References": []}, {"ArticleId": 83112274, "Title": "SPICE Behavioral Modeling of TiO2 Memristors for Digital Logic Applications", "Abstract": "<p>This work presents a behavior-based memristor model for the simulation of novel digital logic architectures. This model exhibits the nonvolatile hard switching current–voltage curves of the experimentally realized memristors. Because the model is implemented via the widely available traditional SPICE (Simulation Program with Integrated Circuit Emphasis) circuit components, its accuracy is not dependent on still-emerging device transport theory and auxiliary variables. The memristor model is used in material implication (IMPLY) gates to perform both combinational and sequential logics. As IMPLY gates exhibit the complete functionality required for digital logic, this work presents a simple realistic memristor model for use in the simulation of novel digital logic architectures.</p>", "Keywords": "", "DOI": "10.1142/S0218126621200024", "PubYear": 2021, "Volume": "30", "Issue": "4", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry and Physics, Mary Baldwin University, Staunton, VA 24401, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Farrah-<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83112424, "Title": "Benchmarking open source NFV MANO systems: OSM and ONAP", "Abstract": "With the increasing trend in softwarization and virtualization of network functions and systems, NFV management and orchestration (MANO) solutions are being developed to meet the agile and flexible management requirements of virtualized network services in the 5G era and beyond. In this regard, ETSI ISG NFV has specified a standard MANO system that is used as a reference by vendors as well as open-source MANO projects. These MANO systems are inherently very complex and have a direct impact on the overall performance of NFV systems. However, unlike traditional networking functions and systems, there are no well-defined test methods and KPIs based on which the performance of the NFV-MANO system can be tested, validated and benchmarked. Given the absence of formal MANO specific evaluation techniques based on which the performance and features of a MANO system can be quantified, and compared against, we introduce in this paper a formal benchmarking methodology and KPIs for MANO systems. For illustration purposes, we analyze and compare the performance of the two most popular open-source NFV MANO projects, namely ONAP and OSM, using a complex open-source virtual customer premises equipment (vCPE) VNF. Our results show the current features support, performance to be expected and gaps to be covered in future releases.", "Keywords": "NFV-MANO ; OSM ; ONAP ; Benchmarking", "DOI": "10.1016/j.comcom.2020.07.013", "PubYear": 2020, "Volume": "161", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NEC Laboratories GmbH, Kurfürsten-Anlage 36, 69115 Heidelberg, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "Zarrar <PERSON><PERSON>", "Affiliation": "NEC Laboratories GmbH, Kurfürsten-Anlage 36, 69115 Heidelberg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NEC Laboratories GmbH, Kurfürsten-Anlage 36, 69115 Heidelberg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "NEC Laboratories GmbH, Kurfürsten-Anlage 36, 69115 Heidelberg, Germany"}], "References": []}, {"ArticleId": 83112454, "Title": "DNA‐Based Chemical Reaction Networks for Biosensing Applications", "Abstract": "<p>The nature of biosensing is a biochemical reaction. DNA‐based chemical reaction networks (DNA‐CRNs) as a powerful programming language for describing behaviors of chemical reactions have shown great potential in designs and applications of biosensors. Due to their programmability, modularity, and versatility of the DNA strand, the performance of different detection strategies can be improved mainly by the rational design of DNA‐CRNs. Herein, an overview of the fundamental theory and biosensing processes of DNA‐CRNs is provided. Various detection strategies of DNA‐CRNs are introduced, either in a simple low‐order reaction model or in a complicated high‐order reaction type, in combination with some typical cases for the different detection purposes. In addition, an overview of the recent development of DNA‐CRNs for monitoring the cell microenvironment is presented, which is of significance to uncover some specific cell behaviors and functions. Finally, the roles of DNA‐CRNs in the rational design of high‐performance biosensors are summarized by pointing out the remaining challenges that impede the precise biosensing using DNA‐CRNs in complicated biological environments.</p>", "Keywords": "biosensors;chemical reaction networks;DNA-based chemical reaction networks", "DOI": "10.1002/aisy.202000086", "PubYear": 2020, "Volume": "2", "Issue": "9", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Sensing Technology and Biomedical Instruments of Guangdong Province, School of Biomedical Engineering, Sun Yat-Sen University, 132 East Waihuan Road, Higher Education Mega Center, Guangzhou, 510006 China"}, {"AuthorId": 2, "Name": "Li <PERSON>", "Affiliation": "Key Laboratory of Sensing Technology and Biomedical Instruments of Guangdong Province, School of Biomedical Engineering, Sun Yat-Sen University, 132 East Waihuan Road, Higher Education Mega Center, Guangzhou, 510006 China"}, {"AuthorId": 3, "Name": "Yizhou Jiang", "Affiliation": "Key Laboratory of Sensing Technology and Biomedical Instruments of Guangdong Province, School of Biomedical Engineering, Sun Yat-Sen University, 132 East Waihuan Road, Higher Education Mega Center, Guangzhou, 510006 China"}, {"AuthorId": 4, "Name": "<PERSON>wan <PERSON>", "Affiliation": "Key Laboratory of Sensing Technology and Biomedical Instruments of Guangdong Province, School of Biomedical Engineering, Sun Yat-Sen University, 132 East Waihuan Road, Higher Education Mega Center, Guangzhou, 510006 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Sensing Technology and Biomedical Instruments of Guangdong Province, School of Biomedical Engineering, Sun Yat-Sen University, 132 East Waihuan Road, Higher Education Mega Center, Guangzhou, 510006 China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Sensing Technology and Biomedical Instruments of Guangdong Province, School of Biomedical Engineering, Sun Yat-Sen University, 132 East Waihuan Road, Higher Education Mega Center, Guangzhou, 510006 China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Sensing Technology and Biomedical Instruments of Guangdong Province, School of Biomedical Engineering, Sun Yat-Sen University, 132 East Waihuan Road, Higher Education Mega Center, Guangzhou, 510006 China"}], "References": []}, {"ArticleId": 83112560, "Title": "UV‐free Texturing using Sparse Voxel DAGs", "Abstract": "<p>An application may have to load an unknown 3D model and, for enhanced realistic rendering, precompute values over the surface domain, such as light maps, ambient occlusion, or other global‐illumination parameters. High‐quality uv‐unwrapping has several problems, such as seams, distortions, and wasted texture space. Additionally, procedurally generated scene content, perhaps on the fly, can make manual uv unwrapping impossible. Even when artist manipulation is feasible, good uv layouts can require expertise and be highly labor intensive.</p> <p>This paper investigates how to use Sparse Voxel DAGs (or DAGs for short) as one alternative to avoid uv mapping. The result is an algorithm enabling high compression ratios of both voxel structure and colors, which can be important for a baked scene to fit in GPU memory. Specifically, we enable practical usage for an automatic system by targeting efficient real‐time mipmap filtering using compressed textures and adding support for individual mesh voxelizations and resolutions in the same DAG. Furthermore, the latter increases the texture‐compression ratios by up to 32 % compared to using one global voxelization, DAG compression by 10 – 15 % compared to using a DAG per mesh, and reduces color‐bleeding problems for large mipmap filter sizes.</p> <p>The voxel‐filtering is more costly than standard hardware 2D‐texture filtering. However, for full HD with deferred shading, it is optimized down to 2.5 ± 0.5 ms for a custom multisampling filtering (e.g., targeted for minification of low‐frequency textures) and 5 ± 2 ms for quad‐linear mipmap filtering (e.g., for high‐frequency textures). Multiple textures sharing voxelization can amortize the majority of this cost. Hence, these numbers involve 1–3 textures per pixel (Fig. 1c).</p>", "Keywords": "CCS Concepts;• Computing methodologies → Texturing", "DOI": "10.1111/cgf.13917", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chalmers University of Technology, Sweden"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chalmers University of Technology, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Chalmers University of Technology, Sweden"}], "References": []}, {"ArticleId": 83112561, "Title": "Robust Shape Collection Matching and Correspondence from Shape Differences", "Abstract": "<p>We propose a method to automatically match two shape collections with a similar shape space structure, e.g. two characters in similar poses, and compute the inter‐maps between the collections. Given the intra‐maps in each collection, we extract the corresponding shape difference operators , and use them to construct an embedding of the shape space of each collection. We then align the two shape spaces, and use the knowledge gained from the alignment to compute the inter‐maps. Unlike existing approaches for collection alignment, our method is applicable to small and large collections alike, and requires no parameter tuning. Furthermore, unlike most approaches for non‐isometric correspondence, our method uses solely the variation within the collection to extract the inter‐maps, and therefore does not require landmarks, descriptors or any additional input. We demonstrate that we achieve high matching accuracy rates, and compute high quality maps on non‐isometric shapes, which compare favorably with automatic state‐of‐the‐art methods for non‐isometric shape correspondence.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Shape analysis", "DOI": "10.1111/cgf.13952", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technion ‐ Israel Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Technion ‐ Israel Institute of Technology"}], "References": []}, {"ArticleId": 83112562, "Title": "Spectral Mollification for Bidirectional Fluorescence", "Abstract": "<p>Fluorescent materials can shift energy between wavelengths, thereby creating bright and saturated colors both in natural and artificial materials. However, rendering fluorescence for continuous wavelengths or combined with wavelength dependent path configurations so far has only been feasible using spectral unidirectional methods. We present a regularization‐based approach for supporting fluorescence in a spectral bidirectional path tracer. Our algorithm samples camera and light sub‐paths with independent wavelengths, and when connecting them mollifies the BSDF at one of the connecting vertices such that it reradiates light across multiple wavelengths. We discuss arising issues such as color bias in early iterations, consistency of the method and MIS weights in the presence of spectral mollification. We demonstrate our method in scenes combining fluorescence and transport phenomena that are difficult to render with unidirectional or spectrally discrete methods.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Ray tracing;Reflectance modeling", "DOI": "10.1111/cgf.13937", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology"}], "References": []}, {"ArticleId": 83112564, "Title": "Binary Ostensibly‐Implicit Trees for Fast Collision Detection", "Abstract": "<p>We present a simple, efficient and low‐memory technique, targeting fast construction of bounding volume hierarchies (BVH) for broad‐phase collision detection. To achieve this, we devise a novel representation of BVH trees in memory. We develop a mapping of the implicit index representation to compact memory locations, based on simple bit‐shifts, to then construct and evaluate bounding volume test trees (BVTT) during collision detection with real‐time performance. We model the topology of the BVH tree implicitly as binary encodings which allows us to determine the nodes missing from a complete binary tree using the binary representation of the number of missing nodes. The simplicity of our technique allows for fast hierarchy construction achieving over 6× speedup over the state‐of‐the‐art. Making use of these characteristics, we show that not only it is feasible to rebuild the BVH at every frame, but that using our technique, it is actually faster than refitting and more memory efficient.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Collision detection", "DOI": "10.1111/cgf.13948", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Edinburgh"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Edinburgh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Edinburgh"}], "References": []}, {"ArticleId": 83112565, "Title": "Locally supported tangential vector, n ‐vector, and tensor fields", "Abstract": "<p>We introduce a construction of subspaces of the spaces of tangential vector, n‐vector, and tensor fields on surfaces. The resulting subspaces can be used as the basis of fast approximation algorithms for design and processing problems that involve tangential fields. Important features of our construction are that it is based on a general principle, from which constructions for different types of tangential fields can be derived, and that it is scalable, making it possible to efficiently compute and store large subspace bases for large meshes. Moreover, the construction is adaptive, which allows for controlling the distribution of the degrees of freedom of the subspaces over the surface. We evaluate our construction in several experiments addressing approximation quality, scalability, adaptivity, computation times and memory requirements. Our design choices are justified by comparing our construction to possible alternatives. Finally, we discuss examples of how subspace methods can be used to build interactive tools for tangential field design and processing tasks.</p>", "Keywords": "", "DOI": "10.1111/cgf.13924", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Delft University of Technology, The Netherlands; Department of Electrical and Information Engineering, Universitas Gadjah Mada, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Delft University of Technology, The Netherlands"}], "References": []}, {"ArticleId": 83112566, "Title": "Spectral Mesh Simplification", "Abstract": "<p>The spectrum of the <PERSON><PERSON><PERSON> operator is instrumental for a number of geometric modeling applications, from processing to analysis. Recently, multiple methods were developed to retrieve an approximation of a shape that preserves its eigenvectors as much as possible, but these techniques output a subset of input points with no connectivity, which limits their potential applications. Furthermore, the obtained Laplacian results from an optimization procedure, implying its storage alongside the selected points. Focusing on keeping a mesh instead of an operator would allow to retrieve the latter using the standard cotangent formulation, enabling easier processing afterwards. Instead, we propose to simplify the input mesh using a spectrum‐preserving mesh decimation scheme, so that the Laplacian computed on the simplified mesh is spectrally close to the one of the input mesh. We illustrate the benefit of our approach for quickly approximating spectral distances and functional maps on low resolution proxies of potentially high resolution input meshes.</p>", "Keywords": "", "DOI": "10.1111/cgf.13932", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>i <PERSON>", "Affiliation": "University of Toronto, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Toronto, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe; LTCI, Télécom Paris, Institut Polytechnique de Paris, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "École Polytechnique, France"}], "References": []}, {"ArticleId": 83112573, "Title": "Interactive Meso‐scale Simulation of Skyscapes", "Abstract": "<p>Although an important component of natural scenes, the representation of skyscapes is often relatively simplistic. This can be largely attributed to the complexity of the thermodynamics underpinning cloud evolution and wind dynamics, which make interactive simulation challenging. We address this problem by introducing a novel layered model that encompasses both terrain and atmosphere, and supports efficient meteorological simulations. The vertical and horizontal layer resolutions can be tuned independently, while maintaining crucial inter‐layer thermodynamics, such as convective circulation and land‐air transfers of heat and moisture. In addition, we introduce a cloud‐form taxonomy for clustering, classifying and upsampling simulation cells to enable visually plausible, finely‐sampled volumetric rendering.</p> <p>As our results demonstrate, this pipeline allows interactive simulation followed by up‐sampled rendering of extensive skyscapes with dynamic clouds driven by consistent wind patterns. We validate our method by reproducing characteristic phenomena such as diurnal shore breezes, convective cells that contribute to cumulus cloud formation, and orographic effects from moist air driven upslope.</p>", "Keywords": "Eulerian simulation;procedural modeling", "DOI": "10.1111/cgf.13954", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Cape Town"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of Cape Town"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LIX, École Polytechnique, CNRS"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ETH Zürich"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Environmental and Geographical Sciences Department, University of Cape Town"}, {"AuthorId": 6, "Name": "Marie<PERSON>Paul<PERSON>", "Affiliation": "LIX, École Polytechnique, CNRS"}], "References": []}, {"ArticleId": 83112640, "Title": "Style‐Controllable Speech‐Driven Gesture Synthesis Using Normalising Flows", "Abstract": "<p>Automatic synthesis of realistic gestures promises to transform the fields of animation, avatars and communicative agents. In off‐line applications, novel tools can alter the role of an animator to that of a director, who provides only high‐level input for the desired animation; a learned network then translates these instructions into an appropriate sequence of body poses. In interactive scenarios, systems for generating natural animations on the fly are key to achieving believable and relatable characters. In this paper we address some of the core issues towards these ends. By adapting a deep learning‐based motion synthesis method called MoGlow, we propose a new generative model for generating state‐of‐the‐art realistic speech‐driven gesticulation. Owing to the probabilistic nature of the approach, our model can produce a battery of different, yet plausible, gestures given the same input speech signal. Just like humans, this gives a rich natural variation of motion. We additionally demonstrate the ability to exert directorial control over the output style, such as gesture level, speed, symmetry and spacial extent. Such control can be leveraged to convey a desired character personality or mood. We achieve all this without any manual annotation of the data. User studies evaluating upper‐body gesticulation confirm that the generated motions are natural and well match the input speech. Our method scores above all prior systems and baselines on these measures, and comes close to the ratings of the original recorded motions. We furthermore find that we can accurately control gesticulation styles without unnecessarily compromising perceived naturalness. Finally, we also demonstrate an application of the same method to full‐body gesticulation, including the synthesis of stepping motion and stance.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Motion capture; Animation;Neural networks;Gestures;Motion capture;Data‐driven animation;Character control;Probabilistic models", "DOI": "10.1111/cgf.13946", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Speech, Music and Hearing, KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Speech, Music and Hearing, KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Speech, Music and Hearing, KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Division of Speech, Music and Hearing, KTH Royal Institute of Technology, Stockholm, Sweden"}], "References": []}, {"ArticleId": 83112656, "Title": "SoftSMPL: Data‐driven Modeling of Nonlinear Soft‐tissue Dynamics for Parametric Humans", "Abstract": "<p>We present SoftSMPL, a learning‐based method to model realistic soft‐tissue dynamics as a function of body shape and motion. Datasets to learn such task are scarce and expensive to generate, which makes training models prone to overfitting. At the core of our method there are three key contributions that enable us to model highly realistic dynamics and better generalization capabilities than state‐of‐the‐art methods, while training on the same data. First, a novel motion descriptor that disentangles the standard pose representation by removing subject‐specific features; second, a neural‐network‐based recurrent regressor that generalizes to unseen shapes and motions; and third, a highly efficient nonlinear deformation subspace capable of representing soft‐tissue deformations of arbitrary shapes. We demonstrate qualitative and quantitative improvements over existing methods and, additionally, we show the robustness of our method on a variety of motion capture databases.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Animation", "DOI": "10.1111/cgf.13912", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}], "References": [{"Title": "Muscle and Fascia Simulation with Extended Position Based Dynamics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "1", "Page": "134", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 83112674, "Title": "Front Matter", "Abstract": "", "Keywords": "", "DOI": "10.1111/cgf.14025", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 83112675, "Title": "Optimizing Object Decomposition to Reduce Visual Artifacts in 3D Printing", "Abstract": "<p>We propose a method for the automatic segmentation of 3D objects into parts which can be individually 3D printed and then reassembled by preserving the visual quality of the final object. Our technique focuses on minimizing the surface affected by supports, decomposing the object into multiple parts whose printing orientation is automatically chosen. The segmentation reduces the visual impact on the fabricated model producing non‐planar cuts that adapt to the object shape. This is performed by solving an optimization problem that balances the effects of supports and cuts, while trying to place both in occluded regions of the object surface. To assess the practical impact of the solution, we show a number of segmented, 3D printed and reassembled objects.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Shape modeling;Mesh geometry models;Shape analysis", "DOI": "10.1111/cgf.13941", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ISTI – CNR"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ISTI – CNR; University of Pisa"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ISTI – CNR"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "ISTI – CNR"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "ISTI – CNR"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "ISTI – CNR"}], "References": []}, {"ArticleId": 83112676, "Title": "Prefilters for Sharp Image Display", "Abstract": "<p>In this paper we use a simplified model of the human visual system to explain why humans tend do prefer “sharpened” digital images. From this model we then derive a family of image prefilters specifically adapted to viewing conditions and user preference, allowing for the trade‐off between ringing and aliasing while maximizing image sharpness. We discuss how our filters can be applied in a variety of situations ranging from Monte Carlo rendering to image downscaling, and we show how they consistently give sharper results while having an efficient implementation and ease of use (there are no free parameters that require manual tuning). We demonstrate the effectiveness of our simple sharp prefilters through a user study that indicates a clear preference to our approach compared to the state‐of‐the‐art.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Image processing", "DOI": "10.1111/cgf.13942", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Go<PERSON>ia R<PERSON>", "Affiliation": "Instituto de Informática – UFRGS"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto de Informática – UFRGS"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto de Informática – UFRGS"}], "References": []}, {"ArticleId": 83112677, "Title": "Modeling and Estimation of Nonlinear Skin Mechanics for Animated Avatars", "Abstract": "<p>Data‐driven models of human avatars have shown very accurate representations of static poses with soft‐tissue deformations. However they are not yet capable of precisely representing very nonlinear deformations and highly dynamic effects. Nonlinear skin mechanics are essential for a realistic depiction of animated avatars interacting with the environment, but controlling physics‐only solutions often results in a very complex parameterization task. In this work, we propose a hybrid model in which the soft‐tissue deformation of animated avatars is built as a combination of a data‐driven statistical model, which kinematically drives the animation, an FEM mechanical simulation. Our key contribution is the definition of deformation mechanics in a reference pose space by inverse skinning of the statistical model. This way, we retain as much as possible of the accurate static data‐driven deformation and use a custom anisotropic nonlinear material to accurately represent skin dynamics. Model parameters including the heterogeneous distribution of skin thickness and material properties are automatically optimized from 4D captures of humans showing soft‐tissue deformations.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Physical simulation", "DOI": "10.1111/cgf.13913", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}], "References": []}, {"ArticleId": 83112680, "Title": "Single Sensor Compressive Light Field Video Camera", "Abstract": "<p>This paper presents a novel compressed sensing (CS) algorithm and camera design for light field video capture using a single sensor consumer camera module. Unlike microlens light field cameras which sacrifice spatial resolution to obtain angular information, our CS approach is designed for capturing light field videos with high angular, spatial, and temporal resolution. The compressive measurements required by CS are obtained using a random color‐coded mask placed between the sensor and aperture planes. The convolution of the incoming light rays from different angles with the mask results in a single image on the sensor; hence, achieving a significant reduction on the required bandwidth for capturing light field videos. We propose to change the random pattern on the spectral mask between each consecutive frame in a video sequence and extracting spatio‐angular‐spectral‐temporal 6D patches. Our CS reconstruction algorithm for light field videos recovers each frame while taking into account the neighboring frames to achieve significantly higher reconstruction quality with reduced temporal incoherencies, as compared with previous methods. Moreover, a thorough analysis of various sensing models for compressive light field video acquisition is conducted to highlight the advantages of our method. The results show a clear advantage of our method for monochrome sensors, as well as sensors with color filter arrays.</p>", "Keywords": "CCS Concepts;• Computer graphics → Computational photography;Image compression", "DOI": "10.1111/cgf.13944", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Linköping University, Sweden"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inria, Rennes, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Inria, Rennes, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Linköping University, Sweden"}], "References": []}, {"ArticleId": 83112695, "Title": "Persistence Analysis of Multi‐scale Planar Structure Graph in Point Clouds", "Abstract": "<p>Modern acquisition techniques generate detailed point clouds that sample complex geometries. For instance, we are able to produce millimeter‐scale acquisition of whole buildings. Processing and exploring geometrical information within such point clouds requires scalability, robustness to acquisition defects and the ability to model shapes at different scales. In this work, we propose a new representation that enriches point clouds with a multi‐scale planar structure graph. We define the graph nodes as regions computed with planar segmentations at increasing scales and the graph edges connect regions that are similar across scales. Connected components of the graph define the planar structures present in the point cloud within a scale interval. For instance, with this information, any point is associated to one or several planar structures existing at different scales. We then use topological data analysis to filter the graph and provide the most prominent planar structures.</p> <p>Our representation naturally encodes a large range of information. We show how to efficiently extract geometrical details (e.g. tiles of a roof), arrangements of simple shapes (e.g. steps and mean ramp of a staircase), and large‐scale planar proxies (e.g. walls of a building) and present several interactive tools to visualize, select and reconstruct planar primitives directly from raw point clouds. The effectiveness of our approach is demonstrated by an extensive evaluation on a variety of input data, as well as by comparing against state‐of‐the‐art techniques and by showing applications to polygonal mesh reconstruction.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Point‐based models; Shape analysis", "DOI": "10.1111/cgf.13910", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Université de Toulouse, CNRS"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, University of Zurich"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Université de Toulouse, CNRS"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Université de Toulouse, CNRS"}], "References": []}, {"ArticleId": 83112696, "Title": "Asynchronous Eulerian Liquid Simulation", "Abstract": "<p>We present a novel method for simulating liquid with asynchronous time steps on Eulerian grids. Previous approaches focus on Smoothed Particle Hydrodynamics (SPH), Material Point Method (MPM) or tetrahedral Finite Element Method (FEM) but the method for simulating liquid purely on Eulerian grids have not yet been investigated. We address several challenges specifically arising from the Eulerian asynchronous time integrator such as regional pressure solve, asynchronous advection, interpolation, regional volume preservation, and dedicated segregation of the simulation domain according to the liquid velocity. We demonstrate our method on top of staggered grids combined with the level set method and the semi‐Lagrangian scheme. We run several examples and show that our method considerably outperforms the global adaptive time step method with respect to the computational runtime on scenes where a large variance of velocity is present.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Physical simulation", "DOI": "10.1111/cgf.13907", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Waseda University, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Waseda University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Japan"}], "References": []}, {"ArticleId": 83112697, "Title": "RGB2AO: Ambient Occlusion Generation from RGB Images", "Abstract": "<p>We present RGB2AO, a novel task to generate ambient occlusion (AO) from a single RGB image instead of screen space buffers such as depth and normal. RGB2AO produces a new image filter that creates a non‐directional shading effect that darkens enclosed and sheltered areas. RGB2AO aims to enhance two 2D image editing applications: image composition and geometry‐aware contrast enhancement. We first collect a synthetic dataset consisting of pairs of RGB images and AO maps. Subsequently, we propose a model for RGB2AO by supervised learning of a convolutional neural network (CNN), considering 3D geometry of the input image. Experimental results quantitatively and qualitatively demonstrate the effectiveness of our model.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Image‐based rendering", "DOI": "10.1111/cgf.13943", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research, U.S."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research, U.S."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research, U.S."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research, U.S."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo, Japan"}], "References": []}, {"ArticleId": 83112724, "Title": "On a tropical version of the Jacobian conjecture", "Abstract": "We prove that, for a tropical rational map if for any point the convex hull of Jacobian matrices at smooth points in a neighborhood of the point does not contain singular matrices then the map is an isomorphism. We also show that a tropical polynomial map on the plane is an isomorphism if all the Jacobians have the same sign (positive or negative). In addition, for a tropical rational map we prove that if the Jacobians have the same sign and if its preimage is a singleton at least at one regular point then the map is an isomorphism.", "Keywords": "Tropical Jacobian conjecture ; Tropical rational mappings", "DOI": "10.1016/j.jsc.2020.07.012", "PubYear": 2022, "Volume": "109", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CNRS, Mathématiques, Université de Lille, Villeneuve d'Ascq, 59655, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Max <PERSON>ck Institute for Mathematics, Bonn, Germany"}], "References": []}, {"ArticleId": 83112726, "Title": "Foreword: Special issue of JSC on the occasion of MEGA 2019", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.jsc.2020.07.001", "PubYear": 2022, "Volume": "109", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Trento, 38123 Trento, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departament de Matemàtiques i Informàtica, Universitat de Barcelona, 08007 Barcelona, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematics, Goethe University, 60054 Frankfurt, Germany"}], "References": []}, {"ArticleId": 83112731, "Title": "Process mining as support to simulation modeling: A hospital-based case study", "Abstract": "The purpose of this paper is to show how the knowledge of Process Mining techniques can provide a robust premise to build a Discrete Event Simulation (DES) model of a healthcare process. In order to analyze some specific processes of an ophthalmology ward of a large Italian hospital, ProM6 framework was implemented, which supports process mining techniques in form of plug-ins; the plug-ins process data, in form of Event Log, in order to extract information about the process. The DES model based on such information was run via a commercial tool, Simul8, which allows building sophisticated process models. A timely algorithm was then deployed to adapt the ProM6 information to the DES model. Following, a conformance analysis was conducted by comparing the original Event Log, and the Simulation tool data, taking into account the main case-related model attributes (routing probabilities profile, time perspective, and resources perspective). The paper aims at developing the line of inquiry for what concerns the deployment of approaches to set forth the link between Process mining and Simulation modeling.", "Keywords": "Discrete event simulation ; Healthcare ; Petri-nets ; Process Mining ; ProM6 ; Simul8", "DOI": "10.1016/j.simpat.2020.102149", "PubYear": 2020, "Volume": "104", "Issue": "", "JournalId": 1087, "JournalTitle": "Simulation Modelling Practice and Theory", "ISSN": "1569-190X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Veterinary Medicine and Animal Productions, University of Naples Federico II, Via Delpino 1, 80137 Naples, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, 84084 Fisciano, SA, Italy"}], "References": [{"Title": "Establishment of maintenance inspection intervals: an application of process mining techniques in manufacturing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "1", "Page": "53", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 83112840, "Title": "Finding a place for equity in CSCL: ambitious learning practices as a lever for sustained educational change", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11412-020-09325-3", "PubYear": 2020, "Volume": "15", "Issue": "3", "JournalId": 29983, "JournalTitle": "International Journal of Computer-Supported Collaborative Learning", "ISSN": "1556-1607", "EISSN": "1556-1615", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Research on Learning and Technology, School of Education, Indiana University, Bloomington, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Research on Learning and Technology, School of Education, Indiana University, Bloomington, USA"}, {"AuthorId": 3, "Name": "<PERSON>-Silver", "Affiliation": "Center for Research on Learning and Technology, School of Education, Indiana University, Bloomington, USA"}], "References": []}, {"ArticleId": 83112984, "Title": "A New Hybrid Diversity Combining Scheme for Mobile Radio Communication Systems over Nakagami Fading Chanel", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijcsit.2020.12301", "PubYear": 2020, "Volume": "12", "Issue": "3", "JournalId": 20755, "JournalTitle": "International Journal of Computer Science and Information Technology", "ISSN": "0975-4660", "EISSN": "0975-3826", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>yebu<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Nnaemeka <PERSON>emez<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83113372, "Title": "Optimized task distribution based on task requirements and time delay in edge computing environments", "Abstract": "Edge computing is a new technology for completing real-time and complex tasks with low latency. However, due to limited storage, computing and communication capabilities of edge nodes, it is often necessary for multiple edge nodes to share a task’s related work load to decrease its overall execution time. To solve the problem, this paper proposes a task distribution method based on the analysis of task requirements and time delay in an edge computing environment. First, the related data is received by a proxy server to obtain the running states of edge nodes. Then, a task-based edge node selection algorithm is designed to select appropriate target edge nodes. It can meet task requirements by using a Bloom filter to filter malicious nodes. Finally, based on the above selected nodes, optimized target edge nodes are selected to achieve the minimum time delay. Based on the selected optimized target edge nodes, this paper proposes an algorithm to optimize task distribution for meeting task requirements and achieve the minimum delay in an edge computing environment. Because the method considers both task requirements and time delay, it can distribute tasks to target nodes at low cost. The experimental results show that the method is feasible and effective and outperforms two commonly-used methods", "Keywords": "Edge computing ; Node selection ; Bloom filter ; Task distribution", "DOI": "10.1016/j.engappai.2020.103774", "PubYear": 2020, "Volume": "94", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information, Anhui Normal University, Wuhu 241002, China;Correspondence to: 189 Jiuhua South Rd., Wuhu, Anhui Province 241002, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electronic Information, Anhui Normal University, Wuhu 241003, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information, Anhui Normal University, Wuhu 241002, China"}], "References": []}, {"ArticleId": 83113469, "Title": "Uniform stability of nonlinear time-varying impulsive systems with eventually uniformly bounded impulse frequency", "Abstract": "We provide novel sufficient conditions for stability of nonlinear and time-varying impulsive systems. These conditions generalize, extend, and strengthen many existing results. Different types of input-to-state stability (ISS), as well as zero-input global uniform asymptotic stability (0-GUAS), are covered by employing a two-measure framework and considering stability of both weak (decay depends only on elapsed time) and strong (decay depends on elapsed time and the number of impulses) flavors. By contrast to many existing results, the stability state bounds imposed are uniform with respect to initial time and also with respect to classes of impulse-time sequences where the impulse frequency is eventually uniformly bounded. We show that the considered classes of impulse-time sequences are substantially broader than other previously considered classes, such as those having fixed or (reverse) average dwell times, or impulse frequency achieving uniform convergence to a limit (superior or inferior). Moreover, our sufficient conditions are stronger, less conservative and more widely applicable than many existing results.", "Keywords": "Impulsive systems ; Nonlinear systems ; Time-varying systems ; Input-to-state stability ; Hybrid systems", "DOI": "10.1016/j.nahs.2020.100933", "PubYear": 2020, "Volume": "38", "Issue": "", "JournalId": 5866, "JournalTitle": "Nonlinear Analysis: Hybrid Systems", "ISSN": "1751-570X", "EISSN": "1878-7460", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto Tecnológico de Buenos Aires, Av. E. <PERSON> 399, Buenos Aires, Argentina"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "International Center for Information and Systems Science (CIFASIS), CONICET-UNR, Ocampo y Esmeralda, 2000 Rosario, Argentina;Corresponding author"}, {"AuthorId": 3, "Name": "Pet<PERSON>", "Affiliation": "Kiel University, Chair of Automatic Control, Kaiserstraße 2, 24143 Kiel, Germany"}], "References": []}, {"ArticleId": 83114254, "Title": "CliNCare: An Educational Game—The Reasoning Behind the Graphic Choices and Their Impact on Player Opinions", "Abstract": "<p>CliNCare is a digital educational game which aims to improve clinical reasoning skills in the delivery of nutrition care of third year dietetic students. From a game art point of view, it was important to decide on the graphical style that would be appealing to users and best support the achievement of the overall aim of the game by providing an authentic learning experience. Photorealism is a common art style applied to contemporary simulation games, however, we wanted to investigate the use of 3D cel-shading and 2D illustration as an alternative, more affordable art style and its ability to provide an engaging and authentic digital learning experience using design-based research principals. This paper discusses the reasoning behind art design choices made in the CliNCare game, presents findings on students feedback of the game fidelity, experience, and usability, as well as draw conclusions on art styles for future digital simulation games.</p>", "Keywords": "3D; Cel-shading; 2D; Illustrations; Photorealism; Cartoon style; Game art; Game design", "DOI": "10.1007/s40869-020-00111-7", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 6750, "JournalTitle": "The Computer Games Journal", "ISSN": "", "EISSN": "2052-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of the Sunshine Coast, Sunshine Coast, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of the Sunshine Coast, Sunshine Coast, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of the Sunshine Coast, Sunshine Coast, Australia"}], "References": []}, {"ArticleId": 83114335, "Title": "Intra-swarm migration of size-variable robotic modules utilizing the Brazil nut effect", "Abstract": "In this work, we apply physical granular segregation phenomena to the problem of intra-swarm module migration in a robotic swarm. These segregation phenomena allow for complex relative motion between interacting particles on the basis of differing physical properties. In particular, the <i>Brazil nut effect</i> is one such phenomenon that causes segregation of particles based on relative size difference. We simulated size-variable modules in order to take advantage of this effect and induce intra-swarm module migration. Intra-swarm module migration refers to the movement of the modules within the confines of the swarm, relative to each other, as well as the swarm container. We introduced two methods to produce different kinds of module movement. The first of these, called the <i>flashlight</i> method, induced movement of an individual module relative to its neighbours. The second, called the <i>zone</i> method, induced an orientation-aligned global module motion within the swarm. In this work, we evaluate the effects of different parameters on the nature of the movements induced by these methods. <img src=\"//:0\" data-src='{\"type\":\"image\",\"src\":\"/na101/home/<USER>/publisher/tandf/journals/content/tadr20/2020/tadr20.v034.i17/01691864.2020.1789503/20200829/images/medium/tadr_a_1789503_uf0001_oc.jpg\"}' />", "Keywords": "Swarm robotics ; size-variable modules ; Brazil nut effect ; granular segregation", "DOI": "10.1080/01691864.2020.1789503", "PubYear": 2020, "Volume": "34", "Issue": "17", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering Science, Osaka University, Toyonaka, Japan"}], "References": []}, {"ArticleId": 83114378, "Title": "Ethiopian <i>Enset</i> Diseases Diagnosis Model Using Image Processing and Machine Learning Techniques", "Abstract": "The diagnosis of diseases on the plant is a very important to provide large quantity and good qualitative agricultural products. Enset is an important food crops produced in Southern parts of the Ethiopia with great role in food security. There are several issues and diseases which try to decline the yield with quality. Particularly, diagnosis of potential diseases on Enset is based on traditional ways. The aim of this study is to design a model for Enset diseases diagnosis using Image processing and Multiclass SVM techniques. This study presented a general process model to classify a given Enset leaf image as normal or infected. The strategy of K-fold stratified cross validation was used to enhance generalization of the model. This diagnosis apply K-means clustering, color distribution, shape measurements, Gabor texture extraction and wavelet transform as key approaches for image processing techniques. The researcher selected two Enset leaf diseases viz. Bacterial Wilt and Fusarium Wilt disease and collected 430 Enset leaf images from Areka agricultural research center and some selected areas in SNNPR. For this research work MATLAB version R2017a tool was used as a platform to simulate the real world data. The proposed model demonstrated with four different kernels, and the overall result indicates that the RBF Kernel achieves the highest accuracy as 94.04% and 92.44% for bacterial wilt and fusarium wilt respectively. Therefore, an efficient practice of IT based solution in this domain will increases productivity and quality of Enset products.", "Keywords": "Multiclass SVM; Kernels; Enset Disease; K-means Clustering; Image Processing", "DOI": "10.11648/j.ijiis.20200901.11", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 27209, "JournalTitle": "International Journal of Intelligent Information Systems", "ISSN": "2328-7675", "EISSN": "2328-7683", "Authors": [{"AuthorId": 1, "Name": "Kibru Abera Ganore", "Affiliation": "Department of Computer Science, Wachemo University, Addis Ababa, Ethiopia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing and Software Engineering, Arba Minch University, Arba Minch, Ethiopia"}], "References": []}, {"ArticleId": 83114569, "Title": "Spectral evolution with approximated eigenvalue trajectories for link prediction", "Abstract": "<p>The spectral evolution model aims to characterize the growth of large networks (i.e., how they evolve as new edges are established) in terms of the eigenvalue decomposition of the adjacency matrices. It assumes that, while eigenvectors remain constant, eigenvalues evolve in a predictable manner over time. This paper extends the original formulation of the model twofold. First, it presents a method to compute an approximation of the spectral evolution of eigenvalues based on the Rayleigh quotient. Second, it proposes an algorithm to estimate the evolution of eigenvalues by extrapolating only a fraction of their approximated values. The proposed model is used to characterize mention networks of users who posted tweets that include the most popular political hashtags in Colombia from August 2017 to August 2018 (the period which concludes the disarmament of the Revolutionary Armed Forces of Colombia). To evaluate the extent to which the spectral evolution model resembles these networks, link prediction methods based on learning algorithms (i.e., extrapolation and regression) and graph kernels are implemented. Experimental results show that the learning algorithms deployed on the approximated trajectories outperform the usual kernel and extrapolation methods at predicting the formation of new edges.</p>", "Keywords": "Link prediction; Spectral evolution model; Twitter mention networks; Spectral decomposition; Rayleigh quotient; Graph kernels", "DOI": "10.1007/s13278-020-00674-3", "PubYear": 2020, "Volume": "10", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Computer Science, Pontificia Universidad Javeriana, Cali, Colombia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Computer Science, Pontificia Universidad Javeriana, Cali, Colombia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Computer Science, Pontificia Universidad Javeriana, Cali, Colombia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Computer Science, Pontificia Universidad Javeriana, Cali, Colombia"}], "References": []}, {"ArticleId": 83114573, "Title": "The enforcement of laws regulating digital cultural content: a proposal", "Abstract": "Law is becoming increasingly digital in nature, and the State is increasingly using digital technology for the purposes of physical legal enforcement. This paper argues that this poses unique issues for legal regulation of cultural content, changing the relationship between the individual and the State. The paper focuses on laws in the UK and China to demonstrate how these issues have arisen, and what the potential consequences of this change could be. The paper culminates in arguing that legislatures and courts should be required to explicitly consider how digital technology influences, in a given law or case, the enforcement of law.", "Keywords": "China copyright censorship", "DOI": "10.1080/13600869.2020.1784567", "PubYear": 2021, "Volume": "35", "Issue": "1", "JournalId": 25543, "JournalTitle": "International Review of Law, Computers & Technology", "ISSN": "1360-0869", "EISSN": "1364-6885", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Law, University of Exeter, Exeter, England"}], "References": []}, {"ArticleId": 83114601, "Title": "Social network change after moving into permanent supportive housing: Who stays and who goes?", "Abstract": "Abstract <p>Social relationships are important among persons experiencing homelessness, but there is little research on changes in social networks among persons moving into permanent supportive housing (PSH). Using data collected as part of a longitudinal study of 405 adults (aged 39+) moving into PSH, this study describes network upheaval during this critical time of transition. Interviews conducted prior to and after three months of living in PSH assessed individual-level (demographics, homelessness history, health, and mental health) and social network characteristics, including network size and composition (demographics, relationship type, and social support). Interviewers utilized network member characteristics to assess whether network members were new or sustained between baseline and three months post-housing. Multilevel logistic regression models assessed characteristics of network members associated with being newly gained or persisting in networks three months after PSH move-in. Results show only one-third of social networks were retained during the transition to PSH, and veterans, African Americans, and other racial/ethnic minorities, and those living in scattered site housing, were more likely to experience network disruption. Relatives, romantic partners, and service providers were most likely to be retained after move-in. Some network change was moderated by tie strength, including the retention of street-met persons. Implications are discussed.</p>", "Keywords": "Homelessness;network change;permanent supportive housing;social networks", "DOI": "10.1017/nws.2020.19", "PubYear": 2021, "Volume": "9", "Issue": "1", "JournalId": 25796, "JournalTitle": "Network Science", "ISSN": "2050-1242", "EISSN": "2050-1250", "Authors": [{"AuthorId": 1, "Name": "Harmony Rhoades", "Affiliation": "University of Southern California, <PERSON>-Peck School of Social Work, Montgomery Ross Fisher Building, Los Angeles, CA 90089."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Missouri, School of Social Work, 709 Clark Hall, Columbia, MO 65211."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Southern California, <PERSON>-Peck School of Social Work, Montgomery Ross Fisher Building, Los Angeles, CA 90089."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Southern California, <PERSON>-Peck School of Social Work, Montgomery Ross Fisher Building, Los Angeles, CA 90089."}, {"AuthorId": 5, "Name": "<PERSON>ich<PERSON> Motte-<PERSON>", "Affiliation": "University of Southern California, <PERSON>-Peck School of Social Work, Montgomery Ross Fisher Building, Los Angeles, CA 90089."}, {"AuthorId": 6, "Name": "<PERSON> Winetrobe", "Affiliation": "University of Southern California, <PERSON>-Peck School of Social Work, Montgomery Ross Fisher Building, Los Angeles, CA 90089."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Southern California, <PERSON>-Peck School of Social Work, Montgomery Ross Fisher Building, Los Angeles, CA 90089."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "University of Southern California, <PERSON>-Peck School of Social Work, Montgomery Ross Fisher Building, Los Angeles, CA 90089."}], "References": []}, {"ArticleId": 83114633, "Title": "Fast contour detection with supervised attention learning", "Abstract": "<p>Recent advances in deep convolutional neural networks have led to significant success in many computer vision tasks, including edge detection. However, the existing edge detectors neglected the structural relationships among pixels, especially those among contour pixels. Inspired by human perception, this work points out the importance of learning structural relationships and proposes a novel real-time attention edge detection (AED) framework. Firstly, an elaborately designed attention mask is employed to capture the structural relationships among pixels at edges. Secondly, in the decoding phase of our encoder–decoder model, a new module called dense upsampling group convolution is designed to tackle the problem of information loss due to stride downsampling. And then, the detailed structural information can be preserved even it is ever destroyed in the encoding phase. The proposed relationship learning module introduces negligible computation overhead, and as a result, the proposed AED meets the requirement of real-time execution with only 0.65M parameters. With the proposed model, an optimal dataset scale F -score of 79.5 is obtained on the BSDS500 dataset with an inference speed of 105 frames per second, which is significantly faster than existing methods with comparable accuracy. In addition, a state-of-the-art performance is achieved on the BSDS500 (81.6) and NYU Depth (77.0) datasets when using a heavier model.</p>", "Keywords": "Edge detection; Structural relationship; Attention learning; Real time", "DOI": "10.1007/s11554-020-00980-1", "PubYear": 2021, "Volume": "18", "Issue": "3", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tongji University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Institute of Intelligent Science and Technology, Tongji University, Shanghai, China"}], "References": []}, {"ArticleId": 83114762, "Title": "Design, Implementation, and Assessment of Innovative Data Warehousing; Extract, Transformation, and Load(ETL); and Online Analytical Processing(OLAP) on BI", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijdms.2020.12301", "PubYear": 2020, "Volume": "12", "Issue": "3", "JournalId": 16205, "JournalTitle": "International Journal of Database Management Systems", "ISSN": "0975-5985", "EISSN": "0975-5705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83114955, "Title": "A novel jigless process applied to a robotic cell for aircraft structural assembly", "Abstract": "<p>The aerospace industry is searching to enhance product quality and performance with lower process cost and investments. One way to achieve that is to maximize automation with less operational resources and machine downtime toward to a friendly product and process assembly. Conventional jigs have rigid frame as main structure and workpieces are loaded by accurate holders, rendering it static. Within this domain, this paper identifies and compares state of art jigs used on automated cells with those of novel jig that meets the operational needs of ordinary aircraft assembly in a disruptive way. This paper details the novel jig as well as the manufacturing benefits using the jigless assembly concept in a drilling robotic working cell. The gains yielded such as infrastructure required, solution cost, performance, and product accessibility are qualitatively demonstrated herein. Additionally, the jigless assembly concept connects the manufacturing demand with the product development environment, based upon Lean manufacturing concepts.</p>", "Keywords": "Jigless assembly; DFAut—Design for Automation; Lean manufacturing; Robotic working cell", "DOI": "10.1007/s00170-020-05700-4", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Aeronautics Institute of Technology, São José dos Campos, Brazil;Embraer SA, São José dos Campos, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Aeronautics Institute of Technology, São José dos Campos, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Aeronautics Institute of Technology, São José dos Campos, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Embraer SA, São José dos Campos, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Embraer SA, São José dos Campos, Brazil"}, {"AuthorId": 6, "Name": "Wagner <PERSON>", "Affiliation": "Embraer SA, São José dos Campos, Brazil"}], "References": [{"Title": "Symbiotic human–robot collaborative approach for increased productivity and enhanced safety in the aerospace manufacturing industry", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "Page": "851", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 83115232, "Title": "Rational, emotional, and attentional models for recommender systems", "Abstract": "<p>This work analyses the decision-making process underlying choice behaviour. First, neural and gaze activity were recorded experimentally from different subjects performing a choice task in a Web Interface. Second, choice models and ensembles were fitted using rational, emotional, and attentional features. The model's predictions were evaluated in terms of their accuracy and rankings were made for each user. The results show that (a) the attentional models are the best in terms of its average performance across all users, (b) each subject shows a different best model, and (c) ensembles may perform better than single choice models but an optimal building method has to be found.</p>", "Keywords": "choice models;decision making;dual process theory;ensembles;recommender systems", "DOI": "10.1111/exsy.12594", "PubYear": 2021, "Volume": "38", "Issue": "4", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Computer Science, CITIUS, University of Santiago de Compostela, Santiago, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Electronics and Computer Science, CITIUS, University of Santiago de Compostela, Santiago, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Neurologyca, Vitoria-Gasteiz, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Neurologyca, Vitoria-Gasteiz, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Electronics and Computer Science, CITIUS, University of Santiago de Compostela, Santiago, Spain"}], "References": []}, {"ArticleId": 83115344, "Title": "Fast and Robust QEF Minimization using Probabilistic Quadrics", "Abstract": "<p>Error quadrics are a fundamental and powerful building block in many geometry processing algorithms. However, finding the minimizer of a given quadric is in many cases not robust and requires a singular value decomposition or some ad‐hoc regularization. While classical error quadrics measure the squared deviation from a set of ground truth planes or polygons, we treat the input data as genuinely uncertain information and embed error quadrics in a probabilistic setting (“probabilistic quadrics”) where the optimal point minimizes the expected squared error. We derive closed form solutions for the popular plane and triangle quadrics subject to (spatially varying, anisotropic) Gaussian noise. Probabilistic quadrics can be minimized robustly by solving a simple linear system — 50× faster than SVD. We show that probabilistic quadrics have superior properties in tasks like decimation and isosurface extraction since they favor more uniform triangulations and are more tolerant to noise while still maintaining feature sensitivity. A broad spectrum of applications can directly benefit from our new quadrics as a drop‐in replacement which we demonstrate with mesh smoothing via filtered quadrics and non‐linear subdivision surfaces.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Mesh models; Mesh geometry models", "DOI": "10.1111/cgf.13933", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "RWTH Aachen University, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "RWTH Aachen University, Germany"}], "References": []}, {"ArticleId": 83115347, "Title": "Interactive Modeling of Cellular Structures on Surfaces with Application to Additive Manufacturing", "Abstract": "<p>The rich and evocative patterns of natural tessellations endow them with an unmistakable artistic appeal and structural properties which are echoed across design, production, and manufacturing. Unfortunately, interactive control of such patterns‐as modeled by Voro<PERSON>i diagrams, is limited to the simple two dimensional case and does not extend well tofreeform surfaces. We present an approach for direct modeling and editing of such cellular structures on surface meshes. The overall modeling experience is driven by a set of editing primitives which are efficiently implemented on graphics hardware. We feature a novel application for 3D printing on modern support‐free additive manufacturing platforms. Our method decomposes the input surface into a cellular skeletal structure which hosts a set of overlay shells. In this way, material saving can be channeled to the shells while structural stability is channeled to the skeleton. To accommodate the available printer build volume, the cellular structure can be further split into moderately sized parts. Together with shells, they can be conveniently packed to save on production time. The assembly of the printed parts is streamlined by a part numbering scheme which respects the geometric layout of the input model.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Shape modeling; Parallel computing methodologies", "DOI": "10.1111/cgf.13929", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Max Planck Institute for Informatics, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graz University of Technology, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Max Planck Institute for Informatics, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Graz University of Technology, Austria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Max Planck Institute for Informatics, Germany"}], "References": []}, {"ArticleId": 83115357, "Title": "Survey of Models for Acquiring the Optical Properties of Translucent Materials", "Abstract": "<p>The outset of realistic rendering is a desire to reproduce the appearance of the real world. Rendering techniques therefore operate at a scale corresponding to the size of objects that we observe with our naked eyes. At the same time, rendering techniques must be able to deal with objects of nearly arbitrary shapes and materials. These requirements lead to techniques that oftentimes leave the task of setting the optical properties of the materials to the user. Matching the appearance of real objects by manual adjustment of optical properties is however nearly impossible. We can render objects with a plausible appearance in this way but cannot compare the appearance of a manufactured item to that of its digital twin. This is especially true in the case of translucent objects, where we need more than a goniometric measurement of the optical properties. In this survey, we provide an overview of forward and inverse models for acquiring the optical properties of translucent materials. We map out the efforts in graphics research in this area and describe techniques available in related fields. Our objective is to provide a better understanding of the tools currently available for appearance specification when it comes to digital representations of real translucent objects.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Reflectance modeling;Appearance and texture representations", "DOI": "10.1111/cgf.14023", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Technical University of Denmark"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "DFM A/S, Danish National Metrology Institute"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "DFM A/S, Danish National Metrology Institute"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "KU Leuven, Belgium"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "RISE ‐ Research Institutes of Sweden"}, {"AuthorId": 6, "Name": "S. K<PERSON> <PERSON><PERSON>", "Affiliation": "Technical University of Denmark"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "KU Leuven, Belgium"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DFM A/S, Danish National Metrology Institute"}], "References": [{"Title": "Microstructure Control in 3D Printing with Digital Light Processing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "1", "Page": "347", "JournalTitle": "Computer Graphics Forum"}, {"Title": "GenSSS: a genetic algorithm for measured subsurface scattering representation", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "307", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 83115361, "Title": "Optimization of direct metal printing process parameters for plastic injection mold with both gas permeability and mechanical properties using design of experiments approach", "Abstract": "<p>Direct metal laser sintering (DMLS) technology plays an important role in molds or dies industry. The distinct feature of the metal components, molds, or dies fabricated by DMLS possesses gas permeability. However, the mechanical properties of the fabricated molds were influenced by degree of gas permeability. In this study, the design of experiments (DOE) approach was employed to optimize the DMLS process parameters for fabricating plastic injection mold with better gas permeability and mechanical properties. It was found that the optimal DMLS process parameters for fabricating plastic injection molds with better mechanical properties and gas permeability are layer thickness of 30 μm, hatching space of 141 μm, scanning speed of 220 mm/s, and laser power of 50 W. The most important DMLS process parameter affecting the mechanical properties and gas permeability is the layer thickness, followed by the hatching space. The gas venting mechanism for molds fabricated by DMLS has been demonstrated.</p>", "Keywords": "Selective laser melting; Gas permeability; Plastic injection mold; Design of experiments", "DOI": "10.1007/s00170-020-05724-w", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>l-<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Ming Chi University of Technology, New Taipei City, Taiwan;Research Center for Intelligent Medical Devices, Ming Chi University of Technology, New Taipei City, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Design, Ming Chi University of Technology, New Taipei City, Taiwan"}], "References": [{"Title": "X-ray CT analysis of the influence of process on defect in Ti-6Al-4V parts produced with Selective Laser Melting technology", "Authors": "<PERSON><PERSON>; <PERSON>ng <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "1-2", "Page": "3", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effects of powders and process parameters on density and hardness of A357 aluminum alloy fabricated by selective laser melting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "1-2", "Page": "371", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental study on grinding-induced residual stress in C-250 maraging steel", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "Page": "953", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Analysis of the surface roughness obtained by the abrasive flow machining process using an abrasive paste with oiticica oil", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "5061", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Influence of printing parameters on structures, mechanical properties and surface characterization of aluminium alloy manufactured using selective laser melting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "5137", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "The effects of hot isostatic pressing (HIP) and solubilization heat treatment on the density, mechanical properties, and microstructure of austenitic stainless steel parts produced by selective laser melting (SLM)", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "1-2", "Page": "109", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Burr formation and its treatments—a review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2189", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Review on direct metal laser deposition manufacturing technology for the Ti-6Al-4V alloy", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; N. K. <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1163", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Characterization of a direct metal printed injection mold with different conformal cooling channels", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1223", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Laser direct metal deposition of variable width thin-walled structures in Inconel 718 alloy by coaxial powder feeding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "3", "Page": "821", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 83115365, "Title": "Design of E-Data Collecting Tools for Probationers", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/7691.32020", "PubYear": 2020, "Volume": "9", "Issue": "1.3", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83115371, "Title": "Polygon Laplacian Made Simple", "Abstract": "<p>The discrete Laplace‐Beltrami operator for surface meshes is a fundamental building block for many (if not most) geometry processing algorithms. While Laplacians on triangle meshes have been researched intensively, yielding the cotangent discretization as the de‐facto standard, the case of general polygon meshes has received much less attention. We present a discretization of the Laplace operator which is consistent with its expression as the composition of divergence and gradient operators, and is applicable to general polygon meshes, including meshes with non‐convex, and even non‐planar, faces. By virtually inserting a carefully placed point we implicitly refine each polygon into a triangle fan, but then hide the refinement within the matrix assembly. The resulting operator generalizes the cotangent Laplacian, inherits its advantages, and is empirically shown to be on par or even better than the recent polygon Laplacian of <PERSON><PERSON> and <PERSON> [AW11] — while being simpler to compute.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Mesh geometry models;• Theory of computation → Computational geometry", "DOI": "10.1111/cgf.13931", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bielefeld University, Germany; the first two authors contributed equally"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Switzerland; the first two authors contributed equally"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Johns Hopkins University, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Bielefeld University, Germany"}], "References": []}, {"ArticleId": 83115376, "Title": "Mixing Yarns and Triangles in Cloth Simulation", "Abstract": "<p>This paper presents a method to combine triangle and yarn models in cloth simulation, and hence leverage their best features. The majority of a garment uses a triangle‐based model, which reduces the overall computational and memory cost. Key areas of the garment use a yarn‐based model, which elicits rich effects such as structural nonlinearity and plasticity. To combine both models in a seamless and robust manner, we solve two major technical challenges. We propose an enriched kinematic representation that augments triangle‐based deformations with yarn‐level details. Naïve enrichment suffers from kinematic redundancy, but we devise an optimal kinematic filter that allows a smooth transition between triangle and yarn models. We also introduce a preconditioner that resolves the poor conditioning produced by the extremely different inertia of triangle and yarn nodes. This preconditioner deals effectively with rank deficiency introduced by the kinematic filter. We demonstrate that mixed yarns and triangles succeed to efficiently capture rich effects in garment fit and drape.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Physical simulation", "DOI": "10.1111/cgf.13915", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Seddi Labs, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Seddi Labs, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Seddi Labs, Madrid, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain"}], "References": []}, {"ArticleId": 83115390, "Title": "Progressive Real‐Time Rendering of One Billion Points Without Hierarchical Acceleration Structures", "Abstract": "<p>Research in rendering large point clouds traditionally focused on the generation and use of hierarchical acceleration structures that allow systems to load and render the smallest fraction of the data with the largest impact on the output. The generation of these structures is slow and time consuming, however, and therefore ill‐suited for tasks such as quickly looking at scan data stored in widely used unstructured file formats, or to immediately display the results of point‐cloud processing tasks.</p> <p>We propose a progressive method that is capable of rendering any point cloud that fits in GPU memory in real time, without the need to generate hierarchical acceleration structures in advance. Our method supports data sets with a large amount of attributes per point, achieves a load performance of up to 100 million points per second, displays already loaded data in real time while remaining data is still being loaded, and is capable of rendering up to one billion points using an on‐the‐fly generated shuffled vertex buffer as its data structure, instead of slow‐to‐generate hierarchical structures. Shuffling is done during loading in order to allow efficiently filling holes with random subsets, which leads to a higher quality convergence behavior.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Rendering; Rasterization", "DOI": "10.1111/cgf.13911", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TU Wien, Institute of Visual Computing & Human‐Centered Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TU Wien, Department of Geodesy and Geoinformation"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "TU Wien, Department of Geodesy and Geoinformation"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "TU Wien, Institute of Visual Computing & Human‐Centered Technology"}], "References": []}, {"ArticleId": 83115392, "Title": "Characteristics of Inconel 625—copper bimetallic structure fabricated by directed energy deposition", "Abstract": "<p>Inconel 625 (In625) is preferred under the circumstances where high strength and corrosion resistance at elevated temperatures are required. However, the restricted thermal conductivity constrains the application of In625 in high heat flux cases. The joining of materials with high thermal conductivity to In625 is capable of improving upon this limitation so as to adapt for temperature-sensitive requirements. In this study, a bi-metallic structure was fabricated by joining In625 on Copper 110 (Cu) substrate with directed energy deposition. Material examination indicated no crack and minor porosity were detected through and along the interface of two materials and the following as-deposited In625. Mechanical performances were characterized at both as-deposited and heat-treated conditions. The resultant yield strength and ultimate tensile strength of as-deposited In625 was 670.97 ± 5.43 MPa and 925.36 ± 9.90 MPa, respectively, with a mean maximum elongation of 0.416 mm/mm. Slightly decreases (by ~ 5%) in tensile strength were observed after heat treatment under 500 °C for 24 h with an enhancement in elongation by ~ 6%. Ductile fracture mode was observed on the fracture surfaces of broken tensile specimens. The impact toughness for as-deposited In625 and heat-treated In625 (under 600° for 24 h) was 118.058 ± 2.285 and 112.045 ± 5.755 J, respectively. A significant improvement in the thermal diffusivity of ~ 100% was experimentally measured when comparing the bi-metallic structure to pure In625. The thickness fraction of Cu played a significant role in the measured thermal diffusivity result.</p>", "Keywords": "Additive manufacturing; Bi-metallic structure; Joining; Inconel 625; Copper; Directed energy deposition", "DOI": "10.1007/s00170-020-05713-z", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical & Aerospace Engineering, Missouri University of Science and Technology, Rolla, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical & Aerospace Engineering, Missouri University of Science and Technology, Rolla, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Toyota Motor Corporation, Toyota, Japan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical & Aerospace Engineering, Missouri University of Science and Technology, Rolla, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical & Aerospace Engineering, Missouri University of Science and Technology, Rolla, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical & Aerospace Engineering, Missouri University of Science and Technology, Rolla, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mechanical & Aerospace Engineering, Missouri University of Science and Technology, Rolla, USA"}], "References": []}, {"ArticleId": 83115396, "Title": "The impact of on‐demand metacognitive help on effortful behaviour: A longitudinal study using task‐related visual analytics", "Abstract": "<p>This longitudinal study investigates the differences in learners' effortful behaviour over time due to receiving metacognitive help—in the form of on‐demand task‐related visual analytics. Specifically, learners' interactions ( N = 67) with the tasks were tracked during four self‐assessment activities, conducted at four discrete points in time, over a period of 8 weeks. The considered and coded time points were: (a) prior to providing the metacognitive help; (b) while the task‐related visual analytics were available (treatment); (c) after the removal of the treatment; and (d) while the option to receive metacognitive help was available again. To measure learners' effortful behaviour across the self‐assessment activities, this study utilized learners' response‐times to correctly/wrongly complete the tasks and on‐task effort expenditure. The panel data analysis shown that the usage of metacognitive help caused statistically significant changes in learners' effortful behaviour, mostly in the third and fourth phase. Statistically significant changes were detected also in the usage of metacognitive help. These results provide empirical evidence on the benefits of task‐related visual analytics to support learners' on‐task engagement, and suggest relevant cues on how metacognitive help could be designed and prompted by focusing on the “task”, instead of the “self”.</p> Lay Description <h3 > What is already known about this topic:</h3> The learning outcomes are improved when students pause to think and reason a hint, and elicit its implications. The learning outcomes are improved when time‐spent is properly allocated on help‐seeking. Former studies followed cross‐sectional research designs to investigate the effects of help on learning constructs. <h3 > What this paper adds:</h3> Task‐related visual analytics have strong positive effect on effortful behaviour. They increase learners' awareness on the “true” requirements of the learning task. They seem to boost learners to practice critical judgement and decision making. This study is one of the very limited in number studies in the field of learning analytics that implemented a longitudinal research design. <h3 > Implications for practice and/or policy:</h3> The time metric can be coded (for describing change) to facilitate the research design. Training the learners to read and make‐sense from visual analytics fosters their metacognition. Training the learners on how to use visual analytics is expected to build their capacity for data‐driven decisions.", "Keywords": "effortful behaviour;learning analytics;longitudinal study;metacognitive help;self‐assessment;task‐related visual analytics", "DOI": "10.1111/jcal.12472", "PubYear": 2021, "Volume": "37", "Issue": "1", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IPPS in Information Systems, University of Macedonia, Thessaloniki, Greece; Sem Sælands vei 9, IT‐Bygget, 7491, Norwegian University of Science and Technology, Trondheim, Norway"}, {"AuthorId": 2, "Name": "Anastasios A<PERSON>", "Affiliation": "IPPS in Information Systems, University of Macedonia, Thessaloniki, Greece"}], "References": [{"Title": "Linking learning behavior analytics and learning science concepts: Designing a learning analytics dashboard for feedback to support learning regulation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "105512", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 83115399, "Title": "Real‐time Anticipation of Occlusions for Automated Camera Control in Toric Space", "Abstract": "<p>Efficient visibility computation is a prominent requirement when designing automated camera control techniques for dynamic 3D environments; computer games, interactive storytelling or 3D media applications all need to track 3D entities while ensuring their visibility and delivering a smooth cinematic experience. Addressing this problem requires to sample a large set of potential camera positions and estimate visibility for each of them, which in practice is intractable despite the efficiency of ray‐casting techniques on recent platforms. In this work, we introduce a novel GPU‐rendering technique to efficiently compute occlusions of tracked targets in Toric Space coordinates – a parametric space designed for cinematic camera control. We then rely on this occlusion evaluation to derive an anticipation map predicting occlusions for a continuous set of cameras over a user‐defined time window. We finally design a camera motion strategy exploiting this anticipation map to minimize the occlusions of tracked entities over time. The key features of our approach are demonstrated through comparison with traditionally used ray‐casting on benchmark scenes, and through an integration in multiple game‐like 3D scenes with heavy, sparse and dense occluders.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Rasterization;Procedural animation;• Applied computing → Media arts", "DOI": "10.1111/cgf.13949", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "L. <PERSON>urg", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, M2S"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "LTCI / Télécom Paris, Institut Polytechnique de Paris, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, M2S; Beijing Film Academy, China"}], "References": []}, {"ArticleId": 83115400, "Title": "An Efficient Transport Estimator for Complex Layered Materials", "Abstract": "<p>Layered materials capture subtle, realistic reflection behaviors that traditional single‐layer models lack. Much of this is due to the complex subsurface light transport at the interfaces of – and in the media between – layers. Rendering with these materials can be costly, since we must simulate these transport effects at every evaluation of the underlying reflectance model. Rendering an image requires thousands of such evaluations, per pixel. Recent work treats this complexity by introducing significant approximations, requiring large precomputed datasetsper material, or simplifying the light transport simulations within the materials. Even the most effective of these methods struggle with the complexity induced by high‐frequency variation in reflectance parameters and micro‐surface normal variation, as well as anisotropic volumetric scattering between the layer interfaces. We present a more efficient, unbiased estimator for light transport in such general, complex layered appearance models. By conducting an analysis of the types of transport paths that contribute most to the aggregate reflectance dynamics, we propose an effective and unbiased path sampling method that reduces variance in the reflectance evaluations. Our method additionally supports reflectance importance sampling, does not rely on any precomputation, and so integrates readily into existing renderers. We consistently outperform the state‐of‐the‐art by <b>~2 – 6 </b>× in equal‐quality (i.e., equal error) comparisons.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Rendering", "DOI": "10.1111/cgf.13936", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Luis <PERSON>", "Affiliation": "Université de Montréal, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "McGill University, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "McGill University, Canada"}], "References": []}, {"ArticleId": 83115403, "Title": "Efficient Minimum Distance Computation for Solids of Revolution", "Abstract": "<p>We present a highly efficient algorithm for computing the minimum distance between two solids of revolution, each of which is defined by a planar cross‐section region and a rotation axis. The boundary profile curve for the cross‐section is first approximated by a bounding volume hierarchy (BVH) of fat arcs. By rotating the fat arcs around the axis, we generate the BVH of fat tori that bounds the surface of revolution. The minimum distance between two solids of revolution is then computed very efficiently using the distance between fat tori, which can be boiled down to the minimum distance computation for circles in the three‐dimensional space. Our circle‐based approach to the solids of revolution has distinctive features of geometric simplification. The main advantage is in the effectiveness of our approach in handling the complex cases where the minimum distance is obtained in non‐convex regions of the solids under consideration. Though we are dealing with a geometric problem for solids, the algorithm actually works in a computational style similar to that of handling planar curves. Compared with conventional BVH‐based methods, our algorithm demonstrates outperformance in computing speed, often 10–100 times faster. Moreover, the minimum distance can be computed very efficiently for the solids of revolution under deformation, where the dynamic reconstruction of fat arcs dominates the overall computation time and takes a few milliseconds.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Minimum distance computation;Solid of revolution;Bounding volume hierarchy;Fat arc;Toroidal patch;Deformation;Acceleration", "DOI": "10.1111/cgf.13950", "PubYear": 2020, "Volume": "39", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "S.‐H. Son", "Affiliation": "Dept. of Computer Science and Eng., Seoul National University, South Korea"}, {"AuthorId": 2, "Name": "S.<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Multimedia Eng., Dongguk University, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Computer Science and Eng., Seoul National University, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Dept., Technion, Israel"}], "References": []}, {"ArticleId": 83115434, "Title": "Towards secure and network state aware bitrate adaptation at IoT edge", "Abstract": "Video streaming is critical in IoT systems, enabling a variety of applications such as traffic monitoring and health caring. Traditional adaptive bitrate streaming (ABR) algorithms mainly focus on improving Internet video streaming quality where network conditions are relatively stable. These approaches, however, suffer from performance degradation at IoT edge. In IoT systems, the wireless channels are prone to interference and malicious attacks, which significantly impacts Quality-of-Experience (QoE) for video streaming applications. In this paper, we propose a secure and network-state-aware solution, SASA, to address these challenges. We first study the buffer-level constraint when increasing bitrate. We then analyze the impact of throughput overestimation in bitrate decisions. Based on these results, SASA is designed to consist of both an offline and an online phase. In the offline phase, SASA precomputes the best configurations of ABR algorithms under various network conditions. In the online phase, SASA adopts an online Bayesian changepoint detection method to detect network changes and apply precomputed configurations to make bitrate decisions. We implement SASA and evaluate its performance using 429 real network traces. We show that the SASA outperforms state-of-the-art ABR algorithms such as RobustMPC and Oboe in the IoT environment through extensive experiments.", "Keywords": "Adaptive bitrate algorithm;IoT", "DOI": "10.1186/s13677-020-00189-4", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information and Telecommunication Branch, State Grid Jiangsu Electric Power Company, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tsinghua Wuxi Research Institute of Applied Technologies, Wuxi, China"}, {"AuthorId": 3, "Name": "Weiwei Miao", "Affiliation": "Information and Telecommunication Branch, State Grid Jiangsu Electric Power Company, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Information and Telecommunication Branch, State Grid Jiangsu Electric Power Company, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Information and Telecommunication Branch, State Grid Jiangsu Electric Power Company, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information and Telecommunication Branch, State Grid Jiangsu Electric Power Company, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information and Telecommunication Branch, State Grid Jiangsu Electric Power Company, Nanjing, China"}], "References": []}]