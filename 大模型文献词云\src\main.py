import dataProcess
import os
import api

api = api.Api()

def main():
    # 前置准备，保证keywords.json文件存在
    pre_process()
    # 读取关键词字典
    data_processor = dataProcess.DataProcess("D:/项目/大模型文献词云/data/json")
    keywords_dict = data_processor.read_keywords_from_file()
    # 获取待查询的期刊名称
    journal_title = get_journal_title(keywords_dict)
    # 获取该期刊名称在keywords_dict中的关键词-次数字典
    journal_keywords_dict = keywords_dict[journal_title]
    # 根据该字典生成词云
    data_processor.generate_word_cloud(journal_keywords_dict, journal_title)

# 前置准备
def pre_process():
    # 如果keywords.json文件不存在，则执行前置准备
    if not os.path.exists("D:/项目/大模型文献词云/data/processed/keywords.json"):
        # 创建DataProcess对象，指定json文件夹路径
        data_processor = dataProcess.DataProcess('D:/项目/大模型文献词云/data/json')
        # 读取所有json文件，提取关键词
        data_processor.read_all_json()
        # 处理关键词列表，将非白名单关键词转为小写
        data_processor.process_keywords_list()
        # 将关键词列表转换为计数字典
        data_processor.convert_keywords_to_count_dict()
        # 如果keywords.json文件不存在，则将处理后的关键词写入文件
        data_processor.write_keywords_to_file()

# 获取待查询的期刊名称
def get_journal_title(keywords_dict):
    # 从键盘输入期刊名称
    journal_title = input("请输入期刊名称（可能的名称有：Neural Processing Letters、SN Computer Science）：")
    # 如果期刊名称不存在，则调用api获取期刊名称
    if journal_title not in keywords_dict:
        journal_title = api.get_journal_title(journal_title, keywords_dict.keys())
        print(f"您输入的期刊名称不存在,最可能的期刊名称是：{journal_title}")
    return journal_title

if __name__ == "__main__":
    main()