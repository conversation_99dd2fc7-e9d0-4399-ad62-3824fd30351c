[{"ArticleId": 108705169, "Title": "M-Quantile Estimation for GARCH Models", "Abstract": "<p>M-regression and quantile methods have been suggested to estimate generalized autoregressive conditionally heteroscedastic (GARCH) models. In this paper, we propose an M-quantile approach, which combines quantile and M-regression to obtain a robust estimator of the conditional volatility when the data have abrupt observations or heavy-tailed distributions. Monte Carlo experiments are conducted to show that the M-quantile approach is more resistant against additive outliers than M-regression and quantile methods. The usefulness of the method is illustrated on two financial datasets.</p>", "Keywords": "GARCH; M-estimation; Quantile; Robustness; Outliers; Abrupt observations", "DOI": "10.1007/s10614-023-10398-z", "PubYear": 2024, "Volume": "63", "Issue": "6", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "Patrick <PERSON>", "Affiliation": "PPGEco and Department of Economics, Federal University of Espírito Santo, Vitória, Brazil; Laboratoire des Signaux et Systèmes, Université Paris-Saclay, CNRS, CentraleSupélec, Gif-sur-Yvette, France; Corresponding author."}, {"AuthorId": 2, "Name": "Valderio A. Reisen", "Affiliation": "PPGEco and Department of Economics, Federal University of Espírito Santo, Vitória, Brazil; Department of Statistics, Federal University of Minas Gerais, Belo Horizonte, Brazil; Laboratoire des Signaux et Systèmes, Université Paris-Saclay, CNRS, CentraleSupélec, Gif-sur-Yvette, France; Institute of Mathematics and Statistics, Federal University of Bahia, Bahia, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratoire des Signaux et Systèmes, Université Paris-Saclay, CNRS, CentraleSupélec, Gif-sur-Yvette, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "PPGEco and Department of Economics, Federal University of Espírito Santo, Vitória, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Statistics, Federal University of Minas Gerais, Belo Horizonte, Brazil; Laboratoire des Signaux et Systèmes, Université Paris-Saclay, CNRS, CentraleSupélec, Gif-sur-Yvette, France"}], "References": []}, {"ArticleId": 108705210, "Title": "Integrated Machine Learning Model for Comprehensive Heart Disease Risk Assessment Based on Multi-Dimensional Health Factors", "Abstract": "", "Keywords": "machine learning algorithms; cardiovascular diseases; logistic regression; imbalance classification; hyper-parameter tuning", "DOI": "10.37745/ejcsit.2013/vol11n34458", "PubYear": 2023, "Volume": "11", "Issue": "3", "JournalId": 89079, "JournalTitle": "European Journal of Computer Science and Information Technology", "ISSN": "2054-0957", "EISSN": "2054-0965", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Romie C<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108705395, "Title": "NBA: defensive distillation for backdoor removal via neural behavior alignment", "Abstract": "Recently, deep neural networks have been shown to be vulnerable to backdoor attacks. A backdoor is inserted into neural networks via this attack paradigm, thus compromising the integrity of the network. As soon as an attacker presents a trigger during the testing phase, the backdoor in the model is activated, allowing the network to make specific wrong predictions. It is extremely important to defend against backdoor attacks since they are very stealthy and dangerous. In this paper, we propose a novel defense mechanism, Neural Behavioral Alignment (NBA), for backdoor removal. NBA optimizes the distillation process in terms of knowledge form and distillation samples to improve defense performance according to the characteristics of backdoor defense. NBA builds high-level representations of neural behavior within networks in order to facilitate the transfer of knowledge. Additionally, NBA crafts pseudo samples to induce student models exhibit backdoor neural behavior. By aligning the backdoor neural behavior from the student network with the benign neural behavior from the teacher network, NBA enables the proactive removal of backdoors. Extensive experiments show that NBA can effectively defend against six different backdoor attacks and outperform five state-of-the-art defenses.", "Keywords": "Deep neural network;Backdoor removal;Knowledge distillation", "DOI": "10.1186/s42400-023-00154-z", "PubYear": 2023, "Volume": "6", "Issue": "1", "JournalId": 5427, "JournalTitle": "Cybersecurity", "ISSN": "", "EISSN": "2523-3246", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Security, Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Information Security, Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China"}], "References": [{"Title": "A survey of deep learning techniques for autonomous driving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "3", "Page": "362", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 108705410, "Title": "Reinforcement Learning in Education 4.0: Open Applications and Deployment Challenges", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijcsit.2023.15304", "PubYear": 2023, "Volume": "15", "Issue": "3", "JournalId": 20755, "JournalTitle": "International Journal of Computer Science and Information Technology", "ISSN": "0975-4660", "EISSN": "0975-3826", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108705473, "Title": "An improved TOPSIS method for multi-criteria decision making based on hesitant fuzzy β neighborhood", "Abstract": "<p>Multi-criteria Decision Making (MCDM) plays a very vital role in many application fields. There are many classical methods to solve the MCDM problems if the available information is crisp. However, the uncertainty and ambiguity inherent in the MCDM often makes these methods unsuitable for solving this kind of problem. Aims at the failures of TOPSIS method that can not rank the alternatives completely in a Hesitant Fuzzy β -Covering Approximation Space (HFβCAS), we develop an improved TOPSIS method. First, we define two pairs of hesitant fuzzy relationship based on hesitant fuzzy β -neighborhood, and construct the corresponding hesitant fuzzy covering rough set models; further we discuss the properties and relationships between the models. Second, we introduce a new comprehensive weight determination method by using the precision degree of hesitant fuzzy covering rough set and the maximizing deviation method. Third, we construct a γ-βCHF-TOPSIS method to MCDM which generalizes the TOPSIS method in an HFβCAS. Finally, two real decision-making problems are used to illustrate the concrete implementation process of γ-βCHF-TOPSIS method, and demonstrate its effectiveness and reasonability.</p>", "Keywords": "Hesitant fuzzy set; Multi-criteria decision making (MCDM); TOPSIS method; Hesitant fuzzy β-neighborhood; Hesitant fuzzy covering rough set", "DOI": "10.1007/s10462-023-10510-7", "PubYear": 2023, "Volume": "56", "Issue": "S1", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Hebei Normal University, Shijiazhuang, China; School of Economics and Management, Hebei University of Science and Technology, Shijiazhuang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Mi", "Affiliation": "School of Mathematical Sciences, Hebei Normal University, Shijiazhuang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Hebei University of Science and Technology, Shijiazhuang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Physics, Shijiazhuang Tiedao University, Shijiazhuang, China"}], "References": [{"Title": "Fermatean fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "663", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "PF-TOPSIS method based on CPFRS models: An application to unconventional emergency events", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106192", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Sustainable supplier selection in healthcare industries using a new MCDM method: Measurement of alternatives and ranking according to COmpromise solution (MARCOS)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106231", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Intuitionistic fuzzy TOPSIS method based on CVPIFRS models: An application to biomedical problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "315", "JournalTitle": "Information Sciences"}, {"Title": "An outranking method for multicriteria decision making with probabilistic hesitant information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "3", "Page": "", "JournalTitle": "Expert Systems"}, {"Title": "Novel classes of coverings based multigranulation fuzzy rough sets and corresponding applications to multiple attribute group decision-making", "Authors": "<PERSON><PERSON><PERSON> Ma; <PERSON><PERSON><PERSON>; Bingzhen Sun", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "6197", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Decision making with probabilistic hesitant fuzzy information based on multiplicative consistency", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "8", "Page": "1233", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "PROMETHEE II method based on variable precision fuzzy rough sets with fuzzy neighborhoods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "2", "Page": "1281", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Multi-attribute group decision making method based on prospect theory under hesitant probabilistic fuzzy environment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106804", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A new similarity measure between picture fuzzy sets and its application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "103956", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An extended hesitant fuzzy set using SWARA-MULTIMOORA approach to adapt online education for the control of the pandemic spread of COVID-19 in higher education institutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "1", "Page": "181", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An approach to MCGDM based on multi-granulation Pythagorean fuzzy rough set over two universes and its application to medical decision problem", "Authors": "Bingzhen Sun; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "3", "Page": "1887", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Classifying the degree of exposure of customers to COVID-19 in the restaurant industry: A novel intuitionistic fuzzy set extension of the TOPSIS-Sort", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107906", "JournalTitle": "Applied Soft Computing"}, {"Title": "Intuitionistic principal value Z-linguistic hybrid geometric operator and their applications for multi-attribute group decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "5", "Page": "3863", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Novel measures for linguistic hesitant Pythagorean fuzzy sets and improved TOPSIS method with application to contributions of system-of-systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "117088", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel failure model and effect analysis method using a flexible knowledge acquisition framework based on picture fuzzy sets", "Authors": "Xi<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105625", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 108705535, "Title": "<PERSON><PERSON><PERSON> Website Perguruan Tinggi Sebagai Second-Order Construct Menggunakan Pendekatan PLS-SEM", "Abstract": "<p>Website perguruan tinggi mengalami penurunan pemeringkatan Webometric, QS World University Ranking dan klasterisasi di tingkat nasional. Peneliti melakukan riset terkait dengan adanya gap pada performa website perguruan tinggi. Tujuan dilakukannya penelitian ini adalah untuk menganalisis faktor yang membentuk dimensi kualitas website (WebQual) dan pengaruhnya pada kepuasan pengguna dan niat mengunjungi kembali website perguruan tinggi. Jenis penelitian adalah kuantitatif dengan menggunakan data primer yang hasilnya diperoleh dari jawaban responden atas isian kuesioner. Penentuan sampel menggunakan teknik Purposive Sampling dengan total 379 responden. Teknik analisis menggunakan Explanatory Factor Analysis (EFA) untuk menentukan faktor pembentuk WebQual dan Partial Least Square dengan Structural Equation Model (PSL-SEM) untuk menguji model penelitian. <PERSON>il penelitian menunjukkan bahwa EFA mampu mengelompokkan dari sepuluh variabel menjadi tiga variabel baru pembentuk dimensi WebQual sebagai second-order construct yaitu kualitas informasi, kualitas interaksi layanan, dan kemanfaatan website. Hasil PLS-SEM menunjukkan bahwa dimensi WebQual memiliki pengaruh positif terhadap kepuasan pengguna, dan kepuasan pengguna berpengaruh positif terhadap niat mengunjungi kembali website perguruan tinggi. Implikasi dari penelitian ini bahwa semakin bagus kualitas website perguruan tinggi dapat meningkatkan kepuasan penggunanya. Hal ini memberi dampak pada keinginan pengguna untuk melakukan kunjungan kembali ke website perguruan tinggi di masa yang akan datang.</p>", "Keywords": "Kepuasan Pengguna;Kualitas Website;Niat Mengunjungi Kembali;PLS-SEM;Second-Order Construct", "DOI": "10.34148/teknika.v12i2.617", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 66608, "JournalTitle": "Teknika", "ISSN": "2549-8037", "EISSN": "2549-8045", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Program Studi Sistem Informasi, Universitas Hayam Wuruk <PERSON>, Surabaya, <PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program <PERSON><PERSON>, Universitas Hayam Wu<PERSON>, Sur<PERSON>ya, <PERSON><PERSON>"}, {"AuthorId": 3, "Name": "Ari Cahaya Puspitaningrum", "Affiliation": "Program Studi Sistem Informasi, Universitas Hayam Wuruk <PERSON>, Surabaya, <PERSON><PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Sistem Informasi, Universitas Hayam Wuruk <PERSON>, Surabaya, <PERSON><PERSON>"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Program Studi Informatika, Universitas Hayam Wuruk <PERSON>banas, Surabaya, J<PERSON>"}], "References": []}, {"ArticleId": 108705615, "Title": "A PC-kriging-HDMR Integrated with an Adaptive Sequential Sampling Strategy for High-Dimensional Approximate Modeling", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijcsit.2023.15305", "PubYear": 2023, "Volume": "15", "Issue": "3", "JournalId": 20755, "JournalTitle": "International Journal of Computer Science and Information Technology", "ISSN": "0975-4660", "EISSN": "0975-3826", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108705697, "Title": "Perancangan UI/UX Pada Website Rumah Tahfidz Akhwat Menggunakan Metode Design Thinking", "Abstract": "<p><PERSON><PERSON>, informasi mengenai Rumah <PERSON> (RTA) Raudhatul Jannah Pekanbaru hanya didapat melalui berita mulut ke mulut dan brosur edaran, begitu pula dengan seluruh proses bisnis yang terjadi, semuanya dilakukan secara manual. Mempertimbangkan kondisi tersebut, muncul sebuah inovasi berupa perancangan UI/UX untuk persiapan pengembangan website resmi RTA menggunakan metode Design Thinking yang akan dilakukan melalui penelitian ini. Rancangan website ini memiliki fitur 3 utama, yaitu menampilkan informasi mengenai RTA, pendaftaran murid baru, dan layanan siswa. Perancangan dilakukan menggunakan aplikasi Figma. Hasil perancangan berupa prototype interaktif dengan dua tipe, tipe A dan B. Dalam proses perancangan, digunakan 14 komponen. Kedua prototype memiliki 6 menu utama, dengan jumlah halaman prototype A sebanyak 22 halaman dan prototype B sebanyak 12 halaman. Pengujian dilakukan menggunakan Usability Testing dengan metode Cognitive walkthrough, SEQ, A/B Testing, dan UEQ. Pengujian dilakukan terhadap 20 partisipan siswa, 10 partisipan staf, dan 10 partisipan umum. Pengujian Cognitive walkthrough siswa memperoleh hasil 80% untuk kedua prototype, pengujian staf memperoleh hasil 100% untuk kedua prototype, dan pengujian umum memperoleh hasil 100% untuk kedua prototype. Pengujian UEQ untuk kedua prototype (A : B) memperoleh nilai attractiveness sebesar 2,24 : 2,49, perspicuity 1,43 : 1,33, efficiency 1,98 : 1,98, dependability 2,30 : 2,29, stimulation 2,09 : 2,14, dan novelty 1,72 : 1,91. Berdasarkan hasil pengujian UEQ yang dilakukan, kedua prototype sudah mencapai skala minimal good dan dapat direkomendasikan untuk pengembangan sistem. Prototype hasil penelitian ini bermanfaat untuk membantu pihak RTA memiliki alternatif high-fidelity desain website yang sudah teruji kegunaannya dan siap untuk diimplementasikan dalam pengembangan website.</p>", "Keywords": "Design Thinking;<PERSON><PERSON><PERSON> (RTA);UEQ;UI/UX;Usability Testing", "DOI": "10.34148/teknika.v12i2.599", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 66608, "JournalTitle": "Teknika", "ISSN": "2549-8037", "EISSN": "2549-8045", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Sistem Informasi, Politeknik Caltex Riau, Pekanbaru, Riau"}, {"AuthorId": 2, "Name": "Indah Lestari", "Affiliation": "Program Studi Sistem Informasi, Politeknik Caltex Riau, Pekanbaru, Riau"}], "References": []}, {"ArticleId": 108705701, "Title": "Dynamic Control and Performance Evaluation of Microcontroller-Based Smart Industrial Heat Extractor", "Abstract": "", "Keywords": "", "DOI": "10.37745/ejcsit.2013/vol11n35974", "PubYear": 2023, "Volume": "11", "Issue": "3", "JournalId": 89079, "JournalTitle": "European Journal of Computer Science and Information Technology", "ISSN": "2054-0957", "EISSN": "2054-0965", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108705710, "Title": "Retraction of: Data visualization technique to study the conceptual metaphors in <i><PERSON><PERSON> of Hafiz</i> and <i><PERSON><PERSON> of Sa'adi</i>", "Abstract": "", "Keywords": "", "DOI": "10.1093/llc/fqad001", "PubYear": 2023, "Volume": "38", "Issue": "3", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [], "References": []}, {"ArticleId": 108705736, "Title": "RANCANG BANGUN APLIKASI POINT OF SALE PENJUALAN KOPI DENGAN MENGGUNAKAN FRAMEWORK CODEIGNITER BERBASIS WEB", "Abstract": "<p>ABSTRAKWarung Jus Kuphi 7 merupakan warung yang bergerak di bidang kuliner yang beralamat di Jalan A. H. Nasution Komp. Metrolink D 1. Selama ini warung halim memiliki beberapa kendala seperti menyimpan data transaksi sehari – hari masih menggunakan buku, sehingga kemungkinan besar data yang di catat bisa saja hilang dikarenakan tidak dijaga dengan baik sehingga catatan tersebut tidak dapat digunakan sebagai informasi jika warung tersebut membutuhkannya. Selain itu terdapat kendala lainya seperti pencatatan stok barang kurang lengkap, mengakibatkan data yang ada stok di warung dengan catatan stock di buku sering mengalami perbedaan data. Berangkat dari permasalahan diatas penulis coba membuat aplikasi point of sale  untuk digunakan oleh warkop halim. Point of sale sendiri memiliki arti memproses data seperti pembelian, pen<PERSON><PERSON> , trans<PERSON>i hutang, retur penjualan, dan pelaporan transaksi yang diperlukan pebisnis membuat keputusan hasil dari penelitian ini yaitu menghasilkan suatu aplikasi point of sale untuk digunakan sebagai penjualan warung kopi. Kata Kunci : Point Of Sale, Penjualan, Warung Kopi. </p>", "Keywords": "", "DOI": "10.46576/syntax.v4i1.2892", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 90282, "JournalTitle": "Syntax : Journal of Software Engineering, Computer Science and Information Technology", "ISSN": "2776-7027", "EISSN": "2723-0538", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Harapan <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Harapan <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Harapan <PERSON>"}], "References": []}, {"ArticleId": *********, "Title": "Studi Konsumsi Baterai Pada Penggunaan Filter Sinar Biru Tablet Komputer Untuk Aktivitas Daring", "Abstract": "<p>Pengguna perangkat seluler di Indonesia semakin meningkat dari tahun ke tahun. Pada tahun 2022, tercatat lebih dari 370 juta pengguna seluler, melebihi total populasi negara. Penggunaan gawai yang semakin intens dapat meningkatkan risiko gangguan kesehatan, khus<PERSON><PERSON> terhadap organ penglihatan. Salah satu potensi bahaya dari perangkat seluler portabel yang dapat menyebabkan risiko kesehatan mata adalah paparan radiasi sinar biru dari layar gawai. Filter sinar biru, yang sudah disematkan oleh pabrikan gawai pada produk seluler terkini, dapat menjadi salah satu solusi yang bisa dimanfaatkan oleh pengguna untuk meminimalkan risiko tersebut. Namun, masih banyak pemilik perangkat yang belum menyadari keberadaan fitur ini. <PERSON><PERSON> itu, penggunaan sejumlah fitur pada gawai diprediksi akan meningkatkan konsumsi daya baterai. Artikel ini akan menyajikan hasil survei pengguna terkait pengetahuan mereka tentang fitur filter sinar biru. <PERSON><PERSON><PERSON> lanju<PERSON>, pengukuran konsumsi baterai menggunakan aplikasi Accubattery juga dilakukan terhadap penggunaan filter sinar biru untuk beberapa aktivitas daring. Hasil survei menunjukkan bahwa lebih banyak pengguna yang belum mengetahui tentang filter sinar biru pada gawai yang mereka miliki. Hasil pengukuran daya baterai menunjukkan bahwa penggunaan filter dapat meningkatkan konsumsi baterai perangkat dengan peningkatan terbesar didapatkan untuk aktivitas menonton video secara daring, yaitu hampir dua kali lipat. Berdasar hasil perhitungan selisih konsumsi daya ketika filter diaktifkan dan tidak, pengguna direkomendasikan untuk mengaktifkan filter saat aktivitas rapat daring dan belajar daring, karena konsumsi daya dari kedua kegiatan tersebut tidak mengalami kenaikan yang signifikan.</p>", "Keywords": "<PERSON><PERSON>;<PERSON>lter;Kons<PERSON>i Baterai;<PERSON><PERSON>;Tablet", "DOI": "10.34148/teknika.v12i2.608", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 66608, "JournalTitle": "Teknika", "ISSN": "2549-8037", "EISSN": "2549-8045", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Ke<PERSON>ama<PERSON> dan <PERSON>, Politeknik <PERSON>, <PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Ke<PERSON>ama<PERSON> dan <PERSON>, Politeknik <PERSON>, <PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Program Studi Teknik Ke<PERSON>ama<PERSON> dan <PERSON>, Politeknik <PERSON>, <PERSON><PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi <PERSON>, Politeknik <PERSON>, <PERSON><PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi <PERSON>, Politeknik <PERSON>, <PERSON><PERSON>"}], "References": []}, {"ArticleId": 108705829, "Title": "ConvFormer: parameter reduction in transformer models for 3D human pose estimation by leveraging dynamic multi-headed convolutional attention", "Abstract": "<p>Recently, fully-transformer architectures have replaced the defacto convolutional architecture for the 3D human pose estimation task. In this paper, we propose ConvFormer , a novel convolutional transformer that leverages a new dynamic multi-headed convolutional self-attention mechanism for monocular 3D human pose estimation. We designed a spatial and temporal convolutional transformer to comprehensively model human joint relations within individual frames and globally across the motion sequence. Moreover, we introduce a novel notion of temporal joints profile for our temporal ConvFormer that fuses complete temporal information immediately for a local neighborhood of joint features. We have quantitatively and qualitatively validated our method on three common benchmark datasets: Human3.6 M, MPI-INF-3DHP, and HumanEva. Extensive experiments have been conducted to identify the optimal hyper-parameter set. These experiments demonstrated that we achieved a significant parameter reduction relative to prior transformer models while attaining State-of-the-Art (SOTA) or near SOTA on all three datasets. Additionally, we achieved SOTA for Protocol III on H36M for both GT and CPN detection inputs. Finally, we obtained SOTA on all three metrics for the MPI-INF-3DHP dataset and for all three subjects on HumanEva under Protocol II.</p>", "Keywords": "Transformers; 3D human pose estimation; Dynamic convolutions; Monocular motion capture", "DOI": "10.1007/s00371-023-02936-5", "PubYear": 2024, "Volume": "40", "Issue": "4", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": "Inseer, Coralville, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Inseer, Coralville, USA"}], "References": [{"Title": "3D human pose estimation by depth map", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "7", "Page": "1401", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 108706141, "Title": "Incorporating a Web-Based System to Implement Services and Recommendation System for International Students in Northeastern University (NEU)", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijcseit.2023.13301", "PubYear": 2023, "Volume": "13", "Issue": "1/2/3", "JournalId": 19790, "JournalTitle": "International Journal of Computer Science, Engineering and Information Technology", "ISSN": "2231-3605", "EISSN": "2231-3117", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108706306, "Title": "Detection of Indonesian Food to Estimate Nutritional Information Using YOLOv5", "Abstract": "<p>Currently, the development of online food delivery service applications is very popular. The application offers convenience in finding and fulfilling food needs. That circumstance has an impact such as not controlling the type and amount of food consumed. Therefore, to maintain a healthy lifestyle, people need to eat healthy and nutritious food. The goal of this research is to build a model using the YOLOv5 model that can detect images of Indonesian food so that nutritional estimation can then be carried out by taking information per serving data sourced from the FatSecret Indonesia website. The methods of this research include data collection, data pre-processing, training, testing, evaluation, image detection, and model export. The outcome of this research is an object detection model that is ready to be implemented in android applications or websites to detect images of Indonesian food which can be estimated for each nutrient. Based on the detection results, 98.6% for an average of a curacy, 95% for precision, 95.3% for recall, and 95% for F1-Score were obtained. The results of the detection are then used to estimate nutrition by taking information per portion from the FatSecret Indonesia website. From the experiments that were carried out on seven pictures of Indonesian food, the estimation was carried out well by displaying various nutritional information including energy, protein, fat, and carbohydrates.</p>", "Keywords": "YOLOv5;Object Detection;Nutritions", "DOI": "10.34148/teknika.v12i2.636", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 66608, "JournalTitle": "Teknika", "ISSN": "2549-8037", "EISSN": "2549-8045", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Universitas Amikom Purwokerto, Purwokerto, Central Java"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Universitas Amikom Purwokerto, Purwokerto, Central Java"}, {"AuthorId": 3, "Name": "Pungkas Subarkah", "Affiliation": "Department of Informatics, Universitas Amikom Purwokerto, Purwokerto, Central Java"}], "References": []}, {"ArticleId": 108706336, "Title": "Research Advances and Insights on Trade Liberalisation Based on CiteSpace Analysis", "Abstract": "With the unfolding of globalization, there is a trend of trade liberalization in the world today. Trade liberalization has also become a major research hotspot in recent years. This paper presents a statistical analysis and summary of the literature related to trade liberalization research in the WOS database from 2000 to 2022 through the use of keyword co-occurrence networks, cluster analysis and emergent word analysis in visualization software (CiteSpace), in order to provide reference and support for theoretical research innovation in trade liberalization research.", "Keywords": "", "DOI": "10.23977/infse.2023.040601", "PubYear": 2023, "Volume": "4", "Issue": "6", "JournalId": 93764, "JournalTitle": "Information Systems and Economics", "ISSN": "2523-6407", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 108706417, "Title": "Heritability of morphological and behavioural traits in evolving robots", "Abstract": "In the field of evolutionary robotics, choosing the correct genetic representation is a complicated and delicate matter, especially when robots evolve behaviour and morphology at the same time. One principal problem is the lack of methods or tools to investigate and compare representations. In this paper we introduce and evaluate such a tool based on the biological notion of heritability. Heritability captures the proportion of phenotypic variation caused by genotypic variation and is often used to better understand the transmissibility of traits in real biological systems. As a proof of concept, we compare the heritability of various robot traits in two systems, one using a direct (tree based) representation and one using an indirect (grammar based) representation. We measure changes in heritability during the course of evolution and investigate how direct and indirect representation can be biased towards more exploration or exploitation throughout the course of evolution. The empirical study shows that heritability can be a useful tool to analyze different representations without running complete evolutionary processes using them.", "Keywords": "Evolutionary robotics; Morphological evolution; Representation; Heritability", "DOI": "10.1007/s12065-023-00860-0", "PubYear": 2024, "Volume": "17", "Issue": "3", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, Amsterdam, The Netherlands; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, Amsterdam, The Netherlands; Autonomous Robotics Research Centre, Technology Innovation Institute, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, Amsterdam, The Netherlands"}, {"AuthorId": 4, "Name": "J<PERSON><PERSON><PERSON> Ellers", "Affiliation": "Vrije Universiteit Amsterdam, Amsterdam, The Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, Amsterdam, The Netherlands"}], "References": [{"Title": "Environmental Regulation Using Plasticoding for the Evolution of Robots", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "107", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Learning directed locomotion in modular robots with evolvable morphologies", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107688", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 108706553, "Title": "RANCANG BANGUNG SISTEM INFORMASI BOOKING TEMPAT PADA SALON LELY GUNA MEMUDAHKAN KONSUMEN BERBASIS WEB", "Abstract": "<p>Salon Lely merupakan salah satu toko kecantikan yang berlokasi di Jl.AR.Hakim, Gg. Rahayu II No.1 B Medan. Salon Lely ini menyediakan jasa perawatan rambut dan kecantikan seperti potong rambut, creambath, smoothing, rebonding, hair toning, hair colouring, catok, keriting, facial dan layanan salon lainnya. Permasalahan sering terjadi di salon lely sering terjadinya antrian panjang saat customer datang ke lokasi yang mengakibatkan harus mengantri terlebih dahulu, para customer juga tidak bisa melakukan pemesanan online tanpa datang langsung ke lokasi salon. Berangkat dari permasalahan diatas penulis coba membuat aplikasi sistem informasi pemesanan tempat guna meningkatkan pelayanan kepada customer tujuan dilakukannnya penelitian ini yaitu untuk menyediakan fasilitas pelayanan konsumen berupa pemesanan yang dapat dilakukan secara online dan dari penelitian menghasilkan Aplikasi sistem informasi pemesanan salon Lely dapat mempermudah pelanggan dalam melakukan pemesanan secara online Kata Kunci : Salon Lely, Sistem Informasi, Pemesanan Online, Konsumen</p>", "Keywords": "", "DOI": "10.46576/syntax.v4i1.2891", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 90282, "JournalTitle": "Syntax : Journal of Software Engineering, Computer Science and Information Technology", "ISSN": "2776-7027", "EISSN": "2723-0538", "Authors": [{"AuthorId": 1, "Name": "Jopandi Syahputra Lubis", "Affiliation": "Universitas Harapan <PERSON>"}, {"AuthorId": 2, "Name": "Irvan Irvan", "Affiliation": "Universitas Harapan <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Harapan <PERSON>"}], "References": []}, {"ArticleId": *********, "Title": "A survey on deep learning-based image forgery detection", "Abstract": "Image is known as one of the communication tools between humans. With the development and availability of digital devices such as cameras and cell phones , taking images has become easy anywhere. Images are used in many medical, forensic medicine, and judiciary applications. Sometimes images are used as evidence, so the authenticity and reliability of digital images are increasingly important. Some people manipulate images by adding or deleting parts of an image, which makes the image invalid. Therefore, image forgery detection and localization are important. The development of image editing tools has made this issue an important problem in the field of computer vision . In recent years, many different algorithms have been proposed to detect forgery in the image and pixel levels . All these algorithms are categorized into two main methods: traditional and deep-learning methods. The deep learning method is one of the important branches of artificial intelligence science. This method has become one of the most popular methods in most computer vision problems due to the automatic identification and prediction process and robustness against geometric transformations and post-processing operations. In this study, a comprehensive review of image forgery types, benchmark datasets, evaluation metrics in forgery detection, traditional forgery detection methods, discovering the weaknesses and limitations of traditional methods, forgery detection with deep learning methods, and the performance of this method is presented. According to the expansion of deep-learning methods and their successful performance in most computer vision problems, our main focus in this study is forgery detection based on deep-learning methods. This survey can be helpful for a researcher to obtain a deep background in the forgery detection field. Introduction Digital images are one of the significant tools for exchanging information. Every day, a large number of images are produced by cameras and smartphones. An estimate suggests that around 1.4 trillion digital photographs were generated in the year 2020 alone [1]. Despite the different and powerful applications, social networks such as Instagram, Telegram, Facebook, and the World Wide Web, these images are broadcast instantly all over the world. User-friendly and reducing the time to send concepts and news through images compared to official documents has caused the use of images to increase day by day. However, some people and organizations abuse this issue and pursue political, cultural, economic, and social goals by manipulating and changing the concepts of images to distribute false information [2]. With the increase in advanced image editing software, such as Adobe Photoshop, which is available as open-source and free software, digital images can be easily manipulated and edited without leaving any clues. Manipulated images can cause problems, especially in medical diagnosis, court sentences, patent infringements, political issues, and insurance claims. The manipulation or forgery is performed by hiding or adding incorrect information to digital images. As a result, the integrity and originality of the structure, texture, color, and frequency of these images are manipulated and these images will not be valid [3]. For example, manipulating the CT scan images of healthy patients to show them as Covid-19 patients was an unethical action that was carried out in the medical field [4]. In another example, a journalist captured a photo of the first day of the G-20 summit in Germany in 2017. This image was manipulated and a photo of the Russian president was added to the original image and published by a Facebook user. This image was shared thousands of times on different social media and news portals, which caused a lot of confusion and discussion. This forgery image can force political leaders to make wrong decisions and political movements, and even start a nuclear war [5]. Therefore, forgery detection is one of the important topics of machine vision. Digital images can be forged by various techniques. Existing techniques for digital image forgery detection can be broadly classified into two major categories: active/non-blind approach and passive/blind approach [6], [7], [8]. These categories and their sub-categories are depicted in Fig. 1. • Active approach: In the active approach, the image is pre-processed and information is embedded into the original image. Some examples of active approaches include digital watermarking and digital signatures. This approach requires special software, hardware, and the original image to enter information into the image or extract information from the image. Without these requirements, this approach will be impossible and ineffective. Moreover, in this approach, forgery detection is easy if the prerequisites are available [9]. • Passive approach: In the passive approach, which is popularly known as the blind approach, no pre-processing of the image is required. The passive approach detects whether the image is a forgery or not and discovers inconsistencies by analyzing the contents and structure of the image. This approach is more favored than an active approach because there is no need for any prior information [9]. Some examples of passive approaches include copy-move forgery, image splicing, image retouching, and object removal. This survey focuses on the passive approach types described in the following. • Copy-move forgery is one of the simplest and most common methods of image manipulation. This type of forgery involves copying one or more parts of the image and pasting it into other locations of the same image. The purpose of the method is mainly to hide some important information or insert some incorrect information into the image. Since the copied part belongs to the same image, it is compatible with the whole image in terms of structure and texture. In addition to copying and pasting, geometric transformations such as rotation, scaling, and post-processing operations such as blurring, changing brightness, compression, and adding noise are performed on the image. These transformations and operations cause the manipulated regions to be not easily recognizable by humans [10], [11]. An example of a copy-move forgery image is shown in Fig. 2 • Image splicing is a method that combines two or more images to make a single image. In the resulting image, there are edges and blur regions at the location of splicing. Using image editing tools, these regions can be merged with the image so that the human vision will not detect the forgery [6], [12]. An example of image splicing is shown in Fig. 3 • Image retouching is the least harmful forgery method among other image forgery methods. This method enhances or reduces the feature of pixels on the image [13]. Also, it is a popular method in photo editing applications and magazines [2]. This approach makes images more attractive by increasing or decreasing certain features such as pixel color. An example of image retouching is shown in Fig. 4 • Object removal is known as a destructive forgery because it might change the semantic content of the image. There are two categories of object removal techniques [14]: copy-move and image inpainting. Copy-move removes the desired object by copying a region from the main image or another image and pasting it to the region of the removed object. Copy-move is widely used for object removal because of its simplicity. Image inpainting was originally presented to restore damaged information and remove scratches from old photographs. This method removes the object by filling the location of the object with the surrounding pixels. Image inpainting can simultaneously maintain both textural and structural consistency. An example of image inpainting is shown in Fig. 5 Forgery detection is one of the most interesting topics for researchers in the last decade. Therefore, many studies have been presented to detect a forgery in two main categories: traditional and deep learning methods. Some excellent reviews of the two methods can be found in [2], [16]. Despite the appropriate review articles in the forgery detection field, the purpose of our review article is to comprehensively examine two methods of forgery detection to compare and discover their strengths, weaknesses, and challenges. On the other hand, according to the point that deep learning methods perform well in most problems if the hardware conditions and appropriate dataset are provided, the other focus of our article is to examine forgery detection with deep learning from two new and different aspects: using different strategies of deep learning methods and different deep learning architectures. The remainder of the paper is organized into the following sections: Section 2 gives a brief background of different deep learning architectures and various evaluation metrics used for forgery detection. Section 3 explains the details of benchmark datasets in copy-move, splicing, and inpainting forgery detection. In Section 4, conventional forgery detection methods with their details are explained and compared. A comprehensive review of deep learning-based forgery detection with different strategies and deep learning architectures is given in Section 5. Finally, the conclusion of all forgery detection methods, their disadvantages and advantages, existing challenges, and future suggestions are given in Section 6. Section snippets Background Nowadays, most forgery detection methods are based on machine learning and deep learning methods. This section presents a summary of machine learning algorithms, deep learning architectures, and evaluation metrics according to reviewed studies. Benchmark dataset The datasets are the main tools for measuring and comparing different algorithms in any research field [39]. When researchers use different datasets or their own-prepared datasets, they will not be able to compare their results with the others. It would be more convenient for researchers to use some standard and common datasets. There are several datasets in the forgery detection field. The following are some of the most important examples of them. Conventional forgery detection methods Conventional Copy Move Forgery Detection (CMFD) methods can be mainly classified into two categories: Block-based and Keypoint-based methods. These methods include three consecutive steps for forgery detection: feature extraction, feature matching, and forgery localization [9], [58]. Figure 16 shows the steps of two conventional CMFD methods [59]. The description of each step is given below. 1. Pre-processing step: The first step in some forgery detection algorithms is pre-processing. In this step, Forgery detection with deep learning In recent years, deep learning methods have been considered in computer vision such as forgery detection. The deep learning method can automatically extract hierarchical features from the data. This method learns rich semantic representations and avoids manual feature development. The biggest problem with deep learning methods is the need for a lot of data for the training process [104]. Following this, some solutions to solve this problem are mentioned. Conclusion This paper has surveyed different forgery methods, forgery datasets, and forgery detection methods using block-based, keypoint-based, and deep learning methods at the whole image and pixel levels. Studies show that the calculation time and complexity of the block-based methods are high, and they perform poorly with some geometric transformations and post-processing. The keypoint-based method is more robust to geometric transformations. This method performs poorly in detecting small forgery Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Fatemeh Zare Mehrjardi received her BSc in software engineering from Yazd University in 2013 and her MSc in Computer Engineering, and Artificial Intelligence from Yazd University in 2015. She is currently a PhD candidate in Artificial Intelligence at Yazd University. Her research interests are Image Processing, Computer Vision, Machine Learning, and Deep learning. References (156) Z. He et al. Digital image splicing detection based on Markov features in DCT and DWT domain Pattern Recognit. (2012) D. Chauhan et al. Survey on keypoint based copy-move forgery detection methods on image Procedia Comput. Sci. (2016) Z. Deng et al. Efficient kNN classification algorithm for big data Neurocomputing (2016) J. Gu et al. Recent advances in convolutional neural networks Pattern Recognit. (2018) R. Ghosh et al. RNN based online handwritten word recognition in Devanagari and Bengali scripts using horizontal zoning Pattern Recognit. (2019) J. Jam et al. A comprehensive review of past and present image inpainting methods Comput. Vision Image Understanding (2021) J. Yang et al. A novel copy-move forgery detection algorithm via two-stage filtering Digit. Signal Process. (2021) X. Bi et al. Fast copy-move forgery detection using local bidirectional coherency error refinement Pattern Recognit. (2018) Y. Wang et al. Robust and accurate detection of image copy-move forgery using PCET-SVD and histogram of block similarity measures J. Inf. Secur. Appl. (2020) T. Mahmood et al. A robust technique for copy-move forgery detection and localization in digital images via stationary wavelet and discrete cosine transform J. Vis. Commun. Image Represent. (2018) A. Hegazi et al. An improved copy-move forgery detection based on density-based clustering and guaranteed outlier removal J. King Saud Univ.-Comput. Inf. Sci. (2021) F. Yang et al. Copy-move forgery detection based on hybrid features Eng. Appl. Artif. Intell. (2017) S. Tyagi et al. A detailed analysis of image and video forgery detection techniques Vis. Comput. (2022) A.H. Saber et al. A survey on image forgery detection using different forensic approaches Adv. Sci. Technol. Eng. Syst. J. (2020) K. Chandrasegaran et al. Discovering transferable forensic features for CNN-generated images detection Computer Vision–ECCV 2022: 17th European Conference, Tel Aviv, Israel, October 23–27, 2022, Proceedings, Part XV (2022) S.H. Gill et al. Extended forgery detection framework for COVID-19 medical data using convolutional neural network Comput. Mater. Continua (2021) M.M. Islam et al. A robust forgery detection method for copy–move and splicing attacks in images Electronics (Basel) (2020) B. Shwetha et al. Digital image forgery detection techniques: a survey ACCENTS Trans. Inf. Secur. (2017) Z. Zhang et al. A survey on passive image copy-move forgery detection J. Inf. Process. Syst. (2018) I.A. Zedan et al. Copy move forgery detection techniques: a comprehensive survey of challenges and future directions Int. J. Adv. Comput. Sci. Appl. (2021) B. Diallo et al. Robust forgery detection for compressed images using CNN supervision Forensic Sci. Int. Rep. (2020) R. Thakur et al. Recent advances in digital image manipulation detection techniques: a brief review Forensic Sci. Int. (2020) K. Amandeep et al. Digital image forgery and techniques of forgery detection: a brief review Int. J. Tech. Res. Sci. (2016) Z. Liang et al. A robust forgery detection algorithm for object removal by exemplar-based image inpainting Multimed. Tools Appl. (2018) N. Kumar et al. Semantic segmentation-based image inpainting detection Innovations in Electrical and Electronic Engineering (2021) M. Zanardelli et al. Image forgery detection: a survey of recent deep-learning approaches Multimed. Tools Appl. (2022) L.M. Dang et al. Sensor-based and vision-based human activity recognition: a comprehensive survey Pattern Recognit. (2020) A.S. Murugavel et al. Hierarchical multi-class SVM with ELM kernel for epileptic EEG signal classification Med. Biol. Eng. Comput. (2016) B. Kamiński et al. A framework for sensitivity analysis of decision trees Cent. Eur. J. Oper. Res. (2018) S. Albelwi et al. A framework for designing the architectures of deep convolutional neural networks Entropy (2017) N.C. Dang et al. Sentiment analysis based on deep learning: acomparative study Electronics (Basel) (2020) R. Fu et al. Using LSTM and GRU neural network methods for traffic flow prediction 2016 31st Youth Academic Annual Conference of Chinese Association of Automation (YAC) (2016) R. Girshick et al. Region-based convolutional networks for accurate object detection and segmentation IEEE Trans. Pattern Anal. Mach. Intell. (2015) R. Girshick et al. Rich feature hierarchies for accurate object detection and semantic segmentation Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (2014) R. Girshick Fast r-CNN Proceedings of the IEEE International Conference on Computer Vision (2015) S. Ren et al. Faster r-CNN: towards real-time object detection with region proposal networks Adv. Neural Inf. Process. Syst. (2015) K. He et al. Mask r-CNN Proceedings of the IEEE International Conference on Computer Vision (2017) J. Redmon et al. You only look once: unified, real-time object detection Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (2016) W. Liu et al. SSD: single shot multibox detector Computer Vision–ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11–14, 2016, Proceedings, Part I 14 (2016) W.H.L. Pinaya et al. Autoencoders Machine Learning (2020) A. Creswell et al. Generative adversarial networks: an overview IEEE Signal Process. Mag (2018) O.M. AlQershi et al. Evaluation of copy-move forgery detection: datasets and evaluation metrics Multimed. Tools Appl. (2018) V.S. Kulkarni et al. Comparison of methods for detection of copy-move forgery in digital images Spvryan’s Int. J. Eng. Sci. Technol (2014) M. Gardella et al. Forgery detection in digital images by multi-scale noise estimation J. Imaging (2019) K.D. Kadam et al. Multiple image splicing dataset (MISD): a dataset for multiple splicing Data (2021) A. Torralba et al. Unbiased look at dataset bias Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR) (2011) I. Amerini et al. Copy-move forgery detection and localization by means of robust clustering with j-linkage Signal Process. Image Commun. (2013) D. Tralic et al. CoNoFoD – new database for copy-move forgery detection Proceedings ELMAR-2013 (2013) E.A. Armas Vega et al. Digital images authentication technique based on DWT, DCT and local binary patterns Sensors (2018) B. Wen et al. Coverage – a novel database for copy-move forgery detection 2016 IEEE International Conference on Image Processing(ICIP) (2016) View more references Cited by (0) Recommended articles (6) Research article A bibliography of pixel-based blind image forgery detection techniques Signal Processing: Image Communication, Volume 39, Part A, 2015, pp. 46-74 Show abstract With the advent of powerful image editing tools, manipulating images and changing their content is becoming a trivial task. Now, you can add, change or delete significant information from an image, without leaving any visible signs of such tampering. With more than several millions pictures uploaded daily to the net, the move towards paperless workplaces, and the introduction of e-Government services everywhere, it is becoming important to develop robust detection methods to identify image tampering operations and validate the credibility of digital images. This led to major research efforts in image forensics for security applications with focus on image forgery detection and authentication. The study of such detection techniques is the main focus of this paper. In particular, we provide a comprehensive survey of different forgery detection techniques, complementing the limitations of existing reviews in the literature. The survey covers image copy-move forgery, splicing, forgery due to resampling, and the newly introduced class of algorithms, namely image retouching. We particularly discuss in detail the class of pixel-based techniques which are the most commonly used approaches, as these do not require any a priori information about the type of tampering. The paper can be seen as a major attempt to provide an up-to-date overview of the research work carried in this all-important field of multimedia. Research article A robust copy-move forgery detection technique based on discrete cosine transform and cellular automata Journal of Information Security and Applications, Volume 54, 2020, Article 102510 Show abstract Copy Move Forgery (CMF) is a type of digital image forgery in which an image region is copied and pasted to another location within the same image with malicious intent to misrepresent its meaning. To prevent misinterpretation of an image content, several Copy Move Forgery Detection (CMFD) methods have been proposed in the past. However, the existing methods show limited robustness on images altered with post-processing attacks such as noise addition, compression, blurring etc. In this paper, we propose a robust method for detecting copy-move forgeries under different post-processing attacks. We use Discrete Cosine Transform (DCT) to extract features from each block. Next, Cellular Automata is employed to construct feature vectors based on the sign information of the DCT coefficients. Finally, feature vectors are matched using the kd-tree based nearest-neighbor searching method to find the duplicated areas in the image. Experimental results show that the proposed method performs exceptionally well relative to the other state-of-the-art methods from the literature even when an image is heavily affected by the post-processing attacks, in particular, JPEG compression and additive white Gaussian noise. Furthermore, experiments confirm the robustness of the proposed method against the range of combined attacks. Research article PL-GNet: Pixel Level Global Network for detection and localization of image forgeries Signal Processing: Image Communication, 2023, Article 117029 Show abstract Unlike most Image Forgery Detection and Localization (IFDL) methods that classify the tampered regions by local patch, the features from the whole image in the spatial and frequency domains are leveraged in this paper to classify each pixel in the image. This paper proposes a high-confidence pixel level global network called PL-GNet to combat real-life image forgery that commonly involves different types and visually looks particularly realistic. There are three building blocks in our end-to-end PL-GNet framework: (1) An Encoding net allows us to extract the global features and generate the high-quality feature maps which indicate possible tampered regions. The newly designed first layer and backbone network architecture based on atrous convolution in Encoding net are adopted to capture the changes of pixel relationships and extract rich multi-scale spatial information. (2) A Long Short Term Memory (LSTM) network based on co-occurrence matrix is designed to capture the tampering traces and the discriminative features between manipulated and non-manipulated regions. (3) A Decoding net that incorporates the output of Encoding net and LSTM network learns the mapping from low-resolution feature maps to pixel-wise prediction masks. Furthermore, we conduct a series of ablation experiments to optimize the design of the Encoding network systematically. Extensive experiments on the six challenging datasets demonstrate that our PL-GNet outperforms each subnetwork and consistently achieves state-of-the-art performance compared to alternative methods over three evaluation metrics. Research article Visual Secret Sharing of Gray and Color Images using Fuzzy Random Grids Applied Soft Computing, Volume 146, 2023, Article 110648 Show abstract Visual secret sharing is a method used to secure images by converting a secret image into multiple shares so that the original image cannot be recognized from individual shares, and no calculations are required during the image decryption, but the original image can be visually seen by stacking the shares. However, one of the significant challenges in this method is the increased size of the shares and the recovered image, which is called pixel expansion. Researchers have proposed several methods to solve this problem, but many of them are limited to binary images, and they cannot handle gray and color images without converting them to binary. This study proposes a novel fuzzy random grid-based method to directly encrypt gray and color images without any pixel expansions. In this method, fuzzy random grids with decimal values between 0.0 to 1.0 are generated during the encryption stage, and fuzzy operators are used for the decryption stage. The evaluation results demonstrate the ability of the proposed solution in encrypting gray and color images without converting them to binary and without any pixel expansions. The individual shares of the proposed method do not reveal any information from the original image, making it a secure method. The quality of the decrypted images has been evaluated using both subjective and objective evaluation methods such as PSNR and SSIM metrics, which show state-of-the-art results. Research article Magnifying multimodal forgery clues for Deepfake detection Signal Processing: Image Communication, Volume 118, 2023, Article 117010 Show abstract Advancements in computer vision and deep learning have led to difficulty in distinguishing the generated Deepfake media. In addition, recent forgery techniques also modify the audio information based on the forged video, which brings new challenges. However, due to the cross-modal bias, recent multimodal detection methods do not well explore the intra-modal and cross-modal forgery clues, which leads to limited detection performance. In this paper, we propose a novel audio-visual aware multimodal Deepfake detection framework to magnify intra-modal and cross-modal forgery clues. Firstly, to capture temporal intra-modal defects, Forgery Clues Magnification Transformer (FCMT) module is proposed to magnify forgery clues based on sequence-level relationships. Then, the Distribution Difference based Inconsistency Computing (DDIC) module based on Jensen–Shannon divergence is designed to adaptively align multimodal information for further magnifying the cross-modal inconsistency. Next, we further explore spatial artifacts by connecting multi-scale feature representation to provide comprehensive information. Finally, a feature fusion module is designed to adaptively fuse features to generate a more discriminative feature. Experiments demonstrate that the proposed framework outperforms independently trained models, and at the same time, yields superior generalization capability on unseen types of Deepfake. Research article A novel copy-move forgery detection algorithm via two-stage filtering Digital Signal Processing, Volume 113, 2021, Article 103032 Show abstract With the wide application of simple image editing software, forgery images have become severe social problems with extremely damaging effects. The copy-move forgery technique is commonly used to temper an image by copying some regions of an image and pasting them somewhere in the same image. In this paper, a novel algorithm is proposed for the detection of copy-move forgery. First, the keypoints of the input image are computed using a keypoint extraction algorithm. Second, a keypoint-matching algorithm is used to match similar keypoints as the candidate keypoint pairs. Third, a proposed novel Two-Stage Filtering algorithm, including the Grid-Based Filter and the Clustering-Based Filter, is applied to filter out most of the false matching keypoint pairs. Subsequently, image matting is achieved by the Delaunay triangulation algorithm so that the marked areas indicate the forgery regions. Three copy-move forgery datasets are used to compare the performances of the proposed algorithm with some state-of-the-art algorithms. The experimental results demonstrate that the proposed algorithm's overall performance is superior to other solutions for detecting copy-move forgery images. Fatemeh Zare Mehrjardi received her BSc in software engineering from Yazd University in 2013 and her MSc in Computer Engineering, and Artificial Intelligence from Yazd University in 2015. She is currently a PhD candidate in Artificial Intelligence at Yazd University. Her research interests are Image Processing, Computer Vision, Machine Learning, and Deep learning. Ali Mohammad Latif obtained his first degree in Electronic Engineering from Isfahan University of Technology in 1993 and his MSc in Electronic Engineering from Amirkabir University of Technology in 1996. In 2001, he joined the academic staff at the electrical engineering department of Yazd University. He worked as a PhD candidate at the University of Isfahan where he obtained his PhD In 2011, he joined the academic staff at the computer department of Yazd University. His research interests are Digital Image Processing, watermarking, and Cryptography. Mosen Sardari Zarchi obtained his first degree in Computer Science from Yazd University in 2007, his M. Sc. in Computer Engineering, Artificial Intelligence from Isfahan University in 2009, and his PhD in Computer Engineering, Artificial Intelligence from Isfahan University in 2015. he joined the academic staff at the computer department of Meybod University in 2015. His research interests are pattern recognition, image processing, machine learning, deep learning, and image retrieval. Razieh Sheikhpour received her PhD in Computer Engineering from Yazd University, Yazd, Iran. Then, she was a Postdoctoral researcher in the Computer Engineering department of Yazd University. Currently, she is an assistant professor at the department of Computer Engineering at Ardakan University, Ardakan, Iran. Her research interests include machine learning, data mining, semi-supervised feature selection, and bioinformatics. View full text © 2023 Published by Elsevier Ltd. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.109778", "PubYear": 2023, "Volume": "144", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Yazd University, Yazd, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, Yazd University, Yazd, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Meybod University, Meybod, Yazd, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Ardakan University, PO Box 184, Ardakan, Iran"}], "References": [{"Title": "Image Inpainting: A Review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "2", "Page": "2007", "JournalTitle": "Neural Processing Letters"}, {"Title": "An efficient copy move forgery detection using deep learning feature extraction and matching algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7355", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A hybrid copy-move image forgery detection technique based on Fourier-Mellin and scale invariant feature transforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "8197", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient method for image forgery detection based on trigonometric transforms and deep learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "18221", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel deep learning framework for copy-moveforgery detection in images", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19167", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A review of digital image forensics", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "85", "Issue": "", "Page": "106685", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Sensor-based and vision-based human activity recognition: A comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107561", "JournalTitle": "Pattern Recognition"}, {"Title": "Copy-move forgery detection using image blobs and BRISK feature", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "Page": "26045", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Low dimensional DCT and DWT feature based model for detection of image splicing and copy-move forgery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "39-40", "Page": "29977", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A passive technique for detecting copy-move forgeries by image feature matching", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "43-44", "Page": "31759", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A deep multimodal system for provenance filtering with universal forgery detection and localization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "17025", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comprehensive review of past and present image inpainting methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "203", "Issue": "", "Page": "103147", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Encoder-decoder based convolutional neural networks for image forgery detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "16", "Page": "22611", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Copy move and splicing forgery detection using deep convolution neural network, and semantic segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "3", "Page": "3571", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Identification of copy-move and splicing based forgeries using advanced SURF and revised template matching", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "16", "Page": "23877", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Extended Forgery Detection Framework for COVID-19 Medical Data Using Convolutional Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "3", "Page": "3773", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Hybrid features and semantic reinforcement network for image forgery detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "2", "Page": "363", "JournalTitle": "Multimedia Systems"}, {"Title": "Detection of Copy-Move Forgery in Digital Image Using Multi-scale, Multi-stage Deep Learning Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "1", "Page": "75", "JournalTitle": "Neural Processing Letters"}, {"Title": "Multiple Image Splicing Dataset (MISD): A Dataset for Multiple Splicing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "10", "Page": "102", "JournalTitle": "Data"}, {"Title": "Copy move forgery detection and segmentation using improved mask region-based convolution network (RCNN)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "109778", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 108706645, "Title": "A Comparative Analysis of Data Mining Methods and Hierarchical Linear Modeling Using PISA 2018 Data", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijdms.2023.15301", "PubYear": 2023, "Volume": "15", "Issue": "2/3", "JournalId": 16205, "JournalTitle": "International Journal of Database Management Systems", "ISSN": "0975-5985", "EISSN": "0975-5705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108706678, "Title": "An Analysis of the Requirements for Smart Guiding Services in Museums Using the Kano-AHP Method", "Abstract": "This study aims to enhance the visiting experience in museums by investigating visitor demands for a smart guiding service system. Through the utilization of semi-structured interviews and field observations, the research explores visitor needs comprehensively. The Kano model is employed to categorize the various requirements, and the hierarchical analysis approach (AHP) is used to determine attribute weights, ensuring a thorough evaluation. By creating a hierarchical structure model for the smart guiding service system in museums and obtaining a comprehensive weight ranking for each indicator, the study provides a solid foundation for the development of effective solutions. Based on the ranking, several proposals are generated, presenting actionable insights for the implementation of a smart guiding service system. The findings emphasize the value of the Kano-AHP model in analyzing visitor demands, offering valuable guidance for museums aiming to establish an efficient and user-centric smart guiding service system.", "Keywords": "", "DOI": "10.23977/jaip.2023.060407", "PubYear": 2023, "Volume": "6", "Issue": "4", "JournalId": 52855, "JournalTitle": "Journal of Artificial Intelligence Practice", "ISSN": "2371-8315", "EISSN": "2371-8412", "Authors": [], "References": []}, {"ArticleId": 108706802, "Title": "A close look at a systematic method for analyzing sets of security advice", "Abstract": "<p>We carry out a detailed analysis of the security advice coding method (SAcoding) of <PERSON> et al., which is designed to analyze security advice in the sense of measuring actionability and categorizing advice items as practices, policies, principles, or outcomes. The main part of our analysis explores the extent to which a second coder’s assignment of codes to advice items agrees with that of a first, for a dataset of 1013 security advice items nominally addressing Internet of Things devices. More broadly, we seek a deeper understanding of the soundness and utility of the SAcoding method, and the degree to which it meets the design goal of reducing subjectivity in assigning codes to security advice items. Our analysis results in suggestions for modifications to the coding tree methodology, and some recommendations. We believe the coding tree approach may be of interest for analysis of qualitative data beyond security advice datasets alone.</p>", "Keywords": "", "DOI": "10.1093/cybsec/tyad013", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 2358, "JournalTitle": "Journal of Cybersecurity", "ISSN": "2057-2085", "EISSN": "2057-2093", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Carleton University , 1125 Colonel <PERSON> Drive, Ottawa, K1S 5B6 ON , Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Carleton University , 1125 Colonel <PERSON> Drive, Ottawa, K1S 5B6 ON , Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Carleton University , 1125 Colonel <PERSON> Drive, Ottawa, K1S 5B6 ON , Canada"}], "References": [{"Title": "Security Best Practices: A Critical Analysis Using IoT as a Case Study", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Privacy and Security"}, {"Title": "Systematic analysis and comparison of security advice as datasets", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "", "Page": "102989", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 108706856, "Title": "The Order of Numbers and the Goldbach Conjecture", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijcsa.2023.13301", "PubYear": 2023, "Volume": "13", "Issue": "3", "JournalId": 18292, "JournalTitle": "International Journal on Computational Science & Applications", "ISSN": "", "EISSN": "2200-0011", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108706940, "Title": "Penerapan REST API Untuk Aplikasi Reservasi Dokter Praktik Berbasis Android (Studi Kasus: Klinik dr. <PERSON><PERSON>)", "Abstract": "<p>Reservasi merupakan salah satu hal penting untuk dilakukan ketika pasien ingin berobat di suatu instansi kesehatan. Pada klinik dr. <PERSON><PERSON> ini, proses reservasi pasien untuk berobat masih menggunakan cara yang konvensional yaitu dengan memaksa pasien untuk melakukan antri dengan cara datang langsung ke klinik dr. <PERSON><PERSON>. Ke<PERSON><PERSON>, ketika pasien tiba di klinik, pasien diminta untuk mengambil nomor antrian yang tersedia pada klinik tersebut. Proses ini dinilai kurang efektif karena pasien harus melakukan antri yang panjang dan menunggu giliran untuk dipanggil ke ruang dokter. Adapun tujuan dari penelitian ini yaitu untuk membuat sebuah aplikasi android untuk melakukan reservasi supaya pasien yang ingin berobat dapat melakukan reservasi tanpa datang langsung ke klinik untuk mengambil nomor antrian yang tersedia pada klinik dr. <PERSON>dra <PERSON>ri ini. Pengembangan aplikasi ini dilakukan menggunakan metode RAD (Rapid Application Development) dengan tahapan Requirement Planning, User Design, Construction and Cutover. Hasil dari penelitian ini berupa aplikasi reservasi berbasis android dan didukung dengan website admin yang diimplementasikan di klinik dr. Candra Safitri supaya memudahkan pasien dalam melakukan reservasi. Berdasarkan pengujian aplikasi menggunakan metode Black Box, dapat ditarik kesimpulan bahwa hasil pengujian fungsionalitas dari website admin dan aplikasi mobile pasien semuanya sukses dan semua modul yang dibuat sudah sesuai dengan rancangan sistem.</p>", "Keywords": "Dokter Praktik;Reservasi;Rapid Application Development;Klinik;REST API", "DOI": "10.34148/teknika.v12i2.615", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 66608, "JournalTitle": "Teknika", "ISSN": "2549-8037", "EISSN": "2549-8045", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Informatika, Universitas Teknologi Yogyakarta, DI Yogyakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Wibowo", "Affiliation": "Program Studi Sistem Informasi, Universitas Teknologi Yogyakarta, DI Yogyakarta"}], "References": []}, {"ArticleId": 108706967, "Title": "Aplikasi Penentuan Kebutuhan Pelatihan Berbasis Kompetensi Untuk Peningkatan Kinerja Staf Analis Laboratorium", "Abstract": "<p>Di antara tahun 2010 hingga tahun 2015, Laboratorium Parahita sedikitnya menerima laporan kesalahan pemeriksaan < 5 kasus yang dikarenakan kelalaian petugas laboratorium. Belum tersedianya program pengadaan pelatihan yang sesuai dengan kebutuhan kompetensi analis laboratorium menjadi penyebab kelalaian tersebut, karena sebagian besar program pelatihan yang diadakan sama dengan karyawan divisi lainnya. Divisi Sumber Daya Manusia masih kesulitan dalam menentukan apa kebutuhan pelatihan yang tepat bagi analis laboratorium, terlebih lagi proses administrasi pengadaan pelatihan masih belum terotomasi membuat data Divisi Sumber Daya Manusia dan Divisi Laboratorium belum sinkron. Tujuan penelitian ini adalah pembuatan aplikasi berbasis website yang digunakan untuk menganalisis kebutuhan pelatihan bagi analis laboratorium. Data-data yang diperlukan dalam pembuatan aplikasi didapatkan dari hasil pengamatan tidak terstruktur terkait proses pengadaan pelatihan saat ini serta hasil wawancara kepada pihak-pihak terkait. Teknik wawancara semiterstruktur diterapkan untuk menggali permasalahan lebih terbuka. Sebanyak 4 jenis data berhasil didapatkan dan divalidasi oleh Laboratorium Parahita, yakni data level kompetensi, data jenis kompetensi dan toleransi gap, data standar penilaian, serta data materi uji kompetensi. Keempat data ini dianalisis dengan teori gap analysis untuk menunjukkan seberapa besar kesenjangan antara kompetensi yang dimiliki dengan kompetensi yang dibutuhkan Laboratorium, termasuk pada sisi level dan jenis kompetensi yang dibutuhkan. Pada pengembangannya, aplikasi ini menggunakan model SDLC Waterfall. Hasilnya, aplikasi memberikan 4 fitur penting yang dapat mendukung proses pengadaan pelatihan, yakni fitur penilaian uji kompetensi, rekapitulasi uji kompetensi, analisis kebutuhan pelatihan dengan gap analysis, dan fitur pengajuan rencana pengadaan pelatihan yang juga didalamnya dapat mencatat nama trainer dan anggaran yang dibutuhkan sehingga riwayat pengadaan pelatihan dapat terekam otomatis. Seluruh fitur pada aplikasi telah diuji coba menggunakan metode blackbox yang hasilnya semua fungsional aplikasi berjalan dengan baik.</p>", "Keywords": "Training Need Analysis;TNA;Kompetensi;Gap Analysis;Waterfall;Parahita", "DOI": "10.34148/teknika.v12i2.622", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 66608, "JournalTitle": "Teknika", "ISSN": "2549-8037", "EISSN": "2549-8045", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Program Studi Sistem Informasi, Universitas Hayam Wuruk <PERSON>, Surabaya, <PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Sistem Informasi, Universitas Dinamika, Surabaya, <PERSON><PERSON>"}], "References": []}, {"ArticleId": 108707010, "Title": "Real-time deployment of BI-RADS breast cancer classifier using deep-learning and FPGA techniques", "Abstract": "<p>Breast cancer is commonly recognized as the second most frequent malignancy in women worldwide. Breast cancer therapy includes surgical surgery, radiation therapy, and medication which can be exceedingly successful, with 90% or higher survival rates, especially when the condition is discovered early. This work is one such approach for early detection of breast cancer relying on the BI-RADS score. In this regard, a computer-aided-diagnosis system based on a bespoke Digital Mammogram Diagnostic Convolutional Neural Network (DMD-CNN) model that can aid in the categorization of mammogram breast lesions is proposed. Furthermore, a PYNQ-based acceleration through the Artix 7 FPGA is employed for deployment of DMD-CNN model’s hardware acceleration platform which is the first of its kind for breast cancer, yielding a performance accuracy of 98.2%, the proposed model exceeded the state-of-the-art approach. The comparative analysis performed in the study has shown that the proposed method has resulted in a 4% increase in accuracy and a good recognition rate of 96% when compared to the existing model. A k-fold cross-validation (k = 5, 7, 9 the reported accuracy score values are 96.2%, 97.5% and 98.1%, respectively) approach was used to test and assess the integrated system. Extensive testing using mammography datasets was carried out to determine the increased performance of the suggested approach. Experiments reveal that when compared to the DMD-CNN model acceleration to GPU, the suggested solution not only optimizes resource utilization but also decreases power consumption to 3.12 W. Hardware acceleration through FPGA resulted in processing and analyzing nearly 91 images in a second where a single image will be processed using CPU.</p>", "Keywords": "FPGA; Deep learning; CNN; Breast cancer; Bi-rads; Mammogram", "DOI": "10.1007/s11554-023-01335-2", "PubYear": 2023, "Volume": "20", "Issue": "4", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, SRM Institute of Science and Technology, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, SRM Institute of Science and Technology, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON>. <PERSON>", "Affiliation": "Department of Electronics and Communication, SRM Institute of Science and Technology, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Networking and Communication Engineering, SRM Institute of Science and Technology, Chennai, India; Department of Translational Medicine and Research, SRM Institute of Science and Technology, Chennai, India; Department of Radio-Diagnosis, SRM Institute of Science and Technology, Chennai, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Networking and Communication Engineering, SRM Institute of Science and Technology, Chennai, India; Department of Translational Medicine and Research, SRM Institute of Science and Technology, Chennai, India; Department of Radio-Diagnosis, SRM Institute of Science and Technology, Chennai, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Networking and Communication Engineering, SRM Institute of Science and Technology, Chennai, India; Department of Translational Medicine and Research, SRM Institute of Science and Technology, Chennai, India; Department of Radio-Diagnosis, SRM Institute of Science and Technology, Chennai, India"}], "References": [{"Title": "Breast cancer masses classification using deep convolutional neural networks and transfer learning", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "Page": "30735", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Automated Categorization of Brain Tumor from MRI Using CNN features and SVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8357", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A quantization assisted U-Net study with ICA and deep features fusion for breast cancer identification using ultrasonic data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e805", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 108707018, "Title": "A fairness‐aware task offloading method in edge‐enabled IIoT with multi‐constraints using AGE‐MOEA and weighted MMF", "Abstract": "By providing distributed and ultra‐low‐latency communication between industrial devices and resource components, the Industrial Internet of Things (IIoT) is at the forefront of a new trend. Such a distributed paradigm is viewed as a collection of autonomous computing resources utilized by multiple heterogeneous devices to achieve higher‐quality interconnection and data exchange. However, stringent requirements of exceptional service and fairness guarantees pose many formidable challenges. To this end, this study investigates the aforementioned concerns in an integrated manner and further proposes a fairness‐aware task offloading method, called FOIMAM. Specifically, the ‐norm is introduced to accommodate the Pareto plane under the non‐Euclidean geometry framework while the evaluation and elimination of low‐quality solutions are completed based on survival scores. Particularly, the fairness requirements are formulated as a multi‐constraint problem and resolved using weighted max‐min fairness. Eventually, numerical results indicate that the proposed method brings substantial improvement in both service efficiency and fairness guarantees.", "Keywords": "Fairness-aware;IIoT;MEC;MMF", "DOI": "10.1002/cpe.7858", "PubYear": 2024, "Volume": "36", "Issue": "10", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Engineering Huaqiao University  Quanzhou China;State Key Laboratory of Novel Software Technology Nanjing University  Nanjing China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering Huaqiao University  Quanzhou China;State Key Laboratory of Novel Software Technology Nanjing University  Nanjing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering Huaqiao University  Quanzhou China;State Key Laboratory of Novel Software Technology Nanjing University  Nanjing China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Software Engineering Shenzhen University  Shenzhen China;Department of Electrical and Computer Engineering University of British Columbia  Vancouver British Columbia Canada"}], "References": [{"Title": "Fairness-Aware Mechanism for Load Balancing in Distributed Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "2275", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Minimizing the Delay and Cost of Computation Offloading for Vehicular Edge Computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "5", "Page": "2897", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "DisCOV: Distributed COVID-19 Detection on X-Ray Images With Edge-Cloud Collaboration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "1206", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "QoE‐aware mobile computation offloading in mobile edge computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "11", "Page": "e6853", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "The global evaluation strategy for many‐objective partial collaborative computation offloading problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "2", "Page": "e7474", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 108707057, "Title": "Mean field games of energy storage devices: A finite difference analysis", "Abstract": "This paper considers a mean field game of energy storage devices (ESDs) in power systems, where electrovalency is affected by the storage population. The competition processes of ESDs are characterized by a pair of coupled partial differential equations (PDEs). The so‐called mean field equilibrium (MFE) is obtained by resolving the PDEs. The resulting MFE strategy is then broadcast to all local controllers in order to independently determine a control input for each device. Since PDEs are difficult to be explicitly calculated, a pair of difference schemes are designed for approximating the exact solutions of the PDEs. Then the sufficient condition of stability is analyzed. Based on the proposed difference schemes, a difference iterative scheme is designed for numerically calculating the MFE. Some simulation results show the effectiveness of the proposed iterative schemes.", "Keywords": "finite difference schemes;large populations;mean field games (MFGs);optimal control", "DOI": "10.1002/asjc.3161", "PubYear": 2023, "Volume": "25", "Issue": "6", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Control and Computer Engineering North China Electric Power University  Beijing China;Super High Voltage Branch of State Grid Xinjiang Electric Power Co., Ltd.  Xinjiang China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Control and Computer Engineering North China Electric Power University  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Control and Computer Engineering North China Electric Power University  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Control and Computer Engineering North China Electric Power University  Beijing China"}], "References": []}, {"ArticleId": 108707122, "Title": "How intergroup counter-empathy drives media consumption and engagement", "Abstract": "Purpose Social media is replete with malicious and unempathetic rhetoric yet few studies explain why these emotions are publicly dispersed. The purpose of the study is to investigate how the intergroup counter-empathic response called schadenfreude originates and how it prompts media consumption and engagement. Design/methodology/approach The study consists of two field surveys of 635 in-group members of two professional sports teams and 300 residents of California and Texas with political party affiliations. The analysis uses SEM quantitative methods. Findings Domain passion and group identification together determine the harmonious/obsessive tendencies of passion for an activity and explain the schadenfreude response toward the rival out-group. Group identification is a stronger driver of obsessive passion compared to harmonious passion. Schadenfreude directly influences the use of traditional media (TV, radio, domain websites), it triggers social media engagement (posting), and it accelerates harmonious passion's effects on social media posting. Research limitations/implications The study is limited by the groups used to evaluate the research model, sports, and politics. Social implications The more highly identified and passionate group members experience greater counter-empathy toward a rival. At extreme levels of group identification, obsessive passion increases at an increasing rate and may characterize extremism. Harboring feelings of schadenfreude toward the out-group prompts those with harmonious passion for an activity to more frequently engage on social media in unempathetic ways. Originality/value This study links the unempathetic, yet common emotion of schadenfreude with passion, intergroup dynamics, and media behavior.", "Keywords": "Passion;Group identification;Schadenfreude;Social media;Counter-empathy;Obsessive passion", "DOI": "10.1108/INTR-07-2022-0552", "PubYear": 2024, "Volume": "34", "Issue": "5", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Systems and Business Analytics, Baylor University , Waco, Texas, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Marketing, Baylor University , Waco, Texas, USA"}], "References": [{"Title": "The mediating influence of smartwatch identity on deep use and innovative individual performance", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "6", "Page": "977", "JournalTitle": "Information Systems Journal"}, {"Title": "Understanding the Role of Affordances in Promoting Social Commerce Engagement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "287", "JournalTitle": "International Journal of Electronic Commerce"}]}, {"ArticleId": 108707266, "Title": "Analysis of Common Supervised Learning Algorithms Through Application", "Abstract": "Supervised learning is a branch of machine learning wherein the machine is equipped with labelled data which it uses to create sophisticated models that can predict the labels of related unlabelled data. the literature on the field offers a wide spectrum of algorithms and applications. However, there is limited research available to compare the algorithms making it difficult for beginners to choose the most efficient algorithm and tune it for their application. This research aims to analyse the performance of common supervised learning algorithms when applied to sample datasets along with the effect of hyper-parameter tuning. for the research, each algorithm is applied to the datasets and the validation curves (for the hyper-parameters) and learning curves are analysed to understand the sensitivity and performance of the algorithms. The research can guide new researchers aiming to apply supervised learning algorithm to better understand, compare and select the appropriate algorithm for their application. Additionally, they can also tune the hyper-parameters for improved efficiency and create ensemble of algorithms for enhancing accuracy.", "Keywords": "", "DOI": "10.5121/acii.2023.10303", "PubYear": 2023, "Volume": "10", "Issue": "1/2/3", "JournalId": 21459, "JournalTitle": "Advanced Computational Intelligence: An International Journal (ACII)", "ISSN": "", "EISSN": "2454-3934", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108707271, "Title": "Confidential Computing in Edge- Cloud Hierarchy", "Abstract": "", "Keywords": "", "DOI": "10.5121/ijcsit.2023.15302", "PubYear": 2023, "Volume": "15", "Issue": "3", "JournalId": 20755, "JournalTitle": "International Journal of Computer Science and Information Technology", "ISSN": "0975-4660", "EISSN": "0975-3826", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Vahagn <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108707303, "Title": "Neural Model-Applying Network (Neuman): A New Basis for Computational Cognition", "Abstract": "NeuMAN represents a new model for computational cognition synthesizing important results across AI, psychology, and neuroscience. NeuMAN is based on three important ideas: (1) neural mechanisms perform all requirements for intelligence without symbolic reasoning on finite sets, thus avoiding exponential matching algorithms; (2) the network reinforces hierarchical abstraction and composition for sensing and acting; and (3) the network uses learned sequences within contextual frames to make predictions, minimize reactions to expected events, and increase responsiveness to high-value information. These systems exhibit both automatic and deliberate processes. NeuMAN accords with a wide variety of findings in neural and cognitive science and will supersede symbolic reasoning as a foundation for AI and as a model of human intelligence. It will likely become the principal mechanism for engineering intelligent systems.", "Keywords": "", "DOI": "10.5121/acii.2023.10301", "PubYear": 2023, "Volume": "10", "Issue": "1/2/3", "JournalId": 21459, "JournalTitle": "Advanced Computational Intelligence: An International Journal (ACII)", "ISSN": "", "EISSN": "2454-3934", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108707348, "Title": "ANALISIS PENINGKAT KINERJA MURNI WEDDING ORGANIZER MENGGUNAKAN DATA SCIENCE DAN SEO", "Abstract": "<p>Weding Organizer merupakan salah satu jasa yang menyediakan pelayanan dan informasi mengenai pernikahan  untuk membantu calon pengantin yang akan menikah dari mulai tahap perencanaan yang akan di digunakan sampai tahap pelaksanaan acara pernikahanMurni Weding Organizer merupakan suatu pelayanan jasa Weding Organizer yang ada di Medan dan pada usaha jasa pelayanan pernikahan ini mereka kurang dalam hal promosi karna hanya dari perorangan saja dan media sosial hal ini mengakibatkan penurunan pemesanan pada usaha Murni Weding Organizer Untuk meningkat kan kualitas dan juga agar usaha Murni Weding Organizer diketahui calon pengantin perlu dilakukan suatu analisis data yang baik dan juga benar sehingga dapat menghasilkan suatu informasi yang berguna untuk usaha mereka dan juga calon pengantin. Analisis data yang akan di gunakan menggunakan Data Science. Data Science adalah interdisiplin yang mengeksplorasi metode ilmiah dan bagaimana mengekstrak pengetahuan dan wawasan dari klan data dari berbagai sumber tidak hanya yang struktur tetapi juga unstrukstur 2. Dengan adanya website diGoogle Bisnis Murni Wedding Organizer  yang dibuat oleh penulis, penulis dapat membantu pemilik usaha jasa Murni Wedding Organizer untuk lebih baik lagi dalam menyebar luaskan informasi jasa pernikahan pada usaha Murni Wedding Organizer Kata Kunci : Wedding Organizer, Data Science, Seo</p>", "Keywords": "", "DOI": "10.46576/syntax.v4i1.2882", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 90282, "JournalTitle": "Syntax : Journal of Software Engineering, Computer Science and Information Technology", "ISSN": "2776-7027", "EISSN": "2723-0538", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Sistem Kendali Jarak Jauh Untuk Pemutusan dan Penyambungan kWh Meter Dengan Ponsel", "Abstract": "<p>Pemutusan sementara saluran listrik ke pelanggan PT. PLN dapat dilakukan untuk mengurangi tunggakan tagihan pembayaran konsumsi energi listrik atau untuk mengatasi keadaan darurat. <PERSON><PERSON> karena itu dalam penelitian ini membahas tentang pembuatan prototipe perangkat pemutus-sambung saluran listrik melalui jaringan GSM yang dapat membantu kerja PT. PLN. Adapun metode pemutusan dan penyambungan saluran listrik ke pelanggan dilakukan dengan menggunakan ponsel yang mengirimkan pesan SMS (Short Message Service) ke peralatan kontrol di Alat Pembatas dan Pengukur (APP). Pesan itu untuk mengubah status relay menjadi on (listrik tersambung) atau off (listrik terputus). Perangkat utama yang digunakan yaitu Arduino Uno, Modul GSM SIM808, dan <PERSON><PERSON>. <PERSON><PERSON> itu, sistem ini juga dilengkapi dengan fitur pengecekan saldo pulsa, titik lokasi dan status/nilai tegangan untuk kebutuhan monitoring kerja sistem. Hasilnya akan ditampilkan pada ponsel petugas melalui SMS dan website Thingspeak. Pengujian sistem secara keseluruhan yang dilakukan terhadap prototipe berjalan dengan baik dan sempurna. Kontak relay tertutup dan terbuka sesuai pesan SMS dan terkonfirmasi pada tampilan ponsel petugas.</p>", "Keywords": "Arduino-Uno;GSM-SIM808;Alat Pembatas dan <PERSON> (APP);Remote Control;kWh Meter", "DOI": "10.34148/teknika.v12i2.607", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 66608, "JournalTitle": "Teknika", "ISSN": "2549-8037", "EISSN": "2549-8045", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Elektro, Universitas Kristen Petra, Surabaya, <PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Teknik Elektro, Universitas Kristen Petra, Surabaya, <PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Teknik Elektro, Universitas Kristen Petra, Surabaya, <PERSON><PERSON>"}], "References": []}, {"ArticleId": 108707503, "Title": "The Effect of Natural Fiber Percentage on the Tensile Strength of Paper Using ANOVA", "Abstract": "<p>Paper is generally made of cellulose fibers derived from wood raw materials. Increased demand for production will have an impact on forest exploitation which can lead to environmental stability. Alternative natural fibers containing cellulose fiber are biomass waste such as Galangal Stems (Alpinia Galanga), Pineapple Leaves (Ananas Cosmosus), Banana Stems (Musa Paradisiaca), and others. The use of natural fibers can reduce the exploitation of wood as a raw material for paper. The purpose of this study was to determine the effect of natural fibers consisting of galangal stems, pineapple leaves, banana contains, and waste paper on the tensile strength of paper using ANOVA. The ratio of the percentage of fiber passed is galangal stems 50:10 and 50:40, pineapple leaves 50:10 and 50:40, banana contains 50:10 and 50:40, and waste paper 100% or without comparison. Tensile strength was carried out according to ASTM-D638, then data processing was carried out using the One Way ANOVA method. The results showed that the highest tensile strength value of banana stem paper and waste paper with a ratio of 50:10 was obtained at 7.04262 MPa resulting in the best tensile strength compared to other fibers. Factors that affect the tensile strength are the length of the fiber, and the bonds between the fibers are related to the fiber content. The results of this study concluded that the greater the number of material components in the manufacture of recycled paper, the greater the tensile strength of the report produced.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v3i2.448", "PubYear": 2023, "Volume": "3", "Issue": "2", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Sri Mutia", "Affiliation": ""}], "References": []}, {"ArticleId": 108707523, "Title": "Ce4+-based self-validated portable platform for highly selective and anti-interference visual sensing of phosphate", "Abstract": "Construction of a convenient and multi-responsive sensor for visual and real-time detecting of phosphate (Pi) is important to monitor the eutrophication pollution or drinking water quality. Herein, we developed a highly selective and anti-interference fluorescent and colorimetric dual-mode sensor for Pi detection. Since only the Ce <sup>4+</sup> ion was used as the center with copper nanoclusters (CuNCs) and 3,3′,5,5′-Tetramethylbenzidine (TMB) as the signal carriers, the construction of the sensor was significantly simplified. The dissociated Ce <sup>4+</sup> showed ability of triggering the CuNCs aggregation-induced emission (AIE) and oxidase-like enzyme, independently bringing red fluorescence (AIE of CuNCs) and blue color (oxidized TMB) signals. With the presence of Pi, the strong binding between Pi and Ce<sup>4+</sup> through P-O-Ce bond quenched both the fluorescent and colorimetric signals. More significantly, the selective and anti-interference ability were effectively improved by self-validation between fluorescent and colorimetric modes. Meanwhile, combining with smartphone, the Pi lowered at 1 μM could be visually detected. Based on the fluorescent and colorimetric outputs could be directly quantified by the naked eye, a paper-based dual-mode visualization platform was constructed. The reliable dual-mode also showed robust analysis of the Pi in environmental water, indicating its great potential as a simple and efficient tool for field monitoring.", "Keywords": "", "DOI": "10.1016/j.snb.2023.134245", "PubYear": 2023, "Volume": "393", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Analytical & Testing Center, Sichuan University, Chengdu, Sichuan 610064, PR China"}, {"AuthorId": 2, "Name": "Chaoting Shi", "Affiliation": "Analytical & Testing Center, Sichuan University, Chengdu, Sichuan 610064, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Analytical & Testing Center, Sichuan University, Chengdu, Sichuan 610064, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Analytical & Testing Center, Sichuan University, Chengdu, Sichuan 610064, PR China;Key Laboratory of Green Chemistry & Technology, Ministry of Education, College of Chemistry, Sichuan University, Chengdu, Sichuan 610064, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Analytical & Testing Center, Sichuan University, Chengdu, Sichuan 610064, PR China;Corresponding author"}], "References": [{"Title": "High-performance dual-channel ratiometric colorimetric sensing of phosphate ion based on target-induced differential oxidase-like activity changes of Ce-Zr bimetal-organic frameworks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "321", "Issue": "", "Page": "128546", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Tb3+ tuning AIE self-assembly of copper nanoclusters for sensitively sensing trace fluoride ions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "342", "Issue": "", "Page": "130071", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Coupling diazotization with oxidase-mimetic catalysis to realize dual-mode double-ratiometric colorimetric and electrochemical sensing of nitrite", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "355", "Issue": "", "Page": "131308", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "The sensitive fluorescence assay of phosphates and alkaline phosphatase based on terbium nanocomplexes synthesized via ligand proportion regulation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "359", "Issue": "", "Page": "131574", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Preparation of dual-ligands Eu-MOF nanorods with dual fluorescence emissions for highly sensitive and selective ratiometric/visual fluorescence sensing phosphate", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "367", "Issue": "", "Page": "132008", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A strategy of “fluorescence-visualization dual channel” for highly sensitive and rapid detection of phosphate biomarkers based on stimulus-responsive infinite coordination polymers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "370", "Issue": "", "Page": "132366", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A strategy of “fluorescence-visualization dual channel” for highly sensitive and rapid detection of phosphate biomarkers based on stimulus-responsive infinite coordination polymers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "370", "Issue": "", "Page": "132366", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 108707615, "Title": "Automation in steep terrain agriculture: an optimal controller to prevent tipping and slipping of tethered robots on slopes", "Abstract": "Autonomous robots have the potential to fundamentally transform conventional farming methods, e.g. by enabling economically viable farming of sloped arable land. However, navigation on slopes in harsh conditions is challenging for robots as they must be prevented from both slipping and tipping. Tethers provide one means of enabling robots to traverse steep and complex terrains with additional advantages of recoverability and connectivity for data and power transfer. Current controllers (for robot and tether) consider only the slipping condition for such robots, meaning that the tether must be mounted low on the robot. This limits the use case and can lead to entanglements and entrapment of the tether. By introducing stability criteria for tethered robots on slopes and developing a novel controller that seeks to avoid both slipping and tipping, we demonstrate how the tether mount height can be raised from 0.27 to 0.81 m on a robot, to enable, for example, the tether to be used above high crops or obstacles. This controller is demonstrated in lab and field conditions on slopes up to 0.4 radians, which translates to approximately 51% of slope. Thereby, the new controller significantly extends the practical usability of tethered robots for agricultural applications on steep slopes.", "Keywords": "Field robotics ; steep slope agricultural robots ; tethered robot modeling and control", "DOI": "10.1080/01691864.2023.2229901", "PubYear": 2023, "Volume": "37", "Issue": "15", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CREATE Lab, EPFL, Lausanne, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CREATE Lab, EPFL, Lausanne, Switzerland"}], "References": [{"Title": "Robot Operating System 2: Design, architecture, and uses in the wild", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "66", "Page": "eabm6074", "JournalTitle": "Science Robotics"}]}, {"ArticleId": *********, "Title": "Die Rechtsbilddatenbank der Abteilung Rechtsvisualisierung an der Universität Zürich", "Abstract": "", "Keywords": "", "DOI": "10.38023/ea0604f6-3442-40e6-814f-2efd7b634273", "PubYear": 2023, "Volume": "", "Issue": "29-Juni-2023", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>tte R<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Freiheit und Zurechenbarkeit", "Abstract": "", "Keywords": "", "DOI": "10.38023/b58af4ab-068a-4290-80e9-afadb01fd675", "PubYear": 2023, "Volume": "", "Issue": "29-Juni-2023", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Ant Colony Optimization with Levy-Based Unequal Clustering and Routing (ACO-UCR) Technique for Wireless Sensor Networks", "Abstract": "<p>Wireless Sensor Networks (WSN) became a novel technology for ubiquitous livelihood and still remains a hot research topic because of its applicability in diverse domains. Energy efficiency treated as a crucial factor lies in the designing of WSN. Clustering is commonly applied to increase the energy efficiency and reduce the energy utilization. The proper choice of cluster heads (CHs) and cluster sizes is important in a cluster-based WSN. The CHs which are placed closer to base station (BS) are affected by the hot spot issue and it exhausts its energy faster than the usual way. For addressing this issue, a new unequal clustering and routing technique using ant colony optimization (ACO) algorithm is presented. Initially, CHs are chosen and clusters are constructed based on several variables. Next, the ACO algorithm with levy distribution is applied for the selection of optimal paths between two nodes in the network. A comprehensive validation set takes place under diverse situations under the position of BS. The experimental outcome verified the superiority of the presented model under several validation parameters.</p>", "Keywords": "", "DOI": "10.1142/S0218126624500439", "PubYear": 2024, "Volume": "33", "Issue": "3", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, R.M.K. Engineering College, Anna University, Chennai, Tamil Nadu 601206, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, R.M.K. Engineering College, Kavaraipettai, Thiruvallur, Tamil Nadu 601 206, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON> Engineering, College, Chennai, Tamil Nadu 600 127, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, <PERSON><PERSON><PERSON><PERSON>, College of Engineering and Technology, Kancheepuram, Tamil Nadu 631 502, India"}], "References": [{"Title": "An optimal support vector machine based classification model for sentimental analysis of online product reviews", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "234", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 108707800, "Title": "A Chinese nested named entity recognition approach using sequence labeling", "Abstract": "Purpose \nThis study aims to introduce an innovative approach that uses a decoder with multiple layers to accurately identify Chinese nested entities across various nesting depths. To address potential human intervention, an advanced optimization algorithm is used to fine-tune the decoder based on the depth of nested entities present in the data set. With this approach, this study achieves remarkable performance in recognizing Chinese nested entities.\n \n \n Design/methodology/approach \nThis study provides a framework for Chinese nested named entity recognition (NER) based on sequence labeling methods. Similar to existing approaches, the framework uses an advanced pre-training model as the backbone to extract semantic features from the text. Then a decoder comprising multiple conditional random field (CRF) algorithms is used to learn the associations between granularity labels. To minimize the need for manual intervention, the Jaya algorithm is used to optimize the number of CRF layers. Experimental results validate the effectiveness of the proposed approach, demonstrating its superior performance on both Chinese nested NER and flat NER tasks.\n \n \n Findings \nThe experimental findings illustrate that the proposed methodology can achieve a remarkable 4.32% advancement in nested NER performance on the People’s Daily corpus compared to existing models.\n \n \n Originality/value \nThis study explores a Chinese NER methodology based on the sequence labeling ideology for recognizing sophisticated Chinese nested entities with remarkable accuracy.", "Keywords": "Chinese nested named entity recognition;Chinese named entity recognition;Sequence labeling", "DOI": "10.1108/IJWIS-04-2023-0070", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 23225, "JournalTitle": "International Journal of Web Information Systems", "ISSN": "1744-0084", "EISSN": "1744-0092", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering , University of Science and Technology Beijing , Beijing, China ; Shunde Innovation School, University of Science and Technology Beijing , Foshan, China and Beijing Key Laboratory of Knowledge Engineering for Materials Science, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering , University of Science and Technology Beijing , Beijing, China ; Shunde Innovation School, University of Science and Technology Beijing , Foshan, China and Beijing Key Laboratory of Knowledge Engineering for Materials Science, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ouyeel Co., Ltd , Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ouyeel Co., Ltd , Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering , University of Science and Technology Beijing , Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering , University of Science and Technology Beijing , Beijing, China"}], "References": [{"Title": "Lexicon enhanced Chinese named entity recognition with pointer network", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "17", "Page": "14535", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A multi-head adjacent attention-based pyramid layered model for nested named entity recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "2561", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Keyword-based faceted search interface for knowledge graph construction and exploration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "5/6", "Page": "453", "JournalTitle": "International Journal of Web Information Systems"}, {"Title": "Chinese Named Entity Recognition Based on BERT and Lightweight Feature Extraction Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "11", "Page": "515", "JournalTitle": "Information"}, {"Title": "News-based intelligent prediction of financial markets using text mining and machine learning: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "217", "Issue": "", "Page": "119509", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 108708074, "Title": "Competitiveness based logistics performance index: An empirical analysis in Organisation for Economic Co-operation and Development countries", "Abstract": "<p>Logistics, beyond being a strategically important function for global supply chains, is a sector of considerable size in terms of the global economy. Thus, economy-wide logistics capabilities have a strategic impact at the national level, supporting countries to achieve a global competitive advantage. For this reason, the logistics performances of the countries not only show the success of using their existing logistics capabilities but also provide significant indications about their competitiveness at the global level. Due to this strategic impact, this study aims to deal with the logistics performances of OECD countries from the perspective of competitiveness and to determine the competitiveness-based logistics performance index (CB-LPI). For this purpose, data envelopment analysis has been applied with two different techniques. The input variables are the Global Competitiveness Index, and the output variables are the Logistic Performance Index. In this empirical study, 7 inputs (infrastructure, skills, product market, financial system, information and communication technology adoption, business dynamism, innovation capability) and 6 output variables (Customs, Infrastructure, International shipments, Logistics quality and competence, Tracking and tracing, Timeliness) have been used. This study is handled in a total of 5 periods. These periods are 2010, 2012, 2014, 2016 and 2018. As a result, it was determined that the competitive logistics performances of 8 countries (Australia, Germany, Italy, Mexico, Poland, Portugal, Spain, and Turkey) were at the level of full efficiency in each period in the application of both techniques. In addition, the CB-LPI covering all OECD countries has been established. Based on the scores obtained, country-based suggestions for countries have been developed.</p>", "Keywords": "", "DOI": "10.1177/17835917231185890", "PubYear": 2023, "Volume": "24", "Issue": "2-3", "JournalId": 30751, "JournalTitle": "Competition and Regulation in Network Industries", "ISSN": "1783-5917", "EISSN": "2399-2956", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "FEAS, Business Mgmt. Dept., OSTIM Technical University, Ankara, Turkiye"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Logistics Program, Hopa Vocational School, Artvin Coruh University, Artvin, Turkiye"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FoAS, Logistics Mgmt. Dept., Istanbul Bilgi University, Beyoğlu, Turkiye"}], "References": []}, {"ArticleId": 108708215, "Title": "Editorial to the special issue on quantum intelligent systems and deep learning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-023-08879-2", "PubYear": 2023, "Volume": "27", "Issue": "18", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tijuana Institute of Technology, Tijuana, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto Politécnico Nacional-CITEDI, Tijuana, Mexico"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tijuana Institute of Technology, Tijuana, Mexico"}], "References": []}, {"ArticleId": 108708235, "Title": "IoT for smart English education: AI-based personalised learning resource recommendation algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2023.10057367", "PubYear": 2023, "Volume": "71", "Issue": "3", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108708350, "Title": "A Source Code Cross-site Scripting Vulnerability Detection Method", "Abstract": "To deal with the potential XSS vulnerabilities in the source code of the power communication network, an XSS vulnerability detection method combining the static analysis method with the dynamic testing method is proposed. The static analysis method aims to analyze the structure and content of the source code. We construct a set of feature expressions to match malignant content and set a \"variable conversion\" method to analyze the data flow of the code that implements interactive functions. The static analysis method explores the vulnerabilities existing in the source code structure and code content. Dynamic testing aims to simulate network attacks to reflect whether there are vulnerabilities in web pages. We construct many attack vectors and implemented the test in the Selenium tool. Due to the combination of the two analysis methods, XSS vulnerability discovery research could be conducted from two aspects: “white-box testing” and “black-box testing”. Tests show that this method can effectively detect XSS vulnerabilities in the source code of the power communication network. Copyright © 2023 KSII.", "Keywords": "Dynamic testing; Static analysis; Vulnerability detection; Webpage attack simulation; XSS(cross-site scripting) vulnerability", "DOI": "10.3837/tiis.2023.06.009", "PubYear": 2023, "Volume": "17", "Issue": "6", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 108708381, "Title": "Effectiveness of Using a Peer Learning Strategy to Reduce the Severity of Repetitive Stereotypical Behavior in Children with Autism Spectrum Disorder", "Abstract": "The study investigates the effectiveness of using a peer learning strategy to reduce the intensity of repetitive stereotypical behavior in children with autism spectrum disorder, and to identify the extent to which the effectiveness of the program continues after the end of the application through a follow-up assessment. The study was applied in Jordan, and the semi-experimental approach was used on the study sample that was selected by the intentional method consisting of (10) children with autism spectrum disorder, their ages ranging from (5-7) years. The researchers developed the scale of stereotypical behavior based on theoretical literature and previous studies, in addition to developing a program based on the use of peer learning strategy to reduce repetitive stereotypical behavior in children with autism spectrum disorder. The results showed that there were significant differences between the average scores of children (experimental sample) on the dimensions of repetitive stereotyped behavior (sensory, motor, verbal, emotional, stereotyped, total score) in the pre and post-measurements in favor of post-measurement Moreover, there were no significant differences between the averages of the children's (experimental sample) on the dimensions of stereotyped repetitive behavior (sensory, motor, verbal, emotional, stereotyped, total grade) in the post-measurement. The study recommended the importance of using a peer learning strategy to develop desirable behaviors in children with autism spectrum disorder. © 2023, Natural Sciences Publishing. All rights reserved.", "Keywords": "Autism spectrum disorder; Peer learning strategy; stereotypical behavior", "DOI": "10.18576/isl/120706", "PubYear": 2023, "Volume": "12", "Issue": "7", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 108708384, "Title": "Proof of location based delivery system using multi-party virtual state channel: a blockchain model", "Abstract": "<p>Supply chain management speeds up the delivery system and product flow towards customers. It is required to update the delivery system and fulfill the customers’ needs. Still, Centralized delivery management approaches are very risky and unsatisfactory, so nowadays, decentralized models are more secure and customer friendly. However, delivering a particular customer in decentralized models is a rigorous task with anonymity or pseudonym. Therefore we propose a location-based decentralized delivery system that provides accurate delivery of products to customers. It is crucial for sellers, Buyers, and Transport service providers that the claimed location is trustworthy and publicly verifiable. The proof of location guarantees the correct geographic location, and the witnesses provide location confirmation to the prover. Further, We introduce an off-chain multiparty payment channel with a supply chain for quicker and more secure payment, and it is free from “flood and loot attacks” and intermediary insecurities. The proposed scheme is protected against malicious activities all involved entities attempt, and smart contracts solve the monopoly. The implementation result uses the ethereum network and shows the gas consumption of different smart contracts with execution costs. Finally, we proved that location verification, disputes solved and successful payment completion are much better than all existing models. </p>", "Keywords": "Blockchain; Proof-of-location; Multi-party virtual channel; Flood and loot attack; Smart contracts; Supply chain", "DOI": "10.1007/s11227-023-05510-x", "PubYear": 2024, "Volume": "80", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Indian Institute of Information Technology Allahabad, Prayagraj, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, Indian Institute of Information Technology Allahabad, Prayagraj, India"}], "References": [{"Title": "Enhanced Lightning Network (off-chain)-based micropayment in IoT ecosystems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; San<PERSON><PERSON> Ghatpande", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "283", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Secure and Privacy-Aware Blockchain Design: Requirements, Challenges and Solutions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "164", "JournalTitle": "Cybersecurity"}, {"Title": "A formally verified blockchain-based decentralised authentication scheme for the internet of things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "12", "Page": "14461", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A secure payment channel rebalancing model for layer-2 blockchain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "", "Page": "100822", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 108708449, "Title": "A hot-module-aware mapping approach in network-on-chip", "Abstract": "<p>In this research, a hot module location-aware heuristic mapping algorithm for placement of hot modules is proposed, which aims to prevent the negative effects of tasks on the performance metrics of 2D network-on-chip. This method uses modular systematic and exact search optimization techniques to obtain an optimal solution to the mapping problem. Significant performance degradation and unfair allocation of system resources can occur with one or more hot modules on the network on the wormhole-base network system. By defining a specific access system for hot modules, the proposed approach optimizes system latency and throughput and minimizes energy consumption under bandwidth limitations. The efficiency and effectiveness of the proposed approach have been compared in simulation environments and similar traffic conditions. Saving energy consumption of the proposed approach compared to the equivalent algorithms for embedded systems, up to 81.69% less delay and 68% energy saving have been obtained.</p>", "Keywords": "Network on chip; Hot modules; Heuristic mapping; Wormhole; Resource management; Delay; Energy consumption; Embedded systems", "DOI": "10.1007/s11227-023-05424-8", "PubYear": 2024, "Volume": "80", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Technology, Islamic Azad University, Tehran, Iran; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Islamic Azad University, Karaj, Iran"}], "References": [{"Title": "A multi-criteria decision based on adaptive routing algorithms with discrete operators for on-chip networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "8", "Page": "10906", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": *********, "Title": "Association of anthropometric characteristics of law enforcement officers with perceived ratings of fit, comfort, and pain in the use of body armor", "Abstract": "<p>Knowledge gaps exist on association between law enforcement officer (LEO) anthropometric characteristics and perceived body armor fit, armor discomfort, and armor-caused pain. This study assessed the correlation and identified influential torso dimensions for armor sizing and design applications. Nine-hundreds and seventy-four LEOs across the U.S. participated in a national study on LEO armor use and body dimensions. Perceived ratings of armor fit, armor discomfort, and body pain were found moderately correlated with each other. In addition, armor fit ratings were associated with certain torso anthropometric characteristics, such as chest circumference, chest breadth, chest depth, waist circumference, waist breadth (sitting), waist front length (sitting), body weight, and body mass index. LEOs who reported armor poor fit, armor discomfort, and armor-caused pain had a larger mean of body dimensions than the \"armor good fit\" group. More women than men had poor fit, discomfort, and body pain in the use of body armor.<b>Practitioner summary:</b> The identified influential body measurements can be used as the \"drivers\" for multivariate analyses to develop an improved armor sizing system to further LEO protection. The study also suggests consideration of gender specific armor sizing systems to accommodate differences in torso configurations between male and female officers and to resolve the concern that more female officers had poor armor fit than male officers.</p>", "Keywords": "armor fit;body armor;body size;police;torso;vest", "DOI": "10.1080/00140139.2023.2232581", "PubYear": 2024, "Volume": "67", "Issue": "4", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "Hongwei Hsiao", "Affiliation": "Texas A&M University, Corpus Christi, TX, USA;National Institute for Occupational Safety and Health (NIOSH), Morgantown, WV, USA"}], "References": [{"Title": "Identifying problems that female soldiers experience with current-issue body armour", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "103384", "JournalTitle": "Applied Ergonomics"}, {"Title": "Encumbered and Traditional Anthropometry of Law Enforcement Officers for Vehicle Workspace and Protective Equipment Design", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "1", "Page": "17", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Assessment of challenges in patrol vehicles and with equipment among law enforcement officers", "Authors": "Hongwei Hsiao", "PubYear": 2023, "Volume": "108", "Issue": "", "Page": "103946", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 108708484, "Title": "Die Ausweitung des Systems „E-Recht“ auf alle Parlamentarischen Materialien in der Bundesgesetzgebung", "Abstract": "", "Keywords": "", "DOI": "10.38023/5900302b-cce5-4f44-a060-d638fbe37f3e", "PubYear": 2023, "Volume": "", "Issue": "29-Juni-2023", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108708550, "Title": "A priori error estimates of <PERSON><PERSON><PERSON><PERSON><PERSON> finite element method for parabolic optimal control problems", "Abstract": "In this paper, the fully discrete finite element approximation for parabolic optimal control problems with pointwise control constraints is studied. We use standard piecewise linear finite elements for the space discretization of the state, and <PERSON><PERSON><PERSON><PERSON><PERSON> scheme for time discretization . For control discretization we consider piecewise linear finite elements approximation and variational discretization. We derive a priori error estimates for state, adjoint state and control. Finally, some numerical examples are provided to confirm our theoretical results. Introduction The optimal control of partial differential equations has a wide range of applications in engineering. More specifically, heat treatment in cancer treatment, optimal temperature control of concrete dams, optimization of oil production processes, temperature control, material processing and forming, chemical reaction, shape design, fluid control, and applications in aerospace, all involve solving optimal control problems described by partial differential equations. Therefore, it is particularly important to investigate efficient numerical methods for solving such problems. At present, many numerical methods can be used to solve these problems, such as finite element method, finite difference method, spectral method, etc. (see [2], [3], [4], [5], [6]). To the best of our knowledge, the first contribution to the convergence analysis of elliptic optimal control problems was given in [7]. The authors studied the finite element discretizations of elliptic optimal control problems with pointwise inequality constraints on the control variables, and obtained the error estimates for the approximation of the optimal control and optimal state. In [8], a variational discretization method was proposed, which does not use explicit discretization of control variables but discretizes control variables implicitly by using the first-order optimality conditions and the discretization of the state and adjoint equations. Some superconvergence results of mixed finite element approximation for elliptic optimal control problems were obtained in [9], [10], [13]. The superconvergence of the finite element approximation for linear and semi-linear elliptic optimal control problems can be found in [18] and [12], respectively. A priori error estimates of the RT0 mixed finite element approximation for elliptic optimal control problems were established in [14]. The superconvergence of the linear and semi-linear elliptic optimal control problems with integral constraints has also been obtained [11], [14]. Parabolic optimal control problems can often be described in environmental modelling, low-temperature superconducting laser energy blasting, petroleum reservoir simulations, and many other fields. The a priori error analysis for the finite element discretization of parabolic optimal control problems without control constraints was done in [2]. The a priori error estimate for space-time finite element discretizations of parabolic optimal control problems was derived in [3]. The Ritz-Galerkin approximation for parabolic optimal control problems was studied in [1]. The a posteriori error estimation of spectral method for parabolic optimal control problems was established in [6]. The Crank-Nicolson linear finite volume element method was considered for parabolic optimal control problems in [16]. In [23], the authors considered the Crank-Nicolson scheme for optimal control problems without control constraints. The superconvergence of the fully discrete finite element approximations for linear and semi-linear parabolic optimal control problems can be found in [19] and [20], respectively. The space-time finite element discretizations of time-optimal control problems was studied in [15]. The superconvergence of fully discrete finite element for parabolic optimal control problems with integral constraints was obtained in [17]. Meidner and Vexler [22] considered a Petrov-Galerkin Crank-Nicolson scheme for parabolic optimal control problems where the controls are only time dependent. The temporal discretization is based on a Petrov-Galerkin variant of the Crank-Nicolson scheme, whereas the spatial discretization employs usual conforming finite elements. With a suitable postprocessing step on the dual partition, a second order convergence is obtained for the discrete control. Motivated by the work [22], the paper [26] considered the Crank-Nicolson time stepping and variational discretization for the similar problem as in [22]. The state equation is treated with a Petrov-Galerkin scheme using a piecewise constant ansatz for the state and piecewise linear continuous test functions. This results in variants of the Crank-Nicolson scheme for the state and the adjoint state. A second order convergence is obtained for the discrete control and the postprocessed state on the dual partition. Compared to the variational framework in [22] and [26], our result is based on the standard Crank-Nicolson difference scheme and provides a priori error estimate for such scheme. Thus, we need higher regularity requirements for the state and adjoint state. In this paper, we study the fully discretization based on the Crank-Nicolson time discretization and linear finite element spatial discretization for parabolic optimal control problems. For the discretization of the state equation, we use standard piecewise linear finite elements in space and the Crank-Nicolson scheme in time. The discrete control variable is obtained by piecewise linear finite element or the projection of the discretized adjoint state on the set of admissible controls. For both cases, we derive an a priori error estimate for the state and control and then use numerical experiments to verify our theoretical results. We consider the following optimal control problem: min u ∈ U a d ⁡ J ( y , u ) = 1 2 ∥ y − y d ∥ L 2 ( 0 , T ; L 2 ( Ω ) ) 2 + α 2 ∥ u ∥ L 2 ( 0 , T ; L 2 ( Ω ) ) 2 subject to { y t − Δ y = f + B u , x ∈ Ω , t ∈ ( 0 , T ] , y = 0 , x ∈ Γ , t ∈ ( 0 , T ] , y ( ⋅ , 0 ) = y 0 , x ∈ Ω , where Ω ⊂ R n ( n = 2 , 3 ) is a bounded convex polygon or polyhedron and Γ is the boundary of Ω. α > 0 , T > 0 . We set the control space U = L 2 ( 0 , T ; L 2 ( Ω ) ) . Let B be a continuous linear operator from L 2 ( 0 , T ; L 2 ( Ω ) ) to L 2 ( 0 , T ; L 2 ( Ω ) ) , U a d be a set defined by U a d = { u : u ∈ U , u a ≤ u ( x , t ) ≤ u b , a . e . in Ω × ( 0 , T ) } , where u a < u b are two constants. The rest of the paper is organized as follows. In section 2, we define the fully discrete finite-element approximation of parabolic optimal control problems and provide useful error estimates. In section 3, we present a priori error estimates for the finite element approximation and variational discretization of the optimal control problem. In section 4, we provide numerical examples to verify our theoretical results. Section snippets Optimal control problem For m ≥ 0 and 1 ≤ p ≤ ∞ , we use the standard Sobolev space W m , p ( Ω ) with norm ∥ ⋅ ∥ m , p and semi-norm | ⋅ | m , p given by ∥ v ∥ m , p p = ∑ | α | ≤ m ∥ D α v ∥ L p p and | v | m , p p = ∑ | α | = m ∥ D α v ∥ L p p , ∀ v ∈ W m , p ( Ω ) . We denote by W 0 m , p ( Ω ) the closure of C 0 ∞ ( Ω ) in W m , p ( Ω ) . For p = 2 , we denote H m ( Ω ) = W m , 2 ( Ω ) , L 2 ( Ω ) = W 0 , 2 ( Ω ) and H 0 m ( Ω ) = W 0 m , 2 ( Ω ) . We denote by L r ( 0 , T ; W m , p ( Ω ) ) the Banach space of all L r integrable functions from [ 0 , T ] to W m , p ( Ω ) with the norm ∥ v ∥ L r ( 0 , T ; W m , p ( Ω ) ) = ( ∫ 0 T ∥ v ∥ m , p r d t ) 1 r for 1 ≤ r < ∞ , and standard modification for r = ∞ . We denote the L 2 ( Error estimates for the optimal control problems Throughout this section we will assume the solutions y , p possess the regularity y , p ∈ H 3 ( 0 , T ; L 2 ( Ω ) ∩ H 2 ( 0 , T ; H 2 ( Ω ) ) . For such a regularity we need smooth right-hand sides and additional compatibility relations. First, we define some intermediate variables. For any v ∈ U a d , let ( y ( v ) , p ( v ) ) be the solution to the following equations: ( y t ( v ) , w ) + a ( y ( v ) , w ) = ( f + B v , w ) , ∀ w ∈ H 0 1 ( Ω ) , t ∈ ( 0 , T ] , y ( v ) ( ⋅ , 0 ) = y 0 , x ∈ Ω . − ( p t ( v ) , q ) + a ( p ( v ) , q ) = ( y ( v ) − y d , q ) , ∀ q ∈ H 0 1 ( Ω ) , t ∈ ( 0 , T ] , p ( v ) ( ⋅ , T ) = 0 , x ∈ Ω . For any v ∈ U a d , let ( y h ( v ) , p h ( v ) ) be the Numerical example In this section we will carry out some numerical experiments to support our theoretical results. We solve the following parabolic optimal control problem: { min u ∈ U a d ⁡ 1 2 ∥ y − y d ∥ L 2 ( 0 , T ; L 2 ( Ω ) ) 2 + 1 2 ∥ u ∥ L 2 ( 0 , T ; L 2 ( Ω ) ) 2 y t − Δ y = f + u , ( x , t ) ∈ Ω × ( 0 , T ] , y ( ⋅ , 0 ) = y 0 , x ∈ Ω . Here U a d = { u ∈ L 2 ( 0 , T ; L 2 ( Ω ) ) , u a ≤ u ( t , x ) ≤ u b , a . e . in Ω , t ∈ ( 0 , T ] , u a , u b ∈ R } . Example 4.1 Let Ω be a square domain ( 0 , 1 ) × ( 0 , 1 ) , T = 1 . The data are as follows: u a = − 1 , u b = 1 , y ( x , t ) = exp ⁡ ( t ) sin ⁡ ( π x ) sin ⁡ ( π y ) , u ( x , t ) = max ⁡ { u a , min ⁡ { u b , ( T − t ) 2 sin ⁡ ( π x ) sin ⁡ ( π y ) } } , p ( x , t ) = − ( T − t ) 2 sin ⁡ ( π x ) sin ⁡ ( π y ) , f ( x , t ) Conclusion In this paper, we discuss the Crank-Nicolson finite element method for parabolic optimal control problems with pointwise inequality constraints on the control variable. The state and adjoint state are approximated using piecewise linear functions. For control discretization, we considered piecewise linear finite element discretization or variational discretization. The corresponding priori error estimates were obtained. Numerical experiments verified the theoretical analysis. Furthermore, many Acknowledgements This work was partially supported by grants from the Xinjiang Uygur Autonomous Region Natural Science Foundation (No. 2022D01C409), the National Natural Science Foundation of China (No. 61962056 ), and the Xinjiang Uygur Autonomous Region Natural Science Fund (No. 2019D01C047 ). References (26) R.S. Falk Approximation of a class of optimal control problems with order of convergence estimates J. Math. Anal. Appl. (1973) J.W. Zhou et al. Superconvergence of triangular mixed finite elements for optimal control problems with an integral constraint Appl. Math. Comput. (2010) I. Lasiecka et al. On discrete-time Ritz-Galerkin approximation of control constrained optimal control problems for parabolic equations Control Cybern. (1978) D. Meidner et al. A priori error estimates for space-time finite element discretization of parabolic optimal control problems part I: problems without control constraints SIAM J. Control Optim. (2008) D. Meidner et al. A priori error estimates for space-time finite element discretization of parabolic optimal control problems part II: problems with control constraints SIAM J. Control Optim. (2008) Y.L. Tang et al. Superconvergence of finite element methods for optimal control problems governed by parabolic equations with time-dependent coefficients East Asian J. Appl. Math. (2013) Y.P. Chen Superconvergence of mixed finite element methods for optimal control problems Math. Comput. (2008) Y.P. Chen et al. A posteriori error estimates of spectral method for optimal control problems governed by parabolic equations Sci. China Ser. A, Math. (2008) M. Hinze A variational discretization concept in control constrained optimization: the linear-quadratic case Comput. Optim. Appl. (2005) Y.P. Chen et al. Error estimates and superconvergence of mixed finite element methods for convex optimal control problems J. Sci. Comput. (2010) Z.L. Lu et al. A posteriori error estimates of lowest order Raviart-Thomas mixed finite element methods for bilinear optimal control problems East Asian J. Appl. Math. (2012) Y.P. Chen et al. Superconvergence for optimal control problems governed by semi-linear elliptic equations J. Sci. Comput. (2009) Y.P. Chen et al. Error estimates and superconvergence of mixed finite element for quadratic optimal control Int. J. Numer. Anal. Model. (2006) View more references Cited by (0) Recommended articles (0) View full text © 2023 Published by Elsevier Ltd. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.camwa.2023.06.017", "PubYear": 2023, "Volume": "144", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830046, China;Institute of Mathematics and Physics, Xinjiang University, Urumqi 830046, China;Corresponding author at: College of Mathematics and System Sciences, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830046, China;School of Mathematics and Statistics, Xi'an Jiaotong University, Xi'an 710049, China"}], "References": []}, {"ArticleId": 108708604, "Title": "Compact spiral shaped slot loaded antenna for multiband application", "Abstract": "<p>Antenna operates at single frequency can be wielding for any one application. If the antenna has to be wield for multiple operations the number of antennas will increase, which in turn increases system size. The proposed printed antenna with novel spiral shaped slot having circular dumble shaped defective structure results in multiple bands can overcome the stated limitation. Printed patch antenna is designed and simulated using High Frequency Structure Simulator software using Glass epoxy substrate. The proposed antenna has a dimensions of 3.34 × 2.47 × 0.16 cm with low cost substrate shows five different working frequencies with good return loss having impedance bandwidth of 7.93% with highest gain of 9.92 dB and virtual size reduction of 28.33%. The proposed antenna is resonating between 4.36 and 15.89 GHz which covers L, C, Ku and K band application.</p>", "Keywords": "", "DOI": "10.1007/s00542-023-05493-y", "PubYear": 2024, "Volume": "30", "Issue": "10", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of PG Studies and Research Studies in Applied Electronics, Gulbarga University, Kalaburagi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of PG Studies and Research Studies in Applied Electronics, Gulbarga University, Kalaburagi, India"}], "References": [{"Title": "Novel Monopole Microstrip Antennas for GPS, WiMAX and WLAN Applications", "Authors": "Biplab Bag; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "2050050", "JournalTitle": "Journal of Circuits, Systems and Computers"}, {"Title": "Design of multi band triangular microstrip patch antenna with triangular split ring resonator for S band, C band and X band applications", "Authors": "<PERSON><PERSON>; Dr<PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "", "Page": "103400", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 108708684, "Title": "Sistem Kendali Proporsional Kualitas Air berupa Ph dan Suhu pada Budidaya Ikan Lele Berbasis IoT", "Abstract": "<p>Banyaknya kematian bibit yang masih kecil dalam pemeliharaan disebabkan oleh kurangnya perhatian dari media dan pemeliharaan kondisi air. Kegagalan budidaya ikan dapat disebabkan oleh air dengan keasaman (pH) yang terlalu asam atau basa, dan suhu yang tidak sesuai akan mempengaruhi angka kematian. Tingkat keasaman (pH) air yang baik untuk ikan lele adalah 6,5 – 8,5. Tingkat pH 9 ke atas akan menyebabkan nafsu makan ikan lele menurun dan tingkat pH air di bawah 5 akan menyebabkan penggumpalan lendir di insang ikan lele dan mudah berkembang biaknya jamur atau bakteri patogen. Suhu air yang baik untuk pertumbuhan ikan adalah antara 20-30°C. Perubahan cuaca yang sewaktu-waktu berubah secara ekstrem, hujan yang terus menerus dan kemudian panas yang tinggi akan membuat suhu dan pH air berubah drastis sehingga menyebabkan ikan lele mengalami stress dan kematian. Berdasarkan pengamatan tersebut, diperlukan suatu alat yang mampu menjaga kualitas air berupa suhu dan pH air. Sistem Pengendalian Kualitas Air berupa pH dan suhu air dengan kontrol proporsional dapat mengontrol keasaman pH dan suhu air pada budidaya ikan lele. Sistem ini terbukti berhasil meningkatkan pertumbuhan pada kolam A dengan sistem. pertumbuhan ikan lele dari 4 cm ke ukuran 5 – 6 cm meningkat 7.7% lebih tinggi dari kolam B tanpa sistem. Pertumbuhan ikan lele ke ukuran 7 – 8 cm pada kolam A 3.5% lebih tinggi daripada kolam B. Pertumbuhan ikan lele ke ukuran 9 cm pada kolam A 4% lebih tinggi dan angka kematian pada kolam A dengan sistem 16% lebih rendah dibandingkan dengan kolam B tanpa sistem. Sistem kendali kualitas air berupa pH dan suhu air ini terbukti mampu meningkatkan pertumbuhan dan menekan kematian ikan lele  dengan menjaga kestabilan suhu dan pH air kolam pada nilai setpoint dan aplikasi android yang dibuat berhasil menampilkan nilai dari pembacaan dari sensor pH dan suhu yang telah ditampung di dalam cloud firebase sehingga memonitoring kualitas air berupa suhu dan pH air dapat dilakukan dari jarak jauh.</p>", "Keywords": "IoT;Proporsional;Kualitas Air;Budidaya Ikan <PERSON>", "DOI": "10.26418/jp.v9i1.59607", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}, {"AuthorId": 2, "Name": "Angga <PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}], "References": []}, {"ArticleId": 108708685, "Title": "Data Mining untuk Pengelompokan Saham pada Sektor Energi dengan Metode K-Means", "Abstract": "<p><PERSON><PERSON> adalah kepemilikan hak oleh perorangan (pemegang saham) pada suatu perusahaan berdasarkan pemberian modal sehingga dianggap  memiliki kepemilikan dan pengawasan perusahaan tersebut berdasarkan bagian tertentu. Menurut data dari Indonesia Stock Exchange (IDX) pada tahun 2020, jumlah investor di Pasar Modal Indonesia yang terdiri dari investor saham, reks<PERSON><PERSON> dan obligasi, mengalami kenaikan 56 persen yaitu 3,87 juta Single Investor Identification (SID) sampai pada tanggal 29 Desember 2020. Kenaikan ini menjadi 4 kali lipat lebih tinggi sejak 4 tahun terakhir. Investor saham juga mengalami kenaikan sebanyak 53 persen menjadi 1,68 juta SID. Hal tersebut menunjukkan besarnya minat masyarakat terhadap keikutsertaan pada kepemilikan saham. Namun dalam berinvestasi terdapat risiko. Risiko dalam berinvestasi di pasar modal sebenarnya dapat diminimalisir dengan pemilihan saham yang benar terutama dalam hal fundamendal perusahaan. Penelitian ini menggunakan metode K-Means untuk mengelompokan saham sesuai dengan karakteristiknya. Berdasarkan perhitungan, didapatkan sebanyak 5 kali Iterasi untuk 4 Kelas/Cluster yang telah didefinisikan diawal. Selain itu, didapatkan hasil bahwa Kelas/Cluster 1 dan 4 diisi oleh emiten-emiten yang memiliki fundamental buruk  serta Kelas/Cluster 2 berisi emiten pemberi dividen yang tinggi. Secara keseluruhan, didapatkan kesimpulan bahwa algoritma K-Mean dapat digunakan untuk membantu para Investor dalam melakukan pencarian emiten yang sesuai dengan karakteristik yang diinginkan.</p>", "Keywords": "Saham;Fundamental;Risiko;Data Mining;K-Means", "DOI": "10.26418/jp.v9i1.62509", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Tanjungpura"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Tanjungpura"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Tanjungpura"}], "References": []}, {"ArticleId": 108708695, "Title": "Von der Bedeutung des Netzwerk(en)s in der frühen Rechtsinformatik in Österreich", "Abstract": "", "Keywords": "", "DOI": "10.38023/0ab27e8b-873a-40e7-a64b-24451ef05576", "PubYear": 2023, "Volume": "", "Issue": "29-Juni-2023", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108708697, "Title": "Optimality in high-dimensional tensor discriminant analysis", "Abstract": "Tensor discriminant analysis is an important topic in tensor data analysis. However, given the many proposals for tensor discriminant analysis methods, there lacks a systematic theoretical study, especially concerning optimality. We fill this gap by providing the minimax lower bounds for the estimation and prediction errors under the tensor discriminant analysis model coupled with the sparsity assumption. We further show that one existing high-dimensional tensor discriminant analysis estimator has matching upper bounds, and is thus optimal. Our results apply to tensors with arbitrary orders and ultra-high dimensions. If one focuses on one-way tensors (i.e., vectors), our results further provide strong theoretical justifications for several popular sparse linear discriminant analysis methods. Numerical studies are also presented to support our theoretical results. Introduction In contemporary scientific study, many data are collected in the form of a multi-dimensional array, also known as tensor. For example, time-course genetic data are often recorded in matrices, while magnetic resonance imaging (MRI) produces three-way tensors. The abundance of tensor data has motivated many novel statistical analysis methods in regression, classification, recommendation system, and among others. These methods mainly tackle two challenges in tensor data analysis: efficient exploitation of the tensor structure and reduction of the high dimensionality. See <PERSON><PERSON> et al. [1] for a review of tensor-based statistical methods. These methods have also led to a growing body of innovative statistical theory that deepens our understanding of tensor data. Tensor discriminant analysis (TDA) is an important topic in tensor analysis that is receiving increasing attention. TDA is a generalization of the classical linear discriminant analysis (LDA) to tensor data. TDA can be applied as a powerful classification tool, as it aims to predict the categorical response based on tensor predictors, and it can also be applied as a dimension reduction tool for data exploration. Moreover, TDA can be derived under intuitive probabilistic models to enable convenient interpretation for how we take advantage of the tensor structure and make the final prediction. An incomplete list of TDA methods include Yan et al. [2], Tao et al. [3], Lai et al. [4], Li and Schonfeld [5], Zhong and Suslick [6], Zeng et al. [7], Hu et al. [8], Molstad and Rothman [9], Pan et al. [10], Xu et al. [11]. However, a significant gap exists in the theoretical study of TDA, especially concerning the optimality properties in high dimensions. Many of the aforementioned TDA methods do not have theoretical studies, while others have strong assumptions or do not consider the optimality. For example, [6], [8] focus on two-way tensors (i.e., matrices), and the dimension can not grow too fast with respect to the sample size. These results do not apply to higher-order tensors with high dimensions where the dimension (along each mode) grows much faster than the sample size. Meanwhile, Pan et al. [10] established some consistency results for high-dimensional tensors with arbitrary orders, but, as we will show, the rate therein is sub-optimal. Moreover, little is known about the minimax lower bound for TDA, or how to attain this lower bound. This paper will fill the theoretical gaps by providing a systematic investigation on the theoretical properties of TDA. We will obtain the minimax lower bound for both coefficient estimation and misclassification rate. We will further show that the high-dimensional TDA (HD-TDA) estimator in Pan et al. [10] is minimax optimal. Our paper gives an answer that is otherwise unavailable in the literature. It characterizes the optimal rates we can achieve in estimation and prediction under an elegant TDA model popular in the literature. It also strengthens the theoretical support for the HD-TDA method by improving its convergence rates. The new rates are developed under the sparsity assumption that is compatible with vector data analysis. Indeed, our results have some nice implications for high-dimensional vector data analysis as well, providing theoretical justifications for several popular such methods. The rest of this paper is organized as follows. In Section 2, we introduce some notations and the background of the TDA model and its estimation. In Section 3, we provide the optimal convergence rate for the estimation error and excess misclassification risk. In Section 4, we use numerical simulations to demonstrate the theoretical convergence rates. In Section 5, we illustrate the superior performance of HD-TDA by applying the method on real data. Section 6 contains a discussion. The proofs are given in the supplementary material. Section snippets Notation We first state some basic notations and definitions. For a matrix A ∈ R p × q , the Frobenius norm is defined as ∥ A ∥ F = ∑ i , j | a i j | 2 . If A is symmetric, let λ min ( A ) and λ max ( A ) denote the smallest and largest eigenvalue of A respectively. Furthermore, A ≻ 0 denotes that A is positive definite. For two sequences of positive numbers a n and b n , a n ≲ b n means that, for some constant c > 0 , a n ≤ c b n for all n , and a n ≍ b n if a n ≲ b n and b n ≲ a n . Finally, we use c 1 , c 2 , … and C 1 , C 2 , … to denote generic positive constants that Main results The focus of this section is to rigorously study the estimation and prediction errors for the HD-TDA classifier, as well as the lower bounds for these errors under the TDA model. We will provide sharp upper bounds for the HD-TDA classifier that imply its optimality under the TDA model. We first present such results for binary classification in Section 3.1. Then we extend the theoretical study to problems with any number of classes in Section 3.2. In Section 3.3, we further provide the Numerical study In this section, we conduct simulations to demonstrate the two optimal convergence rates obtained in Section 3.1 and Section 3.2. We set π k = 1 / K , k = 1 , … , K . We consider various dimensions of the predictors. For matrix models, we consider { ( 25 , 40 ) , ( 50 , 80 ) , ( 100 , 80 ) } . For tensor models, we consider { ( 10 , 10 , 10 ) , ( 20 , 20 , 10 ) , ( 20 , 20 , 20 ) } . As a result, the number of elements in the tensor could be 1000,4000, or 8000. Denote n k as the sample size for the k th class. For n k , we consider a sequence from 75 to Real data analysis In this section, we apply HD-TDA to three different datasets: Grimace face recognition, Electroencephalography (EEG) brain image, and Periodontal Disease (PD). The performance of HD-TDA was evaluated by comparing its classification error rate with other established discriminant analysis methods including l 1 -penalized Fisher’s discriminant analysis ( l 1 -FDA; 42), multiclass sparse discriminant analysis (MSDA; [36], [39]), penalized likelihood method for matrix classification (PLMC; 9), Discussion In this paper, we study the minimax theory of the estimation error and misclassification risk for tensor discriminant analysis. While the optimal convergence rates have been established for vector-based linear discriminant analysis, there is no such theory for tensor discriminant analysis. We fill this gap by providing the minimax lower bounds for the estimation and prediction errors. We further show that they can be achieved by the HD-TDA estimator in Pan et al. [10]. Our results also give Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments The authors would like to thank the editor, associate editor and three anonymous referees, whose suggestions led to great improvement of the paper. Mai’s research is supported in part by CCF-1908969, National Science Foundation. Keqian Min received her BS degree in Financial Mathematics from Southern University of Science and Technology in 2017, and her Ph.D. degree in Statistics from Florida State University in 2022. Her research interests include high-dimensional statistics and tensor data analysis. References (48) M.P. Gallaugher et al. Finite mixtures of skewed matrix variate distributions Pattern Recognit (2018) L. Spyrou et al. Multiview classification and dimensionality reduction of scalp and intracranial EEG data through tensor factorisation J Signal Process Syst (2018) B. Li et al. On dimension folding of matrix-or array-valued statistical objects The Annals of Statistics (2010) X. Bi et al. Tensors in statistics Annu Rev Stat Appl (2021) S. Yan et al. Discriminant analysis with tensor representation 2005 IEEE Computer Society Conference on Computer Vision and Pattern Recognition (CVPR’05) (2005) D. Tao et al. General tensor discriminant analysis and gabor features for gait recognition IEEE Trans Pattern Anal Mach Intell (2007) Z. Lai et al. Sparse tensor discriminant analysis IEEE Trans. Image Process. (2013) Q. Li et al. Multilinear discriminant analysis for higher-order tensor data classification IEEE Trans Pattern Anal Mach Intell (2014) W. Zhong et al. Matrix discriminant analysis with application to colorimetric sensor array data Technometrics (2015) R. Zeng et al. Tensor object classification via multilinear discriminant analysis network 2015 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP) (2015) W. Hu et al. Matrix linear discriminant analysis Technometrics (2020) A.J. Molstad et al. A penalized likelihood method for classification with matrix-valued predictors Journal of Computational and Graphical Statistics (2019) Y. Pan et al. Covariate-adjusted tensor classification in high dimensions J Am Stat Assoc (2019) Z. Xu et al. A portmanteau local feature discrimination approach to the classification with high-dimensional matrix-variate data Sankhya A (2023) T.G. Kolda et al. Tensor decompositions and applications SIAM Rev. (2009) C. Viroli Finite mixtures of matrix normal distributions for classifying three-way data Stat Comput (2011) L. Anderlucci et al. Covariance pattern mixture models for the analysis of multivariate heterogeneous longitudinal data Ann Appl Stat (2015) W.W. Sun et al. Dynamic tensor clustering J Am Stat Assoc (2019) P.A. Tait, P.D. McNicholas, J. Obeid, Clustering higher order data: An application to pediatric multi-variable... X. Gao et al. Regularized matrix data clustering and its application to image analysis Biometrics (2021) Q. Mai et al. A doubly enhanced em algorithm for model-based tensor clustering J Am Stat Assoc (2022) B. Cai et al. Jointly modeling and clustering tensors in high dimensions (2021) M.J. Wainwright High-dimensional statistics: A non-asymptotic viewpoint (2019) J. Fan et al. Statistical Foundations of Data Science (2020) View more references Cited by (0) Recommended articles (6) Research article Hyperbaric oxygen therapy for acute spinal cord injury and other orthopaedic conditions Orthopaedics and Trauma, Volume 37, Issue 4, 2023, pp. 222-225 Show abstract Hyperbaric oxygen (HBO) therapy involves intermittent inhalation by the patient of 100% oxygen under a pressure greater than 1 atmosphere. Both therapeutic and toxic effects can result from two features of this form of treatment; mechanical effects arising from exposure to increased ambient pressure and the physiological effects of hyperoxia. Substantial evidence supports the use of HBO therapy in certain carefully defined settings, though many patients who might benefit go untreated because of their physicians' unfamiliarity with recent research and overall uncertainty about the legitimacy of HBO as a therapy. The author discusses the mechanism of action of HBO therapy and the commonly accepted clinical indications. Research article AGMN: Association graph-based graph matching network for coronary artery semantic labeling on invasive coronary angiograms Pattern Recognition, Volume 143, 2023, Article 109789 Show abstract Semantic labeling of coronary arterial segments in invasive coronary angiography (ICA) is important for automated assessment and report generation of coronary artery stenosis in computer-aided coronary artery disease (CAD) diagnosis. However, separating and identifying individual coronary arterial segments is challenging because morphological similarities of different branches on the coronary arterial tree and human-to-human variabilities exist. Inspired by the training procedure of interventional cardiologists for interpreting the structure of coronary arteries, we propose an association graph-based graph matching network (AGMN) for coronary arterial semantic labeling. We first extract the vascular tree from invasive coronary angiography (ICA) and convert it into multiple individual graphs. Then, an association graph is constructed from two individual graphs where each vertex represents the relationship between two arterial segments. Thus, we convert the arterial segment labeling task into a vertex classification task; ultimately, the semantic artery labeling becomes equivalent to identifying the artery-to-artery correspondence on graphs. More specifically, the AGMN extracts the vertex features by the embedding module using the association graph, aggregates the features from adjacent vertices and edges by graph convolution network, and decodes the features to generate the semantic mappings between arteries. By learning the mapping of arterial branches between two individual graphs, the unlabeled arterial segments are classified by the labeled segments to achieve semantic labeling. A dataset containing 263 ICAs was employed to train and validate the proposed model, and a five-fold cross-validation scheme was performed. Our AGMN model achieved an average accuracy of 0.8264, an average precision of 0.8276, an average recall of 0.8264, and an average F1-score of 0.8262, which significantly outperformed existing coronary artery semantic labeling methods. In conclusion, we have developed and validated a new algorithm with high accuracy, interpretability, and robustness for coronary artery semantic labeling on ICAs. Research article MSINet: Mining scale information from digital surface models for semantic segmentation of aerial images Pattern Recognition, Volume 143, 2023, Article 109785 Show abstract Compared with other kinds of images, aerial images have more obvious object scale distinction and larger resolution, which results in that the whole scale information of aerial images can hardly be explored. To address this difficulty, we develop a novel network based on the digital surface models (DSMs) of aerial images in this paper. The proposed network termed MSINet can efficiently mine scale information through the DSMs from two aspects. Firstly, we propose an interpolation pyramid algorithm to encode the scale information from the DSMs and hence provide a scale prior information to the normal segmentation network. The interpolation pyramid algorithm implements interpolation operations with different scales on the DSMs and detects the pixel value change after the interpolation operations. Objects with different scales will express diverse changes, which provides useful information to encode their scale information. Secondly, aiming to address the problem that the DSMs contain a large amount of noise in the boundary part, a spatial information enhancement module and a mutual-guidance module are developed in this paper. These two modules can fix the misleading guidance information caused by the noise in the boundary part of the DSMs and hence achieve more accurate scale information inserting. The extensive experimental results prove that our methods can outperform other competitors in terms of qualitative and quantitative performance. Research article Conditional Independence Induced Unsupervised Domain Adaptation Pattern Recognition, Volume 143, 2023, Article 109787 Show abstract Learning domain-adaptive features is important to tackle the dataset bias problem, where data distributions in the labeled source domain and the unlabeled target domain can be different. The critical issue is to identify and then reduce the redundant information including class-irrelevant and domain-specific features. In this paper, a conditional independence induced unsupervised domain adaptation (CIDA) method is proposed to tackle the challenges. It aims to find the low-dimensional and transferable feature representation of each observation, namely the latent variable in the domain-adaptive subspace. Technically, two mutual information terms are optimized at the same time. One is the mutual information between the latent variable and the class label, and the other is the mutual information between the latent variable and the domain label. Note that the key module can be approximately reformulated as a conditional independence/dependence based optimization problem, and thus, it has a probabilistic interpretation with the Gaussian process. Temporary labels of the target samples and the model parameters are alternatively optimized. The objective function can be incorporated with deep network architectures, and the algorithm is implemented iteratively in an end-to-end manner. Extensive experiments are conducted on several benchmark datasets, and the results show effectiveness of CIDA. Research article Improved polar complex exponential transform for robust local image description Pattern Recognition, Volume 143, 2023, Article 109786 Show abstract Image description via robust local descriptors plays a vital role in a large number of image representation and matching applications. In this paper, we propose a novel distinctive local image descriptor that is based on the phase and amplitude information of Polar Complex Exponential Transform (PCET). The proposed descriptor, called IPCET (Improved PCET), is robust to the common photometric transformations (e.g., illumination, noise, JPEG compression, and blur) and geometric transformations (e.g., scaling, rotation, translation, and significant affine distortion). We perform extensive experiments to compare our IPCET descriptor with six most cutting-edge region descriptors (i.e., SIFT, Zernike Moment, GLOH, PCA-SIFT, SURF, and ORB). Experimental results demonstrate that our IPCET descriptor outperforms cutting-edge moment-based descriptors. Research article RADAM: Texture recognition through randomized aggregated encoding of deep activation maps Pattern Recognition, Volume 143, 2023, Article 109802 Show abstract Texture analysis is a classical yet challenging task in computer vision for which deep neural networks are actively being applied. Most approaches are based on building feature aggregation modules around a pre-trained backbone and then fine-tuning the new architecture on specific texture recognition tasks. Here we propose a new method named R andom encoding of A ggregated D eep A ctivation M aps (RADAM) which extracts rich texture representations without ever changing the backbone. The technique consists of encoding the output at different depths of a pre-trained deep convolutional network using a Randomized Autoencoder (RAE). The RAE is trained locally to each image using a closed-form solution, and its decoder weights are used to compose a 1-dimensional texture representation that is fed into a linear SVM. This means that no fine-tuning or backpropagation is needed for the backbone. We explore RADAM on several texture benchmarks and achieve state-of-the-art results with different computational budgets. Our results suggest that pre-trained backbones may not require additional fine-tuning for texture recognition if their learned representations are better encoded. Keqian Min received her BS degree in Financial Mathematics from Southern University of Science and Technology in 2017, and her Ph.D. degree in Statistics from Florida State University in 2022. Her research interests include high-dimensional statistics and tensor data analysis. Qing Mai received her BS degree from Nankai University in 2008 and her Ph.D. degree from University of Minnesota in 2013, both in statistics. She is now Associate Professor at Department of Statistics, Florida State University. She is interested in statistical learning, high-dimensional data analysis, tensor data analysis, and dimension reduction. Junge Li received her BS degree in Applied Mathematics from Shanghai University of Finance and Economics in 2015 and her MS degree in Mathematical Finance from Rutgers University in 2018. She is now a Ph.D. student at Department of Statistics, Florida State University. Her research interests include high-dimensional statistics and tensor data analysis. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.109803", "PubYear": 2023, "Volume": "143", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBM, 1 N Castle Dr., Armonk, 10504, NY, USA"}, {"AuthorId": 2, "Name": "Qing Mai", "Affiliation": "Florida State University, 117 N. Woodward Ave., Tallahassee, 32306, FL, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Florida State University, 117 N. Woodward Ave., Tallahassee, 32306, FL, USA"}], "References": [{"Title": "TULIP: A Toolbox for Linear Discriminant Analysis with Penalties", "Authors": "Yuqing Pan; Qing <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "1", "JournalTitle": "The R Journal"}]}, {"ArticleId": 108708731, "Title": "Comparison of Advanced Classification Algorithms Based Intrusion Detection from Real-Time Dataset", "Abstract": "<p>Advanced machine learning (ML) and deep learning (DL) methods have provided efficient intrusion detection with minimum probability of false positives. Most existing IDS models have been evaluated over out-dated benchmark cyber security IDS datasets such as KDDCUP 1999 or NSL-KDD since the collection and extraction of attack features from the real-time network traffic data is a complicated process. This paper aims at generating a real-time dataset for IDS evaluation and testing. The real-time data were obtained by analyzing the real traffic analysis in a computer network with an Internet connection to extract the attack and normal traffic features which were referenced from popular datasets such as KDDCUP 1999 and NSL-KDD. In this study, the real-time data is collected with two attack classes (denial-of-service (DoS) and shellcode) and normal class data for evaluation. Advanced classification approaches of hyper-heuristic support vector machines (HH-SVM), hyper-heuristic improved particle swarm optimization based support vector machines (HHIPSO-SVM), and hyper-heuristic firefly algorithm based convolutional neural networks (HHFA-CNN) along with fuzzy optimized independent component analysis (FOICA) dimensionality reduction technique are evaluated using this dataset. Experimental results showed the effectiveness of the advanced classification methods with and without dimensionality reduction technique with the HHFA-CNN with FOICA achieving high accuracy and reduced time complexity for the real-time dataset.</p>", "Keywords": "cyber security; intrusion detection system; fuzzy optimized independent component analysis; hyper-heuristic support vector machines", "DOI": "10.3103/S0146411623030021", "PubYear": 2023, "Volume": "57", "Issue": "3", "JournalId": 14245, "JournalTitle": "Automatic Control and Computer Sciences", "ISSN": "0146-4116", "EISSN": "1558-108X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON><PERSON><PERSON> College of Arts and Science, Department of Computer Science, KG College of Arts and Science, Coimbatore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON><PERSON><PERSON> College of Arts and Science, Department of Information Technology, Coimbatore, India"}], "References": [{"Title": "Real time dataset generation framework for intrusion detection systems in IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "414", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A Review of the Advancement in Intrusion Detection Datasets", "Authors": "<PERSON><PERSON><PERSON>; R<PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "636", "JournalTitle": "Procedia Computer Science"}, {"Title": "Performance Analysis of Machine Learning Algorithms in Intrusion Detection System: A Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "1251", "JournalTitle": "Procedia Computer Science"}, {"Title": "STL-HDL: A new hybrid network intrusion detection system for imbalanced dataset on big data environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102435", "JournalTitle": "Computers & Security"}, {"Title": "Deep Neural Network Based Real-Time Intrusion Detection System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Dimensionality reduction of the attributes using fuzzy optimized independent component analysis for a Big Data Intrusion Detection System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "1", "Page": "93", "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics"}]}, {"ArticleId": 108708753, "Title": "An Interleaving Guided Metamorphic Testing Approach for Concurrent Programs", "Abstract": "<p> Concurrent programs are normally composed of multiple concurrent threads sharing memory space. These threads are often interleaved, which may lead to some non-determinism in execution results, even for the same program input. This poses huge challenges to the testing of concurrent programs, especially on the test result verification—that is, the prevalent existence of the oracle problem. In this article, we investigate the application of metamorphic testing (MT), a mainstream technique to address the oracle problem, into the testing of concurrent programs. Based on the unique features of interleaved executions in concurrent programming, we propose an extended notion of metamorphic relations, the core part of MT, which are particularly designed for the testing of concurrent programs. A comprehensive testing approach, namely ConMT , is thus developed and a tool is built to automate its implementation on concurrent programs written in Java. Empirical studies have been conducted to evaluate the performance of ConMT, and the experimental results show that in addition to addressing the oracle problem, ConMT outperforms the baseline traditional testing techniques with respect to a higher degree of automation, better bug detection capability, and shorter testing time. It is clear that ConMT can significantly improve the cost-effectiveness for the testing of concurrent programs and thus advances the state of the art in the field. The study also brings novelty into MT, hence promoting the fundamental research of software testing. </p>", "Keywords": "", "DOI": "10.1145/3607182", "PubYear": 2024, "Volume": "33", "Issue": "1", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Science and Technology Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Science and Technology Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Science and Technology Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Swinburne University of Technology, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Swinburne University of Technology, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Chinese Academy of Sciences and University of Chinese Academy of Sciences, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Chinese Academy of Sciences and University of Chinese Academy of Sciences, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chinese Academy of Sciences and University of Chinese Academy of Sciences, China"}], "References": []}, {"ArticleId": *********, "Title": "Designing and implementing a multi-function board to increase the operation time of mobile robots using solar panels", "Abstract": "<p>Today, the use of mobile robots and autonomous vehicles has increased due to their use in various industries, and their performance and duration of operation largely depend on the amount of energy consumed and their batteries. One of the ways to increase the operation time of robots is the use of solar panels that can charge their batteries while moving, but the amount of energy received from solar panels reduces their efficiency due to factors affecting them, such as the angle of the sun, weather conditions, and their use in mobile robots alone is not recommended. In this research, we introduce an electric circuit with very low losses to increase the received power of solar panels and increase their efficiency, which is able to supply the power of the robot through solar panels when the sunlight and the angle of radiation are suitable and charge the batteries through the maximum power point controller (MPPC), and by reducing the amount of energy received from the panels by changing the energy source to the battery, the duration of the system’s dependence on the battery has decreased, which increases the duration of the mobile robots.</p>", "Keywords": "low illumination;maximum power point controller;maximum power point tracking;mobile robots;solar panels", "DOI": "10.11591/ijra.v12i3.pp248-261", "PubYear": 2023, "Volume": "12", "Issue": "3", "JournalId": 46243, "JournalTitle": "IAES International Journal of Robotics and Automation (IJRA)", "ISSN": "2089-4856", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Alberta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tehran University"}], "References": []}, {"ArticleId": *********, "Title": "Design of Routing Architecture for Integrated Network of Sky and Earth", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2023.123055", "PubYear": 2023, "Volume": "12", "Issue": "3", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "前齐 张", "Affiliation": ""}], "References": [{"Title": "A fuzzy delay-bandwidth guaranteed routing algorithm for video conferencing services over SDN networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "17", "Page": "25585", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": *********, "Title": "Recognition of Local Anisotropies of Muon Fluxes Using Normalized Variations for Matrix Observations of the URAGAN Hodoscope", "Abstract": "<p>The recognition of local anisotropies of muon fluxes using the functions of normalized variations for matrix observations of the URAGAN hodoscope is considered. Normalized instrument functions are introduced and spatiotemporal filtration is used, which become the basis of computation of the functions of normalized variations. An algorithm of recognition of local anisotropies is implemented. An experimental study of the application of the functions of normalized variations is carried out that confirms the efficiency of the developed algorithm for recognition of local anisotropies of muon fluxes in times series of matrix observations of the URAGAN hodoscope.</p>", "Keywords": "recognition; local anisotropies; functions of normalized variations; muon fluxes; muon hodoscope; spatiotemporal filtration", "DOI": "10.1134/S1054661823020049", "PubYear": 2023, "Volume": "33", "Issue": "2", "JournalId": 20107, "JournalTitle": "Pattern Recognition and Image Analysis", "ISSN": "1054-6618", "EISSN": "1555-6212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Geophysical Center of the Russian Academy of Sciences, Moscow, Russian Federation; Schmidt Institute of Physics of the Earth of the Russian Academy of Sciences, Moscow, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Geophysical Center of the Russian Academy of Sciences, Moscow, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Geophysical Center of the Russian Academy of Sciences, Moscow, Russian Federation; Schmidt Institute of Physics of the Earth of the Russian Academy of Sciences, Moscow, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Geophysical Center of the Russian Academy of Sciences, Moscow, Russian Federation"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Geophysical Center of the Russian Academy of Sciences, Moscow, Russian Federation"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Geophysical Center of the Russian Academy of Sciences, Moscow, Russian Federation; Schmidt Institute of Physics of the Earth of the Russian Academy of Sciences, Moscow, Russian Federation"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research Nuclear University, Moscow Engineering Physics Institute (MEPhI), Moscow, Russian Federation"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research Nuclear University, Moscow Engineering Physics Institute (MEPhI), Moscow, Russian Federation"}, {"AuthorId": 9, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research Nuclear University, Moscow Engineering Physics Institute (MEPhI), Moscow, Russian Federation"}], "References": [{"Title": "A Method for Local Anisotropy Recognition in Muon Fluxes Based on Matrix Observations of the URAGAN Hodoscope Using Calculations of Systems of Confidence Intervals and Spatiotemporal Filtering", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "4", "Page": "758", "JournalTitle": "Pattern Recognition and Image Analysis"}]}, {"ArticleId": 108709205, "Title": "Pemilihan Kata Benda Bahasa Indonesia Berdasarkan Cakupan Suku Kata Menggunakan Genetic Algoritma untuk Dataset Audio Visual", "Abstract": "<p><PERSON><PERSON> pembentukan model <PERSON><PERSON><PERSON><PERSON> Buatan yang menggunakan pendekatan Deep Learning, dataset memegang peranan yang sangat penting. Memahami dan memilih kumpulan data yang tepat, sangat<PERSON> penting untuk memastikan keberhasilan sebuah model <PERSON><PERSON><PERSON>an Buatan. Salah satu topik yang cukup baru adalah mempelajari bagaimana pembentukan suara dari hasil pembacaan gerakan bibir manusia, dengan cakupan variasi bunyi dan bentuk bibir yang diharapkan dapat membantu pembelajaran sistem. Mayoritas dataset audio visual, yang biasa digunakan untuk pembangunan model pembentukan suara ataupun pembacaan gerakan bibir tidak memperhatikan keluasan cakupan variasi bunyi yang ada. AVID, salah satu dari dataset audio visual berbahasa Indonesia, mengadopsi susunan kata dalam dataset GRID, yang mengubah setiap kata penyusunnya dari Bahasa Inggris ke bahasa Indonesia. Sedangkan pada Bahasa Indonesia sendiri terdapat banyak ragam bunyi yang dibentuk dari satu atau sederet rangkaian fonem. Pen<PERSON><PERSON> yang dilakukan penulis dengan memanfaatkan Genetic Algorithm untuk mendapatkan susunan kombinasi kata benda guna memperoleh nilai cakupan yang optimal. Dengan cakupan kombinasi suku kata yang lebih baik, maka dapat dihasilkan dataset untuk Deep Learning yang lebih baik lagi. Dalam penelitian ini, kata benda yang diproses, diperoleh dari KBBI edisi 2008, baru kemudian difilter untuk mendapatkan kata benda yang tepat mengandung 3 suku kata, yang bukan nama kota, tokoh maupun lokasi. Dari 39.070 kata benda yang ada, diperoleh 2936 kata benda yang akan digunakan. Ujicoba yang telah dilakukan pada 10.000 hingga 200.000 epoch, diperoleh rata-rata cakupan suku kata 72%-75% dengan batasan 26 variasi kata benda penyusunnya.</p>", "Keywords": "Genetic Algorithm;<PERSON><PERSON>;<PERSON><PERSON>;Pembentukan Dataset", "DOI": "10.26418/jp.v9i1.63970", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Negeri Malang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Negeri Malang"}, {"AuthorId": 3, "Name": "Wahyu Sakti Gunawan Irianto", "Affiliation": "Universitas Negeri Malang"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Sains dan Teknologi Terpadu Surabaya"}], "References": []}, {"ArticleId": 108709304, "Title": "Design of Precise Intelligent Service Platform Based on Big Data Analysis—Development of “School Ding Dang” Mini Program", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2023.136129", "PubYear": 2023, "Volume": "13", "Issue": "6", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "灏天 陈", "Affiliation": ""}], "References": []}, {"ArticleId": 108709330, "Title": "A fine-tuned YOLOv5 deep learning approach for real-time house number detection", "Abstract": "<p>Detection of small objects in natural scene images is a complicated problem due to the blur and depth found in the images. Detecting house numbers from the natural scene images in real-time is a computer vision problem. On the other hand, convolutional neural network (CNN) based deep learning methods have been widely used in object detection in recent years. In this study, firstly, a classical CNN-based approach is used to detect house numbers with locations from natural images in real-time. Faster R-CNN, MobileNet, YOLOv4, YOLOv5 and YOLOv7, among the commonly used CNN models, models were applied. However, satisfactory results could not be obtained due to the small size and variable depth of the door plate objects. A new approach using the fine-tuning technique is proposed to improve the performance of CNN-based deep learning models. Experimental evaluations were made on real data from Kayseri province. Classic Faster R-CNN, MobileNet, YOLOv4, YOLOv5 and YOLOv7 methods yield f1 scores of 0.763, 0.677, 0.880, 0.943 and 0.842, respectively. The proposed fine-tuned Faster R-CNN, MobileNet, YOLOv4, YOLOv5, and YOLOv7 approaches achieved f1 scores of 0.845, 0.775, 0.932, 0.972 and 0.889, respectively. Thanks to the proposed fine-tuned approach, the f1 score of all models has increased. Regarding the run time of the methods, classic Faster R-CNN detects 0.603 seconds, while fine-tuned Faster R-CNN detects 0.633 seconds. Classic MobileNet detects 0.046 seconds, while fine-tuned MobileNet detects 0.048 seconds. Classic YOLOv4 and fine-tuned YOLOv4 detect 0.235 and 0.240 seconds, respectively. Classic YOLOv5 and fine-tuned YOLOv5 detect 0.015 seconds, and classic YOLOv7 and fine-tuned YOLOv7 detect objects in 0.009 seconds. While the YOLOv7 model was the fastest running model with an average running time of 0.009 seconds, the proposed fine-tuned YOLOv5 approach achieved the highest performance with an f1 score of 0.972.</p>", "Keywords": "CNN based deep learning; Real-time detection with spatial location; House number detection; Spatial data", "DOI": "10.7717/peerj-cs.1453", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Kayseri University, Kayseri, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Erciyes University, Kayseri, Turkey"}], "References": [{"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Analysis and best parameters selection for person recognition based on gait model using CNN algorithm and image augmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Fake news detection: A hybrid CNN-RNN based deep learning approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100007", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Detection of sitting posture using hierarchical image composition and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Pairing conceptual modeling with machine learning", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "134", "Issue": "", "Page": "101909", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "A comprehensive review of deep learning-based single image super-resolution", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Long Multi-digit Number Recognition from Images Empowered by Deep Convolutional Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "The Computer Journal"}, {"Title": "A soft computing automatic based in deep learning with use of fine-tuning for pulmonary segmentation in computed tomography images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>ís F.<PERSON>; Iágson C.L. Silva", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107810", "JournalTitle": "Applied Soft Computing"}, {"Title": "Online health status monitoring of high voltage insulators using deep learning model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "12", "Page": "4457", "JournalTitle": "The Visual Computer"}, {"Title": "C-Net: A reliable convolutional neural network for biomedical image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "116003", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Review of Yolo Algorithm Developments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "1066", "JournalTitle": "Procedia Computer Science"}, {"Title": "Automated prostate cancer grading and diagnosis system using deep learning-based Yolo object detection algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "117148", "JournalTitle": "Expert Systems with Applications"}, {"Title": "On fine-tuning deep learning models using transfer learning and hyper-parameters optimization for disease identification in maize leaves", "Authors": "<PERSON><PERSON>nian; <PERSON><PERSON><PERSON><PERSON>; P. <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "16", "Page": "13951", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Enhancing classification capacity of CNN models with deep feature selection and fusion: A case study on maize seed classification", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "141", "Issue": "", "Page": "102075", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Survey of fake news detection using machine intelligence approach", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "102118", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Transfer learning and fine‐tuned transfer learning methods' effectiveness analyse in the CNN‐based deep learning models", "Authors": "<PERSON><PERSON> Öztürk; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "4", "Page": "e7542", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A Novel Approach to Improve the Performance of the Database Storing Big Data with Time Information", "Authors": "Murat TAŞYÜREK", "PubYear": 2022, "Volume": "10", "Issue": "4", "Page": "388", "JournalTitle": "Balkan Journal of Electrical and Computer Engineering"}, {"Title": "RETRACTED ARTICLE: A MobileNet-based CNN model with a novel fine-tuning mechanism for COVID-19 infection detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "9", "Page": "5521", "JournalTitle": "Soft Computing"}, {"Title": "ODRP: a new approach for spatial street sign detection from EXIF using deep learning-based object detection, distance estimation, rotation and projection system", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "2", "Page": "983", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 108709407, "Title": "Parallel matrix-free polynomial preconditioners with application to flow simulations in discrete fracture networks", "Abstract": "We develop a robust matrix-free, communication avoiding parallel, high-degree polynomial preconditioner for the Conjugate Gradient method for large and sparse symmetric positive definite linear systems. We discuss the selection of a scaling parameter aimed at avoiding unwanted clustering of eigenvalues of the preconditioned matrices at the extrema of the spectrum. We use this preconditioned framework to solve a 3 × 3 block system arising in the simulation of fluid flow in large-size discrete fractured networks. We apply our polynomial preconditioner to a suitable Schur complement related with this system, which can not be explicitly computed because of its size and density. Numerical results confirm the excellent properties of the proposed preconditioner up to very high polynomial degrees . The parallel implementation achieves satisfactory scalability by taking advantage from the reduced number of scalar products and hence of global communications. Introduction Discretized PDEs and constrained as well as unconstrained optimization problems often require the repeated solution of large and sparse linear systems A x = b , in which A is symmetric positive definite (SPD). For practical scientific and engineering applications, the use of parallel computers is mandatory, due to the large size and resolution of the considered models. The size of these systems can be of order 10 6 ÷ 10 9 and this calls for the use of iterative methods, equipped with ad-hoc preconditioners as accelerators. When the problem size grows up to several millions of unknowns, it is not possible to store the system matrix nor the preconditioner on a single machine. Furthermore, it is necessary to take advantage of several distributed resources to reduce simulation time and, ultimately, the time to market. Also, in many cases the huge size of the matrices can prevent their complete storage. In these instances only the application of the matrix to a vector is available as a routine ( matrix-free regime ). Differently from direct factorization methods, iterative methods do not need the explicit knowledge of the coefficient matrix, however they need to be suitably preconditioned to produce convergence in a reasonable CPU time. The issue is the construction of a preconditioner P ≈ A − 1 which also works in a matrix-free regime. The most common (general-purpose) preconditioners, such as the incomplete Cholesky factorization or most of approximate inverse preconditioners, rely on the knowledge of the coefficients of the matrix. An exception is represented by the AINV preconditioner ([2]), whose construction is however inherently sequential. In all cases, factorization-based methods are not easily parallelizable, the bottleneck being the solution of triangular systems needed when these preconditioners are applied to a vector inside a Krylov subspace-based solver. In this paper we are concerned with the effective development of polynomial preconditioners, i.e. preconditioners that can be expressed as P ≡ p k ( A ) . Polynomial preconditioners are almost ideal candidates to be used as matrix-free parallel preconditioners, since, both in set-up and application, they rely solely on operations, such as the sparse matrix by vector product (SpMV), that are generally provided by highly efficient parallel linear algebra libraries such as PETSc [1], Hypre [17], etc. For instance, the application of p k ( A ) requires k matrix-vector products, without needing the explicit knowledge of the coefficients of matrix A . Moreover, their virtual construction requires only the computation of the coefficients of the polynomials, with negligible computational cost, and the eigenvectors of the preconditioned matrix are the same as those of A . This feature can help accelerating the effect of the polynomial preconditioners by low-rank updates, which take advantage from the (approximate) knowledge of the eigenvectors of PA . The use of polynomial preconditioners for accelerating Krylov subspace methods is not new. We quote for instance the initial works in [25], [31] and [34], [27] where polynomial preconditioners are used to accelerate the Conjugate Gradient and the GMRES [33] methods, respectively. However, these ideas have been recently resumed, mainly in the context of nonsymmetric linear systems, e.g. in [28], [29] or in the acceleration of the Arnoldi method for eigenproblems [15]. An interesting contribution to this subject is [26] where Chebyshev-based polynomial preconditioners are applied in conjunction with sparse approximate inverses. In this paper, starting from the work in [6], we develop a modified Newton-Chebyshev polynomial preconditioner for SPD systems, based on the choice of a parameter aimed at avoiding clustering of eigenvalues around the extrema of the spectrum. A theoretical analysis drives the choice of this parameter. This matrix-free preconditioner is employed in the solution of the discrete problem arising from flow simulations in discrete fracture network (DFN) models. DFN models represent only the fractures as intersecting planar polygons, neglecting the surrounding underground rock formation. The explicit representation of the fractures and their properties in a fully 3D structure requires the prescription of continuity constraints for the fluid flow along the linear intersections. The number of the fractures and their different size, that can change of orders of magnitude, entail a complex and multi-scale geometry, which is not trivial to address. The problem has been effectively reformulated as a PDE-constrained optimization problem in [8], [10]. The formulation relies on the use of non-conforming discretizations of the single fractures and on the minimization of a functional to couple intersecting planes, with no match between the meshes of the fractures and the traces. The problem, often characterized by a huge size, can be algebraically reduced to the solution of a sequence of SPD systems, whose matrix, however, cannot be computed and stored explicitly. Nevertheless, the granular nature of the problem, which can be inherently subdivided in several local problems on the fractures with a moderate exchange of data, is particularly suitable for a massive parallel implementation. In this work we will consider the Preconditioned Conjugate Gradient (PCG) method as iterative solver, accelerated by the modified Newton-Chebyshev polynomial preconditioner. For the parallel implementation, we rely on the Chronos library [20], [24], a linear algebra package specifically designed for high performance computing. Chronos takes advantage of fine-grained parallelism through the use of openMP directives allowing for the use of multiple threads on the same MPI rank. Thanks also to the reduction of global communication required by the repeated scalar products in PCG, the parallel implementation of polynomial preconditioning turns out to be highly efficient, as it will be shown in the numerical experiments. The rest of the paper is organized as follows. In Section 2 we briefly review the Newton-Chebyshev polynomial preconditioner and develop a strategy to avoid unpleasant clustering of eigenvalues around the endpoints of the spectrum. In Section 3 we show how to use our polynomial preconditioner in combination with other accelerators. In Section 4 we describe the test case arising from the DFN application, as well as its algebraic formulation after finite element discretization and reduction to an SPD linear system. In Section 5 we describe our parallel implementation, while Section 6 collects the numerical results of the testing. Section 7 provides some concluding remarks. Section snippets Polynomial preconditioners Two alternative formulations of the optimal polynomial preconditioners for the Conjugate Gradient method for SPD linear systems are presented in [6], following the work in [30] which established a connection between an accelerated Newton method for the matrix equation X − 1 = A and the Chebyshev polynomials in the framework of matrix inversion formula. For the sake of completeness, here we shortly derive these two formulations. Polynomial acceleration of a given preconditioner Let us now assume that a (first level) preconditioner is available in factored form as P seed = W W T , where P seed can be the square root of the inverse diagonal of A , the inverse of the Cholesky factor W = L − 1 or the triangular factor of an approximate inverse preconditioner. In such a case the polynomial preconditioner can be applied to the symmetric matrix A ˆ = W T A W . If the first level preconditioner can be constructed and applied in a matrix-free environment then the whole preconditioner can still be Example of application: discrete fracture network (DFN) flow model As a relevant example of application of the proposed approach, we consider the DFN flow model developed in [8]. The flow simulation in highly-fractured rock systems is computationally very demanding, because of the complexity of the domain and the uncertainty characterizing the geometrical configuration. In this context, DFN models are usually preferred when the fracture network has a dominant impact on the fluid flow dynamics. They explicitly represent the fractures as intersecting planar Parallel implementation An efficient parallel implementation of the application of the Schur complement S u and the explicit computation of its diagonal D S is fundamental for handling large-size problems arising from realistic industrial applications. The proposed algorithm is implemented relying on the Chronos software package, a collection of linear algebra algorithms designed for high performance computers [20]. Chronos is entirely written in C++ using the potential of object-oriented programming (OOP) to easen its Numerical results on the DFN problem The relevant sizes and nonzeros of the test matrices are reported in Table 4. We notice that in the first case n u ≪ n h (large fractures with few mutual intersections) implying that the intermediate matrix Z has more nonzeros than the final Schur complement S u , due to its large row size. For this problem it is more convenient to form explicitly S u and work with the full Schur complement matrix. In the other cases computing the whole Schur complement is not worth due to its size and nonzero number, Conclusions A high-degree polynomial preconditioner has been developed with the aim of reducing the number of scalar products in the Conjugate Gradient iteration. We have shown that the suitable choice of a scaling parameter can speed-up the PCG convergence by avoiding clustering of eigenvalues around the endpoints of the spectral interval. We have given theoretical criteria to select an appropriate value for this parameter. The proposed preconditioning approach reveals particularly useful when the Acknowledgements We acknowledge the CINECA award under the ISCRA initiative, for the availability of high performance computing resources and support. LB and AM also acknowledge the support of the “ INdAM – GNCS Project ”, CUP_E53C22001930001 . References (34) L. Bergamaschi et al. Numerical comparison of iterative eigensolvers for large sparse symmetric matrices Comput. Methods Appl. Mech. Eng. (2002) M. Ferronato et al. A general preconditioning framework for coupled multi-physics problems with application to contact- and poro-mechanics J. Comput. Phys. (2019) M.B. van Gijzen A polynomial preconditioner for the GMRES algorithm J. Comput. Appl. Math. (1995) S. Balay et al. PETSc web page M. Benzi et al. Robust approximate inverse preconditioning for the conjugate gradient method SIAM J. Sci. Comput. (2000) M. Benzi et al. Numerical solution of saddle point problems Acta Numer. (2005) L. Bergamaschi A survey of low-rank updates of preconditioners for sequences of symmetric linear systems Algorithms (2020) L. Bergamaschi et al. Asymptotic convergence of conjugate gradient methods for the partial symmetric eigenproblem Numer. Linear Algebra Appl. (1997) L. Bergamaschi et al. Parallel Newton–Chebyshev polynomial preconditioners for the conjugate gradient method Comput. Math. Methods (2021) S. Berrone et al. A PDE-constrained optimization formulation for discrete fracture network flows SIAM J. Sci. Comput. (2013) S. Berrone et al. On simulations of discrete fracture network flows with an optimization-based extended finite element method SIAM J. Sci. Comput. (2013) S. Berrone et al. Parallel meshing, discretization, and computation of flow in massive discrete fracture networks SIAM J. Sci. Comput. (2019) B. Carpentieri et al. A class of spectral two-level preconditioners SIAM J. Sci. Comput. (2003) K. Chen Matrix Preconditioning Techniques and Applications (2005) Y. Chen et al. Algorithm 887: cholmod, supernodal sparse Cholesky factorization and update/downdate ACM Trans. Math. Softw. (2008) H.C. Elman et al. Finite elements and fast iterative solvers: with applications in incompressible fluid dynamics M. Embree et al. Polynomial preconditioned Arnoldi with stability control SIAM J. Sci. Comput. (2021) View more references Cited by (0) Recommended articles (0) View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.camwa.2023.06.032", "PubYear": 2023, "Volume": "146", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Environmental and Architectural Engineering (ICEA), University of Padua, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Environmental and Architectural Engineering (ICEA), University of Padua, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "M3E - Mathematical Methods and Models for Engineering, University of Padua, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Environmental and Architectural Engineering (ICEA), University of Padua, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Geosciences, University of Trieste, Italy"}], "References": []}, {"ArticleId": 108709409, "Title": "A product form design method integrating Kansei engineering and diffusion model", "Abstract": "The experience economy has shifted consumer demands from the functional to the emotional, and the emotional demands of the user have become a key design consideration. At the same time, product form design often relies solely on the knowledge and experience of designers, resulting in uneven design results and difficult quality assurance. To this end, this paper proposes a product form design method that applies an image generation algorithm oriented toward satisfying the emotional demands of users. Firstly, product images from the network are collected and processed to build a dataset of product images, which is used to train the Diffusion Model (DM) to generate product images that differ from the dataset. Secondly, the Kansei factors are obtained by clustering Kansei words from online reviews using Factor Analysis (FA) and then calculating the weights of the Kansei factors by the Analytic Hierarchy Process (AHP). Thirdly, a questionnaire is distributed to obtain user scores on the Kansei factors of the product images, and the Kansei evaluation value is calculated by weighting, then a prediction model is constructed using Support Vector Regression (SVR) to score and filter the generated images. Finally, the designer selects the highest-scoring images for detailing and tests the effectiveness of the design through user satisfaction. Using the ear thermometer as an example, we have created a product form that meets the emotional demands of users and verifies the scientific validity and effectiveness of the proposed method. Introduction The key to the competitive advantage of a company is the ability of its products to satisfy user demands [1], which consist of both functional and emotional demands. On the one hand, influenced by the experience economy, products that focus only on functionality [2] and ergonomics [3] are no longer of interest to consumers, and emotional demands [4] and aesthetic significance [5] have become key considerations for users when selecting products. On the other hand, the functional parameters of similar products in the market are gradually converging, and enterprises need to invest a lot of resources if they want to attract consumers through functional innovation, but Small and Medium-sized Enterprises (SMEs) will take more risks by focusing on functional innovation due to their limited resources. In addition, with the rise of e-commerce, a large number of consumers buy products online [6], and the form of the product loved by the user can quickly attract the attention of consumers, resulting in the form of the product has become an important factor affecting product sales. Therefore, once an SME launches a product with a form that meets the emotional demands of its users, sales will increase significantly and the company itself will reap great benefits [7]. However, the emotional demands of users are deeply hidden and elusive, and even users themselves are not fully aware of their emotional demands, so there are huge barriers and risks for enterprises to access them [8]. Kansei Engineering (KE) is a technology focused on meeting consumers' emotional demands, quantifying consumer sensibility through engineering methods to support product design [9]. KE can help enterprises identify user needs and obtain a combination of product features preferred by users by studying the relationship between user demands and image features [10], but the subsequent generation of product concepts still needs to be done by designers. In the concept generation stage, designers should collect existing product images for reference and then express the design concept in the form of sketches [11]. The level of innovation of designers is closely related to professional knowledge, work experience, and talent [12], and it takes a lot of time and money to train a good designer. Even experienced designers are inevitably subjective in the design process and may not be able to quickly create concept sketches that fully meet the emotional demands of users [13], resulting in repeated revisions that prolong the design cycle and even affect product sales to the detriment of the company. For SMEs, getting concept sketches that meet user emotional demands in short order will significantly shorten the product design process and improve business efficiency. Image generation technology enables high-quality samples to be generated by learning from existing images. Training with image datasets to generate product images can replace the work of designer sketching, resulting in numerous different sketch solutions in a short period. Diffusion Model (DM) [14] is an emerging technique for image generation and has been used in various fields to produce high-quality images. Smith et al. [15] used the Denoising Diffusion Probabilistic Model (DDPM) to generate good simulations of galaxy observations, which are extremely realistic in terms of galaxy morphology and size. Mandelli et al. [16] used DDPM to generate highly accurate protein blot images that can be used to train and optimize protein detectors. These studies have shown that DMs can generate realistic simulated images through learning. However, the most important criterion for measuring the merits of a design sketch is not authenticity or quantity, but whether the form of the product in the sketch meets the psychological expectations of the consumer. Fortunately, product form can evoke a range of emotional responses from consumers, and such emotions can be measured, for example, through questionnaires [17]. Therefore, user emotions can be measured and the mapping relationship between them and the product features of the image can be established, based on which the concept sketch that best meets user demands can be filtered. In the field of KE, Support Vector Regression (SVR) [18] is a widely used machine learning method for building mappings because it solves nonlinear problems using “kernel” techniques while avoiding overlearning and local minima. Chen et al. [19] used SVR to construct a user image perception prediction model for selecting key design features and validated the model with a knife design case study. Yang et al. [20] used SVR to construct a model of the relationship between design elements and user preferences for vehicle-mounted UAVs, while determining design element priorities using the kano model, then found that products with higher user preference scores also had higher design element priorities, proving that the model constructed by SVR is reliable. These studies show that SVR can establish a mapping relationship between user emotional demands and product form features. By building a model through SVR to predict user Kansei evaluation of generated images, the ones that best meet user expectations can be selected for concept sketches, thus helping enterprises to design product forms that can appeal to consumers and enhance corporate effectiveness. In summary, SMEs face problems such as limited resources, difficulty in accessing user emotional demands, and a degree of subjectivity in design. To this end, this paper proposes a method that integrates KE and image generation technology to help enterprises achieve product forms that are preferred by users. Firstly, DM is used to generate a large number of simulated product images as concept sketch alternatives, eliminating the need to iteratively revise design solutions, resulting in a much shorter design cycle. Secondly, KE technology can obtain the emotional demands of users to help enterprises recognize and understand consumer psychological preferences. Thirdly, a mapping model between user emotional demands and the product form features is constructed by SVR. Finally, the mapping model is used to screen the alternatives that best meet user emotional demands as concept sketches to ensure that the product form launched by the enterprise meets user expectations. The proposed method is aimed at helping SMEs quickly develop a product form that satisfies users and gives them an edge in the fierce market competition with minimal resources. Section snippets Kansei engineering (KE) KE studies the relationship between user emotional demands and product features. The Japanese scholar Nagamachi defined KE as a technique that translates consumer feelings and intentions into design elements. In essence, KE is a theory that has been studied by scholars from three different research directions [21]: user-oriented measurement and evaluation, research of relationships between variables through mathematical algorithms, and virtual KE. Some scholars focus on the measurement and Methodology To help SMEs design product forms that meet user emotional demands with limited resources, this paper proposes a hybrid method that integrates KE and DM. The method applies to products with form as an important selling point, which can accurately identify user emotional demands and provide effective image references and theoretical guidance for product design. For form-driven products, this method can help enterprises design products that users love with few resources. The method consists of Case study This section verifies the validity of the proposed method using an ear thermometer design as an example. Many diseases require measuring body temperature for pre-diagnosis, and ear thermometers have become a necessary medical device in the home. The temperature measurement technology of most ear thermometers on the market today has converged, making functionality no longer the only element influencing user purchase decisions. At the same time, visual elements have a positive impact on consumer Discussion In this study, a hybrid method of product form design is proposed by combining KE and DM, and the effectiveness of the proposed method is verified by an ear thermometer design as an example. First, the ear thermometer images are collected from the network and processed to construct a high-quality ear thermometer image dataset for DM training, and concept sketch alternatives with explicit features are generated. Secondly, Kansei words are extracted from online reviews by word frequency Conclusion This study proposes a product form design method that integrates KE and DM. This method uses DM to generate product images, then investigates the relationship between product features and user emotions through KE techniques to screen concept sketches that meet user emotional demands from the generated images. This study has the following contributions compared with previous studies. (1) In this study, DM is applied to the product design field to generate massive high-quality product images in short Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments The authors would like to thank all of the anonymous referees for the comments and suggestions, which have helped to improve the paper. In addition, the authors wish to thank the Shanghai Pujiang Program and the Chinese Universities Scientific Fund , for financially supporting under Contract No. 2020PJC025 and No. JKZ01212202 . References (59) H.-B. Yan et al. Consumer demand based recombinant search for idea generation Technol. Forecast. Soc. Chang. (2022) Y.-M. Chang et al. Kansei assessment of the constituent elements and the overall interrelations in car steering wheel design Int. J. Ind. Ergon. (2016) X. Wu et al. On the prediction of product aesthetic evaluation based on hesitant-fuzzy cognition and neural network Complexity (2022) C.-H. Lee et al. Service quality driven approach for innovative retail service system design and evaluation: a case study Comput. Ind. Eng. (2019) M. Nagamachi Kansei engineering: a new ergonomic consumer-oriented technology for product development Int. J. Ind. Ergon. (1995) Y.-C. Lee et al. Soccer shoe recommendation system based on multitechnology integration for digital transformation Adv. Eng. Inf. (2022) J.A. Self Communication through design sketches: implications for stakeholder interpretation during concept design Des. Stud. (2019) P. d’Anjou An alternative model for ethical decision-making in design: a Sartrean approach Design Stud. (2011) M.-C. Chen et al. Applying Kansei engineering to design logistics services – a case of home delivery service Int. J. Ind. Ergon. (2015) J.-F. Petiot et al. Measuring consumer perceptions for a better comprehension, specification and assessment of product semantics Int. J. Ind. Ergon. (2004) C.-C. Chen et al. Integrating the Kano model into a robust design approach to enhance customer satisfaction with product design Int. J. Prod. Econ. (2008) Y.-H. Hsiao et al. Logistics service design for cross-border E-commerce using Kansei engineering with text-mining-based online content analysis Telematics Inform. (2017) W. Kim et al. Mining affective experience for a Kansei design study on a recliner Appl. Ergon. (2019) J. Park et al. A fuzzy rule-based approach to modeling affective user satisfaction towards office chair design Int. J. Ind. Ergon. (2004) C.-C. Yang et al. A support vector regression based prediction model of affective responses for product form design Comput. Ind. Eng. (2010) C.-C. Yang Constructing a hybrid Kansei engineering system based on multiple affective responses: application to product form design Comput. Ind. Eng. (2011) D.H. Ackley et al. A learning algorithm for Boltzmann machines Cognit. Sci. (1985) Y. Gan et al. Integrating aesthetic and emotional preferences in social robot design: an Affective design approach with Kansei engineering and deep convolutional generative adversarial network Int. J. Ind. Ergon. (2021) C. Yang et al. Hybrid quality function deployment method for innovative new product design based on the theory of inventive problem solving and Kansei evaluation Adv. Mech. Eng. (2019) C. Wu Application of ergonomics in product design based on computer-aided design Math. Probl. Eng. (2022) C.K. Kwong et al. A modified dynamic evolving neural-fuzzy approach to modeling customer satisfaction for affective design Sci. World J. (2013) M. Wang et al. Research on multiple affective responses design of product based on Kansei engineering and TOPSIS-AISM Math. Probl. Eng. (2022) S. Ahmed et al. Understanding the differences between how novice and experienced designers approach design tasks Res. Eng. Des. (2003) J. Sohl-Dickstein, E.A. Weiss, N. Maheswaranathan, S. Ganguli, Deep unsupervised learning using nonequilibrium... M.J. Smith et al. Realistic galaxy image simulation via score-based generative models Mon. Not. Roy. Astron. Soc. (2022) S. Mandelli et al. Forensic analysis of synthetically generated western blot images IEEE Access (2022) H.M. Khalid et al. Customer emotional needs in product design Concurr. Eng.-Res. Appl. (2006) V.N. Vapnik, The Nature of Statistical Learning Theory, Springer, 1995, doi:... H.Y. Chen, Y.M. Chang, C.C. Yang, A systematic method for selecting critical product form features regarding consumers’... View more references Cited by (0) Recommended articles (0) View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.aei.2023.102058", "PubYear": 2023, "Volume": "57", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Art Design and Media, East China University of Science and Technology, 130 Meilong Road, Xuhui District, Shanghai 200237, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Art Design and Media, East China University of Science and Technology, 130 Meilong Road, Xuhui District, Shanghai 200237, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Art Design and Media, East China University of Science and Technology, 130 Meilong Road, Xuhui District, Shanghai 200237, China;Corresponding authors"}], "References": [{"Title": "Integrating aesthetic and emotional preferences in social robot design: An affective design approach with Kansei Engineering and Deep Convolutional Generative Adversarial Network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "83", "Issue": "", "Page": "103128", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Feeling excited and fluent: how consumers respond to the visual appeals of products in an online shopping environment", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "11", "Page": "1219", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Soccer shoe recommendation system based on multitechnology integration for digital transformation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101457", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Applying Latent Dirichlet Allocation and Support Vector Regression to the Aesthetic Design of Medical Nursing Beds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Xi<PERSON>ing Wu", "PubYear": 2023, "Volume": "23", "Issue": "5", "Page": "1", "JournalTitle": "Journal of Computing and Information Science in Engineering"}]}, {"ArticleId": 108709433, "Title": "Improved TDD operation on Software-Defined Radio platforms towards future wireless standards", "Abstract": "Software-Defined Radio (SDR) platforms are valuable for research and development activities or high-end systems that demand flexible wireless protocols. While low-latency digital baseband processing can be achieved using a dedicated processing unit, like an FPGA or hardware accelerator , its multi-purpose Radio Frequency (RF) front-end often poses a limitation. Zero Intermediate Frequency (ZIF) transceivers are favorable for SDR, however, even for Time Division Duplex (TDD) systems, these transceivers suffer from self-interference when the transmitting and receiving Local Oscillator (LO) is set to the same frequency. To achieve low self-interference, switching from receiving to transmitting mode is needed. However, the time this takes (turnaround time, TT) for configurable RF front-ends often violates the strict timing requirements of protocols like Wi-Fi and 5G, which require response times in the order of microseconds . In this work, we first evaluate the advantages and disadvantages of several methods to suppress self-interference of a ZIF transceiver. Next, a novel approach is proposed, which can reduce the TT to as low as 640 ns using the widely used AD9361 configurable ZIF RF front-end, while the noise floor is at the same level as achieved by the conventional way of switching between transmit and receive mode. We have realized and validated this approach using openwifi — an open-source Wi-Fi implementation on SDR. As a result, the receiver sensitivity is improved by up to 17 dB in the 2.4 GHz band and 9.5 dB in the 5 GHz band, for over-the-air transmissions. Introduction Software-Defined Radio (SDR) platforms enable researchers to experiment and prototype innovative wireless communication solutions more freely as compared to Commercial Off-The-Shelf (COTS) chips thanks to their flexibility and openness. Open-source projects that make use of SDR, such as OpenAirInterface [1], srsRAN [2] (previously srsLTE) and openwifi [3], have recently gained worldwide attention. Apart from providing an implementation of existing wireless standards, many innovations towards future wireless standards are realized on top of it. For example, [4] presents a new physical layer approach to optimize spectrum sharing based on srsLTE. On top of openwifi, [5] randomizes channel state information to preserve privacy, and [6] provides high-precision time synchronization. As these solutions require modifications to the physical layer implementation, they could not have been realized using COTS chips. An SDR generally consists of two parts. The first part, a processing unit, performs the digital signal processing operations, such as modulating and demodulating. Next, an SDR has a configurable Radio Frequency (RF) front-end, which consists amongst others of a Digital-to-Analog Converter (DAC) and Analog-to-Digital Converter (ADC), an amplifier, mixer and several filters. The processing unit can be a separate host computer by using its Central Processing Unit (CPU) that runs the algorithms as programmed by the designer. Another option is to use a Field Programmable Gate Array (FPGA), in which the digital hardware itself is configured by the designer. The latter is especially interesting for experimenting with systems requiring a high data rate, e.g. for future 6G communications [7]. Currently, one of the downsides of SDR using a host computer is the latency induced by the data transfer between the radio front-end and the processing unit [8]. For example, according to [9], the latency between a host computer and a Universal Software Radio Peripheral (USRP) X310 using a PCIe link is 79 µs. For applications that require low latency, an SDR based on a System-on-Chip (SoC) is more suitable, as the connection between CPU and FPGA is faster. As shown in [9], the connection between CPU and FPGA on an SoC can be as low as 1.435 µs. Various works [6], [10], [11], [12] require the low latency aspect. For the implementation of Time Division Duplex (TDD) systems, a radio needs to switch from the receiving (Rx) to the transmitting (Tx) mode (and vice versa). The time this takes is called the turnaround time (TT). The efficiency of a Medium Access Control (MAC) protocol is limited by the TT, as during TT no effective data transfer or carrier sensing can take place. The mechanisms to handle TT in two representative wireless standards (i.e. Wi-Fi and 5G) are illustrated in Fig. 1. In the Wi-Fi standard, an acknowledgment frame as a response to a successfully received frame should be sent to the transmitter after a specific time, called Short InterFrame Space (SIFS). For the standards IEEE 802.11a/g/n, the SIFS is 10 µs when operating in the 2.4 GHz band and 16 µs in the 5 GHz bands [13]. The TT is only a part of the SIFS, as the PHY and MAC processing delay also contribute to the SIFS. Moreover, the slot time, which can be as low as 9 µs for IEEE 802.11n, is determined by the sum of TT, the MAC processing delay, the time for clear channel assessment and twice the air propagation time of the most distant stations (see Figure 10–21 in [13]). Thus, for the design of the MAC, the TT is assumed to be less than 9 µs. Furthermore, in any system using Carrier Sense Multiple Access (CSMA), it is important that the time between sensing an idle environment in Rx mode and actual transmitting in Tx mode is limited, to minimize collision probability. In the 5G standard [14], a guard period (GP) is introduced to accommodate for a downlink (DL) to uplink (UL) delay, consisting of hardware switching, as well as propagation delays. Since the user equipment (UE) may be at different locations, they need to use a specific timing advance for their UL transmission in order to align the received signals at the base station, which takes up part of the guard period. For the smallest cell sizes, a GP of 2 OFDM symbols is recommended, which corresponds to 17.84 µs for a subcarrier spacing of 120 kHz (numerology 3). This thus defines the maximum value the hardware switching can take assuming a negligible propagation delay, but a non-zero propagation delay will limit the switching time even further. The TT of chips designed for typical Wi-Fi MAC protocols is in the order of 1 µs [15]. It is evident that commercial Wi-Fi, cellular or other specialized chipsets can meet their individual protocols’ TT requirement. However, an SDR platform does not have a chip optimized for one specific standard, since they are intended for prototyping vastly diverging wireless systems. Hence its front-end is more generic but less performing when it comes to TT, which to a large extent hinders the applicability of SDR in a real-life network. In this paper, we consider Zero-Intermediate Frequency (ZIF) transceivers, more specifically the AD9361 front-end [16], which is widely used in the SDR research community, as well as in high-end low-volume products. ZIF transceivers use a local oscillator (LO) that produces a frequency equal to or close to the carrier frequency. In this way, less components are needed as compared to transceivers using an intermediate frequency, which makes it more suited for implementation on a chip. The lowest supported TT of the AD9361 is 18 µs according to the specification [17], which is too high for many existing MAC protocols and most certainly not qualified for the future standards. To avoid the TT limitation, both Rx and Tx chain can remain active at all times, but this results in receiver sensitivity degradation due to Tx LO leakage when both chains are operating in the same frequency range. It is worth noticing that the LO leakage is present even if the system is not currently transmitting, which thus limits the performance of TDD (half-duplex) systems. Since the LO leakage exists in the same frequency range as the desired signal, it cannot be eliminated by conventional filtering. The AD9361 already provides RF DC offset and quadrature tracking calibration to mitigate phase and gain error. According to the datasheet, after these corrections, the carrier leakage of the AD9361 equals -50 dBc at 0 dB attenuation. At 2.4 GHz, the chip can achieve maximum 8 dBm output, meaning that the leakage can be as high as -42 dBm. Thus, if the leakage towards the Rx chain is not sufficiently suppressed, it can still drastically influence the receiver sensitivity. For example, the required receiver sensitivity of IEEE 802.11n for Modulation and Coding Scheme (MCS) 0 is -82 dBm and it is even as low as -101.8 dBm for 5G. For the AD9361, no information is given on Tx to Rx port isolation, since it is often dominated by the printed circuit board (PCB) layout, external structures to the chip or antenna isolation. As the AD9361 is a popular chip for modern SDR devices, the strong LO leakage has lead to a common barrier to achieve high performance TDD transceivers on SDR. Since the LO leakage is the main source of interference from the same device, it is hereafter also referred to as self-interference. In this paper, we propose a method for self-interference-free operation of a configurable ZIF transceiver while maintaining a low turnaround time to improve TDD operation on SDR-based wireless systems. In the remainder of this paper, we first discuss the related work on this topic. Next, we explore different options to mitigate self-interference. We then show the realization of our novel method for an SDR-based Wi-Fi transceiver. Subsequently, its performance in terms of improved receiver sensitivity and transmission quality is evaluated using a professional wireless tester, after which we conclude the paper. Section snippets Related work This paper builds upon early work we recently presented in [18]. Herein, we propose the general method to reduce TT and suppress LO leakage for TDD operation on the AD9361. The initial results are merely based on relative noise floor measurements. In this work, we thoroughly evaluate the performance and limitations of alternative methods to suppress the self-interference with acceptable TT. Moreover, we disclose specific operational details of the proposed solution. Furthermore, we perform Self-interference suppression methods This section discusses possible solutions to suppress self-interference using the widely used AD9361 RF front-end. The idea behind these solutions can in general be applied to any ZIF transceiver. For each method, we assess the accompanying TT and measure the potential Tx leakage. We provide a summary describing the advantages and disadvantages of each method in Table 1. Realization To show the realization of the method LO control and to assess its influence on the receiver sensitivity of a typical radio transceiver, we have implemented and integrated the proposed solution into openwifi [3]. This is an open source implementation of the Wi-Fi protocol compatible with the Linux mac80211 subsystem, suited for several SDRs based on the AD9361 chip and SoC architecture. It consists of a driver part written in the C language, which is executed on the embedded CPU, and digital Experimental evaluation In this section, we evaluate the performance of the proposed LO control solution. First of all, measurements via coaxial cable as well as over-the-air are performed in order to quantify the influence of Tx LO control on the receiver sensitivity and noise floor. Next, we examine the influence of our solution on the transmission quality by measuring the error vector magnitude (EVM). Since the solution is integrated with openwifi, both receiver sensitivity and EVM are measured using Wi-Fi Conclusion SDR platforms generally use a multi-purpose RF front-end to enable prototyping of various wireless communication solutions. A ZIF transceiver, which is nowadays the preferred SDR front-end architecture, suffers from self-interference due to LO leakage. This leads to performance degradation even for TDD (half-duplex) systems. Several known methods to suppress the self-interference are discussed, which either cannot achieve a TT suitable for common wireless protocols, e.g. Wi-Fi and 5G systems, Declaration of competing interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. References (42) Cominelli M. et al. IEEE 802.11 CSI randomization to preserve location privacy: An empirical evaluation in different scenarios Comput. Netw. (2021) Nikaein N. et al. OpenAirInterface: A flexible platform for 5G research SIGCOMM Comput. Commun. Rev. (2014) Gomez-Miguelez I. et al. srsLTE: an open-source platform for LTE evolution and experimentation (2016) Jiao X. et al. openwifi: a free and open-source IEEE802.11 SDR implementation on SoC Pereira de Figueiredo F. et al. SCATTER PHY: An open source physical layer for the DARPA spectrum collaboration challenge Electronics (2019) Haxhibeqiri J. et al. Enabling TSN over IEEE 802.11: Low-overhead time synchronization for wi-fi clients Karle C.M. et al. A hardware/software co-design approach to prototype 6G mobile applications inside the GNU radio SDR ecosystem using FPGA hardware accelerators Molla D.M. et al. Software defined radio platforms for wireless technologies IEEE Access (2022) Jiao X. et al. Radio hardware virtualization for coping with dynamic heterogeneous wireless environments Baldesi L. et al. ChARM: Nextg spectrum sharing through data-driven real-time O-RAN dynamic control Seijo O. et al. W-SHARP: Implementation of a high-performance wireless time-sensitive network for low latency and ultra-low cycle time industrial applications IEEE Trans. Ind. Inform. (2021) Wu H. et al. The tick programmable low-latency SDR system GetMobile: Mob. Comput. Commun. (2018) IEEE standard for information technology - part 11: Wireless LAN medium access control (MAC) and physical layer (PHY) specifications (2021) 5G NR user equipment (UE) radio transmission and reception (2019) MAX2828/MAX2829 single-/dual-band 802.11a/b/g world-band transceiver ICs, 19–3455 (2004) AD9361 reference manual, UG-570 (2014) TDD Mode Switching Time, Design Support AD9361/AD9363/AD9364, TDD Mode Switching Time - Documents - Design Support... Havinga T. et al. WIP: Achieving self-interference-free operation on SDR platform with critical TDD turnaround time Sadjina S. et al. A survey of self-interference in LTE-advanced and 5G new radio wireless transceivers IEEE Trans. Microw. Theory Tech. (2020) Wu H. et al. GRT-duplex: A novel SDR platform for full-duplex WiFi Mob. Netw. Appl. (2016) Anttila L. et al. Full-duplexing with SDR devices: Algorithms, FPGA implementation, and real-time results IEEE Trans. Wireless Commun. (2021) View more references Cited by (0) Recommended articles (6) Research article Acute Biological Changes in Gynecologic Surgeons during Surgery: A Prospective Study Journal of Minimally Invasive Gynecology, 2023 Show abstract To assess changes in biological measures of acute stress in surgeons during surgery in real-world settings A prospective cohort study. A tertiary teaching hospital. 8 consultant and 9 training gynecologists. A total of, 161 elective gynecologic surgeries of 3 procedures: laparoscopic hysterectomy, laparoscopic excision of endometriosis, or hysteroscopic myomectomy. Changes in surgeons’ biological measures of acute stress while undertaking elective surgery. Salivary cortisol, mean and maximum heart rate (HR), and indices of the HR variability were recorded before and during surgery. From baseline to during surgery over the cohort, salivary cortisol decreased from 4.1 nmol/L to 3.6 nmol/L (p = .03), maximum HR increased from 101.8 beats per min (bpm) to 106.5 bpm (p <.01), root mean square of standard deviation decreased from 51.1 ms to 39.0 ms (p <.01), and standard deviation of beat-to-beat variability decreased from 73.7 to 59.8 ms (p <.01). Analysis of individual changes in stress by participant-surgery event by paired data graphs reveal inconsistent direction of change in all measures of biological stress despite stratification by surgical experience, role in surgery, level of training, or type of surgery performed. This study measured biometric stress changes at both a group and individual level in real-world, live surgical settings. Individual changes have not previously been reported and the variable direction of stress change by participant-surgery episode identified in this study demonstrates a problematic interpretation of mean cohort findings previously reported. Results from this study suggest that either live surgery with tight environment control or surgical simulation studies may identify what, if any, biological measures of stress can predict acute stress reactions during surgery. Research article An efficient and intelligent traffic flow prediction method based on LSTM and variational modal decomposition Measurement: Sensors, Volume 28, 2023, Article 100843 Show abstract Traffic flow prediction is a very important research field in intelligent transportation system. The traditional prediction methods have a very wide application in traffic flow prediction. However, in the short-term traffic flow prediction, due to the complexity of its influencing factors, the traditional prediction methods cannot predict the short-term traffic flow well. In this paper, the short-term traffic flow prediction model is constructed by using the short-term and short-term memory network, and the modal aliasing problem is solved by using the variational modal decomposition. From the experimental results, the method proposed in this paper is very suitable for short-term traffic flow prediction, and can achieve good prediction effect and accuracy. Research article Technology recognition and traffic characterization for wireless technologies in ITS band Vehicular Communications, Volume 39, 2023, Article 100563 Show abstract The rapid advancement of wireless technologies requires efficient spectrum management considering issues such as interference management and fair coexistence between different technologies. Wireless technology recognition is one of the approaches used to enable intelligent spectrum management. This work proposes a technology classification and traffic characterization system that can recognize and characterize a wide range of wireless technologies that may coexist in the ITS 5.9 GHz band, namely LTE, Wi-Fi, 5G NR, C-V2X PC5, and ITS-G5 technologies. Compared to current state-of-the-art technology recognition solutions, a short time resolution window is selected based on the shortest possible frame duration of the considered technologies. We carried out a “complexity and accuracy trade-off” analysis for six distinct technology recognition models trained and validated at different sampling rates, including 1, 5, 10, 15, 20, and 25 Msps. In addition, the performance of the technology recognition models was evaluated under different channel conditions. For average to high SNR, a less complex CNN model with lower sampling rates (e.g., 5 Msps) can effectively distinguish the signal with 96% classification accuracy. On the other hand, high classification accuracy is obtained using complex, high sampling rate-based CNN models (e.g., 20 Msps) for low (less than 0 dB) SNR channels. A traffic characterization process is also proposed, where the output of the technology recognition is used to identify the traffic characteristics of the technologies in terms of channel occupancy time, transmission pattern, and frame count. The obtained results show that the proposed solution can be used to effectively characterize the identified traffic. Research article Modeling multiuser spectrum allocation for cognitive radio networks Computers & Electrical Engineering, Volume 52, 2016, pp. 266-283 Show abstract Spectrum allocation scheme in cognitive radio networks (CRNs) becomes complex when multiple CR users concomitantly need to be allocated new and suitable bands once the primary user returns. Most existing schemes focus on the gain of individual users, ignoring the effect of an allocation on other users and rely on the ‘periodic sensing and transmission’ cycle which reduces spectrum utilization. This paper introduces a scheme that exploits collaboration among users to detect PU’s return which relieves active CR users from the sensing task, and thereby improves spectrum utilization. It defines a Capacity of Service (CoS) metric based on the optimal sensing parameters which measures the suitability of a band for each contending user and takes into consideration the impact of allocating a particular band on other band seeking users. The proposed scheme significantly improves capacity of service, reduces interference loss and collision, and hence, enhances dynamic spectrum access capabilities. Research article Execution of anticipatory guidance and the knowledge and practice gap among caregivers in Southern Taiwan: A retrospective study Journal of the Formosan Medical Association, 2023 Show abstract This study examined the practice rate of Anticipatory Guidance (AG) and the gap between knowledge and practice among caregivers. We retrospectively collected data from caregivers who brought their children for seven age-based well-child visits (birth to 7 years old) and seven corresponding AG checklists for practice (each ranged from 16 to 19 guidance items, 118 items in total) between 2015 and 2017. Practice rates of guidance items and their association with children's sex, age, residence, and body mass index were collected and analyzed. We enrolled 2310 caregivers (330 per well-child visit). Average practice rates of guidance items in the seven AG checklists were 77.6%–95.1%, generally without significant differences between urban/rural or male/female children. However, lower (<80%) rates were observed for 32 items, including dental check-ups (38.9%), use of fluoride toothpaste (44.6%), screen time (69.4%), and drinking less sugar-sweetened beverages (SSBs) (75.5%), with corresponding knowledge-to-practice gap rates of 55.5%, 47.9%, 30.3%, and 23.8%, respectively. “Drinking less SSBs” was the only item with a higher obesity rate in the non-achieved group versus the achieved group (16.7% vs. 7.4%, p = 0.036; odds ratio: 3.509, 95% CI: 1.153–10.677, p = 0.027). Caregivers in Taiwan practiced most AG recommendations. However, dental check-ups, fluoride toothpaste use, drinking less SSBs, and limiting screen time were less executed items. A higher obesity rate was found among 3–7-year–old children whose caregivers failed to practice the “Drink less SSBs” guidance. Strategies to overcome the gap between knowledge and practice are needed to improve these less-achieved guidance items. Research article Associations between Chronic Kidney Disease and Thinning of Neuroretinal Layers in Multi-ethnic Asian and Caucasian Populations Ophthalmology Science, 2023, Article 100353 Show abstract To evaluate the relationships between chronic kidney disease (CKD) with retinal nerve fiber layer (RNFL) and ganglion cell-inner plexiform layer (GCIPL) thickness profiles in Asian and Caucasian eyes. Cross-sectional analysis 5066 Asian participants (1367 Malays, 1772 Indians and 1927 Chinese) from the Singapore Epidemiology of Eye Diseases Study (SEED) were included, consisting of 9594 eyes for peripapillary RNFL analysis and 8661 eyes for GCIPL analysis. Additionally, 45 064 Caucasian participants (87 649 eyes) from the UK Biobank (UKBB) were included for both macular RNFL analysis and GCIPL analysis. Non-glaucoma participants aged ≥40 years with complete data of estimated glomerular filtration rate (eGFR) were included from both SEED and UKBB. In SEED, peripapillary RNFL and GCIPL thickness were measured by Cirrus HD-OCT 4000. In UKBB, macular RNFL and GCIPL were measured by Topcon 3D-OCT 1000 Mark II. CKD was defined as eGFR<60 ml/min/1.73m <sup>2</sup> in both datasets. To evaluate the associations between kidney function status with RNFL and GCIPL thickness profiles, multivariable linear regression with generalized estimating equation models were performed in SEED and UKBB datasets separately. Average peripapillary and macular RNFL thickness; macular GCIPL thickness In SEED, after adjusting for age, gender, ethnicity, systolic blood pressure, anti-hypertensive medication, diabetes, hyperlipidemia, body mass index, smoking status and intraocular pressure, presence of CKD (β=-1.31; 95% CI -2.37 to -0.26; P=0.015) and reduced eGFR (per 10ml/min/1.73m <sup>2</sup> ; β=-0.32; 95% CI -0.50 to -0.13; P=0.001) were associated with thinner average peripapillary RNFL. Presence of CKD (β=-1.63; 95% CI -2.42 to -0.84) and reduced eGFR (per 10ml/min/1.73m <sup>2</sup> ; β=-0.30; 95% CI –0.44 to -0.16) were consistently associated with thinner GCIPL in SEED (all P<0.001). In UKBB, after adjusting for the above-mentioned covariates (except ethnicity), reduced eGFR (per 10ml/min/1.73m <sup>2</sup> ; β=-0.06; 95% CI -0.10 to -0.01; P=0.008) was associated with thinner macular RNFL and CKD (β=-0.62; 95% CI –1.16 to -0.08; P=0.024) was associated with thinner average GCIPL. We consistently observed associations between CKD and thinning of RNFL and GCIPL across Asian and Caucasian eyes. These findings further suggest that compromised kidney function is associated with RNFL and GCIPL thinning. <sup>☆</sup> This research was partially funded by the Flemish FWO SBO #S003921N VERI-END.com project. View full text © 2023 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.comcom.2023.06.026", "PubYear": 2023, "Volume": "209", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IDLab, Department of Information Technology at Ghent University - imec, Technologiepark-Zwijnaarde 126, B-9052 Ghent, Belgium;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IDLab, Department of Information Technology at Ghent University - imec, Technologiepark-Zwijnaarde 126, B-9052 Ghent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IDLab, Department of Information Technology at Ghent University - imec, Technologiepark-Zwijnaarde 126, B-9052 Ghent, Belgium"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IDLab, Department of Information Technology at Ghent University - imec, Technologiepark-Zwijnaarde 126, B-9052 Ghent, Belgium"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "IDLab, Department of Information Technology at Ghent University - imec, Technologiepark-Zwijnaarde 126, B-9052 Ghent, Belgium"}], "References": [{"Title": "IEEE 802.11 CSI randomization to preserve location privacy: An empirical evaluation in different scenarios", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "191", "Issue": "", "Page": "107970", "JournalTitle": "Computer Networks"}]}, {"ArticleId": *********, "Title": "Research on Power Control Algorithm Based on Distributed Reinforcement Learning", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2023.123052", "PubYear": 2023, "Volume": "12", "Issue": "3", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "Tracking People in Video Using Neural Network Features and Facial Identification Taking into Account the Mask Mode", "Abstract": "<p>Detection and tracking of people in video in distributed video surveillance systems is a difficult task, which has become even more complicated in the conditions of the mask mode, when some people may be wearing masks. To solve this problem, the paper proposes algorithms for detecting masked people and further tracking them using facial recognition systems based on neural networks. To train a neural network to detect masked faces, an approach is proposed that involves applying masks to faces from existing data sets, which makes it possible to expand the training sample and increase the accuracy of recognition of masked faces. The features of masked faces are used to establish the correspondence of people in the frames. This makes it possible to increase the efficiency of detection and tracking upon hiding of people behind objects of the background, high similarity of external features of several people, and analysis of the trajectories of their movement. Examples of detection and tracking of people are shown and appropriate recommendations are given.</p>", "Keywords": "detection and tracking of people in video; neural networks; masked face recognition", "DOI": "10.1134/S1054661823020177", "PubYear": 2023, "Volume": "33", "Issue": "2", "JournalId": 20107, "JournalTitle": "Pattern Recognition and Image Analysis", "ISSN": "1054-6618", "EISSN": "1555-6212", "Authors": [{"AuthorId": 1, "Name": " Shiping Ye", "Affiliation": "Zhejiang Shuren University, Hangzhou, China; International Science and Technology Cooperation Base of Zhejiang Province: Remote Sensing Image Processing and Application, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Belarusian State University, Minsk, Republic of Belarus"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Polotsk State University, Novopolotsk, Republic of Belarus"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "EarthView Image Inc., Huzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "United Institute for Informatics Problems, National Academy of Sciences of Belarus, Minsk, Republic of Belarus"}, {"AuthorId": 6, "Name": "S. V. <PERSON>", "Affiliation": "Belarusian State University, Minsk, Republic of Belarus; United Institute for Informatics Problems, National Academy of Sciences of Belarus, Minsk, Republic of Belarus"}], "References": [{"Title": "Deep learning in video multi-object tracking: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "61", "JournalTitle": "Neurocomputing"}, {"Title": "Person Tracking and Reidentification for Multicamera Indoor Video Surveillance Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "827", "JournalTitle": "Pattern Recognition and Image Analysis"}, {"Title": "Face mask detection using YOLOv3 and faster R-CNN models: COVID-19 environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "13", "Page": "19753", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Masked-face recognition using deep metric learning and FaceMaskNet-21", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "11", "Page": "13268", "JournalTitle": "Applied Intelligence"}, {"Title": "Object Detection in Video Surveillance Based on Multiscale Frame Representation and Block Processing by a Convolutional Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Pattern Recognition and Image Analysis"}, {"Title": "Masked Face Recognition Using Generative Adversarial Networks by Restoring the Face Closed Part", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Optical Memory and Neural Networks"}]}, {"ArticleId": 108709737, "Title": "Multimodal Fake News Detection Incorporating External Knowledge and User Interaction Feature", "Abstract": "<p>With the development of online social media, the number of various news has exploded. While social media provides an information platform for news release and dissemination, it also makes fake news proliferate, which may cause potential social risks. How to detect fake news quickly and accurately is a difficult task. The multimodal fusion fake news detection model is the current research focus and development trend. However, in terms of content, most existing methods lack the mining of background knowledge hidden in the news content and ignore the connection between background knowledge and existing knowledge system. In terms of the propagation chain, the research tends to emphasize only the single chain from the previous communication node, ignoring the intricate communication chain and the mutual influence relationship among users. To address these problems, this paper proposes a multimodal fake news detection model, A-KWGCN, based on knowledge graph and weighted graph convolutional network (GCN). The model fully extracted the features of the content and the interaction between users of the news dissemination. On the one hand, the model mines relevant knowledge concepts from the news content and links them with the knowledge entities in the wiki knowledge graph, and integrates knowledge entities and entity context as auxiliary information. On the other hand, inspired by the “similarity effect” in social psychology, this paper constructs a user interaction network and defines the weighted GCN by calculating the feature similarity among users to analyze the mutual influence of users. Two public datasets, Twitter<PERSON> and Twitter<PERSON>, are selected to evaluate the model, and the accuracy reaches 0.905 and 0.930, respectively. In the comparison experiments, A-KWGCN model has more significant advantages than the other six comparison models in four evaluation indexes. Also, ablation experiments are conducted to verify that knowledge module and weighted GCN module play the significant role in the detection of fake news.</p>", "Keywords": "", "DOI": "10.1155/2023/8836476", "PubYear": 2023, "Volume": "2023", "Issue": "", "JournalId": 8496, "JournalTitle": "Advances in Multimedia", "ISSN": "1687-5680", "EISSN": "1687-5699", "Authors": [{"AuthorId": 1, "Name": "Lifang Fu", "Affiliation": "College of Letters and Science, Northeast Agricultural University, Harbin 150000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Northeast Agricultural University, Harbin 150000, China"}], "References": [{"Title": "DeepFakE: improving fake news detection using tensor decomposition-based deep neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "2", "Page": "1015", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "FakeBERT: Fake news detection in social media with a BERT-based deep learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "11765", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Fake news detection: A hybrid CNN-RNN based deep learning approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100007", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Rumor2vec: A rumor detection framework with joint text and propagation structure representation learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "137", "JournalTitle": "Information Sciences"}, {"Title": "Fake news detection based on news content and social contexts: a transformer-based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "335", "JournalTitle": "International Journal of Data Science and Analytics"}, {"Title": "DSS: A hybrid deep model for fake news detection using propagation tree and stance network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116635", "JournalTitle": "Expert Systems with Applications"}, {"Title": "MCred: multi-modal message credibility for fake news detection using BERT and CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "8", "Page": "10617", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 108709745, "Title": "Worte als Zeichen als Visualisierungen als (rechtliches) Programm", "Abstract": "", "Keywords": "", "DOI": "10.38023/678bb073-334a-492f-93ee-6ad8e05b6675", "PubYear": 2023, "Volume": "", "Issue": "29-Juni-2023", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108709766, "Title": "Impact of cyclone <PERSON><PERSON><PERSON> on phytoplankton size classes structure in the Arabian Sea", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2023.2229499", "PubYear": 2024, "Volume": "45", "Issue": "23", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center of Studies in Resources Engineering, Indian Institute of Technology Bombay, Mumbai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center of Studies in Resources Engineering, Indian Institute of Technology Bombay, Mumbai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Studies in Resources Engineering, Indian Institute of Technology Bombay, Mumbai, India"}], "References": [{"Title": "Long-time-scale investigation of phytoplankton communities based on their size in the Arabian Sea", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "15", "Page": "5992", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 108709838, "Title": "Evaluasi CeLOE Learning Management System (LMS) Universitas Telkom Dengan Technique for User Experience Evaluation In E-Learning (TUXEL) 2.0", "Abstract": "<p>Era Society 5.0 yang maju dengan pesat mendorong perkembangan informasi dan teknologi dalam memudahkan berbagai aktivitas manusia, termasuk dalam hal proses pendidikan yang semakin fleksibel  tanpa terikat waktu dan tempat dengan memanfaatkan platform pembelajaran Learning Management System (LMS). Riset ini bertujuan untuk menguji pengalaman pengguna LMS di Centre for E-Learning and Open Education (CeLOE) Universitas Telkom serta memberikan rekomendasi pengalaman belajar e-learning yang menyenangkan. Sebanyak 233 mahasiswa mengisi kuisioner untuk melakukan evaluasi CeLOE LMS dengan metode Technique for User Experience Evaluation in E-Learning (TUXEL) 2.0. TUXEL merupakan metode evaluasi pengalaman pengguna dengan fokus pada tiga inspeksi utama; inspeksi General Usability, inspeksi Pedagogical Usability, dan evaluasi pada User Experience. Hasil penelitian pada inspeksi General Usability ditemukan 4 permasalahan, sedangkan pada inspeksi Pedagogical Usability ditemukan 8 permasalahan. Pada evaluasi User Experience, secara rata-rata keseluruhan nilai cenderung positif (good). Berdasarkan ketiga evaluasi di atas, CeLOE LMS Universitas Telkom dinilai sudah cukup baik dalam mendukung proses e-learning walaupun ada beberapa bagian butuh perbaikan, terutama bagian teknis dan tampilan tatap muka pengguna (user interface).</p>", "Keywords": "E-Learning;LMS;CeLOE;TUXEL;Evaluasi <PERSON>", "DOI": "10.26418/jp.v8i1.51345", "PubYear": 2022, "Volume": "8", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Telkom University"}], "References": []}, {"ArticleId": 108709848, "Title": "Adaptive Shape Prediction Model (ASPM) for touched and overlapping cell segmentation in cytology images", "Abstract": "Despite the recent development in segmentation of cytological images, designing automatic and accurate segmentation model for touched and overlapping cells is still challenging owing to the poor image contrast with unclear cell contours. In this paper, we introduce a contour and shape-aware segmentation model to establish the shape of touched and overlapping cells; named Adaptive Shape Prediction Model (ASPM). This model is fast, simple, adaptable, and it can be easily generalized for all kinds of cytological images as it does not rely on texture or specific shape knowledge. An open-source Matlab implementation of ASPM is available.", "Keywords": "Segmentation ; Edge-based contouring ; Shape-based deformation ; Touched and overlapping cells ; Cytology images", "DOI": "10.1016/j.simpa.2023.100540", "PubYear": 2023, "Volume": "17", "Issue": "", "JournalId": 66395, "JournalTitle": "Software Impacts", "ISSN": "2665-9638", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Mutah University, Jordan"}], "References": []}, {"ArticleId": 108709858, "Title": "Correction to: Geometrical optimization of centerless grinding process by profiled workrest", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-023-11861-9", "PubYear": 2023, "Volume": "127", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CNR – STIIMA, Milan, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Quasar Labs, Flat 1710, New Providence Wharf, London, England"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CNR – STIIMA, Milan, Italy"}], "References": []}, {"ArticleId": 108709860, "Title": "Sistem Monitoring Smart Greenhouse pada Lahan Terbatas Berbasis Internet of Things (IoT)", "Abstract": "<p>Beberapa faktor lingkungan yang sangat berpengaruh pada proses pertumbuhan dan kesuburan tanaman adalah Faktor suhu, air, kelembapan tanah, kelembapan udara, dan cahaya. Pengembangan metode bercocok tanam cerdas semakin luas didukung dengan teknologi Greenhouse yang mana kondisi iklim bercocok tanam dapat direkayasa. Tidak menutup kemungkinan bahwa tanaman yang tidak cocok ditanam di Indonesia dapat di tanam didalam Greenhouse. Untuk itulah dikembangkan sebuah sistem “Rancang Bangun Smart Greenhouse berbasis IoT (Internet of Things)” berbasis agroteknologi. Sistem Monitoring Smart Greenhouse berhasil diimplementasikan dengan membaca kondisi Suhu, pH Tanah, Kelembaban Tanah dan Udara, dan data tersebut dikirimkan ke server untuk ditampilkan ke pengguna sistem. Data yang diperoleh dari Smart Greenhouse diolah menggunakan operator logika untuk mengendalikan perangkat-perangkat outputan didalam Smart Greenhouse yaitu Lampu, <PERSON><PERSON>, <PERSON><PERSON>, Mist Maker dan Pompa Air. Sistem ini dapat memudahkan pengguna untuk memonitoring dan mengendalikan suhu, air, kelembapan tanah, kelembapan udara, dan cahaya didalam Smart Greenhouse yang sesuai dengan kebutuhan tanaman.</p>", "Keywords": "Smart Greenhouse;Sensor;Internet of Things;Monitoring Tanaman;agroteknologi", "DOI": "10.26418/jp.v8i1.52770", "PubYear": 2022, "Volume": "8", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tanjungpura University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tanjungpura University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tanjungpura University"}], "References": []}, {"ArticleId": 108709867, "Title": "Optimal financing strategy and contract design of supply chain with yield uncertainty under cap-and-trade regulation", "Abstract": "Purpose The purpose of the study is to investigate the financing channels and carbon emission abatement preferences of supply chain members, and further examine the optimal contract design of the retailer. Design/methodology/approach This paper develops a low-carbon supply chain composed of one retailer and one manufacturer, in which the retailer provides trade credit to the manufacturer. Considering the cap-and-trade regulation, the manufacturer with uncertain yield makes decision on whether to invest in emission abatement. There are bank loan and trade credit to finance production for the manufacturer and green credit to finance emission abatement investment. Meanwhile, the retailer may provide the manufacturer with three kinds of contracts to improve emission abatement efficiency, namely, revenue sharing, cost sharing or both sharing. Findings The results show that the retailer prefers to offer financing service at lower interest rate, but trade (and green) credit financing is always optimal for manufacturer and supply chain. The investment in emission abatement is value-added to all players. The sharing contracts offered by the retailer at lower sharing ratios can realize Pareto improvement of the system regardless of the financing scheme. However, comparing with the revenue or cost sharing contract, the existence of optimal sharing ratios makes the both sharing contract more favorable to the retailer. Practical implications The findings provide guidance for the emission-dependent manufacturer in financing and emission abatement decisions, as well as recommendations for the retailer to offer loan service and sharing contract. Originality/value This paper integrates green credit into bank loan or trade credit to analyze the financing decision of the manufacturer with uncertain yield and further considers the influence of three kinds of sharing contracts introduced by the retailer on improving operational performance.", "Keywords": "Yield Uncertainty;Cap-and-trade regulation;Bank and green credit;Trade and green credit;Sharing contract", "DOI": "10.1108/K-02-2023-0226", "PubYear": 2024, "Volume": "53", "Issue": "10", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Economics and Management , Nanjing University of Aeronautics and Astronautics , Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Economics and Management , Nanjing University of Aeronautics and Astronautics , Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Economics and Management , Nanjing University of Aeronautics and Astronautics , Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Economics and Management , Nanjing University of Aeronautics and Astronautics , Nanjing, China"}], "References": [{"Title": "Economics of loan growth, credit risk and bank capital in Islamic banks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "12", "Page": "3591", "JournalTitle": "Kybernetes"}]}, {"ArticleId": *********, "Title": "<PERSON><PERSON><PERSON>ifikasi untuk Memprediksi Karakteristik Mahasiswa pada Pembelajaran Daring", "Abstract": "<p>Keberhasilan pelaksanaan pembelajaran secara daring di masa pandemi dari sisi mahasiswa dipengaruhi oleh faktor eksternal dan internal. Faktor eksternal antara lain ketersediaan sinyal atau jaringan yang baik, kuota internet, serta perangkat penunjang seperti smartphone dan laptop. Se<PERSON> itu, aspek internal seperti motivasi belajar, budaya belajar, dan  kondisi fisik serta psikologi yang baik juga berperan penting dalam keberhasilan proses belajar mahasiswa. Faktor-faktor ini menjadi input untuk membangun model prediksi karakteristik mahasiswa peserta daring. Pada proses pemodelan diawali dengan tahap pre-processing melalui seleksi fitur menggunakan uji independen Chi-Square untuk menentukan variabel yang berpengaruh pada proses prediksi variabel respon. Hasil seleksi variabel independen menghasilkan 16 variabel yang berpengaruh dari total 22 variabel awal.  Adapun jenis label pada variabel respon terdiri dari 4 kelompok yaitu mahasiswa aktif dengan fasilitas terbatas, aktif dengan fasilitas baik, pasif dengan fasilitas baik dan pasif dengan fasilitas terbatas. Pada data penelitian ini, distribusi variabel respon termasuk dalam kategori tidak seimbang (imbalanced class) dengan proporsi kelas terkecil adalah 2,20%. Untuk melakukan balancing data digunakan teknik oversampling sebelum tahap pemodelan. Sementara algoritma klasifikasi yang diujicobakan terdiri dari 6 algoritma yaitu Naïve Bayes (NB), Support Vector Machine (SVM), Random Forest (RF), Regresi Logistik (LR), K-Nearest Neighbor (KNN), dan Decision Tree (DT). Hasil komparasi kinerja menunjukkan bahwa algoritma Support Vector Machine (SVM) lebih unggul dengan nilai F-1 Score 92,8% dan AUC sebesar 99,01%.</p>", "Keywords": "F1-Score;AUC;Imbalanced;<PERSON>las<PERSON>ikasi;Oversampling", "DOI": "10.26418/jp.v8i1.50452", "PubYear": 2022, "Volume": "8", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Negeri Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Negeri Surabaya"}], "References": []}, {"ArticleId": 108709894, "Title": "Reduksi Atribut Menggunakan Chi Square untuk Optimasi Kinerja Metode Decision Tree C4.5", "Abstract": "<p>Pada metode decision tree C4.5, proses split atribut masih belum dapat secara maksimal mengoptimalkan kinerja akurasi pada decision tree yang disebabkan oleh noisy pada atribut yang kurang relevan. Hal tersebut berimplikasi terhadap ukuran dari pohon keputusan menjadi over-fitting sehingga perolehan akurasi pengujian menjadi kurang maksimal. Reduksi atribut merupakan salah satu cara yang dapat dilakukan dalam melakukan seleksi terhadap atribut data yang memiliki persentase pengaruh cenderung kecil sehingga diharapkan mampu dalam meningkatkan akurasi pada metode klasifikasi data. Adapun metode yang diusulkan pada penelitian ini yang digunakan untuk mereduksi atribut yang kurang relevan dari dataset yaitu dengan metode Chi Square sehingga menghasilkan atribut yang mempunyai pengaruh besar terhadap data dan kemudian diklasifikasikan menggunakan decision tree C4.5. Untuk melakukan pengujian terhadap model yang diusulkan, maka penelitian ini menggunakan dataset dari kaggle.com yaitu South Germany Credit yang terdiri dari 1000 records data dengan 20 atribut. Evaluasi kinerja klasikasi yang diusulkan yaitu berdasarkan Confusion Matrix. Dari hasil uji metode yang diusulkan, didapatkan kesimpulan bahwa metode yang diusulkan mampu meningkatkan akurasi decision tree c4.5 dengan rata-rata peningkatan akurasi sebesar 2.5%.</p>", "Keywords": "Decision Tree C4.5;Chi Square;Reduksi Atribut;Confusion Matrix", "DOI": "10.26418/jp.v9i1.56542", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sumatera Utara"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas Sumatera Utara"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sumatera Utara"}], "References": []}, {"ArticleId": 108709918, "Title": "Ekstraksi Fitur Daun dengan Penerapan Metode Compressive Sensing", "Abstract": "<p>Daun menjadi salah satu daya tarik manusia untuk melakukan kegiatan berkebun atau kegiatan lain seperti penjualan tanaman karena dari segi bentuk daun yang unik dan karakteristik yang bermacam-macam. Untuk mengetahui karakteristik pada jenis daun dilakukan proses ekstraksi fitur. Tujuan dari ekstraksi fitur ini untuk mengetahui bentuk, tekstur, warna,ukuran, dan nilai yang digunakan sebagai pembeda antara satu objek dengan objek lain. Pada penelitian ini menggunakan 32 citra daun. Metode yang digunakan pada penelitian ini yaitu Compressive Sensing (CS), Gray Level Co-Occurrence Matrix (GLCM), dan K-Nearest Neighbour (K-NN). Hasil yang diperoleh menunjukan kualitas dari kompresi ketika dilakukan pengujian dengan menggunakan rasio kompresi, MSE, PSNR, dan akura<PERSON>. Didapatkan hasil terbaik ketika menggunakan data latih bernilai 20, data uji bernilai 4, block bernilai 32, baris kompresi bernilai 32, dan resize berukuran 512  512 menghasilkan rasio kompresi bernilai 3,1%, PSNR bernilai 22,1 dB, dan akurasi bernilai 100%.</p>", "Keywords": "Compressive Sensing (CS);Gray Level Co-Occurrence Matrix (GLCM);K-Nearest Neighbour (K-NN);Ekstraksi Fitur Daun;PSNR", "DOI": "10.26418/jp.v8i2.49101", "PubYear": 2022, "Volume": "8", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> R<PERSON>", "Affiliation": "Telkom University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Telkom University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Telkom University"}], "References": []}, {"ArticleId": 108709919, "Title": "Klasifikasi Mazhab <PERSON>kan Metode Naïve Bayes (Studi Kasus: Salat)", "Abstract": "<p>Salat dimulai dengan gerakan takbir dan diakhiri dengan gerakan salam berdasarkan rukun dan syarat pada ketentuan hukum Islam. Dunia fikih penuh dengan perbedaan pendapat, termasuk dalam pembahasan salat. Per<PERSON>aan dalam fikih terwujud dalam bentuk mazhab-mazhab. Sebagai umat islam dianjurkan untuk mengetahui mazhab siapa yang diterapkandalam beribadah kepada Allah SWT. Klasifikasi fikih dalam salat bertujuan untuk mengetahui kecenderungan mazhab yang diikuti oleh seseorang.Naïve Bayes adalah salah satu jenis metode untuk melakukan klasifikasi data. Penelitian ini akan membuat suatu aplikasi pengklasifikasian mazhab fikih salat yang berbasis android dengan 4 mazhab dan menggunakan metode naïve bayes. Sistem ini dibangun dengan Flutter dan database MySQL. Teknologi aplikasi mobile android ini diharapkan dapat memberikan solusi alternatif untuk mengklasifikasikan mazhab dalam fikih salat. Teknik pengujian dilakukan dengan pengujian alpha oleh user (ustadz) dan uji akurasi sistem dilakukan dengan membandingkan hasil antara perhitungan manual dan perhitungan oleh sistem yang dibuat. Pengujian alpha menunjukkan hasil bahwa aplikasi mampu bekerja dengan baik, dan sesuai dengan pengujian akurasi yang menunjukkan akurasi sistem pada aplikasi adalah 100%.</p>", "Keywords": "Fiqih;Salat;Mazhab;Naïve Bayes;Klasifikasi", "DOI": "10.26418/jp.v8i1.51418", "PubYear": 2022, "Volume": "8", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Islam Negeri <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Islam Negeri <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitas Islam Negeri <PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Islam Negeri <PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>iz<PERSON>", "Affiliation": "Universitas Islam Negeri <PERSON>"}], "References": []}, {"ArticleId": 108709927, "Title": "Naïve Bayes untuk Prediksi Tingkat Pemahaman <PERSON> Online Terhadap Mata Kuliah Algoritma Struktur Data", "Abstract": "<p><PERSON>yak masalah yang terjadi dalam proses pembel<PERSON><PERSON> daring, salah satunya adalah kesulitan mahasiswa dalam memahami materi dengan baik. Berbagai upaya telah diklakukan dosen untuk mendukung pembelajaran secara daring, mulai dari penjelasan materi langsung melalui media conference maupun pembuatan video pembelajaran. Untuk mengetahui apakah siswa benar-benar memahami materi yang disampaikan dosen, maka diperlukan penelusuran dengan tujuan jika terdapat banyak mahasiswa yang belum menguasai materi maka perlu ada perubahan pada proses penyampaian materinya. Penelitian ini melakukan prediksi tingkat pemahaman dalam perkuliahan daring. Data yang digunakan pada penelitian ini adalah data nilai mata kuliah Algoritma Struktur Data. Data dengan variable tugas, praktikum, UTS, UAS dan jumlah kehadiran perkuliahan diperoleh dari nilai mahasiswa program studi D3 Manajemen Informatika angkatan 2020 dan 2019. Metode yang digunakan pada penelitian ini adalah Naïve Bayes dengan hasil prediksi pada angkatan 2019 sebesar 46.15% dengan akurasi confusion matrix 69.23% dan hasil prediksi angkatan 2020 sebesar 54.54% dengan akurasi confusion matrix 72.73%, sehingga dapat disimpulkan bahwa perkuliahan online tingkat pemahamanya meningkat dari perkuliahan offline.</p>", "Keywords": "<PERSON><PERSON><PERSON>;Online;<PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON>", "DOI": "10.26418/jp.v8i1.48848", "PubYear": 2022, "Volume": "8", "Issue": "1", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Amikom Yogyakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Amikom Yogyakarta"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Amikom Yogyakarta"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Amikom Yogyakarta"}], "References": []}, {"ArticleId": 108709945, "Title": "Socio-cognitive caste-based optimization", "Abstract": "Metaheuristics are universal optimization algorithms that are used to solve difficult problems, which are unsolvable by classic approaches. In this paper, we aim to construct a novel class of socio-cognitive metaheuristics based on the caste metaphor. We focus on classic evolutionary and agent-based metaheuristics, adding a sociologically inspired structure of the population and cognitively inspired variation operators. In addition to giving the background and details of the proposed algorithms, we apply them to the optimization of a variety of difficult benchmark problems.", "Keywords": "Socio-cognitive computing ; Metaheuristics ; Global optimization", "DOI": "10.1016/j.jocs.2023.102098", "PubYear": 2023, "Volume": "72", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "AGH University of Science and Technology, <PERSON><PERSON> 30, Krakow, 30-010, Poland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AGH University of Science and Technology, <PERSON><PERSON> 30, Krakow, 30-010, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AGH University of Science and Technology, <PERSON><PERSON> 30, Krakow, 30-010, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AGH University of Science and Technology, <PERSON><PERSON> 30, Krakow, 30-010, Poland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Southern University of Science and Technology (SUSTech), Shenzhen, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Southern University of Science and Technology (SUSTech), Shenzhen, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "AGH University of Science and Technology, <PERSON><PERSON> 30, Krakow, 30-010, Poland"}, {"AuthorId": 8, "Name": "Aleksander Byrski", "Affiliation": "AGH University of Science and Technology, <PERSON><PERSON> 30, Krakow, 30-010, Poland;Corresponding author"}], "References": []}, {"ArticleId": 108710029, "Title": "Klasifikasi Citra Burung Jalak Menggunakan Artificial Neural Network dan <PERSON>", "Abstract": "<p>Klasifikasi Citra Burung Jalak Menggunakan Fitur ekstraksi GLCM dan Artificial Neural Network sebelumnya sudah pernah diteliti. <PERSON><PERSON> dalam penelitian tersebut menunjukkan tingkat akurasi dalam klasifikasi jenis burung jalak hanya mencapai 49,20% dengan split ratio 50:50.<PERSON><PERSON>rena itu, peneliti mengusulkan klasifikasi citra burung jalak menggunakan Artificial Neural Network dan Random Forest. Klasifikasi ini bertujuan untuk meningkatkan hasil akurasi sebelumnya. Hasil dalam pengujian yang dilakukan antara Artificial Neural Network dengan Random Forest bisa disimpulkan bahwa pada fitur Wavelet memiliki hasil yang maksimal pada proses klasifikasi burung jalak. Hasil dalam pengujian dimulai dengan Artificial Neural Network memiliki nilai tertinggi pada precision mencapai 0.986, recall 0.987, f-measure sebesar 0.988 dan accuracy sebesar 89% pada split ratio 50:50. <PERSON><PERSON> dari Random Forest memiliki nilai tertinggi pada precision mencapai 1.000, recall mencapai 0.877, f-measure mencapai 0.975 dan accuracy mencapai 100% dengan perbandingan mulai dengan 50:50. Hasil klasifikasi citra burung jalak dari segi matrix confusion menunjukkan bahwa perbandingan data antara 10:90 sampai dengan 90:10 juga sangat berpengaruh dalam proses ketepatan dalam mengklasifikasi. Pengujian yang telah dilakukan telah membuktikan bahwa metode Random Forest dapat memperbaiki kinerja dan hasil pada metode Artificial Neural Network. Serta dalam hal ini menunjukkan Random Forest lebih baik dalam ketepatan dan keakuratan dibandingkan dengan Artificial Neural Network dalam mengklasifikasi jenis burung jalak</p>", "Keywords": "Burung Jalak;Artificial Neural Network;Random Forest;GLCM;Gabor;Wavelet", "DOI": "10.26418/jp.v8i2.53480", "PubYear": 2022, "Volume": "8", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Widyagama Malang"}], "References": []}, {"ArticleId": 108710065, "Title": "An Effective Combination of Convolutional Neural Network and Support Vector Machine Classifier for Arabic Handwritten Recognition", "Abstract": "<p>Recognition of handwritten Arabic characters remains a major challenge for researchers, given the significant differences in handwriting. This paper presents a hybrid method based on combining the most efficient classification techniques. A trained convolutional neural network (CNN) was applied to extract features from character images. Then, a support vector machine (SVM) was used for classification. By combining CNN and SVM, the aim is to exploit both technologies’ strengths. Four hybrid models are proposed in this work. Several databases such as HACDB, HIJJA, AHCD, and MNIST were used to evaluate them. The results obtained are satisfactory compared to similar studies in the literature, with a test accuracy of 89.7, 88.8, 97.3, and 99.4%, respectively.</p>", "Keywords": "Arabic handwritten recognition; support vector machine; convolutional neural network; hybrid models", "DOI": "10.3103/S0146411623030069", "PubYear": 2023, "Volume": "57", "Issue": "3", "JournalId": 14245, "JournalTitle": "Automatic Control and Computer Sciences", "ISSN": "0146-4116", "EISSN": "1558-108X", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "LDDI Laboratory, Mathematics and Computer Science Department, Faculty of Sciences and Technology, University of Adrar, Adrar, Algeria"}], "References": [{"Title": "Hybrid CNN-SVM Classifier for Handwritten Digit Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2554", "JournalTitle": "Procedia Computer Science"}, {"Title": "Arabic handwriting recognition system using convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; Isra Al-Turai<PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2249", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 108710094, "Title": "Structure prediction and materials design with generative neural networks", "Abstract": "The prediction of stable crystal structures is an important part of designing solid-state crystalline materials with desired properties. Recent advances in structural feature representations and generative neural networks promise the ability to efficiently create new stable structures to use for inverse design and to search for materials with tailored functionalities.", "Keywords": "Computational methods;Design; synthesis and processing;Computer Science;general", "DOI": "10.1038/s43588-023-00471-w", "PubYear": 2023, "Volume": "3", "Issue": "7", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [{"AuthorId": 1, "Name": "Da Yan", "Affiliation": "Department of Computer Science, University of Alabama at Birmingham, Birmingham, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Physics, University of Alabama at Birmingham, Birmingham, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, University of Alabama at Birmingham, Birmingham, USA"}], "References": []}, {"ArticleId": 108710155, "Title": "Predictors Based on Convolutional Neural Networks for the Movement Strategy of Trainable Agents for Building Customized Image Descriptors", "Abstract": "<p>We present a description of various custom image descriptor modifications that are used as part of an image classification pipeline with text elements. The problem under consideration is related to the classification of images of commercial facades by the type of services provided. Some of the proposed descriptor types are presented for the first time and demonstrate state-of-the-art performance on open datasets. In our study, we used a special type of descriptor for image areas with text based on traces of the movement of agents. The traces in question are generated using parameterized movement strategies, which are presented and compared in this article.</p>", "Keywords": "combined classifier; visual and text signs; optical character recognition; specialized image descriptor", "DOI": "10.1134/S105466182302013X", "PubYear": 2023, "Volume": "33", "Issue": "2", "JournalId": 20107, "JournalTitle": "Pattern Recognition and Image Analysis", "ISSN": "1054-6618", "EISSN": "1555-6212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, St. Petersburg, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, St. Petersburg, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, St. Petersburg, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, St. Petersburg, Russian Federation"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, St. Petersburg, Russian Federation"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, St. Petersburg, Russian Federation"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "St. Petersburg Electrotechnical University LETI, St. Petersburg, Russian Federation"}], "References": []}, {"ArticleId": 108710259, "Title": "Intelligent Video Streaming at Network Edge: An Attention-Based Multiagent Reinforcement Learning Solution", "Abstract": "<p>Video viewing is currently the primary form of entertainment for modern people due to the rapid development of mobile devices and 5G networks. The combination of pervasive edge devices and adaptive bitrate streaming technologies can lessen the effects of network changes, boosting user quality of experience (QoE). Even while edge servers can offer near-end services to local users, it is challenging to accommodate a high number of mobile users in a dynamic environment due to their restricted capacity to maximize user long-term QoE. We are motivated to integrate user allocation and bitrate adaptation into one optimization objective and propose a multiagent reinforcement learning method combined with an attention mechanism to solve the problem of multiedge servers cooperatively serving users. Through comparative experiments, we demonstrate the superiority of our proposed solution in various network configurations. To tackle the edge user allocation problem, we proposed a method called attention-based multiagent reinforcement learning (AMARL), which optimized the problem in two directions, i.e., maximizing the QoE of users and minimizing the number of leased edge servers. The performance of AMARL is proved by experiments.</p>", "Keywords": "", "DOI": "10.3390/fi15070234", "PubYear": 2023, "Volume": "15", "Issue": "7", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "Xiangdong Tang", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao 266071, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao 266071, China; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao 266071, China"}], "References": [{"Title": "Online user allocation in mobile edge computing environments:A decentralized reactive approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "101904", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Intelligent aerial video streaming: Achievements and challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "103564", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Multi-agent deep reinforcement learning for user association and resource allocation in integrated terrestrial and non-terrestrial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "109827", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 108710348, "Title": "Domain adversarial-based multi-source deep transfer network for cross-production-line time series forecasting", "Abstract": "<p>In industrial settings, building a time series prediction model for new production lines or equipment with new sensors can be challenging due to a lack of historical data. Additionally, there is a temporal covariate shift (TCS) in time series data, which can lead to negative transfer when using transfer models with a single source domain. To this end, a Domain Adversarial-based Multi-source Deep Transfer Network (DA-MDTN) is proposed to transfer knowledge from multiple sub-source for cross-production-line time series forecasting. Firstly, the proposed approach utilizes a source domain segmentation strategy to obtain multiple sub-source domains with different distributions. Multi-way adaptation is then utilized to minimize the distribution difference between the target domain and each sub-source domain. Secondly, domain perplexity scores are taken as weight coefficients for each predictor to indicate the probability of target domain samples belonging to different sub-source domains under deep features. Thirdly, a training strategy of pre-training, multi-way adversarial learning, and predictor adaptation is employed to maximize the utilization of labeled data in the target domain to enhance prediction performance of the model in the target domain and to ensure the generalization ability of the model. Finally, DA-MDTN is evaluated using real data from the esterification stage of the DuPont three-reactors plant. The results indicate that the approach provides a viable solution for building time series prediction models in industrial processes with new equipment or production lines, where historical data may be limited, and there is a TCS in the time series data.</p>", "Keywords": "Time series forecasting; Source domain segmentation; Multi-source domain transfer learning; Adversarial transfer learning; Polyester esterification", "DOI": "10.1007/s10489-023-04729-8", "PubYear": 2023, "Volume": "53", "Issue": "19", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of Digitized Textile & Apparel Technology, Ministry of Education, Donghua University, Shanghai, China; College of Information Sciences and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of Digitized Textile & Apparel Technology, Ministry of Education, Donghua University, Shanghai, China; College of Information Sciences and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Engineering Research Center of Digitized Textile & Apparel Technology, Ministry of Education, Donghua University, Shanghai, China; College of Information Sciences and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 4, "Name": "Hui<PERSON>", "Affiliation": "Engineering Research Center of Digitized Textile & Apparel Technology, Ministry of Education, Donghua University, Shanghai, China; College of Information Sciences and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Research Center of Digitized Textile & Apparel Technology, Ministry of Education, Donghua University, Shanghai, China; College of Information Sciences and Technology, Donghua University, Shanghai, China"}], "References": [{"Title": "Preprocessing methodology for time series: An industrial world application case study", "Authors": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "385", "JournalTitle": "Information Sciences"}, {"Title": "Stock price forecast based on combined model of ARI-MA-LS-SVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "5379", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Industrial time series forecasting based on improved Gaussian process regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "20", "Page": "15853", "JournalTitle": "Soft Computing"}, {"Title": "A novel active multi-source transfer learning algorithm for time series forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1326", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi-source fast transfer learning algorithm based on support vector machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "8451", "JournalTitle": "Applied Intelligence"}, {"Title": "Integrating Multi-Source Transfer Learning, Active Learning and Metric Learning paradigms for Time Series Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107583", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel Domain Adaptive Deep Recurrent Network for multivariate time series prediction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104498", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Bayesian optimization based dynamic ensemble for time series forecasting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Ponnuthurai Nagaratnam Suganthan", "PubYear": 2022, "Volume": "591", "Issue": "", "Page": "155", "JournalTitle": "Information Sciences"}, {"Title": "An improved dynamic Chebyshev graph convolution network for traffic flow prediction with spatial-temporal attention", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "14", "Page": "16104", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi-source transfer learning guided ensemble LSTM for building multi-load forecasting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117194", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Transferring model structure in Bayesian transfer learning for Gaussian process regression", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "108875", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Wasserstein distance based multi-scale adversarial domain adaptation method for remaining useful life prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "3", "Page": "3622", "JournalTitle": "Applied Intelligence"}, {"Title": "A novel intelligent fault diagnosis method of rotating machinery based on signal-to-image mapping and deep Gabor convolutional adaptive pooling network", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117716", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel adversarial domain adaptation transfer learning method for tool wear state prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "254", "Issue": "", "Page": "109537", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 108710354, "Title": "High-speed train positioning based on a combination of Beidou navigation, inertial navigation and an electronic map", "Abstract": "<p>To date, the studies on the combination positioning of high-speed trains have made great progress, but the positioning accuracy of these methods is relatively low. Map-matching positioning can improve positioning accuracy, but information transmission is time-consuming. Few studies incorporate it into combination positioning. To solve the problem, this paper proposes a high-speed train positioning method based on combining a Beidou navigation system, an inertial navigation system, and an electronic map. First, the combination positioning problem is transformed into a multi-objective optimization problem, which weights the direction similarity and distance error to form a fitness function and converts the railway line and the maximum error range of each positioning system into constraints. Second, an improved differential evolution algorithm is proposed to solve this problem. By referencing the gray wolf algorithm, the differential evolution algorithm updates individuals by varying toward the direction of multiple optimal values. Then, a new combination positioning algorithm for high-speed trains is proposed. In the simulation, the increase in positioning speed and accuracy is analyzed and validated. Compared to the current algorithms, the proposed algorithm performs better. The proposed method has practical value for improving the reliability and safety of train operations.</p>", "Keywords": "electronic map; multi-objective optimization; differential evolution algorithm; Beidou navigation system; high-speed train positioning", "DOI": "10.1007/s11432-022-3659-2", "PubYear": 2023, "Volume": "66", "Issue": "7", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Automation Engineering, East China Jiaotong University, Nanchang, China; Key Laboratory of Advanced Control & Optimization of Jiangxi Province, East China Jiaotong University, Nanchang, China; State Key Laboratory of Performance Monitoring Protecting of Rail Transit Infrastructure, East China Jiaotong University, Nanchang, China"}, {"AuthorId": 2, "Name": "Shuai<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Automation Engineering, East China Jiaotong University, Nanchang, China; Key Laboratory of Advanced Control & Optimization of Jiangxi Province, East China Jiaotong University, Nanchang, China; State Key Laboratory of Performance Monitoring Protecting of Rail Transit Infrastructure, East China Jiaotong University, Nanchang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Automation Engineering, East China Jiaotong University, Nanchang, China; Key Laboratory of Advanced Control & Optimization of Jiangxi Province, East China Jiaotong University, Nanchang, China; State Key Laboratory of Performance Monitoring Protecting of Rail Transit Infrastructure, East China Jiaotong University, Nanchang, China"}], "References": [{"Title": "Hybrid quantum particle swarm optimization algorithm and its application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "5", "Page": "159201", "JournalTitle": "Science China Information Sciences"}, {"Title": "An adaptive mutation strategy for differential evolution algorithm based on particle swarm optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "1571", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Improved dynamic grey wolf optimizer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "6", "Page": "877", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": 108710364, "Title": "Interval type-2 fuzzy set induced fuzzy rank-level fusion for face recognition", "Abstract": "Type-2 fuzzy set is extensively studied in the past due to its superiority over type-1 fuzzy set, especially while dealing with data with higher uncertainty and higher association amongst them. This paper proposes a framework by introducing interval type-2 fuzzy set induced fuzzy rank-level fusion for face recognition utilizing multi-feature vectors. It utilizes the outputs of a classifier as confidence factors . We address the wide intra-class variability issue by introducing interval type-2 fuzzy sets by using these confidence factors to generate secondary membership values from the intra-class face images, which are then reduced to a primary membership value. It also mitigates the influence of inter-class similarity by excluding the inter-class face images in the computation. Furthermore, multi-feature vectors for a face image are used to utilize the underlying different discriminant features, which also in turn address the issues of inter-class similarity. For each feature vector, we generate interval type-2 fuzzy set based fuzzy ranks corresponding to all classes. To reduce the complexity, top k fuzzy ranks are fused with corresponding membership values to obtain the fuzzy ranks. Likewise, the interval type-2 fuzzy set based multiple fuzzy ranks are obtained using multiple feature vectors. Finally, these fuzzy ranks are fused with the corresponding complemented confidence factors to get the final fuzzy ranks, based on which a face image is classified and recognized. The method is evaluated on several face databases and found to be superior to the many state-of-the-art methods.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110584", "PubYear": 2023, "Volume": "145", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Application, RCC Institute of Information Technology, Kolkata 700015, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Jadavpur University, 188, Raja S.C. Mullick Road, Kolkata 700 032, India;Corresponding author"}], "References": [{"Title": "2D-human face recognition using SIFT and SURF descriptors of face’s feature regions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "447", "JournalTitle": "The Visual Computer"}, {"Title": "Multi-classifier information fusion in risk analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "121", "JournalTitle": "Information Fusion"}, {"Title": "Local improvement approach and linear discriminant analysis-based local binary pattern for face recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "13", "Page": "7691", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A feature-level fusion based improved multimodal biometric recognition system using ear and profile face", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "1867", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Rank level fusion of multimodal biometrics using genetic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "28", "Page": "40931", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 108710413, "Title": "Multi-agent Reinforcement Learning Aided Sampling Algorithms for a Class of Multiscale Inverse Problems", "Abstract": "<p>In this work, we formulate a class of multiscale inverse problems within the framework of reinforcement learning (RL) and solve it by a sampling method. We propose a multi-agent actor-critic RL algorithm to accelerate the multi-level Monte Carlo Markov Chain (MCMC) sampling once the problem is formulated as an RL process. The policies of the agents are used to generate proposals in the MCMC steps, and the critic, which is centralized, is in charge of estimating the expected reward. There are several difficulties in the implementation of the inverse problem involving features of multiple scales by using traditional MCMC sampling. Firstly, the computation of the posterior distribution involves evaluating the forward solver, which is time-consuming for problems with heterogeneities. This motivates to use the type of multi-level algorithms. Secondly, it is hard to find a proper transition function. To overcome these issues, we learn an RL policy as the proposal generator. We verify our proposed algorithm by solving different benchmark cases of multiscale inverse problems. Our experiments show that the proposed method improves the sampling process and speeds up the residual convergence.</p>", "Keywords": "Multiscale; Inverse problem; Reinforcement learning; MCMC", "DOI": "10.1007/s10915-023-02279-2", "PubYear": 2023, "Volume": "96", "Issue": "2", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, The Chinese University of Hong Kong, Shatin, China"}, {"AuthorId": 2, "Name": "Wing Tat Leung", "Affiliation": "Department of Mathematics, City University of Hong Kong, Kowloon, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, The University of Hong Kong, Pokfulam, China; Hong Kong Quantum AI Lab, Pak Shek Kok, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Carnegie Mellon University, Pittsburgh, USA"}], "References": []}, {"ArticleId": 108710435, "Title": "Pendekatan Data Science untuk Mengukur Empati Masyarakat terhadap Pandemi Menggunakan Analisis <PERSON> dan <PERSON><PERSON><PERSON><PERSON>", "Abstract": "<p>Empati merupakan kemampuan seseorang untuk turut merasakan penderitaan orang lain. Pandemi covid yang melanda dunia, telah menyisakan banyak kehilangan dan keterpurukan. Penelitian ini bertujuan untuk mengetahui emosi masyarakat terhadap penderitaan sesama menggunakan pendekatan sentimen analisis. Dataset yang digunakan adalah komentar masyarakat di Twitter tentang pandemi Covid dalam rentang waktu November-Desember 2020. Data diambil dengan teknik crawling menggunakan library twint, didapatkan data sebanyak 2386 komentar, namun komentar yang mengandung empati hanya sebanyak 984 data. Dataset empati kemudian dilabeli oleh tiga orang menggunakan teknik majority voting. Hasil pengukuran dataset empati menunjukkan 55,7% komentar masyarakat indonesia mengandung empati positif (berempati), 37,4% empati negatif (tidak berempati), dan 6,9% netral. Untuk membentuk model yang dapat mendeteksi empati secara otomatis, maka digunakan  dataset empati sebanyak 400, dengan 200 kelas positif dan 200 kelas negatif, kelas netral tidak digunakan pada penelitian ini karena jumlah data sangat sedikit. Metode machine learning yang digunakan untuk membangun model adalah Support Vector Machine (SVM) dengan metode ekstraksi fitur reliefF. Berdasarkan penelitian yang dilakukan, akurasi sistem dengan metode SVM tanpa seleksi fitur ReliefF adalah 83%. Sedangkan akurasi yang diperoleh sistem dengan seleksi fitur ReliefF mencapai 93% dengan penggunaan 85% fitur dari total keseluruhan fitur.</p>", "Keywords": "<PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON>;SVM;ReliefF", "DOI": "10.26418/jp.v8i3.56655", "PubYear": 2022, "Volume": "8", "Issue": "3", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Trunojoyo Madura"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Trunojoyo Madura"}], "References": []}, {"ArticleId": 108710446, "Title": "A direction vector-guided multi-objective evolutionary algorithm for variable linkages problems", "Abstract": "Recombination is a key component for the evolutionary algorithm to provide promising offspring solutions. However, conventional recombination operators cannot generate high-quality solutions for variable linkages problems due to the particularity of the Pareto optimal set (PS). To tackle this problem, a two-stage multi-objective evolutionary algorithm based on direction vector guidance (DSMOEA) is proposed in this paper. Firstly, a portion of the population is transformed by the eigenmatrix of the covariance matrix to increase the probability of generating high-quality offspring. Then the representative solutions are selected in the transformed population to create the direction vectors. Under the guidance of the direction vectors, the population rapidly approaches PS and generates promising offspring solutions. Finally, Differential Evolution (DE) is performed for searching globally to increase the diversity of the population. The proposed algorithm is tested on three classes of variable linkages problems with 30, 50, and 100 dimensions to verify its performance. The results show that the algorithm is promising for variable linkages problems. Introduction An optimization problem in the real world usually involves two or three objectives, which even conflict with each other [1], [2]. In general, they are known as multi-objective optimization problems and can be formulated as follows: min F ( x ) = ( f 1 ( x ) , f 2 ( x ) , … , f m ( x ) ) s . t . x ∈ X , F ∈ Y where X ⊆ R n is the decision space and x = ( x 1 , x 2 , … , x n ) is the decision vector; Y ⊆ R m is the objective space and F ∈ Y is the object vector [3]. Multi-objective optimization problems are prevalent in reality, such as optimization of transportation routes [4], [5], production scheduling [6], [7], system design [8], [9], and industrial planning [10], [11], etc. They are usually complex, nonlinear, and even intertwined [12]. To respond to the challenges of realistic optimization problems, scholars have performed some operations on the comprehensive benchmark problems to make the artificial problems closer to the actual problems [13], [14]. Variable linkage problem is one of the representatives which simulates the real-world problem by rotating the benchmark problem [15], [16]. Most conventional evolutionary algorithms have poor performance on such issues, and the main reason is that the recombination operators in them are too dependent on the coordinate system [17], [18]. To be specific, it can only effectively search along the main coordinate axis when searching in decision space. For example, NSGA-II generates offspring solutions by simulated binary crossover (SBX) [19]. Since SBX operator is heavily dependent on the coordinate system, the quality of the offspring solutions is not high and will result in premature convergence on variable linkages problems [20]. In order to solve the difficulties encountered by conventional evolutionary algorithms on variable linkages problems, researchers have proposed many methods. One kind of research idea is to adopt Differential Evolution (DE). Storn et al. first proposed DE in 1997 [21]. Because of its simplicity and versatility, DE can be easily improved by modifying specific components, embedding adaptive schemes, or adding further search actions (by changing the DE-motion operator) [22], [23], [24], [25]. In addition, the mutation operator in the DE algorithm has rotation invariance, so some of its variants are often used to solve variable linkages problems [26]. For example, Li et al. [27] proposed MOEA/D-DE, combining the decomposition-based idea with DE. The results indicated that MOEA/D-DE was promising in dealing with variable linkages problems with complicated PS. Coordinate transformation is an effective method to offset variable linkages [26], [28]. Guo et al. [28] modified the crossover operator through the Eigen coordinate system and improved the performance of DE in the high-condition rotation fitness environment. Furthermore, DE with the covariance learning strategy performs well on variable linkages problems [17], [29]. For example, Jiang et al. [30] proposed the dynamic covariance matrix learning technology (DCML) to establish the appropriate coordinate system for the crossover operator. Compared with the previous improved technologies (such as abandoning BCO or replacing BCO with other operators), the proposed DCML is a distributed information type of group-based machine learning technology, which can effectively utilize population information and has guidance for the generation of offspring. Secondly, using other methods to replace the recombination operator is also a research idea. For example, the estimation of distributed algorithms (EDAs) based on population information generate high-quality solutions by establishing a probability model instead of recombination operators. Under mild conditions, it can be concluded from the Karush–Kuhn–Tucker condition that the PS of continuous multi-objective optimization problems in decision space is a piecewise continuous ( m − 1 ) − D manifold, where m is the number of objectives [31]. Based on this regularity, two typical distributed algorithms RM-MEDA and IM-MOEA are proposed [15], [16]. The former is based on regularity and uses local principal component analysis (PCA) to establish a probability model. The latter is based on Gaussian process and establishes an inverse model from the objective space to the decision space. Such algorithms are highly theoretical and have achieved good results on variable linkages problems. Meanwhile, the idea of building models also has applicability in real-world problems. Certainly, some scholars focus on the recombination operator and modify it to eliminate the dependence on the coordinate system. This method is characterized by simplicity, efficiency, and high scalability. At the same time, adding other operations can also overcome some limitations of the recombination operator itself. On single-objective test problems, simplex crossover (SPX) [32], parent-centered crossover (PCX) [33], and unimodal normal distribution crossover (UNDX) [34] are typically performed as new crossover operators to increase the rotation invariance of the algorithm. In the field of multi-objective, Santiago et al. [35] proposed an adaptive operator selection method based on fuzzy logic to improve the quality of approximate solutions. Based on the probability, the controller selects the operator that is most likely to contribute to the improvement of the current Pareto front approximation, which increases the flexibility of the algorithm. Pan et al. [36] proposed a rotation-based simulated binary crossover (RSBX) method to deal with the variable linkages problems. The main idea is to rotate the population in the decision space according to the eigenmatrix of the covariance matrix. Then SBX generates the offspring near the PS by adaptive probability. The advantage of this algorithm is that the structure is simple, and the powerful performance of SBX can be exerted again by using reverse thinking to rotate the population. The above approaches all revolve around how to handle the linkages between variables. The transformation of the coordinate system and the improvement of the recombination operator can be regarded as two mutually inverse processes. The former considers the limitation of the recombination operator from the problems to establish an appropriate coordinate system. At the same time, the latter improves the recombination operator to get rid of its dependence on the coordinate system. However, these two thoughts are full of randomness and uncertainty in generating offspring solutions. Randomness and uncertainty may cause an increase in calculation cost and slow down the convergence rate [37]. This is probably a common problem with most recombination operators. EDAs replace the recombination operator and establish a probability model based on population information to guide the generation of the offspring solutions. Usually, these algorithms have high computational complexity, and the model often lacks universality. Based on the above shortcomings, a two-stage algorithm based on direction vectors is proposed to solve the variable linkage problems. The main contributions of this paper are as follows: (1) The direction vector is used to guide the generation of offspring solutions for variable linkages problems. To a certain extent, it can overcome the shortcomings of high randomness and slow convergence of the recombination operators such as SBX. (2) The application of a two-stage idea ensures the comprehensive performance of the algorithm. The direction vectors ignore the diversity of solutions while ensuring fast convergence, so DE is used for global search to improve the diversity of the population. The rest of this paper is arranged as follows: Section 2 introduces the variable linkage problem and some basic knowledge involved in this paper. Then, Section 3 describes the principle and steps of DSMOEA. In Section 4, numerical results are presented and analyzed. Finally, Section 5 concludes this paper. Section snippets Background and motivation This section first explains the concepts of the variable linkages problems and shows how to add variable linkage to the benchmark problem. Then, the salient features of DE used in the paper are proved and analyzed. Finally, it summarizes the shortcomings of the previous studies on this issue, and presents the motivation of our work. Proposed approach When solving variable linkages problems, the performance of most optimization algorithms deteriorates dramatically, especially in terms of convergence. There are two main reasons for this: one is that the linkage between variables makes PS nonlinear, and conventional recombination operators cannot generate offspring solutions close to PS. The other is that because the variables are intertwined, each dimension of the decision variable cannot be optimized independently, which greatly increases Test problems In order to systematically verify the effectiveness of DSMOEA, three types of benchmark problems are used to compare DSMOEA with other algorithms, namely RM, IM and UF. RM problems come from the literature RM-MEDA [15], which are obtained by modifying ZDT and DTLZ functions. Among them, RM1–RM4 add a linear linkage through the first one of formula (22), and RM5–RM8 add a nonlinear linkage through the second one of formula (22). RM9 and RM10 are multimodal problems with nonlinear linkages. RM Conclusion In this paper, a two-stage multi-objective algorithm based on direction vector (DSMOEA) is proposed for variable linkages problems. DSMOEA adopts population preprocessing technology to eliminate the influence of variable linkage. Direction vectors are used to guide the generation of offspring solutions to solve the problems of high randomness,uncertainty and high computation cost present in previous studies on variable linkages problems. In addition, in order to ensure the good distribution of CRediT authorship contribution statement Qinghua Gu: Conceptualization, Methodology, Investigation, Supervision, Project administration, Funding acquisition, Writing – original draft. Shaopeng Zhang: Methodology, Software, Validation, Visualization, Writing – original draft, Writing – review & editing. Qian Wang: Methodology, Writing – original draft, Writing – review & editing. Neal N. Xiong: Writing – review & editing. Declaration of competing interest The authors declare that there are no conflicts of interest regarding the publication of this paper. Acknowledgments This work was supported in part by the National Natural Science Foundation of China under Grant No. 52074205 and Grant No. 51774228 , in part by Shaanxi province fund for Distinguished Young Scholars, China under Grant No. 2020JC-44 . References (48) Zhou A.M. et al. Multiobjective evolutionary algorithms: A survey of the state of the art, (in English) Swarm Evol. Comput. (2011) Zhou C. et al. Entropy based evolutionary algorithm with adaptive reference points for many-objective optimization problems, (in English) Inform. Sci. (2018) Halim Z. et al. Quantifying and optimizing visualization: An evolutionary computing-based approach, (in English) Inform. Sci. (2017) Zangari M. et al. Multiobjective decomposition-based Mallows Models estimation of distribution algorithm, A case of study for permutation flowshop scheduling problem, (in English) Inform. Sci. (2017) Caraffini F. et al. Parallel memetic structures, (in English) Inform. Sci. (2013) Chen X. et al. Biogeography-based optimization with covariance matrix based migration, (in English) Appl. Soft Comput. (2016) Al-Dabbagh R.D. et al. Algorithmic design issues in adaptive differential evolution schemes: Review and taxonomy, (in English) Swarm Evol. Comput. Rev. (2018) Das S. et al. Recent advances in differential evolution - An updated survey, (in English) Swarm Evol. Comput. (2016) Jiang Q.Y. Multi-objective differential evolution with dynamic covariance matrix learning for multi-objective optimization problems with variable linkages, (in English) Knowl.-Based Syst. (2017) Opara K.R. et al. Differential Evolution: A survey of theoretical analyses, (in English) Swarm Evol. Comput. (2019) Guerrero-Pena E. et al. Multi-objective evolutionary algorithm with prediction in the objective space (2019) Emmerich M.T.M. et al. A tutorial on multiobjective optimization: fundamentals and evolutionary methods, (in English) Nat. Comput. (2018) Coello C.A.C. Evolutionary multi-objective optimization: A historical view of the field, (in English) IEEE Comput. Intell. Mag. (2006) T. Mao, A.S. Mihaita, F. Chen, H.L. Vu, Boosted Genetic Algorithm Using Machine Learning for Traffic Control... Chang Z.Z. et al. 5G private network deployment optimization based on RWSSA in open-pit mine, (in English) IEEE Trans. Ind. Inf. (2022) G. Long, Y.W. Wang, T.C. Lim, Optimal parametric design of delayless subband active noise control system based on... Gu Q.H. et al. Forecasting Nickel futures price based on the empirical wavelet transform and gradient boosting decision trees, (in English) Appl. Soft Comput. (2021) Gu Q.H. et al. A surrogate-assisted multi-objective particle swarm optimization of expensive constrained combinatorial optimization problems, (in English) Knowl.-Based Syst. (2021) Picard C. et al. Realistic constrained multiobjective optimization benchmark problems from design, (in English) IEEE Trans. Evol. Comput. (2021) Caraffini F. et al. A study on rotation invariance in differential evolution Swarm Evol. Comput. (2019) Zhang Q.F. et al. RM-MEDA: A regularity model-based multiobjective estimation of distribution algorithm IEEE Trans. Evol. Comput. (2008) Cheng R. et al. A multiobjective evolutionary algorithm using Gaussian process-based inverse modeling, (in English) IEEE Trans. Evol. Comput. (2015) Deb K. et al. A fast and elitist multiobjective genetic algorithm: NSGA-II IEEE Trans. Evol. Comput. (2002) Deb K. et al. Self-adaptive genetic algorithms with simulated binary crossover, (in English) Evol. Comput. (2001) View more references Cited by (0) Recommended articles (6) Research article Online evolutionary neural architecture search for multivariate non-stationary time series forecasting Applied Soft Computing, Volume 145, 2023, Article 110522 Show abstract Time series forecasting (TSF) is one of the most important tasks in data science. TSF models are usually pre-trained with historical data and then applied on future unseen datapoints. However, real-world time series data is usually non-stationary and models trained offline usually face problems from data drift. Models trained and designed in an offline fashion can not quickly adapt to changes quickly or be deployed in real-time. To address these issues, this work presents the Online NeuroEvolution-based Neural Architecture Search (ONE-NAS) algorithm, which is a novel neural architecture search method capable of automatically designing and dynamically training recurrent neural networks (RNNs) for online forecasting tasks. Without any pre-training, ONE-NAS utilizes populations of RNNs that are continuously updated with new network structures and weights in response to new multivariate input data. ONE-NAS is tested on real-world, large-scale multivariate wind turbine data as well as the univariate Dow Jones Industrial Average (DJIA) dataset. Results demonstrate that ONE-NAS outperforms traditional statistical time series forecasting methods, including online linear regression, fixed long short-term memory (LSTM) and gated recurrent unit (GRU) models trained online, as well as state-of-the-art, online ARIMA strategies. Additionally, results show that utilizing multiple populations of RNNs which are periodically repopulated provide significant performance improvements, allowing this online neural network architecture design and training to be successful. Research article BE-GWO: Binary extremum-based grey wolf optimizer for discrete optimization problems Applied Soft Computing, 2023, Article 110583 Show abstract Since most metaheuristic algorithms for continuous search space have been developed, a number of transfer functions have been proposed including S-shaped, V-shaped, linear, U-shaped, and X-shaped to convert the continuous solution to the binary one. However, most existing transfer functions do not provide exploration and exploitation required to solve complex discrete problems. Thus, in this study, an improved binary GWO named extremum-based GWO (BE-GWO) algorithm is introduced. The proposed algorithm proposes a new cosine transfer function (CTF) to convert the continuous GWO to the binary form and then introduces an extremum (Ex) search strategy to improve the efficiency of converted binary solutions. The performance of the BE-GWO was evaluated through solving two binary optimization problems, the feature selection and the 0-1 multidimensional knapsack problem (MKP). The results of feature selection problems were compared with several well-known binary metaheuristic algorithms such as BPSO, BGSA, BitABC, bALO, bGWO, BDA, BSSA, and BinABC. Moreover, the results were compared with four versions of the binary GWO, the binary PSO, and the binary ABC. In addition, the BE-GWO algorithm was evaluated to solve the 0-1 MKP with difficult and very difficult benchmark instances and the results were compared with several binary GWO variants. The results of two binary problems were statistically analyzed by the Friedman test. The experimental results showed that the proposed BE-GWO algorithm enhances the performance of binary GWO in terms of solution accuracy, convergence speed, exploration, and balancing between exploration and exploitation. Research article Mass movement susceptibility prediction and infrastructural risk assessment (IRA) using GIS-based Meta classification algorithms Applied Soft Computing, Volume 145, 2023, Article 110591 Show abstract In mountainous areas, mass movements are among the most dangerous natural hazards. Infrastructure is a crucial component and is thought of as human wealth. This infrastructure is frequently impacted by mass movements, whose frequency and size are anticipated to rise in the future due to the unequal distribution of rainfall events brought on by climate change. To deal with the anticipated repercussions, the study area, the Northern part of Morocco, needs to implement new management and maintenance practices. Thus, the main motivation of this study was to examine the mass movement vulnerability and assess risk on infrastructures in two provinces of North Morocco, i.e. Chefchaouen and Tetouan. The present study employed Reduced Error Pruning Tree (REPTree) and its ensemble with Bagging, AdaBoost, and Random SubSpace (i.e. REPTree Bagging , REPTree AdaBoost , and REPTree RandomSubSpace ) for mass movement susceptibility mapping (MMSM) based on a comprehensive dataset of 100 mass movements locations which include debris flow, landslide, and rock fall during past 20 years (2000–2020) as well as 12 MM conditioning factors. The result revealed that REPTree RandomSubSpace is the most viable model for MMSM with AUC = 0.8656. In addition, REPTree Bagging , REPTree AdaBoost , and REPTree models also offer acceptable results with AUC of 0.8338, 0.8269, and 0.7942, respectively. After MMSM, the infrastructural risk was assessed and the result showed that among the six infrastructural features considered, buildings and forests have a greater risk of mass movement in the study area. Most of the mass movement and infrastructural risk-prone areas are found in the central and north-eastern parts of the study area. The results further revealed that elevation, land use, lithology, rainfall, and distance from roads are important variables for mass movement and infrastructure risk assessment (IRA). The results of this study offer a systematic sight for decision-makers to mitigate natural disasters and infrastructural risk in the study region. Research article Steel surface defect detection based on self-supervised contrastive representation learning with matching metric Applied Soft Computing, Volume 145, 2023, Article 110578 Show abstract Defect detection is crucial in the quality control of industrial applications. Existing supervised methods are heavily reliant on the large amounts of labeled data. However, labeled data in some specific fields are still scarce, and it requires professionals to do expensive manual annotations. In this paper, we construct a novel self-supervised steel surface defect detection model by learning better embedding feature representation of the defect on large amounts of unlabeled data, which can achieve excellent results in downstream detection tasks. Commonly used image embeddings strategies in self-supervised contrastive learning methods destroy the spatial structures of the image and are not suitable for pre-training of object detection. To address the aforementioned issue, we preserve convolutional feature maps to mine robust data structures and local features, which can enhance the representation capability of the upstream model and make it applicable for transfer to object detection tasks. Besides, in order to eliminate the effect of random augmentations of contrastive learning, which can introduce noise on multi-target coexistence datasets, the Earth Mover’s Distance (EMD) metric is employed to evaluate the contrastive matching similarity. Finally, a Self-supervised Contrastive Representation Learning framework with EMD (SCRL-EMD) is constructed through learning on large-scale unlabeled data and then transferred to Faster R-CNN and RetinaNet for detection performance validation on two public steel defect datasets. Comparative experimental results show that our method can achieve superior results than the state-of-the-art approaches. Compared to the baseline model, it achieves 4.1% and 6.8% mAP improvement on the two datasets, respectively. More importantly, a further improvement can be achieved on a smaller downstream dataset, revealing the meaningful potential of our method in exploiting more readily available unlabeled data. Research article A deep convolutional neural network for salt-and-pepper noise removal using selective convolutional blocks Applied Soft Computing, Volume 145, 2023, Article 110535 Show abstract In recent years, there has been an unprecedented upsurge in applying deep learning approaches, specifically convolutional neural networks (CNNs), to solve image denoising problems, owing to their superior performance. However, CNNs mostly rely on Gaussian noise, and there is a conspicuous lack of exploiting CNNs for salt-and-pepper (SAP) noise reduction. In this paper, we proposed a deep CNN model, namely SeConvNet, to suppress SAP noise in gray-scale and color images. To meet this objective, we introduce a new selective convolutional (SeConv) block. SeConvNet is compared to state-of-the-art SAP denoising methods using extensive experiments on various common datasets. The results illustrate that the proposed SeConvNet model effectively restores images corrupted by SAP noise and surpasses all its counterparts at both quantitative criteria and visual effects, especially at high and very high noise densities. Research article A hybrid collaborative framework for integrated production scheduling and vehicle routing problem with batch manufacturing and soft time windows Computers & Operations Research, Volume 159, 2023, Article 106346 Show abstract This paper studies a new integrated production scheduling and vehicle routing problem where the production of customer orders is performed under a batch manufacturing environment and order deliveries are made by multi-trip heterogeneous vehicles in soft time windows. A bi-objective mixed-integer programming model with maximizing total profits and minimizing total weighted earliness and tardiness has been established. We develop a hybrid collaborative framework to solve this problem, which nests the collaborative mechanism in an optimization mode based on the hybrid algorithm. In the collaborative mechanism, a property on the ideal optimal departure time of the tour is first proposed, based on which an exact strategy is developed to simultaneously coordinate batch manufacturing and tour departure schedules. High-quality integrated solutions are provided by simultaneously making both production scheduling and vehicle routing decisions. Then, in order to get the best integrated solution, we adopt a multi-objective evolutionary algorithm improved by an adaptive large neighborhood search strategy based on the specific problem and coding form to realize the optimization mode. Computational experiments are performed on a dataset containing 30 instances of various scales. The results show that the proposed hybrid collaborative framework performs well in cardinality, convergence, distribution and spread, which is a very competitive method to solve this problem. View full text © 2023 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110581", "PubYear": 2023, "Volume": "145", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Qinghua Gu", "Affiliation": "School of Resources Engineering, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China;Xi’an Key Laboratory for Intelligent Industrial Perception, Calculation and Decision, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China;Corresponding author at: School of Resources Engineering, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Resources Engineering, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China;Xi’an Key Laboratory for Intelligent Industrial Perception, Calculation and Decision, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Xi’an Key Laboratory for Intelligent Industrial Perception, Calculation and Decision, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China;School of Management, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Resources Engineering, Xi’an University of Architecture and Technology, Xi’an, Shaanxi, 710055, China;Department of Mathematics and Computer Science, Northeastern State University, Tahlequah, OK, USA"}], "References": [{"Title": "Adaptive simulated binary crossover for rotated multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100759", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A surrogate-assisted multi-objective particle swarm optimization of expensive constrained combinatorial optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "223", "Issue": "", "Page": "107049", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Forecasting Nickel futures price based on the empirical wavelet transform and gradient boosting decision trees", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107472", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 108710472, "Title": "<PERSON><PERSON><PERSON> Algoritma LSTM dan Na<PERSON> untuk Ana<PERSON>is <PERSON>", "Abstract": "<p>New Normal merupakan sebuah sebutan bagi kebijakan pemerintah untuk mengizinkan masyarakatnya melakukan aktifitas seperti biasa di tengah pandemi Covid-19 yang sedang melanda dengan tetap memperhatikan protokol kesehatan. Kebijakan ini menimbulkan berbagai tanggapan dari masyarakat terutama di media sosial twitter. Untuk itu, diperlukan proses analisis sentimen untuk melakukan pemrosesan terhadap teks yang didapat dari twitter. Analisis sentimen adalah bentuk representasi dari text mining dan text processing. Pada penelitian ini melakukan perbandingan kinerja metode Long Short Therm Memory dengan Naïve Bayes terhadap analisis sentimen Kebijakan New Normal. Hasil yang diperoleh dari penelitian ini yaitu metode  LSTM memiliki kinerja yang lebih baik bila dibandingkan dengan Naïve Bayes. Metode LSTM menghasilkan nilai akurasi, presisi dan recall sebesar 83.33%. Sedangkan metode Naïve Bayes memiliki nilai akurasi, presisi dan recall sebesar 82%.</p>", "Keywords": "<PERSON><PERSON><PERSON>;LSTM;Naïve Bayes;New Normal", "DOI": "10.26418/jp.v8i2.54704", "PubYear": 2022, "Volume": "8", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Teknokrat Indonesia"}], "References": []}, {"ArticleId": 108710481, "Title": "Rekomendasi Makanan Pasien Hiperlipidiemia Berdasarkan Hasil Klasifikasi Menggunakan Metode Naïve Bayes dan <PERSON>", "Abstract": "<p><PERSON><PERSON><PERSON> merupakan kebutuhan manusia untuk memenuhi nutrisi dalam keberlangsungan hidup. <PERSON>un setiap orang perlu memperhatikan makanan yang dikonsumsi karena akan memengaruhi kondisi tubuh. Salah satu zat dalam tubuh manusia yang perlu diperhatikan ketika mengonsumsi makanan adalah lemak. Penelitian terkait konsumsi makanan dengan kadar lemak dalam tubuh sudah banyak dilakukan. Terbukti terdapat jenis makanan yang dapat memengaruhi kesehatan tubuh karena menyebabkan zat lemak yang berlebihan. Pen<PERSON>tian yang dilakukan kali ini adalah klasifikasi data kebutuhan gizi dengan membandingkan algoritma naïve bayes dan decision tree. Hasil klasifikasi antara kedua algoritma tersebut digunakan untuk memberikan rekomendasi makanan yang sesuai untuk dikonsumsi oleh orang yang menderita hiperlipidemia, yakni kondisi di mana kadar lemak dalam tubuh berlebihan. Penelitian dimulai dari pengumpulan data, pra-proses data dengan normalisasi, klasifikasi dengan beberapa model naïve bayes dan algoritma decision tree, menganalisis hasil klasifikasi dengan confusion matrix, dan melakukan implementasi rekomendasi makanan berdasarkan hasil model klasifikasi yang paling optimal. Pada penelitian ini diperoleh hasil bahwa model ComplementNB memiliki akurasi tinggi dibanding model lain namun secara keseluruhan algoritma decision tree lebih stabil dibanding naïve bayes.</p>", "Keywords": "Naïve Bayes;Classification;Decision Tree;Food Recommendation;Hyperlipid;Colesterol", "DOI": "10.26418/jp.v8i2.56386", "PubYear": 2022, "Volume": "8", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}], "References": []}, {"ArticleId": 108710584, "Title": "Modeling Business Capabilities in Enterprise Architecture Practice: The Case of Business Capability Models", "Abstract": "Business capability modeling is a narrow domain of enterprise architecture modeling, which currently remains insufficiently explored. This study identifies nine general business capability modeling approaches and corresponding usage scenarios of business capability models most of which have not been systematically described or even mentioned in the existing literature. This study represents arguably the first intentional effort to explore the practical usage of business capability models in organizations for the purposes of aligning business and IT.", "Keywords": "System Modeling ; enterprise architecture (EA) ; EA modeling ; business capability modeling ; business capability models (BCMs) ; usage", "DOI": "10.1080/10580530.2023.2231635", "PubYear": 2024, "Volume": "41", "Issue": "2", "JournalId": 21771, "JournalTitle": "Information Systems Management", "ISSN": "1058-0530", "EISSN": "1934-8703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Informatics, Graduate School of Business, HSE University, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Community College, King Saud University, Riyadh, Saudi Arabia"}], "References": [{"Title": "Enterprise Architecture Practice under a Magnifying Glass: Linking Artifacts, Activities, Benefits, and Blockers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "1", "Page": "668", "JournalTitle": "Communications of the Association for Information Systems"}, {"Title": "The practical roles of enterprise architecture artifacts: A classification and relationship", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "147", "Issue": "", "Page": "106897", "JournalTitle": "Information and Software Technology"}, {"Title": "Enterprise architecture artifacts as boundary objects: An empirical analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "155", "Issue": "", "Page": "107108", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 108710585, "Title": "Gesetzgebung als Puzzle – was, wenn die Teilchen nicht passen?", "Abstract": "", "Keywords": "", "DOI": "10.38023/2adab6d9-0791-4f59-bda5-ddc7bf91e272", "PubYear": 2023, "Volume": "", "Issue": "29-Juni-2023", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": " <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 108710617, "Title": "Issue Information", "Abstract": "<p>No abstract is available for this article.</p>", "Keywords": "", "DOI": "10.1002/asjc.2878", "PubYear": 2023, "Volume": "25", "Issue": "4", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [], "References": []}, {"ArticleId": 108710662, "Title": "Forecasting students' adaptability in online entrepreneurship education using modified ensemble machine learning model", "Abstract": "Entrepreneurship education has become essential in recent years. This education system may not be unconnected with the global agitation for value creation, employability skills and job creation. Engaging in entrepreneurial training provides students with the skills needed to enhance their ability to create marketable and profitable solutions to emerging problems. To do this, many emerging entrepreneurs rely on technology to engage in entrepreneurship education. This study presents a machine learning technique to predict the adaptability level of students in online entrepreneurship education. The suitability of different algorithms like Random Forest, C5.0, CART and Artificial Neural Network was examined using the Kaggle Educational dataset. The algorithms recorded a high accuracy rate and affirmed machine learning techniques' ability to forecast students' adaptation to online entrepreneurship training. The findings of this research contribute to the field of online entrepreneurship education by providing a reliable and efficient approach for predicting students' adaptability. The proposed modified ensemble machine learning model can assist educators and administrators in identifying students who may require additional support, tailoring instructional strategies, and designing targeted interventions to enhance their adaptability and overall learning experience in online entrepreneurship education.", "Keywords": "Entrepreneurship ; Online education ; Adaptability ; Academic forecasting ; Machine learning", "DOI": "10.1016/j.array.2023.100303", "PubYear": 2023, "Volume": "19", "Issue": "", "JournalId": 66362, "JournalTitle": "Array", "ISSN": "2590-0056", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SRM University, Delhi-NCR, Sonipat, Haryana, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Vocational and Technical Education, Faculty of Education, Alex <PERSON> Federal University, Ndufu-Alike, Abakaliki, Nigeria;Adjunct Faculty, Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Amity University Haryana, Gurugram, Haryana, India;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Chandigarh University, Gharuan, Punjab, 140413, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Sir <PERSON><PERSON><PERSON><PERSON>, Udaipur, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering and Applications, GLA University, Mathura, UP, 281406, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Apex Institute of Technology (CSE), Chandigarh University Gharuan, Mohali, Punjab, India"}], "References": []}]