import requests
from bs4 import BeautifulSoup

import pyecharts

header = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0"
}
for start_num in range(0, 250, 25):
    response = requests.get(f"https://movie.douban.com/top250?start={start_num}" , headers=header)
    html = response.text
    soup = BeautifulSoup(html, "html.parser")
    all_titles = soup.find_all("span", class_="title")
    for title in all_titles:
        if "/" not in title.string:
            print(title.string)
