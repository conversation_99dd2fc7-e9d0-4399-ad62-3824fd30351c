[{"ArticleId": 87831259, "Title": "Youth Trust in Social Media Companies and Expectations of Justice", "Abstract": "Social media platforms aspire to deliver fair resolutions after online harassment. Platforms rely on sanctions like removing content or banning users but these punitive responses provide little opportunity for justice or reparation for targets of harassment. This may be especially important for youth, who experience pervasive harassment which can have uniquely harmful effects on their wellbeing. We conducted a text-message based survey with 832 U.S. adolescents and young adults, ages 14-24, to explore their attitudes towards social media companies' responses to online harassment. We find that youth are twice as likely (41% versus 20%) not to trust social media companies' ability to achieve a fair resolution as they are to trust them. Nearly two-thirds (62%) of youth expressed a preference for an apology from the offender after online harassment, and they were twice as likely to prefer a private apology to a public one (29% versus 14%). Preferences also vary by identity, revealing how a one-size-fits-all approach can harm some youth while benefitting others. We reflect on the opportunities and risks associated with institutional trust and restorative justice for supporting youth who experience online harassment.", "Keywords": "content moderation; criminal justice; fairness; online harassment; punishment; reparative justice; restorative justice; trust; youth", "DOI": "10.1145/3449076", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}], "References": [{"Title": "Reconsidering Self-Moderation", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831260, "Title": "\"Time to Take a Break\"", "Abstract": "Although often ignored, adult gamers, as with children and adolescents, can suffer from problematic gaming. This study explores the use of a built-in gaming gradual intervention system (G-GIS) designed to help adult gamers achieve their desired gaming habits in an autonomous and acceptable manner. In this study, we interviewed 26 heavy adult gamers (i.e., adult gamers who played frequently and for relatively long periods of time) of an online poker game using an in-built G-GIS. Then, we triangulated the interviewees' results with their real-world behavioral data collected over eight months to explore how demographics, attitudes, and contextual factors influenced their use of the G-GIS. The results indicate that family and occupation demographics play key roles in determining adult gamers' gaming habits and their self-control under G-GIS intervention. We also revealed that adult gamers' attitudes and contextual factors can facilitate or hinder the effectiveness of the G-GIS. The findings of this study extend our understanding of heavy adult gamers and reveal how a G-GIS influences adult gamers at the individual level, which can be applied in the design of future game intervention systems, particularly for adult gamers.", "Keywords": "adult gamer; gradual intervention system; interview study; qualitative research", "DOI": "10.1145/3449077", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>ei<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, Beijing, China"}], "References": []}, {"ArticleId": 87831261, "Title": "A Deeper Investigation of the Importance of Wikipedia Links to Search Engine Results", "Abstract": "A growing body of work has highlighted the important role that Wikipedia's volunteer-created content plays in helping search engines achieve their core goal of addressing the information needs of hundreds of millions of people. In this paper, we report the results of an investigation into the incidence of Wikipedia links in search engine results pages (SERPs). Our results extend prior work by considering three U.S. search engines, simulating both mobile and desktop devices, and using a spatial analysis approach designed to study modern SERPs that are no longer just \"ten blue links\". We find that Wikipedia links are extremely common in important search contexts, appearing in 67-84% of desktop SERPs for common and trending queries, but less often for medical queries. Furthermore, we observe that Wikipedia links often appear in \"Knowledge Panel\" SERP elements and are in positions visible to users without scrolling, although Wikipedia appears less often and in less prominent positions on mobile devices. Our findings reinforce the complementary notions that (1) Wikipedia content and research has major impact outside of the Wikipedia domain and (2) powerful technologies like search engines are highly reliant on free content created by volunteers.", "Keywords": "data leverage; search engines; user-generated content; wikipedia", "DOI": "10.1145/3449078", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}], "References": [{"Title": "Can \"Conscious Data Contribution\" Help Users to Exert \"Data Leverage\" Against Technology Companies?", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831262, "Title": "A Composite Framework of Co-located Asymmetric Virtual Reality", "Abstract": "As the variety of possible interactions with virtual reality (VR) continues to expand, researchers need a way to relate these interactions to users' needs and goals in ways that advance understanding. Existing efforts have focused mainly on the symmetric use of technology, which excludes a rising form of interaction known as asymmetric VR, in which co-located participants use different interfaces to interact with a shared environment. There must be a clear path to creating asymmetric VR systems that are rooted in previous work from several fields, as these systems have use cases in education, hybrid reality teams (using VR and other technologies to interact online and face to face), accessibility, as well as entertainment. Currently, there is no systematic way to characterize 1) how a system may be asymmetric, 2) how the different mediation technology and affordances within asymmetric VR support (or do not support) users' goals, and 3) the relationships and collaborative capabilities between users of these different technologies. In this paper, the authors use a scoping review to explore relevant conceptual frameworks for asymmetric interaction, mediation technology, and computer supported cooperative work to clarify the dimensions of asymmetry and synthesize the literature into a Composite framework for Asymmetric VR (CAVR). The paper concludes with suggestions of ways to test and expand the framework in order to guide future research as it identifies the most-beneficial interaction paradigms for co-located asymmetric VR.", "Keywords": "asymmetric vr; collaboration; conceptual frameworks; extended reality (xr); mixed reality (mr); workspace awareness", "DOI": "10.1145/3449079", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Iowa State University, Ames, IA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Iowa State University, Ames, IA, USA"}], "References": [{"Title": "Technologies for Enhancing Collocated Social Interaction: Review of Design Solutions and Approaches", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "1-2", "Page": "29", "JournalTitle": "Computer Supported Cooperative Work (CSCW)"}]}, {"ArticleId": 87831263, "Title": "<PERSON><PERSON><PERSON>", "Abstract": "We designed and developed Fakey, a game to improve news literacy and reduce misinformation spread by emulating a social media feed. We analyzed player interactions with articles in the feed collected over 19 months within a real-world deployment of the game. We found that Fakey is effective in priming players to be suspicious of articles from questionable sources. Players who interact with more articles in the game enhance their skills in spotting mainstream content, thus confirming the utility of <PERSON><PERSON><PERSON> for improving news literacy. Semi-structured interviews with those who played the game revealed that players find it simple, fun, and educational. The principles and mechanisms used by <PERSON><PERSON><PERSON> can inform the design of social media functionality to help people distinguish between credible and questionable content in their news feeds.", "Keywords": "fake news; game; low-credibility content; misinformation; news feed; news literacy; social media", "DOI": "10.1145/3449080", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "New York University Abu Dhabi, Abu Dhabi, UAE"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}], "References": [{"Title": "Learning to evaluate: An intervention in civic online reasoning", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "103711", "JournalTitle": "Computers & Education"}]}, {"ArticleId": ********, "Title": "Where Responsible AI meets Reality", "Abstract": "Large and ever-evolving technology companies continue to invest more time and resources to incorporate responsible Artificial Intelligence (AI) into production-ready systems to increase algorithmic accountability. This paper examines and seeks to offer a framework for analyzing how organizational culture and structure impact the effectiveness of responsible AI initiatives in practice. We present the results of semi-structured qualitative interviews with practitioners working in industry, investigating common challenges, ethical tensions, and effective enablers for responsible AI initiatives. Focusing on major companies developing or utilizing AI, we have mapped what organizational structures currently support or hinder responsible AI initiatives, what aspirational future processes and structures would best enable effective initiatives, and what key elements comprise the transition from current work practices to the aspirational future.", "Keywords": "industry practice; organizational structure; responsible ai", "DOI": "10.1145/3449081", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Partnership on AI &amp; Accenture, San Francisco, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Partnership on AI, San Francisco, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Spotify, San Francisco, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Accenture, San Francisco, CA, USA"}], "References": []}, {"ArticleId": 87831265, "Title": "Nobody Puts <PERSON><PERSON><PERSON> in a Binary", "Abstract": "Prior work on transgender technology users in CSCW has primarily focused on how they interact with algorithms and communication technology, empirically identifying specific use cases and profiles, and speaking largely to the designers and developers of these platforms. This work has emphasized how trans people are excluded, harmed, and misrepresented in existing platforms, algorithms, and research methods. While these critiques are important, this paper explores what trans-inclusive quantitative methods could be by applying a participant- or user-driven approach. While the problem of trans-inclusive, -affirming, or -empowering research methods is not specifically a CSCW problem---as we directly confront by comparing and contrasting the perspectives of CSCW and conventional demography---we argue that a CSCW lens may be uniquely suited to addressing it. To this end, this paper makes several contributions: conceptually, we identify points of increasing convergence between conventional demographic research methods (and criticisms thereof) and CSCW, focusing on shared limitations surrounding how identity is handled in research; methodologically, we present a sketch of how these limitations might be addressed by using social network analysis to \"triangulate\" social identities while considering them relative and situated; empirically, we implement these methods in a case study of gender within the broader social context of Reddit and discuss the results.", "Keywords": "digital demography; feminism; identity; lgbtq+; manosphere; red pill; reddit; social network analysis; transgender", "DOI": "10.1145/3449082", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": [{"Title": "\"I run the world's largest historical outreach project and it's on a cesspool of a website.\" Moderating a Public Scholarship Site on Reddit: A Case Study of r/AskHistorians", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831266, "Title": "CASS", "Abstract": "Chatbots systems, despite their popularity in today's HCI and CSCW research, fall short for one of the two reasons: 1) many of the systems use a rule-based dialog flow, thus they can only respond to a limited number of pre-defined inputs with pre-scripted responses; or 2) they are designed with a focus on single-user scenarios, thus it is unclear how these systems may affect other users or the community. In this paper, we develop a generalizable chatbot architecture (CASS) to provide social support for community members in an online health community. The CASS architecture is based on advanced neural network algorithms, thus it can handle new inputs from users and generate a variety of responses to them. CASS is also generalizable as it can be easily migrate to other online communities. With a follow-up field experiment, CASS is proven useful in supporting individual members who seek emotional support. Our work also contributes to fill the research gap on how a chatbot may influence the whole community's engagement.", "Keywords": "ai deployment; bot; chatbot; conversational agent; emotional support; explainable ai; healthcare; human ai collaboration; human ai interaction; machine learning; neural network; online community; peer support; pregnancy; social support; system building; trustworthy ai", "DOI": "10.1145/3449083", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Software, Chinese Academy of Sciences &amp; University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Hong Kong University of Science and Technology, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Pace University, New York, NY, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "IBM Research, Yorktown Heights, NY, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Hong Kong University of Science and Technology, Hong Kong, Hong Kong"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Software, Chinese Academy of Sciences, Beijing, China"}], "References": [{"Title": "The Design and Implementation of XiaoIce, an Empathetic Social Chatbot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "1", "Page": "53", "JournalTitle": "Computational Linguistics"}, {"Title": "Same benefits, different communication patterns: Comparing Children's reading with a conversational agent vs. a human partner", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "161", "Issue": "", "Page": "104059", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 87831267, "Title": "Lessons Learned from Designing an AI-Enabled Diagnosis Tool for Pathologists", "Abstract": "Despite the promises of data-driven artificial intelligence (AI), little is known about how we can bridge the gulf between traditional physician-driven diagnosis and a plausible future of medicine automated by AI. Specifically, how can we involve AI usefully in physicians' diagnosis workflow given that most AI is still nascent and error-prone (\\eg in digital pathology)? To explore this question, we first propose a series of collaborative techniques to engage human pathologists with AI given AI's capabilities and limitations, based on which we prototype Impetus --- a tool where an AI takes various degrees of initiatives to provide various forms of assistance to a pathologist in detecting tumors from histological slides. We summarize observations and lessons learned from a study with eight pathologists and discuss recommendations for future work on human-centered medical AI systems.", "Keywords": "digital pathology; human-ai collaboration; human-centered ai; medical ai", "DOI": "10.1145/3449084", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Los Angeles, Los Angeles, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Los Angeles, Los Angeles, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> '<PERSON>' <PERSON>", "Affiliation": "University of California, Los Angeles, Los Angeles, CA, USA"}], "References": []}, {"ArticleId": 87831268, "Title": "YouTube Recommendations and Effects on Sharing Across Online Social Platforms", "Abstract": "In January 2019, YouTube announced its platform would exclude potentially harmful content from video recommendations while allowing such videos to remain on the platform. While this action is intended to reduce YouTube's role in propagating such content, continued availability of these videos via hyperlinks in other online spaces leaves an open question of whether such actions actually impact sharing of these videos in the broader information space. This question is particularly important as other online platforms deploy similar suppressive actions that stop short of deletion despite limited understanding of such actions' impacts. To assess this impact, we apply interrupted time series models to measure whether sharing of potentially harmful YouTube videos in Twitter and Reddit changed significantly in the eight months around YouTube's announcement. We evaluate video sharing across three curated sets of anti-social content: a set of conspiracy videos that have been shown to experience reduced recommendations in YouTube, a larger set of videos posted by conspiracy-oriented channels, and a set of videos posted by alternative influence network (AIN) channels. As a control, we also evaluate these effects on a dataset of videos from mainstream news channels. Results show conspiracy-labeled and AIN videos that have evidence of YouTube's de-recommendation do experience a significant decreasing trend in sharing on both Twitter and Reddit. At the same time, however, videos from conspiracy-oriented channels actually experience a significant increase in sharing on Reddit following YouTube's intervention, suggesting these actions may have unintended consequences in pushing less overtly harmful conspiratorial content. Mainstream news sharing likewise sees increases in trend on both platforms, suggesting YouTube's suppression of particular content types has a targeted effect. In summary, while this work finds evidence that reducing exposure to anti-social videos within YouTube potentially reduces sharing on other platforms, increases in the level of conspiracy-channel sharing raise concerns about how producers -- and consumers -- of harmful content are responding to YouTube's changes. Transparency from YouTube and other platforms implementing similar strategies is needed to evaluate these effects further.", "Keywords": "content quality; cross-platform; moderation; reddit; twitter; youtube", "DOI": "10.1145/3449085", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "New Jersey Institute of Technology, Newark, NJ, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "New York University, New York, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "New York University, New York, NY, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "New York University, New York, NY, USA"}], "References": [{"Title": "Algorithmic extremism: Examining YouTube's rabbit hole of radicalization", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "First Monday"}]}, {"ArticleId": 87831269, "Title": "Characterizing Student Engagement Moods for Dropout Prediction in Question Pool Websites", "Abstract": "Problem-Based Learning (PBL) is a popular approach to instruction that supports students to get hands-on training by solving problems. Question Pool websites (QPs) such as LeetCode, Code Chef, and Math Playground help PBL by supplying authentic, diverse, and contextualized questions to students. Nonetheless, empirical findings suggest that 40% to 80% of students registered in QPs drop out in less than two months. This research is the first attempt to understand and predict student dropouts from QPs via exploiting students' engagement moods. Adopting a data-driven approach, we identify five different engagement moods for QP students, which are namely challenge-seeker, subject-seeker, interest-seeker, joy-seeker, and non-seeker. We find that students have collective preferences for answering questions in each engagement mood, and deviation from those preferences increases their probability of dropping out significantly. Last but not least, this paper contributes by introducing a new hybrid machine learning model (we call Dropout-Plus) for predicting student dropouts in QPs. The test results on a popular QP in China, with nearly 10K students, show that Dropout-Plus can exceed the rival algorithms' dropout prediction performance in terms of accuracy, F1-measure, and AUC. We wrap up our work by giving some design suggestions to QP managers and online learning professionals to reduce their student dropouts.", "Keywords": "dropout prediction; engagement mood; online judge; online learning; problem-based learning (pbl); question pool website (qp)", "DOI": "10.1145/3449086", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, Hong Kong"}, {"AuthorId": 3, "Name": "Pan Hui", "Affiliation": "Hong Kong University of Science and Technology &amp; University of Helsinki, Hong Kong, Hong Kong"}], "References": []}, {"ArticleId": 87831270, "Title": "The Language of Situational Empathy", "Abstract": "Empathy is the tendency to understand and share others' thoughts and feelings. Literature in psychology has shown through surveys potential beneficial implications of empathy. Prior psychology literature showed that a particular type of empathy called \"situational empathy\" --- an immediate empathic response to a triggering situation (e.g., a distressing situation) --- is reflected in the language people use in response to the situation. However, this has not so far been properly measured at scale. In this work, we collected 4k textual reactions (and corresponding situational empathy labels) to different stories. Driven by theoretical concepts, we developed computational models to predict situational empathy from text and, in so doing, we built and made available a list of empathy-related words. When applied to Reddit posts and movie transcripts, our models produced results that matched prior theoretical findings, offering evidence of external validity and suggesting its applicability to unstructured data. The capability of measuring proxies for empathy at scale might benefit a variety of areas such as social media, digital healthcare, and workplace well-being.", "Keywords": "computational model; empathy; lexicon", "DOI": "10.1145/3449087", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Nokia Bell Labs &amp; University of Nottingham, Cambridge, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Nokia Bell Labs &amp; IT University of Copenhagen, Cambridge, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nokia Bell Labs, Cambridge, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Nokia Bell Labs &amp; King's College London, Cambridge, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Indiana University, Indianapolis, IN, USA"}], "References": []}, {"ArticleId": 87831271, "Title": "Using the Lenses of Emotion and Support to Understand Unemployment Discourse on Reddit", "Abstract": "Unemployed individuals experience emotional upheaval and financial constraints, and they struggle to share their challenges and obtain support from their family members and social circles. While prior work has shown that social media platforms enable their users to safely share their turmoil, hardships, and emotional upheavals and seek support, little is known about how the unemployed use such platforms. Based on a mixed-methods analysis of data retrieved from unemployment communities on Reddit, our study reveals that redditors in these communities engage in exchanges that range from queries seeking information about job searching to discussions about sensitive topics such as the health and social implications of unemployment. Also, while redditors use mostly negative emotional tonality in their texts when initiating a new post, the comments in response provide considerable social support in the form of esteem, emotional, informational, network, and instrumental support. Based on these findings, we share the theoretical implications of our work and offer design", "Keywords": "emotion; job search; reddit; social support; unemployed; unemployment", "DOI": "10.1145/3449088", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Syracuse University, Syracuse, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Studies, Syracuse, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Syracuse University, Syracuse, NY, USA"}], "References": []}, {"ArticleId": 87831272, "Title": "The Unique Challenges for Creative Small Businesses Seeking Feedback on Social Media", "Abstract": "Social media can be an effective source of feedback on open-ended work, such as product design. Unlike large businesses with entire teams dedicated to \"social,\" little is understood about how small business owners-constrained both in personnel and resources-leverage the benefits of direct, informal communication channels afforded by social media. Drawing on a series of design workshops and interviews with 26 small business owners at a local feminist makerspace, we report on the unique challenges small business owners experience when seeking feedback on open-ended work via social media. We found participants carefully balanced large-scale access to diverse audiences with attempts to receive reliable feedback, and they often targeted audiences narrowly to reinstate control and build trust. In addition, the small business owners in our workshop idealized building authentic relationships with their social audiences to create collectively. To do so successfully, participants detailed the extensive behind-the-scenes work required of them such as navigating blurred personal and business identities and the self-regulation necessary to continuously stay engaged and not internalize discouraging feedback.", "Keywords": "creative entrepreneurship; design feedback; online community; online feedback exchange; product development; social commerce; social media", "DOI": "10.1145/3449089", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "California Polytechnic University, San Luis Obispo, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}], "References": [{"Title": "Critique Me", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> Liu", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831273, "Title": "Modular Politics", "Abstract": "Governance in online communities is an increasingly high-stakes challenge, and yet many basic features of offline governance legacies-juries, political parties, term limits, and formal debates, to name a few-are not in the feature-sets of the software most community platforms use. Drawing on the paradigm of Institutional Analysis and Development, this paper proposes a strategy for addressing this lapse by specifying basic features of a generalizable paradigm for online governance called Modular Politics. Whereas classical governance typologies tend to present a choice among wholesale ideologies, such as democracy or oligarchy, Modular Politics would enable platform operators and their users to build bottom-up governance processes from computational components that are modular and composable, highly versatile in their expressiveness, portable from one context to another, and interoperable across platforms. This kind of approach could implement pre-digital governance systems as well as accelerate innovation in uniquely digital techniques. As diverse communities share and connect their components and data, governance could occur through a ubiquitous network layer. To that end, this paper proposes the development of an open standard for networked governance.", "Keywords": "governance; institutional analysis and development; interoperability; online communities; peer production; platforms; standards", "DOI": "10.1145/3449090", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 2, "Name": "Primavera De Filippi", "Affiliation": "Harvard University &amp; CNRS, Cambridge, MA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of California, Davis, <PERSON>, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Oxford, Oxford, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": [{"Title": "What We’ve built Is a computational language (and that’s very important!)", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101132", "JournalTitle": "Journal of Computational Science"}]}, {"ArticleId": 87831274, "Title": "AI-Mediated Communication", "Abstract": "AI-Mediated Communication (AI-MC) is interpersonal communication that involves an artificially intelligent system that can modify, augment, or even generate content to achieve communicative and relational goals. AI-MC is increasingly involved in human communication and has the potential to impact core aspects of human communication, such as language production, interpersonal perception and task performance. Through a between-subjects experimental design we examine how these processes are influenced when integrating AI-generated language in the form of suggested text responses (Google's smart replies) into a text-based referential communication task. Our study replicates and extends the impacts of a positivity bias in AI-generated language and introduces the adjacency pair framework into the study of AI-MC. We also find preliminary yet mixed evidence to suggest that AI-generated language has the potential to undermine some dimensions of interpersonal perception, such as social attraction. This study contributes important concepts for future work in AI-MC and offers findings with implications for the design of AI systems in human-to-human communication.", "Keywords": "ai-mediated communication; impression formation; linguistic alignment; sentiment; tasks", "DOI": "10.1145/3449091", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell Tech, Cornell University, New York, NY, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}], "References": [{"Title": "AI as a moral crumple zone: The effects of AI-mediated communication on attribution and trust", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "106190", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 87831275, "Title": "Exploring Lightweight Interventions at Posting Time to Reduce the Sharing of Misinformation on Social Media", "Abstract": "When users on social media share content without considering its veracity, they may unwittingly be spreading misinformation. In this work, we investigate the design of lightweight interventions that nudge users to assess the accuracy of information as they share it. Such assessment may deter users from posting misinformation in the first place, and their assessments may also provide useful guidance to friends aiming to assess those posts themselves.\n In support of lightweight assessment, we first develop a taxonomy of the reasons why people believe a news claim is or is not true; this taxonomy yields a checklist that can be used at posting time. We conduct evaluations to demonstrate that the checklist is an accurate and comprehensive encapsulation of people's free-response rationales.\n In a second experiment, we study the effects of three behavioral nudges---1) checkboxes indicating whether headings are accurate, 2) tagging reasons (from our taxonomy) that a post is accurate via a checklist and 3) providing free-text rationales for why a headline is or is not accurate---on people's intention of sharing the headline on social media. From an experiment with 1668 participants, we find that both providing accuracy assessment and rationale reduce the sharing of false content. They also reduce the sharing of true content, but to a lesser degree that yields an overall decrease in the fraction of shared content that is false.\n Our findings have implications for designing social media and news sharing platforms that draw from richer signals of content credibility contributed by users. In addition, our validated taxonomy can be used by platforms and researchers as a way to gather rationales in an easier fashion than free-response.", "Keywords": "behavioral nudges; misinformation; reasons why people believe news; social media", "DOI": "10.1145/3449092", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Regina, Regina, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}], "References": [{"Title": "Investigating Differences in Crowdsourced News Credibility Assessment", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831276, "Title": "Code of Conduct Conversations in Open Source Software Projects on Github", "Abstract": "The rapid growth of open source software necessitates a deeper understanding of moderation and governance methods currently used within these projects. The code of conduct, a set of rules articulating standard behavior and responsibilities for participation within a community, is becoming an increasingly common policy document in open source software projects for setting project norms of behavior and discouraging negative or harassing comments and conversation. This study describes the conversations around adopting and crafting a code of conduct as well as those utilizing code of conduct for community governance. We conduct a qualitative analysis of a random sample of GitHub issues that involve the code of conduct. We find that codes of conduct are used both proactively and reactively to govern community behavior in project issues. Oftentimes, the initial addition of a code of conduct does not involve much community participation and input. However, a controversial moderation act is capable of inciting mass community feedback and backlash. Project maintainers balance the tension between disciplining potentially offensive forms of speech and encouraging broad and inclusive participation. These results have implications for the design of inclusive and effective governance practices for open source software communities.", "Keywords": "collaboration; open source software", "DOI": "10.1145/3449093", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Texas at Austin, Austin, TX, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> Pandurang<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}], "References": []}, {"ArticleId": 87831277, "Title": "Shared Understanding in Care Coordination for Children's Behavioral Health", "Abstract": "Care coordination involves crossing boundaries to connect services in support of the health and well-being of an individual. In this paper, we describe how care coordination depends on the ability to develop a shared understanding of care goals and progress. A distributed group of professionals and non-professional caregivers need to share information to provide consistent and holistic support across settings. We conducted fieldwork comprising of 20 interviews and 51 hours of observation across three different programs focused on children's behavioral health. From this empirical investigation of practices used by distributed care teams, we generated a conceptual framework of shared understanding in care coordination. We identified barriers to shared understanding, as well as nine practices that contribute to its development via two key mechanisms: (1) building relationships across boundaries, and (2) sharing actionable information. We conclude with design implications for enhancing the collaborative practices of members of a care team to cross boundaries despite the barriers that are common in behavioral health and other contexts requiring complex care coordination.", "Keywords": "distributed care teams; information sharing", "DOI": "10.1145/3449095", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, GA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}], "References": []}, {"ArticleId": 87831278, "Title": "CrowdFolio", "Abstract": "Freelancers increasingly earn their livelihood through online marketplaces. To attract new clients, freelancers continuously curate their online portfolios to convey their unique skills and style. However, many lack access to rapid, regular, and inexpensive feedback needed to improve their portfolios. Existing crowd feedback systems, which collect feedback on individual creative projects (i.e., decomposed approach), could fill this need, but it is unclear how they might support feedback on multiple projects (i.e., holistic approach). In a between-subjects study with 30 freelancers, we compared decomposed and holistic feedback collection approaches using CrowdFolio, a crowd feedback system for portfolios. The holistic approach helped freelancers discover new ways to describe their work, while the decomposed approach provided detailed insight about the visual attractiveness of projects. This study contributes evidence that portfolio feedback systems, regardless of collection approach, can positively support professional development by impacting how freelancers portray themselves online and reflect on their identity.", "Keywords": "careers; crowdsourcing; feedback; freelancing; online professional identity; professional development", "DOI": "10.1145/3449096", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "Eureka Foong", "Affiliation": "University of Tokyo, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Adobe Research, San Francisco, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Adobe Research, Seattle, WA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}], "References": [{"Title": "Platformic Management, Boundary Resources for Gig Work, and Worker Autonomy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "1-2", "Page": "153", "JournalTitle": "Computer Supported Cooperative Work (CSCW)"}]}, {"ArticleId": 87831279, "Title": "Designing Sharing Economy Platforms through a 'Solidarity HCI' lens", "Abstract": "Despite sharing economy's promise of a novel, inclusive and community building socio-techno-economic system, sharing economy has been indicted, among others for profiteering from previously private and occasionally non-monetized activities, for turning the activity of sharing into an individualistic and impersonal one, for reproducing stereotypes and creating precarious jobs. In the epicenter of such critiques are the 'big' and 'limelight-ed' platform-firms, such as AirBnB and Uber and the digital infrastructures they employ. To the best of our knowledge, the majority of related research focuses on sharing economy platforms of this ilk. In response, acknowledging that the problem is not the agency of the digital in the activity of sharing per se, but that the wrong people set the terms, design and benefit from this mediation, we find it timely to explore the existence of community-driven sharing economy initiatives and explore how they use the digital to support their ends. As a result, in this paper we report from our engagement with a ride-sharing initiative, called 'Share the ride ;)' that has operated within a Facebook group since 2009 and is the most popular ride-sharing 'platform' in Greece. Extrapolating from our findings and while adopting a 'Solidarity HCI' approach, a call to design for 'human' rather than market needs, we participate in the 'sharing discourse' by providing design implications for the development of sharing economy platforms which can favor community building, participation, self-organization and the nurturing of a generative sharing ideology. To this end, we suggest the development of malleable sharing economy platforms and of mechanisms that can support the development of relational trust(s) and enduring social connections. Finally, we underscore that in order to favor the establishment of such relations, those platforms should employ architectures which esteem pluralism and self-affirmation.", "Keywords": "malleability; relational trust; ride-sharing; self-affirmation; sharing economy; solidarity hci", "DOI": "10.1145/3449097", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Newcastle University, Newcastle Upon Tyne, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Crete, Rethymno, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Newcastle University, Newcastle upon Tyne, United Kingdom"}], "References": []}, {"ArticleId": 87831280, "Title": "Plan Early, Revise <PERSON>", "Abstract": "Receiving feedback on preliminary work allows content creators to gain insight and improve outcomes. However, a lack of commitment for gathering feedback and experiencing evaluation apprehension can delay feedback seeking. In this paper, we operationalize goal setting theory for planning feedback goals and test the effects on feedback seeking and revision. In an online experiment, participants (N=245) wrote an initial story after planning feedback goals (or not), submitted the story for feedback at a time of their choice, and revised the story based on feedback received. Participants anticipated feedback from a supervisor or peer to induce different levels of evaluation apprehension. We found that participants who planned proximal feedback goals sought feedback when their stories were less developed and revised the stories more after receiving feedback compared to when participants planned distant goals. Additionally, participants who anticipated feedback from a supervisor, regardless of goal planning, improved the quality of their stories the most. We did not find that goal setting or the provider's role affected evaluation apprehension. Our findings indicate that content creators should be guided to plan proximal feedback goals to encourage sharing of early drafts of creative work and receive feedback from someone in a position of higher perceived power to foster the most revision and improvement on those drafts.", "Keywords": "creative work; evaluation apprehension; formative feedback; iteration; self-regulation", "DOI": "10.1145/3449098", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana Champaign, Urbana, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana Champaign, Urbana, IL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Illinois at Urbana Champaign, Urbana, IL, USA"}], "References": []}, {"ArticleId": 87831281, "Title": "Towards Supporting Data-Driven Practices in Stroke Telerehabilitation Technology", "Abstract": "Telerehabilitation technology has the potential to support the work of patients and clinicians by collecting and displaying patients' data to inform, motivate, and support decision-making. However, few studies have investigated data-driven practices in telerehabilitation. In this qualitative study, we conducted interviews and a focus group with the use of data visualization probes to investigate the experience of stroke survivors and healthcare providers with game-based telerehabilitation involving physical and occupational therapy. We find that \\hlstudy participants saw potential value in the data to support their work. However, they experienced challenges when interpreting data to arrive at meaningful insights and actionable information. Further, patients' personal relationships with their goals and data stand in contrast with clinicians' more matter-of-fact perspectives. Informed by these results, we discuss implications for telerehabilitation technology design.", "Keywords": "patient-generated data; stroke; telerehabilitation", "DOI": "10.1145/3449099", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Indiana University, Bloomington, Bloomington, IN, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Irvine, Irvine, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of California, Irvine, Irvine, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> R. <PERSON>. <PERSON>", "Affiliation": "Federal University of Pará, Belem, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of California, Los Angeles, Los Angeles, CA, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of California Irvine, Irvine, CA, USA"}], "References": []}, {"ArticleId": 87831282, "Title": "An Image of Society", "Abstract": "Algorithmically-mediated content is both a product and producer of dominant social narratives, and it has the potential to impact users' beliefs and behaviors. We present two studies on the content and impact of gender and racial representation in image search results for common occupations. In Study 1, we compare 2020 workforce gender and racial composition to that reflected in image search. We find evidence of underrepresentation on both dimensions: women are underrepresented in search at a rate of 42% women for a field with 50% women; people of color are underrepresented with 16% in search compared to an occupation with 22% people of color (the latter being proportional to the U.S. workforce). We also compare our gender representation data with that collected in 2015 by <PERSON> et al., finding little improvement in the last half-decade. In Study 2, we study people's impressions of occupations and sense of belonging in a given field when shown search results with different proportions of women and people of color. We find that both axes of representation as well as people's own racial and gender identities impact their experience of image search results. We conclude by emphasizing the need for designers and auditors of algorithms to consider the disparate impacts of algorithmic content on users of marginalized identities.", "Keywords": "algorithm audit; algorithmic bias; marginalized identities; search media", "DOI": "10.1145/3449100", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Stanford University, Stanford, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "McGill University, Montreal, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, CA, USA"}], "References": [{"Title": "I Can't Breathe", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831283, "Title": "My Bad! Repairing Intelligent Voice Assistant <PERSON><PERSON><PERSON> Improves Interaction", "Abstract": "One key technique people use in conversation and collaboration is conversational repair. Self-repair is the recognition and attempted correction of one's own mistakes. We investigate how the self-repair of errors by intelligent voice assistants affects user interaction. In a controlled human-participant study (N =101), participants asked Amazon Alexa to perform four tasks, and we manipulated whether <PERSON><PERSON> would \"make a mistake'' understanding the participant (for example, playing heavy metal in response to a request for relaxing music) and whether <PERSON><PERSON> would perform a correction (for example, stating, \"You don't seem pleased. Did I get that wrong?'') We measured the impact of self-repair on the participant's perception of the interaction in four conditions: correction (mistakes made and repair performed), undercorrection (mistakes made, no repair performed), overcorrection (no mistakes made, but repair performed), and control (no mistakes made, and no repair performed). Subsequently, we conducted free-response interviews with each participant about their interactions. This study finds that self-repair greatly improves people's assessment of an intelligent voice assistant if a mistake has been made, but can degrade assessment if no correction is needed. However, we find that the positive impact of self-repair in the wake of an error outweighs the negative impact of overcorrection. In addition, participants who recently experienced an error saw increased value in self-repair as a feature, regardless of whether they experienced a repair themselves.", "Keywords": "conversational design; error-recognition; intelligent voice assistants; self-repair", "DOI": "10.1145/3449101", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cornell Tech, New York, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tongji University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Cornell Tech, New York, NY, USA"}], "References": [{"Title": "Tell Me About Yourself", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}]}, {"ArticleId": 87831284, "Title": "Joint Action Storyboards", "Abstract": "Building and maintaining common ground is vital for effective collaboration in CSCW. Moreover, subtle changes in a CSCW user interface can significantly impact grounding and collaborative processes. Yet, researchers and technology designers lack tools to understand how specific user interface designs may hinder or facilitate communication grounding. In this work, we leverage the well-established theory of communication grounding to develop a visual framework, called Joint Action Storyboards (JASs), to analyze and articulate how interaction minutiae impact the costs of communication grounding. JASs can depict an integrated view of mental actions of collaborators, their physical interactions with each other and the CSCW environment, and the corresponding grounding costs incurred. We present the development of JASs and discuss its various benefits for HCI and CSCW research. Through a series of case studies, we demonstrate how JASs provide an analysis tool for researchers and technology designers and serve as a tool to articulate the impact of interaction minutiae on communication grounding.", "Keywords": "cscw; cscw frameworks, communication grounding; design evaluation methods, storyboards; grounding costs; groupware", "DOI": "10.1145/3449102", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Waterloo, Waterloo, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Waterloo, Waterloo, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Guelph, Guelph, ON, Canada"}], "References": []}, {"ArticleId": 87831285, "Title": "Tags, Borders, and Catalogs", "Abstract": "Through a computational reading of the online book reviewing community LibraryThing, we examine the dynamics of a collaborative tagging system and learn how its users refine and redefine literary genres. LibraryThing tags are overlapping and multi-dimensional, created in a shared space by thousands of users, including readers, bookstore owners, and librarians. A common understanding of genre is that it relates to the content of books, but this resource allows us to view genre as an intersection of user communities and reader values and interests. We explore different methods of computational genre measurement within the open space of user-created tags. We measure overlap between books, tags, and users, and we also measure the homogeneity of communities associated with genre tags and correlate this homogeneity with reviewing behavior.Finally, by analyzing the text of reviews, we identify the thematic signatures of genres on LibraryThing, revealing similarities and differences between them. These measurements are intended to elucidate the genre conceptions of the users, not, as in prior work, to normalize the tags or enforce a hierarchy. We find that LibraryThing users make sense of genre through a variety of values and expectations, many of which fall outside common definitions and understandings of genre.", "Keywords": "book reviews; natural language processing; tagging", "DOI": "10.1145/3449103", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}], "References": []}, {"ArticleId": 87831286, "Title": "Understanding the Telework Experience of People with Disabilities", "Abstract": "To understand the lived experience of how people with disabilities telework in the United States, 25 people were interviewed. The participants included people who are blind or low vision, deaf or hard of hearing, neurodiverse, have limited mobility/dexterity, and have chronic health issues. The interviews focused on how they used video calling, screen sharing, and collaborative editing technologies to accomplish their telework. The interviews found ways in which design choices made in telework technologies interact with people's abilities, especially those who are blind or low vision, since the tools rely heavily on the visual channel to enable remote collaboration. A central theme emerged around how design choices made in telework technologies affect the digital representation of people's online activities in the video call interface: those who turn off their video (because they are blind or do not want to expend the cognitive effort to present themselves over video) are relegated to a static icon on a blank video frame with their name while those who are deaf and speak silently through a sign language interpreter never show up in interfaces that use active speaker detection to choose which video streams to display. Users with disabilities may avoid using screen sharing and collaborative editing tools which \"leak\" cues that disclose their disabilities. Because the interviews were conducted during the first month of the COVID-19 pandemic response, they also provided a preview of how the sudden shift to pervasive teleworking affected their telework experience.", "Keywords": "collaboration technology; computer-mediated communication; people with disabilities; telework; video calling", "DOI": "10.1145/3449104", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Microsoft Research, Mountain View, CA, USA"}], "References": []}, {"ArticleId": 87831287, "Title": "Misfires, Missed Data, Misaligned Treatment", "Abstract": "Technology bears important relationships to our health and wellness and has been utilized over the past two decades as an aid to support both self-management goals as well as collaboration among treatment teams.However, when chronic illnesses such as eating disorders (ED) are managed outside of institutionalized care settings, designing effective technology to support collaboration in treatment necessitates that we understand the relationships between patients, clinicians, and support networks. We conducted in-depth, semi-structured, interviews with 9 ED patients and 10 clinicians to understand the ED journey through the lens of collaborative efforts, technology use, and potential detriments. Based on our analysis of these 19 interviews, we present novel findings on various underlying disconnects within the collaborative ED treatment process disconnects among clinicians, between treatment foci, among preferences in tracking, within support networks, and inpatients' own identities. Our findings highlight how these various disconnects are concomitant with and gaps can stem from a lack of collaboration between different stakeholders in the ED journey. We also identify methods of facilitating collaboration in these disconnects through technological mediators.", "Keywords": "collaboration; eating disorder; recovery; treatment", "DOI": "10.1145/3449105", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Lehigh University, Bethlehem, PA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, GA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, GA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Lehigh University, Bethlehem, PA, USA"}], "References": []}, {"ArticleId": 87831288, "Title": "The Effects of User Comments on Science News Engagement", "Abstract": "Online sources such as social media have become increasingly important for the proliferation of science news, and past research has shown that reading user-generated comments after an article (i.e. in typical online news and blog formats) can impact the way people perceive the article. However, recent studies have shown that people are likely to read the comments before the article itself, due to the affordances of platforms like Reddit which display them up-front. This leads to questions about how comments can affect people's expectations about an article, and how those expectations impact their interest in reading it at all. This paper presents two experimental studies to better understand how the presentation of comments on Reddit affects people's engagement with science news, testing potential mediators such as the expected difficulty and quality of the article. Study 1 provides experimental evidence that difficult comments can reduce people's interest in reading an associated article. Study 2 is a pre-registered follow-up that uncovers a similarity heuristic; the various qualities of a comment (difficulty, information quality, entertainment value) signal that the article will be of similar quality, ultimately affecting participants' interest in reading it. We conclude by discussing design implications for online science news communities.", "Keywords": "comments; r/science; reddit; science communication; science news", "DOI": "10.1145/3449106", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": []}, {"ArticleId": 87831289, "Title": "Understanding the Technological Practices and Needs of Music Therapists", "Abstract": "Music therapists provide critical, evidence-based care to a diverse range of clients. However, despite their active role in empowering individuals affected by disability, stigma, grief, and trauma, music therapists remain understudied by the HCI community. We present the results of a mixed methods study of 10 interviewees and 20 survey respondents in the U.S., all of whom are practicing music therapists. Our results show that music therapists engage in technology-aided practices such as making personalized connections with clients, assisting in identity formation, encouraging musicking (music-making), and preserving legacies. Results also show that music therapists face key challenges such as environmental, societal, and financial constraints, including high workload, lack of awareness of the value of music therapy among the general community, and limited access to secure technologies for remote client care. In light of these challenges, we present a set of design implications for creating future technologies for music therapists. This work diverges from previous studies on music therapy technologies, which focus largely on interventions with music therapy clients, by highlighting the often-neglected perspectives from music therapists.", "Keywords": "assistive technology; music technology; music therapist; music therapy; personalized technology", "DOI": "10.1145/3449107", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Virginia, Charlottesville, VA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Virginia, Charlottesville, VA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Min", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Virginia, Charlottesville, VA, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}], "References": []}, {"ArticleId": 87831290, "Title": "Understanding Wikipedia Practices Through Hindi, Urdu, and English Takes on an Evolving Regional Conflict", "Abstract": "Wikipedia is the product of thousands of editors working collaboratively to provide free and up-to-date encyclopedic information to the project's users. This article asks to what degree Wikipedia articles in three languages - Hindi, Urdu, and English - achieve Wikipedia's mission of making neutrally-presented, reliable information on a polarizing, controversial topic available to people around the globe. We chose the topic of the recent revocation of Article 370 of the Constitution of India, which, along with other recent events in and concerning the region of Jammu and Kashmir, has drawn attention to related articles on Wikipedia. This work focuses on the English Wikipedia, being the preeminent language edition of the project, as well as the Hindi and Urdu editions. Hindi and Urdu are the two standardized varieties of Hindustani, a lingua franca of Jammu and Kashmir. We analyzed page view and revision data for three Wikipedia articles to gauge popularity of the pages in our corpus, and responsiveness of editors to breaking news events and problematic edits. Additionally, we interviewed editors from all three language editions to learn about differences in editing processes and motivations, and we compared the text of the articles across languages as they appeared shortly after the revocation of Article 370. Across languages, we saw discrepancies in article tone, organization, and the information presented, as well as differences in how editors collaborate and communicate with one another. Nevertheless, in Hindi and Urdu, as well as English, editors predominantly try to adhere to the principle of neutral point of view (NPOV), and for the most part, the editors quash attempts by other editors to push political agendas.", "Keywords": "collaboration; jammu & kashmir; wikipedia", "DOI": "10.1145/3449108", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Virginia Polytechnic Institute and State University, Blacksburg, VA, USA"}, {"AuthorId": 2, "Name": "Viral Pasad", "Affiliation": "Virginia Polytechnic Institute and State University, Blacksburg, VA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Virginia Tech, Blacksburg, VA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Wisconsin - Madison, Madison, WI, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Virginia Polytechnic Institute and State University, Blacksburg, VA, USA"}], "References": []}, {"ArticleId": 87831291, "Title": "The Making of Women", "Abstract": "This paper investigates how making activities and participation in makerspaces supports the wellbeing and empowerment of women, particularly in making domains that are typically male-dominated. We spent six months undertaking participant observations in a women-only makerspace that runs workshops aimed at teaching women skills in using power tools and woodwork. We conducted contextual interviews with 12 workshop attendees as well as with the makerspace founder and lead instructor. Through the lens of feminist HCI and legitimate peripheral participation, we present trajectories of participation within a women-only makerspace - from beginning as a peripheral participant to becoming a competent and confident maker. We found that through structured workshops in a women-only space that actively teach making skills, the women-only makerspace works to transform the current makerspace landscape so more women can engage with these spaces and participate within them. We contribute three core qualities to foster participation: women-only but without a 'feminist' label, configuring a formal and collaborative learning environment, and reification through artefacts. Collectively these work towards new configurations of makerspaces for women that enable their participation within them, and we detail how such configurations work to create trajectories for women's participation.", "Keywords": "feminism; feminist hci; hackerspaces; makerspaces; making", "DOI": "10.1145/3449109", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Queensland University of Technology, Brisbane, QLD, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Queensland University of Technology, Brisbane, QLD, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Queensland University of Technology, Brisbane, QLD, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Independent Consultant, Adelaide, SA, Australia"}], "References": [{"Title": "\"The Personal is Political\"", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831292, "Title": "Evaluating MIDST, A System to Support Stigmergic Team Coordination", "Abstract": "Data science teams working on a shared analysis face coordination problems such as dividing up the work to be done, monitoring performance and integrating the pieces. Research on distributed software development teams has raised the potential of stigmergic coordination, that is, coordination through a shared work product in place of explicit communication. The MIDST system was developed to support stigmergic coordination by making individual contributions to a shared work product visible, legible and combinable. In this paper, we present initial studies of a total of 40 student teams (24 using MIDST) that shows that teams that used MIDST did experience the intended system affordances to support their work, did seem to coordinate at least in part stigmergically and performed better on an assigned project.", "Keywords": "awareness; data-science teams; stigmergic coordination; translucency", "DOI": "10.1145/3449110", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Syracuse University, Syracuse, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Syracuse University, Syracuse, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Syracuse University, Syracuse, NY, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tulane University, New Orleans, LA, USA"}], "References": []}, {"ArticleId": 87831293, "Title": "Examining Interactions Between Community Members and University Safety Organizations through Community-Sourced Risk Systems", "Abstract": "An increasing number of safety departments in organizations across the U.S. are offering mobile apps that allow their local community members to report potential risks, such as hazards, suspicious events, ongoing incidents, and crimes. These \"community-sourced risk'' systems are designed for the safety departments to take action to prevent or reduce the severity of situations that may harm the community. However, little is known about the actual use of such community-sourced risk systems from the perspective of both community members and the safety departments. This study is the first large-scale empirical analysis of community-sourced risk systems. More specifically, we conducted a comprehensive system log analysis of LiveSafe--a community-sourced risk system--that has been used by more than two hundred universities and colleges. Our findings revealed a mismatch between what the safety departments expected to receive and what their community members actually reported, and identified several factors (e.g., anonymity, organization, and tip type) that were associated with the safety departments' responses to their members' tips. Our findings provide design implications for chatbot-enabled community-risk systems and make practical contributions for safety organizations and practitioners to improve community engagement.", "Keywords": "community-sourced risk; community-sourcing; crowdsourcing; safety reporting", "DOI": "10.1145/3449111", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign, Champaign, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ", Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Independent Researcher, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign, Champaign, IL, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign, Champaign, IL, USA"}], "References": []}, {"ArticleId": 87831294, "Title": "Nowcasting Gentrification Using Airbnb Data", "Abstract": "There is a rumbling debate over the impact of gentrification: presumed gentrifiers have been the target of protests and attacks in some cities, while they have been welcome as generators of new jobs and taxes in others. Census data fails to measure neighborhood change in real-time since it is usually updated every ten years. This work shows that Airbnb data can be used to quantify and track neighborhood changes. Specifically, we consider both structured data (e.g., number of listings, number of reviews, listing information) and unstructured data (e.g., user-generated reviews processed with natural language processing and machine learning algorithms) for three major cities, New York City (US), Los Angeles (US), and Greater London (UK). We find that Airbnb data (especially its unstructured part) appears to nowcast neighborhood gentrification, measured as changes in housing affordability and demographics. Overall, our results suggest that user-generated data from online platforms can be used to create socioeconomic indices to complement traditional measures that are less granular, not in real-time, and more costly to obtain.", "Keywords": "airbnb; economics; gentrification; natural language processing; user-generated data", "DOI": "10.1145/3449112", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Southern California, Los Angeles, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Southern California, Los Angeles, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Middlesex University &amp; University of Turin, London, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "King's College &amp; Nokia Bell Labs, Cambridge, United Kingdom"}], "References": []}, {"ArticleId": 87831295, "Title": "You Keep Using That Word", "Abstract": "CSCW researchers have long inquired into the ways that identity informs, and is informed by, the design of technological systems. Gender is a regularly considered aspect of identity, with extensive work documenting and exploring gendered experiences and designs with the aim of addressing inequalities in, or through, design. Recent work has questioned the way that we conceptualise and \"measure\" gender, advocating more nuanced classificatory schemes to avoid silencing or obscuring trans and/or non-binary experiences. Building on and extending this research, our work examines how gender is conceptualised more broadly. Drawing from a range of theoretical perspectives in gender studies, feminist and postcolonial theory, we argue for the treatment of gender as \"multiplicitous\" when we conceptualise and interpret research, in order to avoid unintentionally perpetuating silencing and inequality even as we work to tackle it. Illustrating our argument with examples from both within and without CSCW, we suggest both new research directions for CSCW scholars inquiring into gender, and sensitising questions that scholars can use when constructing and evaluating studies.", "Keywords": "gender; methodological design; multiplicity", "DOI": "10.1145/3449113", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Johns Hopkins University, Baltimore, MD, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Johns Hopkins University, Baltimore, MD, USA"}], "References": [{"Title": "\"They Just Don't Get It\": Towards Social Technologies for Coping with Interpersonal Racism", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "How We've Taught Algorithms to See Identity: Constructing Race and Gender in Image Databases for Facial Analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Intersectionality in HCI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "5", "Page": "68", "JournalTitle": "interactions"}, {"Title": "A Seat at the Table", "Authors": "<PERSON><PERSON><PERSON>; India Irish", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "I Can't Breathe", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831296, "Title": "Street-Level Algorithms and AI in Bureaucratic Decision-Making", "Abstract": "Studies of algorithmic decision-making in Computer-Supported Cooperative Work (CSCW) and related fields of research increasingly recognize an analogy between AI and bureaucracies. We elaborate this link with an empirical study of AI in the context of decision-making in a street-level bureaucracy: job placement. The study examines caseworkers' perspectives on the use of AI, and contributes to an understanding of bureaucratic decision-making, with implications for integrating AI in caseworker systems. We report findings from a participatory workshop on AI with 35 caseworkers from different types of public services, followed up by interviews with five caseworkers specializing in job placement. The paper contributes an understanding of caseworkers' collaboration around documentation as a key aspect of bureaucratic decision-making practices. The collaborative aspects of casework are important to show because they are subject to process descriptions making case documentation prone for an individually focused AI with consequences for the future of how casework develops as a practice. Examining the collaborative aspects of caseworkers' documentation practices in the context of AI and (potentially) automation, our data show that caseworkers perceive AI as valuable when it can support their work towards management, (strengthen their cause, if a case requires extra resources), and towards unemployed individuals (strengthen their cause in relation to the individual's case when deciding on, and assigning a specific job placement program). We end by discussing steps to support cooperative aspects in AI decision-support systems that are increasingly implemented into the bureaucratic context of public services.", "Keywords": "algorithmic decision-making; bureaucracy; casework; job placement; public services; street-level algorithms", "DOI": "10.1145/3449114", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Copenhagen, Copenhagen, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Copenhagen, Copenhagen, Denmark"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Copenhagen, Copenhagen, Denmark"}], "References": [{"Title": "The Role of Discretion in the Age of Automation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "303", "JournalTitle": "Computer Supported Cooperative Work (CSCW)"}, {"Title": "\"We Would Never Write That Down\"", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831297, "Title": "\"We Even Borrowed Money From Our Neighbor\"", "Abstract": "Mobile-based scams are on the rise in emerging markets. However, the awareness about these scams and ways to avoid them remains limited among mobile users. We present a qualitative analysis of the dynamics of mobile-based fraud (specifically, SMS and call-based fraud) in Pakistan. We interviewed 96 participants, including different stakeholders in the mobile financial ecosystem: 71 victims of mobile-based scams, seven non-victims, 15 mobile money agents, and three officials from regulatory agencies that investigate mobile-based fraud. Leveraging the perspectives from these stakeholders and analyzing mobile-based fraud with a four-step social-engineering attack framework, we make four concrete contributions: First, we identify the nuances as well as specific tactics, methods, and resources that fraudsters use to scam mobile users. Second, we look at other actors, beyond the victim and the adversary, involved or affected by fraud and their roles at each step of the fraud process. Third, we discuss victims' understanding of mobile fraud, their behavior post-realization, and their attitudes toward reporting fraud. Finally, we discuss possible points of intervention and offer design recommendations to thwart mobile fraud, including addressing the vulnerabilities discovered in the ecosystem, utilizing existing actors to mitigate the consequences of these attacks, and realigning the design of fraud reporting mechanisms with the sociocultural practices.", "Keywords": "financial services; fraud; phishing; qualitative interviews; security; smartphone; sms-based fraud; social engineering; vishing", "DOI": "10.1145/3449115", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Washington &amp; Lahore Information Technology University, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Lahore University of Management Sciences, Lahore, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology University Lahore, Gujranwala, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Western Washington University, Bellingham, WA, USA"}], "References": []}, {"ArticleId": 87831298, "Title": "Safe Sexting", "Abstract": "The internet facilitates opportunities for adolescents to form relationships and explore their sexuality but seeking intimacy online has also become a stressor. As a result, adolescents often turn to the internet to seek support concerning issues related to sex because of its accessibility, interactivity, and anonymity. We analyzed 3,050 peer comments and 1,451 replies from adolescents (837 posts) who sought advice and/or support about online sexual experiences involving known others. We found peers mostly provided information and emotional support. They gave advice on how to handle negative online sexual experiences and mitigate their long-term repercussions, often based on their own negative experiences. They provided emotional support by letting teens know that they were not alone and should not blame themselves. A key implication of these findings is that these situations seemingly occurred regularly and youth were converging on a subset of norms about how to handle such situations in a way that supported one another. Yet, in some cases, they also resorted to victim-blaming or retaliating against those who broke these norms of \"safe\" sexting. Teens were grateful for emotional support and advice that helped them engage safely but were defensive when peers were critical of their relationships. Together, our findings suggest that youth are self-organizing to converge on guidelines and norms around safe sexting but have trouble framing their messages so that they are more readily accepted. In our paper, we contribute to the adolescent online safety literature by identifying youth-focused beliefs about safe sexting by analyzing the ways in which online peers give advice and support. We provide actionable recommendations for facilitating the exchange of positive advice and support via online peer-support platforms.", "Keywords": "adolescence; digital trace data; online safety; qualitative analysis; sexting; social support; teens", "DOI": "10.1145/3449116", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Central Florida &amp; University of Oulu, Orlando, FL, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Central Florida, Orlando, FL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Central Florida, Orlando, FL, USA"}], "References": []}, {"ArticleId": 87831299, "Title": "What is Spiritual Support and How Might It Impact the Design of Online Communities?", "Abstract": "Spirituality is an understudied topic in social computing; however, for Online Health Community (OHC) users facing life-threatening illness, it is of fundamental importance. Through in-depth focus groups with OHC stakeholders in a US context, we derive a definition of \"spiritual support\" for use by designers and researchers who study online social support. We show that spiritual support is an integral dimension that underlies other social support types, and that if we ignore spirituality in design, we fail to mitigate problematic issues that arise in online spaces when users' spiritual values clash. Based on participants' ideations, we provide design implications for OHCs and other social media to better facilitate spiritual support through: (1) representing spiritual beliefs, (2) assistance with supportive communication, (3) support network visualization and mobilization, and (4) advance care planning and digital legacy.", "Keywords": "caregiver; caringbridge; digital legacy; health; online health communities; patient; prayer support; religion; social support; spiritual support; spirituality; supportive communication; visualization", "DOI": "10.1145/3449117", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}], "References": [{"Title": "Experiences of Trust in Postmortem Profile Management", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Social Computing"}, {"Title": "\"I Cannot Do All of This Alone\"", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Understanding the Impact of COVID-19 on Online Mental Health Forums", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Management Information Systems"}]}, {"ArticleId": 87831300, "Title": "<PERSON><PERSON><PERSON>", "Abstract": "Bangladesh (a low-income country) has a significant number of people dependent on alms for daily survival. These people, who we address as extremely impoverished people (EIP) are deprived of even basic healthcare. Their extreme levels of poverty, coupled with low literacy skills, and complete lack of access to technology means that they are unaware of existing low-cost/free healthcare services (as arranged by local hospitals) available for EIPs. In this paper, we address this gap by means of a carefully-crafted solution, <PERSON><PERSON><PERSON> (a term in Bengali that translates to \"Doctor's Home'' in English), that is contextually tailored to enable healthcare access to impoverished people. Extracting critical insights from our field study with (N=70) EIPs, we create a pathway for availing lower-cost healthcare solutions using intermediaries for information dissemination. These intermediaries are small businesses that impoverished people visit often. We also conduct field studies with (N=71) intermediary partners and (N=10) hospitals to identify challenges and realities of such intermediary-based solutions. Based on our findings, we design, iteratively develop, deploy, and user-test our system in real cases and collect feedback from related stakeholders. Preliminary analysis on usage of our system (deployed at intermediaries) revealed 255 healthcare requests made by EIPs via our system in six months. We connect our finding to the broader interests of CSCW around contextualized intermediation, inclusive healthcare, and sustainability of deployed systems.", "Keywords": "bangladesh; hci4d; healthcare; ictd; infomediary; intermediary; low-resource; technology", "DOI": "10.1145/3449118", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Engineering and Technology, Dhaka, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bangladesh University of Engineering and Technology, Dhaka, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Engineering and Technology, Dhaka, Bangladesh"}, {"AuthorId": 4, "Name": "Noshin Ulfat", "Affiliation": "Bangladesh University of Engineering and Technology, Dhaka, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of South Florida, Tampa, FL, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Bangladesh University of Engineering and Technology, Dhaka, Bangladesh"}], "References": [{"Title": "Leveraging Free-Hand Sketches for Potential Screening of PTSD", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 87831301, "Title": "Owning and Sharing", "Abstract": "Intelligent personal assistants (IPA), such as Amazon Alexa and Google Assistant, are becoming increasingly present in multi-user households leading to questions about privacy and consent, particularly for those who do not directly own the device they interact with. When these devices are placed in shared spaces, every visitor and cohabitant becomes an indirect user, potentially leading to discomfort, misuse of services, or unintentional sharing of personal data. To better understand how owners and visitors perceive IPAs, we interviewed 10 in-house users (account owners and cohabitants) and 9 visitors from a student and young professionals sample who have interacted with such devices on various occasions. We find that cohabitants in shared households with regular IPA interactions see themselves as owners of the device, although not having the same controls as the account owner. Further, we determine the existence of a smart speaker etiquette which doubles as trust-based boundary management. Both in-house users and visitors demonstrate similar attitudes and concerns around data use, constant monitoring by the device, and the lack of transparency around device operations. We discuss interviewees' system understanding, concerns, and protection strategies and make recommendation to avoid tensions around shared devices.", "Keywords": "bystanders/visitors; multi-user; privacy perceptions; protection mechanisms; smart speakers; social rules; voice assistants", "DOI": "10.1145/3449119", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Edinburgh, Edinburgh, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Edinburgh, Edinburgh, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Edinburgh, Edinburgh, United Kingdom"}], "References": [{"Title": "He Is Just Like Me", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Smart Home Personal Assistants", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 87831302, "Title": "Does This Photo Make Me Look Good?", "Abstract": "In recent years, the use and importance of visual communication through photos have grown considerably. However, we have little understanding of the alignment between the intentions of the photo posters and the reactions of viewers. To address this gap, we replicated previous work that studied the alignment of poster and outsider judgments of text posts by extending it to photo posts. In our study of 573 users across four social media platforms, we found that outsiders generally judge photo posts more positively than anticipated by posters. Examining viewer engagement on social media revealed that photos depicting family and friends receive fewer reactions. We apply our insight to propose novel solutions that can help users create a more positive digital presence by aligning their photo posts with the expectations of their audiences.", "Keywords": "audience feedback; impression management; outsider judgment; photo posts; privacy; self-presentation; social media; visual communication", "DOI": "10.1145/3449120", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Denver, Denver, CO, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}], "References": [{"Title": "\"It's easier than causing confrontation\": Sanctioning Strategies to Maintain Social Norms and Privacy on Social Media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831303, "Title": "Contact Zones", "Abstract": "Our food system is a socio-material, heterogeneous infrastructure whose complexity and interconnectedness often remains invisible to citizens. While moments of crisis expose the vulnerabilities and injustices underlying this system, this paper seeks to explore which processes and tools CSCW could purposely design to 'open up' food infrastructures and bring young and adult people in contact with different aspects of the food system to cultivate food citizenship from a more-than-human perspective. Through a collaboration with a local primary school and four different food organisations (a mushroom grower, a vegetable farm, a bread-baking community centre, and a food bank) in North East England, UK, we designed 'contact zones' that enabled a class of students aged 7 to 8 years to encounter socio-material food practices at each partnering organisation's site and in the classroom. Our insights show young people's rich engagement in the socio-materiality of place, food, and practices; how encountering food practices across very different sites helped surface the interconnectedness of the food system; and how the contact zones opened spaces to practice food citizenship. The paper offers design implications towards infrastructuring more-than-human food pedagogies. It discusses inherent power dynamics of more-than-human design collaborations, critically evaluates the role of technology in more-than-human relations, and presents three design opportunities towards a relational understanding of food.", "Keywords": "citizenship; contact zones; critical pedagogy; human-food interaction; more-than-human; relationality; young people", "DOI": "10.1145/3449121", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Newcastle University, Newcastle upon Tyne, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Newcastle University, Newcastle upon Tyne, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northumbria University, Newcastle upon Tyne, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Newcastle University, Newcastle upon Tyne, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Newcastle University, Newcastle upon Tyne, United Kingdom"}], "References": []}, {"ArticleId": 87831304, "Title": "Leveling Up Teamwork in Esports", "Abstract": "A large body of research has underscored the importance of the cognitive process of team cognition and its relation to team performance. However, little research has focused on applying such an important teamwork process to computer-mediated collaboration within a fast-paced virtual environment. In this paper, we use esports as a research platform to address this limitation due to its fast-paced nature and its heavy reliance on teamwork. We report the experience and perceptions of 20 players with regard to their descriptions of team cognition within esports. We found that esports players relied on their game experience and understanding of role interdependencies in order to develop team cognition with strangers. We also found that experienced teams utilized a mutual understanding of teammate skills and personalities in order to predict responses and limit the verbal communication required to make quick team decisions. We contribute to CSCW by extending the cognitive understanding of computer-mediated collaboration and by advancing research on team cognition and how it can occur within a fast-paced virtual environment.", "Keywords": "computer-mediated collaboration; esports; shared mental model; shared understanding; team cognition", "DOI": "10.1145/3449123", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}], "References": [{"Title": "Simulations at Work —a Framework for Configuring Simulation Fidelity with Training Objectives", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "1-2", "Page": "85", "JournalTitle": "Computer Supported Cooperative Work (CSCW)"}]}, {"ArticleId": 87831305, "Title": "Networked Authoritarianism at the Edge", "Abstract": "This paper describes how village-level officials, relatively new to the Internet, use popular digital platforms on smartphones to supplement and extend long-standing patterns of information control and authoritarian power in rural Cambodia. They use these tools to monitor local affairs, report to the central government, and promote local government activities, practices which intimidate villagers and encourage their political withdrawal and self-censorship. This paper makes three contributions to the literature on networked authoritarianism and rural governance. First, technological changes currently underway in the Cambodian rural bureaucracy reflect a generational transition, as long-standing officials struggle to use new media easily or effectively, leading to new anxieties and breakdowns for these traditional holders of power. Second, bureaucratic information practices in these villages rely on material practices ranging from paper, face to face meetings, and loudspeakers, to new tools such as Facebook and smartphones - underlining significant continuities in mechanisms of bureaucratic power and control. Third, networked authoritarian practices conjure for villagers the historical links between information control and violence, and the effectiveness of these tactics on chilling speech is often rooted in villagers' memories of fear.", "Keywords": "authoritarianism; cambodia; governance; hci4d; history; privacy", "DOI": "10.1145/3449124", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Royal Holloway, University of London, London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Cornell Tech, New York, NY, USA"}], "References": []}, {"ArticleId": 87831306, "Title": "Practice-Based Teacher Questioning Strategy Training with ELK", "Abstract": "Practice is essential for learning. However, for many interpersonal skills, there often are not enough opportunities and venues for novices to repeatedly practice. Role-playing simulations offer a promising framework to advance practice-based professional training for complex communication skills, in fields such as teaching. In this work, we introduce ELK (Eliciting Learner Knowledge), a role-playing simulation system that helps K-12 teachers develop effective questioning strategies to elicit learners' prior knowledge. We evaluate ELK with 75 pre-service teachers through a mixed-method study. We find that teachers demonstrate a modest increase in effective questioning strategies and develop sympathy towards students after using ELK for 3 rounds. We implement a supplementary activity in ELK in which users evaluate transcripts generated from past role-play sessions. We have tentative evidence that a combination of role-play and evaluating conversation moves may be more effective for learning. We contribute design implications of using role-play systems for communication strategy training.", "Keywords": "communication strategy training; conversation; decomposition of practice; learning; role-play; scalable professional training; sympathy building; teacher education", "DOI": "10.1145/3449125", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}], "References": [{"Title": "Designing Alternative Representations of Confusion Matrices to Support Non-Expert Public Understanding of Algorithm Performance", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831307, "Title": "Remote, but Connected", "Abstract": "Data science practitioners face the challenge of continually honing their skills such as data wrangling and visualization. As data scientists seek online spaces to network, learn and share resources with one another, each individual has to employ their own ad-hoc strategy to practice their data science skills. Given these disjointed efforts, it is crucial to ask: how can we build an inclusive, welcoming online community of practice that unites data scientists in their collective efforts to become experts? Daily hashtags on Twitter are used on specific days and have shown promise in forming a community of practice (CoP) in social networking sites like Twitter, but how do they benefit the community and its members? To understand how daily hashtags benefit data scientists and form an online CoP, we conducted a qualitative study on #TidyTuesday---a daily hashtag project for data scientists using R---using the framework of CoP as a lens for analysis. We conducted semi-structured interviews with 26 participants and uncovered motivations behind their participation in #TidyTuesday, how the project benefited them, and how it cultivated an online CoP. Our findings contribute to the CSCW research on community of practices by providing design trade-offs of using daily hashtags on Twitter, and guidelines on growing and sustaining an online community of practice for data scientists.", "Keywords": "community of practice; data science; hashtag movements; online social movements", "DOI": "10.1145/3449126", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "North Carolina State University, Raleigh, NC, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Microsoft, Redmond, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "North Carolina State University, Raleigh, NC, USA"}], "References": [{"Title": "How do Data Science Workers Collaborate? Roles, Workflows, and Tools", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Data Analysts and Their Software Practices: A Profile of the Sabermetrics Community and Beyond", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831308, "Title": "When Forcing Collaboration is the Most Sensible Choice", "Abstract": "Individuals share increasing amounts of personal multimedia data, exposing themselves (uploaders) as well as others (data subjects). Non-consensual sharing of multimedia data that depicts others raises so-called multiparty privacy conflicts (MPCs), which can have severe consequences. To limit the incidence of MPCs, a family of Precautionary mechanisms have recently been developed that force uploaders to collaborate with the other data subjects to prevent MPCs. However, there is still very little work on understanding how users perceive the precautionary mechanisms together with which ones they prefer and why. In addition, precautionary mechanisms have some limitations, e.g., they require linking content to the co-owners' identity. Therefore, we also explore alternatives to precautionary mechanisms and propose a new class of solutions-Dissuasive mechanisms-that aim at deterring the uploaders from sharing without consent. We then present a user-centric comparison of precautionary and dissuasive mechanisms, through a large-scale survey (N = 1792; a representative sample of adult Internet users). Our results showed that respondents prefer precautionary to dissuasive mechanisms. These enforce collaboration, provide more control to the data subjects, but also they reduce uploaders' uncertainty around what is considered appropriate for sharing. We learned that threatening legal consequences is the most desirable dissuasive mechanism, and that respondents prefer the mechanisms that threaten users with immediate consequences (compared with delayed consequences). Dissuasive mechanisms are in fact well received by frequent sharers and older users, while precautionary mechanisms are preferred by women and younger users. We discuss the implications for design, including considerations about side leakages, consent collection, and censorship.", "Keywords": "dissuasive strategies; dual system theory; interdependent privacy; multiparty privacy conflicts; online social networks; privacy", "DOI": "10.1145/3449127", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Lausanne, Lausanne, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Lausanne, Lausanne, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Lausanne, Lausanne, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Lausanne, Lausanne, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "King's College London, London, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Lausanne, Lausanne, Switzerland"}], "References": [{"Title": "A Survey on Interdependent Privacy", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "\"It's easier than causing confrontation\": Sanctioning Strategies to Maintain Social Norms and Privacy on Social Media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Privacy Norms and Preferences for Photos Posted Online", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Privacy self-management and the issue of privacy externalities: of thwarted expectations, and harmful exploitation", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "1", "JournalTitle": "Internet Policy Review"}]}, {"ArticleId": 87831309, "Title": "Construction of Shared Situational Awareness in Traffic Management", "Abstract": "The construct of \"situational awareness\" (SA) has a rich and productive history within both academic literature and practice. Situational awareness as a technical term has its earliest roots in formative human factors research in service of military and flight applications. However, its value as a construct in other domains, particularly those having to do with rapid sensemaking in safety-sensitive conditions, has led to a broader applied and theoretical interest over the past few decades. As a discipline, CSCW has been relatively less engaged with this concept, but has empirical and theoretical tools that will be valuable to its study. To bring CSCW more fully into the conversation, we present a description of how operators in a city department of transportation's transportation management center (TMC) develop and maintain situational awareness for themselves and the key recipients of their critical information outputs. We identify some of the schemas operators must develop in order to effectively construct situational awareness and dynamically articulate common fields of work, and the social collaborative practices they engage in to support that awareness. Implications for design and further research are proposed.", "Keywords": "common field of work; control rooms; distributed cognition; situational awareness; transportation", "DOI": "10.1145/3449128", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": []}, {"ArticleId": 87831310, "Title": "Wikipedia Beyond the English Language Edition", "Abstract": "Do models of collaboration among contributors of Wikipedia generalize beyond the larger, western editions of the encyclopedia? In this study, we expanded upon the known collaborative mechanisms on the English Wikipedia and demonstrated that the collaboration model is best captured through the interplay of these mechanisms. We annotated talk page conversations for types of power plays or vies for control over edits that are made to articles, to understand how policy and power play mechanisms in editors' discussions account for behavior in English (EN), Farsi (FA), and Chinese (ZH) language editions of Wikipedia. Our findings show that the same power plays used in EN exist in both FA and ZH but the frequency of their usage differs across the editions. These variations suggest that editors in different language communities value contrasting types of policies to compete for power while discussing and editing articles. Our study contributes to a deeper understanding of how collaboration models developed from a western perspective translate to non-western languages.", "Keywords": "chinese; collaboration; english; farsi; language; qualitative analysis; wikipedia", "DOI": "10.1145/3449129", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": []}, {"ArticleId": ********, "Title": "Effects of Algorithmic Flagging on Fairness", "Abstract": "Online community moderators often rely on social signals such as whether or not a user has an account or a profile page as clues that users may cause problems. Reliance on these clues can lead to \"overprofiling'' bias when moderators focus on these signals but overlook the misbehavior of others. We propose that algorithmic flagging systems deployed to improve the efficiency of moderation work can also make moderation actions more fair to these users by reducing reliance on social signals and making norm violations by everyone else more visible. We analyze moderator behavior in Wikipedia as mediated by RCFilters, a system which displays social signals and algorithmic flags, and estimate the causal effect of being flagged on moderator actions. We show that algorithmically flagged edits are reverted more often, especially those by established editors with positive social signals, and that flagging decreases the likelihood that moderation actions will be undone. Our results suggest that algorithmic flagging systems can lead to increased fairness in some contexts but that the relationship is complex and contingent.", "Keywords": "ai; causal inference; community norms; fairness; machine learning; moderation; online communities; peer production; sociotechnical systems; wikipedia", "DOI": "10.1145/3449130", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Microsoft, Redmond, WA, USA"}], "References": [{"Title": "ORES", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831312, "Title": "Dis\"Like\"", "Abstract": "The social diversification hypothesis (SDH) examines the benefits of internet communication for disadvantaged individuals. In a novel application, we use it to explore the perceived costs of internet use, particularly Facebook across two different analyses. First, in-depth interviews with 45 socio-economically and racially diverse participants from a midwestern college town revealed that People of Color and those without 4-year degree were more suspicious of Facebook and more likely to have been the victim of an online scam. In contrast, Whites and those with four-year degrees were often overwhelmed by, but resigned to using, Facebook, expressing instead frustration with the perceived socio-emotional limitations of communicating online. Qualitative findings were then triangulated with a 2018 Pew survey of 2,002 U.S. adults: disadvantaged groups perceive greater social and personal costs of being online while advantaged groups are more dependent on the Internet. Findings suggest that the costs of internet use vary by access to social capital. In addition to a novel theoretical application of the SDH, we discuss the need for demographic-specific digital literacy design and corporate policy changes that can help mitigate these costs.", "Keywords": "facebook; income; non-use; race; social capital; social diversification; social media", "DOI": "10.1145/3449131", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of California, Santa Barbara, Santa Barbara, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of California, Santa Barbara, Santa Barbara, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of California, Santa Barbara, Santa Barbara, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Indiana University Bloomington, Bloomington, IN, USA"}], "References": []}, {"ArticleId": 87831313, "Title": "StreamSketch", "Abstract": "Creative live streams, where artists or designers demonstrate their creative process, have emerged as a unique and popular genre of live streams due to the real-time interactivity they afford. However, streamer-viewer interactions on most live streaming platforms only enable users to utilize text and emojis to communicate, which limits what viewers can convey and share in real time. To investigate the design space of potential visual and non-textual modalities within creative live streams, we first analyzed existing Twitch extensions and conducted a formative study with streamers who share creative activities to uncover key challenges that these streamers face. We then designed and implemented a prototype system, StreamSketch, which enables viewers and streamers to interact during live streams using multiple modalities, including freeform sketches and text. The prototype was evaluated by two professional artist streamers and their viewers during six streaming sessions. Overall, streamers and viewers found that StreamSketch provided increased engagement and new affordances compared to the traditional text-only modality, and highlighted how efficiency, moderation, and tool integration were continued challenges.", "Keywords": "creativity; live streaming; sketching; user engagement; visualization", "DOI": "10.1145/3449132", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Toronto, Toronto, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Adobe Research, Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Adobe Research, San Jose, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Adobe Research, Seattle, WA, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign, Urbana, IL, USA"}], "References": [{"Title": "Multimodal interfaces and communication cues for remote collaboration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "4", "Page": "313", "JournalTitle": "Journal on Multimodal User Interfaces"}]}, {"ArticleId": 87831314, "Title": "Belonging There", "Abstract": "The world is entering a new normal of hybrid organisations, in which it will be common that some members are co-located and others are remote. Hybridity is rife with asymmetries that affect our sense of belonging in an organisational space. This paper reports a study of an XR Telepresence technology probe to explore how remote workers might present themselves and be perceived as an equal and unique embodied being in a workplace. VROOM (Virtual Robot Overlay for Online Meetings) augments a standard Mobile Robotic Telepresence experience by (1) adding a virtual avatar overlay of the remote person to the local space, viewable through a HoloLens worn by the local user, through which the remote user can gesture and express themselves, and (2) giving the remote user an immersive 360° view of the local space, captured by a 360° camera on the robot, which they can view through a VR headset. We ran a study to understand how pairs of participants (one local and one remote) collaborate using VROOM in a search and word-guessing game. Our findings illustrate that there is much potential for a system like VROOM to support dynamic collaborative activities in which embodiment, gesturing, mobility, spatial awareness, and non-verbal expressions are important. However, there are also challenges to be addressed, specifically around proprioception, the mixing of a physical robot body with a virtual human avatar, uncertainties of others' views and capabilities, fidelity of expressions, and the appearance of the avatar. We conclude with further design suggestions and recommendations for future work.", "Keywords": "augmented reality; avatar; awareness; hybrid meetings; mixed reality; remote collaboration; remote embodiment; telepresence; video-mediated communication; virtual reality", "DOI": "10.1145/3449133", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Calgary, Calgary, AB, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Vancouver, Vancouver, BC, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University College London, London, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Microsoft Research Cambridge, Cambridge, United Kingdom"}], "References": [{"Title": "The Rocketbox Library and the Utility of Freely Available Rigged Avatars", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "20", "JournalTitle": "Frontiers in Virtual Reality"}]}, {"ArticleId": 87831315, "Title": "Understanding the Education of Children with Autism in Bangladesh", "Abstract": "This paper aims to understand the educational settings of children with autism (CWA) in the specialized schools of Bangladesh from their parents' perspective. Sparse research in the same context to understand teachers' perceptions has been found. Though parents and teachers are an integral part of these children's educational experience, limited research has been conducted to deeply understand and facilitate parent-teacher relationships to offer CWA an improved learning experience in Bangladesh. Our in-depth qualitative study on urban parents (N=10) showed current challenges and opportunities relating to the schools designed for CWA. The work takes a closer look at the existing concerns and confirms that parent-teacher communication in autism schools here is a complex one because of other interferences (e.g., social, and familial stigma, lack of transparency from administrators, insufficient qualified teachers) as felt by the parents. Nevertheless, the remarkable coverage of cellular networks and increasing usage of mobile phones in Bangladesh places us in a unique position to expect positive changes by employing ICT, which we believe will open avenues for future researchers and guide them in implementing robust technology to support an improved learning environment for the CWA and their families.", "Keywords": "autism and parents; autism in bangladesh; ict; special education", "DOI": "10.1145/3449134", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "North South University, Dhaka, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "North South University, Dhaka, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "North South University, Dhaka, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "North South University, Dhaka, Bangladesh"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Western Washington University, Bellingham, WA, USA"}], "References": []}, {"ArticleId": 87831316, "Title": "Network Capacity as Common Pool Resource", "Abstract": "Congestion control mechanisms, by which network users share constrained capacity on Internet links, are heavily studied in computer science. Such mechanisms are traditionally automated, assuming that users do not wish to be involved in addressing congestion. However, in community-owned and operated networks, users have control over daily operational choices. We explore the design of community-based congestion policies and mechanisms, through the lens of network capacity as a Common Pool Resource (CPR).\n Through a series of workshops and interviews in a rural community in Oaxaca, Mexico, we encounter design opportunities for new types of tools supporting communal network management. Participants expressed desires for preserving individual privacy while collecting longitudinal data to track the network's impact on the community, prioritization of high-value applications, equal link sharing between users, and human-mediated congestion management in lieu of automated enforcement. We report qualitative insights and offer design directions for future systems to address network resources in a manner compatible with <PERSON>strom's principles for CPR governance.", "Keywords": "community networks; field study; hci for development; infrastructure; rural areas", "DOI": "10.1145/3449135", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": []}, {"ArticleId": 87831317, "Title": "Detecting Expressive Writing in Online Health Communities by Modeling Aggregated Empirical Data", "Abstract": "Although expressive writing has proved to be beneficial on physical, mental, and social health of individuals, it has been restrained to lab-based experimental studies. In the real world, individuals may naturally engage with expressive writing when dealing with difficult times, especially when facing a tough health journey. Health blogging may serve as an easy-to-access method for self-therapy, if spontaneous expressive writing occurs. However, many posts may not be expressive enough to provide the therapeutic power. In this study, we build a Gaussian naive Bayes model to detect expressive writing in an online health community, CaringBridge. Because we lack full text data as training data, we use a method to learn model parameters from meta-analysis of the literature. The classifier reaches reasonable accuracy on the test set annotated by the authors. We also explore factors that may influence users' spontaneous expressive writing. We find gender, health condition, author type, and privacy settings can affect individuals' spontaneous expressive writing. Finally, we reflect on our methodology and results and provide design implications for online health communities.", "Keywords": "aggregated data; caringbridge; classification; expressive writing; health blogging; meta-analysis; online health community; published result; review; survey", "DOI": "10.1145/3449136", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "Haiwei <PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}], "References": []}, {"ArticleId": 87831318, "Title": "Lost in Co-curation", "Abstract": "Online tools enable users to co-create artifacts remotely. However, creative collaborations can also occur for the social process of collaboration itself, for which measures of success and engagement expectations can be more ambiguous, and individuals' dedication and social dynamics more important. Co-curating music in collaborative playlists (CPs) is one example of creative collaboration that encompasses both roles, and can therefore have more subtleties within its interactions. We conducted two studies using online surveys to understand perceived comfort with and hesitation toward the social dynamics embedded in CPs. Differences in collaborators' ownership perceptions toward CPs and their comfort in interacting with these CPs emerged. We also found a varying desire for situated communication, dependent upon the action taken and perceived ownership (of both a CP and the songs contained), with more users expecting greater comfort when a communication channel exists. From these results, we present four design considerations for more positive and engaging experiences in creative online co-curation.", "Keywords": "collaboration discomfort; collaborative co-curation; communication channel; hesitation; music playlist; online collaboration; ownership; questionnaire; survey", "DOI": "10.1145/3449137", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "So Yeon Park", "Affiliation": "Stanford University, Stanford, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Virginia Polytechnic Institute and State University, Blacksburg, VA, USA"}], "References": [{"Title": "Social Music Curation That Works", "Authors": "So Yeon Park; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831319, "Title": "Delivery Work and the Experience of Social Isolation", "Abstract": "The isolating nature of platform-based work, particularly gig work involving deliveries, has created unintended consequences over how workers engage with peers, friends, family, and society in general. We performed a qualitative study involving interviews with 21 delivery workers in Bangalore, India to understand how workers experienced and responded to social isolation perpetuated by the nature and daily function of their work. We found that the stigma and individual nature of app-based delivery work restricts access to inter-relational and instrumental support. As a response, workers organized peer networks for both companionship and emergency assistance. We analyze how the cultural context of India heightens these experiences, and offer ideas for mitigating the risks of isolation as a result of gig work.", "Keywords": "delivery work; gig work; global south; india; isolation", "DOI": "10.1145/3449138", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Independent Researcher, Bangalore, Karnataka, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Microsoft Research India, Bangalore, Karnataka, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}], "References": [{"Title": "Watched, but Moving", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831320, "Title": "A Transformer-based Framework for Neutralizing and Reversing the Political Polarity of News Articles", "Abstract": "People often prefer to consume news with similar political predispositions and access like-minded news articles, which aggravates polarized clusters known as \"echo chamber\". To mitigate this phenomenon, we propose a computer-aided solution to help combat extreme political polarization. Specifically, we present a framework for reversing or neutralizing the political polarity of news headlines and articles. The framework leverages the attention mechanism of a Transformer-based language model to first identify polar sentences, and then either flip the polarity to the neutral or to the opposite through a GAN network. Tested on the same benchmark dataset, our framework achieves a 3%-10% improvement on the flipping/neutralizing success rate of headlines compared with the current state-of-the-art model. Adding to prior literature, our framework not only flips the polarity of headlines but also extends the task of polarity flipping to full-length articles. Human evaluation results show that our model successfully neutralizes or reverses the polarity of news without reducing readability. We release a large annotated dataset that includes both news headlines and full-length articles with polarity labels and meta-data to be used for future research. Our framework has a potential to be used by social scientists, content creators and content consumers in the real world.", "Keywords": "automatic polarity transfer; echo chamber; journalism; neural networks; political polarization; selective exposure; transformer-based models", "DOI": "10.1145/3449139", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dartmouth College, Hanover, NH, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Texas at Austin, Austin, TX, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dartmouth College, Hanover, NH, USA"}], "References": []}, {"ArticleId": 87831321, "Title": "StarryThoughts", "Abstract": "Enabling the free and equal exchange of arguments on social issues in a respectful manner is an integral part of establishing the democratic ideal. However, the current manifestation of online spaces tends to facilitate the gathering of like-minded people, leading to the polarization of opinions. Such polarization inhibits the sharing of diverse opinions and deteriorates respect for disagreeing opinions. To tackle this issue, we present StarryThoughts, an online system that supports users to express and explore diverse perspectives on social issues. The system supports three types of exploration of the collected arguments online: navigating opinions based on the demographic identities of the posters, checking the the stereotypes users hold towards demographics in relation to given social issues, and engaging with opinions with semantically different point-of-views. By deploying the system to the public in co-operation with a nationwide broadcasting company, we collected 1,950 opinions with 144 free-form responses from 1,209 visitors as initial data and iterated on the design. Results from a user study with 56 participants showed that the system enables participants to explore a wide range of opinions on social issues, be more informed on the various arguments, and be more confident about their opinions. From our findings, we provide several design considerations for building online systems for supporting users to explore diverse opinions on social issues.", "Keywords": "identities; opinion exploration; polarization; public sphere", "DOI": "10.1145/3449140", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "KAIST, Daejeon, Republic of Korea"}], "References": []}, {"ArticleId": 87831322, "Title": "You Recommend, I Buy", "Abstract": "As an emerging business phenomenon especially in China, instant messaging (IM) based social commerce is growing increasingly popular, attracting hundreds of millions of users and is becoming one important way where people make everyday purchases. Such platforms embed shopping experiences within IM apps, e.g., WeChat, WhatsApp, where real-world friends post and recommend products from the platforms in IM group chats and quite often form lasting recommending/buying relationships. How and why do users engage in IM based social commerce? Do such platforms create novel experiences that are distinct from prior commerce? And do these platforms bring changes to user social lives and relationships? To shed light on these questions, we launched a qualitative study where we carried out semi-structured interviews on 12 instant messaging based social commerce users in China. We showed that IM based social commerce: 1) enables more reachable, cost-reducing, and immersive user shopping experience, 2) shapes user decision-making process in shopping through pre-existing social relationship, mutual trust, shared identity, and community norm, and 3) creates novel social interactions, which can contribute to new tie formation while maintaining existing social relationships. We demonstrate that all these unique aspects link closely to the characteristics of IM platforms, as well as the coupling of user social and economic lives under such business model. Our study provides important research and design implications for social commerce, and decentralized, trusted socio-technical systems in general.", "Keywords": "manual recommendation; reachability; social commerce; social relationship; user experience; word of mouth", "DOI": "10.1145/3449141", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "Hancheng Cao", "Affiliation": "Stanford University, Stanford, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Harvard Business School, Cambridge, MA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Kyoto University, Kyoto, Japan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Tsinghua University, Beijing, China"}], "References": [{"Title": "My Team Will Go On", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831323, "Title": "Do Group Memberships Online Protect Addicts in Recovery Against Relapse?", "Abstract": "The Social Identity Model of Recovery (SIMOR) suggests that addiction recovery is a journey through time where membership in various groups facilitates success. With the help of computational approaches, we now have access to new resources to study whether a wide variety of different online communities can be part of the addiction recovery journey. In this work, we study the effects of two main social factors on recovery success: first, multiple group membership defined in terms of richness of online community engagement; second, active participation operationalized as the evenness in engagement with these groups. We then model recovery from addiction by applying the extended Cox regression model which accounts for the effect of these two factors on time to relapse. We applied our analysis to a dataset of 457 recovering opioid addicts that self-announced the date of their recovery, indicating that at least 219 (48%) addicts relapsed during the recovery period. We find that multiple group membership - in terms of the number of other forums that a subject had posted in - as well as active participation - in terms of how evenly their posts were spread amongst the different forums - reduced the risk of relapse. We discuss our findings with regards to the opportunity, but also risk, that online group membership poses for recovering opioid addicts, as well as the possible contribution that computational social science methods can make to the study of addiction and recovery.", "Keywords": "addiction recovery; online communities; social cure; survival analysis", "DOI": "10.1145/3449142", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Exeter, Exeter, Devon, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Exeter, Exeter, Devon, United Kingdom"}], "References": []}, {"ArticleId": 87831324, "Title": "Interdependence in Action", "Abstract": "Prior work on AI-enabled assistive technology (AT) for people with visual impairments (VI) has treated navigation largely as an independent activity. Consequently, much effort has focused on providing individual users with wayfinding details about the environment, including information on distances, proximity, obstacles, and landmarks. However, independence is also achieved by people with VI through interacting with others, such as in collaboration with sighted guides. Drawing on the concept of interdependence, this research presents a systematic analysis of sighted guiding partnerships. Using interaction analysis as our primary mode of data analysis, we conducted an empirical, qualitative study with 4 couples, each made up of person with a vision impairment and their sighted guide. Our results show how pairs used interactional resources such as turn-taking and body movements to both co-constitute a common space for navigation, and repair moments of rupture to this space. This work is used to present an exemplary case of interdependence and draws out implications for designing AI-enabled AT that shifts the emphasis away from independent navigation, and towards the carefully coordinated actions between people navigating together.", "Keywords": "ai; assistive technology; interaction analysis; interdependence; sighted guiding; vision impairment", "DOI": "10.1145/3449143", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "City, University of London, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "City, University of London, London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "City, University of London, London, United Kingdom"}], "References": []}, {"ArticleId": 87831325, "Title": "\"That's dastardly ingenious\"", "Abstract": "Scholars have previously described how online communities engage in particular discourses and forms of argumentation. In parallel, HCI and STS researchers have described discourses surrounding ethics and values and their role in shaping design processes and outcomes. However, little work has addressed the intersection of ethical concern and the discourses of non-expert users. In this paper, we describe the argumentation strategies used by Redditors on the subreddit 'r/assholedesign' as they discuss ethically problematic design artifacts. We used content and sequence analysis methods to identify the building blocks of ethical argumentation in this online community, including ethical positioning when raising issues of concern, identification of potential remedies to the original design artifact or issues of concern, and means of extending or negating these elements. Through this analysis, we reveal the breadth of ethical argumentation strategies used \"in-the-wild\" by non-experts, resulting in an increased awareness of the capacity of community members to engage in \"everyday ethics\" regardless of specific ethics training. We describe future opportunities to connect these ethical argumentation strategies with design practices, education, and methods.", "Keywords": "ethical argumentation; ethics; reddit; values", "DOI": "10.1145/3449144", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, IN, USA"}], "References": []}, {"ArticleId": 87831326, "Title": "Detection of Social Identification in Workgroups from a Passively-sensed WiFi Infrastructure", "Abstract": "Social identification: how much individuals psychologically associate themselves with a group has been posited as an essential construct to measure individual and group dynamics. Studies have shown that individuals who identify very differently from their workgroup provide critical cues to the lack of social support or work overloads. However, measuring identification is typically achieved through time-consuming and privacy-invasive surveys. We hypothesize that the extremities in-group norm affects individuals' behaviors, thus more likely to give rise to negative appraisals. As a more convenient and less-invasive technique, we propose a method to predict individuals who are increasingly different in identifying themselves with their working peers using mobility data passively sensed from the WiFi infrastructure. To test our hypothesis, we collected WiFi data of 62 college students over a whole semester. Students provided regular self-reports on their identification towards a workgroup as ground truth. We analyze the contrasts between groups' mobility patterns and build a classification model to determine students who identify very differently from their workgroup. The classifier achieves approximately 80% True Positive Rate (TPR), 73% True negative rate (TNR), and 78% Accuracy (ACC). Such a mechanism can help distinguish students who are more likely to struggle with negative workgroup appraisals and enable interventions to improve their overall team experience.", "Keywords": "education; mobility; social identification; wifi; workgroup", "DOI": "10.1145/3449145", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Singapore Management University, Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Singapore Management University, Singapore, Singapore"}], "References": []}, {"ArticleId": 87831327, "Title": "On the Use of Multi-sensory Cues in Symmetric and Asymmetric Shared Collaborative Virtual Spaces", "Abstract": "Physical face-to-face collaboration with someone gives a higher-quality experience compared to mediated communication options, such as a phone- or video-based chat. Participants can share rich sensory cues to multiple human senses in a physical space. Also, the perceptual sensing of the surrounding environment including other peoples' reactions can influence human communication and emotion, and thus collaborative performance. Shared spaces in virtual environments provide degraded sensory experiences because most commercial virtual reality systems typically provide only visual and audio feedback. The impact of richer, multi-sensory feedback on joint decision-making tasks in VR is still an open area of research. Two independent studies exploring this topic are presented in this paper. We implemented a multi-sensory system that delivers vision, audio, tactile, and smell feedback, and we compared the system to a typical VR system. The scenario placed two users in a virtual theme-park safari ride with a number of non-player character (NPC) passengers to simulate realistic scenarios compared to the real-world and we varied the type and complexity of NPCs reactions to participants.\n In Experiment 1, we provided both users with either multi-sensory or typical sensory feedback symmetrically as a between-subjects factor, and used NPC reaction type as a within-subjects factor. In Experiment 2, we provided sensory feedback asymmetrically to each user (i.e., one had multi-sensory cues and the other had typical sensory cues) as a between-subjects factor, and used NPC reaction type as a within-subjects factor. We found that the number of sensory channels and NPC reactions did not influence user perception significantly under either symmetric or asymmetric sensory feedback conditions. However, after accounting for individual personality traits (e.g., assertive, passive), as well as any existing relationship between the pairs, we found that increasing the number of sensory channels can significantly improve subjective responses.", "Keywords": "asymmetry; co-presence; cognition; communication and collaboration in vr; immersion; multi-sensory vr; multisensory; perception; presence; shared space vr; social presence; symmetry", "DOI": "10.1145/3449146", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Canterbury, Christchurch, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Canterbury, Christchurch, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Twente, Enschede, Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Canterbury, Christchurch, New Zealand"}], "References": []}, {"ArticleId": 87831328, "Title": "The Work of Workplace Disclosure", "Abstract": "Health disclosure at work is complicated for people with invisible chronic conditions. Due to the lack of visible symptoms, invisible conditions affect the work life of people in ways that are not obvious to others. This study examines how people disclose and conceal their conditions in the workplace and opens the design space for this topic. In the first phase, we analyzed posts on two subreddit forums, r/migraine and r/fibromyalgia, and found a range of strategies that individuals use to disclose or conceal their conditions. In the second phase, we created five technological design concepts based on these strategies that were shown to eight people with migraines or fibromyalgia in semi-structured interviews. Based on these phases, we contribute understandings of disclosure and concealment of invisible conditions in the workplace for future research, such as potential areas for intervention ranging from individual to societal level efforts, as well as the potential and limitations of relying on empathy from others.", "Keywords": "accessibility; chronic conditions; disability; empathy; fibromyalgia; invisible conditions; migraine; workplace", "DOI": "10.1145/3449147", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Maryland, College Park, MD, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Maryland, College Park, MD, USA"}], "References": []}, {"ArticleId": 87831329, "Title": "Problematic Machine Behavior", "Abstract": "While algorithm audits are growing rapidly in commonality and public importance, relatively little scholarly work has gone toward synthesizing prior work and strategizing future research in the area. This systematic literature review aims to do just that, following PRISMA guidelines in a review of over 500 English articles that yielded 62 algorithm audit studies. The studies are synthesized and organized primarily by behavior (discrimination, distortion, exploitation, and misjudgement), with codes also provided for domain (e.g. search, vision, advertising, etc.), organization (e.g. Google, Facebook, Amazon, etc.), and audit method (e.g. sock puppet, direct scrape, crowdsourcing, etc.). The review shows how previous audit studies have exposed public-facing algorithms exhibiting problematic behavior, such as search algorithms culpable of distortion and advertising algorithms culpable of discrimination. Based on the studies reviewed, it also suggests some behaviors (e.g. discrimination on the basis of intersectional identities), domains (e.g. advertising algorithms), methods (e.g. code auditing), and organizations (e.g. Twitter, TikTok, LinkedIn) that call for future audit attention. The paper concludes by offering the common ingredients of successful audits, and discussing algorithm auditing in the context of broader research working toward algorithmic justice.", "Keywords": "algorithm auditing; algorithmic accountability; algorithmic authority; algorithmic bias; algorithmic harm; critical algorithm studies; ethics; literature review; policy", "DOI": "10.1145/3449148", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}], "References": [{"Title": "Measuring Misinformation in Video Search Platforms: An Audit Study on YouTube", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "3 Stars on Yelp, 4 Stars on Google Maps", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831330, "Title": "Prior and Prejudice", "Abstract": "Modern machine learning and computer science conferences are experiencing a surge in the number of submissions that challenges the quality of peer review as the number of competent reviewers is growing at a much slower rate. To curb this trend and reduce the burden on reviewers, several conferences have started encouraging or even requiring authors to declare the previous submission history of their papers. Such initiatives have been met with skepticism among authors, who raise the concern about a potential bias in reviewers' recommendations induced by this information. In this work, we investigate whether reviewers exhibit a bias caused by the knowledge that the submission under review was previously rejected at a similar venue, focusing on a population of novice reviewers who constitute a large fraction of the reviewer pool in leading machine learning and computer science conferences. We design and conduct a randomized controlled trial closely replicating the relevant components of the peer-review pipeline with $133$ reviewers (master's, junior PhD students, and recent graduates of top US universities) writing reviews for $19$ papers. The analysis reveals that reviewers indeed become negatively biased when they receive a signal about paper being a resubmission, giving almost 1 point lower overall score on a 10-point Likert item (Δ = -0.78, 95% CI = [-1.30, -0.24]) than reviewers who do not receive such a signal. Looking at specific criteria scores (originality, quality, clarity and significance), we observe that novice reviewers tend to underrate quality the most.", "Keywords": "biases; peer review; randomized controlled trial", "DOI": "10.1145/3449149", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> B<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Maryland &amp; Microsoft Research, College Park, MD, USA"}], "References": []}, {"ArticleId": 87831331, "Title": "A Tale of Creativity and Struggles", "Abstract": "Game jams are intense and time-sensitive online or face-to-face game creation events where a digital game is developed in a relatively short time frame (typically 48 to 72 hours) exploring given design constraints and end results are shared publicly. They have increasingly become emerging sites where non-professional game developers, amateurs, and hobbyists engage in bottom-up technological innovation by collaboratively designing and developing more creative and novel digital products. Drawing on 28 interviews, in this paper we focus on how game developers collaborate as small teams to innovate game design and development from the bottom up in virtual game jams (i.e., exclusively online) and the unique role of virtual game jams in their technological innovation. We contribute to CSCW by providing new empirical evidence of how team practices for innovation may emerge in a novel technology community that is not widely studied before. We also expand a growing research agenda in CSCW on explicating nuanced social behaviors, processes, and consequences of bottom-up technological innovation.", "Keywords": "computer-mediated collaboration; game development; indie game development; team practices; technological innovation; virtual game jams", "DOI": "10.1145/3449150", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Clemson University, Clemson, SC, USA"}], "References": [{"Title": "\"Pro-Amateur\"-Driven Technological Innovation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "GROUP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Mitigating Exploitation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831332, "Title": "Social-Emotional-Sensory Design Map for Affective Computing Informed by Neurodivergent Experiences", "Abstract": "One of the grand challenges of artificial intelligence and affective computing is for technology to become emotionally-aware and thus, more human-like. Modeling human emotions is particularly complicated when we consider the lived experiences of people who are on the autism spectrum. To understand the emotional experiences of autistic adults and their attitudes towards common representations of emotions, we deployed a context study as the first phase of a Grounded Design research project. Based on community observations and interviews, this work contributes empirical evidence of how the emotional experiences of autistic adults are entangled with social interactions as well as the processing of sensory inputs. We learned that (1) the emotional experiences of autistic adults are embodied and co-constructed within the context of physical environments, social relationships, and technology use, and (2) conventional approaches to visually representing emotion in affective education and computing systems fail to accurately represent the experiences and perceptions of autistic adults. We contribute a social-emotional-sensory design map to guide designers in creating more diverse and nuanced affective computing interfaces that are enriched by accounting for neurodivergent users.", "Keywords": "accessibility; autism; emotions; interpersonal communication; social-emotional learning", "DOI": "10.1145/3449151", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": []}, {"ArticleId": ********, "Title": "More Accounts, Fewer <PERSON>", "Abstract": "Algorithmic timeline curation is now an integral part of Twitter's platform, affecting information exposure for more than 150 million daily active users. Despite its large-scale and high-stakes impact, especially during a public health emergency such as the COVID-19 pandemic, the exact effects of Twitter's curation algorithm generally remain unknown. In this work, we present a sock-puppet audit that aims to characterize the effects of algorithmic curation on source diversity and topic diversity in Twitter timelines. We created eight sock puppet accounts to emulate representative real-world users, selected through a large-scale network analysis. Then, for one month during early 2020, we collected the puppets' timelines twice per day. Broadly, our results show that algorithmic curation increases source diversity in terms of both Twitter accounts and external domains, even though it drastically decreases the number of external links in the timeline. In terms of topic diversity, algorithmic curation had a mixed effect, slightly amplifying a cluster of politically-focused tweets while squelching clusters of tweets focused on COVID-19 fatalities and health information. Finally, we present some evidence that the timeline algorithm may exacerbate partisan differences in exposure to different sources and topics. The paper concludes by discussing broader implications in the context of algorithmic gatekeeping.", "Keywords": "algorithm auditing; content ranking; social media; twitter", "DOI": "10.1145/3449152", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}], "References": []}, {"ArticleId": 87831334, "Title": "Anyone else have this experience", "Abstract": "Self-tracking technologies, ranging from digital thermometers to wearable fitness trackers, allow users to use personal data accumulated from their everyday activities. But, to use these data, people have to make sense of how these numbers and figures are relevant to their lives in some way in order to make decisions and gain new insight. This process is impacted by people's emotional reactions to their data. While seeking support from others can be an effective strategy for overcoming these emotional challenges, self-trackers face unique barriers in sharing their personal data. Our study investigates 1) how users seek out support online for emotional barriers elicited by their self-tracking data and 2) what self-described impact this sharing has on their self-tracking practices. To investigate these topics, we analyzed discussions in two online communities on Reddit.com centered around infertility and trying to conceive that consistently describe self-tracking experiences. We found that community members described three distinct driving emotional tensions with their self-tracking data. In seeking community input, users were focused on support for understanding and acting upon their feelings and emotions. Even when data was uncertain, frustrating, or viewed as inaccurate, comparing and learning with others benefited users through feelings of connection, control, and humor this collective sense-making provided. Additionally, we found that users taking breaks from self-tracking in whole or part appeared to support their emotional well-being and long-term motivation to track. Based on these findings, we conclude that self-tracking data has social and emotional value beyond perceived accuracy and individual treatment goals.", "Keywords": "emotion; infertility; online communities; personal informatics; reddit; self-tracking; sense-making", "DOI": "10.1145/3449153", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Michigan State University, East Lansing, MI, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Michigan State University, East Lansing, MI, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Michigan State University, East Lansing, MI, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Michigan State University, East Lansing, MI, USA"}], "References": [{"Title": "Abandonment of personal quantification: A review and empirical study investigating reasons for wearable activity tracking attrition", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "223", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 87831335, "Title": "Learning to Ignore", "Abstract": "Bulk email is a primary communication channel within organizations, with all-company emails and regular newsletters serving as a mechanism for making employees aware of policies and events. Ineffective communication could result in wasted employee time and a lack of compliance or awareness. Previous studies on organizational emails focused mostly on recipients. However, organizational bulk email system is a multi-stakeholder problem including recipients, communicators, and the organization itself.\n We studied the effectiveness, practice, and assessments of the organizational bulk email system of a large university from multi-stakeholders' perspectives. We conducted a qualitative study with the university's communicators, recipients, and managers. We delved into the organizational bulk email's distributing mechanisms of the communicators, the reading behaviors of recipients, and the perspectives on emails' values of communicators, managers, and recipients.\n We found that the organizational bulk email system as a whole was strained, and communicators are caught in the middle of this multi-stakeholder problem. First, though the communicators had an interest in preserving the effectiveness of channels in reaching employees, they had high-level clients whose interests might outweigh judgment about whether a message deserves widespread circulation. Second, though communicators thought they were sending important information, recipients viewed most of the organizational bulk emails as not relevant to them. Third, this disagreement was amplified by the success metric used by communicators. They viewed their bulk emails as successful if they had a high open rate. But recipients often opened and then rapidly discarded emails without reading the details. Last, while the communicators in general understood the challenge, they had a limited set of targeting and feedback tools to support their task.", "Keywords": "email; organizational communication", "DOI": "10.1145/3449154", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "Ruoyan Kong", "Affiliation": "University of Minnesota - Twin Cities, Minneapolis, MN, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Minnesota - Twin Cities, Minneapolis, MN, USA"}], "References": []}, {"ArticleId": 87831336, "Title": "Dissecting the Meme Magic: Understanding Indicators of Virality in Image Memes", "Abstract": "Despite the increasingly important role played by image memes, we do not yet have a solid understanding of the elements that might make a meme go viral on social media. In this paper, we investigate what visual elements distinguish image memes that are highly viral on social media from those that do not get re-shared, across three dimensions: composition, subjects, and target audience. Drawing from research in art theory, psychology, marketing, and neuroscience, we develop a codebook to characterize image memes, and use it to annotate a set of 100 image memes collected from 4chan's Politically Incorrect Board (/pol/). On the one hand, we find that highly viral memes are more likely to use a close-up scale, contain characters, and include positive or negative emotions. On the other hand, image memes that do not present a clear subject the viewer can focus attention on, or that include long text are not likely to be re-shared by users.\n We train machine learning models to distinguish between image memes that are likely to go viral and those that are unlikely to be re-shared, obtaining an AUC of 0.866 on our dataset. We also show that the indicators of virality identified by our model can help characterize the most viral memes posted on mainstream online social networks too, as our classifiers are able to predict 19 out of the 20 most popular image memes posted on Twitter and Reddit between 2016 and 2018. Overall, our analysis sheds light on what indicators characterize viral and non-viral visual content online, and set the basis for developing better techniques to create or moderate content that is more likely to catch the viewer's attention.", "Keywords": "internet culture; internet memes; new aesthetics; social media; viral content", "DOI": "10.1145/3449155", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Boston University, Boston, MA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Binghamton University, Binghamton, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Binghamton University, Binghamton, NY, USA"}, {"AuthorId": 4, "Name": "Emiliano De Cristofaro", "Affiliation": "University College London, London, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Max Planck Institute for Informatics, Saarbrücken, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Boston University, Boston, MA, USA"}], "References": []}, {"ArticleId": 87831337, "Title": "Tech-Art-Theory", "Abstract": "This paper explores the nature and potential of improvisation as a method for learning and teaching in CSCW and HCI. It starts by reviewing concepts of improvisational learning in classic and more recent work in educational theory, art and music, and HCI that emphasize the reconstructive, materially-driven, error-engaged, transgressive, and collaborative nature of human learning processes. It then describes three pedagogical interventions of our own in which improvisational techniques were deployed as methods of teaching and learning. From this integrated study, we report specific pedagogical conditions (socio-material evaluations, multi-sensory practices, and making safe spaces for error) that can support improvisational learning, and three common challenges of HCI pedagogy relevance, assessment, and inclusion that improvisational methods can help to address.", "Keywords": "art; ethnography; improvisation; learning; music; pedagogy; performance", "DOI": "10.1145/3449156", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}], "References": []}, {"ArticleId": 87831338, "Title": "Auditing the Information Quality of News-Related Queries on the Alexa Voice Assistant", "Abstract": "Smart speakers are becoming increasingly ubiquitous in society and are now used for satisfying a variety of information needs, from asking about the weather or traffic to accessing the latest breaking news information. Their growing use for news and information consumption presents new questions related to the quality, source diversity, and comprehensiveness of the news-related information they convey. These questions have significant implications for voice assistant technologies acting as algorithmic information intermediaries, but systematic information quality audits have not yet been undertaken. To address this gap, we develop a methodological approach for evaluating information quality in voice assistants for news-related queries. We demonstrate the approach on the Amazon Alexa voice assistant, first characterising Alexa's performance in terms of response relevance, accuracy, and timeliness, and then further elaborating analyses of information quality based on query phrasing, news category, and information provenance. We discuss the implications of our findings for future audits of information quality on voice assistants and for the consumption of news information via such algorithmic intermediaries more broadly.", "Keywords": "algorithmic accountability; audit framework; information quality; voice assistants", "DOI": "10.1145/3449157", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}], "References": [{"Title": "The Role of Conversational Grounding in Supporting Symbiosis Between People and Digital Assistants", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": ********, "Title": "Self-tracking in Parkinson's The Lived Efforts of Self-management", "Abstract": "People living with Parkinson's disease engage in self-tracking as part of their health self-management. Whilst health technologies designed for this group have primarily focused on improving the clinical assessments of the disease, less attention has been given to how people with Parkinson's use technology to track and manage their disease in their everyday experience. We report on a qualitative study in which we systematically analysed posts from an online health community (OHC) comprising people with Parkinson's (PwP). Our findings show that PwP track a diversity of information and use a wide range of digital and non-digital tools, informed by temporal and structured practices. Using an existing framework of sensemaking for chronic disease self-management, we also identify new ways in which PwP engage in sensemaking, alongside a set of new challenges that are particular to the character of this chronic disease. We relate our findings to technologies for self-tracking offering design implications.", "Keywords": "<PERSON><PERSON><PERSON>'s disease; self-management; self-tracking; sensemaking", "DOI": "10.1145/3449158", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, University of London &amp; UCL Knowledge Lab, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UCL Knowledge Lab, London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, University of London, London, United Kingdom"}], "References": [{"Title": "Managing healthcare conflicts when living with multiple chronic conditions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "102494", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 87831340, "Title": "A Design Exploration of Health-Related Community Displays", "Abstract": "The global population is ageing, leading to shifts in healthcare needs. It is well established that increased physical activity can improve the health and wellbeing of many older adults. However, motivation remains a prime concern. We report findings from a series of focus groups where we explored the concept of using community displays to promote physical activity to a local neighborhood. In doing so, we contribute both an understanding of the design space for community displays, as well as a discussion of the implications of our work for the broader CSCW community. We conclude that our work demonstrates the potential for developing community displays for increasing physical activity amongst older adults.", "Keywords": "community design; digital civics; personal informatics; physical activity", "DOI": "10.1145/3449159", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The Open University, Milton Keynes, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The Open University, Milton Keynes, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Community Action: MK, Milton Keynes, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Community Action: MK, Milton Keynes, United Kingdom"}], "References": [{"Title": "The benefits and challenges of using crowdfunding to facilitate community-led projects in the context of digital civics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "33", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 87831341, "Title": "The Complementary Nature of Perceived and Actual Time Spent Online in Measuring Digital Well-being", "Abstract": "As online platforms become ubiquitous, there is growing concern that their use can potentially lead to negative outcomes in users' personal lives, such as disrupted sleep and impacted social relationships. A central question in the literature studying these problematic effects is whether they are associated with the amount of time users spend on online platforms. This is often addressed by either analyzing self-reported measures of time spent online, which are generally inaccurate, or using objective metrics derived from server logs or tracking software. Nonetheless, how the two types of time measures comparatively relate to problematic effects -- whether they complement or are redundant with each other in predicting problematicity -- remains unknown. Additionally, transparent research into this question is hindered by the literature's focus on closed platforms with inaccessible data, as well as selective analytical decisions that may lead to reproducibility issues.\n In this work, we investigate how both self-reported and data-derived metrics of time spent relate to potentially problematic effects arising from the use of an open, non-profit online chess platform. These effects include disruptions to sleep, relationships, school and work performance, and self-control. To this end, we distributed a gamified survey to players and linked their responses with publicly-available game logs. We find problematic effects to be associated with both self-reported and data-derived usage measures to similar degrees. However, analytical models incorporating both self-reported and actual time explain problematic effects significantly more effectively than models with either type of measure alone. Furthermore, these results persist across thousands of possible analytical decisions when using a robust and transparent statistical framework. This suggests that the two methods of measuring time spent measure contain distinct, complementary information about problematic usage outcomes and should be used in conjunction with each other.", "Keywords": "online well-being; problematic platform use; specification curve analysis; survey methodology", "DOI": "10.1145/3449160", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Toronto, Toronto, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Toronto, Toronto, ON, Canada"}], "References": []}, {"ArticleId": 87831342, "Title": "Moderator <PERSON><PERSON><PERSON> for Deliberative Discussion", "Abstract": "Online chat functions as a discussion channel for diverse social issues. However, deliberative discussion and consensus-reaching can be difficult in online chats in part because of the lack of structure. To explore the feasibility of a conversational agent that enables deliberative discussion, we designed and developed DebateBot, a chatbot that structures discussion and encourages reticent participants to contribute. We conducted a 2 (discussion structure: unstructured vs. structured) × 2 (discussant facilitation: unfacilitated vs. facilitated) between-subjects experiment (N = 64, 12 groups). Our findings are as follows: (1) Structured discussion positively affects discussion quality by generating diverse opinions within a group and resulting in a high level of perceived deliberative quality. (2) Facilitation drives a high level of opinion alignment between group consensus and independent individual opinions, resulting in authentic consensus reaching. Facilitation also drives more even contribution and a higher level of task cohesion and communication fairness. Our results suggest that a chatbot agent could partially substitute for a human moderator in deliberative discussions.", "Keywords": "chatbot; consensus reaching; conversational agent; deliberative discussion", "DOI": "10.1145/3449161", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}], "References": []}, {"ArticleId": 87831343, "Title": "Values (Mis)alignment", "Abstract": "Social platforms hold great promise for supporting marginalized communities, such as the LGBTQ+ community, yet they are frequently sites of further stigmatization and harm. By engaging a diverse sample of 31 US LGBTQ+ users in five qualitative, design-based value elicitation exercises, we find that misalignments between perceived platform values and the values of the marginalized users they serve are at the heart of this disconnect. We inductively identify two community-based design values for supporting LGBTQ+ users: self-determination and inclusion. These values can be used as design heuristics for both improving existing platforms as well as guiding future new platform development. Based on participant feedback, we provide directions for enacting these values to better align platform values with this marginalized population's needs.", "Keywords": "anxiety; design values; lgbtq+; online communities; platforms; queer hci; social media; value-sensitive design", "DOI": "10.1145/3449162", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Northwestern University, Evanston, IL, USA"}], "References": []}, {"ArticleId": 87831344, "Title": "AI-Assisted Human Labeling", "Abstract": "Human labeling of training data is often a time-consuming, expensive part of machine learning. In this paper, we study \"batch labeling\", an AI-assisted UX paradigm, that aids data labelers by allowing a single labeling action to apply to multiple records. We ran a large scale study on Mechanical Turk with 156 participants to investigate labeler-AI-batching system interaction. We investigate the efficacy of the system when compared to a single-item labeling interface (i.e., labeling one record at-a-time), and evaluate the impact of batch labeling on accuracy and time. We further investigate the impact of AI algorithm quality and its effects on the labelers' overreliance, as well as potential mechanisms for mitigating it. Our work offers implications for the design of batch labeling systems and for work practices focusing on labeler-AI-batching system interaction.", "Keywords": "agents; ai; collaboration; data labeling", "DOI": "10.1145/3449163", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBM Research, Yorktown Heights, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IBM Research, Yorktown Heights, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IBM Research Australia, Melbourne, VIC, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "IBM Research, Hastings on Hudson, NY, USA"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Independent Researcher, San Jose, CA, USA"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "IBM Research, Yorktown Heights, NY, USA"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "IBM Research, Cambridge, MA, USA"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "IBM Research, Yorktown Heights, NY, USA"}], "References": [{"Title": "Snorkel: rapid training data creation with weak supervision", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "2-3", "Page": "709", "JournalTitle": "The VLDB Journal"}, {"Title": "Human-Centered Artificial Intelligence: Reliable, Safe & Trustworthy", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "6", "Page": "495", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "How do Data Science Workers Collaborate? Roles, Workflows, and Tools", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 87831345, "Title": "Analyzing Twitter Users' Behavior Before and After Contact by the Russia's Internet Research Agency", "Abstract": "Social media platforms have been exploited to conduct election interference in recent years. In particular, the Russian-backed Internet Research Agency (IRA) has been identified as a key source of misinformation spread on Twitter prior to the 2016 U.S. presidential election. The goal of this research is to understand whether general Twitter users changed their behavior in the year following first contact from an IRA account. We compare the before and after behavior of contacted users to determine whether there were differences in their mean tweet count, the sentiment of their tweets, and the frequency and sentiment of tweets mentioning @realDonaldTrump or @HillaryClinton. Our results indicate that users overall exhibited statistically significant changes in behavior across most of these metrics, and that those users that engaged with the IRA generally showed greater changes in behavior.", "Keywords": "democracy; internet research agency; ira; trump; twitter", "DOI": "10.1145/3449164", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 2, "Name": "Rhett <PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 6, "Name": "Qin Lv", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO, USA"}], "References": []}, {"ArticleId": 87831509, "Title": "Meta Learning for Few-Shot One-Class Classification", "Abstract": "<p>We propose a method that can perform one-class classification given only a small number of examples from the target class and none from the others. We formulate the learning of meaningful features for one-class classification as a meta-learning problem in which the meta-training stage repeatedly simulates one-class classification, using the classification loss of the chosen algorithm to learn a feature representation. To learn these representations, we require only multiclass data from similar tasks. We show how the Support Vector Data Description method can be used with our method, and also propose a simpler variant based on Prototypical Networks that obtains comparable performance, indicating that learning feature representations directly from data may be more important than which one-class algorithm we choose. We validate our approach by adapting few-shot classification datasets to the few-shot one-class classification scenario, obtaining similar results to the state-of-the-art of traditional one-class classification, and that improves upon that of one-class classification baselines employed in the few-shot setting.</p>", "Keywords": "meta-learning; one-class classification; deep learning; computer vision meta-learning ; one-class classification ; deep learning ; computer vision", "DOI": "10.3390/ai2020012", "PubYear": 2021, "Volume": "2", "Issue": "2", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Federal University of Bahia (UFBA), Salvador 40110-909, Brazil ↑ Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Federal University of Bahia (UFBA), Salvador 40110-909, Brazil ↑ Current address: Institute for Artificial Intelligence (AI + X), University of South Florida, Tampa, FL 33620, USA. Academic Editor: <PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": 87831682, "Title": "Exact Reduction of the Generalized Lotka–Volterra Equations via Integral and Algebraic Substitutions", "Abstract": "Systems of interacting species, such as biological environments or chemical reactions, are often described mathematically by sets of coupled ordinary differential equations. While a large number β of species may be involved in the coupled dynamics, often only α<β species are of interest or of consequence. In this paper, we explored how to construct models that include only those given α species, but still recreate the dynamics of the original β-species model. Under some conditions detailed here, this reduction can be completed exactly, such that the information in the reduced model is exactly the same as the original one, but over fewer equations. Moreover, this reduction process suggests a promising type of approximate model—no longer exact, but computationally quite simple.", "Keywords": "generalized Lotka–Volterra equations; exact reduction; algebraic substitutions; memory kernel generalized Lotka–Volterra equations ; exact reduction ; algebraic substitutions ; memory kernel", "DOI": "10.3390/computation9050049", "PubYear": 2021, "Volume": "9", "Issue": "5", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Colorado Boulder, Boulder, CO 80309-0430, USA Academic Editors: <PERSON><PERSON> and <PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": ********, "Title": "Discovering critical KPI factors from natural language in maintenance work orders", "Abstract": "<p>Optimizing maintenance practices is a continuous process that must take into account the evolving state of the equipment, resources, workers, and more. To help streamline this process, facilities need a concise procedure for identifying critical tasks and assets that have major impact on the performance of maintenance activities. This work provides a process for making data investigations more effective by discovering influential equipment, actions, and other environmental factors from tacit knowledge within maintenance documents and reports. Traditional application of text analysis focuses on prediction and modeling of system state directly. Variation in domain data, quality, and managerial expectations prevent the creation of a generic method to do this with real industrial data. Instead, text analysis techniques can be applied to discover key factors within a system, which function as indicators for further, in-depth analysis. These factors can point investigators where to find good or bad behaviors, but do not explicitly perform any anomaly detection. This paper details an adaptable procedure tailored to maintenance and industrial settings for determining important named entities within natural language documents. The procedure in this paper utilizes natural language processing techniques to extract these terms or concepts from maintenance work orders and measure their influence on Key Performance Indicators (KPIs) as defined by managers and decision makers. We present a case study to demonstrate the developed workflow (algorithmic procedure) to identify terms associated with concepts or systems which have strong relationships with a selected KPI, such as time or cost. This proof of concept uses the length of time a Maintenance Work Order (MWO) remains open from creation to completion as the relevant performance indicator. By identifying tasks, assets, and environments that have significant relevance to KPIs, planners and decision makers can more easily direct investigations to identify problem areas within a facility, better allocate resources, and guide more effective analysis for both monitoring and improving a facility. The output of the analysis workflow presented in this paper is not intended as a direct indicator of good or bad practices and assets, but instead is intended to be used to help direct and improve the effectiveness of investigations determining those. This workflow provides a preparatory investigation that both conditions the data, helps guide investigators into more productive and effective investigations of the latent information contained in human generated work logs, specifically the natural language recorded in MWOs. When this information preparing and gathering procedure is used in conjunction with other tacit knowledge or analysis tools it gives a more full picture of the efficiency and effectiveness of maintenance strategies. When properly applied, this methodology can identify pain points, highlight anomalous patterns, or verify expected outcomes of a facility’s maintenance strategy.</p>", "Keywords": "Maintenance; Key performance indicators; Natural language processing; Intelligence augmentation; Artificial intelligence", "DOI": "10.1007/s10845-021-01772-5", "PubYear": 2022, "Volume": "33", "Issue": "6", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Systems Integration Division, Engineering Laboratory, National Institute of Standards and Technology, Gaithersburg, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Systems Integration Division, Engineering Laboratory, National Institute of Standards and Technology, Gaithersburg, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Systems Integration Division, Engineering Laboratory, National Institute of Standards and Technology, Gaithersburg, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Systems Integration Division, Engineering Laboratory, National Institute of Standards and Technology, Gaithersburg, USA"}], "References": [{"Title": "Preventive maintenance scheduling optimization based on opportunistic production-maintenance synchronization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "545", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A digital twins concept model for integrated maintenance: a case study for crane operation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "7", "Page": "1863", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Maintenance optimization for a multi-unit system with digital twin simulation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "7", "Page": "1953", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 87831865, "Title": "sBotics - Gamified Framework for Educational Robotics", "Abstract": "<p>This paper proposes a learning framework for Educational Robotics named sBotics, which includes a complete environment for teaching and programming skills acquisition designed for both teachers and K-12 students. Our framework has been developed using a gamified approach with the system and simulated environment developed in the Unity game engine. The main novelty of this platform is its ease-of-use combined with the flexibility to create a variety of scenarios with endless learning potential, in contrast to our evaluations where no alternatives with such characteristics were found for the K-12 range that we are targeting. Also as a contribution of our proposal, robot programs are treated as games that are affected by a disturbance model, which acts in the robotic system and environment variables. This model is introduced in order to approximate what happens in a real robot programming platform. Besides, it is possible for the user, throughout its use, to code in three levels of abstraction: its overly intuitive native programming language called R-Educ, BlockEduc (R-Educ version of Blockly), and C#. Programs can be compiled and interpreted by virtual robots executing any given command. As for teachers, the framework API offers tools that can be employed in the assembly and customization of the learning setup. The whole platform has been built as a tool dedicated to spreading the worlds of Robotics and Programming among youngsters, as well as making them more affordable to everyone. It has been validated by our experiments and is currently being used during the novel Coronavirus pandemic by the official RoboCupJunior Rescue trials in Brazil, currently with more than a thousand competing teams (about 5 thousands students).</p><p>© The Author(s), under exclusive licence to Springer Nature B.V. 2021.</p>", "Keywords": "Educational robotics;Learning and programming framework;Robots", "DOI": "10.1007/s10846-021-01364-8", "PubYear": 2021, "Volume": "102", "Issue": "1", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Nascimento", "Affiliation": "Metrópole Digital Institute, Universidade Federal do Rio Grande do Norte, Natal, RN Brazil."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Diretoria Acadêmica de Gestão e Tecnologia da Informação, Instituto Federal de Educação Tecnolócgica do Rio Grande do Norte, Natal, RN Brazil."}, {"AuthorId": 3, "Name": "Thiago do Nascimento Ferreira", "Affiliation": "Diretoria Acadêmica de Gestão e Tecnologia da Informação, Instituto Federal de Educação Tecnolócgica do Rio Grande do Norte, Natal, RN Brazil."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Program in Computer and Electrical Engineering, Universidade Federal do Rio Grande do Norte, Natal, RN Brazil."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate Program in Computer and Electrical Engineering, Universidade Federal do Rio Grande do Norte, Natal, RN Brazil."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>nça<PERSON>", "Affiliation": "Graduate Program in Computer and Electrical Engineering, Universidade Federal do Rio Grande do Norte, Natal, RN Brazil."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Diretoria Acadêmica de Gestão e Tecnologia da Informação, Instituto Federal de Educação Tecnolócgica do Rio Grande do Norte, Natal, RN Brazil."}], "References": []}, {"ArticleId": 87831866, "Title": "An Embedded Quaternion-Based Extended Kalman Filter Pose Estimation for Six Degrees of Freedom Systems", "Abstract": "<p>This paper proposes a formulation of quaternion-based Extended Kalman Filter pose estimation for six degrees of freedom systems embedded in an FPGA with commercial processors. Our approach uses the fusion of a camera and an inertial measurement unit to estimate simultaneously the position and the orientation of the system of interest. In addition, a Stewart platform is used to validate and evaluate the estimated pose. Although this work considers the use of common low-cost sensors and the use of markers with simple geometry, the results show excellent performance of the developed filter, being able to estimate the pose and orientation with an error below 8.14 mm and 0.63<sup> o ̱</sup>, respectively. Furthermore, the effectiveness of the approach has also been evaluated, showing that the filter is able to converge quickly when the markers are retrieved after a loss of camera data for a short period of time.</p>", "Keywords": "Extended Kalman filter; Stewart platform; Quaternion; Pose estimation; Embedded systems; FPGA", "DOI": "10.1007/s10846-021-01377-3", "PubYear": 2021, "Volume": "102", "Issue": "1", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Technology – Pontifical Catholic University of Rio Grande do Sul, PUCRS, Porto Alegre, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Technology – Pontifical Catholic University of Rio Grande do Sul, PUCRS, Porto Alegre, Brazil;Systems, Estimation, Control and Optimization Department, University of Mons, UMONS, Mons, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Technology – Pontifical Catholic University of Rio Grande do Sul, PUCRS, Porto Alegre, Brazil"}], "References": []}, {"ArticleId": 87831917, "Title": "Numerical Approximations and Error Analysis of the Cahn–Hilliard Equation with Reaction Rate Dependent Dynamic Boundary Conditions", "Abstract": "<p>We consider numerical approximations and error analysis for the <PERSON><PERSON>–<PERSON> equation with reaction rate dependent dynamic boundary conditions (<PERSON><PERSON> et al. ESAIM Math Model Numer Anal 55(1):229–282, 2021). Based on the stabilized linearly implicit approach, a first-order in time, linear and energy stable scheme for solving this model is proposed. The corresponding semi-discretized-in-time error estimates for the scheme are also derived. Numerical experiments, including the simulations with different energy potentials, the comparison with the former work, the convergence results for the relaxation parameter \\(K\\rightarrow 0\\) and \\(K\\rightarrow \\infty \\) and the accuracy tests with respect to the time step size, are performed to validate the accuracy of the proposed scheme and the error analysis. </p>", "Keywords": "<PERSON><PERSON>–<PERSON> equation; Reaction rate; Dynamic boundary conditions; Numerical approximations; Error analysis", "DOI": "10.1007/s10915-021-01475-2", "PubYear": 2021, "Volume": "87", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Beijing Normal University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory of Mathematics and Complex Systems, Ministry of Education and School of Mathematical Sciences, Beijing Normal University, Beijing, China"}], "References": [{"Title": "Numerical Approximations and Error Analysis of the Cahn–Hilliard Equation with Reaction Rate Dependent Dynamic Boundary Conditions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "87", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 87831918, "Title": "Efficient Conformal Parameterization of Multiply-Connected Surfaces Using Quasi-Conformal Theory", "Abstract": "<p>Conformal mapping, a classical topic in complex analysis and differential geometry, has become a subject of great interest in the area of surface parameterization in recent decades with various applications in science and engineering. However, most of the existing conformal parameterization algorithms only focus on simply-connected surfaces and cannot be directly applied to surfaces with holes. In this work, we propose two novel algorithms for computing the conformal parameterization of multiply-connected surfaces. We first develop an efficient method for conformally parameterizing an open surface with one hole to an annulus on the plane. Based on this method, we then develop an efficient method for conformally parameterizing an open surface with k holes onto a unit disk with k circular holes. The conformality and bijectivity of the mappings are ensured by quasi-conformal theory. Numerical experiments and applications are presented to demonstrate the effectiveness of the proposed methods.</p>", "Keywords": "Surface parameterization; Conformal mapping; Quasi-conformal theory; Multiply-connected surfaces; Annulus; Poly-annulus; 65D18; 68U05; 52C26; 30C20", "DOI": "10.1007/s10915-021-01479-y", "PubYear": 2021, "Volume": "87", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Massachusetts Institute of Technology, Cambridge, USA"}], "References": [{"Title": "Tooth morphometry using quasi-conformal theory", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "99", "Issue": "", "Page": "107064", "JournalTitle": "Pattern Recognition"}, {"Title": "PlgCirMap: A MATLAB toolbox for computing conformal mappings from polygonal multiply connected domains onto circular domains", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100464", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 87831919, "Title": "A Conservative Linearly-Implicit Compact Difference Scheme for the Quantum Zakharov System", "Abstract": "<p>This paper is devoted to developing and analysing a highly accurate conservative method for solving the quantum Zakharov system. The scheme is based on a linearly-implicit compact finite difference discretization and conserve the mass as well as energy in discrete level. Detailed numerical analysis is presented which shows the method is fourth-order accurate in space and second-order accurate in time. Several numerical examples are reported to confirm the conservation properties and high accuracy of the proposed scheme. Finally the compact scheme is applied to study the convergence rate of the quantum Zakharov system to its limiting model in the semi-classical limit.</p>", "Keywords": "Quantum Zakharov system; Conservative properties; Compact finite difference scheme; Convergence; 35Q53; 65M15; 65M70", "DOI": "10.1007/s10915-021-01482-3", "PubYear": 2021, "Volume": "87", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "South China Research Center for Applied Mathematics and Interdisciplinary Studies, South China Normal University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Yau Mathematical Sciences Center, Tsinghua University, Beijing, China;Zentrum Mathematik, Technische Universität München, Garching bei München, Germany"}], "References": []}, {"ArticleId": 87831920, "Title": "Fifth-Order Hermite Targeted Essentially Non-oscillatory Schemes for Hyperbolic Conservation Laws", "Abstract": "<p>We present a targeted essentially non-oscillatory (TENO) scheme based on Her<PERSON> polynomials for solving hyperbolic conservation laws. Hermite polynomials have already been adopted in weighted essentially non-oscillatory (WENO) schemes (<PERSON><PERSON> and <PERSON> in J Comput Phys 193:115–135, 2003). The Hermite TENO reconstruction offers major advantages over the earlier reconstruction; namely, it is a compact Hermite-type reconstruction and has low dissipation by virtue of TENO’s stencil voting strategy. Next, we formulate a new high-order global reference smoothness indicator for the proposed scheme. The flux calculations and time-advancing schemes are carried out by the local Lax–<PERSON> flux and third-order strong-stability-preserving Run<PERSON>–<PERSON> methods, respectively. The scalar and system of the hyperbolic conservation laws are demonstrated in numerical tests. In these tests, the proposed scheme improves the shock-capturing performance and inherits the good small-scale resolution of the TENO scheme.</p>", "Keywords": "High-order schemes; WENO schemes; Finite-volume method; Hyperbolic conservation laws; Shock-capturing; 65M08; 35L65", "DOI": "10.1007/s10915-021-01485-0", "PubYear": 2021, "Volume": "87", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Universitas Indonesia, Depok, Indonesia"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Universitas Indonesia, Depok, Indonesia"}, {"AuthorId": 3, "Name": "Engkos A<PERSON>", "Affiliation": "Department of Mechanical Engineering, Universitas Indonesia, Depok, Indonesia"}], "References": []}, {"ArticleId": 87831932, "Title": "Challenges of real-world reinforcement learning: definitions, benchmarks and analysis", "Abstract": "<p>Reinforcement learning (RL) has proven its worth in a series of artificial domains, and is beginning to show some successes in real-world scenarios. However, much of the research advances in RL are hard to leverage in real-world systems due to a series of assumptions that are rarely satisfied in practice. In this work, we identify and formalize a series of independent challenges that embody the difficulties that must be addressed for RL to be commonly deployed in real-world systems. For each challenge, we define it formally in the context of a Markov Decision Process, analyze the effects of the challenge on state-of-the-art learning algorithms, and present some existing attempts at tackling it. We believe that an approach that addresses our set of proposed challenges would be readily deployable in a large number of real world problems. Our proposed challenges are implemented in a suite of continuous control environments called realworldrl-suite which we propose an as an open-source benchmark.</p>", "Keywords": "Reinforcement learning; Real-world; Applied reinforcement learning", "DOI": "10.1007/s10994-021-05961-4", "PubYear": 2021, "Volume": "110", "Issue": "9", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Google Research, Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "DeepMind, London, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DeepMind, London, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "DeepMind, London, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DeepMind, London, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "DeepMind, London, UK"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "DeepMind, London, UK"}], "References": [{"Title": "Learning dexterous in-hand manipulation", "Authors": "OpenAI: <PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "1", "Page": "3", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 87831941, "Title": "A flexible image encryption algorithm based on 3D CTBCS and DNA computing", "Abstract": "<p>In this paper, a novel image encryption algorithm based on a new discrete chaotic system is presented. The chaotic characteristics of the new chaotic system are analyzed by phase diagram, <PERSON><PERSON><PERSON><PERSON> exponent, bifurcation diagram and complexity analysis. The randomness of chaotic sequences is test by the NIST SP 800-22 test package. The calculation time with different length sequence is recorded. Based on the analyses results, a flexible encryption scheme is designed for 2D image and 3D image. The hash value which is calculated from the SHA-256 hash function is used to change the initial conditions of the discrete system firstly. And then, the chaotic sequences are used to scramble and spread the value for the images by Arnold matrix and DNA diffusion algorithm. The security analysis shows that the proposed encryption algorithm possesses higher security features to preserve the subject and resist conventional attack. The results of this paper offer a different realization scheme for protection of image files.</p>", "Keywords": "Image encryption; Discrete chaotic system; DNA encryption; Arnold matrix confusion-diffusion", "DOI": "10.1007/s11042-021-10764-9", "PubYear": 2021, "Volume": "80", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Dalian Polytechnic University, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Dalian Polytechnic University, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Dalian Polytechnic University, Dalian, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Dalian Polytechnic University, Dalian, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Dalian Polytechnic University, Dalian, China"}], "References": [{"Title": "An image encryption algorithm based on 3-D DNA level permutation and substitution scheme", "Authors": "Chang<PERSON> Zhu; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7227", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 87831942, "Title": "A system for detection of moving caption text in videos: a news use case", "Abstract": "<p>Extraction of news text captions aims at a digital understanding of what is happening in a specific region during a certain period that helps in better communication between different nations because we can easily translate the plain text from one language to another. Moving text captions causes blurry effects that are a significant cause of text quality impairments in the news channels. Most of the existing text caption detection models do not address this problem in a way that captures the different dynamic motion of captions, gathers a full news story among several frames in the sequence, resolves the blurring effect of text motion, offers a language-independent model, or provides it as an end-to-end solution for the community to use. We process the frames coming in sequence and extract edge features using either the <PERSON><PERSON> transform or our color-based technique. We verify text existence using a Convolutional Neural Network (CNN) text detection pre-trained model. We analyze the caption motion status using hybrid pre-trained Recurrent Neural Network (RNN) of Long Short-Term Memory (LSTM) type model and correlation-based model. In case the motion is determined to be horizontal rotation, there are two problems. First, it means that text keeps rotating with no stop resulting in a high blurring effect that affects the text quality and consequently resulting in low character recognition accuracy. Second, there are successive news stories which are separated by the channel logo or long spaces. We managed to solve the first problem by deblurring the text image using either Bicubic Spline Interpolation (BSI) technique or the Denoising Autoencoder Neural Network (DANN). We solved the second problem using a Point Feature Matching (PFM) technique to match the existing channel logo with the channels’ logo database (ground truth). We evaluate our framework using Abbyy® SDK as a standalone tool used for text recognition supporting different languages.</p>", "Keywords": "Caption text; News applications; Text localization; Text detection; Text enhancement; Text recognition; Super-resolution; Autoencoder; OCR; Deep neural network", "DOI": "10.1007/s11042-021-10856-6", "PubYear": 2021, "Volume": "80", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cairo University, Cairo, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cairo University, Cairo, Egypt"}], "References": []}, {"ArticleId": 87831943, "Title": "Robust 3D mesh zero-watermarking based on spherical coordinate and Skewness measurement", "Abstract": "<p>As the great advance in technology, 3D models are commonly used in multiple fields such as healthcare, filmdom, interactive entertainment, and construction industry. To prevent 3D data from unauthorized access and illegal tampering, we aim to propose a brand-new zero-watermarking technique based on the transformation of spherical coordinate and skewness of angle statistic. Without distorting the quality of 3D object, the main challenge in zero-watermarking is the robust feature selection and target construction. Since we have adopted the skewness measure of the spherical angle to be the resilient feature, the robustness can be highly enhanced. According to experimental results, the new method can stay stable under common signal processing operations such as translation, vertex reordering, uniform scaling, noise addition, smoothing, simplification, and cropping. This has demonstrated that the new method is suitable for the applications which need highly accurate 3D models.</p>", "Keywords": "3D mesh model; Zero-watermarking; Robustness; Spherical coordinate; Skewness", "DOI": "10.1007/s11042-021-10878-0", "PubYear": 2021, "Volume": "80", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Computer Science, Feng-Chia University, Taichung, Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Computer Science, Feng-Chia University, Taichung, Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Computer Science, Feng-Chia University, Taichung, Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Computer Science, Feng-Chia University, Taichung, Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Illinois, Urbana-Champaign, Champaign, USA"}], "References": [{"Title": "Prediction Error Expansion (PEE) based Reversible polygon mesh watermarking scheme for regional tamper localization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "11437", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Optimization of 3D Triangular Mesh Watermarking Using ACO-<PERSON>’s Law", "Authors": "", "PubYear": 2020, "Volume": "14", "Issue": "10", "Page": "4042", "JournalTitle": "KSII Transactions on Internet and Information Systems"}]}]