[{"ArticleId": 96043385, "Title": "A deep learning method for automatic evaluation of diagnostic information from multi-stained histopathological images", "Abstract": "Manual screening of large-scale histopathological images is an extremely time-consuming, laborious and subjective procedure. Accurate evaluation of diagnostic information from multi-color stained images requires expertise due to the complex nature of histopathology and the lack of quantifiable measurement. In this work, a novel deep learning method is developed based on a convolutional siamese network, in which the information quantification task is transformed into a similarity assessment between lesion and non-lesion patterns on histopathological images. The subtle changes underlying the microstructure of tissue biopsies can be captured through an optimization of training loss within a low-to-high-level feature space. A new information score is introduced to quantify the abnormality in tissue appearance and stain pattern. Experiments on 3 independent data cohorts including 5 types of color-stained images demonstrate that our method can achieve promising performance compared with state-of-the-art methods. Results show that the proposed information score can serve as an effective measure to evaluate the importance of multi-stained images, and ultimately facilitate automatic diagnosis for clinical multi-stained histopathology.", "Keywords": "Information evaluation ; Deep learning ; Siamese network ; Multi-stained histopathological images", "DOI": "10.1016/j.knosys.2022.109820", "PubYear": 2022, "Volume": "256", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Biomedical Science and Medical Engineering, Beihang University, Beijing, 100191, China;Beijing Advanced Innovation Center for Biomedical Engineering, Beihang University, Beijing, 100191, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Biomedical Science and Medical Engineering, Beihang University, Beijing, 100191, China;Beijing Advanced Innovation Center for Biomedical Engineering, Beihang University, Beijing, 100191, China;Corresponding author at: School of Biomedical Science and Medical Engineering, Beihang University, Beijing, 100191, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Pathology, Beijing AnZhen Hospital, Capital Medical University, Beijing 100029, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathology, Peking Union Medical College Hospital, Chinese Academy of Medical Sciences & Peking Union Medical College, Beijing 100730, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathology, Beijing AnZhen Hospital, Capital Medical University, Beijing 100029, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Qin", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing, 100191, China;Corresponding author"}], "References": [{"Title": "Robust nuclei segmentation in histopathology using ASPPU-Net and boundary refinement", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "144", "JournalTitle": "Neurocomputing"}, {"Title": "A survey on deep learning in medicine: Why, how and when?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "111", "JournalTitle": "Information Fusion"}, {"Title": "Visual object tracking based on residual network and cascaded correlation filters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8427", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "VP-NIQE: An opinion-unaware visual perception natural image quality evaluator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "463", "Issue": "", "Page": "17", "JournalTitle": "Neurocomputing"}, {"Title": "SCSTCF: Spatial-Channel Selection and Temporal Regularized Correlation Filters for visual tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "", "Page": "108485", "JournalTitle": "Applied Soft Computing"}, {"Title": "An object tracking framework with recapture based on correlation filters and Siamese networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "107730", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 96043391, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-9473(22)00185-2", "PubYear": 2023, "Volume": "177", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [], "References": []}, {"ArticleId": 96043413, "Title": "CNN-Based Multiterrain Moving Target Recognition Model for Unattended Ground Sensor Systems", "Abstract": "<p>In recent years, many deep learning algorithms based on seismic signals have been proposed to solve the moving target recognition problem in unattended ground sensor systems. Despite the excellent performance of these deep networks, most of them can only be deployed on cloud-based devices and cannot be deployed on low-power hardware devices due to the large network size. Second, since seismic signals are affected by the terrain, employing only seismic signals as reconnaissance means for unattended ground sensors cannot achieve multiterrain-type adaptability. In response, this paper proposes an MFC-TinyNet method facing a multiterrain. The method adds depthwise separable convolutional layers to the network, which effectively reduces the size of the network while keeping the target recognition accuracy constant, and solves the problem that the model is difficult to deploy on low-power hardware. It also uses the Mel-frequency spectrum feature extraction method to fuse sound and seismic signals to improve the accuracy of the model’s moving target recognition on a multiterrain. Experiments demonstrate that the method can combine the two advantages of the small network model and multiterrain applicability.</p>", "Keywords": "", "DOI": "10.1155/2022/7542114", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>g <PERSON>", "Affiliation": "College of Intelligent Science, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Intelligent Science, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Intelligent Science, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligent Science, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligent Science, National University of Defense Technology, Changsha 410073, China"}], "References": [{"Title": "Recognition and prediction of ground vibration signal based on machine learning algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Li", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "1937", "JournalTitle": "Neural Computing and Applications"}, {"Title": "New SAR target recognition based on YOLO and very deep multi-canonical correlation analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "15-16", "Page": "5800", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 96043488, "Title": "CAMFuzz: Explainable Fuzzing with Local Interpretation", "Abstract": "Grey-box fuzzing techniques have been widely used in software bug finding. In general, there are many decisions to make in the fuzzing process, including which code block in the target program should be explored first, which bytes of an input seed should be mutated to reach the target code block, and how to mutate the chosen input bytes. However, existing solutions usually rely on random exploration or certain heuristics to choose where and how to fuzz, which limits the efficiency of fuzzing. In this paper, we propose a novel solution CAMFuzz to guide the fuzzing process with explainable decisions in explainable artificial intelligence (XAI). First, we propose a dynamic weight adjustment algorithm, which considers both the difficulty of reaching a block and the number of unvisited blocks nearby, to find code blocks worthy to explore first. Second, we utilize a widely used local interpretation technique, i.e., class activation mapping (CAM), to recognize which part of an input seed should be mutated to reach a given target code block. Therefore, CAMFuzz can distinguish which part of code in the program is more important and which positions in the input file should be mutated first, in order to achieve a better code coverage and bug finding efficiency. Third, to further help the fuzzer increase fuzzing efficiency, we leverage a lightweight static program analysis to help the fuzzer identify magic values. We implement a prototype of CAMFuzz and evaluate it on 13 real-world programs (including 11 open source targets, 2 closed-source commercial products including a Microsoft component and Hancom Office) Results show that CAMFuzz outperforms state-of-the-art fuzzers in both code coverage and bug finding. To detail, CAMFuzz on average achieves 2.07 $$\\times$$ \n × \n more bugs and 1.17 $$\\times$$ \n × \n coverage improvements. In total, it found 19 previously unknown vulnerabilities, of which 6 have been assigned by CVE so far.", "Keywords": "Fuzzing;Explainable artificial intelligence;Grey-box fuzzing", "DOI": "10.1186/s42400-022-00116-x", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 5427, "JournalTitle": "Cybersecurity", "ISSN": "", "EISSN": "2523-3246", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Network Assessment Technology, CAS, Beijing, China; Beijing Key Laboratory of Network Security and Protection Technology, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Network Assessment Technology, CAS, Beijing, China; Beijing Key Laboratory of Network Security and Protection Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Network Sciences and Cyberspace, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Network Assessment Technology, CAS, Beijing, China; Beijing Key Laboratory of Network Security and Protection Technology, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Network Assessment Technology, CAS, Beijing, China; Beijing Key Laboratory of Network Security and Protection Technology, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Network Assessment Technology, CAS, Beijing, China; Beijing Key Laboratory of Network Security and Protection Technology, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Network Assessment Technology, CAS, Beijing, China; Beijing Key Laboratory of Network Security and Protection Technology, Beijing, China"}], "References": []}, {"ArticleId": 96043542, "Title": "Forew<PERSON>", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11786-022-00533-8", "PubYear": 2022, "Volume": "16", "Issue": "2-3", "JournalId": 6018, "JournalTitle": "Mathematics in Computer Science", "ISSN": "1661-8270", "EISSN": "1661-8289", "Authors": [{"AuthorId": 1, "Name": "Matthew <PERSON>", "Affiliation": "Coventry University, Coventry, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université de Lille, Lille, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Plekhanov Russian University, Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CNRS, Inria, and the University of Lorraine, Nancy, France; MPI Informatics and Saarland University, Saarbrücken, Germany"}], "References": []}, {"ArticleId": 96043640, "Title": "Attention-YOLOV4: a real-time and high-accurate traffic sign detection algorithm", "Abstract": "<p>The technology of traffic sign detection is an important technology in autonomous driving. However due to the small size of traffic signs and the complicated background there are some problems in the practical application of traffic sign detection technology: the detection accuracy of one-stage traffic sign detection algorithm is low, the detection speed of two-stage traffic sign detection algorithm is slow. To achieve real-time and accuracy detection for traffic signs, we treat traffic sign detection as an end-to-end problem in this paper and proposed an improved one-stage traffic sign detection algorithm: Attention-YOLO V4. In order to achieve real-time and high-accurate detection of traffic signs so we analyzed the principle of YOLO V4 for small objects detection: in order to improve the ability of YOLO V4 backbone network extracting traffic sign features we decide to combine channel attention mechanism with residual block, in order to improve the ability of YOLO Head detect traffic signs we combine channel attention mechanism with YOL<PERSON> head. We used TT100K dataset to evaluate Attention-YOLO V4 algorithm, compared with the existing methods, our method achieve real-time and accurately performance in complex backgrounds.</p>", "Keywords": "Traffic signs detection; YOLO V4; Real-time and accuracy detection", "DOI": "10.1007/s11042-022-13251-x", "PubYear": 2023, "Volume": "82", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shanghai University of Electric Power, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai University of Electric Power, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai University of Electric Power, Shanghai, China"}], "References": []}, {"ArticleId": 96043744, "Title": "A study of bacterial adhesion and safety of gloves used continuously", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G1-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Hiromi IMAI", "Affiliation": "Chiba Prefectural University of Health Sciences"}, {"AuthorId": 2, "Name": "Tomoko SANADA", "Affiliation": "Chiba Prefectural University of Health Sciences"}, {"AuthorId": 3, "Name": "Kentaro WATANABE", "Affiliation": "Chiba Prefectural University of Health Sciences"}, {"AuthorId": 4, "Name": "Fusako KAWABE", "Affiliation": "Chiba Prefectural University of Health Sciences"}, {"AuthorId": 5, "Name": "Tetsuo MISAWA", "Affiliation": "Chiba Institute of Technology"}], "References": []}, {"ArticleId": 96043751, "Title": "Sustainability of Training Effectiveness on Medical Incident Report Writing for Junior Resident", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1G2-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Yoshitaka MAEDA", "Affiliation": "Jichi Medical University, Medical Simulation Center"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jichi Medical University, Medical Simulation Center"}, {"AuthorId": 3, "Name": "Yoshikazu ASADA", "Affiliation": "Jichi Medical University, Medical Simulation Center"}, {"AuthorId": 4, "Name": "Shinichi YAMAMOTO", "Affiliation": "Jichi Medical University Hospital"}, {"AuthorId": 5, "Name": "Masahisa SHIMPO", "Affiliation": "Jichi Medical University Hospital"}, {"AuthorId": 6, "Name": "Hiroshi KAWAHIRA", "Affiliation": "Jichi Medical University, Medical Simulation Center"}], "References": []}, {"ArticleId": 96043752, "Title": "Effect of color characteristics of chromatic colors on visibility in people with low vision", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1E3-03", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Hisato OHNO", "Affiliation": "Railway Technical Research Institute"}, {"AuthorId": 2, "Name": "Ayako SUZUKI", "Affiliation": "Railway Technical Research Institute"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Railway Technical Research Institute"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>I<PERSON>", "Affiliation": "Railway Technical Research Institute"}], "References": []}, {"ArticleId": 96043756, "Title": "Study on the possibility of footprint identification method by predicting center-of-gravity shift", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1F3-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>i OKAMOTO", "Affiliation": "Graduate School of Computer Science and System Engineering, Okayama Prefectural University"}, {"AuthorId": 2, "Name": "Seiji SAITO", "Affiliation": "Faculty of Computer Science and System Engineering, Okayama Prefectural University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>hi OYAM<PERSON>", "Affiliation": "Faculty of Computer Science and System Engineering, Okayama Prefectural University"}, {"AuthorId": 4, "Name": "Masanao KOEDA", "Affiliation": "Faculty of Computer Science and System Engineering, Okayama Prefectural University"}, {"AuthorId": 5, "Name": "<PERSON>oshi YAMAUCHI", "Affiliation": "Faculty of Computer Science and System Engineering, Okayama Prefectural University"}], "References": []}, {"ArticleId": 96043760, "Title": "Electronic Information Displays to the Digitization of School Education and International Standards", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.S1D3-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Yuzo HISATAKE", "Affiliation": "Shizuoka University. Organization for Innovation and Social Collaboration"}], "References": []}, {"ArticleId": 96043770, "Title": "Effects of VR-based motion feedback on strength training", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G3-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Hiroaki MATSUBA", "Affiliation": "Waseda university"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> MORIKA<PERSON>", "Affiliation": "Waseda university"}, {"AuthorId": 3, "Name": "Takashi KAWAI", "Affiliation": "Waseda university"}], "References": []}, {"ArticleId": 96043772, "Title": "Effectiveness of Target Prediction Methods in Consideration of Fixation Drift in Eye-gaze Input Systems", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D5-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}], "References": []}, {"ArticleId": 96043777, "Title": "Trends in Ergonomics Standards for Accessibility", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.S1B1-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Naoki SAKAKIBARA", "Affiliation": "Seisen Jo<PERSON>in College Faculty if Human Studies Studies"}], "References": []}, {"ArticleId": 96043779, "Title": "Structural prototyping of a powered exoskeleton for patients with spinal cord injury", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G4-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Occupational Safety and Health, Japan"}, {"AuthorId": 2, "Name": "Hiroyasu IKEDA", "Affiliation": "National Institute of Occupational Safety and Health, Japan"}], "References": []}, {"ArticleId": 96043783, "Title": "Analysis of the Human Error Occurrence Process in Non-stationary System Operation (2)", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D1-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Masakazu NAGASHIMA", "Affiliation": "Toshiba Infrastructure Systems & Solutions Corporation"}, {"AuthorId": 2, "Name": "Yuma KADOKURA", "Affiliation": "Toshiba Infrastructure Systems & Solutions Corporation"}, {"AuthorId": 3, "Name": "Nobuki NEMOTO", "Affiliation": "Toshiba Infrastructure Systems & Solutions Corporation"}, {"AuthorId": 4, "Name": "Ikuo KOYAMA", "Affiliation": "Toshiba Infrastructure Systems & Solutions Corporation"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Toshiba Infrastructure Systems & Solutions Corporation"}], "References": []}, {"ArticleId": 96043791, "Title": "A study on unlearned class exclusion methods in a gait identification", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1F2-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kazuki MORI", "Affiliation": "Hosei University，Graduate School of Science and Engineering"}, {"AuthorId": 2, "Name": "Kaoru SUZUKI", "Affiliation": "Hosei University，Faculty of Science and Engineering"}], "References": []}, {"ArticleId": 96043793, "Title": "Real-time acquisition and analysis of physiological indices in daily life situations using the cloud", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D2-03", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kaito HAYASHI", "Affiliation": "Graduate School of Robotics and Design, Osaka Institute of Technology"}, {"AuthorId": 2, "Name": "Mieko OHSUGA", "Affiliation": "Faculty of Robotics and Design, Osaka Institute of Technology"}], "References": []}, {"ArticleId": 96043799, "Title": "A Proposal of Usability Testing Method Using Gaze Tracking", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D1-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Atsuya KIMURA", "Affiliation": "Graduate School, Shibaura Institute of Technology"}, {"AuthorId": 2, "Name": "Tatsuya SASHIZAWA", "Affiliation": "Toppan Forms Co., Ltd."}, {"AuthorId": 3, "Name": "Kazuki OHASHI", "Affiliation": "Toppan Forms Co., Ltd."}, {"AuthorId": 4, "Name": "R<PERSON>ji <PERSON>", "Affiliation": "Graduate School, Shibaura Institute of Technology"}], "References": []}, {"ArticleId": 96043816, "Title": "Study of behavior to prevent infection using nudge message", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1G2-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kanagawa University, faculty of Engineering"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Kanagawa University, faculty of Engineering"}], "References": []}, {"ArticleId": 96043828, "Title": "Analysis of individual characteristics in binocular vision using SSVEPs by a simple EEG device", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D2-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Ryo MIZUNO", "Affiliation": "TOKAI OPTICAL CO., LTD."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> KOZAKI", "Affiliation": "TOKAI OPTICAL CO., LTD."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>a SUZUKI", "Affiliation": "TOKAI OPTICAL CO., LTD."}, {"AuthorId": 4, "Name": "Koji INUI", "Affiliation": "Department of Functioning and Disability, Institute for Developmental Research"}], "References": []}, {"ArticleId": 96043839, "Title": "段差昇降動作における歩行補助つえの位置変化に関する基礎的分析", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2E4-01", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Science and Technology, Nihon University"}, {"AuthorId": 2, "Name": "Mitsumasa YODA", "Affiliation": "School of Science and Technology, Nihon University"}], "References": []}, {"ArticleId": 96043844, "Title": "食品パッケージにおける記載項目の重要度と行動分析の関連性", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1A3-03", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Tsukasa IKENO", "Affiliation": "Tokyo Denki University, The Graduate School of Science and Engineering"}, {"AuthorId": 2, "Name": "Hiroyuki YAGUCHI", "Affiliation": "Tokyo Denki University, Science and Engineering"}, {"AuthorId": 3, "Name": "Hitomi NOJIMA", "Affiliation": "Universal Communication Design Association"}, {"AuthorId": 4, "Name": "Yohei MORISHITA", "Affiliation": "Universal Communication Design Association"}], "References": []}, {"ArticleId": 96043848, "Title": "ABWの導入効果指標化", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.S1G3-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "RIKEN"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tokyo Metropolitan University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Panasonic Operational Excellence Co., Ltd."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Panasonic Corporation"}], "References": []}, {"ArticleId": 96043851, "Title": "歩行者の歩行挙動と周辺環境の同時計測手法の開発", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2A4-06", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Haruki TAYAMA", "Affiliation": "Seikei University"}, {"AuthorId": 2, "Name": "Kento MATSUZAWA", "Affiliation": "Seikei University"}, {"AuthorId": 3, "Name": "Yu NISHIO", "Affiliation": "Seikei University"}, {"AuthorId": 4, "Name": "Takanobu OGAWA", "Affiliation": "Seikei University"}], "References": []}, {"ArticleId": 96043863, "Title": "Study on Drowsiness Assessment by Spectral Analysis of Pupil Diameter", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2E1-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> FUJIHA<PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 2, "Name": "Atsuo MURATA", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 3, "Name": "Toshihisa DOI", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}], "References": []}, {"ArticleId": 96043907, "Title": "What is required of pharmaceutical packaging for the proper use of pharmaceuticals", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.S2D3-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shonan University of Medical Sciences"}, {"AuthorId": 2, "Name": "Hiroyuki ITOH", "Affiliation": "Koshinkai Shiomidai Hospital, Department of Pharmacy Services)"}, {"AuthorId": 3, "Name": "Kiyomi SADAMOTO", "Affiliation": "Shonan University of Medical Sciences, Faculty of Pharmacy"}], "References": []}, {"ArticleId": 96043929, "Title": "Potentials Method for Mn/Gn/1/r and Mn/Gn/1/∞ Queueing Systems with Typical Dependences of the Input Flow Intensity on the Number of Customers", "Abstract": "<p>The application of the potential method to finding the stationary distribution of the number of customers in the M <sub> n </sub>/ G <sub> n </sub>/1/ r and M <sub> n </sub>/ G <sub> n </sub>/1/∞ queueing systems with threshold operation strateges is proposed. The dependences of the input flow intensity on the number of customers are considered, which are characteristic both for closed systems, which are models of the reliability theory, and for queueing systems with random rarefaction of the input flow. Service intensity control strategies are constructed on the assumption that the intensity can vary at the time when servicing starts. Formulas to determine Laplace transforms of the distribution of the number of customers in the system during the busy period and for calculating the average duration of the busy period are obtained.</p>", "Keywords": "single-channel queueing flow; Poisson input flow of the second kind; dependence of service time on the state of the system; potentials method", "DOI": "10.1007/s10559-022-00474-x", "PubYear": 2022, "Volume": "58", "Issue": "3", "JournalId": 17407, "JournalTitle": "Cybernetics and Systems Analysis", "ISSN": "1060-0396", "EISSN": "1573-8337", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON> National University of Lviv, Lviv, Ukraine"}], "References": [{"Title": "Reliability of a Series System with Redundancy and Threshold Recovery Strategies", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "4", "Page": "629", "JournalTitle": "Cybernetics and Systems Analysis"}]}, {"ArticleId": ********, "Title": "Intensional Kleene and <PERSON> theorems for abstract program semantics", "Abstract": "Classical results in computability theory, notably <PERSON>&<PERSON>x27;s theorem, focus on the extensional content of programs, namely, on the partial recursive functions that programs compute. Later work investigated intensional generalisations of such results that take into account the way in which functions are computed, thus affected by the specific programs computing them. In this paper, we single out a novel class of program semantics based on abstract domains of program properties that are able to capture nonextensional aspects of program computations, such as their asymptotic complexity or logical invariants, and allow us to generalise some foundational computability results such as Rice&#x27;s <PERSON><PERSON> and <PERSON><PERSON><PERSON>&#x27;s Second Recursion Theorem to these semantics. In particular, it turns out that for this class of abstract program semantics, any nontrivial abstract property is undecidable and every decidable over-approximation necessarily includes an infinite set of false positives which covers all the values of the semantic abstract domain.", "Keywords": "Computability theory ; Recursive function ; <PERSON>'s theorem ; <PERSON><PERSON><PERSON>'s second recursion theorem ; Program analysis ; Affine program invariants", "DOI": "10.1016/j.ic.2022.104953", "PubYear": 2022, "Volume": "289", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dipartimento di Matematica, University of Padova, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dipartimento di Matematica, University of Padova, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University College London, UK"}], "References": []}, {"ArticleId": 96044058, "Title": "Single-Trace Side-Channel Attacks on the Toom-Cook: The Case Study of Saber", "Abstract": "<p>The Toom-Cook method is a well-known strategy for building algorithms to multiply polynomials efficiently. Along with NTT-based polynomial multiplication, Toom-Cook-based or Karatsuba-based polynomial multiplication algorithms still have regained attention since the start of the NIST’s post-quantum standardization procedure. Compared to the comprehensive analysis done for NTT, the leakage characteristics of <PERSON><PERSON><PERSON><PERSON> have not been discussed. We analyze the vulnerabilities of <PERSON><PERSON><PERSON><PERSON> in the reference implementation of <PERSON>ber, a third round finalist of NIST’s post-quantum standardization process. In this work, we present the first single-trace attack based on the soft-analytical side-channel attack (SASCA) targeting the Toom-Cook. The deep learning-based power analysis is combined with SASCA to decrease the number of templates since there are a large number of similar operations in the Toom-Cook. Moreover, we describe the optimized factor graph and improved belief propagation to make the attack more practical. The feasibility of the attack is verified by evaluation experiments. We also discuss the possible countermeasures to prevent the attack.</p>", "Keywords": "post-quantum cryptography;Saber KEM;Toom<PERSON>Cook;side-channel attack;deep learning", "DOI": "10.46586/tches.v2022.i4.285-310", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing Agricultural University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Lab, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing Agricultural University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Lab, Hangzhou, China; Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Wuhan University, Wuhan, China"}], "References": []}, {"ArticleId": 96044066, "Title": "A modified gray wolf‐based chameleon swarm algorithm for minimizing energy consumption and enabling secure communication in wireless sensor network", "Abstract": "<p>The efficiency and security of WSNs are improved by designing multiple lightweight cryptographic techniques and a 6LoWPAN protocol, depending on resource utilization and a reasonable amount of energy. This is still caused by constraints such as power resource management, authentication, key management, and flexibility protocols. To address these challenges, a novel approach called the modified gray wolf-based chameleon swarm (MGW-CS) algorithm is presented to select the cluster heads from WSN nodes. The MGW-CS algorithm is an amalgamation of both improved gray wolf optimizer and chameleon swarm algorithm. During cluster head selection, the QoS parameters such as network lifetime, energy efficiency, and throughput and packet delivery ratio are improved and optimized. By presenting a lightweight and flexible cryptographic model to manage encryption complexities, the encryption parameters are automatically selected based on the currently available resources of each sensor node for data encryption. Among various WSN nodes, data exchanging and secure communication are established with the development of novel authentication and a lightweight key management model. The proposed model is implemented using the NS-2 software. The proposed methodology offers a packet delivery rate of 98.6% for a total of 500 nodes when compared to the different state-of-art techniques.</p>", "Keywords": "chameleon swarm algorithm;improved gray wolf optimizer;lightweight encryption model and secure key management;wireless sensor network", "DOI": "10.1002/cpe.7295", "PubYear": 2022, "Volume": "34", "Issue": "26", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Scholar, Department of Computer Science and Engineering Annamalai University  Chidambaram India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of Computer Science and Engineering Annamalai University  Chidambaram India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Professor, Department of Computer Science and Engineering St<PERSON> Joseph's College of Engineering  Chennai India"}], "References": [{"Title": "Increasing fault tolerance ability and network lifetime with clustered pollination in wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2285", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A systematic literature review on general parameter control for evolutionary and swarm-based algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100777", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Chameleon Swarm Algorithm: A bio-inspired optimizer for solving engineering design problems", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114685", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Opposition grasshopper optimizer based multimedia data distribution using user evaluation strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "19", "Page": "29875", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Bee optimization based random double adaptive whale optimization model for task scheduling in cloud computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "35", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 96044075, "Title": "SoK: Fully Homomorphic Encryption over the [Discretized] Torus", "Abstract": "<p>First posed as a challenge in 1978 by <PERSON><PERSON><PERSON> et al., fully homomorphic encryption—the ability to evaluate any function over encrypted data—was only solved in 2009 in a breakthrough result by Gentry (Commun. ACM, 2010). After a decade of intense research, practical solutions have emerged and are being pushed for standardization.This paper explains the inner-workings of TFHE, a torus-based fully homomorphic encryption scheme. More exactly, it describes its implementation on a discretized version of the torus. It also explains in detail the technique of the programmable bootstrapping. Numerous examples are provided to illustrate the various concepts and definitions.</p>", "Keywords": "Fully homomorphic encryption;Discretized torus;TFHE;Programmable bootstrapping;Implementation", "DOI": "10.46586/tches.v2022.i4.661-692", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Zama, Paris, France"}], "References": []}, {"ArticleId": 96044076, "Title": "Faster Constant-Time Decoder for MDPC Codes and Applications to BIKE KEM", "Abstract": "<p>BIKE is a code-based key encapsulation mechanism (KEM) that was recently selected as an alternate candidate by the NIST’s standardization process on post-quantum cryptography. This KEM is based on the Niederreiter scheme instantiated with QC-MDPC codes, and it uses the BGF decoder for key decapsulation. We discovered important limitations of BGF that we describe in detail, and then we propose a new decoding algorithm for QC-MDPC codes called PickyFix. Our decoder uses two auxiliary iterations that are significantly different from previous approaches and we show how they can be implemented efficiently. We analyze our decoder with respect to both its error correction capacity and its performance in practice. When compared to BGF, our constant-time implementation of PickyFix achieves speedups of 1.18, 1.29, and 1.47 for the security levels 128, 192 and 256, respectively.</p>", "Keywords": "Post-quantum cryptography;BIKE;MDPC;LDPC;constant-time decoding", "DOI": "10.46586/tches.v2022.i4.110-134", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "Thales B. Paiva", "Affiliation": "University of Sao Paulo, Sao Paulo, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Sao Paulo, Sao Paulo, Brazil"}], "References": []}, {"ArticleId": 96044085, "Title": "Side-Channel Expectation-Maximization Attacks", "Abstract": "<p>Block ciphers are protected against side-channel attacks by masking. On one hand, when the leakage model is unknown, second-order correlation attacks are typically used. On the other hand, when the leakage model can be profiled, template attacks are prescribed. But what if the profiled model does not exactly match that of the attacked device?One solution consists in regressing on-the-fly the scaling parameters from the model. In this paper, we leverage an Expectation-Maximization (EM) algorithm to implement such an attack. The resulting unprofiled EM attack, termed U-EM, is shown to be both efficient (in terms of number of traces) and effective (computationally speaking). Based on synthetic and real traces, we introduce variants of our U-EM attack to optimize its performance, depending on trade-offs between model complexity and epistemic noise. We show that the approach is flexible, in that it can easily be adapted to refinements such as different points of interest and number of parameters in the leakage model.</p>", "Keywords": "Side-Channel Analysis;Masked Cryptography;Maximum Likelihood Distinguisher;Leakage Model Regression;Expectation Maximization (EM);Unprofiled EM (U-EM) Attack;Epistemic Noise", "DOI": "10.46586/tches.v2022.i4.774-799", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, Palaiseau, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, Palaiseau, France; Secure-IC S.A.S., Paris, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Secure-IC S.A.S., Paris, France; LTCI, Télécom Paris, Institut Polytechnique de Paris, Palaiseau, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, Palaiseau, France"}], "References": []}, {"ArticleId": 96044111, "Title": "Post-Quantum Authenticated Encryption against Chosen-Ciphertext Side-Channel Attacks", "Abstract": "<p>Over the last years, the side-channel analysis of Post-Quantum Cryptography (PQC) candidates in the NIST standardization initiative has received increased attention. In particular, it has been shown that some post-quantum Key Encapsulation Mechanisms (KEMs) are vulnerable to Chosen-Ciphertext Side-Channel Attacks (CC-SCA). These powerful attacks target the re-encryption step in the Fujisaki-Okamoto (FO) transform, which is commonly used to achieve CCA security in such schemes. To sufficiently protect PQC KEMs on embedded devices against such a powerful CC-SCA, masking at increasingly higher order is required, which induces a considerable overhead. In this work, we propose to use a conceptually simple construction, the ΕtS KEM, that alleviates the impact of CC-SCA. It uses the Encrypt-then-Sign (EtS) paradigm introduced by <PERSON> at ISW ’97 and further analyzed by <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> at EUROCRYPT ’02, and instantiates a postquantum authenticated KEM in the outsider-security model. While the construction is generic, we apply it to the CRYSTALS-Kyber KEM, relying on the CRYSTALSDilithium and Falcon signature schemes. We show that a CC-SCA-protected EtS KEM version of CRYSTALS-Kyber requires less than 10% of the cycles required for the CC-SCA-protected FO-based KEM, at the cost of additional data/communication overhead. We additionally show that the cost of protecting the EtS KEM against fault injection attacks, necessarily due to the added signature verification, remains negligible compared to the large cost of masking the FO transform at higher orders. Lastly, we discuss relevant embedded use cases for our EtS KEM construction.</p>", "Keywords": "Post-Quantum Cryptography;Side-Channel Attacks;Chosen-Ciphertext Attacks;Authenticated Key Exchange", "DOI": "10.46586/tches.v2022.i4.372-396", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "NXP Semiconductors, Hamburg, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NXP Semiconductors, Hamburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NXP Semiconductors, Hamburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "NXP Semiconductors, Hamburg, Germany"}], "References": []}, {"ArticleId": 96044114, "Title": "PVDF based piezoelectric condenser loudspeaker and microphone", "Abstract": "The piezocapacive transducers (PCTs) or condenser transducers (CTs) are helpful to transmit and record acoustic signals with better performance. The only drawback in this technology is high power consumption with low fidelity. Therefore in this study we have fabricated the two-in-one (TIO) acoustic transducers to transmit and record the sound signals with a single unit. For this purpose the CTs were made by simple technique of sandwiched layered structures with 3 cm<sup>2</sup> radius. In this study we reported that due to the unique antiferroelectric properties of k = 10 and 20 blends we have attained loudness of 98 dBSPL with the improvement of + 5 dBSPL at 65 Volts. For the reversed transduction i.e. condenser microphone unit we have attained 1 V/Pa at 1 Hz and 1 mV/Pa at 50 Hz with clear sound recognition between 23.5 and 440 Hz of input sound signal at 0 V dc bias. The enhancement in transduction properties were observed due to the combined piezoelectric and antiferroelectric properties of blends because of its quick-charge and quick-discharge (QCQD) characteristics. The low powered robust CTs with high fidelity are much needed for the futuristic applications.", "Keywords": "Ferroelectric ; Antiferroelectric ; Paraelectric ; Pyroelectric ; Transducer ; Loudspeaker ; Microphone", "DOI": "10.1016/j.sna.2022.113861", "PubYear": 2022, "Volume": "346", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, Jiangsu, China;School of Chemical and Materials Engineering, National University of Sciences and Technology (NUST) H-12, Islamabad 46000, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, Jiangsu, China;Corresponding author"}], "References": [{"Title": "Piezoelectric MEMS based acoustic sensors: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "301", "Issue": "", "Page": "111756", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 96044117, "Title": "VERICA - Verification of Combined Attacks: Automated formal verification of security against simultaneous information leakage and tampering", "Abstract": "<p>Physical attacks, including passive Side-Channel Analysis and active Fault Injection Analysis, are considered among the most powerful threats against physical cryptographic implementations. These attacks are well known and research provides many specialized countermeasures to protect cryptographic implementations against them. Still, only a limited number of combined countermeasures, i.e., countermeasures that protect implementations against multiple attacks simultaneously, were proposed in the past. Due to increasing complexity and reciprocal effects, design of efficient and reliable combined countermeasures requires longstanding expertise in hardware design and security. With the help of formal security specifications and adversary models, automated verification can streamline development cycles, increase quality, and facilitate development of robust cryptographic implementations.In this work, we revise and refine formal security notions for combined protection mechanisms and specifically embed them in the context of hardware implementations. Based on this, we present the first automated verification framework that can verify physical security properties of hardware circuits with respect to combined physical attacks. To this end, we conduct several case studies to demonstrate the capabilities and advantages of our framework, analyzing secure building blocks (gadgets), S-boxes build from Toffoli gates, and the ParTI scheme. For the first time, we reveal security flaws in analyzed structures due to reciprocal effects, highlighting the importance of continuously integrating security verification into modern design and development cycles.</p>", "Keywords": "SCA;FIA;Formal Verification;BDD;Symbolic Simulation;Combined Analysis", "DOI": "10.46586/tches.v2022.i4.255-284", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany; DFKI, Bremen, Germany"}], "References": []}, {"ArticleId": 96044121, "Title": "Randomness Optimization for Gadget Compositions in Higher-Order Masking", "Abstract": "<p>Physical characteristics of electronic devices, leaking secret and sensitive information to an adversary with physical access, pose a long-known threat to cryptographic hardware implementations. Among a variety of proposed countermeasures against such Side-Channel Analysis attacks, masking has emerged as a promising, but often costly, candidate. Furthermore, the manual realization of masked implementations has proven error-prone and often introduces flaws, possibly resulting in insecure circuits. In the context of automatic masking, a new line of research emerged, aiming to replace each physical gate with a secure gadget that fulfills well-defined properties, guaranteeing security when interconnected to a large circuit. Unfortunately, those gadgets introduce a significant amount of additional overhead into the design, in terms of area, latency, and randomness requirements.In this work, we present a novel approach to reduce the demands for randomness in such gadget-composed circuits by reusing randomness across gadgets while maintaining security in the probing adversary model. To this end, we embedded the corresponding optimization passes into an Electronic Design Automation toolchain, able to construct, optimize, and implement masked circuits, starting from an unprotected design. As such, our security-aware optimization offers an additional building block for existing or new Electronic Design Automation frameworks, where security is considered a first-class design constraint.</p>", "Keywords": "Masking;Probing Security;Strong Non-Interference;Probe Isolating Non-Interference;Security-Aware Optimization;Security-Aware EDA", "DOI": "10.46586/tches.v2022.i4.188-227", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Cologne, Institute for Computer Science, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany; DFKI, Bremen, Germany"}], "References": []}, {"ArticleId": 96044171, "Title": "Correction to: Mathematical Model of Permanent Magnets and Superconducting Coils", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10559-022-00480-z", "PubYear": 2022, "Volume": "58", "Issue": "3", "JournalId": 17407, "JournalTitle": "Cybernetics and Systems Analysis", "ISSN": "1060-0396", "EISSN": "1573-8337", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> National University of Kyiv, Kyiv, Ukraine"}, {"AuthorId": 2, "Name": "S. <PERSON><PERSON>", "Affiliation": "National Scientific Centre “Institute of Metrology”, Kharkiv, Ukraine"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON> Kharkiv National University of Economics, Kharkiv, Ukraine"}, {"AuthorId": 4, "Name": "V. S. Lyashko", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Institute of Cybernetics, National Academy of Sciences of Ukraine, Kyiv, Ukraine"}], "References": []}, {"ArticleId": 96044204, "Title": "Edge Node Aware Adaptive Data Processing Method for Ubiquitous NB-IoT", "Abstract": "<p>Nowadays, we have entered the golden age of economic development, and the Internet of Things (IoT) has also ushered in the opportunity of development, and the rapid development of IoT is also beneficial to drive the economy forward continuously. Therefore, the research on IoT is very meaningful and meets the contemporary development needs and greatly facilitates people’s daily life through the interconnection of all things. In today’s era of massive data, all kinds of data are complicated and messy, and if the large amount of data obtained is not properly classified and processed, the major problem will be that a pile of disordered and messy data is generated, and it is impossible to find the corresponding useful, engineering value, and regular data among them, and then, such data can only be discarded. Such a simple and brutal way of data processing is not only a waste of data resources but also may inadvertently throw away important, confidential, and private data information. If such data is carelessly discarded, the consequences will be incalculable, because such data information is likely to be used, processed, and disseminated by unscrupulous elements, which will eventually result in the following consequences: for individuals, it is equivalent to making their privacy public, which will seriously affect all aspects of life; for enterprises, if confidential data information is disseminated, then it will bring unpredictable losses to the enterprise. The adaptive data processing method for edge node sensing in ubiquitous NB-IoT can make the data generated from NB-IoT modules in ubiquitous IoT have practical engineering application value after processing, so the data source of this paper is the IoT data generated from NB-IoT communication modules in ubiquitous network (called NB-IoT dataset in this paper). The experimental results show that the accuracy of the adaptive data classification achieved by these two algorithms reaches about 75%, which provides some help to improve the efficiency of data utilization.</p>", "Keywords": "", "DOI": "10.1155/2022/9006152", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Creativity and Art, ShanghaiTech University, Shanghai 201210, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Design and Innovation, Tongji University, Shanghai 200092, China"}], "References": [{"Title": "RETRACTED ARTICLE: Statement of retraction: research on digital image wavelet transform filtering optimization processing method based on DSP Internet of Things", "Authors": "", "PubYear": 2022, "Volume": "44", "Issue": "6", "Page": "597", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "Exploring the role of sports APP in (campus fitness) intelligent solutions using data fusion algorithm and internet of things", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "40", "JournalTitle": "International Journal of Grid and Utility Computing"}, {"Title": "Latency and Energy-Awareness in Data Stream Processing for Edge Based IoT Systems", "Authors": "<PERSON><PERSON><PERSON>; Atslands R da Rocha; <PERSON>", "PubYear": 2022, "Volume": "20", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Grid Computing"}]}, {"ArticleId": 96044220, "Title": "Comparison of Naive <PERSON> Method and Certainty Factor for Diagnosis of Preeclampsia", "Abstract": "<p>Preeclampsia is a disease often suffered by pregnant women caused by several factors such as a history of heredity, blood pressure, urine protein, and diabetes. The data sample used in this study is data on pregnant women in the 2020 time period recorded at health services in the former Cilacap Regency. This study was conducted to compare the final results of the Naive Bayes method and the certainty factor method in providing the results of a diagnosis of preeclampsia seen from the symptoms experienced by these pregnant women. The naïve Bayes approach provides decisions by managing statistical data and probabilities taken from the prediction of the likelihood of a pregnant woman showing symptoms of preeclampsia. Symptoms of preeclampsia, while the certainty factor method determines the certainty value of the diagnosis of preeclampsia in pregnant women based on the calculation of the CF value. The research output compares the two methods, showing that the certainty factor method provides more accurate diagnostic results than the Naive <PERSON>es method. It happens because the CF method requires a minimum value of 0.2 and a maximum of 1 for each rule on the factors/symptoms involved, while the Naive Bayes method only requires values of 0 and 1 for each factor causing preeclampsia in pregnant women.\r  </p>", "Keywords": "", "DOI": "10.24843/LKJITI.2022.v13.i02.p04", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 46965, "JournalTitle": "Lontar Komputer : <PERSON><PERSON> Ilmiah Teknologi Informasi", "ISSN": "2088-1541", "EISSN": "2541-5832", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politeknik Negeri Cilacap"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Politeknik Negeri Cilacap"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Politeknik Negeri Cilacap"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Midwifery, STIKES Graha Mandiri Cilacap"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Politeknik Negeri Cilacap"}], "References": []}, {"ArticleId": 96044229, "Title": "Vigenère Cipher Algorithm Optimization for Digital Image Security using SHA512", "Abstract": "<p>One of the popular cryptographic algorithms is the Vigenère Cipher. This algorithm is included in classical cryptographic algorithms, so its capabilities are limited to text-type data. Through this research, this research try to modify the Vigenère Cipher so that it can be used on digital image media. The improvement is performed using ASCII code as a Vigenère table and the key generated by the SHA512 hash technique with salt. The encryption and decryption process was carried out on ten jpg and ten png files and showed a 100% success rate. Speed and memory consumption tests on the encryption process by comparing it with the AES algorithm show that AES excels in speed with 409,467 Mb/s while Vigenère wins in memory consumption by utilizing only 5,0007 Kb for every Kilobytes of the processed digital image file.\r  </p>", "Keywords": "", "DOI": "10.24843/LKJITI.2022.v13.i02.p02", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 46965, "JournalTitle": "Lontar Komputer : <PERSON><PERSON> Ilmiah Teknologi Informasi", "ISSN": "2088-1541", "EISSN": "2541-5832", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 96044356, "Title": "Towards personalized environment‐aware outdoor gait analysis using a smartphone", "Abstract": "<p>Automatic gait analysis in free-living environments using inertial sensors requires individualized approach as local acceleration and velocity profiles vary with the walker and the topological properties of the environment (e.g., walking in the forest vs. walking on sand). Here, we propose a smartphone-based gait assessment architecture which consists of two data processing modules. The first module employs a set of personalized classifiers for automatic recognition of the walking environment. The second module provides accurate step time estimates by selecting the optimal filtering frequency tailored to the predicted environment. The performance of the architecture was evaluated using experimental data collected from 10 participants walking in 10 different conditions typically encountered during daily living. Compared with ground truth data, the architecture successfully recognized the walking environments; the percentage of correctly classified instances was above 92%. It also estimated step time with high accuracy; the mean absolute error was less than 10 ms, outperforming or at the very least matching the performance levels achieved in controlled laboratory trials (indoor flat surface walking). Compared with using one filtering frequency for all environments, using optimal frequency tailored to each environment reduced step time estimation error by more than 39%. To the best of our knowledge, this is the first study which successfully demonstrates that parameter tuning can improve gait characterization in outdoor environments. However, further research using a larger data set (including more participants with varying demographics and degree of impairment) is needed to confirm this result. Our findings highlight the importance of environment-aware gait analysis, and lay the groundwork for a smartphone-based technology that can be used in the community.</p>", "Keywords": "computational intelligence;gait analysis;outdoor environments;smartphone;wearable sensors", "DOI": "10.1111/exsy.13130", "PubYear": 2023, "Volume": "40", "Issue": "5", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Aberystwyth University  Wales UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science Aberystwyth University  Wales UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Aberystwyth University  Wales UK"}], "References": [{"Title": "Applying deep neural networks and inertial measurement unit in recognizing irregular walking differences in the real world", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "103414", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 96044373, "Title": "Cross-Comparison of channel parameters between FY-3E/MERSI-LL and Himawari-8/AHI in China", "Abstract": "Fengyun-3E (FY-3E) is the world’s first operational meteorological satellite on dawn-dusk orbit. In order to realize the generality of algorithms for different satellites and provide a basis for subsequent inversion studies on remote sensing parameters, such as land surface temperature, fire, and atmospheric water vapour, we perform a cross-comparison of the brightness temperature (TB) of the six infrared (IR) channels between the Medium Resolution Spectral Imager Level L onboard the FY-3E satellite (FY-3E/MERSI-LL) and the Advanced Himawari Imager onboard the Himawari-8 satellite (Himawari-8/AHI). The statistical indicators, correlation coefficient, root mean square error (RMSE) and mean bias, are used to analyse the consistency and differences of the channel parameters between the MERSI-LL and AHI in terms of overall data, different regions and different TB. The results show that the TB data observed by the FY-3E/MERSI-LL and Himawari-8/AHI are generally consistent, with correlation coefficients of more than 0.99, mean biases of less than 1.60 K and RMSEs of less than 1.90 K. In terms of different channels, the TB in channel IR 038 has the best consistency between the two sensors, while the results from the MERSI-LL are higher than those of the AHI for channels IR038, IR086, IR108 and IR120, and lower than those of the AHI for channel IR041. Analysing the situation in different regions, we find that the MERSI-LL and AHI data are highly consistent and relatively stable in the Huang-Huai Region, with low RMSEs, low mean biases and high correlation coefficients (more than 0.95) at all channels. For the different TB ranges, the RMSEs and mean biases for the TB ranges of 270–280 K and 280–290 K between the MERSI-LL and AHI are small, and the data in the 290–300 K TB range has great differences and fluctuations between the two sensors.", "Keywords": "FY-3E/MERSI-LL ; Himawari-8/AHI ; Cross-comparsion ; Evaluation indicator", "DOI": "10.1080/01431161.2022.2116297", "PubYear": 2022, "Volume": "43", "Issue": "12", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Heilongjiang Eco-meteorology Center, Heilongjiang Meteorological Bureau, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Heilongjiang Eco-meteorology Center, Heilongjiang Meteorological Bureau, Harbin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Radiometric Calibration and Validation for Environmental Satellites, National Satellite Meteorological Center (National Center for Space Weather), China Meteorological Administration, Innovation Center for FengYun Meteorological Satellite(FYSIC), Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Radiometric Calibration and Validation for Environmental Satellites, National Satellite Meteorological Center (National Center for Space Weather), China Meteorological Administration, Innovation Center for FengYun Meteorological Satellite(FYSIC), Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Radiometric Calibration and Validation for Environmental Satellites, National Satellite Meteorological Center (National Center for Space Weather), China Meteorological Administration, Innovation Center for FengYun Meteorological Satellite(FYSIC), Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Heilongjiang Eco-meteorology Center, Heilongjiang Meteorological Bureau, Harbin, China"}], "References": []}, {"ArticleId": 96044438, "Title": "Optimization of EDM process parameters based on variable-fidelity surrogate model", "Abstract": "<p>To balance the cost and accuracy in the optimization of EDM process parameters, the variable-fidelity surrogate model (VFM) was introduced in this paper to replace the implicit relationship between the EDM process parameters (peak current, duty cycle, pulse period) and performance functions (material remove rate, MRR, and surface roughness, Ra). Low-fidelity (LF) samples were obtained by using the low-cost EDM heat flow simulation, and high-fidelity (HF) samples were obtained by using the EDM machining experiments. By fusing the LF and HF samples, it is possible to establish an accurate expression of the implicit relationship between the EDM process parameters and performance functions in the variable-fidelity framework. Finally, the EDM process parameter model was established, and sequential quadratic programming (SQP) was used to obtain the optimal solution by calling the VFM. Through the verification experiments, the results showed that the parameter combination obtained by VFM is 15.1% higher than the MRR of the low-fidelity model (LFM) and 13.0% higher than the high-fidelity model (HFM), which reaches 88.285 mg/min, while the Ra has slightly decreased and is within the constraint range. From the above research, it can be concluded that the proposed technology has significant advantages and application potential in the field of EDM machining optimization.</p>", "Keywords": "EDM; Process parameter optimization; Variable-fidelity surrogate model; Co-Kriging", "DOI": "10.1007/s00170-022-09963-x", "PubYear": 2022, "Volume": "122", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Henan Key Laboratory of Mechanical Equipment Intelligent Manufacturing, School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 2, "Name": "Chunyang Yin", "Affiliation": "Henan Key Laboratory of Mechanical Equipment Intelligent Manufacturing, School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Key Laboratory of Mechanical Equipment Intelligent Manufacturing, School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 4, "Name": "Xinyu Han", "Affiliation": "China Railway Engineering Equipment Group Co., Ltd., Zhengzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Key Laboratory of Mechanical Equipment Intelligent Manufacturing, School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Railway Engineering Equipment Group Co., Ltd., Zhengzhou, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Henan Key Laboratory of Mechanical Equipment Intelligent Manufacturing, School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Key Laboratory of Mechanical Equipment Intelligent Manufacturing, School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Zhengzhou, China"}], "References": []}, {"ArticleId": 96044454, "Title": "DeepNAVI: A deep learning based smartphone navigation assistant for people with visual impairments", "Abstract": "Navigation assistance is an active research area, where one aim is to foster independent living for people with vision impairments. Despite the fact that many navigation assistants use advanced technologies and methods, we found that they did not explicitly address two essential requirements in a navigation assistant - portability and convenience. It is equally imperative in designing a navigation assistant for the visually impaired that the device is portable and convenient to use without much training. Some navigation assistants do not provide users with detailed information about the obstacle types that can be detected, which is essential to make informed decisions when navigating in real-time. To address these gaps, we propose DeepNAVI, a smartphone-based navigation assistant that leverages deep learning competence. Besides providing information about the type of obstacles present, our system can also provide information about their position, distance from the user, motion status, and scene information. All this information is offered to users through audio mode without compromising portability and convenience. With a small model size and rapid inference time, our navigation assistant can be deployed on a portable device such as a smartphone and work seamlessly in a real-time environment. We conducted a pilot test with a user to assess the usefulness and practicality of the system. Our testing results indicate that our system has the potential to be a practical and useful navigation assistant for the visually impaired.", "Keywords": "Navigation assistant ; Deep learning ; Blind ; Visual impairment ; Portable ; Smartphone", "DOI": "10.1016/j.eswa.2022.118720", "PubYear": 2023, "Volume": "212", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Oslo Metropolitan University, Oslo, Norway;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Oslo Metropolitan University, Oslo, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Oslo Metropolitan University, Oslo, Norway"}], "References": [{"Title": "The Open Images Dataset V4", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "7", "Page": "1956", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "A scene perception system for visually impaired based on object detection and classification using CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "", "Page": "03039", "JournalTitle": "ITM Web of Conferences"}]}, {"ArticleId": 96044469, "Title": "Constraint-Induced Symmetric Nonnegative Matrix Factorization for Accurate Community Detection", "Abstract": "As a fundamental characteristic of an undirected network, community reveals its networking organization and functional mechanisms, making community detection be a highly-interesting issue in network representation learning. With great interpretability, a symmetric and nonnegative matrix factorization (SNMF)-based approach is frequently adopted to tackle this issue. However, it only adopts a unique feature matrix for describing the symmetry of an undirected network, which unfortunately results in a reduced feature space that evidently impairs its representation learning ability. Motivated by this discovery, this paper proposes a novel C onstraint-induced S ymmetric N onnegative M atrix F actorization (C-SNMF) model that adopts three-fold ideas: a) Representing a target undirected network with multiple latent feature matrices, thus preserving its representation learning capacity; b) Incorporating a symmetry-regularizer into its objective function, which preserves the symmetry of the learnt low-rank approximation to the adjacency matrix, thereby making the resultant detector precisely illustrate the target network&#x27;s symmetry; and c) Introducing a graph-regularizer that preserves local invariance of the network&#x27;s intrinsic geometry into its learning objective, thus making the achieved detector well-aware of community structure within the target network. Note that the regularization coefficients are selected according to the modularity of the learnt community structure on the training data only, thereby greatly improving the achieved model&#x27;s practical significance for real applications. Experimental results on six real-world networks demonstrate that the proposed C-SNMF model significantly outperforms the benchmarks and state-of-the-art models in achieving highly-accurate community detection results.", "Keywords": "Undirected Network ; Social Network ; Information Fusion ; Network Representation Learning ; Community Detection ; Nonnegative Matrix Factorization ; Symmetry-regularization ; Multiple Constraints", "DOI": "10.1016/j.inffus.2022.08.031", "PubYear": 2023, "Volume": "89", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China;Chongqing Key Laboratory of Big Data and Intelligent Computing, Chongqing Institute of Green and Intelligent Technology, Chinese Academy of Sciences, Chongqing, 400714, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University, Chongqing 400715, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Brunel University London, Uxbridge, Middlesex, UB8 3PH, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Brunel University London, Uxbridge, Middlesex, UB8 3PH, United Kingdom"}], "References": [{"Title": "Social network community analysis based large-scale group decision making approach with incomplete fuzzy preference relations", "Authors": "<PERSON><PERSON> Chu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "98", "JournalTitle": "Information Fusion"}, {"Title": "Social network community analysis based large-scale group decision making approach with incomplete fuzzy preference relations", "Authors": "<PERSON><PERSON> Chu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "98", "JournalTitle": "Information Fusion"}, {"Title": "Advancing Non-Negative Latent Factorization of Tensors With Diversified Regularization Schemes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "1334", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Image retrieval from remote sensing big data: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "94", "JournalTitle": "Information Fusion"}, {"Title": "Human face recognition based on convolutional neural network and augmented dataset", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "sup2", "Page": "29", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Stability analysis for delayed neural networks based on a generalized free-weighting matrix integral inequality", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "sup1", "Page": "6", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Recursive filtering of networked nonlinear systems: a survey", "Authors": "<PERSON><PERSON> Mao; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "6", "Page": "1110", "JournalTitle": "International Journal of Systems Science"}, {"Title": "A survey on sliding mode control for networked control systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "6", "Page": "1129", "JournalTitle": "International Journal of Systems Science"}, {"Title": "An improved particle swarm optimization algorithm with adaptive weighted delay velocity", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "188", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Path planning for coal mine robot via improved ant colony optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "283", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "A survey on control for Takagi-Sugeno fuzzy systems subject to engineering-oriented complexities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "334", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Communication-protocol-based analysis and synthesis of networked systems: progress, prospects and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "14", "Page": "3013", "JournalTitle": "International Journal of Systems Science"}, {"Title": "APAL: Adjacency Propagation Algorithm for overlapping community detection in biological networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "574", "JournalTitle": "Information Sciences"}, {"Title": "A review: data driven-based fault diagnosis and RUL prediction of petroleum machinery and equipment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "724", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "A survey on state estimation of complex dynamical networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "16", "Page": "3351", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Resource-efficient and secure distributed state estimation over wireless sensor networks: a survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "16", "Page": "3368", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Fault detection of networked dynamical systems: a survey of trends and techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "16", "Page": "3390", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Multi-sensor filtering fusion meets censored measurements under a constrained network environment: advances, challenges and prospects", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "16", "Page": "3410", "JournalTitle": "International Journal of Systems Science"}, {"Title": "A survey of outlier detection in high dimensional data streams", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "", "Page": "100463", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 96044487, "Title": "Codes with low-dimensional hull", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICOT.2022.10050374", "PubYear": 2022, "Volume": "6", "Issue": "1", "JournalId": 26361, "JournalTitle": "International Journal of Information and Coding Theory", "ISSN": "1753-7703", "EISSN": "1753-7711", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "VIDYA SAGAR", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96044627, "Title": "PROLEAD: A Probing-Based Hardware Leakage Detection Tool", "Abstract": "<p>Even today, Side-Channel Analysis attacks pose a serious threat to the security of cryptographic implementations fabricated with low-power and nanoscale feature technologies. Fortunately, the masking countermeasures offer reliable protection against such attacks based on simple security assumptions. However, the practical application of masking to a cryptographic algorithm is not trivial, and the designer may overlook possible security flaws, especially when masking a complex circuit. Moreover, abstract models like probing security allow formal verification tools to evaluate masked implementations. However, this is computationally too expensive when dealing with circuits that are not based on composable gadgets. Unfortunately, using composable gadgets comes at some area overhead. As a result, such tools can only evaluate subcircuits, not their compositions, which can become the Achilles’ heel of such masked implementations.In this work, we apply logic simulations to evaluate the security of masked implementations which are not necessarily based on composable gadgets. We developed PROLEAD, an automated tool analyzing the statistical independence of simulated intermediates probed by a robust probing adversary. Compared to the state of the art, our approach (1) does not require any power model as only the state of a gate-level netlist is simulated, (2) can handle masked full cipher implementations, and (3) can detect flaws related to the combined occurrence of glitches and transitions as well as higher-order multivariate leakages. With PROLEAD, we can evaluate masked mplementations that are too complex for existing formal verification tools while being in line with the robust probing model. Through PROLEAD, we have detected security flaws in several publicly-available masked implementations, which have been claimed to be robust probing secure.</p>", "Keywords": "Side-Channel Analysis;Leakage Detection;Hardware", "DOI": "10.46586/tches.v2022.i4.311-348", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ruhr University Bochum, Horst Görtz Institute for IT Security, Bochum, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Cologne, Institute for Computer Science, Cologne, Germany"}], "References": []}, {"ArticleId": 96044787, "Title": "Milling performance of Inconel 718 based on DC short electric arc machining with graphite and W-Ag electrode materials", "Abstract": "<p>In order to solve the problems of poor dimensional accuracy, high-specific energy consumption (SEC), and poor surface integrity in DC short electric arc machining (SEAM), this paper proposes to use W-Ag alloy as the electrode material for machining nickel-based alloy (Inconel 718). Firstly, the effect of voltage and electrode material (graphite and W-Ag alloy) on the material removal rate (MRR), relative electrode wear rate (REWR), surface roughness (Sa), and SEC were investigated, and current and voltage during machining were measured by a multichannel data acquisition system. In addition, the surface morphology, cross section, and element distribution after machining were analyzed. The experimental results showed that W-Ag alloy as a tool electrode can obtain better performance in SEAM; its MRR reach 6314 mm<sup>3</sup>/min at 30 V which is twice as high as graphite electrode. The SEC of W-Ag alloy electrode is 32.64 kJ/cm<sup>3</sup>, significantly lower than that of graphite electrode with 84.16 kJ/cm<sup>3</sup>. The surface roughness is lower after machining with W-Ag alloy electrode, and the thermal damage layer is only half of that machined with graphite electrode.</p>", "Keywords": "Inconel 718; Electrode with W-Ag; Short electric arc machining (SEAM); Material removal rate (MRR); Specific energy consumption (SEC); Dimensional accuracy", "DOI": "10.1007/s00170-022-10029-1", "PubYear": 2022, "Volume": "122", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Xinjiang University, Urumqi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 3, "Name": "Xiangyu Dai", "Affiliation": "School of Mechanical Engineering, Xinjiang University, Urumqi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Xinjiang University, Urumqi, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Xinjiang University, Urumqi, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Xinjiang University, Urumqi, China"}], "References": [{"Title": "Milling performance of titanium alloy based on short electric arc machining with direct current power source", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "5-6", "Page": "1641", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental study on high-efficiency DC short electric arc milling of titanium alloy Ti6Al4V", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "9-10", "Page": "2775", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 96044953, "Title": "Multi-objects optimization in µ-EDM using AlCrNi-coated tungsten carbide electrode for Ti-6AL-4 V", "Abstract": "<p>Titanium alloy (Ti-6Al-4 V) is widely used in a number of fields such as medical, aeronautical, and nuclear fields. Since the surface finish should be enhanced as much possible in efficient way, the micro EDM process can be used for machining such materials. The process mechanism can be enhanced by utilizing the coated electrode in the process. In the present study, an effort was made to investigate the effects of process parameters on drilling titanium alloy with aluminum chromium nitride (AlCrNi)-coated aluminum electrode in micro EDM process. An optimization approach was also implemented using Taguchi-DEAR approach. From the experimental interpretation, the conclusions were made as follows. The AlCrNi-coated aluminum electrode can enhance the machinability of micro EDM process. The optimal combination of voltage (140 V), capacitance (10000pF), and spindle rotation (200 rpm) could provide better quality measures among the chosen variables. The moderate voltage and capacitance can remove the material effectively with lower overcut, since the electrical conductivity can effectively enhance the conductivity of the plasma energy column. The coated electrode can create the tiny craters with uniform energy distribution. </p>", "Keywords": "Micro EDM; AlCrNi; Titanium; DEAR; Surface", "DOI": "10.1007/s00170-022-10022-8", "PubYear": 2022, "Volume": "122", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hanoi University of Industry, Ha Noi, Vietnam"}, {"AuthorId": 2, "Name": " Muthuramalingam T.", "Affiliation": "Department of Mechatronics Engineering, SRM Institute of Science and Technology, Kattankulathur, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hanoi University of Industry, Ha Noi, Vietnam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering Department, RIT, Rajaramnagar Affiliated to SUK, Rajaramnagar, India"}, {"AuthorId": 5, "Name": "Dung Hoang Tien", "Affiliation": "Hanoi University of Industry, Ha Noi, Vietnam"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hanoi University of Industry, Ha Noi, Vietnam"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Hanoi University of Industry, Ha Noi, Vietnam"}], "References": [{"Title": "Influence of micro size titanium powder-mixed dielectric medium on surface quality measures in EDM process", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "Page": "797", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Multi-object optimization of EDM by Taguchi-DEAR method using AlCrNi coated electrode", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "5-6", "Page": "1429", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A study on ultrasonic-assisted micro-EDM of titanium alloy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "Page": "2815", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study on debris evacuation of EDM small hole processing on titanium alloy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "Page": "2335", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "The state of the art of electrical discharge drilling: a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "5-6", "Page": "2947", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 96044954, "Title": "Presentation of encryption method for RGB images based on an evolutionary algorithm using chaos functions and hash tables", "Abstract": "<p>In this study, a new method based on chaos functions, and the evolutionary algorithm is proposed for image encryption. Chaos functions are used in this method because of the random occurrence and the sensitivity to the initial values to make the encryption method as secure as possible. Also to enhance the entropy of the image, an evolutionary algorithm is used to select the best layout and mapping. For this purpose, the image is decomposed first. The image components are then disrupted using the evolutionary algorithm, coding rules, and logistic mapping whose initial value is obtained from a hash function. The results show that the proposed method has good speed due to the use of simple operators such as Addition and XOR. Also, since a 256-bit hash function is used in this case and a high search space is generated for the evolutionary algorithm, the algorithm shows good resistance to the types of attacks. Moreover, due to the uncertainty of the decryption algorithm and the generation of a single-use code for each execution of the algorithm, the proposed encryption algorithm offers high security and resistance against differential attacks and plaintext attacks.</p>", "Keywords": "Image encryption; Evolutionary algorithm; DNA encoding; Logistic mapping function; Hash function", "DOI": "10.1007/s11042-022-13734-x", "PubYear": 2023, "Volume": "82", "Issue": "6", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Babol Noshirvani University of Technology, Babol, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Babol Noshirvani University of Technology, Babol, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Babol Noshirvani University of Technology, Babol, Iran"}], "References": [{"Title": "A chaotic colour image encryption scheme combining <PERSON> transform and <PERSON> maps", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "1289", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Image encryption algorithm for synchronously updating Boolean networks based on matrix semi-tensor product theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "16", "JournalTitle": "Information Sciences"}, {"Title": "An efficient image encryption using non-dominated sorting genetic algorithm-III based 4-D chaotic maps", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "1309", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A logistic mapping-based encryption scheme for Wireless Body Area Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "57", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A novel image security technique based on nucleic acid concepts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "102505", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "An image encryption scheme based on a hybrid model of DNA computing, chaotic systems and hash functions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "Page": "24993", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 96045006, "Title": "Hidden power: Impact of the “snippet” on online consumer engagement", "Abstract": "With the increasing influence of online reviews, platforms hosting those reviews have played a central role shaping the review display rendering a snippet of text readily visible, with the remainder hidden, accessible upon request. Using data from 122,438 written reviews from 62,380 reviewers for 1,874 nonchain Atlanta restaurants on TripAdvisor, our research is the first to examine the impact of this display feature on ensuring consumer engagement through casting helpful votes. Based on consumer behavior theories, topic modeling, and sentiment analysis, we demonstrate that hidden content is more effective in garnering helpful votes than visible content. We also find that in hidden content, negativity bias diminishes and the effectiveness of information about features is enhanced. We identify how perceived purchase risk accentuates snippet effectiveness. Our findings provide guidance to platforms and reviewers on prioritizing content and opens avenues on an important aspect of online review display and related user behavior.", "Keywords": "Online consumer reviews ; Snippet display ; Commodity theory ; Effort justification ; Confirmation bias ; Elaboration likelihood model", "DOI": "10.1016/j.elerap.2022.101196", "PubYear": 2022, "Volume": "55", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Pennsylvania State University, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Georgia State University, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Georgia State University, United States;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Winston-Salem State University, United States"}], "References": []}, {"ArticleId": 96045031, "Title": "Phase Portrait of Electrocardiogram as a Means Of Biometry", "Abstract": "<p>The author develops an approach to constructing biometric systems based on the analysis of the phase portrait of a single channel electrocardiogram (ECG) of the test subject. The rules for solving the problem of human identification and verification (authentication) are proposed. Experimental studies have shown that the proposed decision rules provide 96.6% reliability of identification in the analysis of 3,133 ECGs of 167 individuals and 99.5% reliability of verification in the analysis of 204 ECGs of 62 different individuals. Prospects for further research in solving practical biometric problems are outlined.</p>", "Keywords": "electrocardiogram; phase portrait; biometric system; Hausdorff distance; identification and verification of personality", "DOI": "10.1007/s10559-022-00479-6", "PubYear": 2022, "Volume": "58", "Issue": "3", "JournalId": 17407, "JournalTitle": "Cybernetics and Systems Analysis", "ISSN": "1060-0396", "EISSN": "1573-8337", "Authors": [{"AuthorId": 1, "Name": "L. S. Fainzilberg", "Affiliation": "International Scientific and Training Center of Information Technologies and Systems, National Academy of Sciences of Ukraine and Ministry of Education and Science of Ukraine, Kyiv, Ukraine"}], "References": [{"Title": "New Approaches to the Analysis and Interpretation of the Shape of Cyclic Signals", "Authors": "L. S. Fainzilberg", "PubYear": 2020, "Volume": "56", "Issue": "4", "Page": "665", "JournalTitle": "Cybernetics and Systems Analysis"}, {"Title": "Svm for human identification using the ECG signal", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "430", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 96045035, "Title": "Increasing the Semantic Storage Density of Sparse Distributed Memory", "Abstract": "<p>The integration of the compressive sensing (CS) method in the sparse distributed memory (SDM) implementation is proposed for increasing the storage capacity for binary sparse distributed representations of semantics, particularly, in graphics processing units (GPUs).</p>", "Keywords": "sparse distributed memory (SDM); compressive sensing (CS); associative memory; binary sparse distributed representations; neural networks; GPU", "DOI": "10.1007/s10559-022-00465-y", "PubYear": 2022, "Volume": "58", "Issue": "3", "JournalId": 17407, "JournalTitle": "Cybernetics and Systems Analysis", "ISSN": "1060-0396", "EISSN": "1573-8337", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Institute of Cybernetics, National Academy of Sciences of Ukraine, Kyiv, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Institute of Cybernetics, National Academy of Sciences of Ukraine, Kyiv, Ukraine"}], "References": [{"Title": "A comparison of vector symbolic architectures", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "6", "Page": "4523", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 96045077, "Title": "Virtual CT Myelography: A Patch-Based Machine Learning Model to Improve Intraspinal Soft Tissue Visualization on Unenhanced Dual-Energy Lumbar Spine CT", "Abstract": "<p>Background: Distinguishing between the spinal cord and cerebrospinal fluid (CSF) non-invasively on CT is challenging due to their similar mass densities. We hypothesize that patch-based machine learning applied to dual-energy CT can accurately distinguish CSF from neural or other tissues based on the center voxel and neighboring voxels. Methods: 88 regions of interest (ROIs) from 12 patients’ dual-energy (100 and 140 kVp) lumbar spine CT exams were manually labeled by a neuroradiologist as one of 4 major tissue types (water, fat, bone, and nonspecific soft tissue). Four-class classifier convolutional neural networks were trained, validated, and tested on thousands of nonoverlapping patches extracted from 82 ROIs among 11 CT exams, with each patch representing pixel values (at low and high energies) of small, rectangular, 3D CT volumes. Different patch sizes were evaluated, ranging from 3 × 3 × 3 × 2 to 7 × 7 × 7 × 2. A final ensemble model incorporating all patch sizes was tested on patches extracted from six ROIs in a holdout patient. Results: Individual models showed overall test accuracies ranging from 99.8% for 3 × 3 × 3 × 2 patches (N = 19,423) to 98.1% for 7 × 7 × 7 × 2 patches (N = 1298). The final ensemble model showed 99.4% test classification accuracy, with sensitivities and specificities of 90% and 99.6%, respectively, for the water class and 98.6% and 100% for the soft tissue class. Conclusions: Convolutional neural networks utilizing local low-level features on dual-energy spine CT can yield accurate tissue classification and enhance the visualization of intraspinal neural tissue.</p>", "Keywords": "dual-energy computed tomography; spine imaging; spinal canal; spinal cord; convolutional neural networks; CT segmentation dual-energy computed tomography ; spine imaging ; spinal canal ; spinal cord ; convolutional neural networks ; CT segmentation", "DOI": "10.3390/info13090412", "PubYear": 2022, "Volume": "13", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory for Augmented Intelligence in Imaging, Department of Radiology, Ohio State University College of Medicine, Columbus, OH 43210, USA↑Division of Neuroradiology, Department of Radiology, Ohio State University College of Medicine, Columbus, OH 43210, USA; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Biomedical Science, Ohio State University College of Medicine, Columbus, OH 43210, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory for Augmented Intelligence in Imaging, Department of Radiology, Ohio State University College of Medicine, Columbus, OH 43210, USA; Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory for Augmented Intelligence in Imaging, Department of Radiology, Ohio State University College of Medicine, Columbus, OH 43210, USA↑Current Affiliation: Department of Computer Engineering, Eskişehir Technical University, Eskişehir 26555, Turkey"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Division of Neuroradiology, Department of Radiology, Ohio State University College of Medicine, Columbus, OH 43210, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Laboratory for Augmented Intelligence in Imaging, Department of Radiology, Ohio State University College of Medicine, Columbus, OH 43210, USA↑Division of Neuroradiology, Department of Radiology, Ohio State University College of Medicine, Columbus, OH 43210, USA"}], "References": [{"Title": "Spine and Individual Vertebrae Segmentation in Computed Tomography Images Using Geometric Flows and Shape Priors", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "", "Page": "66", "JournalTitle": "Frontiers in Computer Science"}]}, {"ArticleId": 96045092, "Title": "Redshift: Manipulating Signal Propagation Delay via Continuous-Wave Lasers", "Abstract": "<p>We propose a new laser injection attack Redshift that manipulates signal propagation delay, allowing for precise control of oscillator frequencies and other behaviors in delay-sensitive circuits. The target circuits have a significant sensitivity to light, and a low-power continuous-wave laser, similar to a laser pointer, is sufficient for the attack. This is in contrast to previous fault injection attacks that use highpowered laser pulses to flip digital bits. This significantly reduces the cost of the attack and extends the range of possible attackers. Moreover, the attack potentially evades sensor-based countermeasures configured for conventional pulse lasers. To demonstrate Redshift, we target ring-oscillator and arbiter PUFs that are used in cryptographic applications. By precisely controlling signal propagation delays within these circuits, an attacker can control the output of a PUF to perform a state-recovery attack and reveal a secret key. We finally discuss the physical causality of the attack and potential countermeasures.</p>", "Keywords": "Laser Fault Injection;Physically Unclonable Function;Delay-Sensitive Circuits;Oscillator", "DOI": "10.46586/tches.v2022.i4.463-489", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, MI, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Massachusetts, Amherst, MA, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, Tokyo, Japan"}], "References": []}, {"ArticleId": 96045107, "Title": "Perceived Information Revisited: New Metrics to Evaluate Success Rate of Side-Channel Attacks", "Abstract": "<p>In this study, we present new analytical metrics for evaluating the performance of side-channel attacks (SCAs) by revisiting the perceived information (PI), which is defined using cross-entropy (CE). PI represents the amount of information utilized by a probability distribution that determines a distinguishing rule in SCA. Our analysis partially solves an important open problem in the performance evaluation of deep-learning based SCAs (DL-SCAs) that the relationship between neural network (NN) model evaluation metrics (such as accuracy, loss, and recall) and guessing entropy (GE)/success rate (SR) is unclear. We first theoretically show that the conventional CE/PI is non-calibrated and insufficient for evaluating the SCA performance, as it contains uncertainty in terms of SR. More precisely, we show that an infinite number of probability distributions with different CE/PI can achieve an identical SR. With the above analysis result, we present a modification of CE/PI, named effective CE/PI (ECE/EPI), to eliminate the above uncertainty. The ECE/EPI can be easily calculated for a given probability distribution and dataset, which would be suitable for DL-SCA. Using the ECE/EPI, we can accurately evaluate the SR hrough the validation loss in the training phase, and can measure the generalization of the NN model in terms of SR in the attack phase. We then analyze and discuss the proposed metrics regarding their relationship to SR, conditions of successful attacks for a distinguishing rule with a probability distribution, a statistic/asymptotic aspect, and the order of key ranks in SCA. Finally, we validate the proposed metrics through experimental attacks on masked AES implementations using DL-SCA.</p>", "Keywords": "Side-channel analysis;Deep learning;Optimal distinguisher;Success rate;Perceived information", "DOI": "10.46586/tches.v2022.i4.228-254", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "NTT Social Informatics Laboratories, Nippon Telegraph and Telephone Corporation, 3–9–11 Midori-cho, Musashino-shi, Tokyo, 180-8535, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tohoku University, 2–1–1 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 980-8577, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tohoku University, 2–1–1 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 980-8577, Japan"}], "References": []}, {"ArticleId": 96045164, "Title": "Analyzing Operation Efficiency of a City Transportation System by the U-Statistics Methods. I. Interactive Evaluation of Continuous Monitoring Results", "Abstract": "<p>The method of U-statistics is used to analyze the operation efficiency of a large city transportation system as a complex network system with partially ordered traffic flows. Based on the continuous monitoring results of the ordered flow part equipped with GPS-trackers, namely, that of the public transportation, methods of interactive, forecasting, and aggregated analysis of the state and operation process of transportation system components of various hierarchy levels have been developed. The proposed technique can be easily automated and used for operational analysis and forecasting of traffic situation development on city highways and for creating efficient tools to optimize the transportation system operation.</p>", "Keywords": "complex network; network system; road system; U-statistics; evaluation; forecasting; aggregation", "DOI": "10.1007/s10559-022-00475-w", "PubYear": 2022, "Volume": "58", "Issue": "3", "JournalId": 17407, "JournalTitle": "Cybernetics and Systems Analysis", "ISSN": "1060-0396", "EISSN": "1573-8337", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Ya. S. Pidstryhach Institute for Applied Problems of Mechanics and Mathematics, National Academy of Sciences of Ukraine, Lviv, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Ya. S. Pidstryhach Institute for Applied Problems of Mechanics and Mathematics, National Academy of Sciences of Ukraine, Lviv, Ukraine; Ivan <PERSON> National University of Lviv, Lviv, Ukraine"}], "References": []}, {"ArticleId": 96045196, "Title": "High Accuracy Electric Water Heater using Adaptive Neuro-Fuzzy Inference System (ANFIS)", "Abstract": "<p>Nowadays, water heater is a common household appliance. Water heater can be divided into three types, based on fuel sources: gas, diesel, and electric. Electric water heater is the most common due to its ease of use. The problems that often occur on electric water heater are over-temperature due to user error in setting up the thermostat and inaccurate readings caused by a conventional system control. These problems will cause a surge in power consumption. Over-temperature and conventional control inaccuracies can be overcome using the Artificial Intelligence (AI) control algorithm in the form of an adaptive neuro-fuzzy inference system (ANFIS). The proposed algorithm acts as a control by maintaining the stability of the temperature to obtain more accurate results. An accurate temperature reading can lower power consumption in electric water heater. This study tries to simulate Electric Water Heater temperature control using the ANFIS algorithm until stable readings can be achieved in all temperature settings. Results from disturbance tests in the form of external condition that causes sudden temperature change show that the system can maintain stability with an average error margin of 0.045% and the rate of accuracy of 99.955%.</p>", "Keywords": "Adaptive Neuro-Fuzzy Inference System;Electric Water Heater (EWH);Temperature Control", "DOI": "10.22219/kinetik.v7i3.1453", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 31102, "JournalTitle": "KINETIK", "ISSN": "2503-2259", "EISSN": "2503-2267", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}], "References": []}, {"ArticleId": 96045259, "Title": "Model predictive control based consensus scheme of discrete-time multi-agent systems with communication delay", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCIP.2021.125160", "PubYear": 2021, "Volume": "3", "Issue": "4", "JournalId": 25167, "JournalTitle": "International Journal of System Control and Information Processing", "ISSN": "1759-9334", "EISSN": "1759-9342", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96045260, "Title": "Dynamic output feedback based robust controller design for selective catalytic reduction systems", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCIP.2021.125149", "PubYear": 2021, "Volume": "3", "Issue": "4", "JournalId": 25167, "JournalTitle": "International Journal of System Control and Information Processing", "ISSN": "1759-9334", "EISSN": "1759-9342", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96045265, "Title": "Investigating a Combination of Input Modalities, Canvas Geometries, and Inking Triggers on On-Air Handwriting in Virtual Reality", "Abstract": "Humans communicate by writing, often taking notes that assist thinking. With the growing popularity of collaborative\n Virtual Reality (VR) \n applications, it is imperative that we better understand aspects that affect writing in these virtual experiences. On-air writing in VR is a popular writing paradigm due to its simplicity in implementation without any explicit needs for specialized hardware. A host of factors can affect the efficacy of this writing paradigm and in this work, we delved into investigating the same. Along these lines, we investigated the effects of a combination of factors on users’ on-air writing performance, aiming to understand the circumstances under which users can both effectively and efficiently write in VR. We were interested in studying the effects of the following factors: (1) input modality: brush vs. near-field raycast vs. pointing gesture, (2) inking trigger method: haptic feedback vs. button based trigger, and (3) canvas geometry: plane vs. hemisphere. To evaluate the writing performance, we conducted an empirical evaluation with thirty participants, requiring them to write the words we indicated under different combinations of these factors. Dependent measures including the writing speed, accuracy rates, perceived workloads, and so on, were analyzed. Results revealed that the brush based input modality produced the best results in writing performance, that haptic feedback was not always effective over button based triggering, and that there are trade-offs associated with the different types of canvas geometries used. This work attempts at laying a foundation for future investigations that seek to understand and further improve the on-air writing experience in immersive virtual environments.", "Keywords": "Virtual reality; writing; interfaces; interaction; text entry", "DOI": "10.1145/3560817", "PubYear": 2022, "Volume": "19", "Issue": "4", "JournalId": 12654, "JournalTitle": "ACM Transactions on Applied Perception", "ISSN": "1544-3558", "EISSN": "1544-3965", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clemson University, Clemson, South Carolina, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clemson University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Yang Ming Chiao Tung University, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Yang Ming Chiao Tung University, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Yang Ming Chiao Tung University, Taiwan"}], "References": []}, {"ArticleId": 96045334, "Title": "A Study on Graphic Symbols for Switch Display Applying Impression of Simple Geometric Shapes:", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1F1-03", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Moe MIYAUCHI", "Affiliation": "Graduate School of Nihon University"}, {"AuthorId": 2, "Name": "Motonori ISHIBASHI", "Affiliation": "Nihon University"}], "References": []}, {"ArticleId": 96045338, "Title": "Image Survey on tactile pictograms to support the movement of the blind visually impaired", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1E3-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>i K<PERSON>A<PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 2, "Name": "Tota MIZUNO", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 3, "Name": "Naoaki ITAKURA", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}], "References": []}, {"ArticleId": 96045339, "Title": "Hazard perception among the elderly for safety colors recognizable to color vision handicapped", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1E3-01", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Nobuhisa OCHIAI", "Affiliation": "Department of Ophthalmology, School of Medicine, University of Occupational and Environmental Health, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ophthalmology, School of Medicine, University of Occupational and Environmental Health, Japan"}], "References": []}, {"ArticleId": 96045340, "Title": "Influence of flexion time and flexion angle on the reposition sense of spine posture", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2E2-06", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kota SAITO", "Affiliation": "Graduate school of Science and Engineering, Chiba University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Design Research Institute, Chiba University"}], "References": []}, {"ArticleId": 96045348, "Title": "Trial of universal design education for children", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1E3-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osaka University of Art"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>U<PERSON>", "Affiliation": "Mitsubishi Electric Co. Ltd."}, {"AuthorId": 3, "Name": "Yuko EGUCHI", "Affiliation": "Mitsubishi Electric Co. Ltd."}, {"AuthorId": 4, "Name": "Takao KOGURE", "Affiliation": "Inclusive Design Network"}, {"AuthorId": 5, "Name": "Kunio TSUTATANI", "Affiliation": "Inclusive Design Network"}, {"AuthorId": 6, "Name": "Shiho MATSUSHITA", "Affiliation": "International Association for Universal Design"}], "References": []}, {"ArticleId": 96045351, "Title": "Study on the Evaluation of the Characteristics of Work Environments in Virtual Reality", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G3-06", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chitose Institute of Science and Technology"}, {"AuthorId": 2, "Name": "Seiji KIK<PERSON>H<PERSON>", "Affiliation": "Chitose Institute of Science and Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>U<PERSON>", "Affiliation": "Chitose Institute of Science and Technology"}, {"AuthorId": 4, "Name": "YUSUKE KANNO", "Affiliation": "Chitose Institute of Science and Technology"}, {"AuthorId": 5, "Name": "Shinji MIYAKE", "Affiliation": "Chitose Institute of Science and Technology"}, {"AuthorId": 6, "Name": "Daiji KOBAYASHI", "Affiliation": "Chitose Institute of Science and Technology"}], "References": []}, {"ArticleId": 96045357, "Title": "Effects of intermittent training with bilateral arm cooking tasks", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2E4-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON>hi <PERSON>", "Affiliation": "Jissen Women’s University"}, {"AuthorId": 2, "Name": "Mizuki NAKAJIMA", "Affiliation": "Jissen Women’s University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tokyo Kasei Gakuin University"}, {"AuthorId": 4, "Name": "Kenichi EGAWA", "Affiliation": "Tokyo Kasei Gakuin University"}, {"AuthorId": 5, "Name": "Tasuku MIYOSHI", "Affiliation": "Iwate University"}, {"AuthorId": 6, "Name": "Ryota MURANO", "Affiliation": "Waseda University"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tokyo University of Technology"}], "References": []}, {"ArticleId": 96045369, "Title": "Factors that Promote/Discourage Passenger Call Button Use", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2F3-03", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Railway Technical Research Institute"}, {"AuthorId": 2, "Name": "Ayano SAITO", "Affiliation": "Railway Technical Research Institute"}], "References": []}, {"ArticleId": 96045380, "Title": "Effect of Gastrointestinal Endoscope Holding Device on Reduction of Surgeon Fatigue", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G2-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Chika KIMURA", "Affiliation": "Graduate school of science and engineering, Chiba University"}, {"AuthorId": 2, "Name": "Megumi SHIMURA", "Affiliation": "Graduate school of science and engineering, Chiba University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Design research institute, Chiba University"}, {"AuthorId": 4, "Name": "Ma<PERSON>a UESATO", "Affiliation": "Graduate school of medicine, Chiba University"}], "References": []}, {"ArticleId": 96045384, "Title": "Clustering of baby carriers user by their needs and their characteristics", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G1-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Makiko A<PERSON>I", "Affiliation": "Kanagawa Institute of technology"}, {"AuthorId": 2, "Name": "Satoshi SUZUKI", "Affiliation": "Kanagawa Institute of technology"}, {"AuthorId": 3, "Name": "Hidenobu TAKAO", "Affiliation": "Kanagawa Institute of technology"}], "References": []}, {"ArticleId": 96045385, "Title": "Analysis of ASRS Data Using Natural Language Processing:", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2F3-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Mako <PERSON>", "Affiliation": "Keio University"}, {"AuthorId": 2, "Name": "Miwa NAKANISHI", "Affiliation": "Keio University"}], "References": []}, {"ArticleId": 96045390, "Title": "Effect of variation of task duration and work position on total endurance time of repetitive lifting task", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2E2-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Systems Design, Tokyo Metropolitan University"}, {"AuthorId": 2, "Name": "Kazuki HIRANAI", "Affiliation": "National Institute of Occupational Safety and Health, Japan"}, {"AuthorId": 3, "Name": "Atsushi SUGAMA", "Affiliation": "National Institute of Occupational Safety and Health, Japan"}], "References": []}, {"ArticleId": 96045400, "Title": "Relationship Between Drainage Protective Shoes and Gait", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1F4-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Ryota MURANO", "Affiliation": "Waseda University"}, {"AuthorId": 2, "Name": "Taisei SOEJIMA", "Affiliation": "Waseda University"}, {"AuthorId": 3, "Name": "Shu ONOOKA", "Affiliation": "Waseda University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 5, "Name": "Takumi IWAASA", "Affiliation": "Waseda University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 7, "Name": "<PERSON>hi <PERSON>", "Affiliation": "Jissen Women’s University"}], "References": []}, {"ArticleId": 96045405, "Title": "Development of Rehabilitation Training System for Spatial Recognition and Upper Limb Movement", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G4-06", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Tsukasa IWASAKI", "Affiliation": "Faculty of Rehabilitation, Hiroshima International University"}, {"AuthorId": 2, "Name": "Shigekazu ISHIHARA", "Affiliation": "Faculty of Rehabilitation, Hiroshima International University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>HI<PERSON>", "Affiliation": "Faculty of Rehabilitation, Hiroshima International University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> MORIN<PERSON>", "Affiliation": "Faculty of Rehabilitation, Hiroshima International University"}], "References": []}, {"ArticleId": 96045418, "Title": "Development and evaluation of insole-type plantar pressure measurement device for detecting risk of anterior cruciate ligament injury", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2E5-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Sae OKAMOTO", "Affiliation": "Graduate School of Humanities and Sciences, Nara Women’s University"}, {"AuthorId": 2, "Name": "Emi ANZAI", "Affiliation": "Faculty Division of Engineering, Nara Women’s University"}, {"AuthorId": 3, "Name": "Satoru HASHIZUME", "Affiliation": "Faculty of Sport and Health Science, Ritsumeikan University"}, {"AuthorId": 4, "Name": "Naoki SAIWAKI", "Affiliation": "Faculty Division of Engineering, Nara Women’s University"}], "References": []}, {"ArticleId": 96045425, "Title": "Applicability of Experience Sampling Method for Remote Workers in Daily Life", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1D1-03", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Shunsuke MINUSA", "Affiliation": "Hitachi, Ltd., Research & Development Group"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hitachi, Ltd., Research & Development Group"}], "References": []}, {"ArticleId": 96045433, "Title": "Study on mandibular contour tracking method that sequentially updates the search direction for swallowing ability estimation", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G1-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Taisei KANEGAE", "Affiliation": "National Institute of Technology, Kurume Colleage"}, {"AuthorId": 2, "Name": "Hiroaki KOGA", "Affiliation": "National Institute of Technology, Kurume Colleage"}], "References": []}, {"ArticleId": 96045448, "Title": "Relationship between Easiness of Opening Food Packages and Visual Behavior during Package Opening Task", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D1-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Ruka TAKAHASHI", "Affiliation": "Graduate School of Nihon University"}, {"AuthorId": 2, "Name": "Motonori ISHIBASHI", "Affiliation": "Nihon University"}], "References": []}, {"ArticleId": 96045454, "Title": "Basic research on the applicability of behavior change through gamification to highway users", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1B3-06", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Takumi ASAOKA", "Affiliation": "Nagoya University, Graduate School of Environmental Studies"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nagoya University"}], "References": []}, {"ArticleId": 96045461, "Title": "Verification of subjective evaluation and brain activity for autonomous agents with an explanation mechanism", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1F2-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Misato KASUYA", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 2, "Name": "Kasumi ABE", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 3, "Name": "Ta<PERSON>yuki NAGAI", "Affiliation": "Osaka University"}], "References": []}, {"ArticleId": 96045466, "Title": "Analysis of user experience for shopping on unmanned stores in China", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1D1-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kanagawa University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kanagawa University"}], "References": []}, {"ArticleId": 96045471, "Title": "Efficiency of predictive information presentation of possible hazardous event on attention of a driver using advanced driver assistance system", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2F4-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>HASHI", "Affiliation": "National Institute of Advanced Industrial Science and Technology"}], "References": []}, {"ArticleId": 96045475, "Title": "Effects of posture on fatigue and performance in a home-studying environment", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1G1-03", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School Nara Women’s University"}, {"AuthorId": 2, "Name": "Hiroko KUBO", "Affiliation": "Faculty of Engineering, Nara Women’s University"}], "References": []}, {"ArticleId": 96045478, "Title": "A study of framework for creating a future vision", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.S2A2-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "y2.DesignConsulting"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HOLONCREATE Inc."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "HCD YOROZU Consulting"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Musashino Art University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shibaura Institute of Technology"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mitsubishi Electric Corp."}, {"AuthorId": 7, "Name": "<PERSON>aro <PERSON>", "Affiliation": "Yamanashi University"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Casio Computer Co., Ltd"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tokyo Metropolitan University"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Tokyo Metropolitan University"}], "References": []}, {"ArticleId": 96045488, "Title": "Development of AI camera for visualizing physical workload", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D2-06", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kotone TAGAWA", "Affiliation": "Setsunan University, Graduate School"}, {"AuthorId": 2, "Name": "Tsuneo KAWANO", "Affiliation": "Setsunan University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "MATSUO Electric Engineering Co. Ltd."}], "References": []}, {"ArticleId": 96045489, "Title": "Study for Transmitting the Sense of Distance to Objects by Vibrotactile apparent motion", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D5-01", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Tomokazu FURUYA", "Affiliation": "Tokyo Metropolitan College of Industrial Technology"}, {"AuthorId": 2, "Name": "Takeshi KAWASHIMA", "Affiliation": "Kanagawa Institute of Technology"}], "References": []}, {"ArticleId": 96045491, "Title": "Effect of Long-term Whole-body Vibration on Discomfort Evaluation", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2E1-05", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Iori HISADA", "Affiliation": "Graduate School of Systems Engineering, Kindai University"}, {"AuthorId": 2, "Name": "Junya TATASUNO", "Affiliation": "Kindai University"}, {"AuthorId": 3, "Name": "Kazuma ISHIMATSU", "Affiliation": "Jikei University of Health Care Sciences"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Kindai University"}, {"AuthorId": 5, "Name": "Setsuo MAEDA", "Affiliation": "Nottingham Trent University"}], "References": []}, {"ArticleId": 96045493, "Title": "A Proposal of Identification Procedure of Root Cause based on SHEL model", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2F2-01", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}], "References": []}, {"ArticleId": 96045495, "Title": "A Simple Method to Measure Food Thicken by Using Soaking Diaphragms", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2G1-01", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hosei University, Graduate School of Science and Engineering"}, {"AuthorId": 2, "Name": "Kaoru SUZUKI", "Affiliation": "Hosei University, Faculty of Science and Engineering"}], "References": []}, {"ArticleId": 96045497, "Title": "Display Design Requirements Appropriate for Eye-gaze Input System using Automate Cursor Lock Method", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.2D5-06", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Soichiro K<PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 2, "Name": "Atsuo MURATA", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}, {"AuthorId": 3, "Name": "Toshihisa DOI", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University"}], "References": []}, {"ArticleId": 96045502, "Title": "An Evaluation of Augmented Reality Function to Reduce the Wide-Area Surveillance Workload for Remote Air Traffic Control Tower Operations", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1E2-04", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Shinichi FUKASAWA", "Affiliation": "Oki Electric Industry Co., Ltd."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Oki Electric Industry Co., Ltd."}], "References": []}, {"ArticleId": 96045503, "Title": "Driver Behavior Modeling for Safety Assurance of Automated Driving Systems", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.58.1F1-02", "PubYear": 2022, "Volume": "58", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Nobuyuki UCHIDA", "Affiliation": "Japan Automobile Research Institute"}, {"AuthorId": 2, "Name": "Sou KITAJIMA", "Affiliation": "Japan Automobile Research Institute"}, {"AuthorId": 3, "Name": "Jun TAJIMA", "Affiliation": "Setouchi Simulator Co. Ltd."}], "References": []}, {"ArticleId": 96045514, "Title": "Prediction Analysis of College Students’ Physical Activity Behavior by Improving <PERSON> and Support Vector Machine", "Abstract": "<p>Physical exercise for college students is an important means to build a healthy standard of college students and an important way to a healthy campus. In addition to creating good physical fitness, physical exercise has significant effects on improving psychological stress and alleviating psychological problems and mental illnesses among college students. It is important to predict and analyze the physical exercise behavior of college students and explore the positive value of physical exercise for college education. In order to overcome the problem of low accuracy of traditional algorithms in prediction, this paper uses the improved gray wolf algorithm (IGWO) and support vector machine (SVM) for predictive analysis of college students' physical exercise behavior. A nonlinear decreasing convergence factor strategy and an inertia weight strategy are introduced to improve the gray wolf optimization algorithm, which is used to determine the SVM parameters for the purpose of improving the model accuracy. Then, the college students' physical exercise data are input into the model for validation. By constructing a campus behavior data set of college students and conducting experiments, the algorithm achieves 90.45% behavior prediction accuracy, which is better than that of typical prediction models. Finally, individual growth monitoring of college students is targeted to warn students with abnormal behaviors. At the same time, the higher-order information such as physical exercise behavior habits of college students is explored to provide meaningful reference for constructing personalized training.</p>", "Keywords": "", "DOI": "10.1155/2022/4943510", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 8496, "JournalTitle": "Advances in Multimedia", "ISSN": "1687-5680", "EISSN": "1687-5699", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiaxing Vocational and Technical College, Department of Basic and Public Courses, Jiaxing, Zhejiang 314036, China"}], "References": [{"Title": "Demand prediction for a public bike sharing program based on spatio-temporal graph convolutional networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "15", "Page": "22907", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Survey of Traffic Prediction: from Spatio-Temporal Data to Intelligent Transportation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "63", "JournalTitle": "Data Science and Engineering"}]}, {"ArticleId": 96045592, "Title": "Adaptive features selection and EDNN based brain image recognition on the internet of medical things", "Abstract": "A brain tumor is one of the fundamental explanations behind fatality among different sorts of malignant growth; the brain is a touchy, intricate, and focal part of the body. The appropriate and convenient conclusion can deflect the life of an individual somewhat. Subsequently, inside this research study, we proposed a brain tumor classification design to consolidate Adaptive Feature Selection and Entropy-based Deep Neural Network (EDNN) based brain image recognition on the Internet of Medical Things (IOMT). The framework regularly contains four modules pre-processing, including extraction, feature selection or determination, and tumor classification. Primarily, we take out the commotion that is noise from the image. The de-noised image is moved to the skull stripping for the brain region extraction. Afterward, the functional features, such as SFTA, geometric, and LBP , are extracted from pre-processed release images. Select the best features using the Adaptive Grill Herd (AKH) Learning Optimization algorithm. Finally, the proposed EDNN classifies brain images as normal brain or abnormal brain based on the extracted features. The EDNN classifier is modified with entropy-based normalization to mitigate the excess overlap inside the neural network&#x27;s deeper layer towards the maximum pooling layer. Experimental grades demonstrate that the proposed scheme achieves enhanced results compared to the obtainable strategies.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108338", "PubYear": 2022, "Volume": "103", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Osmania University Hyderabad, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Osmania University Hyderabad, India"}], "References": [{"Title": "Deep CNN for Brain Tumor Classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "671", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 96045600, "Title": "An Analysis of the Influence of Different Ways of Context Construction on the Teaching Effect of Intensive Reading and Extensive Reading for English Majors", "Abstract": "<p>Context construction can more effectively put students into the scene and deepen their understanding of reading. The cultivation of students’ comprehensive language use ability is the key teaching activity that English major teachers pay attention to. Language use is closely related to context construction, so it can be said that English teaching cannot leave context creation. In English class, 56.7% of the students have a deep sense of the teachers’ frequent use of context creation, English songs, using facial expressions to do gestures and using objects to create context accounted for 41.3%, 33.7%, and 33.2, respectively. This shows that more than half of the students have a deep feeling about the use of multimedia for context creation. Aiming at the unreasonable design of context creation activities and other problems, this paper provides scientific strategies for teachers to start with modern English teaching. To promote students’ understanding of reading and analyze the teaching effect based on intensive reading and extensive reading so as to improve the teaching quality.</p>", "Keywords": "", "DOI": "10.1155/2022/6833203", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 8496, "JournalTitle": "Advances in Multimedia", "ISSN": "1687-5680", "EISSN": "1687-5699", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Huanghe Science and Technology University, Zhengzhou 450000, China"}], "References": []}, {"ArticleId": ********, "Title": "MetaboAnnotator: an efficient toolbox to annotate metabolites in genome-scale metabolic reconstructions", "Abstract": "Motivation \n Genome-scale metabolic reconstructions have been assembled for thousands of organisms using a wide range of tools. However, metabolite annotations, required to compare and link metabolites between reconstructions, remain incomplete. Here, we aim to further extend metabolite annotation coverage using various databases and chemoinformatic approaches.\n \n \n Results \n We developed a COBRA toolbox extension, deemed MetaboAnnotator, which facilitates the comprehensive annotation of metabolites with database independent and dependent identifiers, obtains molecular structure files, and calculates metabolite formula and charge at pH 7.2. The resulting metabolite annotations allow for subsequent cross-mapping between reconstructions and mapping of, e.g., metabolomic data.\n \n \n Availability and implementation \n MetaboAnnotator and tutorials are freely available at https://github.com/opencobra.\n \n \n Contact \n <EMAIL>\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btac596", "PubYear": 2022, "Volume": "38", "Issue": "20", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, National University of Galway, Galway, Ireland. ;Ryan Institute, National University of Galway, Galway, Ireland. ;Division of Microbiology, National University of Galway, Galway, Ireland. ;APC Microbiome Ireland, University College Cork, Cork, Ireland."}, {"AuthorId": 2, "Name": "German Preciat", "Affiliation": "Analytical BioSciences Division, Leiden Academic Centre for Drug Research, Leiden University, The Netherlands."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Medicine, National University of Galway, Galway, Ireland. ;Analytical BioSciences Division, Leiden Academic Centre for Drug Research, Leiden University, The Netherlands."}], "References": [{"Title": "DEMETER: efficient simultaneous curation of genome-scale reconstructions guided by experimental data and refined gene annotations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "21", "Page": "3974", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 96045707, "Title": "Bipartite event-triggered impulsive output consensus for switching multi-agent systems with dynamic leader", "Abstract": "This paper studies the output consensus of heterogeneous switching linear multi-agent systems (LMASs). The considered switching topology in a signed graph is jointly connected. An event-triggered impulsive control algorithm is proposed, in which the event-triggered time sequence is allowed to be different from the impulsive sequence. Specifically, the leader’s control inputs in the LMASs model are allowed to be designed for the followers. In a fully distributed manner, a general compensator consisting of an impulsive and dynamic event-triggered mechanism is proposed to estimate the state of the leader. Based on the designed compensator and observer, a distributed control law is proposed to realize the bipartite output consensus by using an iterative method. Under the event triggering strategy, the combined measurement method is used, so that each agent only needs to update input information at the moment of its event triggering. Finally, our theoretical results are demonstrated through a numerical example.", "Keywords": "", "DOI": "10.1016/j.ins.2022.08.086", "PubYear": 2022, "Volume": "612", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Changshu Institute of Technology, Changshu 215500, PR China"}, {"AuthorId": 2, "Name": "Yuanyuan Li", "Affiliation": "Department of Applied Mathematics, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Southeast University, Nanjing 210096, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Yangtze Delta Region (Huzhou) Institute of Intelligent Transportation, Huzhou University, Huzhou 313000, China;Corresponding author"}], "References": [{"Title": "H∞ leader-following consensus of nonlinear multi-agent systems under semi-Markovian switching topologies with partially unknown transition rates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "168", "JournalTitle": "Information Sciences"}, {"Title": "Output consensus for switched multi-agent systems with bumpless transfer control and event-triggered communication", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "585", "JournalTitle": "Information Sciences"}, {"Title": "Leader-follower consensus control for linear multi-agent systems by fully distributed edge-event-triggered adaptive strategies", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "555", "Issue": "", "Page": "314", "JournalTitle": "Information Sciences"}, {"Title": "Time-varying Nonholonomic Robot Consensus Formation Using Model Predictive Based Protocol With Switching Topology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "567", "Issue": "", "Page": "201", "JournalTitle": "Information Sciences"}, {"Title": "Bipartite asynchronous impulsive tracking consensus for multi-agent systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "10", "Page": "1522", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}]