[{"ArticleId": 94153656, "Title": "Nonlinear Influence of Commute Time Tolerance Threshold on Commute Mode Choice Based on the Semicompensatory Model", "Abstract": "<p>The tolerance threshold of commute time (TTCT) reflects the longest commute time that commuters can tolerate from home to the workplace. When the commute time exceeds the TTCT, the commuting utility significantly reduces, which has a nonlinear influence on commuting mode choice. To reveal the nonlinear relationship between the commuting utility and commute time, the TTCT is introduced to constrained multinomial logit (CMNL) model based on the semicompensatory decision-making mechanism. In addition, an empirical study is carried out on 405 commuters in Kunming, China. The results show that the CMNL model has a higher fitting accuracy than the MNL model, which indicates that the TTCT is a significant explanatory variable for the commuting mode choice. Moreover, the commuting utility does not decrease linearly with the commute time. An appropriate commute time range (about 5–25 min) could bring positive commute utility to the commuters, but the commute utility is negatively impacted when the commute time is larger than the TTCT. Therefore, it is necessary importing the TTCT in the utility function to improve the predictive power of the commuting mode choice model.</p>", "Keywords": "", "DOI": "10.1155/2022/4802814", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Transpotation Engineering, Kunming University of Science and Technology, Kunming 650500, China;School of Mechanical and Electrical Engineering, Kunming University, Kunming 650214, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Transpotation Engineering, Kunming University of Science and Technology, Kunming 650500, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Transpotation Engineering, Kunming University of Science and Technology, Kunming 650500, China"}], "References": []}, {"ArticleId": 94153743, "Title": "Characterizing the activity patterns of outdoor jogging using massive multi-aspect trajectory data", "Abstract": "Characterizing activity pattern such as behavior preferences or habits is crucial in many fields. However, existing studies mainly focus on the spatial-temporal dimensions of raw trajectory, but ignore the context information in multi-aspect trajectory that affects behavior significantly. In this paper, we present a data-driven framework to characterize outdoor jogging activity patterns with massive multi-aspect trajectory data. In our framework, a novel multi-aspect trajectory Latent Dirichlet Allocation (MAT-LDA) model is presented to discover latent activity patterns from multi-aspect trajectories. Specifically, the model inherits from LDA, but extends its topics and words to mine the combined patterns in multi-aspects. Then, clustering analysis is performed to find and characterize the jogger groups with similar preference patterns. Experiments with real jogging GPS tracks recorded by 16,643 users&#x27; fitness app show that the MAT-LDA model can efficiently discover the latent activity patterns and quantify the correlations and interdependencies between patterns of multi-aspect attributes. Moreover, many interpretable preferences are discovered at individual level, and jogger groups (e.g., mini groups, jog hobbyist) with common context-aware preferences are revealed to understand fitness jogging. Our method can capture activity behavior preferences of multiple aspects from multi-aspect trajectory data, and our work can enrich outdoor fitness application with interpretable preference patterns.", "Keywords": "Outdoor jogging ; Multi-aspect trajectory data ; LDA model ; Clustering ; Activity pattern", "DOI": "10.1016/j.compenvurbsys.2022.101804", "PubYear": 2022, "Volume": "95", "Issue": "", "JournalId": 1688, "JournalTitle": "Computers, Environment and Urban Systems", "ISSN": "0198-9715", "EISSN": "1873-7587", "Authors": [{"AuthorId": 1, "Name": "Zongshun Tian", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing 400045, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing 400045, China;Corresponding author at: School of Management Science and Real Estate, Chongqing University, Chongqing, 400045, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The State Key Laboratory of Information Engineering in Surveying, Mapping and Remote Sensing, Wuhan University, Wuhan 430079, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Resource and Environment Sciences, Wuhan University, Wuhan 430079, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> (Chongqing) International Engineering Technology Co., Ltd, Chongqing 401121, China"}], "References": [{"Title": "Tracking urban geo-topics based on dynamic topic model", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "", "Page": "101419", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "You are how you travel: A multi-task learning framework for Geodemographic inference using transit smart card data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "83", "Issue": "", "Page": "101517", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "Analyzing movement predictability using human attributes and behavioral patterns", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "87", "Issue": "", "Page": "101596", "JournalTitle": "Computers, Environment and Urban Systems"}]}, {"ArticleId": 94154018, "Title": "Evaluating quality in human-robot interaction: A systematic search and classification of performance and human-centered factors, measures and metrics towards an industry 5.0", "Abstract": "Industry 5.0 constitutes a change of paradigm where the increase of economic benefits caused by a never-ending increment of production is no longer the only priority. Instead, Industry 5.0 addresses social and planetary challenges caused or neglected in Industry 4.0 and below. One relevant the most relevant challenges of Industry 5.0 is the design of human-centered smart environments (i.e., that prioritize human well-being while maintaining production performance). In these environments, robots and humans will share the same space and collaborate to reach common objectives. This article presents a literature review of the different aspects concerning the problem of quality measurement in Human-Robot Interaction (HRI) applications for manufacturing environments. To help practitioners and new researchers in the area, this article presents an overview of factors, metrics, and measures used in the robotics community to evaluate performance and human well-being quality aspects in HRI applications. For this, we performed a systematic search in relevant databases for robotics (Science Direct, IEEE Xplore, ACM digital library, and Springer Link). We summarize and classify results extracted from 102 peer-reviewed research articles published until March 2022 in two definition models: 1) a taxonomy of performance aspects and 2) a Venn Diagram of common human factors in HRI. Additionally, we briefly explain the differences between often confusing or overlapped concepts in the area. We also introduce common human factors evaluated by the robotics community and identify seven emergent research topics which can have a relevant impact on Industry 5.0.", "Keywords": "Human-robot interaction ; Human-robot collaboration ; Metrics ; Robotics ; Industry 5.0 ; Society 5.0 2010 MSC: 00–01 ; 99–00", "DOI": "10.1016/j.jmsy.2022.04.007", "PubYear": 2022, "Volume": "63", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Systems Engineering, Tokyo University of Agriculture and Technology, Tokyo, Japan;National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan;Corresponding author at: Department of Mechanical Systems Engineering, Tokyo University of Agriculture and Technology, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Systems Innovation, Graduate School of Engineering Science, Osaka University, Osaka, Japan;Division of Information Science, Graduate School of Science and Technology, Nara Institute of Science and Technology (NAIST), Nara, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Information Science, Graduate School of Science and Technology, Nara Institute of Science and Technology (NAIST), Nara, Japan;Ritsumeikan University, Shiga, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Systems Engineering, Tokyo University of Agriculture and Technology, Tokyo, Japan;National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Systems Engineering, Tokyo University of Agriculture and Technology, Tokyo, Japan;National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan"}], "References": [{"Title": "Intelligence in the Internet of Medical Things era: A systematic review of current and future trends", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "644", "JournalTitle": "Computer Communications"}, {"Title": "Factors affecting trust in high-vulnerability human-robot interaction contexts: A structural equation modelling approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "85", "Issue": "", "Page": "103056", "JournalTitle": "Applied Ergonomics"}, {"Title": "A conceptual framework to evaluate human-robot collaboration", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "3", "Page": "841", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Usability, user experience and accessibility: towards an integrative model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "10", "Page": "1207", "JournalTitle": "Ergonomics"}, {"Title": "Towards Effective Interface Designs for Collaborative HRI in Manufacturing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}, {"Title": "A Systematic Review of Attitudes, Anxiety, Acceptance, and Trust Towards Social Robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "6", "Page": "1179", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Friend or Foe? Understanding Assembly Workers’ Acceptance of Human-robot Collaboration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}, {"Title": "A Survey of Mental Modeling Techniques in Human–Robot Teaming", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "4", "Page": "259", "JournalTitle": "Current Robotics Reports"}, {"Title": "Explainable Robotics in Human-Robot Interactions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "3057", "JournalTitle": "Procedia Computer Science"}, {"Title": "Applying Kansei/Affective Engineering Methodologies in the Design of Social and Service Robots: A Systematic Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "5", "Page": "1161", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Applying Kansei/Affective Engineering Methodologies in the Design of Social and Service Robots: A Systematic Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "5", "Page": "1161", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Systematic literature review on augmented reality in smart manufacturing: Collaboration between human and computational intelligence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "696", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Acceptance of Industrial Collaborative Robots by People With Disabilities in Sheltered Workshops", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "173", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Industry 4.0 and Industry 5.0—Inception, conception and perception", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "530", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 94154135, "Title": "A review on sensory perception for dexterous robotic manipulation", "Abstract": "<p>Sensory perception for dexterous robotic hands is an active research area and recent progress in robotics. Effective dexterous manipulation requires robotic hands to accurately feedback their state or perceive the surrounding environment. This article reviews the state-of-the-art of sensory perception for dexterous robotic manipulation. Two types of sensors, such as intrinsic and extrinsic sensors, are introduced according to their function and layout in robotic hands. These sensors provide rich information to a robotic hand, which contains the posture, the contact information of objects, and the physical information of the environment. Then, a comprehensive analysis of perception methods including planning-level, control-level, and learning-level perceptions is presented. The information obtained from sensory perception can help robotic hands to make decisions effectively. Previously issued reviews mainly focus on the design of tactile senor, while we analyze and discuss the relationship among sensing, perception, and dexterous manipulation. Some potential research topics on sensory perception are also summarized and discussed.</p>", "Keywords": "", "DOI": "10.1177/17298806221095974", "PubYear": 2022, "Volume": "19", "Issue": "2", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Technology, China University of Gaosciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Fuzhou University, Fuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tsinghua National Laboratory for Information Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Technology, China University of Gaosciences, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua National Laboratory for Information Science and Technology, Tsinghua University, Beijing, China"}], "References": [{"Title": "Learning dexterous in-hand manipulation", "Authors": "OpenAI: <PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "1", "Page": "3", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Recognition of surface texture with wearable tactile sensor array: A pilot Study", "Authors": "<PERSON><PERSON> Wang; <PERSON><PERSON><PERSON>; Deqing Mei", "PubYear": 2020, "Volume": "307", "Issue": "", "Page": "111972", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Soft magnetic skin for super-resolution tactile sensing with force self-decoupling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "51", "Page": "eabc8801", "JournalTitle": "Science Robotics"}, {"Title": "Soft Tactile Sensing Skins for Robotics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "343", "JournalTitle": "Current Robotics Reports"}, {"Title": "Cable manipulation with a tactile-reactive gripper", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "12-14", "Page": "1385", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Learning Assembly Tasks in a Few Minutes by Combining Impedance Control and Residual Recurrent Reinforcement Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "1", "Page": "2100095", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 94154146, "Title": "Pose attention and object semantic representation-based human-object interaction detection network", "Abstract": "<p>Human-object interaction (HOI) detection is a core problem in human-centric scene understanding, which is devoted to inferring triplets < human, verb, object > between humans and objects. Previous works mainly determine the interaction of each human-object pair by performing joint inference based on multiple features. In this paper, we design more discriminative representation of the human-object pair and a more effective HOI detection model. On the one hand, we use human poses as an attention mechanism to strengthen features, which is a novel way to deal with human poses in HOI detection. On the other hand, for a more effective representation of objects, a word vector is used to encode objects, and the relation features of humans and objects are captured by a graph convolution network based on object word vectors and human appearance features. These relation features are also strengthened by a human pose attention mechanism. Our model yields favorable results compared to the state-of-the-art HOI detection algorithms on two large-scale benchmark datasets, V-COCO and HICO-DET.</p>", "Keywords": "Human-object interaction detection; Pose attention; Object semantic; Relation feature", "DOI": "10.1007/s11042-022-13146-x", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Huaqiao University, Xiamen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Huaqiao University, Xiamen, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Xiamen Key Laboratory of Computer Vision and Pattern Recognition, Huaqiao University, Xiamen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Big Data Intelligence and Security, Huaqiao University, Xiamen, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Humanities, Xiamen University, Xiamen, China"}], "References": [{"Title": "Improved human-object interaction detection through skeleton-object relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "41", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}]}, {"ArticleId": 94154247, "Title": "On Insufficient Documentation of a Virtual World's Economic Development", "Abstract": "<p>Literature on Second Life (SL) was retrieved, reviewed, and aggregated. Although it was found to provide sufficient information about SL's social, technological, business model, and legal issues, and although SL received overall dense media coverage, the literature was found to be lacking when it came to a thorough documentation of its economic development in 2003-2008. Using the retrieved literature, the attempt to reconstruct the development of the number of avatars, active users, prices and fees, and monetary development exposes the limits of reconstructing the economic development of a virtual world years after its popularity peak. This article serves as an argument and a reminder for a proper documentation of such developments. Future documentation of virtual worlds in academia should thoroughly track the economic development of next-generation virtual worlds. Otherwise, post-analysis, economic development reconstruction, and trace backs might prove difficult, costly, or infeasible.</p>", "Keywords": "", "DOI": "10.4018/IJGCMS.290825", "PubYear": 2022, "Volume": "13", "Issue": "3", "JournalId": 26182, "JournalTitle": "International Journal of Gaming and Computer-Mediated Simulations", "ISSN": "1942-3888", "EISSN": "1942-3896", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Graz, Austria"}], "References": []}, {"ArticleId": 94154282, "Title": "An improved artificial bee colony algorithm based on Bayesian estimation", "Abstract": "Artificial bee colony (ABC) algorithm was proposed by mimicking the cooperative foraging behaviors of bees. As a member of swarm intelligence algorithms, ABC has some advantages in handling optimization problems. However, it has the exploration capacity over the exploitation capacity, which may lead to slow convergence speed and lower solution accuracy. Hence, to enhance the performance of the algorithm, a novel ABC based on Bayesian estimation (BEABC) is presented in this paper. First, instead of using the fitness ratio, the selection probability in ABC is replaced with a new probability calculated by Bayesian estimation. Second, to help the bees adopt more useful information during updating new food sources, a directional guidance mechanism is designed for onlooker bees and scout bees. Finally, the comprehensive performance of BEABC is evaluated by 24 single-objective test functions. The numerical experiment results indicate that BEABC dominates its peers over most test functions, and the significant statistics show that the significant excellence rate of BEABC is $$76\\%$$ \n \n 76 \n % \n \n in the overall comparison. In addition, to further test the performance of BEABC, seven multi-objective problems and two real-word optimization problems are solved. The comparison results show that BEABC can achieve better results than other EA competitors.", "Keywords": "Swarm intelligence; Artificial bee colony; Bayesian estimation; Directional guidance strategy", "DOI": "10.1007/s40747-022-00746-1", "PubYear": 2022, "Volume": "8", "Issue": "6", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Wang", "Affiliation": "School of Mathematics and Statistics, Xianyang Normal University, Xianyang, People’s Republic of China; College of Mathematics and Information Science, Henan Normal University, Xinxiang, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Information Science, Henan Normal University, Xinxiang, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, North China University of Water Resources and Electric Power, Zhengzhou, People’s Republic of China"}], "References": [{"Title": "Artificial bee colony with enhanced food locations for solving mechanical engineering design problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "267", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An integrated particle swarm optimization approach hybridizing a new self-adaptive particle swarm optimization with a modified differential evolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "4849", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Particle swarm optimization with adaptive learning strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105789", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An artificial bee colony algorithm with adaptive heterogeneous competition for global optimization problems", "Authors": "Xiang<PERSON> Chu; <PERSON><PERSON>; Da <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106391", "JournalTitle": "Applied Soft Computing"}, {"Title": "Enhancing artificial bee colony algorithm with multi-elite guidance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "242", "JournalTitle": "Information Sciences"}, {"Title": "Artificial bee colony algorithm based on knowledge fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "3", "Page": "1139", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Adaptive binary artificial bee colony algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107054", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel evolutionary algorithm based on even difference grey model", "Authors": "Zhongbo Hu; <PERSON>g <PERSON>; Qinghua Su", "PubYear": 2021, "Volume": "176", "Issue": "", "Page": "114898", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Mitigating DDoS attacks in VANETs using a Variant Artificial Bee Colony Algorithm based on cellular automata", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "18", "Page": "12191", "JournalTitle": "Soft Computing"}, {"Title": "Privacy preserving rule-based classifier using modified artificial bee colony algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115437", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94154289, "Title": "Breaking the Curse of Class Imbalance: Bangla Text Classification", "Abstract": "<p> This article addresses the class imbalance issue in a low-resource language called Bengali. As a use-case, we choose one of the most fundamental NLP tasks, i.e., text classification, where we utilize three benchmark text corpora: fake-news dataset, sentiment analysis dataset, and song lyrics dataset. Each of them contains a critical class imbalance. We attempt to tackle the problem by applying several strategies that include data augmentation with synthetic samples via text and embedding generation in order to augment the proportion of the minority samples. Moreover, we apply ensembling of deep learning models by subsetting the majority samples. Additionally, we enforce the focal loss function for class-imbalanced data classification. We also apply the outlier detection technique, data resampling, and hidden feature extraction to improve the minority-f1 score. All of our experimentations are entirely focused on textual content analysis, which results in a more than 90% minority f1 score for each of the three tasks. It is an excellent outcome on such highly class-imbalanced datasets. </p>", "Keywords": "", "DOI": "10.1145/3511601", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Engineering & Technology (BUET), Bangladesh, and United International University, Dhaka, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bangladesh University of Engineering & Technology (BUET), Bangladesh, and United International University, Dhaka, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bangladesh University of Engineering & Technology (BUET), Bangladesh, and United International University, Dhaka, Bangladesh"}], "References": []}, {"ArticleId": 94154337, "Title": "Fuzzy Contrast Set Based Deep Attention Network for Lexical Analysis and Mental Health Treatment", "Abstract": "<p>Internet-delivered psychological treatments (IDPT) consider mental problems based on Internet interaction. With such increased interaction because of the COVID-19 pandemic, more online tools have been widely used to provide evidence-based mental health services. This increase helps cover more population by using fewer resources for mental health treatments. Adaptivity and customization for the remedy routine can help solve mental health issues quickly. In this research, we propose a fuzzy contrast-based model that uses an attention network for positional weighted words and classifies mental patient authored text into distinct symptoms. After that, the trained embedding is used to label mental data. Then the attention network expands its lexicons to adapt to the usage of transfer learning techniques. The proposed model uses similarity and contrast sets to classify the weighted attention words. The fuzzy model then uses the sets to classify the mental health data into distinct classes. Our method is compared with non-embedding and traditional techniques to demonstrate the proposed model. From the experiments, the feature vector can achieve a high ROC curve of 0.82 with problems associated with nine symptoms.</p>", "Keywords": "", "DOI": "10.1145/3506701", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Western Norway University of Applied Sciences, Bergen, Norway"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Western Norway University of Applied Sciences, Bergen, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Brandon University, Canada and China Medical University, Taichung, Taiwan"}], "References": [{"Title": "Evaluating and improving lexical resources for detecting signs of depression in text", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "1", "Page": "1", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Con2Vec: Learning embedding representations for contrast sets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "229", "Issue": "", "Page": "107382", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 94154341, "Title": "Leveraging Multilingual News Websites for Building a Kurdish Parallel Corpus", "Abstract": "<p> Machine translation has been a major motivation of development in natural language processing. Despite the burgeoning achievements in creating more efficient machine translation systems, thanks to deep learning methods, parallel corpora have remained indispensable for progress in the field. In an attempt to create parallel corpora for the Kurdish language, in this article, we describe our approach in retrieving potentially alignable news articles from multi-language websites and manually align them across dialects and languages based on lexical similarity and transliteration of scripts. We present a corpus containing 12,327 translation pairs in the two major dialects of Kurdish, Sorani and Kurmanji. We also provide 1,797 and 650 translation pairs in English-Kurmanji and English-Sorani. The corpus is publicly available under the CC BY-NC-SA 4.0 license. <sup>1</sup> </p>", "Keywords": "", "DOI": "10.1145/3511806", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Insight Centre for Data Analytics, National University of Ireland Galway, Galway, Ireland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Kurdistan Hewlêr, Erbil, Kurdistan Region, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of English, Faculty of Education, Koya University, Koya, Kurdistan Region, Iraq"}], "References": []}, {"ArticleId": 94154349, "Title": "Chebyshev chaotic map-based efficient authentication scheme for secure access of VoIP services through SIP", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSN.2022.10046977", "PubYear": 2022, "Volume": "17", "Issue": "1", "JournalId": 15172, "JournalTitle": "International Journal of Security and Networks", "ISSN": "1747-8405", "EISSN": "1747-8413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94154360, "Title": "Um etliche Ecken ged8", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00287-022-01459-y", "PubYear": 2022, "Volume": "45", "Issue": "3", "JournalId": 7707, "JournalTitle": "Informatik-Spektrum", "ISSN": "0170-6012", "EISSN": "1432-122X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Fachbereich Informatik, Universität Hamburg, Hamburg, Deutschland"}], "References": []}, {"ArticleId": 94154366, "Title": "BERIS: An mBERT-based Emotion Recognition Algorithm from Indian Speech", "Abstract": "<p>Emotions, the building blocks of the human intellect, play a vital role in Artificial Intelligence (AI). For a robust AI-based machine, it is important that the machine understands human emotions. COVID-19 has introduced the world to no-touch intelligent systems. With an influx of users, it is critical to create devices that can communicate in a local dialect. A multilingual system is required in countries like India, which has a large population and a diverse range of languages. Given the importance of multilingual emotion recognition, this research introduces BERIS, an Indian language emotion detection system. From the Indian sound recording, BERIS estimates both acoustic and textual characteristics. To extract the textual features, we used Multilingual Bidirectional Encoder Representations from Transformers. For acoustics, BERIS computes the Mel Frequency Cepstral Coefficients and Linear Prediction coefficients, and Pitch. The features extracted are merged in a linear array. Since the dialogues are of varied lengths, the data are normalized to have arrays of equal length. Finally, we split the data into training and validated set to construct a predictive model. The model can predict emotions from the new input. On all the datasets presented, quantitative and qualitative evaluations show that the proposed algorithm outperforms state-of-the-art approaches.</p>", "Keywords": "", "DOI": "10.1145/3517195", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Uttarakhand Technical University, Dehradun, Uttarakhand, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "G. B. Pant Engineering College, Pauri Garhwal, Uttarakhand, India"}], "References": [{"Title": "Speech Emotion Recognition with deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "251", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 94154367, "Title": "Automatic Labeling of Clusters for a Low-Resource Urdu Language", "Abstract": "<p>Document clustering techniques often produce clusters that require human intervention to interpret the meaning of such clusters. Automatic cluster labeling refers to the process of assigning a meaningful phrase to a cluster as a label. This article proposes an unsupervised method for cluster labeling that is based on noun phrase chunking. The proposed method is compared with four other statistical-based methods, including Z-Order, M-Order, T-Order, and YAKE. In addition to the statistical measures based labeling schemes, the approach is also compared with two graph-based techniques: TextRank and PositionRank. The experiments were performed on the low-resource Urdu language corpus of News Headlines. The proposed approach's effectiveness was evaluated using cosine similarity, the Jaccard index, and feedback received from human evaluators. The results show that the proposed method outperforms other methods. It was found that the labels produced were more relevant and semantically rich in contrast to other approaches.</p>", "Keywords": "", "DOI": "10.1145/3511097", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Business Administration (IBA), Karachi, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Business Administration (IBA), Karachi, Pakistan"}], "References": [{"Title": "YAKE! Keyword extraction from single documents using multiple local features", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "257", "JournalTitle": "Information Sciences"}, {"Title": "Cluster analysis of urdu tweets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "2170", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Interpretable semantic textual similarity of sentences using alignment of chunks with classification and regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "10", "Page": "7322", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 94154368, "Title": "Acoustic Analysis of Vowels in Konkani", "Abstract": "<p> Konkani is an under-resourced language mainly spoken on the west coast of India. Although linguistic analyses of vowel sounds in various dialects of Konkani have been done in the past, more accurate analysis of Konkani vowels, especially an acoustic-phonetic analysis, was never carried out. In this article, we present a detailed analysis of nine Konkani vowels, namely /i/, /e/, /ε/, /u/, /o/, /ɔ/, /a/, /ə/, and /ɨ̞/ . The dataset used for the analysis was created from audio recordings of 28 native speakers of Goan Konkani. Based on the experimental results, we propose a vowel chart for Konkani. We also observed a partial loss of Konkani vowel / ɨ̞ / in the regular speech of native speakers. This change is also evident in the substitution analysis of vowel phonemes that was carried out by us as a part of this study. </p>", "Keywords": "", "DOI": "10.1145/3474358", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "S<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Goa University, Taleigao, Goa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Mumbai and Government College of Arts, Science & Commerce, Quepem, Goa"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Goa University, Taleigao, Goa"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Goa University, Taleigao, Goa"}], "References": []}, {"ArticleId": 94154403, "Title": "AI-Powered “Voice Recognition Avatar”", "Abstract": "<p>Consumers like to try innovative technologies when they perceive them to be approachable, convenient, and entertaining. The purpose of this study was to explore the adoption of AI voice recognition avatar in the gameplay. This study's interesting insights show us that gamers are keen and interested in voice recognition technology. The study augments value to TAM theories, perceived value theory, and the flow theory with localization's moderating role. The 218 respondents from China provided useful insights. In China, gaming is a vast industry, and such gaming options can attract current and new gamers. Thus, AI can benefit gamers in this regard and give them the freedom to interact with their avatar through the power of voice recognition.</p>", "Keywords": "", "DOI": "10.4018/IJGCMS.290305", "PubYear": 2021, "Volume": "13", "Issue": "3", "JournalId": 26182, "JournalTitle": "International Journal of Gaming and Computer-Mediated Simulations", "ISSN": "1942-3888", "EISSN": "1942-3896", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dongbei University of Finance and Economics, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dongbei University of Finance and Economics, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Gomal University Dera Ismial Khan, Pakistan"}], "References": [{"Title": "The Effects of Consumers’ In-Store Technology Experience on Perceived Interactivity, Retail Brand Commitment, and Revisit Intention in a Korean Beauty Store", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "534", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 94154406, "Title": "Principles for Engineering Social Interaction in Asynchronous Mobile Games", "Abstract": "<p>Since their first appearance, mobile devices have had exponential technological growth and have gained other functionalities besides standard calls and messaging services. With the advent of smartphones, the mobile gaming market has grown enormously to become one of the most widespread forms of gaming. This paper devises guidelines for engineering online multiplayer asynchronous mobile games. The findings emerge from the launch of a new game where, alternately, players try to guess the answers given by other players to a given set of questions. Departing from the meaning of usability in ISO 9241-11, the authors define idle-ability as the capacity of a product to realize operational and satisfaction goals in a specified context of sociotechnical idleness. Moreover, design principles are proposed to improve idle-ability in the selected genre of mobile games. Asynchronous games are traditionally integrated into social platforms and online communities. The work offers recommendations for its separate deployment in the market, exploring the potential of invisible social bonds among its players.</p>", "Keywords": "", "DOI": "10.4018/IJGCMS.290306", "PubYear": 2021, "Volume": "13", "Issue": "3", "JournalId": 26182, "JournalTitle": "International Journal of Gaming and Computer-Mediated Simulations", "ISSN": "1942-3888", "EISSN": "1942-3896", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Coimbra, CISUC, DEI, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Coimbra, CISUC, DEI, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "HYP, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "HYP, Portugal"}], "References": [{"Title": "Designing for meaningful social interaction in digital serious games", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "", "Page": "100385", "JournalTitle": "Entertainment Computing"}, {"Title": "Gesture recognition based on multi‐modal feature weight", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "e5991", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 94154830, "Title": "Optimising Multilayer Perceptron weights and biases through a Cellular Genetic Algorithm for medical data classification", "Abstract": "In recent years, technology in medicine has shown a significant advance due to artificial intelligence becoming a framework to make accurate medical diagnoses. Models like Multilayer Perceptrons (MLPs) can detect implicit patterns in data, allowing identifying patients conditions that cannot be seen easily. MLPs consist of biased neurons arranged in layers, connected by weighted connections. Their effectiveness depends on finding the optimal weights and biases that reduce the classification error, which is usually done by using the Back Propagation algorithm (BP). But BP has several disadvantages that could provoke the MLP not to learn. Metaheuristics are alternatives to BP that reach high-quality solutions without using many computational resources. In this work, the Cellular Genetic Algorithm (CGA) with a specially designed crossover operator called Damped Crossover (DX), is proposed to optimise weights and biases of the MLP to classify medical data. When compared against state-of-the-art algorithms, the CGA configured with DX obtained the minimal Mean Square Error value in three out of the five considered medical datasets and was the quickest algorithm with four datasets, showing a better balance between time consumed and optimisation performance. Additionally, it is competitive in enhancing classification quality, reaching the best accuracy with two datasets and the second-best accuracy with two of the remaining.", "Keywords": "Multilayer Perceptron ; Training methods ; Cellular Genetic Algorithm ; Metaheuristics ; Medical data classification", "DOI": "10.1016/j.array.2022.100173", "PubYear": 2022, "Volume": "14", "Issue": "", "JournalId": 66362, "JournalTitle": "Array", "ISSN": "2590-0056", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto Universitario para las Tecnologías de la Información y las Comunicaciones, Consejo Nacional de Investigaciones Científicas y Técnicas, Universidad Nacional de Cuyo, Padre Jorge <PERSON> 1300, Mendoza, M5502JMA, Mendoza, Argentina;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto Universitario para las Tecnologías de la Información y las Comunicaciones, Consejo Nacional de Investigaciones Científicas y Técnicas, Universidad Nacional de Cuyo, Padre Jorge Contreras 1300, Mendoza, M5502JMA, Mendoza, Argentina;Facultad de Ingeniería, Universidad Nacional de Cuyo, Centro Universitario, Mendoza, M5502JMA, Mendoza, Argentina"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto Universitario para las Tecnologías de la Información y las Comunicaciones, Consejo Nacional de Investigaciones Científicas y Técnicas, Universidad Nacional de Cuyo, Padre Jorge Contreras 1300, Mendoza, M5502JMA, Mendoza, Argentina;Facultad de Ingeniería, Universidad Nacional de Cuyo, Centro Universitario, Mendoza, M5502JMA, Mendoza, Argentina"}], "References": [{"Title": "Thorax disease classification with attention guided convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "38", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Artificial neural network based crossover for evolutionary algorithms", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106512", "JournalTitle": "Applied Soft Computing"}, {"Title": "Neuroevolution in Deep Neural Networks: Current Trends and Future Challenges", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "6", "Page": "476", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Artificial Neural Network training using metaheuristics for medical data classification: An experimental study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>les B.C. Miranda", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116423", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94154893, "Title": "An uncertainty-quantification framework for assessing accuracy, sensitivity, and robustness in computational fluid dynamics", "Abstract": "Combining different existing uncertainty quantification (UQ) techniques, a framework is obtained to assess a set of metrics in computational physics problems, in general, and computational fluid dynamics (CFD), in particular. The metrics include accuracy, sensitivity and robustness of the simulator’s outputs with respect to uncertain inputs and parameters. These inputs and parameters are divided into two groups: based on the variation of the first group (e.g. numerical/computational parameters such as grid resolution), a computer experiment is designed, the data of which may become uncertain due to the parameters of the second group (e.g. finite time-averaging). To construct a surrogate model based on uncertain data, Gaussian process regression (GPR) with observation-dependent (heteroscedastic) noise is used. To estimate the propagated uncertainties in the simulator’s outputs from the first group of parameters, a probabilistic version of the polynomial chaos expansion (PCE) is employed Global sensitivity analysis is performed using probabilistic Sobol indices. To illustrate its capabilities, the framework is applied to the scale-resolving simulations of turbulent channel and lid-driven cavity flows using the open-source CFD solver Nek5000. It is shown that at wall distances where the time-averaging uncertainty is high, the quantities of interest are also more sensitive to numerical/computational parameters. In particular for high-fidelity codes such as Nek5000, a thorough assessment of the results’ accuracy and reliability is crucial. The detailed analyses and the resulting conclusions can enhance our insight into the influence of different factors on physics simulations, in particular the simulations of high-Reynolds-number turbulent flows including wall turbulence.", "Keywords": "Uncertainty quantification ; Computational fluid dynamics ; Combined uncertainties ; Polynomial chaos expansion ; Gaussian process regression", "DOI": "10.1016/j.jocs.2022.101688", "PubYear": 2022, "Volume": "62", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Swedish e-Science Research Centre (SeRC), Stockholm, Sweden;SimEx/FLOW, Engineering Mechanics, KTH Royal Institute of Technology, SE-100 44 Stockholm, Sweden;Corresponding author at: SimEx/FLOW, Engineering Mechanics, KTH Royal Institute of Technology, SE-100 44 Stockholm, Sweden"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Swedish e-Science Research Centre (SeRC), Stockholm, Sweden;SimEx/FLOW, Engineering Mechanics, KTH Royal Institute of Technology, SE-100 44 Stockholm, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Swedish e-Science Research Centre (SeRC), Stockholm, Sweden;SimEx/FLOW, Engineering Mechanics, KTH Royal Institute of Technology, SE-100 44 Stockholm, Sweden;Corresponding author"}], "References": [{"Title": "On numerical uncertainties in scale-resolving simulations of canonical wall turbulence", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "105024", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 94154907, "Title": "Algebraic solutions for pricing American put options under the constant elasticity of variance (CEV) model: Application of the Lie group approach", "Abstract": "In this work, the algebraic properties of the American put option under the constant elasticity of variance (CEV) model of financial markets are studied with the implementation of the symmetry approach based on <PERSON>’s group theory. The CEV model is described mathematically in terms of the parabolic time–space partial differential equation (PDE) along with the suitable choice of a terminal condition. The classical Lie symmetry analysis is carried out for the governing PDE. Based on the infinitesimal Lie symmetry generators, new classes of group invariant solutions are derived from the mathematical model and the general as well as the specific cases of the CEV model are thoroughly examined. Finally, the results are plotted against the various emergent parameters and their effect are analyzed and discussed.", "Keywords": "Group invariant solutions ; Lie group theory ; American put option ; CEV model ; Black–<PERSON> equation", "DOI": "10.1016/j.jocs.2022.101680", "PubYear": 2022, "Volume": "62", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School Natural Sciences, National University of Sciences and Technology, Islamabad, 45000, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical and Mechanical Engineering, National University of Sciences and Technology, Rawalpindi, 46070, Pakistan;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Dammam Community College, King Fahd University of Petroleum and Minerals, Dhahran 31261, Saudi Arabia;Interdisciplinary Research Center for Hydrogen and Energy Storage, King Fahd University of Petroleum and Minerals, Dhahran 31261, Saudi Arabia"}], "References": []}, {"ArticleId": 94154910, "Title": "Face detection and grimace scale prediction of white furred mice", "Abstract": "Studying the facial expressions of humans has been one of the major applications of computer vision. An open question is whether common machine learning techniques can also be used to track behaviors of animals, which is a less explored research problem. Since animals are not capable of verbal communication, computer vision solutions can provide valuable information to track the animal’s state. We are particularly interested in pain neurobiology research, where rodent models are extensively used to investigate pain interventions. A grimace scale is used to understand the suffering of a mouse in the presence of interventions, which is inferred from various facial features such as the shape of the eyes and ears. In this work, we automate the prediction of the grimace scale on white furred mice using a machine learning approach, following the same principles used for human facial expression recognition: face detection, landmark region extraction, and expression recognition. We demonstrate the use of the you only look once (YOLO) framework for face detection of the mice with outstanding results. For eye region extraction and grimace pain prediction, we propose a novel structure based on a dilated convolutional network. The experimental results are promising, showing that it is possible to differentiate among the pain scale of the mice.", "Keywords": "Mice pain detection ; Deep learning ; Convolutional neural networks", "DOI": "10.1016/j.mlwa.2022.100312", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, The University of Texas at Dallas, Richardson, TX 75080, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, The University of Texas at Dallas, Richardson, TX 75080, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Medicine, University of Houston, Houston, TX 77004, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Brain and Behavioral Science, The University of Texas at Dallas, Richardson, TX 75080, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, The University of Texas at Dallas, Richardson, TX 75080, USA;Corresponding author"}], "References": [{"Title": "YOLO-face: a real-time face detector", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "805", "JournalTitle": "The Visual Computer"}, {"Title": "Data augmentation using MG-GAN for improved cancer classification on gene expression data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "15", "Page": "11381", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 94154920, "Title": "Spatial feature-based convolutional neural network for PolSAR image classification", "Abstract": "The deep learning algorithm has made great breakthroughs in optical image processing. Some deep learning algorithms require a large number of labeled samples for training. For PolSAR data sets, due to the influence of speckle noise and other factors, high-quality labeled data are limited. Therefore, it is meaningful to use deep learning algorithm to solve PolSAR classification problem in limited labeled dataset. This paper proposes a spatial feature-based convolutional neural network (SF-CNN). The network adopts a dual-branch CNN structure. Both of the two branches have the same structure and share parameters. SF-CNN can receive more than one sample as input. SF-CNN’s special structure can expand the original training set by combining different samples, and alleviate the problem of insufficient labeled training data in PolSAR image classification tasks. When training, SF-CNN maps high-dimensional PolSAR image to low-dimensional feature space. In low-dimensional feature space, SF-CNN enhances the ability of network to extract discriminative features by maximizing or minimizing the distance between feature centers of different classes. In order to dig up the relationship between the samples, the test sample features are compared with every training sample feature when testing. Finally, labels of test samples are determined by the comparison result. The result of SF-CNN in PolSAR image classification task is better than that of standard CNN.", "Keywords": "Deep learning ; PolSAR image classification ; Convolutional neural network", "DOI": "10.1016/j.asoc.2022.108922", "PubYear": 2022, "Volume": "123", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, School of Artificial Intelligence, Xidian University, Xi’an, Shaanxi Province 710071, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, School of Artificial Intelligence, Xidian University, Xi’an, Shaanxi Province 710071, China"}, {"AuthorId": 3, "Name": "Licheng Jiao", "Affiliation": "Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, School of Artificial Intelligence, Xidian University, Xi’an, Shaanxi Province 710071, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Data Analysis Technology Lab, Institute of Applied Mathematics, Henan University, Kaifeng 475004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Li", "Affiliation": "Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, School of Artificial Intelligence, Xidian University, Xi’an, Shaanxi Province 710071, China"}], "References": [{"Title": "Dense connection and depthwise separable convolution based CNN for polarimetric SAR image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105542", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 94155157, "Title": "A Dynamic Load Balancing Algorithm for IoV CoMP Communications", "Abstract": "<p>Due to uneven space–time distribution of vehicles, Internet of Vehicles (IoV) has problems with load imbalance and low resource utilization of Base Stations (BSs) in the Coordinated Multi-Point (CoMP) communication scenario. This paper proposes a dynamic load balancing algorithm based on vehicle prediction. It is assumed that the number of vehicles arriving at the BSs obeys the segmented Poisson distribution to determine the current and predicted load statuses of BSs. First, analyze the load status of each BS and the location of users (vehicles). Then, screen out BSs whose load below the full load threshold as a switchable low-load cooperative cluster, which can convert interference signals into useful signals and reduce the interference between adjacent BSs. Finally, complete load balancing by redistributing the communication service of edge users through sharing channel information and user date among coordinated BSs. Because IoV is a dynamic network, the proposed algorithm runs dynamically in cycles. Simulation results show that the algorithm can perform balance the load of BSs well, the overload rates of BSs during the traffic off-peak period and peak period are reduced significantly, and the average information rate of users is greatly improved.</p>", "Keywords": "Internet of vehicles; load balancing; coordinated multi-point", "DOI": "10.1142/S0218126622502206", "PubYear": 2022, "Volume": "31", "Issue": "12", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information Engineering, Yangtze Normal University, Chongqing, P. R. China;School of Microelectronics and Communication Engineering, Chongqing University, Chongqing, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information Engineering, Yangtze Normal University, Chongqing, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Communication and Information Engineering, Chongqing University of Posts and Telecommunications, Chongqing, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information Engineering, Yangtze Normal University, Chongqing, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology, Guilin, P. R. China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Microelectronics and Communication Engineering, Chongqing University, Chongqing, P. R. China"}], "References": [{"Title": "Constructing a prior-dependent graph for data clustering and dimension reduction in the edge of AIoT", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "381", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 94155283, "Title": "Quantifying the progress of goals in intelligent agents", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAOSE.2022.10046991", "PubYear": 2022, "Volume": "7", "Issue": "2", "JournalId": 22920, "JournalTitle": "International Journal of Agent-Oriented Software Engineering", "ISSN": "1746-1375", "EISSN": "1746-1383", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94155306, "Title": "利用方向矢量分解的近场及严格非圆信源三维定位", "Abstract": "<p>The three-dimensional localization problem for noncircular sources in near-field with a centro-symmetric cross array is rarely studied. In this paper, we propose an algorithm with improved estimation performance. We decompose the multiple parameters of the steering vector in a specific order so that it can be converted into the products of several matrices, and each of the matrices includes only one parameter. On this basis, each parameter to be resolved can be estimated by performing a one-dimensional spatial spectral search. Although the computational complexity of the proposed algorithm is several times that of our previous algorithm, the estimation performance, including its error and resolution, with respect to the direction of arrival, is improved, and the range estimation performance can be maintained. The superiority of the proposed algorithm is verified by simulation results.</p>", "Keywords": "Localization; Centro-symmetric cross array; Noncircular sources; Near-field; Steering vector decomposition; TN911.7; 定位; 中心对称十字阵; 非圆信源; 近场; 方向矢量分解", "DOI": "10.1631/FITEE.2100034", "PubYear": 2022, "Volume": "23", "Issue": "4", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electronic and Information Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China;Key Laboratory of Dynamic Cognitive System of Electromagnetic Spectrum Space, Ministry of Industry and Information Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "Jinqing Shen", "Affiliation": "College of Electronic and Information Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China;Key Laboratory of Dynamic Cognitive System of Electromagnetic Spectrum Space, Ministry of Industry and Information Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic and Information Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China;Key Laboratory of Dynamic Cognitive System of Electromagnetic Spectrum Space, Ministry of Industry and Information Technology, Nanjing, China"}], "References": []}, {"ArticleId": 94155499, "Title": "Knowledge-based strategies for multi-agent teams playing against Nature", "Abstract": "We study teams of agents that play against Nature towards achieving a common objective. The agents are assumed to have imperfect information due to partial observability, and have no communication during the play of the game. We propose a natural notion of higher-order knowledge of agents. Based on this notion, we define a class of knowledge-based strategies, and consider the problem of synthesis of strategies of this class. We introduce a multi-agent extension, MKBSC, of the well-known knowledge-based subset construction applied to such games. Its iterative applications turn out to compute higher-order knowledge of the agents. We show how the MKBSC can be used for the design of knowledge-based strategy profiles, and investigate the transfer of existence of such strategies between the original game and in the iterated applications of the MKBSC, under some natural assumptions. We also relate and compare the “intensional” view on knowledge-based strategies based on explicit knowledge representation and update, with the “extensional” view on finite memory strategies based on finite transducers and show that, in a certain sense, these are equivalent.", "Keywords": "Multi-agent games ; Imperfect information ; Higher-order knowledge ; Knowledge-based strategies ; Strategy synthesis ; Dec-POMDP", "DOI": "10.1016/j.artint.2022.103728", "PubYear": 2022, "Volume": "309", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "KTH Royal Institute of Technology, School of Electrical Engineering and Computer Science, Lindstedtsvägen 3, 100 44 Stockholm, Sweden;Corresponding author. ORCID id: 0000-0002-0074-8786"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Stockholm University, Sweden;Institute for Intelligent Systems, University of Johannesburg, South Africa (visiting professorship)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Rocker AB, Sweden"}], "References": [{"Title": "Knowledge-based programs as succinct policies for partially observable domains", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "288", "Issue": "", "Page": "103365", "JournalTitle": "Artificial Intelligence"}, {"Title": "Game description language and dynamic epistemic logic compared", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "292", "Issue": "", "Page": "103433", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 94155500, "Title": "A conceptual design decision approach by integrating rough Bayesian network and game theory under uncertain behavior selections", "Abstract": "Conceptual design decision plays a vital role in the new product development as it affects the direction of subsequent design activities. However, pertinent literature advocates the uncertainty assessment of the terminal scheme, but ignores the function interactions and the uncertain behavior selections derived from user’s preferences in the function-behavior-structure (FBS) design process. Besides, the decision-makers (DM)’s fuzzy judgments for behavior selections in the FBS model are not been addressed. To fill this gap, a conceptual design decision approach by integrating rough Bayesian Network (BN) and game theory under uncertain behavior selections is proposed, which could provide a graphic probabilistic model-based reasoning for the uncertain design process. In this approach, firstly, a sub-function importance model is constructed to achieve the extraction of the core sub-function module. Then, BN approach is developed to analyze the effect of uncertain behavior on the solution of sub-functions, and then support to predict whether to adopt the optimal scheme. And sub-function BN model is constructed based on the FBS model, and an initial BN model is updated by rough set technology. Finally, the probability distribution of uncertain behavior in interactive sub-functions is obtained from BN model, which is used to transform the uncertain behavior solving problem among sub-functions into a non-cooperative game process based on behavioral probabilities, and the optimal scheme is selected. A case study of tree climbing and trimming machine is used to validate the proposed approach and five principle solutions are selected, then the comparison results showed that the sub-function BN is able to provide a valuable design recommendation in new product development.", "Keywords": "Conceptual scheme ; Functional Bayesian network ; Non-cooperative game ; Function-behavior-structure ; Multi-criteria decision-making", "DOI": "10.1016/j.eswa.2022.117108", "PubYear": 2022, "Volume": "202", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Liting Jing", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou 310023, China;National International Joint Research Center of Special Purpose Equipment and Advanced Processing Technology, Zhejiang University of Technology, Hangzhou 310023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Li", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou 310023, China;National International Joint Research Center of Special Purpose Equipment and Advanced Processing Technology, Zhejiang University of Technology, Hangzhou 310023, China"}, {"AuthorId": 3, "Name": "Jun<PERSON> Ma", "Affiliation": "Department of Industrial and Systems Engineering, Mississippi State University, MS 39762, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hangzhou Hangyang Turbomachinery Co., Ltd, Hangzhou 311300, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou 310023, China;National International Joint Research Center of Special Purpose Equipment and Advanced Processing Technology, Zhejiang University of Technology, Hangzhou 310023, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou 310023, China;National International Joint Research Center of Special Purpose Equipment and Advanced Processing Technology, Zhejiang University of Technology, Hangzhou 310023, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou 310023, China;National International Joint Research Center of Special Purpose Equipment and Advanced Processing Technology, Zhejiang University of Technology, Hangzhou 310023, China;Corresponding author"}], "References": [{"Title": "Sustainability driven multi-criteria project portfolio selection under uncertain decision-making environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106236", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A fuzzy rough number-based AHP-TOPSIS for design concept evaluation under uncertain environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106228", "JournalTitle": "Applied Soft Computing"}, {"Title": "Integrated rough VIKOR for customer-involved design concept evaluation combining with customers’ preferences and designers’ perceptions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101138", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "New design concept evaluation method involving customer preferences based on rough distance to redefined ideal solution", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106677", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Decision modeling and analysis in new product development considering supply chain uncertainties: A multi-functional expert based approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114016", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Effects of the entropy weight on TOPSIS", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114186", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A cooperative game theory based user-centered medical device design decision approach under uncertainty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101204", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Data-driven product design evaluation method based on multi-stage artificial neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107117", "JournalTitle": "Applied Soft Computing"}, {"Title": "Additive manufacturing industrial adaptability analysis using fuzzy Bayesian Network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "107216", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Data science for engineering design: State of the art and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "", "Page": "103447", "JournalTitle": "Computers in Industry"}, {"Title": "An integrated product conceptual scheme decision approach based on <PERSON><PERSON><PERSON><PERSON> value method and fuzzy logic for economic-technical objectives trade-off under uncertainty", "Authors": "<PERSON><PERSON> Jing; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "156", "Issue": "", "Page": "107281", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A rough set-based interval-valued intuitionistic fuzzy conceptual design decision approach with considering diverse customer preference distribution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101284", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Assessment and prioritization method of key engineering characteristics for complex products based on cloud rough numbers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101309", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Design concept evaluation of smart product-service systems considering sustainability: An integrated method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "159", "Issue": "", "Page": "107485", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Improved assessment model for candidate design schemes with an interval rough integrated cloud model under uncertain group environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "104352", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A function-oriented biologically analogical approach for constructing the design concept of smart product in Industry 4.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101352", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An automatic method for constructing machining process knowledge base from knowledge graph", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102222", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 94155531, "Title": "Complexity reduction and approximation of multidomain systems of partially ordered data", "Abstract": "Two greedy algorithms for the synthesis and approximation of multidomain systems of partially ordered data are proposed. Given k input partially ordered sets (posets) on the same elements, the algorithms search for the optimally approximating partial orders, minimizing the dissimilarity between the generated and input posets, based on their matrices of mutual ranking probabilities. A general approximation algorithm is developed, together with a specific procedure for approximation over bucket orders, which are the natural choice when the goal is to “condense” the inputs into rankings, possibly with ties. Different loss functions are also employed, and their outputs are compared. A real example pertaining to regional well-being in Italy motivates the algorithms and shows them in action.", "Keywords": "Bucket order ; Complexity reduction ; Multi-indicator system ; Multidimensional ordinal data ; Partially ordered set ; Ranking", "DOI": "10.1016/j.csda.2022.107520", "PubYear": 2022, "Volume": "173", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "MEMOTEF Department, Sapienza University of Rome, Via Del Castro Laurenziano 9, 00161, Rome, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Quantitative Methods, University of Milano-Bicocca, Piazza dell'Ateneo Nuovo, 1, 20126, Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Quantitative Methods, University of Milano-Bicocca, Piazza dell'Ateneo Nuovo, 1, 20126, Milan, Italy"}], "References": []}, {"ArticleId": 94155650, "Title": "A Novel Hybrid Encryption Method to Secure Healthcare Data in IoT-enabled Healthcare Infrastructure", "Abstract": "Nowadays, smart devices are playing a vital role to overtake the conventional healthcare management system. Here, the Internet of Things (IoT) leads the healthcare industry towards its expansion, where things can be connected anytime from anywhere, even in a heterogeneous environment. However, the security of healthcare data and preserving the privacy of users are major concerns in IoT-enabled healthcare infrastructure. This paper presents a novel encryption scheme using elliptic curve cryptography, Advanced Encryption Standard (AES), and Serpent to secure healthcare data in IoT-enabled healthcare infrastructure. This proposed hybrid encryption technique improves security measures of the healthcare data by incorporating both symmetric and asymmetric-based encryption techniques. Moreover, the proposed scheme also ensures data integrity by using the elliptic curve-based digital signature. To prove the efficiency of the proposed scheme, both formal security analysis and performance comparisons are presented in this paper. Results and discussion prove the effectiveness of the proposed scheme.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.107991", "PubYear": 2022, "Volume": "101", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Patna, Bihar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Patna, Bihar, India;Corresponding author"}], "References": [{"Title": "Efficient algorithm for big data clustering on single machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> V<PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "9", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "An efficient public key secure scheme for cloud and IoT security", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "634", "JournalTitle": "Computer Communications"}, {"Title": "A ciphertext-policy Attribute based encryption scheme for wireless body area networks based on ECC", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "102559", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "An improved co-designed AES-ECC cryptosystem for secure data transmission", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Medien <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "118", "JournalTitle": "International Journal of Information and Computer Security"}, {"Title": "Fast and Secure Data Accessing by Using DNA Computing for the Cloud Environment", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "2289", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Preserving Data Confidentiality in Internet of Things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "BILROST: Handling Actuators of the Internet of Things through Tweets on Twitter using a Domain- Specific Language", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "6", "Page": "133", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Application of Artificial Intelligence Algorithms Within the Medical Context for Non-Specialized Users: the CARTIER-IA Platform", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>-<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "6", "Page": "46", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Improving security of medical big data by using Blockchain technology", "Authors": "<PERSON><PERSON><PERSON>; Malaya <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "107529", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 94155788, "Title": "Blockchain Technology Applied in IoV Demand Response Management: A Systematic Literature Review", "Abstract": "<p>Energy management in the Internet of Vehicles (IoV) is becoming more prevalent as the usage of distributed Electric Vehicles (EV) grows. As a result, Demand Response (DR) management has been introduced to achieve efficient energy management in IoV. Through DR management, EV drivers are allowed to adjust their energy consumption and generation based on a variety of parameters, such as cost, driving patterns and driving routes. Nonetheless, research in IoV DR management is still in its early stages, and the implementation of DR schemes faces a number of significant hurdles. Blockchain is used to solve some of them (e.g., incentivization, privacy and security issues, lack of interoperability and high mobility). For instance, blockchain enables the introduction of safe, reliable and decentralized Peer-to-Peer (P2P) energy trading. The combination of blockchain and IoV is a new promising approach to further improve/overcome the aforementioned limitations. However, there is limited literature in Demand Response Management (DRM) schemes designed for IoV. Therefore, there is a need for a systematic literature review (SLR) to collect and critically analyze the existing relevant literature, in an attempt to highlight open issues. Thus, in this article, we conduct a SLR, investigating how blockchain technology assists the area of DRM in IoV. We contribute to the body of knowledge by offering a set of observations and research challenges on blockchain-based DRM in IoV. In doing so, we allow other researchers to focus their work on them, and further contribute to this area.</p>", "Keywords": "blockchain; smart grid; internet of vehicles; demand response; systematic literature review blockchain ; smart grid ; internet of vehicles ; demand response ; systematic literature review", "DOI": "10.3390/fi14050136", "PubYear": 2022, "Volume": "14", "Issue": "5", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for the Future, Department of Digital Innovation, University of Nicosia, Nicosia 2414, Cyprus"}], "References": [{"Title": "GUARDIAN: Blockchain-Based Secure Demand Response Management in Smart Grid System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "613", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Blockchain for Internet of Energy management: Review, solutions, and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "395", "JournalTitle": "Computer Communications"}, {"Title": "Blockchain-Based On-Demand Computing Resource Trading in IoV-Assisted Smart City", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1373", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Load Forecasting in an Office Building with Different Data Structure and Learning Parameters", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "242", "JournalTitle": "Forecasting"}, {"Title": "Blockchain-based prosumer incentivization for peak mitigation through temporal aggregation and contextual clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "100016", "JournalTitle": "Blockchain: Research and Applications"}, {"Title": "Blockchain Application in Internet of Vehicles: Challenges, Contributions and Current Limitations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "12", "Page": "313", "JournalTitle": "Future Internet"}, {"Title": "Blockchain application in P2P energy markets: social and legal aspects", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "1066", "JournalTitle": "Connection Science"}]}, {"ArticleId": 94155809, "Title": "Using KNN Algorithms for Determining the Recipient of Smart Indonesia Scholarship Program", "Abstract": "<p>The Smart Indonesia Card (KIP) scholarship program is a government scholarship program through the Ministry of Religion of the Republic of Indonesia which is given to students who have a good academic level but have a weak economic level. Sultan <PERSON><PERSON><PERSON> Islamic University, Riau accepts new students every year, but the quota for the KIP scholarship program is limited. With the limited quota for the KIP program, a system is needed that is able to classify submission data from students who register for the KIP program, so that the selection process can be carried out, quickly, precisely, and in accordance with the required quota. In this study, the K-Modes and K-Nearest Neighbor (KNN) Algorithms were used by using the achievement variables, report cards, and national exam scores when high school, father's income, parental status, and homeownership status. Reprocessing is carried out before the testing stage, testing is carried out by performing the initial stages, namely clustering using the K-Modes algorithm, then validating or testing data by applying the Grid Search Cross-Validation (GSCV) method, and finally predicting using the KNN algorithm. The test resulted in a performance value of 66.79%</p>", "Keywords": "", "DOI": "10.35143/jkt.v7i2.4962", "PubYear": 2021, "Volume": "7", "Issue": "2", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>to Maspur", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}, {"ArticleId": 94155890, "Title": "Multi-objective optimization of directed energy deposition process by using Taguchi-Grey relational analysis", "Abstract": "<p>In this study, a multi-objective optimization of directed energy deposition (DED) process was conducted with Taguchi-Grey relational analysis. The used part was designed as a flat rectangle which would be deposited by a single-layer and multi-track DED process. Firstly, after finishing Taguchi experiments, the effects of five control factors (laser power, overlap ratio, powder feed rate, scanning speed and laser defocus distance) on three DED product qualities (cladding efficiency, surface roughness and porosity) were, respectively, analyzed. Then, through Grey relational analysis (GRA), an optimal factor setting which can take all qualities into account was found and had better deposition results compared with previous setting. Furthermore, ANOVAs were conducted to find out significant factors of each qualities. By using the significant factors as variations, three second-order polynomial regression predictive models for qualities were created. Based on the GRA and ANOVAs results, additional one-factor-at-a-time (OFAT) experiments which used the optimal setting as the center point were performed. The qualities variation resulting from adjusting overlap ratio and laser defocus distance of optimal setting were investigated, and the results were also used as additional data to verified the accuracies of three regression models.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2022.</p>", "Keywords": "Cladding efficiency;Directional energy deposition;Porosity;Surface roughness;Taguchi-Grey relational analysis", "DOI": "10.1007/s00170-022-09210-3", "PubYear": 2022, "Volume": "120", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Cheng Kung University, No.1, University Road, Tainan, 701401 Taiwan."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Cheng Kung University, No.1, University Road, Tainan, 701401 Taiwan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Cheng Kung University, No.1, University Road, Tainan, 701401 Taiwan."}], "References": [{"Title": "Process optimization for directed energy deposition of SS316L components", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "5-6", "Page": "1387", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 94155995, "Title": "Augmented Convolutional Neural Network Models with Relative Multi-Head Attention for Target Recognition in Infrared Images", "Abstract": "<p>Deep convolutional neural network (CNN) models are typically trained on high-resolution images. When we apply them directly to low-resolution infrared images, for example, the performances will not always be satisfactory. This is due to CNN layers that operate in a local neighborhood, which is already poor in information for infrared images. To overcome these weaknesses and increase information of global nature, a hybrid architecture based on CNN with self-attention mechanism is proposed. This later provides information about the global context by capturing the long-range interactions between the different parts of an image. In this paper, we have incorporated a convolutional–attentional form in the top layers of two pre-trained networks VGGNet and ResNet. The convolutional–attentional form is a concatenation of two paths; the original convolutional feature maps of the pre-trained network, and the output of a relative multi-head attentional block. Extensive experiments are conducted in the FLIR starter thermal dataset, where we achieve a [Formula: see text] overall accuracy in the four-class FLIR starter thermal dataset. Moreover, the proposed architectures exceed the state of the art in target recognition on two-class FLIR starter thermal dataset with a [Formula: see text] improvement in overall classification accuracy. In addition, a study on the effect of different hyper-parameters and error analysis is carried out to give some research forward directions.</p>", "Keywords": "Convolution; FLIR; infrared images; self-attention; target recognition; transfer learning", "DOI": "10.1142/S2301385023500085", "PubYear": 2023, "Volume": "11", "Issue": "3", "JournalId": 26691, "JournalTitle": "Unmanned Systems", "ISSN": "2301-3850", "EISSN": "2301-3869", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ecole Militaire Polytechnique, UER SAI, Algiers 16111, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ecole Militaire Polytechnique, UER SAI, Algiers 16111, Algeria"}, {"AuthorId": 3, "Name": "Abdelkrim Nemra", "Affiliation": "Ecole Militaire Polytechnique, UER SAI, Algiers 16111, Algeria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Laboratoire MIA, Université de La Rochelle, Avenue Michel Crépeau, F-17042 La Rochelle Cedex, France"}], "References": [{"Title": "A Novel Framework for Multiple Ground Target Detection, Recognition and Inspection in Precision Agriculture Applications Using a UAV", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "1", "Page": "45", "JournalTitle": "Unmanned Systems"}, {"Title": "Low-Latency Aerial Images Object Detection for UAV", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "1", "Page": "57", "JournalTitle": "Unmanned Systems"}]}, {"ArticleId": 94156006, "Title": "HINNPerf: Hierarchical Interaction Neural Network for Performance Prediction of Configurable Systems", "Abstract": "<p> Modern software systems are usually highly configurable, providing users with customized functionality through various configuration options. Understanding how system performance varies with different option combinations is important to determine optimal configurations that meet specific requirements. Due to the complex interactions among multiple options and the high cost of performance measurement under a huge configuration space, it is challenging to study how different configurations influence the system performance. To address these challenges, we propose HINNPerf , a novel hierarchical interaction neural network for performance prediction of configurable systems. HINNPerf employs the embedding method and hierarchic network blocks to model the complicated interplay between configuration options, which improves the prediction accuracy of the method. In addition, we devise a hierarchical regularization strategy to enhance the model robustness. Empirical results on 10 real-world configurable systems show that our method statistically significantly outperforms state-of-the-art approaches by achieving average 22.67% improvement in prediction accuracy. In addition, combined with the Integrated Gradients method, the designed hierarchical architecture provides some insights about the interaction complexity and the significance of configuration options, which might help users and developers better understand how the configurable system works and efficiently identify significant options affecting the performance. </p>", "Keywords": "", "DOI": "10.1145/3528100", "PubYear": 2023, "Volume": "32", "Issue": "2", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sun Yat-sen University, Guangzhou, Guangdong Province, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sun Yat-sen University, Guangzhou, Guangdong Province, China"}], "References": []}, {"ArticleId": 94156007, "Title": "Motivation effect of animated pedagogical agent's personality and feedback strategy types on learning in virtual training environment", "Abstract": "<b  >Background</b> The personality and feedback of an animated pedagogical agent (APA) are vital social-emotional features that render the agent perceptually believable. The effect of them on learning in virtual training remains to be examined. <b  >Methods</b> In this paper, an explanation model was proposed to clarify the underlying mechanism of how these two features affect learners. Two studies were conducted to investigate the model. Study 1 reexamined the effect of APA’s personality type and feedback strategy on flow experience and performance, revealing significant effects of feedback strategy on flow and performance, as well as a marginal significant effect of personality type on performance. To explore the mechanism behind these effects, a theoretical model was proposed by distinguishing between intrinsic and extrinsic motivation effect. Study 2 tested the model and round that the APA’s personality type significantly influences factors in the path of extrinsic motivation effect rather than those in the path of intrinsic motivation effect. <b  >Results</b> By contrast, feedback strategy significantly affected factors in the path of intrinsic motivation effect. <b  >Conclusions</b> The proposed model was supported by these results; further distinguishing the two motivation effects is necessary to understand the respective effects of an APA’s personality and feedback features on learning experiences and outcomes.", "Keywords": "Human-centered computing ; Visualization ; Visualization design and evaluation methods", "DOI": "10.1016/j.vrih.2021.11.001", "PubYear": 2022, "Volume": "4", "Issue": "2", "JournalId": 61022, "JournalTitle": "Virtual Reality & Intelligent Hardware", "ISSN": "2096-5796", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical, Electrical and Information Engineering, Shandong University, Shandong, China;Engineering Research Center of Digital Media Technology, MOE, Shandong University, Shandong, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94156088, "Title": "Multilayer cellular learning automata: A computational model to solve multilayer infrastructure problems with its application in community detection for multilayer networks", "Abstract": "Multilayer networks are one type of complex systems that have multiple types of interactions among their nodes. Through the development and extension of the real-world networks, especially social and biological networks, understanding the structure and organization of multilayer systems become more significant. In this paper, we propose a new computational model based on learning automata to overcome the problems related to multilayer networks. In our proposed model, the whole network is modeled as a set of layers in which an irregular cellular learning automata (ICLA) is assigned to each node in a layer. The functionality of cellular learning automata (CLA) is dependent on two important features: local and global environments. In this model, the information from the other layers is evaluated as the global environments, and update rules are performed based on the information from the other layers. Finally, by the convergence of CLA in each layer the global environments cause the multilayer cellular learning automata (MLCLA) to cover all possible states in all nodes of layers. We apply this model to community detection in multilayer networks as an example of the application of the proposed model. To solve this, each CLA by the depth-first search algorithm detects the optimal community in a layer and then sends the information about the identified community to the next layer as a feature vector. The simulation results on multiple well-known multilayer datasets demonstrate the effectiveness and superiority of the proposed algorithm in terms of similarity and modularity measures, and the analysis results of the proposed algorithm in terms of Precision, Recall, and F1-score are higher than the other available community detection methods.", "Keywords": "Multilayer network ; Community detection ; Cellular learning automata ; Complex network", "DOI": "10.1016/j.jocs.2022.101683", "PubYear": 2022, "Volume": "61", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, College of Engineering, Hamedan Branch, Islamic Azad University, Hamedan, Iran"}], "References": [{"Title": "A multi-population differential evolution algorithm based on cellular learning automata and evolutionary context information for optimization in dynamic environments", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106009", "JournalTitle": "Applied Soft Computing"}, {"Title": "A new irregular cellular learning automata-based evolutionary computation for time series link prediction in social networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "1", "Page": "71", "JournalTitle": "Applied Intelligence"}, {"Title": "CFIN: A community-based algorithm for finding influential nodes in complex social networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "3", "Page": "2207", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A comparative study of overlapping community detection methods from the perspective of the structural properties", "Authors": "<PERSON><PERSON><PERSON>; Carolina Ribeiro Xavier; Alexandre <PERSON> Ev<PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}, {"Title": "A survey of community detection methods in multilayer networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "1", "Page": "1", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Attractive community detection in academic social network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "", "Page": "101331", "JournalTitle": "Journal of Computational Science"}]}, {"ArticleId": 94156187, "Title": "A user-centric computer-aided verification process in a virtuality-reality continuum", "Abstract": "Although companies systematically strive for a full digitalisation of their products and their processes, the design phase shows that the quality of models is very unequal. Indeed, detailed design benefits from much more sophisticated methods and tools than the specification and architecture activities. Although, we should note the recent paradigm shift from document-based to model-based systems engineering, these models, which are mainly static 2D diagrams, remain poor to facilitate design verification early on. Thus, to detect most errors during the design phase, companies have no other alternative than to wait up to the testing phase which occurs after several years of development for complex systems. Thus, we propose a user-centric computer-aided verification process to ensure that the design meets the requirements under realistic operational conditions. The verification process provides a progressive immersion into the virtual system before seamlessly transitioning to the real system. Our work is built upon state-of-the-art MBSE methods such as the Property Model Methodology, which enables systems engineers to co-simulate specification models and design models. We improve such MBSE methods by increasing the level of realism that experiences the end-user during the verification of a design by the original combination of Model-In-the-Loop, Immersive Model-In-the-Loop, Human-In-the-Loop, and Hardware-In-the-Loop simulation strategies. A robot arm is used as a use case to illustrate the verification process.", "Keywords": "Model-based systems engineering ; Validation ; Verification ; User-centred design ; Virtual reality ; Human-in-the-loop simulation ; Immersion ; Realism", "DOI": "10.1016/j.compind.2022.103678", "PubYear": 2022, "Volume": "140", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS, Grenoble INP, G-SCOP, Grenoble, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS, Grenoble INP, G-SCOP, Grenoble, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS, Grenoble INP, G-SCOP, Grenoble, France"}], "References": []}, {"ArticleId": 94156214, "Title": "Data on occurrence of perfluoroalkyl substances in influents and effluents collected from different wastewater treatment plants in Latvia", "Abstract": "The data set provided in this paper contains occurrence data of 17 perfluoroalkyl substances (PFAS) (including 10 perfluorinated carboxylic acids and 7 perfluorinated sulfonic acids) in influent and effluent samples collected from 43 wastewater treatment plants (WWTPs) located in different cities in Latvia. Samples were collected in the period June-July 2021. In each WWTP one influent and one effluent sample were collected on the same day. Extraction and clean-up of the samples were performed using solid phase extraction (SPE) on a weak-anion SPE phase. Observed extracts were analysed using high performance liquid chromatography coupled with Orbitrap high resolution mass spectrometry (HPLC-Orbitrap-MS) on the content of selected PFAS representatives. The collected data are with fundamental scientific value and can be applied for local data analysis. The data set is useful for the estimation of overall background levels of PFAS and evaluation of local city WWTPs wastewater remediation efficiency towards the removal of PFAS contamination.", "Keywords": "Occurrence;Perfluorocarboxylic acid;Perfluorosulfonic acid;Wastewater", "DOI": "10.1016/j.dib.2022.108228", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Dzintars Zacs", "Affiliation": "Institute of Food Safety, Animal Health and Environment \"BIOR\", Lejupes iela 3, Riga LV-1076, Latvia."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Food Safety, Animal Health and Environment \"BIOR\", Lejupes iela 3, Riga LV-1076, Latvia. ;University of Latvia, Faculty of Chemistry, Jelgavas street 1, Riga LV-1004, Latvia."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Food Safety, Animal Health and Environment \"BIOR\", Lejupes iela 3, Riga LV-1076, Latvia. ;University of Latvia, Faculty of Chemistry, Jelgavas street 1, Riga LV-1004, Latvia."}], "References": []}, {"ArticleId": 94156329, "Title": "Measuring the Success of ERP via Organizational Climate Constructs", "Abstract": "<p>Agencies depend on enterprise resource planning (ERP) to achieve success. However, the factors that lead to the success and use of these ERP systems have not attracted the needed attention. This study investigates the factors that influence the success and use of an ERP system. The proposed model is based on a quantitative and a mixed-method case study (MM-CS). The results show that organizational climate has a positive effect on information quality, system quality, and service quality constructs of the DeLone and McLean IS success model. The quality constructs also affect user satisfaction negatively as compared to previous studies.</p>", "Keywords": "", "DOI": "10.4018/IJISSS.302882", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 27009, "JournalTitle": "International Journal of Information Systems in the Service Sector", "ISSN": "1935-5688", "EISSN": "1935-5696", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Electronic Science and Technology of China, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Electronic Science and Technology of China, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Ghana, Ghana"}], "References": [{"Title": "An Integrative Framework on Mobile Banking Success", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "1", "Page": "16", "JournalTitle": "Information Systems Management"}, {"Title": "A Compilation and Analysis of Critical Success Factors for the ERP Implementation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "2", "Page": "107", "JournalTitle": "International Journal of Enterprise Information Systems"}, {"Title": "Gender difference in the continuance intention to e-file income tax returns in Pakistan", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "2", "Page": "147", "JournalTitle": "Information Polity"}]}, {"ArticleId": ********, "Title": "Prime automata do not exist", "Abstract": "<PERSON><PERSON><PERSON><PERSON> proposed the concept of prime automaton when he studied the arithmetical properties of finite automaton in 2003, but at the time, he did not know the existence of prime automaton. In this paper, we proved that all finite accessible automata are non-prime.", "Keywords": "Finite automata ; Directed graph ; Prime automata", "DOI": "10.1016/j.tcs.2022.04.027", "PubYear": 2022, "Volume": "922", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Tsinghua University, Beijing 100084, People's Republic of China"}], "References": []}, {"ArticleId": 94156375, "Title": "Data-informed knowledge and strategies", "Abstract": "The article proposes a new approach to reasoning about knowledge and strategies in multiagent systems. It emphasizes data, not agents, as the source of strategic knowledge. The approach brings together Armstrong&#x27;s functional dependency from database theory, a data-informed knowledge modality based on a recent work by <PERSON><PERSON><PERSON> and <PERSON>, and a newly proposed data-informed strategy modality. The main technical result is a sound and complete logical system that describes the interplay between these three logical operators.", "Keywords": "Knowledge ; Strategy ; Know-how ; Multiagent system ; Data ; Completeness", "DOI": "10.1016/j.artint.2022.103727", "PubYear": 2022, "Volume": "309", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Logic and Intelligence, Southwest University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Southampton, Southampton, United Kingdom;Corresponding author"}], "References": [{"Title": "An epistemic logic of blameworthiness", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "283", "Issue": "", "Page": "103269", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 94156483, "Title": "Complexity-guided container replacement synthesis", "Abstract": "<p>Containers, such as lists and maps, are fundamental data structures in modern programming languages. However, improper choice of container types may lead to significant performance issues. This paper presents Cres, an approach that automatically synthesizes container replacements to improve runtime performance. The synthesis algorithm works with static analysis techniques to identify how containers are utilized in the program, and attempts to select a method with lower time complexity for each container method call. Our approach can preserve program behavior and seize the opportunity of reducing execution time effectively for general inputs. We implement Cres and evaluate it on 12 real-world Java projects. It is shown that <PERSON><PERSON> synthesizes container replacements for the projects with 384.2 KLoC in 14 minutes and discovers six categories of container replacements, which can achieve an average performance improvement of 8.1%.</p>", "Keywords": "data structure specification; program optimization; program synthesis", "DOI": "10.1145/3527312", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hong Kong University of Science and Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hong Kong University of Science and Technology, China"}, {"AuthorId": 3, "Name": "Wensheng Tang", "Affiliation": "Hong Kong University of Science and Technology, China"}, {"AuthorId": 4, "Name": "Qingkai Shi", "Affiliation": "Ant Group, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Hong Kong University of Science and Technology, China"}], "References": [{"Title": "Synthesizing replacement classes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Shape Analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1–2", "Page": "1", "JournalTitle": "Foundations and Trends® in Programming Languages"}, {"Title": "Improving energy-efficiency by recommending Java collections", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "3", "Page": "1", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 94156571, "Title": "An Agile New Research Framework for Hybrid Human-AI Teaming: Trust, Transparency, and Transferability", "Abstract": "<p>We propose a new research framework by which the nascent discipline of human-AI teaming can be explored within experimental environments in preparation for transferal to real-world contexts. We examine the existing literature and unanswered research questions through the lens of an Agile approach to construct our proposed framework. Our framework aims to provide a structure for understanding the macro features of this research landscape, supporting holistic research into the acceptability of human-AI teaming to human team members and the affordances of AI team members. The framework has the potential to enhance decision-making and performance of hybrid human-AI teams. Further, our framework proposes the application of Agile methodology for research management and knowledge discovery. We propose a transferability pathway for hybrid teaming to be initially tested in a safe environment, such as a real-time strategy video game, with elements of lessons learned that can be transferred to real-world situations.</p>", "Keywords": "hybrid teams; human-machine; human-AI; teaming; collaboration; trust; transparency; video games", "DOI": "10.1145/3514257", "PubYear": 2022, "Volume": "12", "Issue": "3", "JournalId": 13925, "JournalTitle": "ACM Transactions on Interactive Intelligent Systems", "ISSN": "2160-6455", "EISSN": "2160-6463", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Australian National University, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Australian National University, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Queensland University of Technology, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Defence Science and Technology, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Australian National University, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Australian National University, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Queensland University of Technology, Australia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Queensland University of Technology, Australia"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "University of Queensland, Australia"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Queensland University of Technology, Australia"}], "References": [{"Title": "Machines as teammates: A research agenda on AI in team collaboration", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "2", "Page": "103174", "JournalTitle": "Information & Management"}, {"Title": "Not All Information Is Equal: Effects of Disclosing Different Types of Likelihood Information on Trust, Compliance and Reliance, and Task Performance in Human-Automation Teaming", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "6", "Page": "987", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Decentralized Trust Management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Collaborating with technology-based autonomous agents", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "Internet Research"}, {"Title": "How perceptions of intelligence and anthropomorphism affect adoption of personal intelligent agents", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "343", "JournalTitle": "Electronic Markets"}, {"Title": "Combining gaze and AI planning for online human intention recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "284", "Issue": "", "Page": "103275", "JournalTitle": "Artificial Intelligence"}, {"Title": "Fair AI", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "4", "Page": "379", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Towards a Theory of Longitudinal Trust Calibration in Human–Robot Teams", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Mal<PERSON> F<PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "459", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Measuring the Quality of Explanations: The System Causability Scale (SCS)", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2", "Page": "193", "JournalTitle": "KI - Künstliche Intelligenz"}, {"Title": "Effect of automation transparency in the management of multiple unmanned vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "103243", "JournalTitle": "Applied Ergonomics"}, {"Title": "Towards multi-modal causability with Graph Neural Networks enabling information fusion for explainable AI", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "28", "JournalTitle": "Information Fusion"}, {"Title": "Dancing With Algorithms: Interaction Creates Greater Preference and Trust in Machine-Learned Behavior", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "5", "Page": "854", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Modeling communication of collaborative multiagent system under epistemic planning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "10", "Page": "5959", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 94156599, "Title": "Illumination Invariance Adaptive Sidewalk Detection Based on Unsupervised Feature Learning", "Abstract": "<p>To solve the problem of road recognition when the robot is driving on the sidewalk, a novel sidewalk detection algorithm from the first-person perspective is proposed, which is crucial for robot navigation. The algorithm starts from the illumination invariance graph of the sidewalk image, and the sidewalk “seeds” are selected dynamically to get the sidewalk features for unsupervised feature learning. The final sidewalk region will be extracted by multi-threshold adaptive segmentation and connectivity processing. The key innovations of the proposed algorithm are the method of illumination invariance based on PCA and the unsupervised feature learning for sidewalk detection. With the PCA-based illumination invariance, it can calculate the lighting invariance angle dynamically to remove the impact of illumination and different brick colors’ influence on sidewalk detection. Then the sidewalk features are selected dynamically using the parallel geometric structure of the sidewalk, and the confidence region of the sidewalk is obtained through unsupervised feature learning. The proposed method can effectively suppress the effects of shadows and different colored bricks in the sidewalk area. The experimental result proves that the F-measure of the proposed algorithm can reach 93.11% and is at least 7.7% higher than the existing algorithm.</p>", "Keywords": "Sidewalk detection; robot navigation; shadow removal; parallel geometric features; unsupervised feature learning", "DOI": "10.1142/S0219467823500274", "PubYear": 2023, "Volume": "23", "Issue": "4", "JournalId": 14884, "JournalTitle": "International Journal of Image and Graphics", "ISSN": "0219-4678", "EISSN": "1793-6756", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering, Yanshan University, Qinhuangdao, Hebei 066004, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Engineering Research, Center of Intelligent Control System and Intelligent Equipment Ministry of Education, Key Laboratory of Intelligent Rehabilitation and Neromodulation of Hebei Province, Yanshan University, Qinhuangdao, Hebei 066004, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering, Yanshan University, Qinhuangdao, Hebei 066004, P. R. <PERSON>"}], "References": [{"Title": "Road detection based on simultaneous deep learning approaches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "103605", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Measuring Urban Sidewalk Practicability: a Sidewalk Robot Feasibility Index", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "15053", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 94156613, "Title": "A fake threshold visual cryptography of QR code", "Abstract": "<p>Visual cryptography (VC) is a technique that can encrypt images without complicated calculation. As the image, the quick response (QR) code can also be encrypted by VC to ensure its safe transmission on the network. In recent years, VC has developed rapidly. The original VC has been developed into many forms, such as ( n , n )-VC, ( k , n )-VC. These new VC schemes can be applied to the QR code to make it more secure in the network. This paper proposes a new VC scheme for the QR code by using the mechanism of the error correction code (ECC). ECC is used to control the number of shares to recover whether the secret QR code can be decoded correctly. The codewords in the same block of the QR code are evenly distributed to each share. If the number of shares is less than the threshold, the restored QR code will not be decoded correctly. The number of wrong codewords exceeds its error correction capability. The theoretical analysis shows that the proposed scheme is secure when the standard decoder is used. The experimental results also prove the feasibility of the proposed scheme.</p>", "Keywords": "Safe transmission; Error correction code; QR code; Visual cryptography", "DOI": "10.1007/s11042-022-13011-x", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Electronic and Information Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology, Qingdao, China"}], "References": [{"Title": "Flexible meaningful visual multi-secret sharing scheme by random grids", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7705", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Digital watermarking with improved SMS applied for QR code", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "104049", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "XOR-based visual secret sharing scheme using pixel vectorization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "10", "Page": "14609", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 94156621, "Title": "Docosahexaenoic acid enhances hippocampal insulin sensitivity to promote cognitive function of aged rats on a high-fat diet", "Abstract": "<p><b>INTRODUCTION</b>:Diminished brain insulin sensitivity is associated with reduced cognitive function. Docosahexaenoic acid (DHA) is known to maintain normal brain function.</p><p><b>OBJECTIVES</b>:This study aimed to determine whether DHA impacts hippocampal insulin sensitivity and cognitive function in aged rats fed a high-fat diet (HFD).</p><p><b>METHODS</b>:Eight-month-old female Sprague-Dawley rats were randomly divided into three groups (n = 50 each). Rats in the aged group, HFD group, and DHA treatment group received standard diet (10 kcal% fat), HFD (45 kcal% fat), and DHA-enriched HFD (45 kcal% fat, 1% DHA, W/W) for 10 months, respectively. Four-month-old female rats (n = 40) that received a standard diet served as young controls. Neuroinflammation, oxidative stress, amyloid formation, and tau phosphorylation in the hippocampus, as well as systemic glucose homeostasis and cognitive function, were tested.</p><p><b>RESULTS</b>:DHA treatment relieved a block in the insulin signaling pathway and consequently protected aged rats against HFD-induced hippocampal insulin resistance. The beneficial effects were explained by a DHA-induced decrease in systemic glucose homeostasis dysregulation, hippocampal neuroinflammation and oxidative stress. In addition, DHA treatment broke the reciprocal cycle of hippocampal insulin resistance, Aβ burden, and tau hyperphosphorylation. Importantly, treatment of model rats with DHA significantly increased their cognitive capacity, as evidenced by their increased hippocampal-dependent learning and memory, restored neuron morphology, enhanced cholinergic activity, and activated cyclic AMP-response element-binding protein.</p><p><b>CONCLUSION</b>:DHA improves cognitive function by enhancing hippocampal insulin sensitivity.</p><p>Copyright © 2022. Production and hosting by Elsevier B.V.</p>", "Keywords": "Aging;Cognitive function;Docosahexaenoic acid;High-fat diet;Hippocampus;Insulin resistance", "DOI": "10.1016/j.jare.2022.04.015", "PubYear": 2023, "Volume": "45", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Nutriology, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China; Hubei Key Laboratory of Lipid Chemistry and Nutrition, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Nutriology, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China; Hubei Key Laboratory of Lipid Chemistry and Nutrition, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China."}, {"AuthorId": 3, "Name": "Congcong Ma", "Affiliation": "Department of Nutriology, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China; Hubei Key Laboratory of Lipid Chemistry and Nutrition, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Nutrition and Food Hygiene, School of Public Health, Wuhan University of Science and Technology, Wuhan 430065, P.R. China."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Clinical Nutrition, Tongji Hospital, Tongji Medical College, Huazhong University of Science and Technology, 1095 Jiefang Ave, Wuhan 430030, P.R. China."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Neurology, Hubei Provincial Hospital of Integrated Chinese &amp; Western Medicine, No. 11, Lingjiaohu Road, Wuhan 430015, P.R. China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Nutriology, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China; Hubei Key Laboratory of Lipid Chemistry and Nutrition, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Nutriology, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China; Hubei Key Laboratory of Lipid Chemistry and Nutrition, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Nutriology, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China; Hubei Key Laboratory of Lipid Chemistry and Nutrition, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Nutriology, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China; Hubei Key Laboratory of Lipid Chemistry and Nutrition, Oil Crops Research Institute, Chinese Academy of Agricultural Sciences, 2 Xudong Second Road, Wuhan 430062, P.R. China. Electronic address:  ."}], "References": []}, {"ArticleId": 94156631, "Title": "A new approach for 2-D and 3-D precise measurements of ground deformation from optimized registration and correlation of optical images and ICA-based filtering of image geometry artifacts", "Abstract": "High resolution satellite images with improved spatial and temporal resolution provide unprecedented opportunities to monitor Earth Surface changes in 2D and 3D due, for example, to earthquakes, sand dune migration, ice flow, or landslides. The volume of imagery available for such measurements is rapidly growing but the exploitation of these data is challenging due to the various sources of geometric distortions of the satellite imagery. Here we propose a new approach to extract high-quality surface displacement in 3D based on the correlation of multi-date and multi-platform high resolution optical imagery. We additionally show that when a large enough volume of data is available, it is possible to separate the deformation signal from the artifacts due to the satellite jitter and misalignment of the CCDs, which, together with topographic artifacts, are the main source of noise in the measurements. Our method makes use of a reference DEM, but the outcome is independent of the characteristics of the chosen DEM. We use the case-example of the ground deformation caused by the Ridgecrest earthquake sequence to assess the performance of our proposed approach. We show that it outperforms the more standard approach which combines 2-D correlation and DEM differencing. With our technique, we were able to generate high quality measurements of coseismic ground displacement with GSD of 2.4 m, and uncertainties at the 90% confidence level on the NS, EW and vertical displacement measurements of 0.6 m, 0.7 m, and 0.6 m respectively.", "Keywords": "Sub-pixel image correlation ; Rigorous sensor model ; 2D/3D surface displacement ; ICA", "DOI": "10.1016/j.rse.2022.113038", "PubYear": 2022, "Volume": "277", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "California Institute of Technology, CA, USA;Univ. <PERSON> - IGN/ENSG, LaSTIG Lab., France;Corresponding author at: California Institute of Technology, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "California Institute of Technology, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "California Institute of Technology, CA, USA"}], "References": []}, {"ArticleId": 94156649, "Title": "Tissue P Systems with Vesicles of Multisets", "Abstract": "We consider tissue P systems working on vesicles of multisets with the very simple operations of insertion, deletion, and substitution of single objects. With the whole multiset being enclosed in a vesicle, sending it to a target cell can be indicated in those simple rules working on the multiset. As derivation modes we consider the sequential derivation mode, where, if possible, one rule is applied in a derivation step, and the set maximally parallel derivation mode, where in each derivation step a non-extendable set of rules indicating the same target cell is applied. With the set maximally parallel derivation mode, computational completeness can already be obtained with tissue P systems having a tree structure, whereas tissue P systems even with an arbitrary communication structure are not computationally complete when working in the sequential mode. Adding polarizations — only the three polarizations [Formula: see text], [Formula: see text], [Formula: see text] are sufficient — allows for obtaining computational completeness even for tissue P systems working in the sequential mode.", "Keywords": "Computational completeness; derivation modes; multisets; P systems; polarizations; vesicles", "DOI": "10.1142/S0129054122410015", "PubYear": 2022, "Volume": "33", "Issue": "3n04", "JournalId": 9642, "JournalTitle": "International Journal of Foundations of Computer Science", "ISSN": "0129-0541", "EISSN": "1793-6373", "Authors": [{"AuthorId": 1, "Name": "Artiom Alhazov", "Affiliation": "<PERSON> Institute of Mathematics and Computer Science, Academiei 5 Chişinău, MD-2028, Moldova"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Informatics, TU Wien, Favoritenstraße 9–11, 1040 Vienna, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, Université Évry, IBISC, 91020, Évry-Courcouronnes, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Université Paris Est Creteil, LACL, 94010, Creteil, France"}], "References": []}, {"ArticleId": 94156734, "Title": "Synchronization and pinning control of stochastic coevolving networks", "Abstract": "Network dynamical systems are often characterized by the interlaced evolution of the node and edge dynamics, which are driven by both deterministic and stochastic factors. This manuscript offers a general mathematical model of coevolving network, which associates a state variable to each node and edge in the network, and describes their evolution through coupled stochastic differential equations. We study the emergence of synchronization, be it spontaneous or induced by a pinning control action, and provide sufficient conditions for local and global convergence. We enable the use of the Master Stability Function approach for studying coevolving networks, thereby obtaining conditions for almost sure local exponential convergence, whereas global conditions are derived using a Lyapunov-based approach. The theoretical results are then leveraged to design synchronization and pinning control protocols in two select applications. In the first one, the edge dynamics are tailored to induce spontaneous synchronization, whereas in the second the pinning edges are activated/deactivated and their weights modulated to drive the network towards the pinner’s trajectory in a distributed fashion.", "Keywords": "Network dynamical systems ; Synchronization ; Pinning control ; Coevolving networks", "DOI": "10.1016/j.arcontrol.2022.04.005", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3642, "JournalTitle": "Annual Reviews in Control", "ISSN": "1367-5788", "EISSN": "1872-9088", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, Information, and Bioengineering, Politecnico di Milano, Piazza <PERSON>, 32, 20133 Milan, Italy;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology, University of Naples Federico II, Via Claudio 21, 80125 Naples, Italy;Corresponding authors"}], "References": []}, {"ArticleId": 94156776, "Title": "Cross-domain unsupervised pedestrian re-identification based on multi-view decomposition", "Abstract": "<p>Great improvement has been made in pedestrian re-identification, but the test results in unknown domain are not satisfactory. This is because the pedestrian Re-ID model does not learn good common features between different domains. We found that view-angle related features are invariant in different domains. Based on this, we develop a cross-domain Re-ID method. Specifically, any pedestrian image is decomposed under the constraints of multiple view angles and the pedestrian multi-view features are generated. We propose an improved lightweight capsule network as the multi-view decomposition. The experimental results showed that our method can effectively improve the cross-domain performance of Re-ID.</p>", "Keywords": "Pedestrian re-identification; Lightweight capsule network; Multi-view; Feature decomposition; Domain-invariant", "DOI": "10.1007/s11042-021-11797-w", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Computer, Taiyuan University of Technology, Yuci, China; College of Computer Engineering, Shanxi Vocational University of Engineering Science and Technology, Yuci, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics, Taiyuan University of Technology, Yuci, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information and Computer, Taiyuan University of Technology, Yuci, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanxi Climatic Center, Taiyuan, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University, Hangzhou, China"}, {"AuthorId": 6, "Name": "Haifang Li", "Affiliation": "College of Information and Computer, Taiyuan University of Technology, Yuci, China"}], "References": []}, {"ArticleId": 94156794, "Title": "Hybrid high-order steepness-adjustable harmonic scheme based on combined discontinuity sensors", "Abstract": "Extending discontinuity-capturing schemes to high resolution using a hybrid approach has become popular in recent times. The hybrid schemes reported in literature usually combine high-order compact/explicit-schemes in smooth regions and the weighted essentially non-oscillatory (WENO) scheme in discontinuities by a discontinuity sensor. However, there are two major concerns: (1) the significant dissipation of the WENO scheme will smear the contact discontinuities, and (2) the accuracy of the discontinuity sensor will significantly influence the performance of the hybrid scheme. However, these two aspects are typically coupled. Therefore, in this study, we attempt to replace the discontinuity-capturing schemes (WENO) with discontinuity-preserving schemes (steepness-adjustable harmonic (SAH) scheme). Then, we extend the SAH scheme to high-order accuracy by hybridizing it with a linear fourth-degree polynomial (P4) scheme. The new P4-SAH scheme was employed to evaluate the nine different discontinuity sensors. The numerical results revealed that a single discontinuity sensor can hardly adapt to different numerical cases. Therefore, we propose a new class of discontinuity sensors that combines MP-WGVC discontinuity sensors. Several classical one- and two-dimensional Euler equation benchmark tests were simulated. The numerical results demonstrate that the new hybrid P4-SAH scheme with a combined MP-WGVC sensor not only sharply captures shock waves and contact discontinuities, but also capture small vortex structures.", "Keywords": "Combined discontinuity sensors ; Steepness-adjustable harmonic scheme ; High resolution ; Hybrid scheme", "DOI": "10.1016/j.compfluid.2022.105482", "PubYear": 2022, "Volume": "241", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "Yucang Ruan", "Affiliation": "State Key Laboratory for Turbulence and Complex Systems, College of Engineering, Peking University, Beijing 100871, China;Sino-French Engineer School, Beihang University, Beijing 100191, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Applied Physics and Computational Mathematics, Beijing 100094, China;HEDPS and Center for Applied Physics and Technology, College of Engineering, Peking University, Beijing 100871, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>g <PERSON>", "Affiliation": "Sino-French Engineer School, Beihang University, Beijing 100191, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Applied Physics and Computational Mathematics, Beijing 100094, China;Corresponding authors"}], "References": [{"Title": "On shock sensors for hybrid compact/WENO schemes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "199", "Issue": "", "Page": "104439", "JournalTitle": "Computers & Fluids"}, {"Title": "A flux split based finite-difference two-stage boundary variation diminishing scheme with application to the Euler equations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "213", "Issue": "", "Page": "104725", "JournalTitle": "Computers & Fluids"}, {"Title": "Symmetry-preserving enforcement of low-dissipation method based on boundary variation diminishing principle", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "233", "Issue": "", "Page": "105227", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 94156798, "Title": "Low carbon strategy analysis with two competing supply chain considering carbon taxation", "Abstract": "We investigate the carbon emission reduction efforts and pricing decisions for a two chains system under carbon taxation. In this system, each chain consists of a retailer, who acts as a follower and a manufacturer who acts as a leader. Moreover, we consider two situations: in the vertical orientation, two manufacturers individually make carbon reduction decisions; in the horizontal orientation, two manufacturers make carbon emission abatement decisions simultaneously. Besides, comparison of the optimal solutions are discussed under two situations, the results indicate that: (1) the government can encourage manufacturers to optimize the abatement rate of their products by imposing appropriate carbon taxes under two situations. (2) As long as carbon taxation occurs, each supply chain members’ vertical collaboration will drop carbon   emission rate and product price. (3) When each chain is a decentralized system, although the horizontal collaboration between manufacturers improves the abatement rate of products, it reduces both retailers’ profits and customers’ interests. (4) The two-part tariff contract proposed by the downstream retailers can induce the two manufacturers to abandon their collaboration. This contract can not only realize Pareto improvement for supply chain members, but also protect the environment because of the larger abatement rate. (5) When the fixed fee falls into a certain range, the two-part tariff contract can yield a larger system benefit.", "Keywords": "Carbon tax ; Low carbon supply chain ; Two-part tariff contract ; Supply chain coordination ; Collaboration", "DOI": "10.1016/j.cie.2022.108203", "PubYear": 2022, "Volume": "169", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Tourism and Urban Management, Jiangxi University of Finance and Economics, Nanchang Postcode: 330032, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Tourism and Urban Management, Jiangxi University of Finance and Economics, Nanchang Postcode: 330032, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Tourism and Urban Management, Jiangxi University of Finance and Economics, Nanchang Postcode: 330032, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Huaiyin Normal University, Huaian Postcode: 223300, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Southeast University, Nanjing Postcode: 211189, China;Corresponding author"}], "References": [{"Title": "The impact of carbon emissions tax on vertical centralized supply chain channel structure", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "106303", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Effects of sustainability investment and risk aversion on a two-stage supply chain coordination under a carbon tax policy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Xu; Satyave<PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106324", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Production and carbon emission reduction decisions for remanufacturing firms under carbon tax and take-back legislation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106419", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Production and joint emission reduction decisions based on two-way cost-sharing contract under cap-and-trade regulation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "106549", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Optimal decisions for competitive manufacturers under carbon tax and cap-and-trade policies", "Authors": "<PERSON><PERSON> Sun; <PERSON><PERSON>", "PubYear": 2021, "Volume": "156", "Issue": "", "Page": "107244", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Supply chain coordination by contracts considering dynamic reference quality effect under the O2O environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107802", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": ********, "Title": "The Mediating Role of the IS/IT Auditor for Quality Assurance in the Selection of Accounting Software Packages for SMEs in Developing Economies", "Abstract": "<p>This research designed and tested a model for the selection adoption, customization, and implementation of the best accounting software among the lot that has flooded the software market recently in developing countries. The model assessed the mediating role of the Information Systems (IS) Auditor to mitigate the risks of selecting the wrong packaged software to the barest minimum in developing countries. The researchers analyzed the association that exists between eight variables — Basic Functionality (BF), General Determinants (GD), Package Features (PF), Customization Capability (CC), Financial Reporting Capabilities (FRC) and the IS Auditor (IS/IT Auditor. The study was cross-sectional which purposively sampled professional experts from selected SMEs across Ghana to evaluate the eight variables in the model. 260 experts participated in the assessment. The Delphi Technique was used to reach a consensus on the applicability of the variables in the model. The variable measurement items in the model were subjected to three rounds of scrutiny as required by the Delphi Technique. Multiple regression analysis was also computed in Smart PLS 3 to determine the correlation among the variables in making an appropriate CASP selection decision. The internal consistency and reliability of the dataset were estimated using Statistical Package for Social Sciences.</p><p>The results point to a positive association between all the variables assessed. The values for BF, GM, PF, CC, FRC and IS/IT Auditor, were 0.509, 0.621, 0.511, 0.632, 0.507 0.454. 0.596 and 0.590, respectively. This is substantiated by the average Kendall’s [Formula: see text] of 0.17. Chi-square ([Formula: see text] values ranging between 134.55 and 497.28 for all the three rounds of evaluation and intra-class correlation values ranging from 0.56 to 0.95 affirmed that consensus was achieved. It means that none of these variables could be included in the CASPs decision without the IS/IT Auditor mediating to check its strength in appropriate CASPs selection. Cronbach’s [Formula: see text] values ranging between 0.719 and 0.924 were computed to determine the internal consistency and reliability of the datasets. The average variance extracted (AVE) for each variable was higher than 0.5 which implies that the convergent validity of the construct is still adequate. The triangulations of these variables using different statistical values have proven the robustness and resilience with which IS/IT Auditor could assist in selecting the best CASPs package with lesser risks of implementation failures in developing economies. The model is expected to help SMEs in developing countries select the best CASPs packages among the lots for successful implementation. It is expected that it will serve as a wake-up call for policymakers to adopt a roadmap to enable SMEs to make the best CASPs buying decision. It is also envisaged that academic institutions will also fill the software adoption illiteracy gap by training more graduates in hands-on computerized accounting to increase selection and implementation awareness.</p>", "Keywords": "Basic Functionality; General Determinants; features of package; Customization Capabilities; Financial Reporting Capabilities; IS/IT Auditor", "DOI": "10.1142/S0219877022400053", "PubYear": 2022, "Volume": "19", "Issue": "3", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Accountancy and Accounting Information Systems, Kumasi Technical University, Kumasi, Ghana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Accountancy and Accounting Information Systems, Kumasi Technical University, Kumasi, Ghana"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Accountancy and Accounting Information Systems, Kumasi Technical University, Kumasi, Ghana"}], "References": [{"Title": "SOFTWARE ASSURANCE: THE THINGS A MANAGER NEEDS TO KNOW", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "4", "Page": "1", "JournalTitle": "EDPACS"}]}, {"ArticleId": ********, "Title": "Hardware Trojan Detection Using Unsupervised Deep Learning on Quantum Diamond Microscope Magnetic Field Images", "Abstract": "<p> This article presents a method for hardware trojan detection in integrated circuits. Unsupervised deep learning is used to classify wide field-of-view (4 × 4 mm <sup>2</sup> ), high spatial resolution magnetic field images taken using a Quantum Diamond Microscope (QDM). QDM magnetic imaging is enhanced using quantum control techniques and improved diamond material to increase magnetic field sensitivity by a factor of  4 and measurement speed by a factor of  16 over previous demonstrations. These upgrades facilitate the first demonstration of QDM magnetic field measurement for hardware trojan detection. Unsupervised convolutional neural networks and clustering are used to infer trojan presence from unlabeled data sets of 600 × 600 pixel magnetic field images without human bias. This analysis is shown to be more accurate than principal component analysis for distinguishing between field programmable gate arrays configured with trojan-free and trojan-inserted logic. This framework is tested on a set of scalable trojans that we developed and measured with the QDM. Scalable and TrustHub trojans are detectable down to a minimum trojan trigger size of 0.5% of the total logic. The trojan detection framework can be used for golden-chip-free detection, since knowledge of the chips’ identities is only used to evaluate detection accuracy. </p>", "Keywords": "", "DOI": "10.1145/3531010", "PubYear": 2022, "Volume": "18", "Issue": "4", "JournalId": 12294, "JournalTitle": "ACM Journal on Emerging Technologies in Computing Systems", "ISSN": "1550-4832", "EISSN": "1550-4840", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Maryland, College Park, MD, USA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Maryland, College Park, MD, USA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Harvard University, Cambridge, MA, USA and The MITRE Corporation, Bedford, MA, USA and University of Maryland, College Park, MD, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}], "References": []}, {"ArticleId": 94156933, "Title": "The ASEME methodology", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAOSE.2022.10046964", "PubYear": 2022, "Volume": "7", "Issue": "2", "JournalId": 22920, "JournalTitle": "International Journal of Agent-Oriented Software Engineering", "ISSN": "1746-1375", "EISSN": "1746-1383", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94157034, "Title": "Application of Computer Vision in T-Shirt Dimensions Measurement", "Abstract": "<p>This paper presents a solution to automatically measure the T-shirt dimensions in the garment industry. To address this goal, the paper focuses on utilizing image processing to determine the T-shirt's dimensions. The processing algorithm was provided along with the proposed recognition regions novel approach that was expected to deliver faster processing speed and enhance accuracy. The feasibility was demonstrated by characterizing the accuracy and processing speed. Specifically, five distinctive dimensions were successfully identified and measured; with the replication of 30, the discrepancy varies from 0.095% (for chest) to 2.088% (for collar). The divergence is insignificant compared with the granted tolerances. Finally, the processing time and the mechanical structure of the system deliver productivity of 22 products/minute which is approximately 10 times more rapidly than manual measurement (25 seconds).</p>", "Keywords": "T-Shirt;Auto-dimensioning;computer vision;metrology automation;intelligent system", "DOI": "10.4108/eetinis.v9i31.707", "PubYear": 2022, "Volume": "9", "Issue": "31", "JournalId": 42306, "JournalTitle": "EAI Endorsed Transactions on Industrial Networks and Intelligent Systems", "ISSN": "", "EISSN": "2410-0218", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ho Chi Minh City International University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "Affiliation": "Ho Chi Minh City International University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National United University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Eastern International University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Eastern International University"}], "References": [{"Title": "A vision guided robotic system for flexible gluing process in the footwear industry", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101965", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 94157047, "Title": "Rancang <PERSON>un Sistem Monitoring Penggunaan Air PDAM Berbasis IoT", "Abstract": "<p>Air merupakan salah satu sumber kehidupan yang sangat penting. Salah satu cara untuk melakukan penghematan air yaitu dengan memonitoring debit air yang dikonsumsi perbulannya. Pengukuran debit aliran diterapkan pada setiap rumah tangga yang menggunakan PDAM, sehingga setiap rumah dipasang meteran air, yang mana alat tersebut digunakan untuk mengukur atau mencatat seberapa besar volume air yang telah digunakan untuk keperluan setiap rumah tangga. Pengukuran besarnya volume air yang terdapat pada meteran air digunakan untuk penentuan jumlah tarif yang harus dibayar setiap rumah tangga kepada pihak PDAM setiap bulan pemakaian. Pada penelitian ini dibuat sistem yang dapat memonitoring kualitas dan penggunaan air PDAM. Pada sistem ini digunakan sensor turbidity yang akan mengukur tingkat kekeruhan air dalam satuan NTU. Nilai NTU ini akan menunjukkan kualitas air PDAM apakah layak untuk digunakan dalam kebutuhan sehari-hari atau tidak. Sensor water flow akan dipasang ditengah-tengah pipa PDAM, data debit aliran air yang terukur pada sensor akan diproses pada modul Arduino Uno untuk dikonversi menjadi data perkiraan biaya pengunaan air PDAM. Selanjutnya semua data akan ditampilkan pada LCD, yaitu data tingkat kekeruhan air, data besarnya debit air dan perkiraan biaya yang harus dibayar oleh pelanggan. Semua data akan dikirimkan ke server blynk melalui Camera ESP32. Data yang tersimpan pada server dapat diakses menggunakan aplikasi blynk pada smartphone. Selain itu data juga akan tersimpan pada SD Card sebagai back up data. Hasil pembacaan sensor turbidity menunjukkan nilai kekeruhan air sebesar 5 NTU. Nilai eror dari hasil pembacaan sensor water flow sebesar 1,6% yang artinya tingkat akurasinya sebesar 98,4%. Sedangkan data hasil pembacaan sistem penghitung biaya penggunaan air PDAM tidak terdapat eror sehingga akurasi datanya mencapai 100%.</p>", "Keywords": "PDAM;Monitoring;Sensor;ESP32 Camera", "DOI": "10.35143/jkt.v7i2.5152", "PubYear": 2021, "Volume": "7", "Issue": "2", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}, {"ArticleId": 94157258, "Title": "A 0.6-V 11-uW PVT tolerant DTMOS inverter based OTA", "Abstract": "This paper presents the design of a process-voltage-temperature (PVT) variation tolerant inverter-based operational transconductance amplifier (OTA) employing both a dynamic threshold MOS (DTMOS) technique and a constant voltage biasing (CVB) scheme. The proposed inverter-based OTA offers a higher bandwidth due to implemented DTMOS technique, which realizes higher input transconductance value than a conventional inverter-based OTA design. Simulation results show that the proposed OTA achieves superior slow-slow (SS) corner performance under PVT variations than the conventional inverter-based OTA while consuming only 11-uW and providing a figure of merit (FoM) of 7.0-MHz.pF/uA. As a result, DC gain and unity-gain bandwidth (UGBW) of the proposed OTA improve by 27% and 32% at SS corner under the PVT variations, respectively.", "Keywords": "DTMOS;Inverter based OTA;PVT tolerant", "DOI": "10.17694/bajece.1005797", "PubYear": 2022, "Volume": "10", "Issue": "2", "JournalId": 27857, "JournalTitle": "Balkan Journal of Electrical and Computer Engineering", "ISSN": "2147-284X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Mesut ATASOYU", "Affiliation": "ARTVIN CORUH UNIVERSITY"}], "References": []}, {"ArticleId": 94157264, "Title": "A novel diabetic retinopathy grading using modified deep neural network with segmentation of blood vessels and retinal abnormalities", "Abstract": "<p>This paper proposes a new DR grading for solving the aforementioned problems. The initial process of the proposed model is the pre-processing, which is performed by median filtering. The segmentation of blood vessels and retinal abnormalities like exudates, microaneurysm, and hemorrhages is also done for grading the DR. Before initiating the segmentation of those components, optic disc removal is opted using the open-close watershed transform. Once the optic disc is removed, adaptive active contour methodology is used for performing the blood vessel segmentation. As a major contribution, the threshold value of the active contour method is optimized using proposed FNU-GOA with the aim of maximizing the accuracy of the blood vessel segmented images. Further, the retinal abnormalities like exudates, microaneurysm, and hemorrhages are performed by Otsu thresholding with morphological operation. From both segmented blood vessels and retinal abnormalities, the features like “Gray-Level Co-occurrence Matrix (GLCM)”, “area of Region of Interest (RoI)” and “Local Ternary Pattern (LTP)” are extracted. These features are subjected to the Modified Deep Neural Network (MDNN) for grading the DR. This MDNN focuses on solving the over fitting problems of DNN with the aim of maximizing the accuracy in terms of grading. The improvement of adaptive active contour-based blood vessel segmentation and MDNN-based grading is progressively dependent on the proposed Fitness-based Newly Updated Grasshopper Optimization Algorithm (FNU-GOA). This new algorithm improves the efficiency of grading with better convergence results. The experimental results show promising results as compared with other systems when analyzing various performance measures.</p>", "Keywords": "Diabetic retinopathy grading; Deep neural network; Optic disc removal; Blood vessel segmentation; Abnormality segmentation; Fitness-based newly updated grasshopper optimization algorithm", "DOI": "10.1007/s11042-022-13056-y", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ECE Department, GLA University, Mathura, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ECE Department, GLA University, Mathura, India"}], "References": [{"Title": "Deep neural networks to predict diabetic retinopathy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "5407", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Deep residual transfer learning for automatic diagnosis and grading of diabetic retinopathy", "Authors": "<PERSON>-<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "424", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 94157357, "Title": "Efficient text document clustering approach using multi-search Arithmetic Optimization Algorithm", "Abstract": "Text document clustering is to divide textual contents into clusters or groups. It received wide attention due to the vast amount of daily data from the Web. In the last decade, Meta-Heuristic (MH) techniques have been adopted to solve clustering problems. Motivated by that, the authors introduce a reliable version of the newly developed MH algorithm called Arithmetic Optimization Algorithm (AOA). Math arithmetic operators inspire the AOA: multiplication, subtraction adding, and division. The AOA showed good performance in several global problems; nonetheless, it suffers from entrapment in local optima in complicated and high dimensional problems. Therefore, this paper proposes an improved version of AOA for the text document clustering problem. The Improved AOA (IAOA) introduces an integration between Opposition-based learning (OBL) and Levy flight distribution (LFD) with AOA to tackle the limitations of the traditional AOA. The IAOA is examined with different UCI datasets for the text clustering problems and assessed with a set of CEC2019 benchmark functions as a global optimization algorithm with extensive comparison to existing optimization algorithms. Overall, experimental results show the superiority of the proposed IAOA compared to several optimization algorithms. Moreover, the proposed IAOA is compared with twenty-one state-of-the-art methods using thirty-one benchmark text datasets, and the results proved the superiority of the proposed IAOA.", "Keywords": "Arithmetic Optimization Algorithm (AOA) ; Opposition-based learning ; Levy flight ; Text clustering ; CEC2019 problems", "DOI": "10.1016/j.knosys.2022.108833", "PubYear": 2022, "Volume": "248", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Sciences and Informatics, Amman Arab University, 11953 Amman, Jordan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Umm Al-Qura University, Makkah, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Information Engineering in Surveying, Mapping and Remote Sensing, Wuhan University, Wuhan 430079, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer, Damietta University, Damietta 34517, Egypt"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Faculty of Engineering, Fayoum University, Fayoum, Egypt"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Galala University, Suze 435611, Egypt;Artificial Intelligence Research Center (AIRC), Ajman University, Ajman 346, United Arab Emirates;Department of Mathematics, Faculty of Science, Zagazig University, Zagazig 44519, Egypt"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Engineering, Najafabad Branch, Islamic Azad University, Najafabad, Iran;Big Data Research Center, Najafabad Branch, Islamic Azad University, Najafabad, Iran"}], "References": [{"Title": "An efficient three-way clustering algorithm based on gravitational search", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1003", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Equilibrium optimizer: A novel optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105190", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Boosting salp swarm algorithm by sine cosine algorithm and disrupt operator for feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113103", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Performance analysis of Chaotic Multi-Verse Harris Hawks Optimization: A case study on solving engineering problems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "103370", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Link-based multi-verse optimizer for text documents clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106002", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel hybrid particle swarm optimization and gravitational search algorithm for multi-objective optimization of text mining", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106189", "JournalTitle": "Applied Soft Computing"}, {"Title": "A decomposition-based multi-objective optimization approach for extractive multi-document text summarization", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106231", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hybridizing Gray Wolf Optimization (GWO) with Grasshopper Optimization Algorithm (GOA) for text feature selection and clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106651", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improved artificial bee colony algorithm based on whale optimization algorithm for data clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "43-44", "Page": "32169", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient binary chaotic symbiotic organisms search algorithm approaches for feature selection problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>harehchopogh", "PubYear": 2021, "Volume": "77", "Issue": "8", "Page": "9102", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "African vultures optimization algorithm: A new nature-inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107408", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A hybrid OBL-based firefly algorithm with symbiotic organisms search algorithm for solving continuous optimization problems", "Authors": "<PERSON>; <PERSON><PERSON> Ghareh<PERSON>gh", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "3998", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A comprehensive survey of clustering algorithms: State-of-the-art machine learning applications, taxonomy, challenges, and future research prospects", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "104743", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 94157469, "Title": "Sim 2 PIM: A complete simulation framework for Processing-in-Memory", "Abstract": "With the help of modern memory integration technologies, Processing-in-Memory (PIM) has emerged as a practical approach to mitigate the memory wall while improving performance and energy efficiency in contemporary applications. Since these designs encompass accelerating and increasing the efficiency of critical specific and general-purposed applications, it is expected that these accelerators will be coupled to existing systems and consequently with systems capable of multi-thread computing. However, there is a lack of tools capable of quickly simulating different PIMs designs and their suitable integration with other hosts. This gap is even worse when considering simulations of multi-core systems. This work presents Sim 2 PIM , a Simple Simulator for PIM devices that seamlessly integrates any PIM architecture with the host processor and memory hierarchy. The framework simulation achieves execution speeds and accuracy on par with the perf tool on host code, less than 10% run-time overhead, and around 2% difference in metrics. Additionally, by exploring the thread parallelism in the application and utilizing the host hardware, Sim 2 PIM can achieve more than 8 × simulation speedup compared to a sequential simulation and orders of magnitude compared to other simulators. Sim 2 PIM is available to download at https://pim.computer/ .", "Keywords": "Processing-in-Memory ; Simulator ; Performance ; Cycle accurate ; Multi-threaded", "DOI": "10.1016/j.sysarc.2022.102528", "PubYear": 2022, "Volume": "128", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal University of Rio Grande do Sul, Porto Alegre, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "Paulo C. Santos", "Affiliation": "Federal University of Rio Grande do Sul, Porto Alegre, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Rio Grande do Sul, Porto Alegre, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Federal University of Paraná, Curitiba, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Federal University of Rio Grande do Sul, Porto Alegre, Brazil"}], "References": [{"Title": "A Classification of Memory-Centric Computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "2", "Page": "1", "JournalTitle": "ACM Journal on Emerging Technologies in Computing Systems"}]}, {"ArticleId": 94157494, "Title": "Modeling the Susceptibility of Forest Fires Using a Genetic Algorithm: A Case Study in Mountain Areas of Southwestern China", "Abstract": "<p> Modeling fire susceptibility in fire-prone areas of forest ecosystems was essential for providing guidance to implement prevention and control measures of forest fires. Traditional models were developed on the basis of random selection of absence data (i.e., nonfire data from unburned areas), which could bring uncertainties to modeling results. Here, a new model with the genetic algorithm for Rule-set Production (GARP) algorithm and 10 environmental layers was proposed to process presence-only data in the susceptibility modeling of forest fires in Chongqing city. To do this, 70% of 684 fire occurrence data (479) during the period of 2000–2018 were applied to train the proposed model. And, 30% of these fire occurrence data (205) and the same amount of no-fire data (205) were emerged as validation dataset. The results showed that, for some environmental layers (i.e., distance to the nearest road, land cover, precipitation, distance to the nearest settlement, aspect, relative humidity, elevation, wind speed, and temperature), their P values were less than 0.05, indicating that these 9 environmental layers have significant influence on the spatial distribution of fire susceptibility in Chongqing city. On the contrary, with a higher P value (i.e., 0.126), the slope layer has an insignificant effect on fire susceptibility in the study area. Furthermore, the results of receiver operating characteristic analysis (ROC) showed that the proposed model has a good performance with an AUC value of 0.869, an accuracy value of 0.732, a sensitivity value of 0.59, a specificity value of 0.873, a positive predictive value of 0.823, and a negative predictive value of 0.681. This study revealed the validity of the proposed model in modeling the susceptibility of forest fires. </p>", "Keywords": "", "DOI": "10.1155/2022/5502209", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Institute of Meteorological Sciences, Chongqing 401147, China;College of Resources and Environment, Chengdu University of Information Technology, Chengdu 610225, Sichuan, China"}, {"AuthorId": 2, "Name": "Yanghua Gao", "Affiliation": "Chongqing Institute of Meteorological Sciences, Chongqing 401147, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Resources and Environment, Chengdu University of Information Technology, Chengdu 610225, Sichuan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Institute of Meteorological Sciences, Chongqing 401147, China"}], "References": []}, {"ArticleId": 94157502, "Title": "Research on GPS User Trajectory Analysis and Behavior Prediction Based on Swarm Intelligence Algorithm", "Abstract": "<p>Under the social background of the rapid development of big data, human beings have gradually moved towards a comprehensive digital era, and people have begun to apply digital technology in various fields. Therefore, it is necessary to study methods that can efficiently process big data and solve problems such as data intelligence. Intelligent products and applications that we use everywhere in our lives are changing people’s lives. Among them, location service is one of the important technical supports. No matter which software is opened on the mobile phone, it will prompt us to open location service. Location prediction is an important part of location-based services and plays an indispensable role in the recommendation system and urban resource planning. At present, GPS-based trajectory data is widely concerned in position prediction task. GPS trajectory data belongs to spatiotemporal series data, which not only contains time and position information but also contains abundant context information in trajectory sequence. Most of the traditional position prediction methods focus on the position sequence in GPS trajectory but do not fully mine the context information in the trajectory, which leads to poor prediction results. The intelligent optimization algorithm greatly enriches the optimization technology and provides a feasible solution for those combinatorial optimization problems which are difficult to deal with by traditional optimization technology. GPS trajectory data has the advantages of wide coverage, quick update, easy collection, and low cost, and it also implies abundant road network information. As a result, GPS trajectory data of users has gradually become a new data source for automatic construction of urban road network and has also become a research hotspot of many scholars. In this paper, the idea of swarm intelligence algorithm and swarm intelligence algorithm are used to design and implement a data analysis and behavior prediction system for calculating GPS user trajectory, aiming at solving the shortcomings of existing GPS tracking and positioning.</p>", "Keywords": "", "DOI": "10.1155/2022/7554560", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Guangdong Construction Polytechnic, Guangzhou 510440, China"}], "References": []}, {"ArticleId": 94157670, "Title": "Real-time identification and avoidance of simultaneous static and dynamic obstacles on point cloud for UAVs navigation", "Abstract": "Avoiding hybrid obstacles in unknown scenarios with an efficient flight strategy is a key challenge for unmanned aerial vehicle applications. In this paper, we introduce a more robust technique to distinguish and track dynamic obstacles from static ones with only point cloud input. Then, to achieve dynamic avoidance, we propose the forbidden pyramids method to solve the desired vehicle velocity with an efficient sampling-based method in iteration. The motion primitives are generated by solving a nonlinear optimization problem with the constraint of desired velocity and the waypoint. Furthermore, we present several techniques to deal with the position estimation error for close objects, the error for deformable objects, and the time gap between different submodules. The proposed approach is implemented to run onboard in real-time and validated extensively in simulation and real hardware tests, demonstrating our superiority in tracking robustness, energy cost, and calculating time.", "Keywords": "Motion planning ; UAVs ; Point cloud ; Dynamic environment", "DOI": "10.1016/j.robot.2022.104124", "PubYear": 2022, "Volume": "154", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Aeronautical and Aviation Engineering, Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, The University of Hong Kong, Hong Kong, China;Corresponding author"}], "References": [{"Title": "Dynamic obstacle avoidance for quadrotors with event cameras", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "40", "Page": "eaaz9712", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 94157689, "Title": "Emerging Optical In‐Memory Computing Sensor Synapses Based on Low‐Dimensional Nanomaterials for Neuromorphic Networks", "Abstract": "<p>Emerging optical synapses with in-memory computing sensor (IMCS) performance are considered to be one of the most effective candidates to circumvent the bottleneck of the current Von Neumann structure while developing neuromorphic systems with higher effectiveness and lower energy consumption. Biomimetic properties of optical IMCS synapses in function and form indicate the higher requirements for utilized functional materials, such as stronger optical sensitivity and lower energy dissipation. Because of properties with high optical-sensitivity efficiency and excellent electrical conductivity, low-dimensional nanomaterials have received tremendous interest in modulating optical-induced synaptic plasticity and emulating optical-triggered neuromorphic activity of optical IMCS synapses. Herein, a comprehensive summary of optical IMCS synapses based on low-dimensional nanomaterials is introduced systematically for the first time, including 0D, 1D, and 2D materials. In addition, the content of biomimetic synaptic characteristics, materials classification, operation mechanism, and neuromorphic applications of optical IMCS synapses based on low-dimensional nanomaterials are also summarized in this work. At last, the challenges and outlook related to artificial optical IMCS synapses with low-dimensional nanomaterials are provided.</p>", "Keywords": "artificial intelligence;in-memory computing sensors;low-dimensional nanomaterials;neuromorphic networks;optical synapses", "DOI": "10.1002/aisy.202100236", "PubYear": 2022, "Volume": "4", "Issue": "9", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Advanced Technology School of Science Xi'an Jiaotong-Liverpool University  Suzhou 215123 China;Division of Advanced Nano-Materials Nanofabrication Facility Suzhou Institute of Nano-Tech and Nano-Bionics Chinese Academy of Sciences  Suzhou 215123 China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Advanced Technology School of Science Xi'an Jiaotong-Liverpool University  Suzhou 215123 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Advanced Nano-Materials Nanofabrication Facility Suzhou Institute of Nano-Tech and Nano-Bionics Chinese Academy of Sciences  Suzhou 215123 China"}, {"AuthorId": 4, "Name": "Yi Sun", "Affiliation": "School of Advanced Technology School of Science Xi'an Jiaotong-Liverpool University  Suzhou 215123 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Advanced Technology School of Science Xi'an Jiaotong-Liverpool University  Suzhou 215123 China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Electronics University of Liverpool  Liverpool L69 3GJ UK"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Advanced Technology School of Science Xi'an Jiaotong-Liverpool University  Suzhou 215123 China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Advanced Technology School of Science Xi'an Jiaotong-Liverpool University  Suzhou 215123 China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Advanced Nano-Materials Nanofabrication Facility Suzhou Institute of Nano-Tech and Nano-Bionics Chinese Academy of Sciences  Suzhou 215123 China"}, {"AuthorId": 10, "Name": "Cezhou Zhao", "Affiliation": "School of Advanced Technology School of Science Xi'an Jiaotong-Liverpool University  Suzhou 215123 China"}], "References": [{"Title": "Improved salient object detection using hybrid Convolution Recurrent Neural Network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114064", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optoelectronic Synaptic Devices for Neuromorphic Computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "2000099", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Recent Progress of Optoelectronic and All‐Optical Neuromorphic Devices: A Comprehensive Review of Device Structures, Materials, and Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "4", "Page": "2000119", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 94157691, "Title": "Magnetically Actuated Reactive Oxygen Species Scavenging Nano‐Robots for Targeted Treatment", "Abstract": "<p>Magnetic micro/nanorobots (MagRobots) with unparalleled advantages, including remote mobility, high reconfigurability and programmability, lack of fuel requirement, and versatility, can be manipulated under a magnetic field, which has attracted considerable research attention in the biomedicine. Magnetic materials, as the key components of MagRobots, generate reactive oxygen species (ROS) in vivo to induce tissue/organ damage through Fenton/Fenton-like reactions, which may hinder the clinical application of MagRobots. Here, the biologically active Prussian blue is generated on the surfaces of MagRobots via an in situ reaction to obtain magnetically actuated ROS-scavenging nano-robots (ROSrobots). The generated Prussian blue blocks ROS production and endows the MagRobots with additional functionalities, markedly expanding their potential medical applications. Under the action of a magnetic field, the reconfigurable ROSrobots realize multimode transformation, locomotion, and manipulation in complex environments. Importantly, a simple control method is proposed to achieve movement in 3D geometries to allow the completion of tasks in a complex environment. Furthermore, the osteoarthritis (OA) rat model was employed for proof of concept. Notably, under the guidance of ultrasound imaging, ROSrobots can be accurately injected into the articular cavity to actively target the treatment of OA. This research may further promote the clinical application of MagRobots.</p>", "Keywords": "magnetic materials;magnetic micro/nano-robots;medical robot;Prussian blue;reactive oxygen species", "DOI": "10.1002/aisy.202200061", "PubYear": 2022, "Volume": "4", "Issue": "7", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ultrasound Medicine Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Engineering Research Center for Orthopedic Material Innovation and Tissue Regeneration Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Electrical Engineering Chinese Academy of Sciences  Beijing 100190 China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Ultrasound Medicine Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Ultrasound Medicine Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ultrasound Medicine Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Engineering Research Center for Orthopedic Material Innovation and Tissue Regeneration Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ultrasound Medicine Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ultrasound Medicine Shanghai Jiao Tong University Affiliated Sixth People's Hospital  Shanghai 200233 China"}], "References": [{"Title": "Multifunctional surface microrollers for targeted cargo delivery in physiological blood flow", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "42", "Page": "eaba5726", "JournalTitle": "Science Robotics"}, {"Title": "Endoscopy-assisted magnetic navigation of biohybrid soft microrobots with rapid endoluminal delivery and imaging", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "52", "Page": "eabd2813", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 94157699, "Title": "Point cloud denoising algorithm with geometric feature preserving", "Abstract": "<p>Point cloud data model can be obtained by 3D laser scanning technology, but due to the roughness, surface texture and measurement environment of the object, the original point cloud data model usually contains a large number of noise points. Aiming at the problem that the complex noises in point cloud data model are difficult to remove, a point cloud denoising algorithm with geometric feature preservation is proposed. Firstly, the tensor voting matrix of points and their neighboring points in point cloud was calculated, and the eigenvalues and eigenvectors of the matrix were solved; secondly, the diffusion tensor based on the eigenvalues and eigenvectors was constructed, and the anisotropic diffusion equation based on the diffusion tensor was adopted to filter the noises so as to realize the initial denoising of point cloud; thirdly, the curvature factor and the density of data points in the initial denoising point cloud were defined, and the objective function of fuzzy c-means (FCM) clustering method was constructed by weighting the degree factor. Finally, the feature weighted FCM algorithm was used to further delete the noises in the point cloud, and the final accurate denoising was achieved. The experimental results show that the geometric feature preserving denoising algorithm has good denoising effect on both public point cloud, cultural relic point cloud and outdoor point cloud, so the proposed denoising algorithm is an effective point cloud denoising algorithm.</p>", "Keywords": "Point cloud denoising; Tensor voting; Fuzzy c-means clustering; Curvature; Density", "DOI": "10.1007/s00530-022-00936-4", "PubYear": 2022, "Volume": "28", "Issue": "5", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Xi’an University of Finance and Economics, Xi’an, China"}], "References": [{"Title": "3D point cloud registration denoising method for human motion image using deep learning algorithm", "Authors": "Qidong Du", "PubYear": 2020, "Volume": "26", "Issue": "1", "Page": "75", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 94157701, "Title": "High-accuracy and energy-efficient wearable device for dairy cows’ localization and activity detection using low-cost IMU/RFID sensors", "Abstract": "<p>In precision livestock farming, localization and activity sensors are used to detect dairy cows’ diseases at an early stage and play a significant role in improving the dairy cows’ productivity and welfare. However, these sensors have limitations and must be constantly updated, renewed and improved. Indeed, the localization sensors detect the dairy cows’ position without specifying exactly what they are actually doing (overlap, grooming, etc.). In addition, the activity sensors are attached to the dairy cows’ legs or heads which limits monitoring several reproductive and morphological events such as muscular contraction, overlap acceptance, etc. To overcome these limits, we have designed a new non-invasive back-attached sensor based on a combination of Inertial Measurement Unit (IMU) and passive RFID network allowing dairy cows’ identification, localization and detection of a wide range of activities. The designed classification model is based on Gaussian Mixture Model (GMM), used to individually define dairy cow thresholds allowing discrimination of their activities. The classification model is integrated into a technique based on time-driven system for controlling calculation and transmission. Also, two optimization approaches based on data fusion and temporal sequential appearance of activities were explored. The sensor energy-efficient management was explored using a sleep/wake-up method and the RFID readers localize the dairy cows when they move between feeding, watering, milking and resting zones. The activity synthesis is transmitted with an event-driven approach. The obtained results show that our sensor achieves high overall accuracy rate (99%) with low power consumption (0.05 mA).</p>", "Keywords": "Inertial Measurement Unit; RFID; Finite Mixture Model; Energy-efficient; Precision Livestock Farming; Behavior classification", "DOI": "10.1007/s00542-022-05288-7", "PubYear": 2022, "Volume": "28", "Issue": "5", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LARI Laboratory, Mouloud Mammeri University of Tizi-Ouzou, Tizi-Ouzou, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "LARI Laboratory, Mouloud Mammeri University of Tizi-Ouzou, Tizi-Ouzou, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAMPA Laboratory, Mouloud Mammeri University of Tizi-Ouzou, Tizi-Ouzou, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "LARI Laboratory, Mouloud Mammeri University of Tizi-Ouzou, Tizi-Ouzou, Algeria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LARI Laboratory, Mouloud Mammeri University of Tizi-Ouzou, Tizi-Ouzou, Algeria"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAMPA Laboratory, Mouloud Mammeri University of Tizi-Ouzou, Tizi-Ouzou, Algeria"}], "References": [{"Title": "Smart remote-controller designed with combining speech-recognition microprocessor and wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "1145", "JournalTitle": "Microsystem Technologies"}, {"Title": "Bioelectricity: a new approach to provide the electrical power from vegetative and fruits at off-grid region", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "10", "Page": "3161", "JournalTitle": "Microsystem Technologies"}, {"Title": "Power quality improvement using microsystem technology for wind power plant", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "6", "Page": "1799", "JournalTitle": "Microsystem Technologies"}, {"Title": "Novel features for intensive human activity recognition based on wearable and smartphone sensors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "6", "Page": "1889", "JournalTitle": "Microsystem Technologies"}, {"Title": "A cost effective on-site fault diagnosis method for home appliance rotor failures", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "11", "Page": "3389", "JournalTitle": "Microsystem Technologies"}, {"Title": "An intention-based online bilateral training system for upper limb motor rehabilitation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "211", "JournalTitle": "Microsystem Technologies"}, {"Title": "A fast digital chip implementing a real-time noise-resistant algorithm for estimating blood pressure using a non-invasive, cuffless PPG sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "11", "Page": "3501", "JournalTitle": "Microsystem Technologies"}, {"Title": "A dynamic data driven indoor localisation framework based on ultra high frequency passive RFID system", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3", "Page": "172", "JournalTitle": "International Journal of Sensor Networks"}, {"Title": "Deep learning based real-time Industrial framework for rotten and fresh fruit detection using semantic segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "9", "Page": "3365", "JournalTitle": "Microsystem Technologies"}, {"Title": "Human activity monitoring based on indoor map positioning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "8", "Page": "2919", "JournalTitle": "Microsystem Technologies"}, {"Title": "Design and implementation of a photoplethysmography acquisition system with an optimized artificial neural network for accurate blood pressure measurement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "6", "Page": "2345", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 94157705, "Title": "Siamese convolutional neural network and fusion of the best overlapping blocks for kinship verification", "Abstract": "<p>Analysis of facial images decoding familial features has been attracting the attention of researchers to develop a computerized system interested in determining whether a pair of facial images have a biological kin relationship or not. Given that not all regions of an image are useful to determine the kin relation, thus it is possible to obtain irrelevant and inaccurate information of kinship clues, resulting in false matched kinship. Thus, combining all these regions together will likely produces redundant, irrelevant and deceptive information of kinship, along with higher dimensional space. Motivated by the fact that the facial resemblance among the members in a family can be presented separately in different regions of facial images, where each independent region renders different familial features, there is a high probability that selecting and fusing only the most informative local regions and removing the irrelevant can obtain complementary information for further enhanced accuracy. To this end, unlike other methods, the Fusion of the Best Overlapping Blocks with Siamese Convolutional Neural Network (SCNN-FBOB) is an enhanced method for kinship verification in this paper. This method aimed to simultaneously remove the weak local blocks of the image from a set of overlapping local blocks that achieved low accuracy and only retain the local blocks that achieved high accuracy. Extensive experiments conducted on the benchmark KinFaceW-I and KinFaceW-II databases show highly competitive results over many other state-of-the-art methods.</p>", "Keywords": "Kinship verification; Siamese convolutional neural network; Overlapping local block; Fusion", "DOI": "10.1007/s11042-022-12735-0", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing, Universiti Teknologi Malaysia, Skudai, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Teknologi Malaysia, Skudai, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute For Artificial Intelligence and Big Data Pengkalan Chepa, Universiti Malaysia Kelantan, Bharu, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute For Artificial Intelligence and Big Data Pengkalan Chepa, Universiti Malaysia Kelantan, Bharu, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Taibah University, Medina, Saudi Arabia"}], "References": [{"Title": "Transfer learning and feature fusion for kinship verification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "11", "Page": "7139", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A literature survey on kinship verification through facial images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "377", "Issue": "", "Page": "213", "JournalTitle": "Neurocomputing"}, {"Title": "Deep multi-person kinship matching and recognition for family photos", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107342", "JournalTitle": "Pattern Recognition"}, {"Title": "Discriminative sampling via deep reinforcement learning for kinship verification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "38", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 94157712, "Title": "A Novel Approach to Cross dataset studies in Facial Expression Recognition", "Abstract": "Recognizing facial expressions is a challenging task both for computers and humans. Although recent deep learning-based approaches are achieving high accuracy results in this task, research in this area is mainly focused on improving results using a single dataset for training and testing. This approach lacks generality when applied to new images or when using it in in-the-wild contexts due to diversity in humans (e.g., age, ethnicity) and differences in capture conditions (e.g., lighting or background). The cross-datasets approach can overcome these limitations. In this work we present a method to combine multiple datasets and we conduct an exhaustive evaluation of a proposed system based on a CNN analyzing and comparing performance using single and cross-dataset approaches with other architectures. Results using the proposed system ranged from 31.56% to 61.78% when used in a single-dataset approach with different well-known datasets and improved up to 73.05% when using a cross-dataset approach. Finally, to study the system and humans’ performance in facial expressions classification, we compare the results of 253 participants with the system. Results show an 83.53% accuracy for humans and a correlation exists between the results obtained by the participants and the CNN.", "Keywords": "Convolutional neural networks; Facial expression recognition; Cross-datasets; Facial expression labelling", "DOI": "10.1007/s11042-022-13117-2", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departament de Matemàtiques i Informàtica, Universitat de les Illes Balears, Palma de Mallorca, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departament de Matemàtiques i Informàtica, Universitat de les Illes Balears, Palma de Mallorca, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departament de Matemàtiques i Informàtica, Universitat de les Illes Balears, Palma de Mallorca, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Departament de Matemàtiques i Informàtica, Universitat de les Illes Balears, Palma de Mallorca, Spain"}], "References": [{"Title": "A data aggregation based approach to exploit dynamic spatio-temporal correlations for citywide crowd flows prediction in fog computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "20", "Page": "31401", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Application of variational mode decomposition and chaotic grey wolf optimizer with support vector regression for forecasting electric loads", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107297", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": ********, "Title": "Robust Asymptotic Stability and Projective Synchronization of Time-Varying Delayed Fractional Neural Networks Under Parametric Uncertainty", "Abstract": "<p>In this paper, the robust asymptotic stability and projective synchronization of fractional-order time-varying delayed neural networks with uncertain parameters are studied. On account of the homeomorphism mapping theorem, free-weighting method and generalized <PERSON><PERSON><PERSON> inequality, several sufficient conditions of existence, uniqueness and asymptotic stability of the equilibrium point of the addressed models in the form of LMIs are established. In addition, some criteria ensuring the robust asymptotic projective synchronization between the master system and the slave system are deduced based on a suitable controller. Finally, two numerical simulations are designed to illustrate the effectiveness and rationality of the theoretical results.</p>", "Keywords": "Fractional neural networks; Time-varying delay; Robust asymptotic stability; Parameter uncertainty; Homeomorphism mapping; Projective synchronization", "DOI": "10.1007/s11063-022-10825-6", "PubYear": 2022, "Volume": "54", "Issue": "6", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Chongqing Jiaotong University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Chongqing Jiaotong University, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Chongqing Jiaotong University, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Department of Mathematics, Chongqing Jiaotong University, Chongqing, China"}], "References": [{"Title": "Novel methods to finite-time Mittag-<PERSON><PERSON> synchronization problem of fractional-order quaternion-valued neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "526", "Issue": "", "Page": "221", "JournalTitle": "Information Sciences"}, {"Title": "Robust stability of fractional-order quaternion-valued neural networks with neutral delays and parameter uncertainties", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Yan<PERSON> Chen; Zhenjiang Zhao", "PubYear": 2021, "Volume": "420", "Issue": "", "Page": "70", "JournalTitle": "Neurocomputing"}, {"Title": "Robust stability for a class of fractional-order complex-valued projective neural networks with neutral-type delays and uncertain parameters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Zhenjiang Zhao", "PubYear": 2021, "Volume": "450", "Issue": "", "Page": "399", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 94157723, "Title": "A Finite Volume Method for the 3D Lagrangian Ideal Compressible Magnetohydrodynamics", "Abstract": "<p>We propose a cell-centered Lagrangian scheme for solving the three dimensional ideal magnetohydrodynamics (MHD) equations on unstructured meshes. The physical conservation laws are compatibly discretized on the unstructured meshes to satisfy the geometric conservation law (GCL). By introducing a generalized Lagrange multiplier, the magnetic divergence constraint is coupled with the conservation laws hence the magnetic divergence errors can dissipate and transport to the domain boundaries. Invoking the Galilean invariance, magnetic flux conservation and the thermodynamic consistency, the nodal approximate Riemann solver is derived and the corresponding first order finite volume scheme is then constructed. The piecewise linear spatial reconstruction and two step predictor corrector time integration are then adopted to increase the accuracy of the scheme. Various numerical tests are presented to assert the robustness and accuracy of our scheme.</p>", "Keywords": "Lagrangian methods; Cell-centered scheme; Magnetohydrodynamics; Generalized Lagrange multiplier; Unstructured meshes; 76W05; 65M08", "DOI": "10.1007/s10915-022-01851-6", "PubYear": 2022, "Volume": "91", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate School of China Academy of Engineering Physics, Beijing, China;Institute of Applied Physics and Computational Mathematics, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Applied Physics and Computational Mathematics, Beijing, China"}], "References": []}, {"ArticleId": 94157746, "Title": "Guest Editorial: Recent Advances in Connected and Autonomous Unmanned Aerial/Ground Vehicles", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.comnet.2022.109012", "PubYear": 2022, "Volume": "211", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Roma Tre University, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universit Amar Telidji <PERSON>, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> E<PERSON>", "Affiliation": "Thompson Rivers University, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "LORIA, Universit de Lorraine, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang University, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Song", "Affiliation": "<PERSON><PERSON>ry-Riddle Aeronautical University, USA"}], "References": [{"Title": "GCACS-IoD: A certificate based generic access control scheme for Internet of drones", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "191", "Issue": "", "Page": "107999", "JournalTitle": "Computer Networks"}, {"Title": "Driving assistance system based on data fusion of multisource sensors for autonomous unmanned ground vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "192", "Issue": "", "Page": "108053", "JournalTitle": "Computer Networks"}, {"Title": "A novel resilient and reconfigurable swarm management scheme", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "194", "Issue": "", "Page": "108119", "JournalTitle": "Computer Networks"}, {"Title": "A secure blockchain-oriented data delivery and collection scheme for 5G-enabled IoD environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "195", "Issue": "", "Page": "108219", "JournalTitle": "Computer Networks"}, {"Title": "Energy Efficient Neuro-Fuzzy Cluster based Topology Construction with Metaheuristic Route Planning Algorithm for Unmanned Aerial Vehicles", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "196", "Issue": "", "Page": "108214", "JournalTitle": "Computer Networks"}, {"Title": "A blockchain-based decentralized machine learning framework for collaborative intrusion detection within UAVs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "196", "Issue": "", "Page": "108217", "JournalTitle": "Computer Networks"}, {"Title": "A five-step drone collaborative planning approach for the management of distributed spatial events and vehicle notification using multi-agent systems and firefly algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "198", "Issue": "", "Page": "108282", "JournalTitle": "Computer Networks"}, {"Title": "Beyond 5G for digital twins of UAVs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "197", "Issue": "", "Page": "108366", "JournalTitle": "Computer Networks"}, {"Title": "CNN-SSDI: Convolution neural network inspired surveillance system for UAVs detection and identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "201", "Issue": "", "Page": "108519", "JournalTitle": "Computer Networks"}, {"Title": "EDTP: Energy and Delay Optimized Trajectory Planning for UAV-IoT Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "108623", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 94157797, "Title": "Optimal Convergence Rate of Hamiltonian <PERSON> for Strongly Logconcave Distributions", "Abstract": "We study the Hamiltonian Monte Carlo (HMC) algorithm for sampling from a strongly logconcave density proportional to e−f where f: ℝd → ℝ is μ-strongly convex and L-smooth (the condition number is κ = L/μ). We show that the relaxation time (inverse of the spectral gap) of ideal HMC is N(κ), improving on the previous best bound of N(κ1.5) (<PERSON> et al., 2018); we complement this with an example where the relaxation time is Ω(κ), for any step-size. When implemented with an ODE solver, HMC returns an ε-approximate point in 2-Wasserstein distance using ˜N((κd)0.5 ε−1) gradient evaluations per step and ˜N((κd)1.5 ε−1) total time. © 2022 <PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "Keywords": "Hamiltonian Monte Carlo; logconcave distribution; sampling; spectral gap; strong convexity", "DOI": "10.4086/toc.2022.v018a009", "PubYear": 2022, "Volume": "18", "Issue": "1", "JournalId": 34450, "JournalTitle": "Theory of Computing", "ISSN": "", "EISSN": "1557-2862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Massachusetts Institute of Technology, Cambridge, MA  02139, United States"}, {"AuthorId": 2, "Name": "Santosh S<PERSON>", "Affiliation": "College of Computing, Georgia Institute of Technology, Atlanta, GA  30332, United States"}], "References": []}, {"ArticleId": 94157862, "Title": "Influence-Aware Successive Point-of-Interest Recommendation", "Abstract": "In recent years, with the rapid development of mobile applications, user check-in histories have been increasing. Successive point-of-interest (POI) recommendation has gained growing attention. Existing successive point-of-interest recommendation methods learn long- and short-term user preferences through historical check-in sequences to provide more personalized services. However, due to sparse data and complicated temporal patterns, the application of such technique is still limited by two challenges: 1) difficulty meeting user travel needs in time; 2) difficulty capturing users complicated behavior patterns. To address this problem, we propose a new Inf luence- A ware successive POI recommendation M odel (InfAM), which can learn the influence of POIs in a short-term sequence fragment for next point-of-interest recommendation. To capture periodic patterns of user movements, InfAM takes a user’s check-in data within a day as an input sequence to address the current travel needs of the user. In addition, based on multihead attention mechanism and user embedding, InfAM focuses on the influence of POIs in short-term sequences and general user preferences in these sequences. Therefore, InfAM integrates three specific dependencies, which can fully learn the dynamic interaction between short-term preferences: the influence of POIs in short-term sequence fragments (POI-poi), user preferences (POI-user), and the periodicity of check-ins (POI-time). Evaluation results on real-world datasets show that InfAM achieves state-of-the-art recommendation performance.", "Keywords": "POI recommendation; Influence-aware; Sparse data; Deep learning", "DOI": "10.1007/s11280-022-01055-w", "PubYear": 2023, "Volume": "26", "Issue": "2", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering and Automation, Guilin University Of Electronic Technology, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering and Automation, Guilin University Of Electronic Technology, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Security, Guilin University Of Electronic Technology, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electronic Engineering and Automation, Guilin University Of Electronic Technology, Guilin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Security, Guilin University Of Electronic Technology, Guilin, China"}], "References": [{"Title": "Towards real-time demand-aware sequential POI recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "482", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 94157868, "Title": "Improving adaptive and cognitive skills of children with an intellectual disability and/or autism spectrum disorder: Meta-analysis of randomised controlled trials on the effects of serious games", "Abstract": "The aim of this meta-analysis is to investigate the effectiveness of serious games on social and cognitive skills of children with an intellectual disability (ID) and/or autism spectrum disorder (ASD). Randomised controlled trials of the effectiveness of serious games were identified through a systematic search of PubMed, Web of Science, PsycINFO, and Embase, using keywords related to serious games and the target groups. Eleven studies with a total of 654 participants (mean age 4–11 years) were included. The methodological quality of the studies was assessed using the Cochrane risk of bias assessment tool V2. Serious games were associated with improved adaptive and cognitive skills of children with ID or ASD compared with controls, with a small effect size ( g = 0.420, 95% confidence interval 0.130–0.710; p = .009; number needed to treat = 4.28), with moderate to high heterogeneity. Multiple influence analyses showed that zero heterogeneity could be achieved with exclusion of three studies, one by one, while retaining a significant effect size. Moderation analyses showed no significant differences between the skills examined, the type of controls, involvement of another person, duration of the intervention, or age of the children. These findings indicate that serious games are linked to improvements in adaptive and cognitive functioning of children with ID or ASD, but the association is not necessarily straightforward. Although more study is needed into the effective elements of these games, these results represent an important first step in implementing serious games proven to strengthen daily functioning and social participation of children with ID or ASD.", "Keywords": "Serious game ; Game-based intervention ; Adaptive and cognitive abilities ; Children ; Autism ; Intellectual disabilities", "DOI": "10.1016/j.ijcci.2022.100488", "PubYear": 2022, "Volume": "33", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>je Universiteit Amsterdam, Department of Clinical Child and Family Studies & Amsterdam Public Health, Van der Boechorststraat 7, 1081 BT Amsterdam, The Netherlands;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, Department of Clinical Child and Family Studies & Amsterdam Public Health, Van der Boechorststraat 7, 1081 BT Amsterdam, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, Department of Clinical Child and Family Studies & Amsterdam Public Health, Van der Boechorststraat 7, 1081 BT Amsterdam, The Netherlands;<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Arnhemse Bovenweg 3, 3941 XM Doorn, The Netherlands"}], "References": []}, {"ArticleId": 94157954, "Title": "Perancangan Sistem Informasi Geografis Pariwisata Pasaman Barat Berbasis Android Menggunakan Metode Haversine", "Abstract": "<p>Wisata merupakan kegiatan yang banyak dilakukan oleh masyarakat saat ini yang bertujuan untuk mencari suasana baru diluar aktifitas rutin harian. Daerah tujuan wisata di sumatera cukup banyak dan beragam sekali salah satunya lokasi wisata yang berada di kabupaten Pasaman Barat. Namun banyak objek wisata yang tidak diketahui oleh para wisatawan yang berkunjung, disebabkan karena kurangnya informasi objek wisata yang tersedia. Sistem Informasi Geografis (SIG) dapat menyediakan informasi lokasi suatu tempat, yang dapat digunakan untuk menampilkan lokasi objek wisata yang dibutuhkan oleh para wisatawan. Perancangan sistem ini memberikan perhitungan estimasi jarak dan rekomendasi lokasi jarak terdekat menggunakan metode Haversine yang berfungsi untuk menghitung jarak antara dua titik. Sistem dirancang pada platform android, database MySQL dan bahasa pemograman PHP. Pengujian menggunakan pengujian White Box, Black Box, dan pengujian perbandingan dengan Google Maps Distance. Pada pengujian White Box pada kode program Haversine Formula dan kode program tampilan wisata didapatkan hasil nilai Cyclomatic Complexity 1 dan 2, disimpulkan kode program memliki level resiko yang sederhana dan tanpa banyak resiko sehingga mudah dilakukan maintenance dan diuji alur programnya. Pengujian Black Box disimpulkan bahwa fungsionalitas sistem berjalan dengan baik, pada analisa pengujian perbandingan didapatkan nilai akurasi 100% dan disimpulkan hasil perhitungan sama dengan Google Maps Distance.</p>", "Keywords": "geographic information system;tourism;west pasaman;haversine;google maps", "DOI": "10.35143/jkt.v7i2.4876", "PubYear": 2021, "Volume": "", "Issue": "Vol. 7 No. 2 (2021)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "Sugeng Purwantoro E.S.G.S", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "Alde Thio <PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}, {"ArticleId": 94157984, "Title": "Pengembangan Aplikasi Pengelolaan Tu<PERSON> dan Fun<PERSON>i Balai <PERSON> (Studi Kasus: Sarana dan <PERSON>)", "Abstract": "<p><PERSON>na dan <PERSON>ela<PERSON> di Balai Monitor Spektrum Frekuensi Radio Kelas I Pekanbaru merupakan salah satu dalam bidang pelayanan kepada masyarakat. Saat ini, sarana dan pelayanan belum menerapkan dan memanfaatkan keberadaan teknologi informasi terintegrasi guna mendukung aktifitas kerja sehari-hari sehingga sebagian besar aktifitas operasional pelayanan masih dilakukan secara manual. Karena sebagian aktifitas masih dilakukan secara manual, otomatis mempersulit dan memperlambat petugas untuk melayani masyarakat dan membuat laporan. Sistem Pengelolaan Tugas Dan Fungsi Seksi Sarana Dan Pelayanan diharapkan dapat membantu mengatasi permasalahan dalam memberikan pelayanan kepada masyarakat serta mengelola data layanan. Sistem dibangun menggunakan metode prototyping 3 kali iterasi. Pengujian terhadap aplikasi ini dilakukan dengan cara User Acceptance Test melalui kuisioner dan Blackbox Testing untuk menguji kesesuaian kebutuhan fungsionalnya. Berdasarkan hasil kuisioner didapatkan bahwa aplikasi memenuhi kriteria dari User Acceptance Test dimana 100% Responden setuju desain tampilan aplikasi mudah diingat. 95% Responden setuju aplikasi mudah dipahami. dan 98,3% Responden setuju memiliki fungsi yang efisien. Berdasarkan hasil butir uji Blackbox testing, aplikasi dapat berjalan dengan lancar sesuai dengan kebutuhan fungsional yang diharapkan.</p>", "Keywords": "Facilities and Services;Website;Prototype", "DOI": "10.35143/jkt.v7i2.4894", "PubYear": 2021, "Volume": "", "Issue": "Vol. 7 No. 2 (2021)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}, {"ArticleId": 94157985, "Title": "An Approach to Churn Prediction for Cloud Services Recommendation and User Retention", "Abstract": "<p>The digital world is very dynamic. The ability to timely identify possible vendor migration trends or customer loss risks is very important in cloud-based services. This work describes a churn risk prediction system and how it can be applied to guide cloud service providers for recommending adjustments in the service subscription level, both to promote rational resource consumption and to avoid CSP customer loss. A training dataset was built from real data about the customer, the subscribed service and its usage history, and it was used in a supervised machine-learning approach for prediction. Classification models were built and evaluated based on multilayer neural networks, AdaBoost and random forest algorithms. From the experiments with our dataset, the best results for a churn prediction were obtained with a random forest-based model, with 64 estimators, having 0.988 accuracy and 0.997 AUC value.</p>", "Keywords": "machine learning; churn; decision analysis; forecasting machine learning ; churn ; decision analysis ; forecasting", "DOI": "10.3390/info13050227", "PubYear": 2022, "Volume": "13", "Issue": "5", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Évora, 7000-671 Évora, Portugal↑Centro ALGORITMI, Vista Laboratory, University of Évora, 7000-671 <PERSON><PERSON><PERSON>, Portugal↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Évora, 7000-671 Évora, Portugal↑Centro ALGORITMI, Vista Laboratory, University of Évora, 7000-671 Évora, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Évora, 7000-671 Évora, Portugal↑Centro ALGORITMI, Vista Laboratory, University of Évora, 7000-671 Évora, Portugal"}], "References": [{"Title": "From Big Data to business analytics: The case study of churn prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106164", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": ********, "Title": "Interpretable deep learning LSTM model for intelligent economic decision-making", "Abstract": "For sustainable economic growth, information about economic activities and prospects is critical to decision-makers such as governments, central banks, and financial markets. However, accurate predictions have been challenging due to the complexity and uncertainty of financial and economic systems amid repeated changes in economic environments. This study provides two approaches for better economic prediction and decision-making. We present a deep learning model based on the long short-term memory (LSTM) network architecture to predict economic growth rates and crises by capturing sequential dependencies within the economic cycle. In addition, we provide an interpretable machine learning model that derives economic patterns of growth and crisis through efficient use of the eXplainable AI (XAI) framework. For major G20 countries from 1990 to 2019, our LSTM model outperformed other traditional predictive models, especially in emerging countries. Moreover, in our model, private debt in developed economies and government debt in emerging economies emerged as major factors that limit future economic growth. Regarding the economic impact of COVID-19, we found that sharply reduced interest rates and expansion of government debt increased the probability of a crisis in some emerging economies in the future.", "Keywords": "LSTM ; Deep learning ; Interpretable machine learning ; Economic prediction", "DOI": "10.1016/j.knosys.2022.108907", "PubYear": 2022, "Volume": "248", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Sangjin Park", "Affiliation": "Graduate School of Future Strategy, Korea Advanced Institute of Science and Technology, Daejeon 34141, Republic of Korea;Financial Supervisory Service, Seoul 07321, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Future Strategy, Korea Advanced Institute of Science and Technology, Daejeon 34141, Republic of Korea;Corresponding author"}], "References": [{"Title": "Portfolio formation with preselection using deep learning from long-term financial data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "113042", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An interpretable knowledge-based decision support system and its applications in pregnancy diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "221", "Issue": "", "Page": "106835", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A financial ticket image intelligent recognition system based on deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "222", "Issue": "", "Page": "106955", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 94158051, "Title": "Joint bi-adversarial learning for unsupervised domain adaptation", "Abstract": "An important challenge of unsupervised domain adaptation (UDA) is how to sufficiently utilize the structure and information of the data distribution, so as to exploit the source domain knowledge for a more accurate classification of the unlabeled target domain. Currently, much research work has been devoted to UDA. However, existing works have mostly considered only distribution alignment or learning domain invariant features by adversarial techniques, ignoring feature processing and intra-domain category information. To this end, we design a new cross-domain discrepancy metric, namely joint distribution for maximum mean discrepancy (JD-MMD), and propose a deep unsupervised domain adaptation learning method, namely joint bi-adversarial learning for unsupervised domain adaptation (JBL-UDA). Specifically, JD-MMD measures cross-domain divergence in terms of both discrepancy and relevance by preserving cross-domain joint distribution discrepancy, as well as their class discriminability. Then, with such divergence measure, JBL-UDA models with two learning modalities, one is founded by the bi-adversarial learning from domains and classes implicitly, while the other explicitly addresses domains and classes alignment via the JD-MMD metric. Besides, JBL-UDA explores structural prior knowledge from data classes and domains to generate class-discriminative and domain-invariant representations. Finally, extensive evaluations exhibit state-of-the-art accuracy of the proposed methodology.", "Keywords": "Unsupervised domain adaptation ; Adversarial learning ; Prior knowledge ; Joint distribution discrepancy ; Joint bi-adversarial learning", "DOI": "10.1016/j.knosys.2022.108903", "PubYear": 2022, "Volume": "248", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Qing Tian", "Affiliation": "School of Computer and Software, Nanjing University of Information Science and Technology, Nanjing, China;Engineering Research Center of Digital Forensics, Ministry of Education, Nanjing University of Information Science and Technology, Nanjing, China;Corresponding author at: School of Computer and Software, Nanjing University of Information Science and Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Software, Nanjing University of Information Science and Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "Yi Chu", "Affiliation": "School of Computer and Software, Nanjing University of Information Science and Technology, Nanjing, China"}], "References": [{"Title": "Geometric Knowledge Embedding for unsupervised domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; Yuguang Yan; Yuzhong Ye", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105155", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Survey of Unsupervised Deep Domain Adaptation", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Knowledge Preserving and Distribution Alignment for Heterogeneous Domain Adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": 94158057, "Title": "Correction to: Personal health record system based on social network analysis", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-022-13039-z", "PubYear": 2022, "Volume": "81", "Issue": "19", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Informatics, Faculty of Medical Sciences, Tarbiat Modares University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Medical Informatics, Faculty of Medical Sciences, Tarbiat Modares University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Alzahra University, Tehran, Iran"}], "References": []}, {"ArticleId": 94158089, "Title": "<PERSON><PERSON><PERSON><PERSON>Liouville Fractional Integral Inequalities for Generalized Harmonically Convex Fuzzy-Interval-Valued Functions", "Abstract": "The framework of fuzzy-interval-valued functions (FIVFs) is a generalization of interval-valued functions (IVF) and single-valued functions. To discuss convexity with these kinds of functions, in this article, we introduce and investigate the harmonically $$\\mathsf{h}$$ \n h \n -convexity for FIVFs through fuzzy-order relation (FOR). Using this class of harmonically $$\\mathsf{h}$$ \n h \n -convex FIVFs ( $$\\mathcal{H}-\\mathsf{h}$$ \n \n H \n - \n h \n \n -convex FIVFs), we prove some Hermite–<PERSON>rd ( H ⋅ H ) and <PERSON><PERSON>–<PERSON> ( H ⋅ H <PERSON>r) type inequalities via fuzzy interval R<PERSON>mann–<PERSON>ou<PERSON> fractional integral (FI <PERSON>–<PERSON> fractional integral). The concepts and techniques of this paper are refinements and generalizations of many results which are proved in the literature.", "Keywords": "Harmonically-convex fuzzy-interval-valued function; Her<PERSON>; <PERSON><PERSON><PERSON> inequality; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> inequality", "DOI": "10.1007/s44196-022-00081-w", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, COMSATS University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computers and Information Technology, Taif University, Taif, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Facultad de Economía Y Empresa and Multidisciplinary Institute of Enterprise (IME), University of Salamanca, Salamanca, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, College of Education, University of Sulaimani, Sulaimani, Iraq"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, College of Engineering, Taif University, Taif, Saudi Arabia"}], "References": [{"Title": "Bipolar fuzzy Dombi prioritized aggregation operators in multiple attribute decision making", "Authors": "<PERSON><PERSON><PERSON><PERSON> Jana; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "5", "Page": "3631", "JournalTitle": "Soft Computing"}, {"Title": "Multiple-attribute decision making problems based on SVTNH methods", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "3717", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Some New Classes of Preinvex Fuzzy-Interval-Valued Functions and Inequalities", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1403", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Harmonically Convex Fuzzy-Interval-Valued Functions and Fuzzy-<PERSON><PERSON>–Liouville Fractional Integral Inequalities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1809", "JournalTitle": "International Journal of Computational Intelligence Systems"}]}, {"ArticleId": 94158090, "Title": "Fostering Digital Media-Realted Competences of Student Teachers", "Abstract": "<p>Beyond controversy, identifying and using the potential of digital media for teaching requires media-related skills of future teachers. The paper presents in a first step a framework for digital media-related competencies. Within this framework, different areas are identified in which teachers need media pedagogical (didactical), subject-specific and technological competencies related to the instructional design of digital media. In a second step, an educational approach to support student teachers to develop these competencies is presented. This approach is based on project learning and encompasses a media-based teaching project and a media development project. Hereby, different support structures are implemented, i.e. blended learning proposals and self-learning materials, personal support, and learning analytics. Finally, the project evaluation with formative and summative elements, which guides the iterative process of implementing the educational approach into the existing teacher education programme, is presented.</p>", "Keywords": "Teacher education; Digital competence framework; Project-based learning; Fostering digital competencies", "DOI": "10.1007/s42979-022-01135-8", "PubYear": 2022, "Volume": "3", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "University of Education Weingarten, Weingarten, Germany"}], "References": []}, {"ArticleId": 94158103, "Title": "Robustness of Numerically Computed Contraction Metrics", "Abstract": "<p>Contraction metrics are an important tool to show the existence of exponentially stable equilibria or periodic orbits, and to determine a subset of their basin of attraction. One of the main advantages is that contraction metrics are robust with respect to perturbations of the system, i.e. a contraction metric for one particular system is also a contraction metric for a perturbed system. In this paper, we discuss numerical methods to compute contraction metrics for dynamical systems, with exponentially stable equilibria or periodic orbits, and perform perturbation analysis. In particular, we prove the robustness of such metrics to perturbations of the system and give concrete bounds. The results imply that a contraction metric, computed for a particular system, remains a contraction metric for the perturbed system. We illustrate our results by computing contraction metrics for systems from the literature, both with exponentially stable equilibria and exponentially stable periodic orbits, and then investigate the validity of the metrics for perturbed systems. Parts of the results are published in <PERSON><PERSON><PERSON> et al. (Proceedings of the 18th International Conference on Informatics in Control, Automation and Robotics (ICINCO), 2021).</p>", "Keywords": "Contraction metric; Radial basis functions; Exponentially stable; Equilibrium point; Periodic orbit; Dynamical system", "DOI": "10.1007/s42979-022-01128-7", "PubYear": 2022, "Volume": "3", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Sussex, Brighton, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Physical Sciences, University of Iceland, Reykjavik, Iceland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Physical Sciences, University of Iceland, Reykjavik, Iceland"}], "References": []}, {"ArticleId": 94158107, "Title": "A hesitant fuzzy linguistic bidirectional projection-regret decision making model", "Abstract": "Real-world decision-making problems are usually defined under uncertain contexts due to their complexity. In such situations, the psychological behavior of decision makers is often ignored, and it is assumed that they can provide accurate knowledge despite of being in a context defined as ‘uncertain’. Such assumptions are not realistic and may bias final decisions. To overcome such problems, this paper aims to consider the psychological behavior of decision makers and facilitate preference modeling under uncertainty in the decision-making process. It will do so by introducing a novel decision-making model that includes new bidirectional projection-based regret theory (RT) measures under a hesitant fuzzy linguistic term set (HFLTS) environment, thus establishing novel utility and new regret-rejoice functions to handle decision-making situations. Eventually, a supplier selection case study is used to show the performance of the proposed method, and a fair comparison with two existing methods is carried out to illustrate the advantages of our method.", "Keywords": "Hesitant fuzzy linguistic term set ; Bidirectional projection model ; Regret theory", "DOI": "10.1016/j.cie.2022.108197", "PubYear": 2022, "Volume": "169", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management and E-Business, Zhejiang Gongshang University, Hangzhou, Zhejiang 310018, P.R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Decision Sciences Institute, Fuzhou University, Fuzhou, Fujian 350116, PR China;Key Laboratory of Spatial Data Mining & Information Sharing of Ministry of Education, Fuzhou University, Fuzhou, Fujian 350116, PR China;Corresponding author at: School of Economics and Management, Fuzhou University, Fuzhou, Fujian 350116, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NUS Business School and The Logistics Institute–Asia Pacific, National University of Singapore, Singapore"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Jaén, 23071 Jaén, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Jaén, 23071 Jaén, Spain"}], "References": [{"Title": "Hesitancy degree-based correlation measures for hesitant fuzzy linguistic term sets and their applications in multiple criteria decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "275", "JournalTitle": "Information Sciences"}, {"Title": "A projection-based regret theory method for multi-attribute decision making under interval type-2 fuzzy sets environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "108", "JournalTitle": "Information Sciences"}, {"Title": "A shadowed set-based TODIM method and its application to large-scale group decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "135", "JournalTitle": "Information Sciences"}, {"Title": "Effects of the entropy weight on TOPSIS", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114186", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Two-sided matching decision making with multi-granular hesitant fuzzy linguistic term sets and incomplete criteria weight information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114311", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94158136, "Title": "Multiagent-based deep reinforcement learning for risk-shifting portfolio management", "Abstract": "The growing popularity of quantitative trading in pursuit of a systematic and algorithmic approach to investment has drawn considerable attention among traders and investment firms. Consequently, an effective computational method for evaluating potential risk factors and returns is crucial for the development of algorithmic trading strategies. In traditional finance and financial engineering research, statistical approaches have been widely applied to quantitative analysis. Meanwhile, investor demand for quantitative hedge funds has surged worldwide. In the current study, the multiperiod portfolio selection problem was considered in terms of the realistic transaction cost model, which is a major concern for quantitative hedge fund managers. We developed a dedicated multiagent-based deep reinforcement learning framework with a two-level nested agent structure to determine effective portfolio management methods with different objectives. In addition, we proposed a specially-designed reward function for investment performance evaluation and a novel policy network structure for trading decision-making. To efficiently identify specific asset attributes in a portfolio, each agent is equipped with a refined deep policy network and a special training method that enables the proposed reinforcement learning agent to learn risk transfer behaviors. The results revealed the effectiveness of our proposed framework, which outperformed several established or representative portfolio selection strategies.", "Keywords": "Portfolio selection ; Deep reinforcement learning ; Multiagent", "DOI": "10.1016/j.asoc.2022.108894", "PubYear": 2022, "Volume": "123", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Management, National Yang Ming Chiao Tung University, Hsinchu 30010, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>ao<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, National Yang Ming Chiao Tung University, Hsinchu 30010, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Management, National Yang Ming Chiao Tung University, Hsinchu 30010, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management and Finance, National Yang Ming Chiao Tung University, Hsinchu 30010, Taiwan;Corresponding author"}], "References": [{"Title": "Financial portfolio optimization with online deep reinforcement learning and restricted stacked autoencoder—DeepBreath", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "156", "Issue": "", "Page": "113456", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sentiment-influenced trading system based on multimodal deep reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107788", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 94158160, "Title": "Joint learning dynamic pruning and attention for person re-identification", "Abstract": "<p>In this paper, we investigate the problem of person re-identification by learning pedestrian distinguishing features and reducing model complexity. Traditional methods usually extract pedestrian features by designing better network structures and loss functions, which lack the consideration of the model size and ignore the impact of model efficiency on the accuracy of person re-identification. In this work, an end-to-end joint learning framework, namely PA-Net, with attention model and dynamic filter pruning algorithm is proposed. First, for a feature node, we mine patterns from a compact representation for attention learning, which points out the direction for dynamic filter pruning during training. The compact representation is obtained by stacking its pairwise relations with all feature nodes as a vector. Second, in an epoch training phase, the filters of small ℓ <sub>2</sub>-norm are given high priority of being pruned to temporarily eliminate their contribution to the model output than those of higher ℓ <sub>2</sub>-norm. Pruned filters can still be updated in the next epoch training phase until some filters no longer have any effect on the model and are completely pruned. Third, the weighted regularized triplet (WRT) loss and center loss are used to constrain the original features, and the softmax loss is used to constrain the batch normalized (BN) processed features to obtain the final score. Comprehensive experiments on the Market-1501, DukeMTMC-reID and MSMT17 datasets clearly show the superior performance of our proposed method in comparison with state-of-the-art methods.</p>", "Keywords": "Person Re-ID; Model pruning; Attention mechanism", "DOI": "10.1007/s11042-022-12195-6", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligent Equipment, Shandong University of Science and Technology, Tai’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligent Equipment, Shandong University of Science and Technology, Tai’an, China"}], "References": []}, {"ArticleId": 94158177, "Title": "Modified normative fish swarm algorithm for optimizing power extraction in photovoltaic systems", "Abstract": "<p> Background : According to ASEAN Centre of Energy 2020 report, electrification rates in certain ASEAN countries are considerably low. The energy sector faces issues of unreliable supply, undersupply and high electricity rates. As power supply is an important source of daily requirement, this research work aims to improve the efficiency of solar power generation efficiency. In line with the aim, we proposed a modified Normative Fish Swarm Algorithm (mNFSA) and applied it to Maximum Power Point Tracking (MPPT) process in Photovoltaic (PV) application systems. Significant modifications have been made to the original NFSA, including the removal of unnecessary features, the formulation of modified behaviors, the refinement of adaptive parameters as well as settings, and the implementation of the MPPT architecture. Results : A complete PV system model is constructed for simulation, in which 10 PV arrays with different shading levels are connected in series in the PV panel. For a consistent evaluation, 10 sets of data on maximum extracted power were collected during MPPT simulation process. The statistical data were recorded for comprehensive performance checks. Overall, mNFSA was compared with 6 other optimization algorithms, including 5 state-of-the-art algorithms and 1 related evolutionary algorithm. Conclusions : The results demonstrate that mNFSA outperforms other compared algorithms in terms of maximum extracted power and relative percentage error, demonstrating higher compatibility and effectiveness of mNFSA for MPPT. The statistical results support that mNFSA is one of the most robust algorithms and best suited for MPPT applications.</p>", "Keywords": "Global optimization; Fish swarm algorithm; Maximum power point tracking; Photovoltaic system; Modeling", "DOI": "10.1007/s12065-022-00724-z", "PubYear": 2023, "Volume": "16", "Issue": "4", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Universiti Sains Malaysia, Penang, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Universiti Sains Malaysia, Penang, Malaysia"}], "References": [{"Title": "Normative fish swarm algorithm (NFSA) for optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "2083", "JournalTitle": "Soft Computing"}, {"Title": "Equilibrium optimizer: A novel optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105190", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94158245, "Title": "Performance analysis of reconfigurable intelligent surface assisted systems under channel aging", "Abstract": "Reconfigurable intelligent surfaces (RISs) have attracted significant attention due to their capability in customizing wireless communication environments to improve system performance. In this study, we investigate the performance of an RIS-assisted multi-user multiple-input single-output wireless communication system, considering the impact of channel aging caused by users' relative movements. In particular, first, we propose a model incorporating the joint effects of channel aging and channel estimation errors to investigate the performance of the RIS-assisted system. Then, we derive novel closed-form expressions for characterizing the sum spectral efficiency with zero-forcing precoding. From our analysis, we unveil that an increase in the temporal channel correlation coefficient, the number of base station antennas, and the received power at the users could help improve system performance. Furthermore, increasing the number of reflecting elements $M$ of the RIS generally yields a good system performance, but with a diminishing return when $M$ is sufficiently large. Finally, simulation results are presented to validate the accuracy of the analytical results.", "Keywords": "reconfigurable intelligent surface (RIS);channel aging;spectral efficiency", "DOI": "10.23919/ICN.2022.0002", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 83030, "JournalTitle": "Intelligent and Converged Networks", "ISSN": "2708-6240", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University,Beijing,China,100044"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University,Beijing,China,100044"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Telecommunications, University of New South Wales,Sydney,Australia,2052"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ZTE Corporation, and State Key Laboratory of Mobile Network and Mobile Multimedia Technology,Shenzhen,China,518057"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Rail Traffic Control and Safety, Beijing Jiaotong University,Beijing,China,100044"}], "References": []}, {"ArticleId": 94158323, "Title": "Customer Prioritization Integrated Supply Chain Optimization Model with Outsourcing Strategies", "Abstract": "<p>Pre-COVID-19, most of the supply chains functioned with more capacity than demand. However, COVID-19 changed traditional supply chains' dynamics, resulting in more demand than their production capacity. This article presents a multiobjective and multiperiod supply chain network design along with customer prioritization, keeping in view price discounts and outsourcing strategies to deal with the situation when demand exceeds the production capacity. Initially, a multiperiod, multiobjective supply chain network is designed that incorporates prices discounts, customer prioritization, and outsourcing strategies. The main objectives are profit and prioritization maximization and time minimization. The introduction of the prioritization objective function having customer ranking as a parameter and considering less capacity than demand and outsourcing differentiates this model from the literature. A four-valued neutrosophic multiobjective optimization method is introduced to solve the model developed. To validate the model, a case study of the supply chain of a surgical mask is presented as the real-life application of research. The research findings are useful for the managers to make price discounts and preferred customer prioritization decisions under uncertainty and imbalance between supply and demand. In future, the logic in the proposed model can be used to create web application for optimal decision-making in supply chains.</p>", "Keywords": "COVID-19 effect;customer prioritization;neutrosophic multiobjective optimization;outsourcing;price discounts;uncertain demand", "DOI": "10.1089/big.2021.0292", "PubYear": 2024, "Volume": "12", "Issue": "6", "JournalId": 18108, "JournalTitle": "Big Data", "ISSN": "2167-6461", "EISSN": "2167-647X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management Sciences, Sir <PERSON> Institute of Technology (SS-CASE-IT), Islamabad, Pakistan."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Management Sciences, Sir <PERSON> Institute of Technology (SS-CASE-IT), Islamabad, Pakistan."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, HITEC University, Taxila, Pakistan."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Applied Computing and Technology, Noroff University College, Kristiansand, Norway."}], "References": [{"Title": "Supply chain management under uncertainty with the combination of fuzzy multi-objective planning and real options approaches", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "7", "Page": "5177", "JournalTitle": "Soft Computing"}, {"Title": "A multi-objective pharmaceutical supply chain network based on a robust fuzzy model: A comparison of meta-heuristics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106331", "JournalTitle": "Applied Soft Computing"}, {"Title": "Customer Prioritization for Medical Supply Chain During COVID-19 Pandemic", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "1", "Page": "59", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 94158334, "Title": "A learning workflow based on an integrated digital toolkit to support education in manufacturing system engineering", "Abstract": "Digital modelling of manufacturing systems is experiencing a fast development, but it still shows significant limitations when considering integration and interoperability of enabling technologies. Indeed, there is still a lack of reference integrated workflows to perform the wide span of tasks, ranging from layout configuration and performance evaluations to 3D representations. Commercial software tools are either too complex or expensive to be approached by non-specialists, therefore it is hard to design effective learning activities in manufacturing system engineering. This paper proposes a structured learning workflow based on an open toolkit that takes advantage of a common ontology-based data model to smoothly integrate digital tools for manufacturing system modelling, performance evaluation, and virtual reality representation. After detailing methodologies and digital tools, the proposed workflow is applied to a pilot case in higher education.", "Keywords": "Learning workflows ; Digital tools ; Manufacturing systems engineering", "DOI": "10.1016/j.jmsy.2022.04.003", "PubYear": 2022, "Volume": "63", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politecnico di Milano, Department of Mechanical Engineering, Milan, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Politecnico di Milano, Department of Mechanical Engineering, Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Politecnico di Milano, Department of Mechanical Engineering, Milan, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Camelot Biomedical Systems, Genova, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "STIIMA-CNR, Institute of Intelligent Industrial Technologies and Systems for Advanced Manufacturing, National Research Council, Milan, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Politecnico di Milano, Department of Mechanical Engineering, Milan, Italy"}], "References": []}, {"ArticleId": 94158471, "Title": "Replay spoof detection for speaker verification system using magnitude-phase-instantaneous frequency and energy features", "Abstract": "<p>Spoofing attack detection is one of the essential components in automatic speaker verification (ASV) systems. The success of ASV-2015 shows a great perspective by detecting the voice conversion and speech synthesis spoofs. However, the researchers address fewer replay attack spoof detection systems, and non-professional impersonators most likely use the replay attacks. This paper detects replay attacks on the ASV system using the ASVspoof-2017-v2.0 corpus. This work is mainly partitioned into two parts. The first part shows the significance of Empirical Mode Decomposition (EMD) and Hilbert Spectrum (HS) to detect the replay attack detection by extracting the instantaneous frequency (IF) and instantaneous energies (IE) from frequency components of the speech signal to differentiate the characteristics of genuine and spoof speech, then it given to rectangular filter cepstral coefficients (RFCC) to obtain the desired set of features to detect whether the given speech sample is genuine or spoof. In the second part, a new score-level fusion system is proposed to increase the system performance. Along with the proposed stand-alone method, Constant-Q cepstral coefficients (CQCC) and All-Pole Group Delay Function (APGDF) methods are used to extract the magnitude and phase features set, respectively. The proposed stand-alone and score-level fusion method improves performance accuracy than other state-of-art techniques.</p>", "Keywords": "Empirical mode decomposition; Hilbert spectrum; RFCC; CQCC; APGDF", "DOI": "10.1007/s11042-022-12380-7", "PubYear": 2022, "Volume": "81", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, VIT University, Vellore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, VIT University, Vellore, India"}], "References": []}, {"ArticleId": 94158484, "Title": "Correction to: Fast semantic segmentation network with attention gate and multi-layer fusion", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-022-13037-1", "PubYear": 2022, "Volume": "81", "Issue": "15", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining & Security, Guangxi Normal University, Guilin, China; School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining & Security, Guangxi Normal University, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining & Security, Guangxi Normal University, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining & Security, Guangxi Normal University, Guilin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining & Security, Guangxi Normal University, Guilin, China"}], "References": []}, {"ArticleId": 94158502, "Title": "A multi-modal dataset for gait recognition under occlusion", "Abstract": "<p>Gait recognition aims to identify people by the way they walk. Currently available gait recognition datasets mainly contain single-person gait data in relatively simple walking conditions, which limits research of robust gait recognition methods. In this paper, OG RGB+D dataset is presented to cope with this crucial limitation of other gait datasets. It includes the common walking conditions under occlusion in daily life, that is, those daily walking conditions in which people’s normal walking patterns are occluded, including self-occlusion caused by views, occlusion caused by clothing or objects, and mutual occlusion between people. The dataset provides multi-modal data to support different types of methods, collected by multiple Azure Kinect DK sensors using synchronous data acquisition system (Multi-Kinect SDAS). Moreover, we propose a model-based gait recognition method SkeletonGait for gait recognition in walking conditions under occlusion, which learns discriminative gait features from human dual skeleton model composed of skeleton and anthropometric features through a siamese Spatio-Temporal Graph Convolutional Network (siamese ST-GCN). The experimental results show that SkeletonGait surpasses state-of-the-art methods in the case of severe occlusion. We believe that the introduction of our dataset will enable the community to apply, adapt, and develop various robust gait recognition methods. The dataset will be available at https://github.com/cvNXE/OG-RGB-D-gait-dataset .</p>", "Keywords": "Gait recognition; Dataset; Azure Kinect DK sensor; Anthropometrics", "DOI": "10.1007/s10489-022-03474-8", "PubYear": 2023, "Volume": "53", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Engineering Laboratory for Integrated Aero-Space-Ground-Ocean Big Data Application Technology, School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Integrated Aero-Space-Ground-Ocean Big Data Application Technology, School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, China"}], "References": [{"Title": "Graph-optimized coupled discriminant projections for cross-view gait recognition", "Authors": "Wanjiang Xu", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "8149", "JournalTitle": "Applied Intelligence"}, {"Title": "mmGaitSet: multimodal based gait recognition for countering carrying and clothing changes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "2", "Page": "2023", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 94158506, "Title": "Automatic data volley: game data acquisition with temporal-spatial filters", "Abstract": "Data Volley is one of the most widely used sports analysis software for professional volleyball statistics analysis. To develop the automatic data volley system, the vision-based game data acquisition is a key technology, which includes the 3D multiple objects tracking, event detection and quality evaluation. This paper combines temporal and spatial features of the game information to achieve the game data acquisition. First, the time-vary fission filter is proposed to generate the prior state distribution for tracker initialization. By using the temporal continuity of image features, the variance of team state distribution can be approximated so that the initial state of each player can be filtered out. Second, the team formation mapping with sequential motion feature is proposed to deal with the detection of event type, which represents the players’ distribution from the spatial concept and the temporal relationship. At last, to estimate the quality, the relative spatial filters are proposed by extracting and describing additional features of the subsequent condition in different situations. Experiments are conducted on game videos from the Semifinal and Final Game of 2014 Japan Inter High School Games of Mens Volleyball in Tokyo Metropolitan Gymnasium. The results show 94.1% rounds are successfully initialized, the event type detection result achieves the average accuracy of 98.72%, and the success rate of the events’ quality evaluation achieves 97.27% on average.", "Keywords": "Sports video analysis; Tracker initialization; Event detection; Quality evaluation", "DOI": "10.1007/s40747-022-00752-3", "PubYear": 2022, "Volume": "8", "Issue": "6", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information, Production and Systems, Waseda University, Kitakyushu City, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information, Production and Systems, Waseda University, Kitakyushu City, Japan"}], "References": [{"Title": "An Efficient Attribute Reduction and Fuzzy Logic Classifier for Heart Disease and Diabetes Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "158", "JournalTitle": "Recent Patents on Computer Sciencee"}, {"Title": "RTFN: A robust temporal feature network for time series classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "571", "Issue": "", "Page": "65", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 94158565, "Title": "Antibody-powered lighting-up fluorescence immunosensor based on hemin/G-quadruplex-quenched DNA-hosted dual silver nanoclusters as emitters", "Abstract": "Immunosensors with many merits such as simplicity, high sensitivity and selectivity are intriguing for the detection of antibodies, especially bivalent antibody (e.g. anti-digoxin antibody, DigA). In this work, we report a sensitive immunosensor for DigA based on the switchable fluorescence of DNA-templated silver nanoclusters (AgNCs) modulated by hemin/G-quadruplex (hGq). To explore the fluorescing performance of one hGq-quenching dual-AgNCs ( d -AgNCs), here we design a functional hosting DNA strand (HGH) encoding with one centered G-rich segment for forming hGq complex in the presence of hemin and K<sup>+</sup>, two terminated C-rich template sequences for the identical clustering of d -AgNCs, and two flanked symmetric domains for specific recognizing and linking. Because of the photoexcited electron transfer process to the stacked hGq, the originally presynthesized d -AgNCs colocalized in two ends are non-emissive, which are retainable after complementarily hybridizing with two recognizable single strands that are both tagged with a digoxigenin hapten, a Fab fragment. Upon introducing DigA, the steric strain associated with the affinity binding of two haptens results in the opening and stretch of hGq in a coil spacer, thereby lighting-up the d -AgNCs fluorescence. Based on one-step signal switch stimulated by targeting antibody, this label-free immunosensor strategy is of high specificity and sensitivity with low picomolar detection limit, as well as simplification, rapidness and cost-effectiveness.", "Keywords": "DNA-templated silver nanoclusters ; Hemin/G-quadruplex complex ; Fluorescence immunosensor ; Anti-digoxin antibody ; Photoexcited electron transfer", "DOI": "10.1016/j.snb.2022.131976", "PubYear": 2022, "Volume": "366", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing (Southwest University), Ministry of Education, School of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, PR China;Corresponding authors"}], "References": [{"Title": "A versatile turn-on fluorometric biosensing profile based on split aptamers-involved assembly of nanocluster beacon sandwich", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "324", "Issue": "", "Page": "128586", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 94158591, "Title": "Multi-level features fusion network-based feature learning for machinery fault diagnosis", "Abstract": "Bearings are one of the most critical components in rotating machinery. Since the failures of bearings will cause unexpected machine damages, it is significant to timely and accurately recognize the defects in bearings. However, due to the nonlinear and nonstationary property of vibration signals, it is still a challenging problem to implement feature extraction and fault diagnosis based on vibration signals As a representative deep neural network (DNN), convolutional neural network (CNN) has been widely used for feature learning of vibration signals for machinery fault diagnosis. Due to the hierarchical structure of CNN, multi-level features will be generated by the layer-by-layer convolutional calculation in the deep network. Thus, it is interesting to select the layer-by-layer features in a concatenation layer for multi-level features fusion. In this paper, a novel CNN, multi-level features fusion network (MLFNet) is proposed for feature learning of vibration signals. Firstly, a multi-scale convolution is developed in MLFNet, where multi-branches with different kernel sizes are utilized to extract fault-related features. Secondly, the features at different layers are coupled by a concatenation layer to preserve discriminate information. Thirdly, an adaptive weighted selection based on dynamic feature selection is proposed for multi-level feature fusion. The effectiveness of MLFNet for machinery fault diagnosis is verified on two bearing test-beds. The experimental results demonstrate that MLFNet has good performance of feature extraction on vibration signals. MLFNet obtained the recognition accuracy of 99.75% for case 1 (single condition) and case 2 (varying condition). It has a better performance on bearing fault diagnosis in comparison with these typical DNNs and the state-of-art methods.", "Keywords": "Bearing ; Fault diagnosis ; Convolutional neural network ; Feature learning ; Multi-level features", "DOI": "10.1016/j.asoc.2022.108900", "PubYear": 2022, "Volume": "122", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Tongji University, Shanghai, 201804, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Tongji University, Shanghai, 201804, PR China;Corresponding author"}], "References": [{"Title": "Intelligent rotating machinery fault diagnosis based on deep learning using data augmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "433", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "An improved feature extraction method using texture analysis with LBP for bearing fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106019", "JournalTitle": "Applied Soft Computing"}, {"Title": "Classification of bearing vibration speeds under 1D-LBP based on eight local directional filters", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "16", "Page": "12175", "JournalTitle": "Soft Computing"}, {"Title": "A new feature extraction approach based on one dimensional gray level co-occurrence matrices for bearing fault classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "161", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Knowledge extraction and insertion to deep belief network for gearbox fault diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "197", "Issue": "", "Page": "105883", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Fault diagnosis of rolling bearing of wind turbines based on the Variational Mode Decomposition and Deep Convolutional Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106515", "JournalTitle": "Applied Soft Computing"}, {"Title": "OrbitNet: A new CNN model for automatic fault diagnostics of turbomachines", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107702", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 94158600, "Title": "Output-related fault detection in non-stationary processes using constructive correlative-SAE and demoting correlative-DNN", "Abstract": "Fault detection in non-stationary processes is a timely research topic in industrial systems. The conventional approaches based on principal component regression (PCR) and partial least-squares (PLS) cannot be loosely used for non-stationary and non-linear processes when the statistical behaviour of the measurements does not follow a Gaussian distribution with constant mean and standard deviation values. This paper introduces a new application of deep learning (DL), specifically a combination of proposed correlative stacked auto-encoder (C-SAE) and correlative deep neural networks (C-DNN) for output-related anomaly detection without complete decomposition of process variables with respect to quality output(s). With this aim, two new constructive and demoting loss functions are proposed to relatively decompose the process measurements with respect to their relevance to quality output variable(s). The loss functions are modified with the incorporation of non-linear correlation analysis and hence integrated into SAE and DNN structures to suggest a correlative SAE and DNN. The proposed C-SAE and C-DNN are integrated into a scheme with an inverted pyramid structure that enables output-related fault detection without limiting stationarity assumptions. Moreover, the proposed framework can be freely applied to both linear and non-linear processes. The performance of the proposed DL strategy is tested and validated on a non-stationary numerical example and Tennessee Eastman Process. The comparison results with recent approaches indicate the outperformance of the proposed approach for process output-related fault detection purposes.", "Keywords": "Output-related fault detection ; Constructive correlative stacked auto-encoders ; Demoting correlative deep neural network ; Non-linear correlation analysis", "DOI": "10.1016/j.asoc.2022.108898", "PubYear": 2022, "Volume": "123", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Alberta, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "Qing <PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Alberta, Canada"}], "References": [{"Title": "Deep quality-related feature extraction for soft sensing modeling: A deep learning approach with hybrid VW-SAE", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "375", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": ********, "Title": "China’s commercial bank stock price prediction using a novel K-means-LSTM hybrid approach", "Abstract": "China’s commercial Bank shares have become the backbone of the capital market. The prediction of a bank&#x27;s stock price has been a hot topic in the investment field. However, the stock price is always unstable and non-linear, challenging the traditional statistical models. Inspired by this problem, a novel hybrid deep learning approach is proposed to improve prediction performance. By modifying the distance measurement algorithm into DTW, an improved K-means clustering algorithm is proposed to cluster out banks with similar price trends. Then those clustered stocks are used to train a long and short-term memory (LSTM) neural network model for static and dynamic stock price prediction. Besides, by transforming the output of the LSTM network into multi-step output to predict multi-time intervals at one time, the performance of the long-term forecasts is improved. Through experiments, it is found that the hybrid model performs better than the single model in generalization ability and accuracy(i.e. R-SQUARE, MAE, MSE). Moreover, the multi-step output static prediction outperforms the dynamic rolling prediction for long-term prediction. In summary, this approach can predict stock prices more accurately and help investors and companies to make more profitable decisions.", "Keywords": "Commerical Bank ; Stock price prediction ; K-means ; DTW ; LSTM neural network ; Hybrid model", "DOI": "10.1016/j.eswa.2022.117370", "PubYear": 2022, "Volume": "202", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Yu<PERSON> Chen", "Affiliation": "School of Economics, Center for Studies of Modern Business, Zhejiang Gongshang University, Hangzhou 310018, China;Tailong Finance School, Zhejiang Gongshang University, Hangzhou 310018, China;College of Business Administration, Capital University of Economics and Business, Beijing 100070, China;Corresponding author at: No. 18, Xuezheng Street, Jianggan District, Hangzhou, Zhejiang 310018, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Statistics and Mathematics, Zhejiang Gongshang University, Hangzhou 310018, China;School of Financial Management, Zhejiang Financial College, Hangzhou 310018, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Financial Management, Zhejiang Financial College, Hangzhou 310018, China"}], "References": [{"Title": "Personalized Recommendation System Based on Collaborative Filtering for IoT Scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "685", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Optimizing LSTM for time series prediction in Indian stock market", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2091", "JournalTitle": "Procedia Computer Science"}, {"Title": "Gold volatility prediction using a CNN-LSTM approach", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "113481", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A graph‐based convolutional neural network stock price prediction with leading indicators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "628", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Data science approach to stock prices forecasting in Indonesia during Covid-19 using Long Short-Term Memory (LSTM)", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "47", "JournalTitle": "Journal of Big Data"}, {"Title": "A Long Short-Term Memory Network Stock Price Prediction with Leading Indicators", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "5", "Page": "343", "JournalTitle": "Big Data"}]}]