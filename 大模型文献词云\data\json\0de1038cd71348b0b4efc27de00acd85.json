[{"ArticleId": 111851150, "Title": "Dataset of 16S ribosomal DNA sequence-based identification of bacteriocinogenic lactic acid bacteria isolated from fermented food samples", "Abstract": "The dataset profiled in this research is built on sequencing of lactic acid bacteria 16S rDNA mined from Nono (N<sub>4</sub> and N<sub>5</sub>), Kunu (K<sub>4</sub> and K<sub>1</sub>) and <PERSON><PERSON><PERSON>. The 16S rDNA sequences files are accessible under the data identification numbers: OK017047, OK017046, OK017044, OK017043, OK017045 at the GenBank database, NCBI. Taxonomic identification and phylogenetic tree analysis were done using the online BLAST (blastn) and MEGA11 software, respectively. The effect of the bacteriocin produced by these organisms on spoilage bacteria associated with salad was evaluated using an agar well diffusion assay. Limosilactobacillus pontis strain EOINONO, Limosilactobacillus pontis strain OGENONO, Limosilactobacillus pontis strain SEOGARI, Lactiplantibacillus plantarum strain MJIKUNU and Limosilactobacillus pontis strain EEIKUNU were the identified bacteriocinogenic organisms while Bacillus tequilensis strain SEOABACHA, Bacillus tequilensis strain EEIABACHA, Achromobacter xylosoxidans strain IMABACHA and Achromobacter insolitus strain MJIABACHA were the identified spoilage organisms.", "Keywords": "Abacha;Antimicrobial peptides;Food preservation;Lactic acid bacteria;Taxonomic identification", "DOI": "10.1016/j.dib.2023.110021", "PubYear": 2024, "Volume": "52", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Microbiology, Faculty of Life Sciences, University of Benin, Private Mail Bag 1154, Benin City, Edo State, Nigeria."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Microbiology, Faculty of Life Sciences, University of Benin, Private Mail Bag 1154, Benin City, Edo State, Nigeria."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Food Security and Safety Focus Area, Faculty of Natural and Agricultural Sciences, North-West University, Private Bag X2046, Mmabatho 2735, South Africa."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Food Security and Safety Focus Area, Faculty of Natural and Agricultural Sciences, North-West University, Private Bag X2046, Mmabatho 2735, South Africa. ;Department of Environmental Management and Toxicology, Faculty of Life Sciences, University of Benin, Private Mail Bag 1154, Benin City, Edo State, Nigeria."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Microbiology, Faculty of Life Sciences, University of Benin, Private Mail Bag 1154, Benin City, Edo State, Nigeria."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Microbiology, Faculty of Life Sciences, University of Benin, Private Mail Bag 1154, Benin City, Edo State, Nigeria."}], "References": []}, {"ArticleId": *********, "Title": "Early Validation of Functional Requirements", "Abstract": "<p>Technical specifications and intended functionalities are often gathered in documents that include requirements written in constrained natural language, that is, natural‐like language with restricted syntax. In the automotive industry one challenge is the ability to produce safe vehicles, emphasizing the importance of safety by design. In the framework of case studies based on functions of autonomous vehicles, we introduce a systematic process for building formal models from automotive requirements written in constrained natural language, and for verifying them. By allowing formal verification at the earliest stages of the development cycle our aim is to avoid the costly discovery of errors at later stages.</p>", "Keywords": "requirement analysis;formal verification;rigorous design", "DOI": "10.1002/inst.12467", "PubYear": 2023, "Volume": "26", "Issue": "4", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Rabea Ameur‐Boulifa", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "The Role of Formalism in System Requirements", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111851334, "Title": "Interoperability Forum for Requirements Exchange", "Abstract": "<p>This article illustrates requirement exchanges based on the STEP AP242 application protocol. These results are the outcomes of the systems engineering interoperability forum (SE‐IF) of the ATLAS program, a working group sponsored by the French industry and the French government. The SE‐IF is composed of manufacturers, who define use cases and assign priorities and solution providers, who implement the standard, carry out interoperability tests and establish recommended practices. The objective of this forum is to enable exchange of requirements and their attributes in a fluid and tool‐agnostic way, to facilitate collaborative work in the context of the extended enterprise. This work gives promising perspectives for standardized transfers of all other systems engineering data.</p>", "Keywords": "", "DOI": "10.1002/inst.12468", "PubYear": 2023, "Volume": "26", "Issue": "4", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "El‐Mehdi <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111851345, "Title": "A blockchain-based scheme for edge–edge collaboration management in time-sensitive networking", "Abstract": "Edge–edge collaboration is the main collaboration mode for edge computing (EC) in time-sensitive networking (TSN). This distributed collaboration mode, in which the computing work is implemented collaboratively by the edge computing nodes (ECNs) without the involvement of cloud servers, poses challenges to collaboration management. However, most existing methods of ECN collaboration only focus on the scheduling of computing resources rather than management process design and rarely consider the transparency, security, and fair distribution guarantee mechanism of collaboration benefits. To address this issue, we propose a blockchain-based and decentralized management scheme for ECNs autonomous collaboration in TSN. First, we design an edge–edge collaboration management framework and its workflow based on blockchain for EC in TSN (TECChain), which combines blockchain with TSN technology and EC paradigm. To solve the consensus problem in TECChain, the proof of diligence (PoD) and delegated PoD (DPoD) consensus mechanisms are designed. Furthermore, by leveraging the high-precision synchronous clock foundation of TSN, we propose two time-slot-driven consensus algorithms, named sequential decision-making based on DPoD (S-DPoD) and Byzantine fault tolerance based on DPoD (BFT-DPoD) respectively. Theoretical analysis results show that the proposed algorithms perform better in terms of security, transactions per second (TPS), energy saving, etc., compared to other algorithms. Comparison experiments reveal that S-DPoD consumes fewer interactive messages and has higher Byzantine fault tolerance and success rate of consensus than BFT-DPoD, but BFT-DPoD has lower transaction verification latency and larger TPS.", "Keywords": "Blockchain ; Collaboration management ; Edge computing ; Time-sensitive networking", "DOI": "10.1016/j.jksuci.2023.101902", "PubYear": 2024, "Volume": "36", "Issue": "1", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Jun<PERSON> Chen", "Affiliation": "School of Computer and Science, Chongqing University of Posts and Telecommunications, Chongqing, 400065, Chongqing, China;Key Laboratory of Industrial Internet of Things and Networked Control, Chongqing University of Posts and Telecommunications, Chongqing, 400065, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>u", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Chongqing University of Posts and Telecommunications, Chongqing, 400065, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Chongqing University of Posts and Telecommunications, Chongqing, 400065, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Chongqing University of Posts and Telecommunications, Chongqing, 400065, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Chongqing University of Technology, Chongqing, 400054, Chongqing, China;Corresponding author"}], "References": []}, {"ArticleId": 111851397, "Title": "Understanding the Indirect Effects of Interactive Systems Within Systems of Systems", "Abstract": "<p>Until recently, research into the sustainable design of interactive systems has primarily focused on the direct material impact of a system, through improving its energy efficiency and optimizing its lifecycle. Yet the way a system is designed and marketed often has wider repercussions, such as rebound effects, and systemic change in practices. These effects are harder to assess (and to anticipate) than the direct physical impact of the construction and use of the system itself. Current tools are unable to account for the complexity of these effects: the underlying causal mechanisms, their multi‐level nature, their different temporalities, and the variety of their consequences (environmental and societal). This is why we are seeking to develop a specific methodology and tool, inspired by systemic design and system dynamics. These are intended for decision‐makers and designers of interactive systems within systems of systems (for example, in the fields of agricultural robotics or public transportation). In this paper, we present this modeling approach and our prototype tool through the example of a second‐hand clothing sales platform.</p>", "Keywords": "sustainability;systems of systems;sociotechnical systems;rebound effect;systemic;methodology;modeling tool", "DOI": "10.1002/inst.12464", "PubYear": 2023, "Volume": "26", "Issue": "4", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111851551, "Title": "Dwarf Mongoose Optimization with Transfer Learning-Based Fish Behavior Classification Model", "Abstract": "<p>Behavioral monitoring can be used to monitor aquatic ecosystems and water quality over time. Using precise and rapid fish performance detection, fishermen may make educated management decisions on recirculating aquaculture systems while decreasing labor. Sensors and procedures for recognizing fish behavior are often developed and prepared by researchers in big numbers. Deep learning (DL) techniques have revolutionized the capability to automatically analyze videos, which were utilized for behavior analysis, live fish detection, biomass estimation, water quality monitoring, and species classification. The benefit of DL is that it could automatically study the extraction of image features and reveals brilliant performance in identifying sequential actions. This paper focuses on the design of Dwarf Mongoose Optimization with Transfer Learning-based fish behavior classification (DMOTLB-FBC) model. The presented DMOTLB-FBC technique intends to effectively monitor and classify fish behaviors. Initially, the DMOTLB-FBC technique follows Gaussian filtering (GFI) technique for noise removal process. Besides, a transfer learning (TL)-based neural architectural search network (NASNet) model is used to produce a collection of feature vectors. For fish behavior classification, graph convolution network (GCN) model is employed in this work. To improve the fish behavior classification results of the DMOTLB-FBC technique, the DWO algorithm is applied as a hyperparameter optimizer of the GCN model. The experimentation analysis of the DMOTLB-FBC technique is tested on fish video dataset and the widespread comparison study reported the enhancements of the DMOTLB-FBC technique over other recent approaches.</p>", "Keywords": "Fish behavior classification; aquaculture; computer vision; deep learning; metaheuristics", "DOI": "10.1142/S0219467825500536", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 14884, "JournalTitle": "International Journal of Image and Graphics", "ISSN": "0219-4678", "EISSN": "1793-6756", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sathyabama Institute of Science and Technology, Chennai 600119, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Sathyabama Institute of Science and Technology, Chennai 600119, Tamil Nadu, India"}], "References": []}, {"ArticleId": *********, "Title": "Assessment of undergraduates nursing students’ knowledge toward MRI safety: Cross-sectional study", "Abstract": "<b  >Introduction</b> Magnetic resonance imaging (MRI) has been traditionally regarded as a relatively safe imaging modality due to the absence of ionizing radiation. However, considerable immediate safety risks are intrinsic to the MRI environment, necessitating comprehensive awareness and training among healthcare professionals, particularly nurses, who accompany their patients to the MRI department in most cases. This study was carried out to evaluate the level of knowledge and awareness of MRI safety issues among nursing students within the nursing faculty at Jazan University (JU). <b  >Materials and methods</b> A descriptive, cross-sectional survey encompassing 128 undergraduate nursing students was conducted. A structured questionnaire comprising 25 questions was employed and classified into three sections, encompassing demographic data and MRI safety awareness. Data were analyzed using Microsoft Excel, and frequencies and percentages of responses were calculated and summarized. <b  >Results</b> Of the 128 participants, 111 were females, and 17 were males, distributed across the 2nd (41%), 3rd (20%), and 4th (39%) academic years. Participants exhibited a commendable grasp of MRI contraindications related to ferromagnetic materials such as oxygen cylinders and metal needles. However, their knowledge concerning the operational aspects of MRI, such as examination duration, was found to be lacking, indicating a significant deficiency in comprehension regarding the operational aspects of MRI technology. <b  >Conclusion</b> This study highlights the relatively high level of awareness regarding MRI safety among nursing students at JU. However, the identified lack of knowledge pertaining to the operational aspects of MRI technology emphasizes the need for further education and training in this area. To address this gap, we recommend the establishment of comprehensive MRI safety courses and training programs customized for undergraduate nursing students.", "Keywords": "MRI ; Safety ; Nurse students ; Awareness ; Knowledge", "DOI": "10.1016/j.jrras.2023.100801", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 8, "Name": "Turkey Refaee", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 10, "Name": "Bandar Alwadani", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan 45142, Saudi Arabia"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Department of Radiological Sciences and Medical Imaging, College of Applied Medical Sciences, Majmaah University, Majmaah, 11952, Saudi Arabia"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Department of Medical Imaging, Faculty of Applied Medical Sciences, The Hashemite University, Zarqa, Jordan"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "Department of Medical Imaging, Faculty of Applied Medical Sciences, The Hashemite University, Zarqa, Jordan;Department of Medical Radiography, School of Health Sciences, University of Doha for Science and Technology, Doha, Qatar"}], "References": [{"Title": "Evaluation of awareness and knowledge regarding MRI safety among students in the faculty of applied medical science at Jazan University", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "4", "Page": "100669", "JournalTitle": "Journal of Radiation Research and Applied Sciences"}]}, {"ArticleId": 111851706, "Title": "Dynamic decision-making for knowledge-enabled distributed resource configuration in cloud manufacturing considering stochastic order arrival", "Abstract": "The emergence of COVID-19 caused the stagnation of production activities and promoted the changing market demand. These uncertainties not only brought great challenges to the manufacturing approaches led by a single enterprise, but also threatened the stability of inherent supply chain. To maintain market competitiveness, an efficient distributed manufacturing resource allocation method is urgently needed by manufacturers. Cloud manufacturing (CMfg) is an advanced service-oriented manufacturing paradigm that breaks physical space constraints to integrate distributed resources across enterprises, and provides on-demand configuration of manufacturing services for personalized consumer needs in real-time. The focus of this paper is to achieve dynamic configuration of distributed resources in CMfg considering stochastic order arrival, while reducing overall completion time and improving resource utilization. First, a dynamic knowledge graph for distributed resources is constructed, and its definition and construction methods are introduced. Secondly, semantic matching between massive optional manufacturing resources and multiple types of subtasks is achieved through knowledge extraction, thereby obtaining a candidate set of manufacturing resources that meet basic requirements for each subtask. An artificial intelligence (AI) scheduler based on deep reinforcement learning is developed, and order urgency is incorporated into the design of state observation vectors. AI scheduler can generate optimal decision results based on environmental observations, select high-quality manufacturing service compositions over candidate sets, and ultimately achieve efficient distributed resources configuration. Finally, Dueling DQN-based training method is put forward to optimize AI scheduler, enabling adaptable decision-making performance in dynamic environment. In the experiment, a simulation environment with 18 different settings is designed that considers stochastic processing time, random order compositions and various order arrival patterns. The proposed graph-based matching method, scheduling policy learning method and dynamic decision-making method are tested in the simulation environment. The experiment results demonstrate that the cognitive and AI joint driven distributed manufacturing resource configuration method is superior to traditional methods in terms of policy learning speed and scheduling solution quality.", "Keywords": "", "DOI": "10.1016/j.rcim.2023.102712", "PubYear": 2024, "Volume": "87", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210000, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Mechatronics Engineering, The University of Auckland, Auckland 1142, New Zealand"}, {"AuthorId": 4, "Name": "Haihua Zhu", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210000, China"}], "References": [{"Title": "Shared manufacturing in the sharing economy: Concept, definition and service operations", "Authors": "<PERSON><PERSON> Yu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "106602", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A blockchain technology based trust system for cloud manufacturing", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "5", "Page": "1451", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "An effective dynamic service composition reconfiguration approach when service exceptions occur in real-life cloud manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102143", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A novel knowledge graph-based optimization approach for resource allocation in discrete manufacturing workshops", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102160", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A flexible configuration method of distributed manufacturing resources in the context of social manufacturing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Hai<PERSON> Zhu", "PubYear": 2021, "Volume": "132", "Issue": "", "Page": "103511", "JournalTitle": "Computers in Industry"}, {"Title": "Towards Self-X cognitive manufacturing network: An industrial knowledge graph-based multi-agent reinforcement learning approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Li", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "16", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Adaptive multi-objective service composition reconfiguration approach considering dynamic practical constraints in cloud manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "234", "Issue": "", "Page": "107607", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Logistics-involved service composition in a dynamic cloud manufacturing environment: A DDPG-based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "76", "Issue": "", "Page": "102323", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Probing an intelligent predictive maintenance approach with deep learning and augmented reality for machine tools in IoT-enabled manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "102357", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Parallel batch processing machines scheduling in cloud manufacturing for minimizing total service completion time", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "146", "Issue": "", "Page": "105899", "JournalTitle": "Computers & Operations Research"}, {"Title": "Dynamic job shop scheduling based on deep reinforcement learning for multi-agent manufacturing systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "102412", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Solving task scheduling problems in cloud manufacturing via attention mechanism and deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "", "Page": "452", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Cloud–edge collaboration task scheduling in cloud manufacturing: An attention-based deep reinforcement learning approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "177", "Issue": "", "Page": "109053", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Sequence generation for multi-task scheduling in cloud manufacturing with deep reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "67", "Issue": "", "Page": "315", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Multi-task scheduling in cloud remanufacturing system integrating reuse, reprocessing, and replacement under quality uncertainty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "68", "Issue": "", "Page": "176", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Solving multi-task manufacturing cloud service allocation problems via bee colony optimizer with transfer learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101984", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Semantic models and knowledge graphs as manufacturing system reconfiguration enablers", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "86", "Issue": "", "Page": "102625", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 111851715, "Title": "Parameter estimation of transfer function of viscous clutch with electrorheological fluid and torque control", "Abstract": "<p>This article concerns the identification of a transfer function which contains principal design and dynamic parameters of a hydraulic viscous disc clutch operated and controlled by an electrorheological (ER) fluid as a working fluid. A mathematical model of clutch dynamics is created as part of the clutch identification. It is considered that the changes in torque over time are described by transfer function represented by the first-order system with time delay. A prototypic clutch and a test bench are constructed, and tests are performed. A high voltage step input is used to trigger the torque changes. Constant parameters of the first-order system with time delay are identified on the basis of bench tests. According to the values of these parameters, a proportional-integral (PI) controller is selected for the closed-loop control system. It is found that operation of such a control system utilizing the identified design parameters is acceptable with high accuracy under external disturbances.</p>", "Keywords": "", "DOI": "10.1177/1045389X231200079", "PubYear": 2024, "Volume": "35", "Issue": "4", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechatronics, Jan <PERSON> University in Kielce, Kielce, Poland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical Engineering, Ka<PERSON><PERSON>rz <PERSON> University of Technology and Humanities in Radom, Radom, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "New Chemical Syntheses Institute, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, The State University of New York, Korea (SUNY Korea), Incheon, Republic of Korea;Department of Mechanical Engineering, Industrial University of Ho Chi Minh City (IUH), Ho Chi Minh City, Vietnam"}], "References": []}, {"ArticleId": 111851796, "Title": "Promoting game flow in the design of a hyper-casual mobile game", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSPR.2022.121653", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 85110, "JournalTitle": "International Journal of Student Project Reporting", "ISSN": "2634-1948", "EISSN": "2634-1956", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111852069, "Title": "Unveiling additively manufactured cellular structures in hip implants: a comprehensive review", "Abstract": "The prospect of improved quality of life and the increasingly younger age of patients benefiting from Total Hip Arthroplasty will soon lead to the landmark of 10 million interventions per year worldwide. More than 10% of these procedures lead to significant bone resorption, increasing the need for revision surgeries. Current research focuses on the development of hip implant designs to achieve a stiffness profile closer to the natural bone. Additive Manufacturing has emerged as a viable solution by offering promising results in the fabrication of implant architectures based on metallic cellular structures that have demonstrated their capacity to replicate bone behavior mechanically and biologically. Aiming to offer an up-to-date overview of titanium cellular structures in hip implants, for both acetabular and femoral components, produced by Additive Manufacturing, including its design intricacies and performance, this comprehensive review meticulously examines the historical development of hip implants, encompassing commercial solutions and innovative attempts. A broad view of the practical applications and transformative potential of hip implants incorporating cellular structures is presented, aiming to outline opportunities for innovation.", "Keywords": "Additive manufacturing; Cellular structures; Metallic materials; Total hip arthroplasty; Architectured hip implants; Osteointegration", "DOI": "10.1007/s00170-023-12769-0", "PubYear": 2024, "Volume": "130", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for MicroElectroMechanical Systems (CMEMS-UMinho), University of Minho, Guimarães, Portugal; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for MicroElectroMechanical Systems (CMEMS-UMinho), University of Minho, Guimarães, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Materials Science and Engineering, School of Chemical Technology, Aalto University Foundation, Espoo, Finland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CICECO, Aveiro Institute of Materials, Department of Materials and Ceramic Engineering, University of Aveiro, Aveiro, Portugal"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for MicroElectroMechanical Systems (CMEMS-UMinho), University of Minho, Guimarães, Portugal"}], "References": [{"Title": "Design, optimization, and validation of mechanical properties of different cellular structures for biomedical application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "Page": "1253", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Environmental impacts of conventional and additive manufacturing for the production of Ti-6Al-4V knee implant: a life cycle approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "3-4", "Page": "787", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Design, optimization, and selective laser melting of vin tiles cellular structure-based hip implant", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "7-8", "Page": "2037", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effect of additive manufactured hybrid and functionally graded novel designed cellular lattice structures on mechanical and failure properties", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "128", "Issue": "11-12", "Page": "4873", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 111852251, "Title": "AFIS Report on Challenges for the Near Future", "Abstract": "<p>Systems engineering shall adapt itself with the changes of the world. System engineering communities like (AFIS 2022) and (INCOSE 2021) have been developing their visions with the identification of needs that systems engineering shall integrate to address new challenges. The systems engineering discipline shall be now considered at the enterprise level and be included in the governance of the project. Systems engineering and system thinking shall be deployed on all layers of the organization and horizontally to ensure the consistency the definition, production, and all phases of the lifecycle of the system within all involved organizations. The future is paved with unknowns. Systems engineering can integrate new methodologies, new posture to complete its analytical based tools to better face new kinds of complexity. These new challenges are often due to the reallocation of functions and responsibilities between human and machine in the context of autonomous systems. These collaborative socio‐technical systems induce new questions for systems engineering which needs to integrate new domain of skills and interacts with new disciplines from the soft sciences.</p>", "Keywords": "", "DOI": "10.1002/inst.12461", "PubYear": 2023, "Volume": "26", "Issue": "4", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111852289, "Title": "Perceived Factors Analysis for Depression and Suicidal Ideation among Bangladeshi University Students Using Association Algorithm", "Abstract": "<p>Depression stands as a prominent and prevalent mental health issue, representing a significant global public health concern. Its emergence can be attributed to diverse factors. Suicide stands as a prominent global cause of death, eliciting concern on a widespread scale. This study was to analyze the perceived factors for depression and suicidal ideation among Bangladeshi university students in Bangladesh. There are so many factors such as Loneliness, Hopelessness, Helplessness, Relationship Issues, Grade problems, Academic Pressure, Parental problems, Money problems, Social Comparison, Social Media Influence, Family Expectations, Lack of Sleep, Uncertain Future, Health Issues, Bullying, Substance Abuse and Unemployment etc. These factors vary among male and female students. Apriori association algorithm were used to calculate support, confidence and lift of factors sets. Frequent factors sets and relationship were found from the work using Apriori association algorithm. The work is an online survey-based study about psychological and stress status of participants and statistical analysis is used for concluding the results. The research participants are Bangladeshi university students, Data collection carried out by online questionnaire. The findings from data analysis indicated that academic pressure (72.41%), uncertain future (56.32%), hopelessness (48.28%), family expectation (47.13%), financial crisis (42.53%), loneliness (41.38%) and unemployment (37.93%) are the key factors. The prevention of suicides is achievable. Hence, identifying depression and forecasting the potential for suicide risk serves as a means to prevent instances of self-harm within the university student population.</p>", "Keywords": "", "DOI": "10.9734/ajrcos/2023/v16i4406", "PubYear": 2023, "Volume": "16", "Issue": "4", "JournalId": 63540, "JournalTitle": "Asian Journal of Research in Computer Science", "ISSN": "", "EISSN": "2581-8260", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111852317, "Title": "Adaptive partitioning and efficient scheduling for distributed DNN training in heterogeneous IoT environment", "Abstract": "With the increasing proliferation of Internet-of-Things (IoT) devices, it is a growing trend toward training a deep neural network (DNN) model in pipeline parallelism across resource-constraint IoT devices. To ensure the model convergence and accuracy, synchronous pipeline parallelism is usually adopted. However, the synchronous pipeline can incur a long waiting time due to its gradient aggregation of all microbatches. It is urgent for a DNN model to design an adaptive partitioning and efficient scheduling scheme in heterogeneous IoT environment. To address this problem, we propose a policy gradient based model partitioning and scheduling scheme (PG-MPSS) to minimize per-iteration training time. More specifically, we first design a double-network framework to divide and schedule a DNN model. Then, we adopt a policy gradient algorithm to update the double-network parameters, aiming at learning an optimal double-network model. We conduct extensive experiments to compare the DNN training time of the PG-MPSS scheme with that of Dynamic Programming (DP), Genetic Algorithm (GA), Particle Swarm Optimization (PSO), Average&amp;Greedy (AG) and Proximal Policy Optimization (PPO) five baseline algorithms under different experimental settings. The related experimental results demonstrate that the PG-MPSS scheme can greatly expedite synchronous pipeline training of a DNN model.", "Keywords": "", "DOI": "10.1016/j.comcom.2023.12.034", "PubYear": 2024, "Volume": "215", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, 310018, Hangzhou, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Huang", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, 310018, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Technology, Deakin University, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, 310018, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University, China"}], "References": [{"Title": "A Survey on Distributed Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Survey on recent advances in IoT application layer protocols and machine learning scope for research directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "5", "Page": "727", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Online and reliable SFC protection scheme of distributed cloud network for future IoT application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Yinjin Fu", "PubYear": 2023, "Volume": "208", "Issue": "", "Page": "179", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 111852342, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0166-3615(23)00220-8", "PubYear": 2024, "Volume": "155", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [], "References": []}, {"ArticleId": 111852458, "Title": "Novel phase adjournment data capturing technique for a mobile object in wireless sensor network", "Abstract": "<p>In the modern era, the wireless sensor networks play a vital role in scientific and industrial applications. The enduring energy is a crucial metric parameter to be considered in the process of calculating the life span of the wireless sensor networks. Because each sensor node having non-renewable battery supply. In the first generation of wireless sensor applications, data has been transferred from each sensor node to the sink. Further evolution suggests a cluster head approach for transferring data from sensor field to sink. Different strategies of cluster head selection have provided better energy utilization. In the context of effective energy utilization, this work proposes an algorithmic technique called Phase Adjournment Data Capture Technique for a mobile object. In this technique, data will be captured by the mobile object based on its present traveling layer count. When a mobile object goes beyond the receiving limit, it will initiate the sleep mode flag to indicate the master node to stop transferring the data. This technique extends the improvement in energy consumption and also provides effective control of data flooding. It extends and eradicates the need for an erudite antenna essential for setting the direction of the data.</p>", "Keywords": "Sensor networks; Non-renewable battery supply; Mobile object; Master node; Energy consumption; Duty cycle; Erudite antenna; Data flooding", "DOI": "10.1007/s41870-023-01636-6", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, SDM College of Engineering and Technology, Dharwad, Visvesvaraya Technological University, Belagavi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SDM College of Engineering and Technology, Dharwad, Visvesvaraya Technological University, Belagavi, India"}], "References": [{"Title": "Energy and delay efficient data acquisition in wireless sensor networks by selecting optimal visiting points for mobile sink", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "9", "Page": "11671", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Efficient Mobile Sink Routing in Wireless Sensor Networks Using Bipartite Graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "5", "Page": "182", "JournalTitle": "Future Internet"}]}, {"ArticleId": *********, "Title": "Integration of production–maintenance planning and monitoring simple linear profiles via Hotelling's T2 control chart and particle swarm optimization", "Abstract": "This paper introduces an integrated model that merges three key concepts: quality control through linear profile monitoring using Hotelling&#x27;s T<sup>2</sup> control chart, production cost evaluation, and maintenance policies. The objective function of the integrated model is to minimize total costs. This study is the first to jointly consider production and maintenance costs in the framework of profile monitoring using Hotelling’s T<sup>2</sup> control chart for linear profiles without compromising on the versatility of simple linear profiles as a modeling tool, particularly in calibration applications. Appropriate assumptions are made for the proposed integrated model and the particle swarm optimization algorithm is implemented to determine decision variables. To evaluate the effectiveness of the integrated model, a numerical example and a comprehensive comparative analysis are conducted. Moreover, a sensitivity analysis to study the effects of various input parameters on the optimal solutions is performed. The findings suggest that adopting the integrated model with specific decision variables results in lower expected total costs when compared to the joint model and fixed parameter schemes.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109864", "PubYear": 2024, "Volume": "188", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Iran University of Science & Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "Siama<PERSON>", "Affiliation": "Department of Industrial Engineering, Iran University of Science & Technology, Tehran, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Faculty of Mechanical Engineering, Jundi-Shapur University of Technology, Dezful, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Business Administration, University of Hamburg, Hamburg, Germany"}], "References": [{"Title": "An integrated model of production scheduling, maintenance and quality for a single machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106239", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Integrated optimization of quality and maintenance: A literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "106924", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A novel run rules based MEWMA scheme for monitoring general linear profiles", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "107031", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Joint planning of maintenance, buffer stock and quality control for unreliable, imperfect manufacturing systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107304", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An economic-statistical design of simple linear profiles with multiple assignable causes using a combination of MOPSO and RSM", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "16", "Page": "11087", "JournalTitle": "Soft Computing"}, {"Title": "Monitoring logistic profiles using variable sample interval approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107438", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An ensemble neural network framework for improving the detection ability of a base control chart in non-parametric profile monitoring", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "117572", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A reliability-oriented integration model of production control, adaptive quality control policy and maintenance planning for continuous flow processes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "176", "Issue": "", "Page": "108985", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Monitoring autocorrelated compositional data vectors using an enhanced residuals Hotelling T 2 control chart", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "181", "Issue": "", "Page": "109280", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Analyzing abnormal pattern of hotelling T 2 control chart for compositional data using artificial neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "180", "Issue": "", "Page": "109254", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "New statistical and machine learning based control charts with variable parameters for monitoring generalized linear model profiles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "184", "Issue": "", "Page": "109562", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Incorporating principal component analysis into Hotelling T 2 control chart for compositional data monitoring", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "186", "Issue": "", "Page": "109755", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 111852549, "Title": "Effect of structural changes of Pd/WO3 thin films on response direction and rate in hydrogen detection", "Abstract": "Hydrogen (H<sub>2</sub> ) has attracted considerable attention as a renewable energy carrier owing to its recyclability and environmental friendliness . However, due to its explosive nature at concentrations above 4 % in air, the detection of H <sub>2</sub> is a critical safety concern. Thereby, this study investigates the impact of the growth forms of the Pd/WO<sub>3</sub> thin film layer on the sensor’s ability to detect H<sub>2</sub> , including the response direction and rate of the sensor’s resistance change. The chemoresistive sensors were constructed using a nanoporous WO <sub>3</sub> film (formed via RF sputtering on a Si/SiO<sub>2</sub> wafer) and a palladium layer (deposited via e-beam evaporation). Experimental results display the excellent hydrogen detection performance of the sensors at concentrations ranging from 1- 10 % (in air) by the change of chemoresistance and demonstrate that the strategies used in fabricating the sensors are effective for practical use. By gaining a deeper understanding of the hydrogen sensing mechanisms in Pd/WO <sub>3</sub> thin films, this study reveals how to improve the performance of hydrogen sensors and ensure their safe use in various industries.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135259", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea;Engineering Research Institute, Ajou University, Suwon 16499, the Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea"}, {"AuthorId": 3, "Name": "Le <PERSON> Duy", "Affiliation": "Department of Materials Science and Engineering, Ajou University, Suwon 16499, the Republic of Korea;Faculty of Materials Science and Technology, University of Science, HoChiMinh city 70000, Viet Nam;Vietnam National University (VNU), HoChiMinh city 70000, Viet Nam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea"}, {"AuthorId": 5, "Name": "Chiwan Park", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Automotive Diagnosis & Development Service (ADDS), Anyang 14117, the Republic of Korea"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea"}, {"AuthorId": 10, "Name": "Hyungtak <PERSON>", "Affiliation": "Department of Energy Systems Research, Ajou University, Suwon 16499, the Republic of Korea;Department of Materials Science and Engineering, Ajou University, Suwon 16499, the Republic of Korea;Correspondence to: Department of Materials Science and Engineering, Department of Energy Systems Research, Ajou University, Suwon-si, Gyeonggi-do 16499, Republic of Korea.; Corresponding author"}], "References": [{"Title": "Electrostatic spray deposition of chemochromic WO3-Pd sensor for hydrogen leakage detection at room temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "327", "Issue": "", "Page": "128930", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Confined interfacial alloying of multilayered Pd-Ni nanocatalyst for widening hydrogen detection capacity", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "330", "Issue": "", "Page": "129378", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 111852621, "Title": "B0-VPG Representation of AT-free Outerplanar Graphs", "Abstract": "A $k$-bend path is a non-self-intersecting polyline in the plane made of at most $k+1$ axis-parallel line segments. \n\tB$_{k}$-VPG is the class of graphs which can be represented as intersection graphs of $k$-bend paths in the same plane.\n\tIn this paper, we show that all AT-free outerplanar graphs are B$_{0}$-VPG, i.e., intersection graphs of horizontal and vertical line segments in the plane. \nOur proofs are constructive and give a polynomial time B$_{0}$-VPG drawing algorithm for the class.\n\tIn fact, we show the existence of a B$_{0}$-VPG representation for a superclass of AT-free graphs namely linear outerplanar graphs which we define as the class of subgraphs of biconnected outerpaths. \n\t\tOuterpaths are outerplanar graphs which admit a planar drawing whose weak dual is a path.\n\n\t    Following a long line of improvements, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [SODA 2018] showed that all planar graphs are B$_{1}$-VPG. \n\tSince there are planar graphs which are not B$_{0}$-VPG, characterizing B$_{0}$-VPG graphs among planar graphs becomes interesting. \n\t<PERSON><PERSON><PERSON> et al. [WG 2012] had shown that it is NP-complete to recognize B$_{k}$-VPG graphs within B$_{k+1}$-VPG. Hence recognizing B$_{0}$-VPG graphs within B$_{1}$-VPG is NP-complete in general, but the question\n\tis open when restricted to planar graphs.\n\tThere are outerplanar graphs and AT-free planar graphs which are not B$_{0}$-VPG. \n\tThis piqued our interest in AT-free outerplanar graphs.", "Keywords": "AT-free; B0-VPG; Connectivity augmentation; Graph drawing; Linear outerplanar graph; Outerpath; Outerplanar graphs", "DOI": "10.7155/jgaa.00648", "PubYear": 2023, "Volume": "27", "Issue": "9", "JournalId": 16217, "JournalTitle": "Journal of Graph Algorithms and Applications", "ISSN": "", "EISSN": "1526-1719", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111852667, "Title": "Time series classification with their image representation", "Abstract": "The study is concerned with the problem of classification of multivariate time series using convolutional neural networks (CNNs). As CNNs regard inputs in the form of images, an original image-like format of temporal data is proposed. Along this line, several design alternatives are studied by forming images with the two corresponding coordinates built by the original temporal data and their differences and second differences. An overall design process is presented with a focus on investigating time series-image transformations. Experimental studies involving publicly available data sets are reported, along with a slew of comparative analyses.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.127214", "PubYear": 2024, "Volume": "573", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The Faculty of Mathematics and Information Science, The Warsaw University of Technology, Warsaw, Poland;The Faculty of Applied Information Technology, The University of Information Technology and Management, Rzeszów, Poland;Corresponding author at: The Faculty of Mathematics and Information Science, The Warsaw University of Technology, Warsaw, Poland"}, {"AuthorId": 2, "Name": "Agnieszka Jastrzębska", "Affiliation": "The Faculty of Mathematics and Information Science, The Warsaw University of Technology, Warsaw, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Department of Electrical and Computer Engineering, The University of Alberta, Edmonton, AB T6G 2R3, Canada;The Systems Research Institute, Polish Academy of Sciences, Warsaw, Poland;The Faculty of Engineering and Natural Sciences, Department of Computer Engineering, Istinye University, Sariyer/Istanbul, Turkiye"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Faculty of Applied Information Technology, The University of Information Technology and Management, Rzeszów, Poland"}], "References": [{"Title": "ROCKET: exceptionally fast and accurate time series classification using random convolutional kernels", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "1454", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "InceptionTime: Finding AlexNet for time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "6", "Page": "1936", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Time series classification based on multi-feature dictionary representation and ensemble learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114162", "JournalTitle": "Expert Systems with Applications"}, {"Title": "The great multivariate time series classification bake off: a review and experimental evaluation of recent algorithmic advances", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "2", "Page": "401", "JournalTitle": "Data Mining and Knowledge Discovery"}]}, {"ArticleId": 111852739, "Title": "2D/2D {001}TiO2-/Ti3C2T composite with ultra-high sensing performance for NO2 at room temperature", "Abstract": "The remarkable electrical conductivity , abundance of surface functional groups, and large specific surface area make 2D MXenes-based heterostructures highly promising for room temperature gas sensing applications. However, these MXenes-based heterojunctions composed of different materials may suffer from issues, such as crystal incompatibility and bandgap mismatch. Herein, the {001}TiO <sub>2- x </sub>/Ti<sub>3</sub>C<sub>2</sub>T<sub> x </sub> composite was prepared in situ by hydrothermal process and subsequent annealing. The {001} facets of TiO<sub>2</sub> and oxygen vacancies that provide a heightened exposure of active sites in the composite and more gas diffusion channels, as well as the formation of Schottky barriers between {001}TiO <sub>2</sub> nanosheets and Ti <sub>3</sub>C<sub>2</sub>T<sub> x </sub>, can synergistically enhance the gas performance. The experimental results show that the {001}TiO<sub>2- x </sub>/Ti<sub>3</sub>C<sub>2</sub>T<sub> x </sub> composite exhibits 1.9 and 27.5 times higher than the {001}TiO<sub>2</sub>/Ti<sub>3</sub>C<sub>2</sub>T<sub> x </sub> composite and pristine Ti<sub>3</sub>C<sub>2</sub>T<sub> x </sub> to 10 ppm NO<sub>2</sub> at room temperature, respectively. Moreover, the {001}TiO<sub>2- x </sub>/Ti<sub>3</sub>C<sub>2</sub>T<sub> x </sub> composite presents the great linear response (R<sup>2</sup> = 0.99509), selectivity, repeatability and long-term stability to NO<sub>2</sub>. Density functional theory (DFT) calculations reveal the strong interactions between the {001}TiO<sub>2- x </sub>/Ti<sub>3</sub>C<sub>2</sub>T<sub> x </sub> and NO<sub>2</sub>, indicating excellent sensitivity and selectivity of the composite material towards NO<sub>2</sub>. This work provides an effective way to construct a series of oxygen-defective Ti<sub>3</sub>C<sub>2</sub>T<sub> x </sub>-based composites with exposed high-energy facets for enhanced gas sensing performances.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135258", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Theory and Technology of Intelligent Agriculture Machinery and Equipment, Jiangsu University, Zhenjiang 212013, China;School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "SEU-FEI Nano-Pico Center, Key Laboratory of MEMS of Ministry of Education, School of Electrical Science and Engineering, Southeast University, Nanjing 210096, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SEU-FEI Nano-Pico Center, Key Laboratory of MEMS of Ministry of Education, School of Electrical Science and Engineering, Southeast University, Nanjing 210096, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Wang", "Affiliation": "School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Theory and Technology of Intelligent Agriculture Machinery and Equipment, Jiangsu University, Zhenjiang 212013, China;School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China;Corresponding author at: Key Laboratory for Theory and Technology of Intelligent Agriculture Machinery and Equipment, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Jiangsu University, Zhenjiang 212013, China;Corresponding author"}], "References": [{"Title": "W18O49/Ti3C2Tx Mxene nanocomposites for highly sensitive acetone gas sensor with low detection limit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127274", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Flexible resistive NO2 gas sensor of three-dimensional crumpled MXene Ti3C2Tx/ZnO spheres for room temperature application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "326", "Issue": "", "Page": "128828", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "MXene/Co3O4 composite based formaldehyde sensor driven by ZnO/MXene nanowire arrays piezoelectric nanogenerator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "339", "Issue": "", "Page": "129923", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly sensitive and selective NO2 sensor of alkalized V2CT MXene driven by interlayer swelling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130150", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Room-temperature gas sensors based on three-dimensional Co3O4/Al2O3@Ti3C2T MXene nanocomposite for highly sensitive NO detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "368", "Issue": "", "Page": "132206", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Temperature-driven n- to p-type transition of a chemiresistive NiO/CdS-CdO NO2 gas sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "398", "Issue": "", "Page": "134755", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 111852820, "Title": "EduLGCL: Local-global contrastive learning model for education recommendation", "Abstract": "Contrastive learning has been used in recommendations to learn user and item representations from sparse and long-tail user-item interaction histories. Recommendations applied in education have several key challenges. Firstly, the frequency of student-course interactions is lower, resulting in limited data. Secondly, existing models rely on multimodal data for multi-objective recommendations and they limit the model’s generalizability . Lastly, it is important to consider the impact of static features on different students. Aiming at addressing these challenges, a novel course recommendation system based on Local-Global Contrastive Learning called as EduLGCL is proposed, in which the mutual information between course features and student features is computed to select course features. After expanding the connections, the relationship between students and courses is described from different perspectives. Multiple graph convolutional operations are performed on the interaction history graph which can connect students and courses and represent student clusters. Through the feature extraction layer, the final student representation is obtained. Extensive experiments were conducted on three public datasets and a course dataset collected from a real-world environment to validate the performance of the EduLGCL. The results demonstrate that EduLGCL outperforms baseline methods , particularly in handling user cold-start problems. Our code is available at https://github.com/mintZYJ/EduLGCL .", "Keywords": "", "DOI": "10.1016/j.knosys.2023.111357", "PubYear": 2024, "Volume": "286", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, GuangZhou, 510642, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, GuangZhou, 510642, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, GuangZhou, 510642, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, GuangZhou, 510642, China"}, {"AuthorId": 5, "Name": "<PERSON>you Han", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, GuangZhou, 510642, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Wang", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, GuangZhou, 510642, China;Key Laboratory of Smart Agriculture of Guangzhou, South China Agricultural University, Guangzhou, 510642, China;Corresponding author at: College of Mathematics and Informatics, South China Agricultural University, GuangZhou, 510642, China"}], "References": [{"Title": "A learning path recommendation model based on a multidimensional knowledge graph framework for e-learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105618", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "SocialLGN: Light graph convolution network for social recommendation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "595", "JournalTitle": "Information Sciences"}, {"Title": "Graph convolution machine for context-aware recommender system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "6", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Effects of artificial Intelligence–Enabled personalized recommendations on learners’ learning engagement, motivation, and outcomes in a flipped classroom", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "194", "Issue": "", "Page": "104684", "JournalTitle": "Computers & Education"}, {"Title": "Multi-Head multimodal deep interest recommendation network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "276", "Issue": "", "Page": "110689", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "FGCR: Fused graph context-aware recommender system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "277", "Issue": "", "Page": "110806", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 111852865, "Title": "Topic prevalence and trends of Metaverse in healthcare: A bibliometric analysis", "Abstract": "Metaverse technology is an advanced form of virtual reality and augmented technologies. It merges the digital world with the real world, thus benefitting healthcare services. Medical informatics is promising in the metaverse. Despite the increasing adoption of the metaverse in commercial applications, a considerable research gap remains in the academic domain, which hinders the comprehensive delineation of research prospects for the metaverse in healthcare. This study employs text-mining methods to investigate the prevalence and trends of the metaverse in healthcare; in particular, more than 34,000 academic articles and news reports are analyzed. Subsequently, the topic prevalence, similarity, and correlation are measured using topic-modeling methods. Based on bibliometric analysis, this study proposes a theoretical framework from the perspectives of knowledge, socialization, digitization, and intelligence. This study provides insights into its application in healthcare via an extensive literature review. The key to promoting the metaverse in healthcare is to perform technological upgrades in computer science, telecommunications, healthcare services, and computational biology. Digitization, virtualization, and hyperconnectivity technologies are crucial in advancing healthcare systems. Realizing their full potential necessitates collective support and concerted effort toward the transformation of relevant service providers, the establishment of a digital economy value system, and the reshaping of social governance and health concepts. The results elucidate the current state of research and offer guidance for the advancement of the metaverse in healthcare.", "Keywords": "Metaverse ; Health informatics ; Text mining ; Bibliometric analysis", "DOI": "10.1016/j.dsm.2023.12.003", "PubYear": 2024, "Volume": "7", "Issue": "2", "JournalId": 84310, "JournalTitle": "Journal of Information Technology and Data Management", "ISSN": "2666-7649", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management Science and Engineering, School of Economics and Management, Tsinghua University, Beijing, 100083, China"}, {"AuthorId": 2, "Name": "Donghua Chen", "Affiliation": "Department of Artificial Intelligence, School of Information Technology and Management, University of International Business and Economics, Beijing, 100029, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, Beijing Jiaotong University, Beijing, 100044, China"}], "References": [{"Title": "COVID-19 Pandemic: Shifting Digital Transformation to a High-Speed Gear", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "4", "Page": "260", "JournalTitle": "Information Systems Management"}, {"Title": "Development of metaverse for intelligent healthcare", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "11", "Page": "922", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Metaverse and Personal Healthcare", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "189", "JournalTitle": "Procedia Computer Science"}, {"Title": "Exploring the association between the Proteus effect and intention to play massive multiplayer online role-playing games (MMORPGs)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "34", "Issue": "1", "Page": "58", "JournalTitle": "Internet Research"}]}, {"ArticleId": 111852876, "Title": "Event-triggered fuzzy controller co-design for networked systems with denial-of-service attacks and its applications", "Abstract": "This manuscript focuses on a controller co-design problem of networked interval type-2 (IT2) fuzzy systems with denial-of-service (DoS) attacks via event-triggered mechanism. A L<PERSON><PERSON>nov-<PERSON>ski<PERSON> functional with the sampling property is established to execute stability analysis. New sufficient conditions are acquired for the existence of event-triggered fuzzy controller , and relaxed results are realized. The switched closed-loop system is guaranteed to be exponential stable, and an H ∞ performance is reached. At last, the presented control schemes are used in two engineering systems, which demonstrate the efficiency of the developed control strategy.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.123072", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Control Engineering, Yantai University, Yantai, Shandong 264005, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Sciences, Yantai University, Yantai, Shandong 264005, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Harbin Institute of Technology, Weihai, Shandong 264209, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Jiangsu University, Zhenjiang, Jiangsu 212013, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Science, Shandong Technology and Business University, Yantai, Shandong 264005, China;Corresponding author"}], "References": [{"Title": "LMIs-based stability analysis and fuzzy-logic controller design for networked systems with sector nonlinearities: Application in tunnel diode circuit", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116627", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Observer-based asynchronous event-triggered control for interval type-2 fuzzy systems with cyber-attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "606", "Issue": "", "Page": "805", "JournalTitle": "Information Sciences"}, {"Title": "Event-based security tracking control for networked control systems against stochastic cyber-attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "612", "Issue": "", "Page": "306", "JournalTitle": "Information Sciences"}, {"Title": "Model-based hybrid dynamic event-triggered control for systems subject to DoS attacks: A hybrid system approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "613", "Issue": "", "Page": "268", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 111853108, "Title": "Atomically dispersed Pt on MOF-derived In2O3 for chemiresistive formaldehyde gas sensing", "Abstract": "Single-atom catalysts (SACs) supported on inorganic materials have been confirmed as effective active sites in catalytic reaction . In order to develop gas sensing material with high performance, we reported an atomically dispersed Pt on MOF-derived In <sub>2</sub>O<sub>3</sub> via N doped graphene (NG) sacrificial templating route for the first time. This synthesis strategy, which combines metal-organic frameworks (MOF) precursor and NG template, not only greatly regulates the physicochemical properties of In <sub>2</sub>O<sub>3</sub>, but also makes the single-atom Pt be anchored on In<sub>2</sub>O<sub>3</sub> . Herein, the effect of Pt and NG was discussed based on various characterization methods. Gas sensing tests revealed that the highly dispersed Pt helps to improve the HCHO sensing performance. The Pt SACs inclusion increases the specific surface area, the contents of oxygen vacancies and adsorbed oxygen species of In <sub>2</sub>O<sub>3</sub>, thus increasing the active sites on the surface. These enhancements result in high response (750.4 to 100 ppm), good selectivity, rapid response (2 s to 100 ppm) and low theoretical limit of detection (8.4 ppb) of Pt<sub>1</sub>-In<sub>2</sub>O<sub>3</sub> based sensor to HCHO. In a word, atomically dispersed Pt loaded MOF-derived In<sub>2</sub>O<sub>3</sub> is an efficient sensing material for selective detection of HCHO.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135260", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, International Center of Future Science, Jilin University, 2699 Qianjin Street, Changchun 130012, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, International Center of Future Science, Jilin University, 2699 Qianjin Street, Changchun 130012, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, International Center of Future Science, Jilin University, 2699 Qianjin Street, Changchun 130012, China"}, {"AuthorId": 4, "Name": "Wenjiang Han", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, International Center of Future Science, Jilin University, 2699 Qianjin Street, Changchun 130012, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, International Center of Future Science, Jilin University, 2699 Qianjin Street, Changchun 130012, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "High-Tech Institute of Xi’an, Xi’an 710025, Shaanxi Province, China"}, {"AuthorId": 7, "Name": "Changhua Hu", "Affiliation": "High-Tech Institute of Xi’an, Xi’an 710025, Shaanxi Province, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, International Center of Future Science, Jilin University, 2699 Qianjin Street, Changchun 130012, China"}], "References": [{"Title": "Sn doping effect on NiO hollow nanofibers based gas sensors about the humidity dependence for triethylamine detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "340", "Issue": "", "Page": "129971", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly selective triethylamine sensing based on SnO/SnO2 nanocomposite synthesized by one-step solvothermal process and sintering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "342", "Issue": "", "Page": "130018", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Homojunction between cubic/hexagonal CdS nanocrystal for high and fast response to n-propanol", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "369", "Issue": "", "Page": "132281", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Ce doped SnO/SnO2 heterojunctions for highly formaldehyde gas sensing at low temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON> wang", "PubYear": 2022, "Volume": "373", "Issue": "", "Page": "132640", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Improved ppb-level NO2 conductometric sensor induced by trace Au on SnO2 nanosheet", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "379", "Issue": "", "Page": "133237", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Fabrication and modeling of laser ablated NiO nanoparticles decorated SnO2 based formaldehyde sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "387", "Issue": "", "Page": "133824", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 111853140, "Title": "Efficient multi-branch dynamic fusion network for super-resolution of industrial component image", "Abstract": "This work aims to promote the application of a high-performance super-resolution (SR) method in industry. Considering the lack of industrial datasets to evaluate performance, an industrial image SR dataset called WCI110 is first established, comprising 110 typical welding component images with 2040 × 1524 pixels. Subsequently, a parallel fusion structure of CNN and Transformer (FPFCT) is designed to achieve a high-quality reconstruction of component images. This model mainly contains the flexible parallel fusion (FPF) block and dynamic edge attention (DEA) network. The irreparable information loss caused by the inheritable sampling imperfection of CNN and Transformer can be effectively avoided by the parallel fusion structure in FPF block, while the contour features of multi-scale targets can be dynamically enhanced by DEA modules. The results show that the performance of FPFCT is higher than most advanced SR methods based on CNN, Transformer, or hybrid of CNN and Transformer. The images reconstructed by FPFCT are shown to help find and locate defects more effectively than other methods because its reconstructed target is closer to ground truth in terms of contour sharpness and size. Moreover, FPFCT achieves a remarkable advance in model parameters and time consumption, with a drop of more than 37.5 % in model parameters compared to the state-of-the-art SwinIR, and a reduction of more than 48.9 % in single-image delay time during reconstruction. The high efficiency of FPFCT makes it a promising image preprocessing tool for industrial images.", "Keywords": "", "DOI": "10.1016/j.displa.2023.102633", "PubYear": 2024, "Volume": "82", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China"}, {"AuthorId": 2, "Name": "Mingsong Chen", "Affiliation": "Lightweight Alloy Research Institute of Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China;Corresponding authors at: State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China (<PERSON><PERSON>, <PERSON><PERSON>)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China;Corresponding authors at: State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China (<PERSON><PERSON>, <PERSON><PERSON>)"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China"}, {"AuthorId": 5, "Name": "Chizhou Zhang", "Affiliation": "Lightweight Alloy Research Institute of Central South University, Changsha 410083, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lightweight Alloy Research Institute of Central South University, Changsha 410083, China"}, {"AuthorId": 10, "Name": "Weidong Zeng", "Affiliation": "School of Mechanical and Electrical Engineering, Central South University, Changsha 410083, China;State Key Laboratory of Precision Manufacturing for Extreme Service Performance, Changsha 410083, China"}], "References": [{"Title": "(SARN)spatial-wise attention residual network for image super-resolution", "Authors": "<PERSON><PERSON> Shi; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "1569", "JournalTitle": "The Visual Computer"}, {"Title": "Dual-path attention network for single image super-resolution", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114450", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Residual deep attention mechanism and adaptive reconstruction network for single image super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "5", "Page": "5197", "JournalTitle": "Applied Intelligence"}, {"Title": "Efficient surface defect detection using self-supervised learning strategy and segmentation network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101566", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Multi-level U-net network for image super-resolution reconstruction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102192", "JournalTitle": "Displays"}, {"Title": "Deep Image Deblurring: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "9", "Page": "2103", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Image super-resolution: A comprehensive review, recent trends, challenges and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "230", "JournalTitle": "Information Fusion"}, {"Title": "A hybrid of transformer and CNN for efficient single image super-resolution via multi-level distillation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "", "Page": "102352", "JournalTitle": "Displays"}, {"Title": "A Comprehensive Survey and Taxonomy on Single Image Dehazing Based on Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "13s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Defect-aware transformer network for intelligent visual surface defect detection", "Authors": "Hongbing Shang; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101882", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A new convolutional dual-channel Transformer network with time window concatenation for remaining useful life prediction of rolling bearings", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101966", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Two-branch crisscross network for realistic and accurate image super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "102549", "JournalTitle": "Displays"}, {"Title": "Frequency-guidance Collaborative Triple-branch Network for single image dehazing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "102577", "JournalTitle": "Displays"}]}, {"ArticleId": 111853535, "Title": "A turn-on fluorescence molecularly imprinted sensor based on covalent organic framework for specific and visual detection of α-dicarbonyl compounds", "Abstract": "In this paper, a turn-on fluorescence sensor (COF<sub>TPDD</sub> @ILMIPs) was prepared for the detection and removal of the α–dicarbonyl compound 3-deoxyglucosone (3-DG) in milk. The sensor uses ultra-high quantum yield green fluorescent covalent organic frameworks (COFs) as the fluorescence center, and introduces ionic liquids to improve its fluorescence performance and mass transfer rate, and the selectivity and anti-interference ability of the sensor were improved by molecular imprinted technique. In addition, the sensor was combined with a smartphone sensing platform to achieve real-time quantitative detection of 3-DG in the field. The quantitative detection of 3-DG in a good linear range (0.5–100 μg/L) was observed, and the low limit of detection was 0.31 μg/L. The spiked recoveries of 3-DG in milk samples ranged from 81.96–117.39 %, which provided an effective strategy for the accurate and rapid detection of hazardous substances in milk.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135253", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> He", "Affiliation": "Key Laboratory of Geriatric Nutrition and Health (Beijing Technology and Business University), Ministry of Education, 11 Fucheng Road, Beijing 100048, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Geriatric Nutrition and Health (Beijing Technology and Business University), Ministry of Education, 11 Fucheng Road, Beijing 100048, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Geriatric Nutrition and Health (Beijing Technology and Business University), Ministry of Education, 11 Fucheng Road, Beijing 100048, China;Corresponding author"}, {"AuthorId": 4, "Name": "Baoguo Sun", "Affiliation": "Key Laboratory of Geriatric Nutrition and Health (Beijing Technology and Business University), Ministry of Education, 11 Fucheng Road, Beijing 100048, China"}], "References": [{"Title": "Polyanionic amphiphilic polymer based supramolecular dye-host assembly: Highly selective turn–on probe for protamine sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "371", "Issue": "", "Page": "132582", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Construction of an aminal-linked covalent organic framework-based electrochemiluminescent sensor for enantioselective sensing phenylalanine", "Authors": "<PERSON><PERSON> Song; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "373", "Issue": "", "Page": "132751", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Smartphone flashlight-triggered covalent organic framework nanozyme activity: A universal scheme for visual point-of-care testing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "381", "Issue": "", "Page": "133422", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Paper-based fluorometric sensing of malachite green using synergistic recognition of aptamer-molecularly imprinted polymers and luminescent metal–organic frameworks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "384", "Issue": "", "Page": "133665", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Recognition and nanocatalytic amplification of binary MXene carbon dot surface molecularly imprinted nanoprobe for determination of thiamethoxam by molecular spectroscopy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "390", "Issue": "", "Page": "134032", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A high affinity borate ester bond molecularly imprinted sensor based on multistage amplification of UCNPs-NH2 electrochemiluminescence with bifunctional synergistic accelerator CeO2 @N-MC for ultrasensitive detection of ribavirin", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "393", "Issue": "", "Page": "134230", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 111853639, "Title": "Prediksi Jumlah Produksi Perakitan Komponen Menggunakan ANFIS Yang Dioptimasi Dengan Algoritma K-Means", "Abstract": "<p>Pada industri perakitan jumlah produk yang dihasilkan sangat penting untuk memenuhi jumlah permintaan dari pelanggan sehingga perlu dibuatkan rencana produksi yang tepat. Banyaknya faktor yang mempengaruhi menjadi kendala untuk memperkirakan hasil produksi. Salah satu metode prediksi yang banyak digunakan pada kondisi dengan banyak faktor yang mempengaruhi adalah Adaptive Neuro-Fuzzy Inference System (ANFIS). Akan tetapi kelemahan ANFIS ketika digunakan pada basis data dengan kepadatan yang jarang akan sulit dalam pembentukan aturan dasar fuzzy. Untuk mengatasinya pada penelitian ini dilakukan optimasi dengan mengelompokan rentang nilai label derajat keanggotaan pada variabel input maupun output menggunakan pendekatan algoritma K-means sebelum dataset diinput ke jaringan. Berdasarkan metode Average Forecasting Error Rate (AFER) didapat hasil prediksi metode ANFIS dengan optimasi K-means memiliki persentase error 0,000018%, sedangkan hasil prediksi ANFIS dengan penentuan derajat keanggotaan pada label input dan output fuzzy dilakukan sembarang menghasilkan persentase error 0,000023%. Dari hasil penelitian disimpulkan bahwa penggunaan algoritma K-means untuk pengelompokan basis data jarang untuk penentuan derajat keanggotaan ANFIS bisa diterapkan dan menghasilkan tingkat error lebih rendah dibanding pengelompokan secara sembarang.</p>", "Keywords": "ANFIS; K-means; Fuzzy; Production; Prediction", "DOI": "10.31154/cogito.v9i2.513.252-265", "PubYear": 2023, "Volume": "9", "Issue": "2", "JournalId": 54676, "JournalTitle": "CogITo Smart Journal", "ISSN": "2541-2221", "EISSN": "2477-8079", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Budi luhur"}], "References": []}, {"ArticleId": 111853644, "Title": "Research on fabricating Cu/stainless steel composite thin strips by two-pass cold roll-bonding with intermediate annealing", "Abstract": "<p>Roll bonding is feasible to fabricate T2 copper (Cu)/ stainless steel (SS) composite thin trips, which have great potential for use in micromanufacturing, robotics, aerospace, and other applications. However, the effective bonding of Cu and SS could be hindered by limited diffusion of the elements and uncoordinated deformation of the metal matrices. In this study, Cu/SS composite strips with 0.24 mm thickness were prepared by the two-pass cold rolling process with intermediate annealing at 400 ~ 1000℃. The influences of the intermediate annealing process on the tensile and peeling strength were investigated. Finite element simulation and microstructure evaluation were carried out to analyze the deformation behaviors and bonding mechanisms of the strips. The results indicate that the deformation coordination in the second-pass rolling and the bonding strength were improved by appropriate intermediate annealing processes. The difference in the deformation resistance between Cu and SS became the lowest by intermediate annealing at 1000℃, while the deformation of Cu and SS was severely uncoordinated by annealing at 600℃. The peel strength and elongation of the strips annealed at 1000℃ were 11.65 ± 0.7N/mm and 34.8 ± 1.3% after the second-pass rolling, which were 79.23% and 6.64 times higher than the strips manufactured without intermediate annealing, respectively. In this work, Cu/SS thin strips with high bonding strength and ductility were successfully manufactured by appropriate intermediate annealing process, and the bonding mechanisms were systematically discussed.</p>", "Keywords": "Cu/SS composite thin strips; Intermediate annealing; Deformation behaviors; Finite element simulation; Bonding mechanisms", "DOI": "10.1007/s00170-023-12779-y", "PubYear": 2024, "Volume": "130", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Taiyuan University of Technology, Taiyuan, People’s Republic of China; Engineering Research Center of Advanced Metal Composites Forming Technology and Equipment, Ministry of Education, Taiyuan University of Technology, Taiyuan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Taiyuan University of Technology, Taiyuan, People’s Republic of China; Engineering Research Center of Advanced Metal Composites Forming Technology and Equipment, Ministry of Education, Taiyuan University of Technology, Taiyuan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Taiyuan University of Technology, Taiyuan, People’s Republic of China; Engineering Research Center of Advanced Metal Composites Forming Technology and Equipment, Ministry of Education, Taiyuan University of Technology, Taiyuan, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Taiyuan University of Technology, Taiyuan, People’s Republic of China; Engineering Research Center of Advanced Metal Composites Forming Technology and Equipment, Ministry of Education, Taiyuan University of Technology, Taiyuan, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Taiyuan University of Technology, Taiyuan, People’s Republic of China; Engineering Research Center of Advanced Metal Composites Forming Technology and Equipment, Ministry of Education, Taiyuan University of Technology, Taiyuan, People’s Republic of China; National Key Laboratory of Metal Forming Technology and Heavy Equipment, Taiyuan, People’s Republic of China; Corresponding author."}], "References": []}, {"ArticleId": 111853832, "Title": "Highly sensitive fiber grating hydrogen sensor based on hydrogen-doped Pt/WO3", "Abstract": "Hydrogen sensors are key to the emerging clean hydrogen economies and fiber-optic hydrogen sensors play a unique role owing to their inherent safety and high sensitivity. Currently Pt/WO <sub>3</sub> has been employed as the mainstream hydrogen-sensitive material in high performance hydrogen sensors. Here we develop an ultrasensitive fiber-optic hydrogen sensor with fast response by coating pretreated Pt/WO<sub>3</sub> nanomaterial on fiber Bragg grating. We observe a great enhancement in sensitivity by the hydrogen-doping of the Pt/WO <sub>3</sub> nanomaterials. The generation of H<sub>x</sub>WO<sub>3</sub> composite material is confirmed by XRD, FTIR and Raman analysis. Compared with pure Pt/WO <sub>3</sub>, a 184-fold improvement in sensitivity is achieved by hydrogen doping, with a fast response of 25 s. An impressive limit of detection (LOD) of 30 ppm is demonstrated by employing both the narrowband weak FBGs and the hydrogen-doped Pt/WO<sub>3</sub>. The immunity to ambient temperature fluctuation is demonstrated by self-calibration through detecting the wavelength difference between a pair of FBGs. Good specificity is also demonstrated. This technology shows great potential in high spatial-resolution quasi-distributed hydrogen sensing.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135250", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optical Fiber Sensing and Communications (Ministry of Education of China), University of Electronic Science and Technology of China, Chengdu 611731 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optical Fiber Sensing and Communications (Ministry of Education of China), University of Electronic Science and Technology of China, Chengdu 611731 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optical Fiber Sensing and Communications (Ministry of Education of China), University of Electronic Science and Technology of China, Chengdu 611731 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Photonics & Optical Communications, School of Electrical Engineering & Telecommunications, University of New South Wales, Sydney, NSW 2052, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optical Fiber Sensing and Communications (Ministry of Education of China), University of Electronic Science and Technology of China, Chengdu 611731 China"}, {"AuthorId": 6, "Name": "Yuan Gong", "Affiliation": "Key Laboratory of Optical Fiber Sensing and Communications (Ministry of Education of China), University of Electronic Science and Technology of China, Chengdu 611731 China;Corresponding author"}], "References": []}, {"ArticleId": *********, "Title": "Robust superpixel-based fuzzy possibilistic clustering method incorporating local information for image segmentation", "Abstract": "<p>In recent years, several superpixel-segmentation methods have been developed to efficiently segment noisy images. However, these methods still face challenges such as high computational complexity and poor adaptability. Therefore, this paper develops a novel superpixel-based robust segmentation model including two modules: superpixel generation and superpixel-based image segmentation. In the superpixel generation module, a fuzzy factor containing local spatial information of pixels is introduced into fuzzy possibilistic clustering algorithm with local search. In the superpixel-based segmentation module, a superpixel-based fuzzy C-means algorithm with local spatial information of superpixels is proposed, which nonlinearly combines the membership of superpixels with the membership of their neighboring superpixels. The experimental results demonstrate that the proposed method outperforms existing state-of-the-art segmentation algorithms in terms of evaluation indexes and visual effects.</p>", "Keywords": "Image segmentation; Superpixel; Fuzzy possibilistic C-means; Local information; Robustness", "DOI": "10.1007/s00371-023-03218-w", "PubYear": 2024, "Volume": "40", "Issue": "11", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xi’an University of Posts and Telecommunications, Xi’an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xi’an University of Posts and Telecommunications, Xi’an, People’s Republic of China; Corresponding author."}], "References": [{"Title": "Color-Based Image Segmentation by Means of a Robust Intuitionistic Fuzzy C-means Algorithm", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "3", "Page": "901", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Robust fuzzy c-means clustering algorithm with adaptive spatial & intensity constraint and membership linking for noise image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106318", "JournalTitle": "Applied Soft Computing"}, {"Title": "Efficient kernel fuzzy clustering via random Fourier superpixel and graph prior for color image segmentation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105335", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Fuzzy Superpixel-based Image Segmentation", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109045", "JournalTitle": "Pattern Recognition"}, {"Title": "Color image segmentation based on improved sine cosine optimization algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "23", "Page": "13193", "JournalTitle": "Soft Computing"}, {"Title": "Adaptive hypergraph superpixels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "", "Page": "102369", "JournalTitle": "Displays"}]}, {"ArticleId": *********, "Title": "Numerical analysis of the dynamic characteristics of a fractional-order viscoelastic beam with fixed supports at both ends", "Abstract": "<p>In this paper, we present a generalized model of a viscoelastic beam, which takes into account the influence of axial forces and incorporates a fractional constitutive relationship. In addition, we propose a novel numerical calculation method for analyzing fractional-order viscoelastic beams. This method takes the transverse displacement and bending moment of the beam as state variables, transforms the beam model into a discrete state space equation through the application of the central difference method, and utilizes an improved precise integration algorithm to solve the equation. To evaluate the performance of the method, the responses of the beam under two types of excitations, namely uniformly distributed transverse load and the motion of the support at both ends, are calculated under fixed hinge conditions. The results demonstrate that the present method has excellent accuracy and convergence, and also reveal some nonlinear phenomena of the system.</p>", "Keywords": "", "DOI": "10.1177/1045389X231213133", "PubYear": 2024, "Volume": "35", "Issue": "4", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "Hongyu Li", "Affiliation": "College of Mechanical and Electronic Engineering, Nanjing Forestry University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electronic Engineering, Nanjing Forestry University, Nanjing, China"}, {"AuthorId": 3, "Name": "Yan<PERSON> Chen", "Affiliation": "College of Mechanical and Electronic Engineering, Nanjing Forestry University, Nanjing, China;College of Intelligent Equipment Engineering, Wuxi Taihu University, Wuxi, China"}, {"AuthorId": 4, "Name": "Yongpeng Tai", "Affiliation": "College of Automobile and Traffic Engineering, Nanjing Forestry University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Automobile and Traffic Engineering, Nanjing Forestry University, Nanjing, China"}], "References": [{"Title": "New control strategy for suppressing the local vibration of sandwich beams based on the wave propagation method", "Authors": "<PERSON>; <PERSON><PERSON>; Yong<PERSON>g Tai", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "231", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}]}, {"ArticleId": 111854205, "Title": "Optimal management of full train load services in the shunting yard: A comprehensive study on Shunt-In Shunt-Out policies", "Abstract": "A key aim of the European Union is to double freight rail traffic by 2050 in order to reduce pollution emissions and alleviate congestion by shifting traffic from roads to rail networks. To accomplish this objective, it is crucial to minimize emissions and high costs associated with shunting yard operations while maintaining an acceptable level of service. This research paper introduces a new MINLP model that optimizes the shunt-in and shunt-out (SISO) operations of wagons in a shunting yard that handles full train load services. Additionally, an efficient Multi-Objective Dijkstra Algorithm (MDA) is proposed to handle simultaneous shunt-out operations in a multi-train scenario. The MINLP model takes a mesoscopic approach, aiming to minimize the number of SISO operations while satisfying strategic and tactical objectives such as wagon fleet size, operational costs, and shunting locomotive emissions. Several versions of the mathematical model are described, each employing different Shunt-In (SI) policies with varying criteria for wagon selection and strong goal orientation. The Multi-Objective Dijkstra Algorithm determines the Multi-Objective Shortest Path between mandatory shunts, considering both the time required for shunting and clustering costs. It provides information on the clusters of wagons that need to be shunted out. To assess the effectiveness of the MINLP model, real train timetables for freight trains in the Bettembourg Eurohub Sud Terminal (Luxembourg) are used, and various KPIs related to tactical and strategic objectives are evaluated. Furthermore, the performance of the Multi-Objective Dijkstra Algorithm is compared to the Shunt-Out sub-model, considering average computation time and solution quality in relation to the MINLP model. The computational results demonstrate that the criteria for wagon selection have a significant impact on the analyzed KPIs.", "Keywords": "Railway operations ; Freight rail operation ; Freight trains ; Shunting operations ; Shunting emissions ; Rolling stock circulation", "DOI": "10.1016/j.cie.2023.109865", "PubYear": 2024, "Volume": "188", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil, Computer Science and Aeronautical Technologies Engineering, Roma Tre University, Rome 00154, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Science, Technology and Medicine (FSTM), University of Luxembourg, Esch-Sur-Alzette, L-4364, Luxembourg"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Civil, Computer Science and Aeronautical Technologies Engineering, Roma Tre University, Rome 00154, Italy"}, {"AuthorId": 4, "Name": "Francesco <PERSON>", "Affiliation": "Faculty of Science, Technology and Medicine (FSTM), University of Luxembourg, Esch-Sur-Alzette, L-4364, Luxembourg"}, {"AuthorId": 5, "Name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science, Technology and Medicine (FSTM), University of Luxembourg, Esch-Sur-Alzette, L-4364, Luxembourg"}], "References": []}, {"ArticleId": 111854314, "Title": "Challenges in Early Verification and Validation of System Requirements", "Abstract": "<p>Today, requirements engineering (RE) is a key process in the development of complex systems‐(ISO/IEC/IEEE 2015). Requirements containing quality issues such as ambiguity or inconsistency can lead to late error detection in systems design, resulting in high project cost overruns. This paper presents challenges for early system requirements verification and validation associated to an executable model‐based requirements engineering (eMBRE) process proposal.</p>", "Keywords": "", "DOI": "10.1002/inst.12462", "PubYear": 2023, "Volume": "26", "Issue": "4", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Empirical research on requirements quality: a systematic mapping study", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "2", "Page": "183", "JournalTitle": "Requirements Engineering"}]}, {"ArticleId": 111854320, "Title": "Challenges in Developing a Method to Support the Adoption of a Model‐Based Systems Engineering Methodology", "Abstract": "<p>To improve design performance and achieve sustainability, organisations are looking to change their systems engineering practices. A model‐based systems engineering (MBSE) methodology provides a framework for integrating, associating, orchestrating, and connecting executable and interactive models. It improves early verification and validation of system specifications and architectures, as well as communication and collaboration between project stakeholders. In this sense, MBSE has a truly systemic aspect. The variables to be considered when designing a support strategy are numerous and multidimensional. As a result, this situation can lead to contradictions in the choice of actions to be implemented, or to paradoxes that are likely to slow down the progress of the deployment project with the engineers. Currently, there is no method to support teams in charge of a methodological transformation (for example, in MBSE) to facilitate the adoption of this methodology. This article identifies the main challenges involved in developing such a methodology.</p>", "Keywords": "", "DOI": "10.1002/inst.12463", "PubYear": 2023, "Volume": "26", "Issue": "4", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111854579, "Title": "Monitoring and forecasting usability indicators: A business intelligence approach for leveraging user-centered evaluation data", "Abstract": "Monitoring performance indicators has become a main concern for most organizations today. While much attention has been paid to developing economic, bioinformatics, health, and social media dashboards, little or no attention has been devoted to monitoring and forecasting usability indicators. This can be of interest for analyzing the degree of perceived satisfaction and usability of interactive software products designed or just developed, being also useful as general strategic indicators in human-centered organizations. This paper presents an approach including main measures, Key Performance Indicators, trends, and forecasts to deal with usability information over time and produce new knowledge based on historical data. Also, an instance has been implemented, including data obtained from real software evaluations. Target users have evaluated the approach to validate its suitability, obtaining successful usability results that denote the adequacy of the approach presented.", "Keywords": "Usability ; User experience ; User-centered software development ; Human-centered organization ; Human-computer interaction ; Business intelligence", "DOI": "10.1016/j.scico.2023.103077", "PubYear": 2024, "Volume": "234", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, Universidad Autónoma de Madrid, Madrid, Spain;Corresponding author at: Tomás y Valiente 11, 28049, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Deutsches Elektronen-Synchrotron (DESY) National Research Center, Hamburg, Germany"}], "References": [{"Title": "Assessing long-term user experience on a mobile health application through an in-app embedded conversation-based questionnaire", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "106169", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Effects of learner interaction with learning dashboards on academic performance in an e-learning environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "161", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Creating design guidelines for building city dashboards from a user's perspectives", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "102429", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Enhancing decision-making in user-centered web development: a methodology for card-sorting analysis", "Authors": "<PERSON>; Alma L. <PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "6", "Page": "2099", "JournalTitle": "World Wide Web"}, {"Title": "Measurement of key performance indicators of user experience based on software requirements", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "226", "Issue": "", "Page": "102929", "JournalTitle": "Science of Computer Programming"}]}, {"ArticleId": 111854668, "Title": "A contribution-based indicator of research productivity: theoretical definition and empirical testing in the field of communication", "Abstract": "Purpose: The purpose of this article is to theoretically outline and empirically test two contribution-based indicators: (1) the scholars' annual contribution-based measurement and (2) the annual contribution modified h-index, computing six criteria: total number of papers, computed SCImago Journal Rank values, total number of authors, total number of citations of a scholar’s work, number of years since paper publication and number of annual paper citations. Design/methodology/approach: Despite widespread scholarly agreement about the relevance of research production in evaluation and recruitment processes, the proposed mechanisms for gauging publication output are still rather elementary, consequently obscuring each individual scholar’s contributions. This study utilised the Technique for Order of Preference by Similarity to Ideal Solution method, and the authors built two indicators to value author's contribution. Findings: To test both indicators, this study focussed on the most productive scholars in communication during a specific time period (2017–2020), ranking their annual research contribution and testing it against standard productivity measures (i.e. number of papers and h-index). Originality/value: This article contributes to current scientometric studies by addressing some of the limitations of aggregate-level measurements of research production, providing a much-needed understanding of scholarly productivity based on scholars' actual contribution to research. © 2023, Emerald Publishing Limited.", "Keywords": "Communication; Contribution; Productivity; Prolific scholars; Research evaluation", "DOI": "10.1108/OIR-11-2022-0634", "PubYear": 2024, "Volume": "48", "Issue": "4", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "Olga <PERSON>-Blasco", "Affiliation": "Department of Applied Economics, Universitat de València, Valencia, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Social Communication, University of Public Service, Budapest, Hungary"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Communication Studies, University Carlos III of Madrid, Madrid, Spain"}], "References": []}, {"ArticleId": 111854764, "Title": "Flexible pressure sensors based on reduced graphene oxide @ porous silicone elastomers with good resolution and wide working range", "Abstract": "Current flexible piezoresistive pressure sensors still face challenges, such as poor stability and a limited detection range, making it difficult to accurately discern small-scale pressures in the Pa or MPa range when meeting sudden pressure events. To tackle the above issues, this study presents the development of a competent sensor composed of porous conductive elastomer and laser-induced graphene electrodes. The substrate of the composite elastomer is a porous silicone elastomer obtained by the additive reaction using template methods , and the conductive filler consisting of reduced graphene oxide is synthesized through an eco-friendly approach. We demonstrated that the sensors fabricated here have the ability to precisely resolve pressures from 542 Pa to 1.5 MPa with long-term stability, as well as the detection of subtle signals, such as pulse pulsation and throat vibrations. Above all, such sensors have great potential in the application of human-machine interaction.", "Keywords": "", "DOI": "10.1016/j.sna.2023.114991", "PubYear": 2024, "Volume": "366", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Metal Matrix Composites and National Key Laboratory of Advanced Micro and Nano Manufacture Technology, Shanghai Jiao Tong University, Shanghai 200240, PR China;National Engineering Center Research for Nanotechnology, Shanghai 200241, PR China;Key Laboratory of Polymer Processing Engineering (South China University of Technology), Ministry of Education, Guangzhou 510640, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, 800 Dongchuan Road, Shanghai 200240, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, 800 Dongchuan Road, Shanghai 200240, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Sensing Materials and Chip Integration Technology of Zhejiang Province, Hangzhou Innovation Institute of Beihang University, Hangzhou, PR China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Metal Matrix Composites and National Key Laboratory of Advanced Micro and Nano Manufacture Technology, Shanghai Jiao Tong University, Shanghai 200240, PR China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Frontiers Science Center for Transformative Molecules, School of Chemistry and Chemical Engineering, Shanghai Jiao Tong University, Shanghai 200240, PR China;Corresponding authors"}], "References": [{"Title": "Flexible piezoresistive three-dimensional force sensor based on interlocked structures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "330", "Issue": "", "Page": "112857", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Piezoresistive pressure sensor based on conjugated polymer framework for pedometer and smart tactile glove applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "350", "Issue": "", "Page": "114139", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "A highly sensitive flexible pressure sensor based on laser-induced graphene and a composite dielectric structure", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "358", "Issue": "", "Page": "114460", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 111854787, "Title": "A bitwise approach on influence overload problem", "Abstract": "Increasingly developing online social networks has enabled users to send or receive information very fast. However, due to the availability of an excessive amount of data in today’s society, managing the information has become very cumbersome, which may lead to the problem of information overload. This highly eminent problem, where the existence of too much relevant information available becomes a hindrance rather than a help, may cause losses, delays, and hardships in making decisions. Thus, in this paper, by defining information overload from a different aspect, we aim to maximize the information propagation while minimizing the information overload (duplication). To do so, we theoretically present the lower and upper bounds for the information overload using a bitwise-based approach as the leverage to mitigate the computation complexities and obtain an approximation ratio of 1 − 1 e . We propose two main algorithms, B-square and C-square, and compare them with the existing algorithms. Experiments on two types of datasets, synthetic and real-world networks, verify the effectiveness and efficiency of the proposed approach in addressing the problem.", "Keywords": "", "DOI": "10.1016/j.datak.2023.102276", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Postal Innovation Research Team, Korea Postal Service Agency, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of BMSE & Industrial Engineering, Inha University, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of BMSE & Industrial Engineering, Inha University, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Software, Gachon University, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of BMSE & Industrial Engineering, Inha University, South Korea;Corresponding author"}], "References": [{"Title": "Evolutionary multiobjective optimization to target social network influentials in viral marketing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "113183", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An analytical model for information gathering and propagation in social networks using random graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "101852", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Graph threshold algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "9", "Page": "9827", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Negative influence blocking maximization with uncertain sources under the independent cascade model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "564", "Issue": "", "Page": "343", "JournalTitle": "Information Sciences"}, {"Title": "CSIP: Enhanced Link Prediction with Context of Social Influence Propagation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "", "Page": "100217", "JournalTitle": "Big Data Research"}, {"Title": "Top-k team synergy problem: Capturing team synergy based on C3", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "117", "JournalTitle": "Information Sciences"}, {"Title": "An approach to detect backbones of information diffusers among different communities of a social platform", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "140", "Issue": "", "Page": "102048", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Fighting False Information from Propagation Process: A Survey", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "10", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "An automated multi-web platform voting framework to predict misleading information proliferated during COVID-19 outbreak using ensemble method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "102103", "JournalTitle": "Data & Knowledge Engineering"}]}, {"ArticleId": 111854845, "Title": "Shape classification using a new shape descriptor and multi-view learning", "Abstract": "Shape classification is considered as a vital task in solving many computer vision problems. Different factors such as affine transformations, scaling, rotations, variation in perspective, noise and occlusion have made the shape classification problem to be a hard problem. This work investigates a new shape descriptor that extracts different features from each boundary pixel. This makes it to be more informative and discriminant in comparison with other descriptors. After feature extraction, the “bag-of-features (BoF)” model is employed to construct the final representation for each image. To enhance the functionality of the BoF model, a novel codebook generation approach is presented. The proposed approach tends to derive a more meaningful visual codebook. Consequently, the produced feature vectors can handle inter- and intra-class variations more effectively. Comprehensive experiments conducted on the various complicated shape datasets show the supremacy of our approach compared to other methods.", "Keywords": "", "DOI": "10.1016/j.displa.2023.102636", "PubYear": 2024, "Volume": "82", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Malayer University, P. O. Box: 65719-95863, Malayer, Iran;Corresponding author at: Department of Computer Engineering, Malayer University, P. B. : 65719-95863, Malayer, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of IT Engineering, Faculty of Industrial and Systems Engineering, Tarbiat Modares University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Malayer University, P. O. Box: 65719-95863, Malayer, Iran"}], "References": [{"Title": "2D Object Recognition Techniques: State-of-the-Art Work", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1147", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Scale-space multi-view bag of words for scene categorization", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "1", "Page": "1223", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Plant leaf recognition by integrating shape and texture features", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107809", "JournalTitle": "Pattern Recognition"}, {"Title": "Improving the Performance of Convolutional Neural Networks for Image Classification", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "1", "Page": "51", "JournalTitle": "Optical Memory and Neural Networks"}, {"Title": "Voxel-based three-view hybrid parallel network for 3D object classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "102076", "JournalTitle": "Displays"}, {"Title": "An enhanced and interpretable feature representation approach to support shape classification from binary images", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "348", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A Deep Learning Driven Active Framework for Segmentation of Large 3D Shape Collections", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "144", "Issue": "", "Page": "103179", "JournalTitle": "Computer-Aided Design"}, {"Title": "An efficient framework for zero-shot sketch-based image retrieval", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108528", "JournalTitle": "Pattern Recognition"}, {"Title": "A Comprehensive Survey on Two and Three-Dimensional Fourier Shape Descriptors: Biomedical Applications", "Authors": "<PERSON><PERSON>reh Valizadeh; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "7", "Page": "4643", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Self-designed hierarchical network based hand shape intelligent recognition and evaluation for Chinese zither performing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "", "Page": "102291", "JournalTitle": "Displays"}, {"Title": "Robust multi-view learning with the bounded LINEX loss", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "518", "Issue": "", "Page": "384", "JournalTitle": "Neurocomputing"}, {"Title": "Learnable graph convolutional network and feature fusion for multi-view learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "109", "JournalTitle": "Information Fusion"}, {"Title": "Robust semi-supervised multi-view graph learning with sharable and individual structure", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "109565", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 111854946, "Title": "Multi-view fuzzy C-means clustering with kernel metric and local information for color image segmentation", "Abstract": "Purpose Multi-view fuzzy clustering algorithms are not widely used in image segmentation, and many of these algorithms are lacking in robustness. The purpose of this paper is to investigate a new algorithm that can segment the image better and retain as much detailed information about the image as possible when segmenting noisy images. Design/methodology/approach The authors present a novel multi-view fuzzy c-means (FCM) clustering algorithm that includes an automatic view-weight learning mechanism. Firstly, this algorithm introduces a view-weight factor that can automatically adjust the weight of different views, thereby allowing each view to obtain the best possible weight. Secondly, the algorithm incorporates a weighted fuzzy factor, which serves to obtain local spatial information and local grayscale information to preserve image details as much as possible. Finally, in order to weaken the effects of noise and outliers in image segmentation, this algorithm employs the kernel distance measure instead of the Euclidean distance. Findings The authors added different kinds of noise to images and conducted a large number of experimental tests. The results show that the proposed algorithm performs better and is more accurate than previous multi-view fuzzy clustering algorithms in solving the problem of noisy image segmentation. Originality/value Most of the existing multi-view clustering algorithms are for multi-view datasets, and the multi-view fuzzy clustering algorithms are unable to eliminate noise points and outliers when dealing with noisy images. The algorithm proposed in this paper has stronger noise immunity and can better preserve the details of the original image.", "Keywords": "Robustness;Image segmentation;Multi-view fuzzy clustering;Automatic view-weight learning mechanism", "DOI": "10.1108/EC-08-2023-0403", "PubYear": 2024, "Volume": "41", "Issue": "1", "JournalId": 30507, "JournalTitle": "Engineering Computations", "ISSN": "0264-4401", "EISSN": "1758-7077", "Authors": [{"AuthorId": 1, "Name": "Xiumei Cai", "Affiliation": "Xi'an University of Posts and Telecommunications , Xi'an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Xi'an University of Posts and Telecommunications , Xi'an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xi'an University of Posts and Telecommunications , Xi'an, China"}], "References": [{"Title": "View-collaborative fuzzy soft subspace clustering for automatic medical image segmentation", "Authors": "<PERSON><PERSON> Zhao; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "13-14", "Page": "9523", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient krill herd algorithm for color image multilevel thresholding segmentation problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106063", "JournalTitle": "Applied Soft Computing"}, {"Title": "An objective method to evaluate exemplar‐based inpainted images quality using Jaccard index", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "17", "Page": "26199", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Collaborative feature-weighted multi-view fuzzy c-means clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "108064", "JournalTitle": "Pattern Recognition"}, {"Title": "A Survey on Multiview Clustering", "Authors": "Guo<PERSON> Chao; <PERSON><PERSON><PERSON>; Jinbo Bi", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "146", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Multi-view Spectral Clustering with Adaptive Graph Learning and Tensor <PERSON> p-norm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> Zhang", "PubYear": 2022, "Volume": "468", "Issue": "", "Page": "257", "JournalTitle": "Neurocomputing"}, {"Title": "A new robust fuzzy c-means clustering method based on adaptive elastic distance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "237", "Issue": "", "Page": "107769", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 111854955, "Title": "Dual-layer electroencephalography data during real-world table tennis", "Abstract": "Real-world settings are necessary to improve the ecological validity of neuroscience research, and electroencephalography (EEG) facilitates mobile electrocortical recordings because of its easy portability and high temporal resolution. Table tennis is a whole-body, goal-directed sport that requires constant visuomotor feedback, anticipation, strategic decision-making, object interception, and performance monitoring – making it an interesting testbed for a variety of neuroscience studies. Although traditionally plagued by artifact contamination, recent advances in signal processing and hardware approaches, such as the dual-layer approach, have allowed high fidelity EEG recordings during whole-body maneuvers. Here, we present a dual-layer EEG dataset from 25 healthy human participants playing table tennis with a human opponent and a ball machine. Our dataset includes synchronized, multivariate time series recordings from 120 scalp electrodes, 120 noise electrodes, 8 neck electromyography electrodes, and inertial measurement units on the participant, paddles, and ball machine to record hit events. We also include de-identified T1 anatomical MR images and digitized electrode locations to create subject-specific head models for source localization. In addition, we provide anonymized video recordings and Adobe Premiere project files with hit events labeled (originally used to mark successful/missed hits). Researchers could use the videos to mark their own events of interest. We formatted our dataset in the Brain Imaging Data Structure (BIDS) format to facilitate data reuse and to adhere to the scientific community&#x27;s new organization standard.", "Keywords": "Mobile Brain Body Imaging (MoBI) ; EEG ; Independent component analysis ; Sports neuroscience ; Electrocortical dynamics", "DOI": "10.1016/j.dib.2023.110024", "PubYear": 2024, "Volume": "52", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> Family Department of Biomedical Engineering, University of Florida, Gainesville, FL 32611, United States;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> Family Department of Biomedical Engineering, University of Florida, Gainesville, FL 32611, United States"}], "References": []}, {"ArticleId": 111855043, "Title": "Direction-guided and multi-scale feature screening for fetal head–pubic symphysis segmentation and angle of progression calculation", "Abstract": "Transperineal ultrasound imaging in the mid-sagittal plane can potentially enable the objective quantification of the level of fetal head descent in the birth canal by measuring the angle of progression (AoP). Specifically, the AoP is defined as the angle between a straight line drawn along the longitudinal axis of the pubic symphysis and a line drawn from the inferior edge of the pubic symphysis to the leading edge of the fetal head. However, the process of outlining contours and measuring AoP on ultrasound images is complex and tedious, often leading to misjudgments by physicians. In response to this challenge, we propose the fetal head–pubic symphysis segmentation network (FH-PSSNet) for automatic AoP measurement. The FH-PSSNet model is based on an encoder–decoder framework, incorporating a dual attention module, a multi-scale feature screening module and a direction guidance block. The encoder extracts the relevant features and learns the global feature correlation through a dual attention module, while the decoder utilizes skip connections to preserve spatial information. Additionally, a multi-scale feature screening module with attention mechanism is proposed to extract global contextual information, enhancing object localization . Within the decoder, a dual-branch structure generates both direction information and initial segmentation features. Finally, a direction guidance block corrects the initial segmentation features to refine the segmentation results. Experiments on two datasets demonstrate the effectiveness of the proposed method. Compared to existing approaches, our model showcases competitive performance. The FH-PSSNet significantly improves automatic segmentation and AoP measurement, reducing errors and aiding sonographers in clinical settings.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.123096", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Traditional Chinese Medicine Information Technology, Jinan University, Guangzhou 510632, China;College of Information Science and Technology, Jinan University, Guangzhou 510632, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Traditional Chinese Medicine Information Technology, Jinan University, Guangzhou 510632, China;College of Information Science and Technology, Jinan University, Guangzhou 510632, China"}, {"AuthorId": 3, "Name": "Yaosheng Lu", "Affiliation": "Guangdong Provincial Key Laboratory of Traditional Chinese Medicine Information Technology, Jinan University, Guangzhou 510632, China;College of Information Science and Technology, Jinan University, Guangzhou 510632, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Traditional Chinese Medicine Information Technology, Jinan University, Guangzhou 510632, China;College of Information Science and Technology, Jinan University, Guangzhou 510632, China;Corresponding author at: College of Information Science and Technology, Jinan University, Guangzhou 510632, China"}], "References": [{"Title": "The JNU-IFM dataset for segmenting pubic symphysis-fetal head", "Authors": "<PERSON><PERSON>ng Lu; <PERSON><PERSON><PERSON><PERSON>; Dengjiang Zhi", "PubYear": 2022, "Volume": "41", "Issue": "", "Page": "107904", "JournalTitle": "Data in Brief"}, {"Title": "Automatic liver tumor segmentation from CT images using hierarchical iterative superpixels and local statistical features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117347", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-organ segmentation network for abdominal CT images based on spatial attention and deformable convolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118625", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Brain tumor segmentation based on the fusion of deep semantics and edge information in multimodal MRI", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "376", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 111855082, "Title": "Regression Test Case Prioritization Using Genetic Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2024.10061366", "PubYear": 2024, "Volume": "27", "Issue": "1", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111855119, "Title": "Speech recognition method of English translation robot based on HMM algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJRIS.2024.10061378", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 10882, "JournalTitle": "International Journal of Reasoning-based Intelligent Systems", "ISSN": "1755-0556", "EISSN": "1755-0564", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "LockBit Black Ransomware On Reverse Shell: Analysis of Infection", "Abstract": "<p> This research was conducted due to the widespread occurrence of ransomware attacks, especially in Indonesia, against data that is at the endpoint and has even reached the banking sector. to estimate the likelihood of future ransomware infections. LockBit 3 ransomware aka LockBit Black is ransomware that has penetrated one of the banks in Indonesia, along with a reverse shell which is an infection method that cannot be recognized by every protection so that when combined it can penetrate all sides of protection. The method used to research the combination of ransomware and reverse shell is a hybrid analysis with a combination of static and dynamic analysis, to see every capability that can be carried out by the LockBit Black ransomware and channeled through the reverse shell. In this research, we can see the real impact of the attack and estimate protection in the future from the results of this analysis so that variant ransomware attacks from LockBit can be overcome.</p>", "Keywords": "Ransomware; LockBit Black; Reverse; Shell; Infeksi", "DOI": "10.31154/cogito.v9i2.494.228-240", "PubYear": 2023, "Volume": "9", "Issue": "2", "JournalId": 54676, "JournalTitle": "CogITo Smart Journal", "ISSN": "2541-2221", "EISSN": "2477-8079", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Matana University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Matana University"}], "References": []}, {"ArticleId": 111855203, "Title": "Fast Parallel Hypertree Decompositions in Logarithmic Recursion Depth", "Abstract": "Various classic reasoning problems with natural hypergraph representations are known to be tractable if a hypertree decomposition (HD) of low width exists. The resulting algorithms are attractive for practical use in fields like databases and constraint satisfaction. However, algorithmic use of HDs relies on the difficult task of first computing a decomposition of the hypergraph underlying a given problem instance, which is then used to guide the algorithm for this particular instance. The performance of purely sequential methods for computing HDs is inherently limited, yet the problem is, theoretically, amenable to parallelisation. In this article, we propose the first algorithm for computing hypertree decompositions that is well suited for parallelisation. The newly proposed algorithm\n \n log-\n k \n -decomp\n \n requires only a logarithmic number of recursion levels and additionally allows for highly parallelised pruning of the search space by restriction to so-called balanced separators. We provide a detailed experimental evaluation over the HyperBench benchmark and demonstrate that\n \n log-\n k \n -decomp\n \n outperforms the current state of the art significantly.", "Keywords": "", "DOI": "10.1145/3638758", "PubYear": 2024, "Volume": "49", "Issue": "1", "JournalId": 11269, "JournalTitle": "ACM Transactions on Database Systems", "ISSN": "0362-5915", "EISSN": "1557-4644", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Calabria, Rende, Italy and University of Oxford, Oxford, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "TU Wien, Wien, Austria and University of Oxford, Oxford, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Umeå University, Umeå, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TU Wien, Vienna, Austria"}], "References": [{"Title": "HyperBench", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "1", "JournalTitle": "Journal of Experimental Algorithmics"}, {"Title": "Complexity Analysis of Generalized and Fractional Hypertree Decompositions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "5", "Page": "1", "JournalTitle": "Journal of the ACM"}, {"Title": "Fast and parallel decomposition of constraint satisfaction problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "3", "Page": "284", "JournalTitle": "Constraints"}]}, {"ArticleId": 111855279, "Title": "Simultaneous atmospheric CH4, CO2 and O2 detection using fiber-optic switch (FOS)-based time-division multiplexed NIR laser long optical path absorption spectroscopy", "Abstract": "A near-infrared (NIR) distributed feedback (DFB) laser sensor based on long optical path absorption spectroscopy technique was developed for simultaneous measurement of atmospheric methane (CH <sub>4</sub>), carbon dioxide (CO<sub>2</sub>) and oxygen (O<sub>2</sub>). A fiber-optic switch (FOS) in conjunction with the time-division multiplexing (TDM) technique was adopted to enable different lasers scan over the target gas absorption lines in turn. The long optical path wavelength modulation spectroscopy (WMS) technique with second harmonic detection as well as a laser power fluctuation correction method was used to improve the sensor’s precision and accuracy. For the selected gas absorption lines, located at 6046.945 cm<sup>-1</sup>, 6361.245 cm<sup>-1</sup> and 7877.647 cm<sup>-1</sup>, the detection limit (1σ) of 0.034 parts per million (ppm) for CH<sub>4</sub>, 11.921 ppm for CO<sub>2</sub> and 0.14% for O<sub>2</sub> was achieved according to the concentration and noise level. Allan deviation analysis indicates that the 1-s measurement precision was 0.030 ppm for CH<sub>4</sub>, 11.518 ppm for CO<sub>2</sub> and 0.14% for O<sub>2</sub>, which could be improved to 6.5E-4 ppm, 0.255 ppm and 0.005% at an optimal average time of 800 s. A two-day continuous measurement of the CH<sub>4</sub>, CO<sub>2</sub>, and O<sub>2</sub> concentration from ambient air was carried out, which verifies the stability and robustness of the developed multi-gas sensor.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135256", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Biological and Agricultural Engineering, Jilin University, Changchun 130022, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Biological and Agricultural Engineering, Jilin University, Changchun 130022, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Chen", "Affiliation": "College of Biological and Agricultural Engineering, Jilin University, Changchun 130022, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 5, "Name": "Yujing Sun", "Affiliation": "College of Biological and Agricultural Engineering, Jilin University, Changchun 130022, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Biological and Agricultural Engineering, Jilin University, Changchun 130022, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Biological and Agricultural Engineering, Jilin University, Changchun 130022, PR China;Corresponding author"}], "References": []}, {"ArticleId": 111855331, "Title": "Cascaded recycling amplification mediated in situ synthesis of silver nanoclusters for the construction of sensitive and label-free electrochemical sensor", "Abstract": "A growing number of studies have shown the crucial role of microRNA (miRNA) sensing in cancer clinical diagnosis and prognosis research. In this study, a label-free and enzyme-free electrochemical sensor was presented to achieve sensitive determination of miRNA 122, which was constructed based on in situ synthesis of silver nanoclusters (AgNCs) mediated by cascaded recycling amplification. The efficient and cascaded recycling amplification was initiated by the target miRNA 122, resulting in the release of signal probe with rich cytosine bases, which would be loaded on the sensing interface for in situ synthesis of silver nanoclusters, leading to dramatically increased current for quantitative analysis. This method achieved selective and robust miRNA 122 sensing in human serum samples with a detection limit down to 27 aM. The proposed work would provide an innovative method for determining different biomarkers, holding great potential for boosting the development of clinical diagnosis and drug investigation.", "Keywords": "", "DOI": "10.1016/j.snb.2023.135262", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemistry and Materials Science, Jiangsu Normal University, Xuzhou 221116, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Materials Science, Jiangsu Normal University, Xuzhou 221116, China"}, {"AuthorId": 3, "Name": "Baoting Dou", "Affiliation": "School of Chemistry and Materials Science, Jiangsu Normal University, Xuzhou 221116, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xuzhou Central Hospital, 199 Jiefang Road, Xuzhou 221009, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xuzhou Central Hospital, 199 Jiefang Road, Xuzhou 221009, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Materials Science, Jiangsu Normal University, Xuzhou 221116, China;Corresponding authors"}], "References": []}, {"ArticleId": 111855409, "Title": "Color Offset Compensation Method of 3D Animation Scene Image Based on Color Difference Interpolation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJRIS.2024.10061377", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 10882, "JournalTitle": "International Journal of Reasoning-based Intelligent Systems", "ISSN": "1755-0556", "EISSN": "1755-0564", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111855432, "Title": "Communication optimisation of smart agriculture wireless sensor network based on improved ant colony algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJGUC.2023.10061389", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 19428, "JournalTitle": "International Journal of Grid and Utility Computing", "ISSN": "1741-847X", "EISSN": "1741-8488", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111855443, "Title": "A Modified Algorithm to Solve Minimum Spanning Tree Problem", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJRIS.2024.10061376", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 10882, "JournalTitle": "International Journal of Reasoning-based Intelligent Systems", "ISSN": "1755-0556", "EISSN": "1755-0564", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111855756, "Title": "Advances, challenges and prospects of visible fiber lasers in display technologies", "Abstract": "Especially for displays, visible-wavelength lasers are in high demand for a wide range of applications. Active research and development have been devoted to laser-based displays over the past six decades, but the technology’s commercial viability has been limited primarily by the high cost and bulk of laser sources. Owing to their reliability, cost-effectivity, and high efficiency, fiber lasers are rapidly becoming the preferred technology for displays. Advances in visible fiber laser techniques, such as nonlinear conversion, upconversion pumping, and direct pumping, are addressed in detail in this review. The potential of visible rare-earth-doped fiber lasers to generate high-power continuous-wave (CW) emission at wavelengths of red, green, blue, and yellow (RGBY) is encouraging, given the recent developments in newly designed active fibers, dielectric end-coated fiber technology, and high-brightness GaN pump diodes. Following this, constraints on the scaling of power in visible fiber lasers are analyzed and discussed. Finally, potential applications of visible fiber lasers as a light source in display technologies are discussed.", "Keywords": "", "DOI": "10.1016/j.displa.2023.102630", "PubYear": 2024, "Volume": "82", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "Wensong Li", "Affiliation": "Department of Electronic Engineering, School of Electronic Science and Engineering (National Model Microelectronics College), Xiamen University, Xiamen 361005, China;Fujian Key Laboratory of Ultrafast Laser Technology and Applications, Xiamen University, Xiamen 361005, China;Corresponding authors at: Department of Electronic Engineering, School of Electronic Science and Engineering (National Model Microelectronics College), Xiamen University, Xiamen 361005, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, School of Electronic Science and Engineering (National Model Microelectronics College), Xiamen University, Xiamen 361005, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, School of Electronic Science and Engineering (National Model Microelectronics College), Xiamen University, Xiamen 361005, China;Fujian Key Laboratory of Ultrafast Laser Technology and Applications, Xiamen University, Xiamen 361005, China;Corresponding authors at: Department of Electronic Engineering, School of Electronic Science and Engineering (National Model Microelectronics College), Xiamen University, Xiamen 361005, China"}], "References": [{"Title": "Large-field color vision in anomalous trichromats based on a trichromatic display", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102263", "JournalTitle": "Displays"}, {"Title": "A low speckle laser pico-projector using dynamic light scattering liquid crystal devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "102305", "JournalTitle": "Displays"}]}, {"ArticleId": 111855858, "Title": "A distributed reliable collusion‐free algorithm for selecting multiple coordinators in IOTA using fog computing", "Abstract": "IOTA is a distributed ledger technology with a new structure called Tangle, which offers high scalability, no fees, and near‐instant transfers for the internet‐of‐things (IoT) networks. The most important issue of IOTA is consensus achievement, which is handled by a single node acting as a coordinator. A single and default coordinator node exposes IOTA to the single point of failure and incomplete distribution issues. In this paper, a novel algorithm for selecting multiple coordinators to participate in the consensus process at milestones is proposed (i.e., MCS algorithm) to overcome the problem of a single coordinator in IOTA network. The MCS algorithm is applied in a two‐layered architecture including IoT devices (i.e., Layer 1) and fog nodes (i.e., Layer 2). We define and formulate four different properties as metrics to select multiple coordinators in this architecture. Moreover, a collusion list is defined to decrease the risk of collusion between fog nodes in the network. Experimental results show that using multiple fog nodes as coordinators in IOTA network can improve the average response time and average throughput while does not considerably sacrifice the total cost and system utilization in comparison with the use of a single default coordinator in IOTA network.", "Keywords": "coordinator;distributed ledger (DL);fog computing;IOTA tangle", "DOI": "10.1002/cpe.8009", "PubYear": 2024, "Volume": "36", "Issue": "11", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>avieh Sadat <PERSON>", "Affiliation": "Department of Computer Engineering, South Tehran Branch Islamic Azad University  Tehran Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, South Tehran Branch Islamic Azad University  Tehran Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science Technical University of Darmstadt  Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, South Tehran Branch Islamic Azad University  Tehran Iran"}], "References": [{"Title": "A survey on the security of blockchain systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "841", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "IoT transaction processing through cooperative concurrency control on fog–cloud computing environment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "8", "Page": "5695", "JournalTitle": "Soft Computing"}, {"Title": "A Comprehensive Survey on Attacks, Security Issues and Blockchain Solutions for IoT and IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102481", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A survey and taxonomy of simulation environments modelling fog computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "102042", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Iota Tangle: A cryptocurrency to communicate Internet-of-Things data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "307", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Secure Timestamp-Based Mutual Authentication Protocol for IoT Devices Using RFID Tags: ", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "3", "Page": "20", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "Ensemble machine learning approach for classification of IoT devices in smart home", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "3179", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A blockchain architecture for industrial applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "4", "Page": "100088", "JournalTitle": "Blockchain: Research and Applications"}, {"Title": "Blockchain-Based Cloud-Enabled Security Monitoring Using Internet of Things in Smart Agriculture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Sub<PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "9", "Page": "250", "JournalTitle": "Future Internet"}]}, {"ArticleId": 111855934, "Title": "Estimation of HbA1c for DMT2 risk prediction on the Mexican population based in Artificial Neural Networks", "Abstract": "In this paper, the main objective is to estimate the percentage of glycosylated hemoglobin through an easily accessible computational platform to estimate the risk of generating type 2 diabetes mellitus in the Mexican population. The estimation of the computational tool is developed through an artificial neural network model, which was trained and validated according to a population sample of 1120 Mexican people between 18 and 59 years old. The model inputs were gender, age, body mass index, waist circumference, weekly food consumption, family history, and whether the person suffers from any chronic degenerative disease other than T2DM. We used the percentage of glycosylated hemoglobin as output, estimated according to a dynamic glucose model. The estimation results present a coefficient of determination of 99 %, demonstrating an acceptable performance of the neural network model. The developed platform is an aid tool for health personnel, which seeks to generate a first approximation to the glycemic status of those communities with a high marginalization index for generating disease prevention strategies.", "Keywords": "Artificial Neural Network ; Glycated hemoglobin ; Graphical interface ; Mexican population ; Medical tool ; Physiological factors", "DOI": "10.1016/j.jksuci.2023.101905", "PubYear": 2024, "Volume": "36", "Issue": "1", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TecNM/CENIDET, Electronic Engineering Department, Interior Internado Palmira S/N, Palmira, Cuernavaca, 62490 Morelos, Mexico"}, {"AuthorId": 2, "Name": "Marisol Cervantes-Bobadilla", "Affiliation": "Center of Research in Engineering and Applied Sciences (CIICAp-IICBA)/UAEM, Av. Universidad 1001, Chamilpa, Cuernavaca 62209, Mexico"}, {"AuthorId": 3, "Name": "<PERSON>Pi<PERSON>", "Affiliation": "Faculty of Nutrition, UAEM, Cuernavaca 62350, Morelos, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "TecNM/CENIDET, Electronic Engineering Department, Interior Internado Palmira S/N, Palmira, Cuernavaca, 62490 Morelos, Mexico;Corresponding autor at: Electronic Engineering Department, TecNM/CENIDET, Cuernavaca, Morelos, Mexico.; Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "TecNM/CENIDET, Electronic Engineering Department, Interior Internado Palmira S/N, Palmira, Cuernavaca, 62490 Morelos, Mexico"}, {"AuthorId": 6, "Name": "<PERSON>-Mer<PERSON>z", "Affiliation": "Faculty of Nutrition, UAEM, Cuernavaca 62350, Morelos, Mexico"}], "References": [{"Title": "Machine learning and artificial intelligence based Diabetes Mellitus detection and self-management: A systematic review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "3204", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "ME-CCNN: Multi-encoded images and a cascade convolutional neural network for breast tumor segmentation and recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "9", "Page": "10099", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Multi-unit Discrete Hopfield Neural Network for higher order supervised learning through logic mining: Optimal performance design and attribute selection", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "5", "Page": "101554", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 111856331, "Title": "Acknowledgements to Reviewers", "Abstract": "", "Keywords": "", "DOI": "10.2174/187640291504231228221314", "PubYear": 2023, "Volume": "15", "Issue": "4", "JournalId": 12053, "JournalTitle": "Micro and Nanosystems", "ISSN": "1876-4029", "EISSN": "1876-4037", "Authors": [], "References": []}, {"ArticleId": 111856347, "Title": "Website Performance Measurements and Related Analysis", "Abstract": "", "Keywords": "", "DOI": "10.24818/issn14531305/27.4.2023.02", "PubYear": 2023, "Volume": "27", "Issue": "4/2023", "JournalId": 35086, "JournalTitle": "Informatica Economica", "ISSN": "1453-1305", "EISSN": "1842-8088", "Authors": [{"AuthorId": 1, "Name": "Alexandru COCIORVA", "Affiliation": ""}], "References": []}, {"ArticleId": 111856411, "Title": "Textured Pb(Mg1/3Nb2/3)O3 −Pb(In1/2Nb1/2)O3 −PbTiO3 ceramics with enhanced piezoelectric properties and high Curie temperature prepared by low-temperature sintering", "Abstract": "A low-temperature sintering is crucial for the fabrication of multilayer piezoceramic actuators through low-cost internal metal electrodes. Herein, Pb(Mg1/3Nb2/3)O <sub>3</sub>−Pb(In1/2Nb1/2)O<sub>3</sub> −PbTiO<sub>3</sub> (PMN−PIN−PT) textured ceramics were prepared by a combined use of refined matrix powder and CuO sintering additive. When the matrix powder was refined by planetary ball-milling for 10 h, the sintering temperature of the textured 0.37PMN−0.29PIN−0.34PT ceramics was lowered to 1175 ºC, and a subsequent addition of 1 mol% CuO further lowered it to 950 ºC. The textured 0.37PMN−0.29PIN−0.34PT ceramics with 3 vol% BaTiO <sub>3</sub> (BT) template sintered at 950 ºC for 10 h yielded excellent piezoelectric properties , such as a Lotgering factor (LF) of 97%, a unipolar strain of 0.23% at 2 kV/mm, and a Curie temperature ( T <sub>C</sub>) of 231 ºC. These enhanced piezoelectric properties and high T <sub>C</sub> result from the high texture, transient liquid phase sintering , Cu <sup>2+</sup> ion doping, and minimum BT template content.", "Keywords": "", "DOI": "10.1016/j.sna.2023.114929", "PubYear": 2024, "Volume": "366", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "Temesgen Tadeyos Zate", "Affiliation": "Functional Powder Materials Department, Korea Institute of Materials Science, Changwondaero 797, Changwon 51508, Republic of Korea;Department of Materials Science and Engineering, Ulsan National Institute of Science and Technology (UNIST), Ulsan 44919, Republic of Korea;Advanced Materials Engineering Division, University of Science and Technology (UST), Daejeon 34113, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering, Ulsan National Institute of Science and Technology (UNIST), Ulsan 44919, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering, Ulsan National Institute of Science and Technology (UNIST), Ulsan 44919, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering, Ulsan National Institute of Science and Technology (UNIST), Ulsan 44919, Republic of Korea"}, {"AuthorId": 5, "Name": "Wook <PERSON>", "Affiliation": "Department of Materials Science and Engineering, Ulsan National Institute of Science and Technology (UNIST), Ulsan 44919, Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Functional Powder Materials Department, Korea Institute of Materials Science, Changwondaero 797, Changwon 51508, Republic of Korea;Advanced Materials Engineering Division, University of Science and Technology (UST), Daejeon 34113, Republic of Korea;Corresponding author at: Functional Powder Materials Department, Korea Institute of Materials Science, Changwondaero 797, Changwon 51508, Republic of Korea"}], "References": [{"Title": "Outstanding unipolar strain of textured Pb(Mg1/3Nb2/3)O3–PbZrO3–PbTiO3 piezoelectric ceramics manufactured by particle size distribution control of the plate-like BaTiO3 template", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "335", "Issue": "", "Page": "113373", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 111856707, "Title": "Summarization Method of Argumentation Based on Argumentation Framework", "Abstract": "Visualization of discussions using diagrams is effective in facilitating efficient debates. However, because actual debates involve many conflicts, the diagrams that represent them tend to be complex in structure. Therefore, we use AF and BAF, which have simple structure and semantics to extract arguments that may be included in conclusions of debates, to support real-time debate progression and to verify the logical structure of debates. For this purpose, we propose a “BAF diagram summarization method” that recognizes and reduces subgraphs that represent local arguments about related sub-issues from these diagrams. The final output of this method is a diagram in the form of “Reliability based Argumentation Framework (RAF)”, which consists less nodes and less links between them. RAF is an extensional model of AF in which each argument is assigned an “argument class” based on its feasibility, and semantics for RAF is defined by argument classes of arguments and attack relations between them. The RAF semantics has the role of preserving the semantic information in the BAF diagram summarization method. This is because by computing the semantics of the resulting RAF, it is possible to capture some of the important information in the semantics of the original BAF. Furthermore, we developed an on-line discussion support tool that implements this method, and visualize the results of user input.", "Keywords": "argumentation framework;argument analysis;argument summarization;reliability;incompleteness", "DOI": "10.1527/tjsai.39-1_C-N12", "PubYear": 2024, "Volume": "39", "Issue": "1", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational Intelligence and Systems Science, Interdisciplinary Graduate School of Science and Engineering, Tokyo Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tokyo Institute of Technology"}], "References": [{"Title": "Acceptance in incomplete argumentation frameworks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "295", "Issue": "", "Page": "103470", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 111858237, "Title": "Erratum: Value 行列を手掛かりとしたTransformerの分析 [人工知能学会論文誌38巻2号C-MB7_1-7]", "Abstract": "人工知能学会論文誌Vol.38, No.2 の速報論文「Value 行列を手掛かりとしたTransformer の分析」において，以下の通り，誤りがありましたので，お詫びして訂正いたします．", "Keywords": "", "DOI": "10.1527/tjsai.39-1_Err-MB7", "PubYear": 2024, "Volume": "39", "Issue": "1", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tokushima University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tokushima University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tokushima University"}], "References": []}, {"ArticleId": 111858314, "Title": "事前交渉により交渉順序を調整する自動交渉プロトコル", "Abstract": "Automated negotiations have been studied very widely. Usually, in the field of automated negotiations, researchers focus on bilateral negotiations, in which two agents negotiate for each other. One-to-one bilateral negotiations are more natural in the real world among companies compared with a market-style (one-to-many). In the real world, companies are doing multiple bilateral negotiations every day. However, there is less research on multiple bilateral negotiations. Some mathematical analysis was conducted in the case of 3 agents in the classic economic literature, and also there has been a proposed protocol called SAOP for multiple bilateral negotiations in the international competition, ANAC. In multiple bilateral negotiations, the results may differ greatly depending on the order in which the bilateral negotiations are conducted. However, in previous research on the automatic negotiation protocol between multiple agents, which has been conventionally used in international competitions, almost no research considering the negotiation order was found. Therefore, in this study, we investigated whether better negotiation results could be obtained by using a proposed protocol that dynamically determined the negotiation order. The better negotiation result described here means that the value of the social surplus, which is the sum of the acquired utility values of all the negotiation agents, is high and has the shortest possible time. The method proposed here is social surplus-based pre-negotiation (hereinafter referred to as“ pre-negotiation”), in which the agent who evaluates the most important issues in a multi-agent negotiation starts the negotiation first. In this study, we compared the values of social surplus, the number of agreements, and negotiation time for 3, and 6-agent negotiations against the proposed and conventional methods. The results showed that the protocol proposed in this study achieved the highest social surplus, especially when pre-negotiation was used, and the protocol was more effective as the number of agents participating in the negotiation increased.", "Keywords": "automated negotiation agents;negotiation protocols;social welfare", "DOI": "10.1527/tjsai.39-1_B-M24", "PubYear": 2024, "Volume": "39", "Issue": "1", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON>n <PERSON>", "Affiliation": "Department of Social Informatics, Graduate School of Informatics, Kyoto University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Social Informatics, Graduate School of Informatics, Kyoto University"}], "References": []}, {"ArticleId": 111858468, "Title": "Pulmonary Nodule Detection from 3D CT Image with a Two-Stage Network", "Abstract": "<p>Early detection of lung nodules is an important means of reducing the lung cancer mortality rate. In this paper, we propose a three-dimensional CT image lung nodule detection method based on parallel pooling and dense blocks, which includes two parts, i.e., candidate nodule extraction and false positive suppression. First, a dense U-shaped backbone network with parallel pooling is proposed to obtain the candidate nodule probability map. The parallel pooling structure uses multiple pooling operations for downsampling to capture spatial information comprehensively and address the problem of information loss resulting from maximum and average pooling in the shallow layers. Then, a parasitic network with parallel pooling, dense blocks, and attention modules is designed to suppress false positive nodules. The parasitic network takes the multiscale feature maps of the backbone network as the input. The experimental results demonstrate that the proposed method significantly improves the accuracy of lung nodule detection, achieving a CPM score of 0.91, which outperforms many existing methods.</p>", "Keywords": "", "DOI": "10.1155/2023/3028869", "PubYear": 2023, "Volume": "2023", "Issue": "1", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan 411100, China"}, {"AuthorId": 2, "Name": "Z<PERSON>wei Chi", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan 411100, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan 411100, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 5, "Name": "Yonghua Hu", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan 411100, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan 411100, China"}], "References": [{"Title": "Dual-branch residual network for lung nodule segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105934", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 111858617, "Title": "A novel resource allocation scheme for full-duplex NOMA systems with power splitting-based SWIPT", "Abstract": "This paper introduces novel resource allocation (RA) algorithms for optimizing wireless network performance, focusing on a full-duplex (FD) base station (BS) employing non-orthogonal multiple access (NOMA) technology. Motivated by the intriguing trade-off introduced by the simultaneous wireless information and power transfer (SWIPT) scheme, in which achievable data rates are sacrificed to enhance harvested energy, this study aims to quantify the delicate equilibrium between two vital aspects meticulously: the overall achievable data rate facilitated by NOMA and FD technologies and the energy harvested via the SWIPT approach. Specifically, our focus is on jointly optimizing the enhancement of both data rate and harvested energy for downlink (DL) users where a multi-objective optimization approach is developed in the proposed RA algorithm to study the existing trade-off between these conflicting objective functions. This problem optimizes the power allocation for both the BS and the users, allocating subcarriers, and optimizing the power splitting factor for SWIPT scheme. We developed efficient iterative solutions for the intended RA problems because they involve complex mixed-integer nonlinear optimization challenges that are generally difficult to solve. The effectiveness of the proposed RA framework is demonstrated using simulation results. These results investigate whether FD MC-NOMA systems, enabled by the proposed RA algorithms, provide a significant increase in system throughput compared to both conventional half-duplex (HD) multicarrier orthogonal multiple access (MC-OMA) systems and HD MC-NOMA systems, as well as other baseline methods . The results also demonstrate that our proposed scheme allows for a greater power harvest than the OMA-FD configuration and other baseline schemes.", "Keywords": "", "DOI": "10.1016/j.phycom.2023.102272", "PubYear": 2024, "Volume": "63", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, University of Birjand, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, University of Birjand, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Guilan, Rasht, Iran"}], "References": []}, {"ArticleId": 111858686, "Title": "Class-Aware Mask-guided feature refinement for scene text recognition", "Abstract": "Scene text recognition is a rapidly developing field that faces numerous challenges due to the complexity and diversity of scene text, including complex backgrounds, diverse fonts, flexible arrangements, and accidental occlusions. In this paper, we propose a novel approach called Class-Aware Mask-guided feature refinement (CAM) to address these challenges. Our approach introduces canonical class-aware glyph masks generated from a standard font to effectively suppress background and text style noise, thereby enhancing feature discrimination. Additionally, we design a feature alignment and fusion module to incorporate the canonical mask guidance for further feature refinement for text recognition. By enhancing the alignment between the canonical mask feature and the text feature, the module ensures more effective fusion, ultimately leading to improved recognition performance. We first evaluate CAM on six standard text recognition benchmarks to demonstrate its effectiveness. Furthermore, CAM exhibits superiority over the state-of-the-art method by an average performance gain of 4.1% across six more challenging datasets, despite utilizing a smaller model size. Our study highlights the importance of incorporating canonical mask guidance and aligned feature refinement techniques for robust scene text recognition. Code will be available at https://github.com/MelosY/CAM .", "Keywords": "", "DOI": "10.1016/j.patcog.2023.110244", "PubYear": 2024, "Volume": "149", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information and Communications, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artifcial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Inc., Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information and Communications, Huazhong University of Science and Technology, Wuhan, China;Hubei Key Laboratory of Smart Internet Technology, Huazhong University of Science and Technology, Wuhan, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON>ang Bai", "Affiliation": "School of Software Engineering, Huazhong University of Science and Technology, Wuhan, China"}], "References": [{"Title": "Multi-branch guided attention network for irregular text recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "425", "Issue": "", "Page": "278", "JournalTitle": "Neurocomputing"}, {"Title": "Scene Text Detection and Recognition: The Deep Learning Era", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "161", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "STAN: A sequential transformation attention-based network for scene text recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107692", "JournalTitle": "Pattern Recognition"}, {"Title": "Separating Content from Style Using Adversarial Learning for Recognizing Text in the Wild", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "4", "Page": "960", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "MASTER: Multi-aspect non-local network for scene text recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107980", "JournalTitle": "Pattern Recognition"}, {"Title": "MASTER: Multi-aspect non-local network for scene text recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107980", "JournalTitle": "Pattern Recognition"}, {"Title": "Towards open-set text recognition via label-to-prototype learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109109", "JournalTitle": "Pattern Recognition"}, {"Title": "End-to-End page-Level assessment of handwritten text recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "109695", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 111858731, "Title": "マンガ書籍中の不適切な画像検出システム", "Abstract": "This paper describes a system for detecting inappropriate images in manga books intended for reading by boys and girls. However, since a page of a manga book has multiple frames, each of which contains images of different scenes, it is necessary to determine inappropriate images for each frame of the page. In this study, we developed an inappropriate image detection system that combines a CNN for automatically extracting frames on a page as polygons and a CNN for recognizing the presence or absence of inappropriate objects in each extracted frame, specializing in detecting exposed female breasts, the most frequently appearing inappropriate image in manga books. This system has improved the efficiency and quality of inappropriate image detection, which was previously possible only by human visual inspection.", "Keywords": "AI application;system development;manga;flame detection;inappropriate image detection;NSFW;CNN;deep learning", "DOI": "10.1527/tjsai.39-1_A-N76", "PubYear": 2024, "Volume": "39", "Issue": "1", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Environment and Information Sciences Yokohama National University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Environment and Information Sciences , Yokohama National University"}], "References": []}, {"ArticleId": 111859554, "Title": "Wi-Gitation: Replica Wi-Fi CSI Dataset for Physical Agitation Activity Recognition", "Abstract": "<p>Agitation is a commonly found behavioral condition in persons with advanced dementia. It requires continuous monitoring to gain insights into agitation levels to assist caregivers in delivering adequate care. The available monitoring techniques use cameras and wearables which are distressful and intrusive and are thus often rejected by older adults. To enable continuous monitoring in older adult care, unobtrusive Wi-Fi channel state information (CSI) can be leveraged to monitor physical activities related to agitation. However, to the best of our knowledge, there are no realistic CSI datasets available for facilitating the classification of physical activities demonstrated during agitation scenarios such as disturbed walking, repetitive sitting–getting up, tapping on a surface, hand wringing, rubbing on a surface, flipping objects, and kicking. Therefore, in this paper, we present a public dataset named Wi-Gitation. For Wi-Gitation, the Wi-Fi CSI data were collected with twenty-three healthy participants depicting the aforementioned agitation-related physical activities at two different locations in a one-bedroom apartment with multiple receivers placed at different distances (0.5–8 m) from the participants. The validation results on the Wi-Gitation dataset indicate higher accuracies (F1-Scores ≥0.95) when employing mixed-data analysis, where the training and testing data share the same distribution. Conversely, in scenarios where the training and testing data differ in distribution (i.e., leave-one-out), the accuracies experienced a notable decline (F1-Scores ≤0.21). This dataset can be used for fundamental research on CSI signals and in the evaluation of advanced algorithms developed for tackling domain invariance in CSI-based human activity recognition.</p>", "Keywords": "", "DOI": "10.3390/data9010009", "PubYear": 2024, "Volume": "9", "Issue": "1", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, University of Twente, 7522 NB Enschede, The Netherlands;Faculty of Behavioural, Management and Social Sciences, University of Twente, Enschede, 7522 NB Enschede, The Netherlands;Faculty of Information Technology and Electrical Engineering, University of Oulu, 90570 Oulu, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, University of Twente, 7522 NB Enschede, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Faculty of Behavioural, Management and Social Sciences, University of Twente, Enschede, 7522 NB Enschede, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, University of Twente, 7522 NB Enschede, The Netherlands"}, {"AuthorId": 5, "Name": "Duc V. <PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, University of Twente, 7522 NB Enschede, The Netherlands"}], "References": [{"Title": "Seizure episodes detection via smart medical sensing system", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "4363", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "WiFi Sensing with Channel State Information", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A dataset for Wi-Fi-based human-to-human interaction recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "", "Page": "105668", "JournalTitle": "Data in Brief"}, {"Title": "A dataset for Wi-Fi-based human activity recognition in line-of-sight and non-line-of-sight indoor environments", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "33", "Issue": "", "Page": "106534", "JournalTitle": "Data in Brief"}, {"Title": "WiStress", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 111859566, "Title": "Utilizing LASSO for Breast Cancer Prediction: A Hyper Machine Learning Technique with Significant", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/121223", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 111859579, "Title": "Analysis and compensation of stop-and-go approximation in high-resolution spaceborne synthetic aperture radar", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2023.2293477", "PubYear": 2024, "Volume": "45", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "Haisheng Li", "Affiliation": "Key Laboratory of Electronics and Information Technology for Space Systems, National Space Science Center, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Electronics and Information Technology for Space Systems, National Space Science Center, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Electronics and Information Technology for Space Systems, National Space Science Center, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "Tiancheng Chen", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China;Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing, China"}], "References": [{"Title": "Continuous pulse repetition interval variation for highly squint high-resolution spotlight SAR imaging", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "209", "JournalTitle": "Remote Sensing Letters"}]}, {"ArticleId": 111859843, "Title": "Electric Actuation of Transport Vehicles: Overview of Technical Characteristics and Propulsion Solutions through a Systematic Patent Analysis", "Abstract": "<p>Electric transport vehicles offer sustainable transportation solutions with benefits, such as reduced emissions, noise, and operating costs. This paper draws an overview of the available technical solutions to actuate transport vehicles with electric drives, as depicted by patent literature. A dataset of 1784 patents was created; the documents were selected through a systematic approach, and the patents were then classified according to a number of user-defined categories. The dataset was analyzed by applying two different methods: (i) a quantitative analysis (literature overview), enabling glance evaluations about the defined categories, and (ii) a qualitative analysis (detailed analysis), which focuses on the detection of interesting design features or innovative solutions. The results of this work not only provide an alternative and complementary overview to the analysis of solutions that may emerge from a scientific literature review, but can also offer support in strategic planning to companies wishing to protect their innovations and remain competitive in the evolving market of transport vehicles.</p>", "Keywords": "", "DOI": "10.3390/act13010015", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Brescia, Via Branze 38, 25123 Brescia, BS, Italy; Antares Vision S.p.A., Via del Ferro 16, 25039 Travagliato, BS, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Brescia, Via Branze 38, 25123 Brescia, BS, Italy; Antares Vision S.p.A., Via del Ferro 16, 25039 Travagliato, BS, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Brescia, Via Branze 38, 25123 Brescia, BS, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Brescia, Via Branze 38, 25123 Brescia, BS, Italy"}, {"AuthorId": 5, "Name": "Cinzia Amici", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Brescia, Via Branze 38, 25123 Brescia, BS, Italy; Corresponding author"}], "References": []}, {"ArticleId": 111859866, "Title": "Optimal Composition Theorem for Randomized Query Complexity", "Abstract": "", "Keywords": "query complexity; randomized decision tree; composed function; lower bound", "DOI": "10.4086/toc.2023.v019a009", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 34450, "JournalTitle": "Theory of Computing", "ISSN": "", "EISSN": "1557-2862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111860094, "Title": "Analysis of COVID-19 Disease Transmission with the Impact of Vaccine", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/121229", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 111860478, "Title": "A Distributed Lightweight PUF-Based Mutual Authentication Protocol for IoV", "Abstract": "<p>In recent times, the advent of innovative technological paradigms like the Internet of Things has paved the way for numerous applications that enhance the quality of human life. A remarkable application of IoT that has emerged is the Internet of Vehicles (IoV), motivated by an unparalleled surge of connected vehicles on the roads. IoV has become an area of significant interest due to its potential in enhancing traffic safety as well as providing accurate routing information. The primary objective of IoV is to maintain strict latency standards while ensuring confidentiality and security. Given the high mobility and limited bandwidth, vehicles need to have rapid and frequent authentication. Securing Vehicle-to-Roadside unit (V2R) and Vehicle-to-Vehicle (V2V) communications in IoV is essential for preventing critical information leakage to an adversary or unauthenticated users. To address these challenges, this paper proposes a novel mutual authentication protocol which incorporates hardware-based security primitives, namely physically unclonable functions (PUFs) with Multi-Input Multi-Output (MIMO) physical layer communications. The protocol allows a V2V and V2R to mutually authenticate each other without the involvement of a trusted third-party (server). The protocol design effectively mitigates modeling attacks and impersonation attempts, where the accuracy of predicting the value of each PUF response bit does not exceed 54%, which is equivalent to a random guess.</p>", "Keywords": "", "DOI": "10.3390/iot5010001", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 58626, "JournalTitle": "IoT", "ISSN": "", "EISSN": "2624-831X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Electrical Engineering, University of Maryland, Baltimore, MD 21250, USA; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Biomedical Engineering, University of Nevada, Reno, NV 89557, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Electrical Engineering, University of Maryland, Baltimore, MD 21250, USA"}], "References": [{"Title": "Efficient privacy-preserving authentication protocol using PUFs with blockchain smart contracts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101958", "JournalTitle": "Computers & Security"}, {"Title": "A survey on physical unclonable function (PUF)-based security solutions for Internet of Things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "183", "Issue": "", "Page": "107593", "JournalTitle": "Computer Networks"}, {"Title": "Multifactor authentication scheme using physically unclonable functions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "", "Page": "100343", "JournalTitle": "Internet of Things"}, {"Title": "Three-factor authentication protocol using physical unclonable function for IoV", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "45", "JournalTitle": "Computer Communications"}, {"Title": "A novel lightweight PUF based authentication protocol for IoT without explicit CRPs in verifier database", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "6227", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 111860795, "Title": "The Effectiveness of a Counseling Program Based on Acceptance and Commitment Therapy in Managing Psychological Crises for a Sample of Female Undergraduate and Postgraduate Students", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/121225", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 111861369, "Title": "Development and application of a maturity model for Industrial Agile Working", "Abstract": "In recent years, several companies approached agile working strategies to increase the resilience of their business. Agile work started spreading also to the manufacturing sector, thanks to the adoption of technologies that increasingly allow remote control of production systems. Nevertheless, agile working in the industry, namely Industrial Agile Working (IAW), requires organisational transformations and investments in technological innovations to guarantee its successful implementation. The first objective of this paper is to define the most relevant dimensions that affect the applicability of agile working practices in industrial contexts. The second objective is to develop an Industrial Agile Working Maturity Model (IAWMM) to measure the maturity of a company in adopting IAW practices. Starting from a set of dimensions identified through scientificic literature analysis, the IAWMM was tested by involving several companies in the manufacturing sector. The refined IAWMM was finally applied to a small sample of companies. The results report a good readiness of the companies concerning the knowledge and application of the key enabling technologies but still a low willingness concerning organisational change and flexible workforce management models. However, the IAW appears to be a promising strategy to promote a better workforce wellbeing and companies’ efficiency and resilience.", "Keywords": "Agile Work ; Smart Working ; Work organization ; Smart Manufacturing ; Maturity model", "DOI": "10.1016/j.cie.2023.109877", "PubYear": 2024, "Volume": "188", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management, Information and Production Engineering, University of Bergamo, Viale Marconi 5, 24044 Dalmine BG, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Management, Information and Production Engineering, University of Bergamo, Viale Marconi 5, 24044 Dalmine BG, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Management, Information and Production Engineering, University of Bergamo, Viale Marconi 5, 24044 Dalmine BG, Italy"}], "References": [{"Title": "Work design in future industrial production: Transforming towards cyber-physical systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "105679", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Industry 5.0: A survey on enabling technologies and potential applications", "Authors": "<PERSON><PERSON><PERSON>; Quoc-<PERSON><PERSON>am; Prabadevi B", "PubYear": 2022, "Volume": "26", "Issue": "", "Page": "100257", "JournalTitle": "Journal of Industrial Information Integration"}, {"Title": "Workforce and supply chain disruption as a digital and technological innovation opportunity for resilient manufacturing systems in the COVID-19 pandemic", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "108158", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Industrial Smart Working: a socio-technical model for enabling successful implementation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "505", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 111861993, "Title": "Extraction network of forests and lakes along the railway based on remote sensing images", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2023.2295835", "PubYear": 2024, "Volume": "45", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>ling Ye", "Affiliation": "Jiangsu Collaborative Innovation Center of Atmospheric Environment and Equipment Technology, Information and Systems Science Institute, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information and Systems Science Institute, Nanjing University of Information Science & Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Information and Systems Science Institute, Nanjing University of Information Science & Technology, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Collaborative Innovation Center of Atmospheric Environment and Equipment Technology, Information and Systems Science Institute, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information and Systems Science Institute, Nanjing University of Information Science & Technology, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Collaborative Innovation Center of Atmospheric Environment and Equipment Technology, Information and Systems Science Institute, Nanjing, China"}], "References": [{"Title": "Scale Sequence Joint Deep Learning (SS-JDL) for land use and land cover classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111593", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 111862079, "Title": "Skin-inspired flexible pressure sensor with hierarchical interlocked spinosum microstructure by laser direct writing for high sensitivity and large linearity", "Abstract": "Recently, wearable pressure sensors have attracted tremendous attention due to their potential applications in human motion and health monitoring. However, the preparation process of widely used microstructure pressure sensors is complex, and the trade-off between high sensitivity and wide linearity is hard to achieve, which restricts their practical applications. Herein, inspired by the geometry of the skin epidermis, this paper proposes a flexible piezoresistive sensor with hierarchical interlocked spinosum microstructure by laser direct writing technology with scalability, high efficiency, and strong controllability. Due to the hierarchical interlocked structure and gradient stiffness, the pressure sensor with a spinosum microstructure has a sensitivity of up to 68.3 kPa <sup>−1</sup> in a wide linear range of 0–13.5 kPa and exhibits a good durability (10000 cycles). Human health signals such as pulse, arm bending, and leg movements have been monitored in real-time by attaching the pressure sensor to the human body. In addition, a rehabilitation device integrating various sensors was also developed to illustrate the scalability of this strategy. The hierarchical interlocked spinosum microstructure makes the flexible pressure sensor have broad application prospects in wearable electronic devices.", "Keywords": "", "DOI": "10.1016/j.sna.2023.114988", "PubYear": 2024, "Volume": "366", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "Longsheng Lu", "Affiliation": "School of Mechanical & Automotive Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical & Automotive Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Science and Technology Venture Capital Development Center, Beijing 100036, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical & Automotive Engineering, South China University of Technology, Guangzhou 510641, China;Corresponding author"}], "References": []}, {"ArticleId": 111862135, "Title": "Enhancing Student Affairs for Intellectual Security of Scholarship Students", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/121224", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 111862636, "Title": "A product performance rapid simulation approach driven by digital twin data: Part 1. For variable product structures", "Abstract": "In the product design stage, the effective performance analysis method is the premise to guarantee the application performance of products. However, digital simulation, as a widely used performance analysis method, has a long simulation cycle and high requirements for computer hardware performance , which leads to long product design and iteration time. Facing this problem, a product performance rapid simulation approach driven by digital twin (DT) data for variable product structures is proposed to replace the digital simulation method. Firstly, the framework for variable product structures is designed, which includes a digital simulation module, a data-driven module, and a surrogate-based simulation module. Then, module implementation of the proposed framework is introduced in detail, in which the twin data-driven product performance rapid simulation model (RSM) for variable product structures is studied based on the makeTwin reference architecture. Finally, a case study is conducted on the iterative design of a connecting rod , focusing on equivalent stress evaluation. The result shows that the proposed method is feasible, as well as costs a shorter simulation time than traditional digital simulation.", "Keywords": "", "DOI": "10.1016/j.aei.2023.102337", "PubYear": 2024, "Volume": "59", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China"}, {"AuthorId": 5, "Name": "Songhua Ma", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China"}], "References": [{"Title": "A hybrid predictive maintenance approach for CNC machine tool driven by Digital Twin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101974", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A comprehensive survey of anomaly detection techniques for high dimensional big data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Digital twin and blockchain enhanced smart manufacturing service collaboration and management", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "903", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Digital twin to improve the virtual-real integration of industrial IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "", "Page": "100196", "JournalTitle": "Journal of Industrial Information Integration"}, {"Title": "Digital Twin: Generalization, characterization and implementation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "113524", "JournalTitle": "Decision Support Systems"}, {"Title": "Review of deep learning: concepts, CNN architectures, challenges, applications, future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "53", "JournalTitle": "Journal of Big Data"}, {"Title": "Data-driven early fault diagnostic methodology of permanent magnet synchronous motor", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "115000", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Application of the Digital Twin for in process monitoring of the micro injection moulding process quality", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "135", "Issue": "", "Page": "103568", "JournalTitle": "Computers in Industry"}, {"Title": "A radial basis function surrogate model assisted evolutionary algorithm for high-dimensional expensive optimization problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "108353", "JournalTitle": "Applied Soft Computing"}, {"Title": "Enhancing computational fluid dynamics with machine learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "6", "Page": "358", "JournalTitle": "Nature Computational Science"}]}, {"ArticleId": 111862874, "Title": "Video surveillance-based multi-task learning with swin transformer for earthwork activity classification", "Abstract": "Bulldozers, pivotal in earthworks, traditionally undergo supervision through labor-intensive and potentially unreliable manual methods. This research proposes a vision-based method for automating the monitoring of bulldozer operations. First, this research develops a specialized dataset for deep learning , the bulldozer earthmoving activity dataset. Following this, a novel multi-task video classification network (MTVTNet), the multi-task video transformer network, utilizing a video swin transformer architecture, is proposed. This network is adept at concurrently detecting the shoveling action, state, and soil classification of a bulldozer. The effectiveness of this model is demonstrated through its application in a real-world construction setting, achieving a remarkable 99.68% mean average precision. This method not only facilitates comprehensive automated supervision of bulldozer earthmoving activities but also serves as a valuable data source for assessing the operational efficiency of these machines.", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107814", "PubYear": 2024, "Volume": "131", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Center of Technology Innovation for Digital Construction, Huazhong University of Science and Technology, Wuhan, Hubei, China;School of Civil and Hydraulic Engineering, Huazhong University of Science and Technology, Wuhan, Hubei, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Center of Technology Innovation for Digital Construction, Huazhong University of Science and Technology, Wuhan, Hubei, China;School of Civil and Hydraulic Engineering, Huazhong University of Science and Technology, Wuhan, Hubei, China;Institute of Artificial Intelligence, Huazhong University of Science & Technology, Wuhan, Hubei, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Center of Technology Innovation for Digital Construction, Huazhong University of Science and Technology, Wuhan, Hubei, China;School of Civil and Hydraulic Engineering, Huazhong University of Science and Technology, Wuhan, Hubei, China;Corresponding author. National Center of Technology Innovation for Digital Construction, Huazhong University of Science and Technology, Wuhan, Hubei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Center of Technology Innovation for Digital Construction, Huazhong University of Science and Technology, Wuhan, Hubei, China;School of Civil and Hydraulic Engineering, Huazhong University of Science and Technology, Wuhan, Hubei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shantui Construction Machinery Co., Ltd, Jining, Shandong, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Shantui Construction Machinery Co., Ltd, Jining, Shandong, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Shantui Construction Machinery Co., Ltd, Jining, Shandong, China"}], "References": [{"Title": "Bottom-up image detection of water channel slope damages based on superpixel segmentation and support vector machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101205", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A deep neural network ensemble of multimodal signals for classifying excavator operations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "290", "JournalTitle": "Neurocomputing"}, {"Title": "Graph relation network for person counting in construction site using UAV", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107562", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Generative adversarial learning strategy for enhanced lightweight crack delineation networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101575", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Optimized Convolutional Neural Network for Road Detection with Structured Contour and Spatial Information for Intelligent Vehicle System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "6", "Page": "", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}, {"Title": "Lane detection in intelligent vehicle system using optimal 2- tier deep convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "5", "Page": "7293", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "UAV imagery based potential safety hazard evaluation for high-speed railroad using Real-time instance segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101819", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Excavator 3D pose estimation using deep learning and hybrid datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101875", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": *********, "Title": "Electrohydrodynamic Stability of Self-gravitating Fluid Cylinder", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/130101", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "Improving Financial Analyst Predictions through Intangible Asset Impairment Accounting", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/130103", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "Hydromagnetic Stability of a Self-gravitating Oscillating Fluid Cylinder", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/130102", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "A Robust Timing Synchronization Algorithm Based on PSSS for LTE-V2X", "Abstract": "<p>In recent years, Long-Term Evolution Vehicle-to-Everything (LTE-V2X) communication technology has received extensive attention. Timing synchronization is a crucial step in the receiving process, addressing Timing Offsets (TOs) resulting from random propagation delays, sampling frequency mismatches between the transmitter and receiver or a combination of both. However, the presence of high-speed relative movement between nodes and a low antenna height leads to a significant Doppler frequency offset, resulting in a low Signal-to-Noise Ratio (SNR) for received signals in LTE-V2X communication scenarios. This paper aims to investigate LTE-V2X technology with a specific focus on time synchronization. The research centers on the time synchronization method utilizing the Primary Sidelink Synchronization Signal (PSSS) and conducts a comprehensive analysis of existing algorithms, highlighting their respective advantages and disadvantages. On this basis, a robust timing synchronization algorithm for LTE-V2X communication scenarios is proposed. The algorithm comprises three key steps: coarse synchronization, frequency offset estimation and fine synchronization. Enhanced robustness is achieved through algorithm fusion, optimal decision threshold design and predefined frequency offset values. Furthermore, a hardware-in-the-loop simulation platform is established. The simulation results demonstrate a substantial performance improvement for the proposed algorithm compared to existing methods under adverse channel conditions characterized by high frequency offsets and low SNR.</p>", "Keywords": "", "DOI": "10.3390/computers13010012", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "China United Network Communications Group Co., Ltd., Beijing 100033, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China United Network Communications Group Co., Ltd., Beijing 100033, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China; Corresponding author"}], "References": []}, {"ArticleId": 111863672, "Title": "Determining the Best Woven and Non-Woven Floor Covering (Carpets or Rugs) Commonly Used In Jazan Region, According To Their Resistance to Burning", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/121226", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 111863712, "Title": "Quadratic stability of uncertain large-scale networked systems", "Abstract": "", "Keywords": "", "DOI": "10.1080/23307706.2023.2292105", "PubYear": 2024, "Volume": "11", "Issue": "4", "JournalId": 30013, "JournalTitle": "Journal of Control and Decision", "ISSN": "2330-7706", "EISSN": "2330-7714", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Qingdao University, Qingdao, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Qingdao University, Qingdao, People's Republic of China;Shandong Key Laboratory of Industrial Control Technology, Qingdao University, Qingdao, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Qingdao University, Qingdao, People's Republic of China;Shandong Key Laboratory of Industrial Control Technology, Qingdao University, Qingdao, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Qingdao University, Qingdao, People's Republic of China;Shandong Key Laboratory of Industrial Control Technology, Qingdao University, Qingdao, People's Republic of China"}], "References": [{"Title": "Quadratic stabilisation of switched affine systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Control and Decision"}]}, {"ArticleId": 111863808, "Title": "Modeling User Participation in Facebook Live by Applying the Mediating Role of Social Presence", "Abstract": "<p>The rapid development of mobile Internet technology has brought about the flourishing growth of social media live streaming. This study employs social presence theory as the primary framework to investigate the impact of Facebook Live’s features of sociality, immediacy, and entertainment on users’ sense of presence. These features were then considered within the dimensions of awareness, emotion, and cognition. The influence of social presence on user engagement behaviors was divided into browsing behavior, interactive behavior, and creative behavior. Using snowball sampling, an online survey was administered to Facebook Live users, and 416 valid responses were collected. The research team used software to analyze the data, primarily encompassing descriptive statistics, reliability and validity analyses, structural equation modeling, and mediation effects testing. The research findings are as follows. First, the media characteristics of Facebook Live significantly influence the sense of presence. Specifically, sociality, immediacy, and entertainment on Facebook Live have a notable impact on users’ awareness, emotion, and cognition. Second, different dimensions of social presence have distinct effects on various user engagement behaviors. Notably, the dimensions of awareness, emotion, and cognition of social presence positively affect users’ browsing and interactive behaviors, while emotion influences users’ creative behavior. The third finding was that awareness, emotion, and cognition act as intermediates between Facebook Live’s media characteristics and user engagement behaviors. Implications are discussed.</p>", "Keywords": "", "DOI": "10.3390/info15010023", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Journalism, Fudan University, Shanghai 200437, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Adult and Continuing Education, National Taiwan Normal University, Taipei City 106, Taiwan; Corresponding author"}], "References": []}, {"ArticleId": 111863897, "Title": "XAI-PDF: A Robust Framework for Malicious PDF Detection Leveraging SHAP-Based Feature Engineering", "Abstract": "<p>With the increasing number of malicious PDF files used for cyberattacks, it is essential to develop efficient and accurate classifiers to detect and prevent these threats. Machine Learning (ML) models have successfully detected malicious PDF files. This paper presents XAI-PDF, an efficient system for malicious PDF detection designed to enhance accuracy and minimize decision-making time on a modern dataset, the Evasive-PDFMal2022 dataset. The proposed method optimizes malicious PDF classifier performance by employing feature engineering guided by Shapley Additive Explanations (SHAP). Particularly, the model development approach comprises four phases: data preparation, model building, explainability of the models, and derived features. Utilizing the interpretability of SHAP values, crucial features are identified, and new ones are generated, resulting in an improved classification model that showcases the effectiveness of interpretable AI techniques in enhancing model performance. Various interpretable ML models were implemented, with the Lightweight Gradient Boosting Machine (LGBM) outperforming other classifiers. The Explainable Artificial Intelligence (XAI) global surrogate model generated explanations for LGBM predictions. Experimental comparisons of XAI-PDF with baseline methods revealed its superiority in achieving higher accuracy, precision, and F1-scores with minimal False Positive (FP) and False Negative (FN) rates (99.9%, 100%, 99.89%,0.000, and 0.002, respectively). Additionally, XAI-PDF requires only 1.36 milliseconds per record for predictions, demonstrating increased resilience in detecting evasive malicious PDF files compared to state-of-the-art methods</p>", "Keywords": "explainable artificial intelligence; feature engineering; Machine learning; malicious PDF detection; shapley additive explanations", "DOI": "10.34028/iajit/21/1/12", "PubYear": 2024, "Volume": "21", "Issue": "1", "JournalId": 69739, "JournalTitle": "The International Arab Journal of Information Technology", "ISSN": "1683-3198", "EISSN": "2309-4524", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Cybersecurity, Princess <PERSON><PERSON><PERSON> for Technology, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Cybersecurity, Jordan University of Science for Technology, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Data Science, Princess <PERSON><PERSON><PERSON> University for Technology, Jordan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Data Science, Princess <PERSON><PERSON><PERSON> University for Technology, Jordan"}], "References": []}, {"ArticleId": 111864291, "Title": "A Comparative Study on Recent Automatic Data Fusion Methods", "Abstract": "<p>Automatic data fusion is an important field of machine learning that has been increasingly studied. The objective is to improve the classification performance from several individual classifiers in terms of accuracy and stability of the results. This paper presents a comparative study on recent data fusion methods. The fusion step can be applied at early and/or late stages of the classification procedure. Early fusion consists of combining features from different sources or domains to form the observation vector before the training of the individual classifiers. On the contrary, late fusion consists of combining the results from the individual classifiers after the testing stage. Late fusion has two setups, combination of the posterior probabilities (scores), which is called soft fusion, and combination of the decisions, which is called hard fusion. A theoretical analysis of the conditions for applying the three kinds of fusion (early, late, and late hard) is introduced. Thus, we propose a comparative analysis with different schemes of fusion, including weaknesses and strengths of the state-of-the-art methods studied from the following perspectives: sensors, features, scores, and decisions.</p>", "Keywords": "", "DOI": "10.3390/computers13010013", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Telecomunicaciones y Aplicaciones Multimedia, Universitat Politècnica de València, 46022 Valencia, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Telecomunicaciones y Aplicaciones Multimedia, Universitat Politècnica de València, 46022 Valencia, Spain; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto de Telecomunicaciones y Aplicaciones Multimedia, Universitat Politècnica de València, 46022 Valencia, Spain"}], "References": [{"Title": "Pattern recognition techniques for provenance classification of archaeological ceramics using ultrasounds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "441", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Vector score alpha integration for classifier late fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "48", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "FuseVis: Interpreting Neural Networks for Image Fusion Using Per-Pixel Saliency Visualization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "98", "JournalTitle": "Computers"}, {"Title": "Deep Feature Fusion of Fingerprint and Online Signature for Multimodal Biometrics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "2", "Page": "21", "JournalTitle": "Computers"}, {"Title": "Online Multimodal Inference of Mental Workload for Cognitive Human Machine Systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "6", "Page": "81", "JournalTitle": "Computers"}, {"Title": "A Novel Multi-Modality Image Simultaneous Denoising and Fusion Method Based on Sparse Representation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "10", "Page": "129", "JournalTitle": "Computers"}, {"Title": "Towards Accurate Skin Lesion Classification across All Skin Categories Using a PCNN Fusion-Based Data Augmentation Approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "3", "Page": "44", "JournalTitle": "Computers"}, {"Title": "Multimodal tweet classification in disaster response systems using transformer-based bidirectional attention model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "2", "Page": "1607", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Dual-IDS: A bagging-based gradient boosting decision tree model for network anomaly intrusion detection system", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119030", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A proxy learning curve for the <PERSON><PERSON> classifier", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109240", "JournalTitle": "Pattern Recognition"}, {"Title": "EEGT: Energy Efficient Grid-Based Routing Protocol in Wireless Sensor Networks for IoT Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "5", "Page": "103", "JournalTitle": "Computers"}, {"Title": "Novel Deep Feature Fusion Framework for Multi-Scenario Violence Detection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "9", "Page": "175", "JournalTitle": "Computers"}, {"Title": "Video Summarization Based on Feature Fusion and Data Augmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "9", "Page": "186", "JournalTitle": "Computers"}, {"Title": "Detecting Breast Tumors in Tomosynthesis Images Utilizing Deep Learning-Based Dynamic Ensemble Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "11", "Page": "220", "JournalTitle": "Computers"}]}, {"ArticleId": 111864411, "Title": "Do Agricultural Knowledge and Innovation Systems Have the Dynamic Capabilities to Guide the Digital Transition of Short Food Supply Chains?", "Abstract": "<p>The digitalization of agriculture generates a new environment for the actors involved in agrifood production. In such a context, Agricultural Knowledge and Innovation Systems (AKISs) face the challenge of reconsidering their operational paradigms, redefining priorities, and designing strategies to achieve new aims. To do so, the actors participating in AKISs should develop and exploit a set of competencies known as dynamic capabilities, including the aptitude to sense the change in the external environment, the capacity to seize the opportunities that this change creates, and an ability to transform and adapt themselves to the new conditions that digitalization generates. In this study, using as examples the AKISs operating in Greece and Italy, we aimed to uncover if and how actors participating in these systems attempt and manage to deploy such capabilities. Based on a qualitative approach and drawing on data from two workshops, we discovered that seizing the opportunities sensed is a challenging task for AKIS actors. Our results also indicate that knowledge is a pivotal resource for AKISs, allowing actors to enhance their transformative capacity. However, to create a “collective” knowledge base, AKISs should ensure a functional connection between stakeholders and strengthen the roles of actors not actively engaged with the system, like public advisory organizations, universities, and technology providers.</p>", "Keywords": "", "DOI": "10.3390/info15010022", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Economics, School of Agriculture, Aristotle University of Thessaloniki, 54124 Thessaloniki, Greece; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Economics, School of Agriculture, Aristotle University of Thessaloniki, 54124 Thessaloniki, Greece"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Economics and Law, University of Cassino and Southern Lazio, 03043 Cassino, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Economics and Law, University of Cassino and Southern Lazio, 03043 Cassino, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Supply Chain Management, International Hellenic University, 60100 Katerini, Greece"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Economics and Law, University of Cassino and Southern Lazio, 03043 Cassino, Italy"}, {"AuthorId": 7, "Name": "Giuseppe La Rocca", "Affiliation": "Department of Economics and Law, University of Cassino and Southern Lazio, 03043 Cassino, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Agricultural and Food Sciences and Interdepartmental Center for Industrial Agri-Food Research (CIRI-AGRO), Alma Mater Studiorum—Università di Bologna, 40126 Bologna, Italy"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Supply Chain Management, International Hellenic University, 60100 Katerini, Greece; Corresponding author"}], "References": [{"Title": "Smart farming: Agriculture's shift from a labor intensive to technology native industry", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "", "Page": "100142", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 111864418, "Title": "Geometric Approach for Assessing the Sustainable Development of Territories", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/121230", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "A novel approach to traffic modelling based on road parameters, weather conditions and GPS data using feedforward neural networks", "Abstract": "This article presents the development of a estimation model using an artificial neural network to estimate the traffic factor parameter, which reflects changes in travel time for a single transport connection. The modeling of traffic is highly challenging due to the complex and non-linear nature of road systems, the interactions between diverse road users, and the ripple effects of congestion. The research problem addressed is the need to consider a wide range of factors that contribute to congestion, as identified in the literature. To address this challenge, a universal Feedforward Neural Network (FNN) was designed, specifically the Multilayer Perceptron (MLP) 38–10-1 model. The input layer neurons of the MLP receive a vector of input values representing various factors such as road types, technical properties, time of day, days of the week, incidents, weather conditions, and population density. The model was trained using a dataset consisting of 41,945 records of fully completed input variables, extracted from a larger dataset of 300,000 travel time measurements collected from a real transport network mapped on a macro scale (Mazovia, Poland). During the research, the model achieved a satisfactory level of Mean Absolute Percentage Error (MAPE) for the test set (9.12%) and a Normalized Root Mean Squared Error (NRMSE) below 0.02, indicating good estimation performance. Furthermore, this work proposes specialized models with the structure of MLP 34–10-1 for individual types of roads, which also demonstrated satisfactory estimation abilities. These models have practical applications in traffic management and planning. Overall, this research addresses the research problem of modeling factors for predicting travel time changes using an artificial neural network approach. The developed models provide accurate estimations and offer potential applications in transportation systems.", "Keywords": "Traffic ; Traffic Estimation ; Artificial Neural Networks ; Machine Learning ; Intelligent Transportation Systems ; GPS data", "DOI": "10.1016/j.eswa.2023.123067", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Security, Logistics and Management, Military University of Technology, gen. Sylwestra Kaliskiego Street 2, 00-908 Warsaw, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical Engineering, Military University of Technology, gen. Sylwestra Kaliskiego Street 2, 00-908 Warsaw, Poland"}], "References": [{"Title": "RETRACTED ARTICLE: Smart traffic management system in metropolitan cities", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "7529", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Deep spatio-temporal graph convolutional network for traffic accident prediction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "135", "JournalTitle": "Neurocomputing"}, {"Title": "Comparison of Min-Max normalization and Z-Score Normalization in the K-nearest neighbor (kNN) Algorithm to Test the Accuracy of Types of Breast Cancer", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "1", "Page": "13", "JournalTitle": "IJIIS: International Journal of Informatics and Information Systems"}, {"Title": "Forecasting vehicular traffic flow using MLP and LSTM", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "24", "Page": "17245", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Activation functions in deep learning: A comprehensive survey and benchmark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "503", "Issue": "", "Page": "92", "JournalTitle": "Neurocomputing"}, {"Title": "MLP-based Learnable Window Size for Bitcoin price prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "109584", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hybrid graph convolution neural network and branch-and-bound optimization for traffic flow forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "100", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Transparent deep machine learning framework for predicting traffic crash severity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "2", "Page": "1535", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An efficient multilayer RBF neural network and its application to regression problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "4133", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Uncertainty quantification and consideration in ML-aided traffic-driven service provisioning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "202", "Issue": "", "Page": "13", "JournalTitle": "Computer Communications"}, {"Title": "Switching-Like Event-Triggered State Estimation for Reaction–Diffusion Neural Networks Against DoS Attacks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "7", "Page": "8997", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 111865638, "Title": "Understanding the Complexity of Motivational Orientations towards Learning English among Pakistani Female University Students", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/130105", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 111865780, "Title": "FG-HFS: A feature filter and group evolution hybrid feature selection algorithm for high-dimensional gene expression data", "Abstract": "High dimensional and small samples characterize gene expression data and contain a large number of genes unrelated to disease. Feature selection improves the efficiency of disease diagnosis by selecting a small number of important genes. Unfortunately, existing algorithms do not consider the correlation between features, and search algorithms tend to fall into the local optimal solution in the feature search process. To this end, this paper proposes a feature filter and group evolution hybrid feature selection algorithm (FG-HFS) for high-dimensional gene expression data. Unlike existing algorithms, we propose using spectral clustering to group redundant features into a group. Then, we propose a redundant feature filter algorithm. According to the principle of approximate Markov blanket, grouped feature groups are filtered to delete these redundant features. Among them, filtered features are evenly divided by density according to the feature exponential strategy. Most importantly, we propose using the group evolution multi-objective genetic algorithm to search the filtered feature subsets and evaluate the candidate feature subsets according to the in-group and out-group so as to select the feature subsets with the highest accuracy and the least number. Experimental results show that the average accuracy (ACC) and Matthews correlation coefficient (MCC) indexes of the selected feature subsets (FSs) by the FG-HFS algorithm on 5 gene expression datasets are 92.76% and 88.76%, respectively, which are significantly better than the existing algorithms. In addition, the FSs and ACC/FSs indexes of the FG-HFS algorithm are also better than the existing algorithms, which fully proves the superiority of the FG-HFS algorithm. More importantly, the <PERSON><PERSON> and <PERSON> statistical experiments results show that the feature selection effect of FG-HFS algorithm is significantly better than that of existing algorithms, no matter in pairwise comparison or multiple comparison.", "Keywords": "Gene expression data ; Feature selection ; Spectral clustering ; Symmetric uncertainty ; Multi-objective genetic algorithm", "DOI": "10.1016/j.eswa.2023.123069", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Gynecologic Oncology, The First Affiliated Hospital of Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Gynecologic Oncology, The First Affiliated Hospital of Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biological Sciences, Xi’an Jiaotong-Liverpool University, Suzhou, Jiangsu 215123, China;School of Computing and Mathematical Sciences, University of Leicester, Leicester, LE1 7RH, UK"}, {"AuthorId": 6, "Name": "Junding Sun", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China;School of Computing and Mathematical Sciences, University of Leicester, Leicester, LE1 7RH, UK;School of Computer Science and Engineering, Southeast University, Nanjing, Jiangsu 210096, China;Corresponding author at: School of Computing and Mathematical Sciences, University of Leicester, Leicester, LE1 7RH, UK"}], "References": [{"Title": "Improved Salp Swarm Algorithm based on opposition based learning and novel local search algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113122", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An efficient unsupervised feature selection procedure through feature clustering", "Authors": "<PERSON>yang Yan; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "277", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Daily Activity Feature Selection in Smart Homes Based on Pearson Correlation Coefficient", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "2", "Page": "1771", "JournalTitle": "Neural Processing Letters"}, {"Title": "A NSGA2-LR wrapper approach for feature selection in network intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "172", "Issue": "", "Page": "107183", "JournalTitle": "Computer Networks"}, {"Title": "A k-NN method for lung cancer prognosis with the use of a genetic algorithm for feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113981", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fetal cardiac cycle detection in multi-resource echocardiograms using hybrid classification framework", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "825", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Feature selection method using improved CHI Square on Arabic text classifiers: analysis and application", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "10373", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "TAGA: Tabu Asexual Genetic Algorithm embedded in a filter/filter feature selection approach for high-dimensional data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "565", "Issue": "", "Page": "105", "JournalTitle": "Information Sciences"}, {"Title": "A cluster-based oversampling algorithm combining SMOTE and k-means for imbalanced medical data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "574", "JournalTitle": "Information Sciences"}, {"Title": "Improved binary particle swarm optimization for feature selection with new initialization and search space reduction strategies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107302", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hybrid filter-wrapper feature selection using whale optimization algorithm: A multi-objective approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115312", "JournalTitle": "Expert Systems with Applications"}, {"Title": "CS-BPSO: Hybrid feature selection based on chi-square and binary PSO algorithm for Arabic email authorship analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107224", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-label feature selection based on label correlations and feature redundancy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108256", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A multi-objective particle swarm optimization algorithm based on two-archive mechanism", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "", "Page": "108532", "JournalTitle": "Applied Soft Computing"}, {"Title": "A hybrid feature selection approach based on information theory and dynamic butterfly optimization algorithm for data classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "116621", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Classifying the multi-omics data of gastric cancer using a deep feature selection method", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "116813", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An ultrasound standard plane detection model of fetal head based on multi-task learning and hybrid knowledge graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "135", "Issue": "", "Page": "234", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Comparing filter and wrapper approaches for feature selection in handwritten character recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "168", "Issue": "", "Page": "39", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "CGUFS: A clustering-guided unsupervised feature selection algorithm for gene expression data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "9", "Page": "101731", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 111865839, "Title": "Author Index Volume 22 (2023)", "Abstract": "", "Keywords": "", "DOI": "10.1142/S1469026823990018", "PubYear": 2023, "Volume": "22", "Issue": "4", "JournalId": 17616, "JournalTitle": "International Journal of Computational Intelligence and Applications", "ISSN": "1469-0268", "EISSN": "1757-5885", "Authors": [], "References": []}, {"ArticleId": 111865849, "Title": "Internet-of-Things Traffic Analysis and Device Identification Based on Two-Stage Clustering in Smart Home Environments", "Abstract": "<p>Smart home environments, which consist of various Internet of Things (IoT) devices to support and improve our daily lives, are expected to be widely adopted in the near future. Owing to a lack of awareness regarding the risks associated with IoT devices and challenges in replacing or the updating their firmware, adequate security measures have not been implemented. Instead, IoT device identification methods based on traffic analysis have been proposed. Since conventional methods process and analyze traffic data simultaneously, bias in the occurrence rate of traffic patterns has a negative impact on the analysis results. Therefore, this paper proposes an IoT traffic analysis and device identification method based on two-stage clustering in smart home environments. In the first step, traffic patterns are extracted by clustering IoT traffic at a local gateway located in each smart home and subsequently sent to a cloud server. In the second step, the cloud server extracts common traffic units to represent IoT traffic by clustering the patterns obtained in the first step. Two-stage clustering can reduce the impact of data bias, because each cluster extracted in the first clustering is summarized as one value and used as a single data point in the second clustering, regardless of the occurrence rate of traffic patterns. Through the proposed two-stage clustering method, IoT traffic is transformed into time series vector data that consist of common unit patterns and can be identified based on time series representations. Experiments using public IoT traffic datasets indicated that the proposed method could identify 21 IoTs devices with an accuracy of 86.9%. Therefore, we can conclude that traffic analysis using two-stage clustering is effective for improving the clustering quality, device identification, and implementation in distributed environments.</p>", "Keywords": "", "DOI": "10.3390/fi16010017", "PubYear": 2024, "Volume": "16", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering and Science, Shibaura Institute of Technology, Tokyo 135-8548, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering and Science, Shibaura Institute of Technology, Tokyo 135-8548, Japan; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering and Science, Shibaura Institute of Technology, Tokyo 135-8548, Japan"}], "References": [{"Title": "A Survey of Security Vulnerability Analysis, Discovery, Detection, and Mitigation on IoT Devices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "27", "JournalTitle": "Future Internet"}, {"Title": "IoT Traffic: Modeling and Measurement Experiments", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "140", "JournalTitle": "IoT"}]}, {"ArticleId": 111865917, "Title": "Optimization Design and Experiment of High-Speed Drag-Reducing Trencher Based on Conservation Tillage", "Abstract": "<p>In the realm of high-speed precision broadcasting, the existing seeder opener proves inadequate for the speed of the seeding operation. We focus on the duckbill opener and employ the quadratic regression orthogonal rotation combination test design method to optimize the structural parameters of the opener. Throughout the experiment, the primary performance metrics encompassed the opener’s working resistance and the side dumping distance. The selected experimental factors comprised the penetration angle, the angle of soil entry gap, the shovel body width, and the shovel length. The optimal arrangement of structural parameters has been determined: a penetration angle, a soil entry gap angle, a shovel body width of 21 mm, and a shovel length of 142 mm. These parameters contribute to increased velocity, reduced operational resistance, and minimal soil disturbance. Under this combination, the relative deviations between the recorded measurements and the theoretical outcomes for working resistance and the side dumping distance stand at 4.24% and 1.06%, respectively; these confirm the credibility of the optimization results. We performed adaptability testing and conducted a comparative analysis under various operational conditions to assess the innovative opener’s ability to reduce force, minimize soil disruption, and maintain depth stability. The findings are as follows: At a depth of 5 cm and velocities ranging from 6 km/h to 8 km/h, an average working resistance reduction of 19.73%, a 5.64% decrease in the side dumping distance, and an average depth stability of 89.5% were observed. When operated at a speed of 7 km/h with a depth ranging from 3 cm to 5 cm, an average reduction of 19.66% in operational resistance, a 2.59% decrease in the side dumping distance, and an average depth stability of 91.1% were recorded. These results illustrate the innovative opener’s capacity to significantly reduce working resistance and side dumping distance while satisfying the depth stability requisites.</p>", "Keywords": "", "DOI": "10.3390/act13010016", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Agricultural Equipment Technology, Chinese Academy of Agricultural Mechanization Sciences, Beijing 100083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Agricultural Equipment Technology, Chinese Academy of Agricultural Mechanization Sciences, Beijing 100083, China"}, {"AuthorId": 3, "Name": "Yuxi Ji", "Affiliation": "National Key Laboratory of Agricultural Equipment Technology, Chinese Academy of Agricultural Mechanization Sciences, Beijing 100083, China"}, {"AuthorId": 4, "Name": "Shengbo Gao", "Affiliation": "National Key Laboratory of Agricultural Equipment Technology, Chinese Academy of Agricultural Mechanization Sciences, Beijing 100083, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Agricultural Equipment Technology, Chinese Academy of Agricultural Mechanization Sciences, Beijing 100083, China; Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Agricultural Equipment Technology, Chinese Academy of Agricultural Mechanization Sciences, Beijing 100083, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Agriculture and Rural Bureau of Zhoukou City, Zhoukou 466741, China"}, {"AuthorId": 8, "Name": "Xi<PERSON>", "Affiliation": "College of Agricultural Equipment Engineering, Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "College of Agricultural Equipment Engineering, Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Luoyang Jianguang Special Equipment Co., Ltd., Luoyang 471003, China"}], "References": []}, {"ArticleId": 111865934, "Title": "Solving Fuzzy Nonlinear Optimization Problems Using Null Set Concept", "Abstract": "<p>In the present paper, we propose a new method for minimizing the fuzzy single-objective function under fuzzy constraints. The algorithm of the method is based on the use of the null set concept. The null set concept allows us to use partial ordering for subtraction between fuzzy numbers, such as simple subtraction and the <PERSON><PERSON><PERSON> difference. From this, we have defined the types of solutions for a single-objective optimization problem, namely optimal solutions and H-optimal solutions. In practice, the method starts by turning the initial optimization problem into a deterministic nonlinear bi-objective optimization problem. Then, it uses <PERSON><PERSON><PERSON>’s optimality conditions to find the best solution of the bi-objective optimization problem. Finally, it deduces the solution to the initial problem using fuzzy algebraic operations to convert the deterministic solution into a fuzzy solution. Through some theorems, we have demonstrated that the obtained solutions by our method are optimal or H-optimal. Furthermore, the resolution of five examples of which a real-world problem has allowed us to compare our algorithm to other algorithms taken into the literature. With these results, our method can be seen as a good choice for solving a single-objective optimization problem where the objective and constraint functions are fuzzy.</p>", "Keywords": "Fuzzy nonlinear optimization; Null set; <PERSON><PERSON><PERSON> difference; Ranking function; Partial ordering; Convex cones; 03E72; 90C46; 90C70", "DOI": "10.1007/s40815-023-01626-7", "PubYear": 2024, "Volume": "26", "Issue": "2", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire de Mathématiques, Informatique et Applications, Université Norbert ZONGO, Koudougou, Burkina Faso"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire de Mathématiques, Informatique et Applications, Université Norbert ZONGO, Koudougou, Burkina Faso; Laboratoire d’Analyse Numérique, d’Informatique et de Biomathématique, Université Joseph KI-ZERBO, Ouagadougou, Burkina Faso; Corresponding author."}], "References": [{"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Prairie Dog Optimization Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "22", "Page": "20017", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 111865994, "Title": "Resource Indexing and Querying in Large Connected Environments", "Abstract": "<p>The proliferation of sensor and actuator devices in Internet of things (IoT) networks has garnered significant attention in recent years. However, the increasing number of IoT devices, and the corresponding resources, has introduced various challenges, particularly in indexing and querying. In essence, resource management has become more complex due to the non-uniform distribution of related devices and their limited capacity. Additionally, the diverse demands of users have further complicated resource indexing. This paper proposes a distributed resource indexing and querying algorithm for large connected environments, specifically designed to address the challenges posed by IoT networks. The algorithm considers both the limited device capacity and the non-uniform distribution of devices, acknowledging that devices cannot store information about the entire environment. Furthermore, it places special emphasis on uncovered zones, to reduce the response time of queries related to these areas. Moreover, the algorithm introduces different types of queries, to cater to various user needs, including fast queries and urgent queries suitable for different scenarios. The effectiveness of the proposed approach was evaluated through extensive experiments covering index creation, coverage, and query execution, yielding promising and insightful results.</p>", "Keywords": "", "DOI": "10.3390/fi16010015", "PubYear": 2024, "Volume": "16", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, E2S UPPA, LIUPPA, University Pau & Pays Adour, 64600 Anglet, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, E2S UPPA, LIUPPA, University Pau & Pays Adour, 64600 Anglet, France; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, E2S UPPA, LIUPPA, University Pau & Pays Adour, 40000 Mont de marsan, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Scient Analytics, 10 Impasse Grassi, 13100 Aix-en-Provence, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Almeria, 04120 Almeria, Spain"}], "References": [{"Title": "An adaptively multi-attribute index framework for big IoT data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "104841", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 111866255, "Title": "Provenance Verification of Smart Contracts: Analysing the Cost of Ensuring Authenticity over the Logic Hosted in Blockchain Networks", "Abstract": "<p>The lack of sufficient guarantee about the authenticity of running smart contracts is a major entry barrier to blockchain networks. Byauthenticity,we refer to the smart contract ownership or provenance; this implies perfect matching between a published source-code and the corresponding running version of a given smart contract. Block verifiers are services that check the provenance authenticity of the logic contained in blockchain networks. Nevertheless, as a block verifier is an external verification service, it consumes time to use it; and the derived overhead may not comply with temporal requirements of time-sensitive domains like cyber-physical systems. Such systems require that the temporal cost of using external services is assessed prior to the final system deployment. To the best of our knowledge, there are no previous contributions on the determination of the temporal cost of the smart-contract provenance verification process. This paper presents the design and implementation of a middleware that assesses the temporal overhead of accessing the verification services; the middleware is hosted in the global ledger and runs the verification services over large sets of smart contracts. Our contribution is validated by providing an implementation on a real blockchain network, employing actual smart contract verifier logic, and analysing the temporal behavior of the overall system operations to comply with the time-sensitive requirements of cyber-physical systems.</p>", "Keywords": "", "DOI": "10.3390/info15010024", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> García<PERSON>", "Affiliation": "These authors contributed equally to this work.; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>-C<PERSON><PERSON><PERSON>", "Affiliation": "These authors contributed equally to this work"}], "References": []}, {"ArticleId": 111866502, "Title": "Detection of DDoS Attacks using Enhanced FS with BRSA- based Deep Learning Model in IoT Environment", "Abstract": "", "Keywords": "", "DOI": "10.18576/isl/121231", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}]