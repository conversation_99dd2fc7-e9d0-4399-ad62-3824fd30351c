[{"ArticleId": 78771617, "Title": "Towards Knowledge Warehousing: Application to Smart Housing", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDATS.2021.10025610", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 27737, "JournalTitle": "International Journal of Data Analysis Techniques and Strategies", "ISSN": "1755-8050", "EISSN": "1755-8069", "Authors": [{"AuthorId": 1, "Name": "Habiba Drias", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 78771666, "Title": "Health$en$e<sup>TM</sup>: Developing a Board Game on Value-based Healthcare Financing", "Abstract": "Background. With rising healthcare costs, there is a need to transform healthcare financing to provide better care value and sustainability. Healthcare providers and consumers need to be educated about value-based care and financing. This can be done through games. Intervention & Methods. We describe the design of our board game Health$en$e TM which aims to let players simulate the role of funding patients’ care as the patients move across the care value chain. In the game, players will learn how certain care funding innovations help to optimize healthcare expenditure for better value. Discussion & Conclusion. We envisage that some game elements of Health$en$e TM may motivate players to transform healthcare financing systems in the real world. We formulate a matrix to predict the possible associations between game elements and psychological core drives relevant to Health$en$e TM . Further analysis of the game’s potential impact and validation of the matrix could be conducted in due course after the game is launched in a workshop.", "Keywords": "", "DOI": "10.1177/1046878119888710", "PubYear": 2020, "Volume": "51", "Issue": "1", "JournalId": 3394, "JournalTitle": "Simulation & Gaming", "ISSN": "1046-8781", "EISSN": "1552-826X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Healthcare Group (NHG), Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Healthcare Group (NHG), Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Healthcare Group (NHG), Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Healthcare Group (NHG), Singapore"}, {"AuthorId": 5, "Name": "Ho <PERSON>", "Affiliation": "National Healthcare Group (NHG), Singapore"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "National Healthcare Group (NHG), Singapore"}], "References": []}, {"ArticleId": 78771941, "Title": "Multiple-attribute decision making problems based on SVTNH methods", "Abstract": "<p>The neutrosophic set (NS) is a leading tool in modeling of situations involving incomplete, indeterminate and inconsistent information. The single-valued neutrosophic sets (SVNs) is more useful tool than neutrosophic sets in some applications of engineering and scientific problems. In this paper, we study Hamacher operations and operations between single-valued trapezoidal neutrosophic numbers. Then we propose the single-valued trapezoidal neutrosophic Hamacher weighted arithmetic averaging (SVTNHWA) operator, single-valued trapezoidal neutrosophic Hamacher ordered weighted arithmetic averaging (SVTNHOWA) operator, single-valued trapezoidal neutrosophic Hamacher hybrid weighted averaging (SVTNHHWA) operator, single-valued trapezoidal neutrosophic Hamacher weighted geometric averaging (SVTNHWGA) operator and single-valued trapezoidal neutrosophic Hamacher ordered weighted geometric averaging (SVTNHOWGA) operator and single-valued trapezoidal neutrosophic Hamacher hybrid weighted geometric averaging (SVTNHHWGA) operator, and obtain some of their properties. Furthermore, we developed a multiple-attribute decision-making method in single-valued trapezoidal neutrosophic (SVTN) environment based on these operators. Finally, we proposed an application of MADM problem in assessment of potential of software system commercialization.</p>", "Keywords": "Single-valued trapezoidal neutrosophic number; Hamacher operation; Arithmetic averaging operator; Geometric averaging operator; MADM method", "DOI": "10.1007/s12652-019-01568-9", "PubYear": 2020, "Volume": "11", "Issue": "9", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "Chiranjibe Jana", "Affiliation": "Department of Applied Mathematics with Oceanology and Computer Programming, Vidyasagar University, Midnapore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Tabuk, Tabuk, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics with Oceanology and Computer Programming, Vidyasagar University, Midnapore, India"}], "References": [{"Title": "Bipolar fuzzy Dombi prioritized aggregation operators in multiple attribute decision making", "Authors": "<PERSON><PERSON><PERSON><PERSON> Jana; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "5", "Page": "3631", "JournalTitle": "Soft Computing"}, {"Title": "A novel exponential distance and its based TOPSIS method for interval-valued intuitionistic fuzzy sets using connection number of SPA theory", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "1", "Page": "595", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 78771990, "Title": "Predicting transfer from a game-based learning environment", "Abstract": "This study examined the predictive impact of variables from self-regulated learning models on transfer following multiple gameplay sessions in a classroom game-based learning environment (GBLE). The game focused on science curriculum including map models, map navigation, and landforms. Fifth-grade students (N = 594) interacted with CRYSTAL ISLAND - UNCHARTED DISCOVERY during six 50-min sessions over four weeks integrated with classroom instruction. The transfer activity required students to create an island on grid paper that included at least seven different landforms, a map navigation activity created for peers, and an attached map scale model. Results of multiple regression analyses revealed that when other variables were accounted for, none of the measured motivational variables including interest for the game, performance-approach goal orientation, mastery-approach goal orientation, or self-efficacy for science were significant positive predictors of transfer. Interestingly, video game self-efficacy was a significant negative predictor of transfer performance. Prior knowledge and change in science knowledge after gameplay were also significant predictors of transfer. Girls performed as well as boys on the transfer task despite completing less game quests and had significantly lower posttest content scores. Interest was a consistent predictor of performance for girls but not boys for in-game performance, posttest content scores, and transfer scores. Regression models were systematically less able to predict performance moving from content knowledge, to in-game performance, to transfer. Implications for the design of GBLEs to facilitate transfer are discussed.", "Keywords": "Game-based learning ; Transfer ; Science ; Gender ; Motivation", "DOI": "10.1016/j.compedu.2019.103780", "PubYear": 2020, "Volume": "146", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "North Carolina State University, Teacher Education and Learning Sciences, 602D Poe Hall, Raleigh, NC, 27695, USA"}], "References": []}, {"ArticleId": 78772393, "Title": "eCAMI: simultaneous classification and motif identification for enzyme annotation", "Abstract": "Abstract Motivation <p>Carbohydrate-active enzymes (CAZymes) are extremely important to bioenergy, human gut microbiome, and plant pathogen researches and industries. Here we developed a new amino acid k-mer-based CAZyme classification, motif identification and genome annotation tool using a bipartite network algorithm. Using this tool, we classified 390 CAZyme families into thousands of subfamilies each with distinguishing k-mer peptides. These k-mers represented the characteristic motifs (in the form of a collection of conserved short peptides) of each subfamily, and thus were further used to annotate new genomes for CAZymes. This idea was also generalized to extract characteristic k-mer peptides for all the Swiss-Prot enzymes classified by the EC (enzyme commission) numbers and applied to enzyme EC prediction.</p> Results <p>This new tool was implemented as a Python package named eCAMI. Benchmark analysis of eCAMI against the state-of-the-art tools on CAZyme and enzyme EC datasets found that: (i) eCAMI has the best performance in terms of accuracy and memory use for CAZyme and enzyme EC classification and annotation; (ii) the k-mer-based tools (including PPR-Hotpep, CUPP and eCAMI) perform better than homology-based tools and deep-learning tools in enzyme EC prediction. Lastly, we confirmed that the k-mer-based tools have the unique ability to identify the characteristic k-mer peptides in the predicted enzymes.</p> Availability and implementation <p>https://github.com/yinlabniu/eCAMI and https://github.com/zhanglabNKU/eCAMI.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz908", "PubYear": 2020, "Volume": "36", "Issue": "7", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Artificial Intelligence, Nankai University, Tianjin 300071, China;College of Computer Science, Nankai University, Tianjin 300071, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Artificial Intelligence, Nankai University, Tianjin 300071, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Food Science and Technology, Nebraska Food for Health Center, University of Nebraska, Lincoln, NE 68588, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematical Sciences, Northern Illinois University, DeKalb, IL 60115, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Food Science and Technology, Nebraska Food for Health Center, University of Nebraska, Lincoln, NE 68588, USA"}], "References": []}, {"ArticleId": 78773079, "Title": "Evaluation and Research on Financial Competitiveness of Innovation-Driven Enterprises Based on Interval Data Mining", "Abstract": "<p>By studying the issue of evaluating the financial competitiveness of innovation-driven enterprises, the author proposed a new financial competitiveness evaluation method based on interval data mining. First, in order to effectively utilize the evaluation information that is provided by experts, the author suggested that the 95% confidence interval of the expert group’s evaluation information should be used as the interval evaluation data. Hence, the uncertainty of the evaluation process was effectively reduced, and the reliability of the evaluation results was improved. Then, empirical analysis was conducted on the financial competitiveness of an innovation-driven enterprise and its competitors using the interval data as the mining object, and a financial competitiveness evaluation method was given. The financial competitiveness level of this innovation-driven enterprise was analyzed according to the influence factors and overall situations, and suggestions to improve the innovation-driven enterprise’s financial competitiveness were provided in a targeted way. Finally, a discussion was made on how to strengthen the financial competitiveness of the innovation-driven enterprise with respect to five aspects.</p>", "Keywords": "", "DOI": "10.1142/S0218001420590405", "PubYear": 2020, "Volume": "34", "Issue": "12", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> International Business School, Universiti Teknologi Malaysia, UTM 54100, Kuala Lumpur, Malaysia;School of Economics and Management, Chuzhou University, Chuzhou 239000, Anhui, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> International Business School, Universiti Teknologi Malaysia, UTM 54100, Kuala Lumpur, Malaysia"}], "References": []}, {"ArticleId": 78773419, "Title": "Causation, levels of analysis and explanation in systems ergonomics – A Closer Look at the UK NHS Morecambe Bay investigation", "Abstract": "This paper extends an earlier examination of the concept of ‘mesoergonomics’ (<PERSON><PERSON><PERSON> et al., 2014) and its application to Human Factors/Ergonomics (HFE). <PERSON><PERSON><PERSON> et al. (2014) developed a framework for mesoergonomic inquiry based on a set of steps and questions, the purpose of which was to encourage researchers to cross system levels in the studies (e.g., organisation-group-individual levels of analysis) and to explore alternative causal mechanisms and relationships within their data. The present paper further develops the framework and draws on previous work across a diverse range of sources (safety science, systems theory, the sociology of disaster and ethology) which has examined the subject of accident causation, levels of analysis and explanatory factors contributing to system failure. The outcomes from this exercise are a revised framework which seeks to explore what we term ‘isomorphisms’ and includes questions covering: (a) how internal isomorphisms develop or evolve within the system; and, (b) how these isomorphisms are shaped by cultural, professional and other forms of external influence. The workings of the revised framework are illustrated through using the example of the UK NHS Morecambe Bay Investigation (Kirkup, 2015). The paper concludes with a summary of ways forward for the framework, as well as new directions for theory within systems ergonomics/human factors.", "Keywords": "Causality and human factors/ergonomics;Mesoergonomics;Systems ergonomics", "DOI": "10.1016/j.apergo.2019.103011", "PubYear": 2020, "Volume": "84", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Human Factors and Complex Systems Group, School of Design and Creative Arts, Loughborough University, Loughborough, LE11 3TU, UK"}], "References": []}, {"ArticleId": 78773421, "Title": "Mobile device security defense method and system based on address jump using sliding window technology", "Abstract": "<p>In order to solve the problem that IP address jump cannot be synchronized in real time due to the unstable speed of network data transmission in the Internet of Things system, in this paper, sliding window mechanism is introduced into network communication, and an Internet of Things system with network IP address jump function is constructed. The system adopts time synchronization scheme based on PTP protocol and IP address hopping strategy based on sliding window. The system includes three modules: time synchronization module based on PTP protocol to enhance security performance, address hopping module based on OpenFlow as core technology, and SDN routing module. The system can realize the security defense function of mobile devices on the Internet of Things. Finally, through the experimental test, it is found that, when the system time error is less than 18 s, the system's packet loss rate is stable and low. The anti-attack ability is very strong.</p>", "Keywords": "address jump;OpenFlow;SDN;sliding window", "DOI": "10.1002/cpe.5625", "PubYear": 2022, "Volume": "34", "Issue": "14", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Changzhou Vocational Institute of Engineering  Changzhou China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Logistics Management and Engineering Nanning Normal University  Nanning China"}], "References": []}, {"ArticleId": ********, "Title": "Design and Development of a Novel 2-Degree-of-Freedom Parallel Robot", "Abstract": "Summary <p>Parallel robots are widely used in the fields of manufacturing, medical science, education, scientific research, etc. Many studies have been conducted on the topic already. However, shortcomings still exist, especially in certain situations. To meet the demand of good speed and load performances at the same time, this work presents a novel 2-degree-of-freedom parallel robot. The structural design, static, stiffness, and reachable workspace analysis of the robot are given in the manuscript. Experiment regarding the accuracy and speed performance is conducted, and the results are provided. In the end, potential applications of the proposed robot are suggested.</p>", "Keywords": "2-DOF parallel robot;Workspace;Servomotor", "DOI": "10.1017/S0263574719000419", "PubYear": 2020, "Volume": "38", "Issue": "1", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "Chang<PERSON> Cheng", "Affiliation": "School of Mechanical and Electric Engineering,Guangzhou University,Guangzhou 510006, P.R.,China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Huang", "Affiliation": "School of Mechanical and Electric Engineering,Guangzhou University,Guangzhou 510006, P.R.,China,↑Center for Research on Leading Technology of Special Equipment, School of Mechanical and Electric Engineering,Guangzhou University,Guangzhou 510006, P.R.,China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electric Engineering,Guangzhou University,Guangzhou 510006, P.R.,China"}], "References": []}, {"ArticleId": 78773648, "Title": "PAC: A novel self-adaptive neuro-fuzzy controller for micro aerial vehicles", "Abstract": "There exists an increasing demand for a flexible and computationally efficient controller for micro aerial vehicles (MAVs) due to a high degree of environmental perturbations. In this work, an evolving neuro-fuzzy controller, namely Parsimonious Controller (PAC) is proposed. It features fewer network parameters than conventional approaches due to the absence of rule premise parameters. PAC is built upon a recently developed evolving neuro-fuzzy system known as parsimonious learning machine (PALM) and adopts new rule growing and pruning modules derived from the approximation of bias and variance. These rule adaptation methods have no reliance on user-defined thresholds, thereby increasing the PAC’s autonomy for real-time deployment. PAC adapts the consequent parameters with the sliding mode control (SMC) theory in the single-pass fashion. The boundedness and convergence of the closed-loop control system’s tracking error and the controller’s consequent parameters are confirmed by utilizing the La<PERSON><PERSON>–<PERSON><PERSON> theorem. Lastly, the controller’s efficacy is evaluated by observing various trajectory tracking performance from a bio-inspired flapping wing micro aerial vehicle (BI-FWMAV) and a rotary wing micro aerial vehicle called hexacopter. Furthermore, it is compared to three distinctive controllers. Our PAC outperforms the linear PID controller and feed-forward neural network (FFNN) based nonlinear adaptive controller. Compared to its predecessor, G-controller, the tracking accuracy is comparable, but the PAC incurs significantly fewer parameters to attain similar or better performance than the G-controller.", "Keywords": "Micro aerial vehicle ; Neuro-fuzzy ; Parsimonious learning machine ; Self-adaptive", "DOI": "10.1016/j.ins.2019.10.001", "PubYear": 2020, "Volume": "512", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Information Technology, UNSW Canberra, ACT 2612, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanyang Technological University Singapore, 639798, Singapore;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Information Technology, UNSW Canberra, ACT 2612, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Engineering and Information Technology, UNSW Canberra, ACT 2612, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Knowledge Based Mathematical Systems, Johannes Kepler University, Linz, Austria"}], "References": []}, {"ArticleId": 78773728, "Title": "A Methodological Critique of the Interpretive Ranking Process for Examining IS Project Failure", "Abstract": "This research critically analyzes the Interpretive Ranking Process (IRP) using an illustrative empirically derived IS project failure related case study to articulate a deeper understanding of the method. The findings emphasize the suitability of the method for a number of practical applications, but also highlight the limitations for larger matrix sized problems. The IRP process to derive the dominance between IS project failure factors is judged to be methodical and systematic, enabling the development of clear dominating interactions.", "Keywords": "IS project failure ; interpretive ranking process ; factor interrelationships ; factor dominance", "DOI": "10.1080/10580530.2019.1696588", "PubYear": 2020, "Volume": "37", "Issue": "2", "JournalId": 21771, "JournalTitle": "Information Systems Management", "ISSN": "1058-0530", "EISSN": "1934-8703", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Emerging Markets Research Centre (EMaRC), School of Management, Swansea University Bay Campus, Swansea, Wales, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Emerging Markets Research Centre (EMaRC), School of Management, Swansea University Bay Campus, Swansea, Wales, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, University of Bradford, Bradford, UK"}], "References": []}, {"ArticleId": 78773863, "Title": "A Benchmarking of DCM-Based Architectures for Position, Velocity and Torque-Controlled Humanoid Robots", "Abstract": "This paper contributes toward the benchmarking of control architectures for bipedal robot locomotion. It considers architectures that are based on the Divergent Component of Motion (DCM) and composed of three main layers: trajectory optimization, simplified model control, and whole-body quadratic programming (QP) control layer. While the first two layers use simplified robot models, the whole-body QP control layer uses a complete robot model to produce either desired positions, velocities, or torques inputs at the joint-level. This paper then compares two implementations of the simplified model control layer, which are tested with position, velocity, and torque control modes for the whole-body QP control layer. In particular, both an instantaneous and a Receding Horizon controller are presented for the simplified model control layer. We show also that one of the proposed architectures allows the humanoid robot iCub to achieve a forward walking velocity of 0.3372[Formula: see text]m/s, which is the highest walking velocity achieved by the iCub robot.", "Keywords": "", "DOI": "10.1142/S0219843619500348", "PubYear": 2020, "Volume": "17", "Issue": "1", "JournalId": 16624, "JournalTitle": "International Journal of Humanoid Robotics", "ISSN": "0219-8436", "EISSN": "1793-6942", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dynamic Interaction Control, Istituto Italiano di Tecnologia, Genoa, Italy;DIBRIS, University of Genoa, Genoa, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dynamic Interaction Control, Istituto Italiano di Tecnologia, Genoa, Italy;DIBRIS, University of Genoa, Genoa, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Intelligent Systems Research Institute, National Institute of Advanced Industrial Science and Technology, Tsukuba, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dynamic Interaction Control, Istituto Italiano di Tecnologia, Genoa, Italy;DIBRIS, University of Genoa, Genoa, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dynamic Interaction Control, Istituto Italiano di Tecnologia, Genoa, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dynamic Interaction Control, Istituto Italiano di Tecnologia, Genoa, Italy"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Dynamic Interaction Control, Istituto Italiano di Tecnologia, Genoa, Italy"}], "References": []}, {"ArticleId": 78774193, "Title": "Study on the prediction of stock price based on the associated network model of LSTM", "Abstract": "Abstract Stock market has received widespread attention from investors. It has always been a hot spot for investors and investment companies to grasp the change regularity of the stock market and predict its trend. Currently, there are many methods for stock price prediction. The prediction methods can be roughly divided into two categories: statistical methods and artificial intelligence methods. Statistical methods include logistic regression model, ARCH model, etc. Artificial intelligence methods include multi-layer perceptron, convolutional neural network, naive Bayes network, back propagation network, single-layer LSTM, support vector machine, recurrent neural network, etc. But these studies predict only one single value. In order to predict multiple values in one model, it need to design a model which can handle multiple inputs and produces multiple associated output values at the same time. For this purpose, it is proposed an associated deep recurrent neural network model with multiple inputs and multiple outputs based on long short-term memory network. The associated network model can predict the opening price, the lowest price and the highest price of a stock simultaneously. The associated network model was compared with LSTM network model and deep recurrent neural network model. The experiments show that the accuracy of the associated model is superior to the other two models in predicting multiple values at the same time, and its prediction accuracy is over 95%.", "Keywords": "Deep learning; Machine learning; Long short-term memory (LSTM); Deep recurrent neural network; Associated network", "DOI": "10.1007/s13042-019-01041-1", "PubYear": 2020, "Volume": "11", "Issue": "6", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, China"}, {"AuthorId": 2, "Name": "Liangxi Qin", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, China"}], "References": []}, {"ArticleId": 78774602, "Title": "Effect of gravity in wheel/terrain interaction models", "Abstract": "<p>Predicting the motion of wheeled robots in unstructured environments is an important and challenging problem. The study of planetary exploration rovers on soft terrain introduces the additional need to consider the effect of nonterrestrial gravitational fields on the forces and torques developed at the wheel/terrain interface. Simply reducing the wheel load under Earth's gravity overestimates the traveled distance and predicts better performance than is actually observed in reduced‐gravity measurements. In this paper, we study the effect of gravity on wheel/terrain interaction. Experiments were conducted to assess the effect of reduced gravity on the velocity profile of the soil under the wheel, as well as on the traction force and sinkage developed by the wheel. It was shown that in the velocity field of the soil, the decay of the tangential velocity component becomes gradual with reducing gravity, and the decay of the normal to rim velocity is slower in Lunar gravity. It was also found that wheel flexibility can have an important effect on the dynamics as the contact patch and effective radius vary periodically. These results were then used together with traditional semiempirical terramechanics models to determine and validate the simulated drawbar pull values. The developed simulation model includes the effect of wheel flexibility, dynamic sinkage, and gravity.</p>", "Keywords": "modeling and simulation;reduced gravity experiment;velocity field;wheel flexibility;wheel–soil interaction", "DOI": "10.1002/rob.21924", "PubYear": 2020, "Volume": "37", "Issue": "5", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "László L<PERSON>", "Affiliation": "Department of Mechanical Engineering, McGill University, Montréal, Québec, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, McGill University, Montréal, Québec, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratorio de Ingeniería Mecánica, Department of Naval and Industrial Engineering, University of A Coruña, Ferrol, Spain"}, {"AuthorId": 4, "Name": "Parna Niksirat", "Affiliation": "Department of Electrical and Computer Engineering, Concordia University, Montréal, Québec, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Concordia University, Montréal, Québec, Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, McGill University, Montréal, Québec, Canada"}], "References": []}, {"ArticleId": 78774635, "Title": "A barrier to innovation: Europe’s ad-hoc cross-border framework for testing prototype autonomous vehicles", "Abstract": "The conglomeration of regulatory frameworks for the testing of prototype autonomous vehicles in Europe creates a challenging task for developers and researchers planning pilots across borders. While there are examples of international autonomous driving projects and cooperation in autonomous vehicle research, Europe lacks a mutually recognised testing procedure for autonomous vehicle pilots, and incompatible legal and administrative processes in each country creates a disincentive for ambitious cross-border testing. The diverse climate and topography of Europe potentially provides a rigorous testing ground for autonomous vehicles, and an opportunity to prepare the new technology to deal with varied signage, language and driver behaviour encountered when travelling across multiple countries. Prototype vehicles tested in such conditions provide valuable insight for research and product development. This may be encouraged by a more harmonised prototype testing framework including a pan-European type-approval exemption scheme for prototype vehicles, and for cross-border tests to be coordinated by regional organisations interested in promoting development in border areas.", "Keywords": "Autonomous vehicles ; cross-border ; regulatory sandbox", "DOI": "10.1080/13600869.2019.1696651", "PubYear": 2020, "Volume": "34", "Issue": "1", "JournalId": 25543, "JournalTitle": "International Review of Law, Computers & Technology", "ISSN": "1360-0869", "EISSN": "1364-6885", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Transport Studies, University of Leeds, Leeds, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Institute for Transport Studies, University of Leeds, Leeds, UK"}], "References": []}, {"ArticleId": 78774665, "Title": "A wrapper approach-based key temperature point selection and thermal error modeling method", "Abstract": "<p>A wrapper approach-based key temperature point selection and thermal error modeling method is proposed to concurrently screen the optimal key temperature points and construct the thermal error model. This wrapper approach can strengthen the intrinsic relation between the key temperature points and the thermal error model to ensure the strong prediction performance. On the whole, the least squares support vector machine (SVM) is used as the basic thermal error modeling method and the binary bat algorithm (BBA) is used as the optimization algorithm. The selection status of temperature points and the values of hyperparameters γ and σ <sup>2</sup> of SVM are coded in separate binary parts of the artificial bat’s position vector of BBA. The cost function is designed by balancing the prediction error and the number of key temperature points. For verification, the thermal error experiment was conducted on a horizontal machining center. Feeding the collected experimental temperature data and thermal error data to the proposed method, three optimal key temperature points were screened out and the corresponding optimal hyperparameters were simultaneously searched. To verify the superiority of the proposed method, the prediction performance comparison analysis was conducted with the conventional filter-based method. Specifically, in the conventional method, the key temperature points were screened by combining fuzzy c means (FCM) clustering and correlation analysis, and the multiple linear regression (MLR), the backpropagation neural network (BPNN), and the SVM were used to build the thermal error model, respectively. Comparison results showed that the prediction accuracy of the proposed method increased by up to 44.0% compared to the conventional method, which suggests the superior prediction performance of the proposed method.</p>", "Keywords": "Key temperature points; Thermal error; Support vector machine; Binary bat algorithm; Prediction performance", "DOI": "10.1007/s00170-019-04647-5", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Advanced Manufacturing Engineering, Chongqing University of Posts and Telecommunications, Chongqing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 78774700, "Title": "An 89% solution adoption rate at a two-year follow-up: evaluating the effectiveness of an animated agricultural video approach", "Abstract": "Securing the adoption of scalable agro-educational information and communication technology (ICT) solutions by farmers remains one of the international development community’s most elusive goals – in part due to two key gaps in the data: (1) limited comparisons of competing knowledge-delivery methods, and (2) few to no follow-ups on long-term knowledge retention and solution adoption. Addressing both of these gaps, this follow-up study measures farmer knowledge retention and solution adoption two years after being trained on an improved postharvest bean storage method in northern Mozambique. The results found animated-video knowledge delivery at least as effective as a traditional extension approach for knowledge retention (97.9%) and solution adoption (89%). As animated video can more cost-effectively reach the widest – even geographically isolated – populations, it readily complements extension services and international development community efforts to secure knowledge transfer and recipient buy-in for innovations. Implications and future research for adult learning are also discussed.", "Keywords": "Adoption and diffusion of IT and rate of uptake ; development issues ; scalable infrastructures for development ; development Issues ; sustainable development in developing and transition economies ; development issues", "DOI": "10.1080/02681102.2019.1697632", "PubYear": 2020, "Volume": "26", "Issue": "3", "JournalId": 20356, "JournalTitle": "Information Technology for Development", "ISSN": "0268-1102", "EISSN": "1554-0170", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": "Department of Food Science and Human Nutrition, Michigan State University, East Lansing, MI, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Greenlee School of Journalism and Communication, Iowa State University, Ames, IA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Agricultural Research Institute of Mozambique (IIAM), Maputo, Mozambique"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Agricultural Research Institute of Mozambique (IIAM), Maputo, Mozambique"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Sociology, Iowa State University, Ames, IA, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Entomology, Michigan State University, East Lansing, MI, USA"}], "References": []}, {"ArticleId": 78774741, "Title": "An enhanced cuckoo optimization algorithm for task graph scheduling in cluster-computing systems", "Abstract": "<p>Optimized task scheduling is key to achieve high performance in the cluster-computing systems whose application is broad ranging from scientific to the military purposes. This combinatorial problem is NP-hard from the time complexity perspective, where applying newly proposed metaheuristics to it deserves further investigation based on the well-known no-free-lunch theorem. Accordingly, in this paper, an enhanced version of cuckoo optimization algorithm (COA) named E-COA is proposed to cope with the static task scheduling problem in the mesh topology cluster-computing environments. The proposed approach is equipped with an efficient adaptive semi-stochastic egg-laying strategy that significantly improves the local and global search potentiality of the basic COA. The experiments on a comprehensive set of randomly generated task graphs with different structural parameters reveal the efficiency of the proposed approach from the performance point of view, especially for the small-scale samples, and where the number of clusters in the machine is very restricted, i.e., we are in the lack of computational resource. </p>", "Keywords": "Cluster-computing environments; Mesh topology; Task graph scheduling; Cuckoo optimization algorithm (COA); Metaheuristics", "DOI": "10.1007/s00500-019-04520-3", "PubYear": 2020, "Volume": "24", "Issue": "13", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Gotvand, Shoushtar Branch, Islamic Azad University, Shoushtar, Iran"}], "References": []}, {"ArticleId": 78774772, "Title": "Quantitative analysis of relative active tectonics using geomorphic indices in Band-Golestan basin, northeastern Iran", "Abstract": "<p>Investigation of geomorphic indices is a useful approach to identify the specific characteristics of active tectonics in each region. In this study, five geomorphic indices including stream length-gradient index, basin asymmetry, hypsometric integral, ratio of valley-floor width, and index of basin shape were estimated to assess relative active tectonics in the Band-Golestan basin, as a part of Binaloud mountainous zone, located in northeastern Iran. This region includes several faults, formed mainly by contact, fraction, and re-crystallization processes between shale and sandstone rocks. An average index of the active tectonics was produced to categorize the intensity classes of tectonic activity of the study area. According to the mean values of indices, the whole basin has active or semi-active tectonics. After that, the overall index of Iat with a value of 1.88 indicated the high class of tectonic activity for the study area. Furthermore, the obtained result of tectonic activity was confirmed by fieldwork and previous literature. In this regard, the active tectonics of the study area has corresponded to the highest values of sediment yield, soil losses, dynamic failures, and various types of landslide events.</p>", "Keywords": "Geomorphic indices; Active tectonics; Drainage basin; Iran", "DOI": "10.1007/s41324-019-00303-y", "PubYear": 2020, "Volume": "28", "Issue": "4", "JournalId": 2564, "JournalTitle": "Spatial Information Research", "ISSN": "2366-3286", "EISSN": "2366-3294", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical Geography, Ferdowsi University of Mashhad, Mashhad, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physical Geography, Ferdowsi University of Mashhad, Mashhad, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Environment and Natural Resources, Ferdowsi University of Mashhad, Mashhad, Iran"}], "References": []}, {"ArticleId": 78774776, "Title": "A two-level facility location and sizing problem for maximal coverage", "Abstract": "This paper presents a two-stage hierarchical location problem for systems where the lower level facilities act as the first points contact for the customers while the upper level facilities act as suppliers of the lower level facilities that either serve them or provide advanced services to customers. Furthermore, more recent and realistic coverage constructs such as gradual and cooperative covering are included in our setting. Although our problem can be applicable in various settings, the most fitting application is in wireless telecommunication networks to determine the location of base stations and mobile switching centers. We have developed two competing formulations for the problem, each of which involve nonlinear components that are difficult to deal with. We then develop their respective linearizations and tested their performances. These formulations are solved by commercial optimizers for a set of reasonably large problem instances and it is found that majority of the problems can be solved within a maximum of 10% optimality gap within a short time.", "Keywords": "Hierarchical location ; Maximal coverage ; Gradual covering", "DOI": "10.1016/j.cie.2019.106204", "PubYear": 2020, "Volume": "139", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Industrial Engineering Department, Naval Academy, National Defense University, Istanbul 34940, Turkey;Industrial Engineering Department, Bahcesehir University, Istanbul 34353, Turkey;Corresponding author at: Industrial Engineering Department, Naval Academy, National Defense University, Istanbul 34940, Turkey."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Sabanci Business School, Sabanci University, Istanbul 34956, Turkey"}], "References": []}, {"ArticleId": 78775017, "Title": "Two‐stage stochastic minimum s  −  t cut problems: Formulations, complexity and decomposition algorithms", "Abstract": "<p>We introduce the two‐stage stochastic minimum s − t cut problem. Based on a classical linear 0‐1 programming model for the deterministic minimum s − t cut problem, we provide a mathematical programming formulation for the proposed stochastic extension. We show that its constraint matrix loses the total unimodularity property, however, preserves it if the considered graph is a tree. This fact turns out to be not surprising as we prove that the considered problem is ‐hard in general, but admits a linear time solution algorithm when the graph is a tree. We exploit the special structure of the problem and propose a tailored Benders decomposition algorithm. We evaluate the computational efficiency of this algorithm by solving the Benders dual subproblems as max‐flow problems. For many tested instances, we outperform a standard <PERSON><PERSON> decomposition by two orders of magnitude with the <PERSON><PERSON> decomposition exploiting the max‐flow structure of the subproblems.</p>", "Keywords": "Benders decomposition;combinatorial optimization;complexity;minimum s − t cut problem;total unimodularity;two‐stage stochastic programming", "DOI": "10.1002/net.21922", "PubYear": 2020, "Volume": "75", "Issue": "3", "JournalId": 7540, "JournalTitle": "Networks", "ISSN": "0028-3045", "EISSN": "1097-0037", "Authors": [{"AuthorId": 1, "Name": "Steffen Rebennack", "Affiliation": "Department of Stochastic Optimization, Institute for Operations Research, Karlsruhe Institute of Technology, Karlsruhe, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Pittsburgh, Pittsburgh, Pennsylvania"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Singh", "Affiliation": "Department of Stochastic Optimization, Institute for Operations Research, Karlsruhe Institute of Technology, Karlsruhe, Germany; Discrete Mathematics, Friedrich‐Alexander‐Universität Erlangen‐Nürnberg, Erlangen, Germany"}], "References": []}, {"ArticleId": 78775245, "Title": "Asynchronous vs. synchronous interfacing to time-triggered communication systems", "Abstract": "Time-triggered communication facilitates the construction of multi-component real-time systems whose components are in control of their temporal behaviour. However, the interface of a time-triggered communication system has to be accessed with care, to avoid that the temporal independence of components gets lost. This paper shows two interfacing strategies, one for asynchronous interface access (in two variants, one being the new Rate-bounded Non-Blocking Communication protocol) and one for time-aware, synchronized interface access, that allow components to maintain temporal independence. The paper describes and compares these interfacing strategies.", "Keywords": "Networking ; Real-time systems ; Time-triggered communication ; Non-blocking communication", "DOI": "10.1016/j.sysarc.2019.101690", "PubYear": 2020, "Volume": "103", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Vienna University of Technology, Vienna, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Hertfordshire, Hatfield, United Kingdom;Corresponding author."}], "References": []}, {"ArticleId": 78775302, "Title": "Data on prevalence, distribution and risk factors for Foot and Mouth Disease in grazing cattle in haor areas of Bangladesh", "Abstract": "<p>Foot and mouth disease (FMD) is a highly contagious and devastating viral disease among all cloven-footed animals. In Bangladesh, the disease is endemic, with outbreaks occurring throughout the year in the haor regions. Thus, the FMD outbreaks impact livelihoods in the haor area and are of great concern. Therefore, a cross-sectional study was undertaken to evaluate the prevalence, distribution, and risk factors for clinical FMD in some selected areas of haor in Sylhet division of Bangladesh. We examined 1,388 cattle, of which 343 were clinically affected with FMD (prevalence 24.71%, CI95% = 22.44 - 26.98) during the period from July 2017 through June 2018. Though production loss was observed, no mortality was recorded in the infected animals. The data article shows the spatial distribution of FMD prevalence. The temporal pattern indicates a higher number of FMD cases in June (47.01%, CI95% = 38.97 - 55.07). The gender was found associated (OR = 2.98; p &lt; 0.001) with the potential risk of FMD occurrence through univariate analysis. Besides, indigenous breeds of cattle (OR = 2.83; p &lt; 0.001) are found to be more susceptible to FMD compared to exotic and crossbreeds. The risk factors identified in this article will serve as a baseline for the development of risk based FMD control program in future.</p><p>© 2019 The Author(s).</p>", "Keywords": "Bangladesh;Cattle;Distribution;FMD;Haor areas;Prevalence;Risk factors", "DOI": "10.1016/j.dib.2019.104843", "PubYear": 2020, "Volume": "28", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Medicine, Sylhet Agricultural University, Sylhet, 3100, Bangladesh."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Epidemiology and Public Health, Sylhet Agricultural University, Sylhet, 3100, Bangladesh."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Upazila Veterinary Hospital, Sulla, Sunamganj, Bangladesh."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Medicine, Sylhet Agricultural University, Sylhet, 3100, Bangladesh."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Medicine, Sylhet Agricultural University, Sylhet, 3100, Bangladesh."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Aquatic Resource Management, Sylhet Agricultural University, Sylhet, 3100, Bangladesh."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Epidemiology and Public Health, Sylhet Agricultural University, Sylhet, 3100, Bangladesh."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Medicine, Sylhet Agricultural University, Sylhet, 3100, Bangladesh."}], "References": []}, {"ArticleId": 78775346, "Title": "Advanced orthogonal learning-driven multi-swarm sine cosine optimization: Framework and case studies", "Abstract": "Sine cosine algorithm (SCA) is a widely used nature-inspired algorithm that is simple in structure and involves only a few parameters. For some complex tasks, especially high-dimensional problems and multimodal problems, the basic method may have problems in harmonic convergence or trapped into local optima. To efficiently alleviate this deficiency, an improved variant of basic SCA is proposed in this paper. The orthogonal learning, multi-swarm, and greedy selection mechanisms are utilized to improve the global exploration and local exploitation powers of SCA. In preference, the orthogonal learning procedure is introduced into the conventional method to expand its neighborhood searching capabilities. Next, the multi-swarm scheme with three sub-strategies is adopted to enhance the global exploration capabilities of the algorithm. Also, a greedy selection strategy is applied to the conventional approach to improve the qualities of the search agents. Based on these three strategies, we called the improved SCA as OMGSCA. The proposed OMGSCA is compared with a comprehensive set of meta-heuristic algorithms including six other improved SCA variants, basic version, and ten advanced meta-heuristic algorithms. We employed thirty IEEE CEC2014 benchmark functions, and eight advanced meta-heuristic algorithms on seventeen real-world benchmark problems from IEEE CEC2011. Also, non-parametric statistical Wilcoxon sign rank and the Friedman tests are performed to monitor the performance of the proposed method. The obtained experimental results demonstrate that the introduced strategies can significantly improve the exploratory and exploitative inclinations of the basic algorithm. The convergence speed of the original method has also been improved, substantially. The results suggest the proposed OMGSCA can be used as an effective and efficient auxiliary tool for solving complex optimization problems.", "Keywords": "Sine cosine algorithm ; Orthogonal learning ; Multi-swarm ; Greedy selection", "DOI": "10.1016/j.eswa.2019.113113", "PubYear": 2020, "Volume": "144", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou 325035, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Surveying and Geospatial Engineering, College of Engineering, University of Tehran, Tehran, Iran;Department of Computer Science, School of Computing, National University of Singapore, Singapore, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Digital Media, Shenzhen Institute of Information Technology, Shenzhen, 518172, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Yangzhou University, Yangzhou, 225127, China;Corresponding authors."}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Chen", "Affiliation": "College of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou 325035, China;Corresponding authors."}], "References": [{"Title": "Gaussian mutational chaotic fruit fly-built optimization and feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112976", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 78775701, "Title": "Intelligent evaluation of test suites for developing efficient and reliable software", "Abstract": "Test suites play an important role in developing reliable software applications. Generally, the behaviour of software applications is verified by executing test suites to find defects. The quality of a test suite needs to be evaluated and enriched (if needed) especially for testing critical systems, such as plane-navigation system. This paper presents a novel method for comparing concrete and executable test suites using equivalence classes. This comparison identifies gaps in test suites with respect to each other. These gaps indicate potential weaknesses in the test suites. Furthermore, this method provides a mechanism to enrich the test suites using these gaps. In this method, we devise equivalence classes, and associate each test case to an equivalence class. We, then, simulate the comparison of test suites by comparing sets of equivalence classes. The method compares test suites in a platform independent manner. The test suites, which are compared, are smaller than the original test suites because the redundant test cases are removed from the test suites, which makes it efficient. We exercise our method over three case studies to demonstrate its viability and effectiveness. The first case study illustrates the application of the method and evaluates its effectiveness using a mutation analysis. The second case study evaluates its effectiveness using mutation and coverage analyses. The final case study evaluates it on a real case study, which is Lucene search engine. <img src=\"//:0\" data-src='{\"type\":\"image\",\"src\":\"/na101/home/<USER>/publisher/tandf/journals/content/gpaa20/0/gpaa20.ahead-of-print/17445760.2019.1696342/20191203/images/medium/gpaa_a_1696342_uf0001_ob.jpg\"}' />", "Keywords": "Test adequacy ; test suite comparison ; test suite enrichment ; quality of testing ; extending test adequacy", "DOI": "10.1080/17445760.2019.1696342", "PubYear": 2021, "Volume": "36", "Issue": "2", "JournalId": 14122, "JournalTitle": "International Journal of Parallel, Emergent and Distributed Systems", "ISSN": "1744-5760", "EISSN": "1744-5779", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science & Technology, University of Canberra, Canberra, ACT, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science & Technology, University of Canberra, Canberra, ACT, Australia"}], "References": []}, {"ArticleId": 78775780, "Title": "Model-based event-triggered dynamic output predictive control of networked uncertain systems with random delay", "Abstract": "This paper explores a dynamic output predictive control issue for networked systems with random delays and uncertainties. Based on the networked predictive control systems, an adaptive event-triggered dynamic output predictive control strategy is presented. And a networked delay compensator method is employed to compensate the communication delay actively. Furthermore, the problem we addressed can be transformed into the delay-dependent stability and stabilisation problem of networked control systems. Finally, the <PERSON><PERSON><PERSON><PERSON> functional approach and linear matrix inequality method are introduced to solve the expected solution. Both the results of numerical simulations and experiment are used to verify the effectiveness of the proposed method.", "Keywords": "Dynamic output feedback control ; adaptive event-triggered control ; networked predictive control systems ; uncertainties ; random delay", "DOI": "10.1080/00207721.2019.1691751", "PubYear": 2020, "Volume": "51", "Issue": "1", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, People's Republic of China"}], "References": []}, {"ArticleId": 78776348, "Title": "A Hierarchical Clustering algorithm based on Silhouette Index for cancer subtype discovery from genomic data", "Abstract": "<p>Identifying potential novel subtypes of cancers from genomic data requires techniques to estimate the number of natural clusters in the data. Determining the number of natural clusters in a dataset has been a challenging problem in Machine Learning. Employing an internal cluster validity index such as Silhouette Index together with a clustering algorithm has been a widely used technique for estimating the number of natural clusters, which has limitations. We propose a Hierarchical Agglomerative Clustering algorithm which automatically estimates the numbers of natural clusters and gives the associated clustering solutions along with dendrograms for visualizing the clustering structure. The algorithm has a Silhouette Index-based criterion for selecting the pair of clusters to merge, in the process of building the clustering hierarchy. The proposed algorithm could find decent estimates for the number of natural clusters, and the associated clustering solutions when applied to a collection of cancer gene expression datasets and general datasets. The proposed method showed better overall performance when compared to that of a set of prominent methods for estimating the number of natural clusters, which are used for cancer subtype discovery from genomic data. The proposed method is deterministic. It can be a better alternative to contemporary approaches for identifying potential novel subtypes of cancers from genomic data.</p>", "Keywords": "Cluster analysis; Hierarchical Clustering; Silhouette Index; Cluster number estimation; Cancer subtype discovery; Gene expression data; Consensus Clustering", "DOI": "10.1007/s00521-019-04636-5", "PubYear": 2020, "Volume": "32", "Issue": "15", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Calicut, Calicut, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Calicut, Calicut, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Calicut, Calicut, India"}], "References": []}, {"ArticleId": 78776474, "Title": "Transliteration of Arabizi into Arabic Script for Tunisian Dialect", "Abstract": "<p>The evolution of information and communication technology has markedly influenced communication between correspondents. This evolution has facilitated the transmission of information and has engendered new forms of written communication (email, chat, SMS, comments, etc.). Most of these messages and comments are written in Latin script, also called Arabizi . Moreover, the language used in social media and SMS messaging is characterized by the use of informal and non-standard vocabulary, such as repeated letters for emphasis, typos, non-standard abbreviations, and nonlinguistic content like emoticons. Since the Tunisian dialect suffers from the unavailability of basic tools and linguistic resources compared to Modern Standard Arabic, we resort to the use of these written sources as a starting point to build large corpora automatically. In the context of natural language processing and to benefit from these networks’ data, transliterating from Arabizi to Arabic script is a necessary step because most recently available tools for processing the Tunisian dialect expect Arabic script input. Indeed, the transliteration task can help construct and enrich parallel corpora and dictionaries for the Tunisian dialect and can be useful for developing various natural language processing applications such as sentiment analysis, opinion mining, topic detection, and machine translation. In this article, we focus on converting the Tunisian dialect text that is written in Latin script to Arabic script following the Conventional Orthography for Dialectal Arabic. Then, we propose two models to transliterate Arabizi into Arabic script for the Tunisian dialect, namely a rule-based model and a discriminative model as a sequence classification task based on conditional random fields). In the first model, we use a set of transliteration rules to convert the Tunisian dialect Arabizi texts to Arabic script. In the second model, transliteration is performed both at word and character levels. In the end, our models got a character error rate of 10.47%.</p>", "Keywords": "Arabizi corpus; CRF model; Natural language processing; Tunisian dialect; diacritization; rule-based approach; transliteration", "DOI": "10.1145/3364319", "PubYear": 2020, "Volume": "19", "Issue": "2", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sfax"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sfax"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Sfax"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sfax"}], "References": []}, {"ArticleId": 78777032, "Title": "Momental directional patterns for dynamic texture recognition", "Abstract": "Understanding the chaotic motions of dynamic textures (DTs) is a challenging problem of video representation for different tasks in computer vision. This paper presents a new approach for an efficient DT representation by addressing the following novel concepts. First, a model of moment volumes is introduced as an effective pre-processing technique for enriching the robust and discriminative information of dynamic voxels with low computational cost. Second, two important extensions of Local Derivative Pattern operator are proposed to improve its performance in capturing directional features. Third, we present a new framework, called Momental Directional Patterns, taking into account the advantages of filtering and local-feature-based approaches to form effective DT descriptors. Furthermore, motivated by convolutional neural networks, the proposed framework is boosted by utilizing more global features extracted from max-pooling videos to improve the discrimination power of the descriptors. Our proposal is verified on benchmark datasets, i.e., UCLA, DynTex, and DynTex++, for DT classification issue. The experimental results substantiate the interest of our method.", "Keywords": "Dynamic texture ; Dynamic texture recognition ; LDP ; SBP ; LBP ; Moment images ; Video representation", "DOI": "10.1016/j.cviu.2019.102882", "PubYear": 2020, "Volume": "194", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Toulon, Aix Marseille Université, CNRS, LIS, Marseille, France;HCMC University of Technology and Education, Faculty of IT, Ho Chi Minh City, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Toulon, Aix Marseille Université, CNRS, LIS, Marseille, France;HCMC University of Technology and Education, Faculty of IT, Ho Chi Minh City, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université de Toulon, Aix Marseille Université, CNRS, LIS, Marseille, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Caen Basse-Normandie, CNRS, GREYC, UMR 6072, 14000 Caen, France"}], "References": []}, {"ArticleId": 78777038, "Title": "Graph convolutional neural network for multi-scale feature learning", "Abstract": "Automatic deformable 3D modeling is computationally expensive, especially when considering complex position, orientation and scale variations. We present a volume segmentation framework to utilize local and global regularizations in a data-driven approach. We introduce automated correspondence search to avoid manually labeling landmarks and improve scalability. We propose a novel marginal space learning technique, utilizing multi-resolution pooling to obtain local and contextual features without training numerous detectors or excessively dense patches. Unlike conventional convolutional neural network operators, graph-based operators allow spatially related features to be learned on the irregular domain of the multi-resolution space, and a graph-based convolutional neural network is proposed to learn representations for position and orientation classification. The graph-CNN classifiers are used within a marginal space learning framework to provide efficient and accurate shape pose parameter hypothesis prediction. During segmentation, a global constraint is initially non-iteratively applied, with local and geometric constraints applied iteratively for refinement. Comparison is provided against both classical deformable models and state-of-the-art techniques in the complex problem domain of segmenting aortic root structure from computerized tomography scans. The proposed method shows improvement in both pose parameter estimation and segmentation performance.", "Keywords": "Deep learning ; Graph convolutional neural network ; Medical image segmentation ; Marginal space learning ; Aortic root ; Computerized tomography", "DOI": "10.1016/j.cviu.2019.102881", "PubYear": 2020, "Volume": "194", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Swansea University, Singleton Park, Swansea SA2 8PP, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Xi<PERSON>", "Affiliation": "Department of Computer Science, Swansea University, Singleton Park, Swansea SA2 8PP, United Kingdom;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Swansea University, Singleton Park, Swansea SA2 8PP, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Swansea University, Singleton Park, Swansea SA2 8PP, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Peninsula Radiology Academy, Plymouth Hospitals NHS Trust, Plymouth PL6 5WR, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Plymouth University Schools of Medicine & Dentistry, Plymouth Hospitals NHS Trust, Plymouth PL6 8BT, United Kingdom"}], "References": []}, {"ArticleId": 78777064, "Title": "A crowd-efficient learning approach for NER based on online encyclopedia", "Abstract": "<p>Named Entity Recognition (NER) is a core task of NLP. State-of-art supervised NER models rely heavily on a large amount of high-quality annotated data, which is quite expensive to obtain. Various existing ways have been proposed to reduce the heavy reliance on large training data, but only with limited effect. In this paper, we propose a crowd-efficient learning approach for supervised NER learning by making full use of the online encyclopedia pages. In our approach, we first define three criteria (representativeness, informativeness, diversity) to help select a much smaller set of samples for crowd labeling. We then propose a data augmentation method, which could generate a lot more training data with the help of the structured knowledge of online encyclopedia to greatly augment the training effect. After conducting model training on the augmented sample set, we re-select some new samples for crowd labeling for model refinement. We perform the training and selection procedure iteratively until the model could not be further improved or the performance of the model meets our requirement. Our empirical study conducted on several real data collections shows that our approach could reduce 50% manual annotations with almost the same NER performance as the fully trained model.</p>", "Keywords": "NER; Crowdsourcing; Crowd-efficient; Named entity recognition", "DOI": "10.1007/s11280-019-00736-3", "PubYear": 2020, "Volume": "23", "Issue": "1", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence, School of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence, School of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> University of Science and Technology, Jeddah, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IFLYTEK Research, Suzhou, China;State Key Laboratory of Cognitive Intelligence, IFLYTEK, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence, School of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence, School of Computer Science and Technology, Soochow University, Suzhou, China"}], "References": []}, {"ArticleId": 78777107, "Title": "A novel mapping technique for ray tracer to system-level simulation", "Abstract": "Simulations have become remarkably useful in evaluating the performance of new techniques and algorithms in communication networks. This is due to its comparative cost, time and complexity advantage over the analytical and field trial approaches. For large-scale networks, system-level simulators (SLS) are used to assess the performance of the systems. The SLS typically employs statistical channel models to characterize the propagation environment. However, the communication channels can be more accurately modeled using the deterministic ray tracing tools, though at the cost of higher complexity. In this work, we present a novel framework for a hybrid system that integrates both the ray tracer and the SLS. In the hybrid system, the channel strength in terms of the signal-to-noise ratio (SNR) is fed from the ray tracer to the SLS which then uses the values for further tasks such as resource allocation and the consequent performance evaluation. Using metrics such as user throughput and spectral efficiency, our results show that the hybrid system predicts the system performance more accurately than the baseline SLS without ray tracing. The hybrid system will thus facilitate the accurate assessment of the performance of next-generation wireless systems.", "Keywords": "", "DOI": "10.1016/j.comcom.2019.11.039", "PubYear": 2020, "Volume": "150", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Telecomunicações, 3810-193 Aveiro, Portugal;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Telecomunicações, 3810-193 Aveiro, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Instituto de Telecomunicações, 3810-193 Aveiro, Portugal"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Telecomunicações, 3810-193 Aveiro, Portugal"}, {"AuthorId": 5, "Name": "Saba Al-Rubaye", "Affiliation": "Autonomous and Cyberphysical Systems Center, Cranfield University, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Instituto de Telecomunicações, 3810-193 Aveiro, Portugal;University of South Wales, Pontypridd, CF37 1DL, United Kingdom"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Excellence, EXFO, Canada"}], "References": []}, {"ArticleId": 78777124, "Title": "Paper-based smartphone chemosensor for reflectometric on-site total polyphenols quantification in olive oil", "Abstract": "High polyphenols levels are one of the main quality characters of Extra Virgin Olive Oil (EVOO), leading to high antioxidant activity and contributing to the EVOO health beneficial effects. Analytical methods for the rapid assessment of EVOO polyphenols content, possibly directly at the place of production or distribution, are therefore important for the commercial enhancement of this product. Unfortunately, most analytical methods for quantification of polyphenols in EVOO require laboratory instrumentation and preliminary sample extraction procedures. In this paper we describe a paper-based chemosensor based on the well-known Folin-Ciocalteu colorimetric assay for the rapid (assay time 15 min) quantitative detection of polyphenols in EVOO without any preliminary sample extraction. Use of n-propanol for diluting the EVOO sample allows its direct analysis on paper supports loaded with the Folin-Ciocalteu reagent. The color change of the paper supports is measured using a smartphone camera. A disposable analytical cartridge containing all necessary reagents, including calibration standards, and accessories for performing the assay using a Samsung S8 smartphone have been developed to perform on-site analysis. Measurement of total polyphenol content of EVOO samples gave results in good agreement with the conventional Folin-Ciocalteu assay, and the limit of detection was 30 μg gallic acid equivalents g<sup>−1</sup> EVOO. The same approach can be employed to measure polyphenols in other oils of vegetal origin.", "Keywords": "Folin-Ciocalteu assay ; Polyphenols ; Smartphone reflectance detection ; Extra virgin olive oil ; Chemosensor ; 3D printing technology", "DOI": "10.1016/j.snb.2019.127522", "PubYear": 2020, "Volume": "305", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry <PERSON>, Alma Mater Studiorum – University of Bologna, Via Selmi 2, 40126, Bologna, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry <PERSON>, Alma Mater Studiorum – University of Bologna, Via Selmi 2, 40126, Bologna, Italy;Interdepartmental Centre for Renewable Sources, Environment, Sea and Energy (CIRI FRAME), Alma Mater Studiorum - University of Bologna, Via Sant'Alberto 163, 48123 Ravenna, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry <PERSON>, Alma Mater Studiorum – University of Bologna, Via Selmi 2, 40126, Bologna, Italy;Interdepartmental Centre for Renewable Sources, Environment, Sea and Energy (CIRI FRAME), Alma Mater Studiorum - University of Bologna, Via Sant'Alberto 163, 48123 Ravenna, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical and Surgical Sciences, Alma Mater Studiorum – University of Bologna, Via Massarenti 9, 40138 Bologna, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry <PERSON>, Alma Mater Studiorum – University of Bologna, Via Selmi 2, 40126, Bologna, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry <PERSON>, Alma Mater Studiorum – University of Bologna, Via Selmi 2, 40126, Bologna, Italy"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical and Neuromotor Sciences Alma Mater Studiorum, University of Bologna, Via Irnerio 48, 40126, Bologna, Italy;Biostructures and Biosystems National Institute (INBB), Viale Delle Medaglie d'Oro 305, 00136, Rome, Italy"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry Giacomo Ciam<PERSON>an, Alma Mater Studiorum – University of Bologna, Via Selmi 2, 40126, Bologna, Italy;Interdepartmental Centre for Renewable Sources, Environment, Sea and Energy (CIRI FRAME), Alma Mater Studiorum - University of Bologna, Via Sant'Alberto 163, 48123 Ravenna, Italy;Biostructures and Biosystems National Institute (INBB), Viale Delle Medaglie d'Oro 305, 00136, Rome, Italy;Interdepartmental Centre of Industrial Agrifood Research (CIRI Agrifood), Alma Mater Studiorum, University of Bologna, Piazza Goidanich 60, 47521, Cesena, FC, Italy;Corresponding author at: Department of Chemistry Giacomo Ciamician, Alma Mater Studiorum – University of Bologna, Via Selmi 2, 40126, Bologna, Italy."}], "References": []}, {"ArticleId": 78777176, "Title": "A hybrid approach for portfolio selection with higher-order moments: Empirical evidence from Shanghai Stock Exchange", "Abstract": "Skewness and kurtosis, the third and fourth order moments, are statistics to summarize the shape of a distribution function. Recent studies show that investors would take these higher-order moments into consideration to make a profitable investment decision. Unfortunately, due to the difficulties in solving the multi-objective problem with higher-order moments, the literature on portfolio selection problem with higher-order moments is few. This paper proposes a new hybrid approach to solve the portfolio selection problem with skewness and kurtosis, which includes not only the multi-objective optimization but also the data-driven asset selection and return prediction, where the techniques of two-stage clustering, radial basis function neural network and genetic algorithm are employed. With the historical data from Shanghai stock exchange, we find that the out-of-sample performance of our model with higher-order moments is significantly better than that of traditional mean-variance model and verify the robustness of our hybrid algorithm.", "Keywords": "Portfolio optimization ; Higher-order moments ; Genetic algorithm ; Machine learning algorithm", "DOI": "10.1016/j.eswa.2019.113104", "PubYear": 2020, "Volume": "145", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, Xiamen University, Xiamen 361005, China;Amoy Key Lab. of Big Data Intelligent Analysis and Decision, Xiamen 361005, China"}, {"AuthorId": 2, "Name": "Jingdong Zhong", "Affiliation": "School of Economics, Peking University, Beijing 100871, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Department of Finance and Insurance, Nanjing University, Jiangsu Province 210093, China;Corresponding author."}], "References": []}, {"ArticleId": 78777454, "Title": "An intelligent decision support system prototype for hinterland port logistics", "Abstract": "Port logistics is characterised by a high degree of fragmentation, uncertainty and complexity. In a context with these characteristics, decision support can be of significant value. This study presents the prototype of an intelligent decision support system (DSS) that leads to horizontal and vertical cooperation among freight agents involved in port logistics. A multi-agent simulation model is presented where heterogeneous actions are enabled as a result of an adaptive reinforcement learning algorithm that is inspired by human decision-making strategies. The model combines optimisation modelling and decision theory to operate in a dynamic environment characterised by information asymmetry among agents and dynamic changes over time. A simulation demonstrates the dynamics and convergence to equilibrium of the interactions of heterogeneous agents for delivery and pick up of import/export shipments. In particular, we answer two specific research questions. What is the likely impact of an intelligent DSS in hinterland container transport as a value-added service of port community system (PCS)? How can an optimum cooperative strategy be formulated to meet the dynamic demand and supply of freight agents in hinterland container transport and achieve agility in the age of hyper-competition? By addressing these two research questions we make a specific contribution by developing an agent-based simulation model in a real-world and large-scale case study, by using a reinforcement learning model based on probability matching theory that allows simulating realistically the adaptive behaviour of agents. The results of the simulation of two weeks of container movements indicate huge savings in total transport costs and distance travelled as well as higher utilisation of trucks from the sharing of resources. Moreover, we show that it is strictly better for smaller freight agents to cooperate – a conclusion generated endogenously inside the model as a trade-off between exploration and exploitation. As a result of this prototype simulation, major freight agents will not necessarily benefit from the DSS mainly because they are already using the economies of scale.", "Keywords": "Agent-based model ; Port community system ; Vehicle routing problem ; Reinforcement learning ; Freight transportation", "DOI": "10.1016/j.dss.2019.113227", "PubYear": 2020, "Volume": "130", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "Elnaz Irannezhad", "Affiliation": "Australian Institute of Business and Economics, University of Queensland, Australia;Corresponding author at: Australian Institute of Business and Economics, University of Queensland, Brisbane, QLD, Postcode 4072, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Civil Engineering, University of Queensland, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Civil Engineering, University of Queensland, Australia"}], "References": []}, {"ArticleId": ********, "Title": "A cooperative advertising collaboration policy in supply chain management under uncertain conditions", "Abstract": "Many industries are facing big challenges to design supply chains in a way to maximize the profit and meet the heightened expectations of the customer. This new era entirely relies on the dynamic advantages of competition and the role played by the collaboration policy. A global economy and increasing demand have put a huge pressure on supply chain partners to build a collaboration policy based on price, order quantity, and advertising. Companies are adopting the idea of ”shaking hands” to obtain more profit instead of taking risks through competition. Cooperative (co-op) advertising is a significant policy of centralized supply chain management (SCM) to boost the revenues generated by the supplier, manufacturer, and retailers. The uncertain costs associated with the supply chain management also create obstacles in economic analysis and feasibility. These uncertainties are associated with the basic costs of all supply chain partners, which are represented using a signed distance formula. This paper develops the concept of co-op advertising among the supplier, manufacturer, and retailers with a variable demand driven by selling price and advertising costs, where all basic costs are considered as fuzzy. The profit is optimized by considering variable cycle time, shipments, pricing and advertising costs for the decision support system of the supply chain management. The optimal results of the co-op advertisement ensured an increase in the revenue of whole supply chain.", "Keywords": "Supply chain management ; Three-echelon supply chain ; Collaboration policy ; Advertisement cost ; Fuzzy costs", "DOI": "10.1016/j.asoc.2019.105948", "PubYear": 2020, "Volume": "88", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Yonsei University, 50 Yonsei-ro, Sinchon-dong, Seodaemun-gu, Seoul 03722, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Jalozai Campus, University of Engineering and Technology, Peshawar 25000, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical, Aerospace, and Nuclear Engineering, Ulsan National Institute of Science and Technology, Ulsan 44919, South Korea;Corresponding author"}], "References": []}, {"ArticleId": 78777857, "Title": "A novel fuzzy-Markov forecasting model for stock fluctuation time series", "Abstract": "<p>In order to reveal intrinsic fluctuation rules hidden in a stock market time series dataset with noise, a novel forecasting model combining Markov chain theory with fuzzy set theory is proposed in this study. A fuzzified one-step transition matrix of Markov Chain in the paper represents inherent rules of historical fluctuation. Comparing with existing models, the advantage of the proposed model is that transition matrix can express the relationship between history and current flexibly while the introduction of fuzzy theory can help to alleviate noises. Therefore, the proposed model could handle complex patterns during state transitions and the relatively simple forecasting algorithm could reduce the calculation cost. We apply the proposed method to forecast well-known stock indexes such as (Taiwan Stock Exchange Capitalization Weighted Stock Index) TAIEX, (Shanghai Stock Exchange Composite Index) SHSECI and so on. Experimental results demonstrate that our proposed method outperforms other traditional models.</p>", "Keywords": "Fluctuation time series; Markov chain; Fuzzy set; Forecasting model", "DOI": "10.1007/s12065-019-00328-0", "PubYear": 2020, "Volume": "13", "Issue": "2", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "Hongjun Guan", "Affiliation": "School of Management Science and Engineering, Shandong University of Finance and Economics, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering, Shandong University of Finance and Economics, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON>ang <PERSON>", "Affiliation": "Courant Institute of Mathematical Sciences, New York University, New York, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Shandong University of Finance and Economics, Jinan, China"}], "References": []}, {"ArticleId": 78777863, "Title": "Hybrid Operational High Step-Up DC–DC Converter", "Abstract": "<p>High step-up DC–DC voltage converters are widely employed in uninterruptible power supplies and photovoltaic systems. The voltage levels of batteries and photovoltaic panels are, usually, low when compared with the grid-connected inverter requirements. The employment of several PV panels or batteries associated in series, directly connected to the inverter, simplifies the system but at the expense of the cost increase and of a smaller solar lighting efficiency/utilization. Several high step-up DC–DC voltage conversions are presented in the technical literature and include the basic boost converter and its derivations such as cascade, quadratic and interleaved, and voltage multipliers based on Cockcroft–Walton, switched capacitors and hybrid converters. This work presents a hybrid converter based on the boost and the switched-capacitor voltage multiplier. Its features are a low energy processing, robustness and balanced cell voltages. Comparative analysis involving the proposal and existent solutions is carried out. The research is supported by simulation essays carried out with PSpice software and preliminary experimental verification.</p>", "Keywords": "Hybrid converter; Switched-capacitor converter; High step-up DC–DC voltage converter", "DOI": "10.1007/s40313-019-00548-w", "PubYear": 2020, "Volume": "31", "Issue": "2", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "Aluísio Alves de Melo Bento", "Affiliation": "State University of Rio de Janeiro - UERJ, Rio de Janeiro, Brazil"}], "References": []}, {"ArticleId": 78777898, "Title": "Analysis of a novel RF MEMS switch using different meander techniques", "Abstract": "<p>In this paper, two types of RF MEMS switches namely step structure and Normal beam structure are designed and analyzed using different meander techniques. Three techniques namely plus, zigzag and three-square meander were used to lower the pull-in voltage. The actuating beam is designed with the rectangular perforations affects the performance of a switch by lowering the pull-in voltage, switching speed and results in better isolation. In this paper a comparative analysis is done for all three meander techniques with and without perforations on the beam. Total six structures have been designed with the combination three meanders and two different beam structures. The proposed stepdown structure exhibits high performance characteristics with a very low pull-in voltage of 1.2 V having an airgap of 0.8 µm between the actuation electrodes. The gold is used as beam material and HfO<sub>2</sub> as the dielectric material such that the upstate and downstate capacitance is seen as 1.02 fF and 49 fF. The FEM analysis is done to calculate the spring constant and thereby the pull-in voltage and behavior of the switch is studied with various parameters. The switch with a step structure and three-square meander configuration has shown best performance of all by requiring a pull-in voltage of 1.2 V and lower switching time of 0.2 µs. The proposed switch also exhibits good RF performance characteristics with an insertion loss below − 0.07 dB and return loss below − 60 dB over the frequency range of 1–40 GHz. At 28 GHz a high isolation of − 68 dB is exhibited.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04703-w", "PubYear": 2020, "Volume": "26", "Issue": "5", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National MEMS Designing Center, National Institute of Technology, Silchar, India;Department of Electronics and Communication Engineering, MEMS Research Center, KL University, Guntur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National MEMS Designing Center, National Institute of Technology, Silchar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, MEMS Research Center, KL University, Guntur, India"}], "References": []}, {"ArticleId": 78777982, "Title": "Spatiotemporal neural networks for action recognition based on joint loss", "Abstract": "<p>Action recognition is a challenging and important problem in a myriad of significant fields, such as intelligent robots and video surveillance. In recent years, deep learning and neural network techniques have been widely applied to action recognition and attained remarkable results. However, it is still a difficult task to recognize actions in complicated scenes, such as various illumination conditions, similar motions, and background noise. In this paper, we present a spatiotemporal neural network model with a joint loss to recognize human actions from videos. This spatiotemporal neural network is comprised of two key connected substructures. The first one is a two-stream-based network extracting optical flow and appearance features from each frame of videos, which characterizes the human actions of videos in spatial dimension. The second substructure is a group of Long Short-Term Memory structures following the spatial network, which describes the temporal and transition information in videos. This research effort presents a joint loss function for training the spatiotemporal neural network model. By introducing the loss function, the action recognition performance is improved. The proposed method was tested with video samples from two challenging datasets. The experiments demonstrate that our approach outperforms the baseline comparison methods.</p>", "Keywords": "Action recognition; Spatiotemporal architecture; LSTM; Joint loss", "DOI": "10.1007/s00521-019-04615-w", "PubYear": 2020, "Volume": "32", "Issue": "9", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "Hongbin Sun", "Affiliation": "Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON>ning <PERSON>", "Affiliation": "Xi’an Jiaotong University, Xi’an, China"}], "References": []}, {"ArticleId": 78778030, "Title": "A decentralized and secure blockchain platform for open fair data trading", "Abstract": "<p>As the value of data has received considerable attention, data trading shows broad market prospects. The existing data trading methods, including private trades and centralized trades, have high risks regarding transaction security and data protection. To solve this problem, we propose a decentralized trading solution for open fair data trading by deploying the smart contract on the blockchain network. The data for sale are encrypted and stored on the distributed storage platform but not directly on the blockchain network. Because the trading content is the decryption key of the data, the proposed new method can alleviate the storage pressure of the blockchain by reducing the transaction cost. We conduct a security analysis which shows that our scheme achieves secure, practical, open, and fair trading. We implement our trading contract with solidity and test it on the Ethereum's test network, and extensive experiments demonstrate desirable feasibility of our proposal.</p>", "Keywords": "blockchain;cloud storage;data trading;fair exchange;fog computing", "DOI": "10.1002/cpe.5578", "PubYear": 2020, "Volume": "32", "Issue": "7", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Technology, Beihang University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science, Engineering and Technology, Swinburne University of Technology, Melbourne, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Crytape Research, Cryptape Co, Ltd, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Technology, Beihang University, Beijing, China"}, {"AuthorId": 5, "Name": "Zhenyu Guan", "Affiliation": "School of Cyber Science and Technology, Beihang University, Beijing, China; Zhenyu Guan, School of Cyber Science and Technology, Beihang University, Beijing 100083, China.; <PERSON><PERSON><PERSON>, School of Cyber Science and Technology, Beihang University, Beijing 100083, China; or; Key Laboratory of Aerospace Network Security, Ministry of industry and information technology"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Technology, Beihang University, Beijing, China; Key Laboratory of Aerospace Network Security, Ministry of Industry and Information Technology; Zhenyu Guan, School of Cyber Science and Technology, Beihang University, Beijing 100083, China.; <PERSON><PERSON><PERSON>, School of Cyber Science and Technology, Beihang University, Beijing 100083, China; or; Key Laboratory of Aerospace Network Security, Ministry of industry and information technology"}], "References": []}, {"ArticleId": 78778826, "Title": "Efficient CGM-based parallel algorithms for the longest common subsequence problem with multiple substring-exclusion constraints", "Abstract": "A variant of the Longest Common Subsequence (LCS) problem is the LCS problem with multiple substring-exclusion constraints (M-STR-EC-LCS), which has great importance in many fields especially in bioinformatics. This problem consists to compute the LCS of two strings X and Y of length n and m respectively that excluded a set of d constraints P = { P 1 , P 2 , … , P d } of total length r . Recently, <PERSON> et al. proposed a sequential solution based on the dynamic programming technique that requires O ( n m r ) execution time and space. To the best of our knowledge, there is no parallel solutions for this problem. This paper describes new efficient parallel algorithms on Coarse Grained Multicomputer model (CGM) to solve this problem. Firstly, we propose a multi-level Direct Acyclic Graph (DAG) that determines the correct evaluation order of sub-problems in order to avoid redundancy due to overlap. Secondly, we propose two CGM parallel algorithms based on our DAG. The first algorithm is based on a regular partitioning of the DAG and requires O ( n m r p ) execution time with O ( p ) communication rounds where p is the number of processors used. Its main drawback is high idleness time of processors because due to the dependencies between the nodes in the DAG, over time it has many idle processors. The second algorithm uses an irregular partitioning of the DAG that minimizes this idleness time by allowing the processors to stay active as long as possible. It requires O ( n m r p ) execution time with O ( k p ) communication rounds. k is a constant integer allowing to setup the irregular partitioning. The both algorithms require O ( r | Σ | p ) preprocessing time where |Σ| is the length of the alphabet. The experimental results performed show a good agreement with theoretical predictions.", "Keywords": "Parallel algorithms ; Coarse grained multicomputer ; Dynamic programming ; Multiple-constrained LCS ; Direct acyclic graph", "DOI": "10.1016/j.parco.2019.102598", "PubYear": 2020, "Volume": "91", "Issue": "", "JournalId": 5203, "JournalTitle": "Parallel Computing", "ISSN": "0167-8191", "EISSN": "1872-7336", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of mathematics and computer science, University of Dschang, Dschang, Cameroon;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of mathematics and computer science, University of Dschang, Dschang, Cameroon"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of mathematics and computer science, University of Dschang, Dschang, Cameroon"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Computer Science Lab-MIS, University of Picardie Jules Verne, Amiens, France"}], "References": []}, {"ArticleId": 78778834, "Title": "An approach to merge collaborating processes of an inter-organizational business process for artifact lifecycle synthesis", "Abstract": "<p>Artifact-centric approach to business process modeling has received considerable attention for elevating data logic to the same level as the process flow logic. With the emergence of this modeling paradigm, several recent works have focused on synthesizing the indispensable lifecycles of key business entities called artifacts from the standalone activity-centric processes. However, synthesizing artifact lifecycles from the inter-organizational business processes (IOBP) is challenging, as the artifacts and states are shared among two or more collaborating processes. Thus, unlike a standalone process, the synthesis of artifact lifecycles from an IOBP require the process interactions to be captured by preserving the dependencies between the involved artifacts and states in the resulting lifecycles. Therefore, in this paper, we propose an automated approach that aims at merging the collaborating processes of an IOBP in order to support the synthesis of artifact lifecycles from an IOBP. The proposed approach is comprised of algorithms that combine the nodes of collaborating processes to generate an integrated process that can be used to synthesize the artifact lifecycles pertinent to an IOBP. We demonstrate the proposed approach using an e-business process scenario and the validity is proved using theorems and a prototype implementation.</p>", "Keywords": "Artifact-centric modeling; Inter-organizational business process; Process merging and interaction patterns; 68Q85", "DOI": "10.1007/s00607-019-00770-z", "PubYear": 2020, "Volume": "102", "Issue": "4", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Computer and Mathematical Sciences, Auckland University of Technology, Auckland, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Computer and Mathematical Sciences, Auckland University of Technology, Auckland, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Computer and Mathematical Sciences, Auckland University of Technology, Auckland, New Zealand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software and Electrical Engineering, Swinburne University of Technology, Victoria, Australia"}], "References": []}, {"ArticleId": ********, "Title": "Traceability of Information Routing Based on Fuzzy Associative Memory Modelling in Fisheries Supply Chain", "Abstract": "<p>Traceability is the ability to verify the history and location of a food product, thus providing information on each supply-chain actor, who the immediate supplier is, and to whom the product was sent. The information system approach has been used to manage and integrate all such information by collecting, storing, then retrieving data and information about the product from earlier stages of the production process. Besides documentation and information sharing, such traceability information systems can also support timely resolution of customer complaints. This paper presents modelling of routing and handling time prediction using a fuzzy associative memory (FAM) method. As a first response to customers, information about the time required to resolve an issue can be provided after the source of a product defect has been traced. With regards to handling, traceability can assist with several issues, e.g., product replacement, product recalls based on retrieval of the contact numbers of affected customers on a recall list , and inspections at each production unit to ensure food safety standards. Based on such activities, it is assumed that the handling time will be affected by the size of the product inventory that can be used to replace a defective product, the amount of product that must be recalled from the market, and the time required internally for the inspection process, which is set as the FAM input variable. A FAM is a set of fuzzy-set pairs ( A , B ) that maps an input vector fuzzy set A to an output vector fuzzy set B . Our experiments show that, from such a FAM formulation, one can obtain 27 rules. The FAM will encode a fuzzy-set pair ( A , B ) to obtain matrix memories, denoted by M . As the prediction result, the matrix B can be obtained from the computational matrix A and the matrix M ; For instance, in case of a contamination incident in a fish product with inventory conditions of as much as 4 tonnes, the product recall amounts to 21 tonnes and the inspection will take 25 h, while the results of the computational experiment show that the total handling time for this case will be 66 h with low error rates.</p>", "Keywords": "Traceability; Fuzzy associative memory; Handling time", "DOI": "10.1007/s40815-019-00754-3", "PubYear": 2020, "Volume": "22", "Issue": "2", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Agro-Industrial Technology, Faculty of Agricultural Technology and Engineering, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Agro-Industrial Technology Study Program, Faculty of Halal Food Science, Djuanda University, Bogor, Indonesia"}], "References": []}, {"ArticleId": 78779057, "Title": "Transfer learning for vehicle detection using two cameras with different focal lengths", "Abstract": "This paper proposes a vehicle detection method using transfer learning for two cameras with different focal lengths. A detected vehicle region in an image of one camera is transformed into a binary map. After that, the map is used to filter convolutional neural network (CNN) feature maps which are computed for the other camera’s image. We also introduce a robust evolutionary algorithm that is used to compute the relationship between the two cameras in an off-line mode efficiently. We capture video sequences and sample them to make a dataset that includes images with different focal lengths for vehicle detection. We compare the proposed vehicle detection method with baseline detection methods, including faster region proposal networks (Faster-RCNN), single-shot-multi-Box detector (SSD), and detector using recurrent rolling convolution (RRC), in the same experimental context. The experimental results show that the proposed method can detect vehicles at a wide range of distances accurately and robustly, and significantly outperforms the baseline detection methods.", "Keywords": "Transfer learning ; Object detection ; Different focal lengths", "DOI": "10.1016/j.ins.2019.11.034", "PubYear": 2020, "Volume": "514", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering and Computer Science, Gwangju Institute of Science and Technology, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering and Computer Science, Gwangju Institute of Science and Technology, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering and Computer Science, Gwangju Institute of Science and Technology, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Engineering and Applied Science, University of Regina, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering and Computer Science, Gwangju Institute of Science and Technology, Korea;Corresponding author."}], "References": []}, {"ArticleId": 78779148, "Title": "Visual Analysis of Merchandise Sales Trend Based on Online Transaction Log", "Abstract": "<p>Online transaction log records the relevant information of the users, commodities and transactions, as well as changes over time, which can help analysts understand commodities’ sales. The existing visualization methods mainly analyze the purchase behavior from the perspective of users, while analyzing the sales trend of commodities can better help merchants to make business decisions. Based on the transaction log, this paper puts forward the visual analysis framework of commodity sales trend and the corresponding data processing algorithm. The concepts of volatility and dynamic performance of sales trend are proposed, through which the multi-dimensional sales data of time-oriented are displayed in two-dimensional space. The “Feature Ring” is designed to display the detailed sales information of the products. Based on the above methods, a visual analysis system is designed and implemented. The usability and validity of the visualization methods are verified by using JD online transaction data. The visualization methods enable manufacturers to formulate production plans and carry out product research and develop better.</p>", "Keywords": "", "DOI": "10.1142/S0218001420590363", "PubYear": 2020, "Volume": "34", "Issue": "11", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yu", "Affiliation": "Shenyang Institute of Computing Technology, Chinese Academy of Sciences, Shenyang 110168, P. R. China;University of Chinese Academy of Sciences, Beijing 100049, P. R. China;Department of Electrical Engineering, Yingkou Institute of Technology, Yingkou 115014, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenyang Institute of Computing Technology, Chinese Academy of Sciences, Shenyang 110168, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Yingkou Institute of Technology, Yingkou 115014, P. R. China;Department of Information Science, Dalian Maritime University, Dalian 116026, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shenyang Institute of Computing Technology, Chinese Academy of Sciences, Shenyang 110168, P. R. China;University of Chinese Academy of Sciences, Beijing 100049, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Shenyang Institute of Computing Technology, Chinese Academy of Sciences, Shenyang 110168, P. R. China;University of Chinese Academy of Sciences, Beijing 100049, P. R. China"}], "References": []}, {"ArticleId": 78779786, "Title": "LocAuth: A fine-grained indoor location-based authentication system using wireless networks characteristics", "Abstract": "Location-based information has become an attractive attribute for use in many services including localization, tracking, positioning, and authentication. An additional layer of security can be obtained by verifying the identity of users who wish to access confidential resources only within restricted, small, indoor trusted zones. The objective of this paper is to construct highly secure indoor areas primarily by detecting only legitimate users within their work cubicles. In this paper, we present a fine-grained location-based authentication system (LocAuth) which ensures the physical presence of the user within his/her small trusted zone (2 m<sup>2</sup> area). To do this, <PERSON>cAuth exploits the ambient wireless network characteristics (e.g., BSSID, SSID, and RSSI) of nearby Wi-Fi and Bluetooth devices observed from each trusted zone. We propose a novel technique called Top-Ranked Network Nodes (TRNNs) to accurately overcome the fluctuations in wireless signals and enhance the ability to distinguish targeted trusted zone from neighboring areas. In addition, we developed an application to implement LocAuth on Android-based smartphones and tested it in a real indoor environment. The tested area is composed of seven adjacent and closely spaced work cubicles located in our lab. We evaluated LocAuth in two ways: through RSSI-based nearest neighbors (RSSI-based NN) and through supervised machine learning algorithm (Support Vector Machines). The results of the experiment show the effectiveness of <PERSON>c<PERSON><PERSON> by achieving a high classification accuracy (above 98%). This demonstrates its feasibility in terms of both accuracy as well as fine-grained classification.", "Keywords": "Location based authentication ; Wireless networks ; Security ; Machine learning ; Wi-Fi ; Bluetooth ; Indoor trusted zones", "DOI": "10.1016/j.cose.2019.101683", "PubYear": 2020, "Volume": "89", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sungkyunkwan University, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sungkyunkwan University, South Korea;Data61 CSIRO, Australia;Corresponding author."}], "References": [{"Title": "Indoor Positioning Based on Visible Light Communication", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 78780070, "Title": "On The Steady Incompressible Laminar Saltwater Flow In Minkowski 2-D Subspace Continuum Through A Rectangular MHD Micro-pump", "Abstract": "Background The problem of a steady, incompressible and fully developed laminar fluid flow is discussed. This study focuses on the predictions of the pumping performance of 1 M NaCl solution. Method The corresponding Navier-Stokes momentum (MHD) equations are solved analytically by introducing a new ansatz combining the wave-position characters of the flow modelled in Minkowski 2-D subspace continuum subjected to suitable boundary conditions. Results Solving for the velocity profile of the working fluid across the micro-channel; the obtained solutions which include the dimensional flow velocity, volumetric flow rate, average velocity and pressure gradient are plotted under various operating currents, magnetic flux densities and micro-channel aspect ratio values. Conclusions Our results provide that increasing high ratios of the electrode to channel lengths serves to attain more coverable channel area. It is also noted that an excess of the applied fields tends to compensate the velocity degradation for deep channel configuration. Graphics show that the decreased friction force between the channel top and bottom is a preferable factor to improve the process in practice. Our results may be useful to shed light on studying the microfluidic systems flow.", "Keywords": "", "DOI": "10.1016/j.sna.2019.111703", "PubYear": 2020, "Volume": "303", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science, Menoufia University, Gamal Abdel Nasser Street, Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Science, Department of Physics, Menoufiya University Shebin Elkom, Egypt 32511"}], "References": []}, {"ArticleId": 78780152, "Title": "MWCNT and α-Fe2O3 embedded rGO-nanosheets based hybrid structure for room temperature chloroform detection using fast response/recovery cantilever based sensors", "Abstract": "This paper presents a simple, novel and cost effective process to synthesize MWCNTs and α-Fe<sub>2</sub>O<sub>3</sub> embedded in rGO sheet layers, which is found to be a highly selective and sensitive material for the detection of chloroform vapours in room temperature conditions. The nanocomposite rGO- MWCNT- α-Fe<sub>2</sub>O<sub>3</sub> has showed enhanced chloroform vapour sensing performance characteristics compared to rGO- α-Fe<sub>2</sub>O<sub>3</sub> and pure α-Fe<sub>2</sub>O<sub>3</sub> which have been reported earlier. The MWCNTS alongwith rGO sheets provide improved sensitivity mainly due to a high surface area (49 m<sup>2</sup>/ g and 72 m<sup>2</sup>/ g) with doping of nanotubes in the composite matrix as compared to other established counterparts like α-Fe<sub>2</sub>O<sub>3</sub> (12.6 m<sup>2</sup>/g) and rGO- α-Fe<sub>2</sub>O<sub>3</sub> (19.05 m<sup>2</sup>/g). The nanocomposite films also show a faster response (2 s at 1 ppm) and recovery time (1 s at 1 ppm) due to a high diffusivity of vapours into the structure owing to the 3-Dimensional nature of the nano-composite structure which makes the directly exposed area multifolds. The material is suitably immobilized on SU-8 based nanocantilever platform which show enhanced deflection upto 250 ppb vapour detection. The nanocomposite functionalised cantilever are further tested with other types of volatile organic compounds and the sensor is found to show a high selectivity towards chloroform. The presence of trace volatile organic compounds (VOC) in human breath is a very common way of detection of malignancy and thus this sensor may eventually help in such recognition in the near future.", "Keywords": "Cantilever ; Chloroform ; Graphene oxide ; MWCNT ; VOC sensor", "DOI": "10.1016/j.snb.2019.127457", "PubYear": 2020, "Volume": "305", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Design Programme, Indian Institute of Technology, Kanpur, U.P. 208016, India;Microsystems Fabrication Laboratory, Department of Mechanical Engineering, Indian Institute of Technology, Kanpur, U.P. 208016, India;Biophotonics Laboratory, Department of Physics, Indian Institute of Technology, Kanpur, U.P. 208016, India;Centre for Nanosciences, Indian Institute of Technology, Kanpur, U.P. 208016, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Biophotonics Laboratory, Department of Physics, Indian Institute of Technology, Kanpur, U.P. 208016, India;Department of Biological Sciences and Bioengineering, Indian Institute of Technology, Kanpur, U.P. 208016, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Microsystems Fabrication Laboratory, Department of Mechanical Engineering, Indian Institute of Technology, Kanpur, U.P. 208016, India;Department of Manufacturing Science, Central Institute of Plastics Engineering and Technology. Lucknow, U.P. 226008, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Nanosciences, Indian Institute of Technology, Kanpur, U.P. 208016, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Biophotonics Laboratory, Department of Physics, Indian Institute of Technology, Kanpur, U.P. 208016, India;Department of Physics, Indian Institute of Technology, Kanpur, U.P. 208016, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Design Programme, Indian Institute of Technology, Kanpur, U.P. 208016, India;Microsystems Fabrication Laboratory, Department of Mechanical Engineering, Indian Institute of Technology, Kanpur, U.P. 208016, India;Centre for Nanosciences, Indian Institute of Technology, Kanpur, U.P. 208016, India;Corresponding author at: Design Programme, Indian Institute of Technology, Kanpur, U.P. 208016, India"}], "References": []}, {"ArticleId": 78780154, "Title": "One–step solvothermal synthesis of 3D tube–globular Dy2O3 nanostructure for ultra–fast response to humidity", "Abstract": "3D tube–globular Dy<sub>2</sub>O<sub>3</sub> was prepared for the first time by a convenient one–step solvothermal method followed by calcination. The structure and morphology of Dy<sub>2</sub>O<sub>3</sub> were characterized by XRD, SEM, TEM, XPS and BET techniques. The 3D tube–globular Dy<sub>2</sub>O<sub>3</sub> nanostructure (∼1 μm diameter) was self–assembled by a large number of nanotubes with a diameter of about 20 nm. The effect of different calcination temperatures of the precursors on the humidity sensing was investigated. Compared with other metal oxide humidity sensing materials, the tube–globular Dy<sub>2</sub>O<sub>3</sub> has the advantages of ultra–fast response time (0.35 s), good linear relationship, smaller humidity hysteresis and good stability. The excellent humidity sensing is closely related to its novel 3D tube–globular nanostructure. In addition, the growth mechanism and humidity sensing mechanism of the material were also studied in detail.", "Keywords": "Humidity sensor ; 3D tube–globular ; Dy<sub>2</sub>O<sub>3</sub> ; ultra–fast response time", "DOI": "10.1016/j.snb.2019.127434", "PubYear": 2020, "Volume": "305", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Polymer Product Engineering, Johannes Kepler University Linz, Austria"}, {"AuthorId": 8, "Name": "Li<PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, China;Corresponding authors"}], "References": []}, {"ArticleId": 78780478, "Title": "Proposal of new eco-manufacturing feature interaction-based methodology in CAD phase", "Abstract": "<p>Following the environmental crises of recent decades, a turning point in the awareness of the fragility of ecosystems has been marked, i.e., environmental awareness. This has contributed to the development of various environmental laws and regulations such as the “Waste Electrical and Electronic Equipment,” the “Restriction of Hazardous Substances,” and the “Registration, Evaluation, Authorization and Restriction of Chemicals” regulations and the “Energy Using Products” Directives. Our work contributes to the development of eco-friendly product manufacturing processes. In order to estimate and optimize the environmental impacts of a product, most of the methodologies, concepts, and tools that integrate computer-aided design (CAD) and life cycle assessment systems generally exploit the feature technology at the level of each feature independently of the others, i.e., “microplanning.” The feature interaction technology (FIT) is treated only in few studies, but it is pivotal in the eco-manufacturing process. In this paper, we propose a new manufacturing-scenarios-based methodology by using FIT and a Multi-criteria Decision Support Method (MCDSM), which helps manufacturers maintain their marketplaces by producing goods in an eco-friendly way. In fact, this methodology helps designers choose from the CAD design phase the most ecological manufacturing process from possible existent scenarios in real time.</p>", "Keywords": "CAD; Eco-design; Eco manufacturing; Environmental impact; Multi-criteria decision support; Scenarios; Feature interaction", "DOI": "10.1007/s00170-019-04483-7", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire Génie <PERSON>, Ecole Nationale d’Ingénieurs de Monastir, Université de Monastir, Monastir, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire de Mécanique de Sousse, Ecole Nationale d’Ingénieurs de Sousse, Université de Sousse, Sousse Erriadh, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire Génie <PERSON>, Ecole Nationale d’Ingénieurs de Monastir, Université de Monastir, Monastir, Tunisia"}], "References": []}, {"ArticleId": 78781013, "Title": "Removing moiré patterns from single images", "Abstract": "Interference between the grids of the camera sensor and the screen cause moiré patterns to always appear on photographs captured from a screen, significantly affecting people’s ability to review images. We propose a novel method to remove such a screen moiré pattern from a single image. We characterize the degraded image as a composition of two layers: the latent layer and the moiré pattern layer. Because the screen moiré pattern is global and content-independent, we regard it as a group of sublayers, and we find that each sublayer after the shear transformation has a low-rank property. Combined with the piecewise constant feature of the latent layer, a convex model is proposed to solve the demoiréing problem. Experiments on synthetic and real data demonstrate its feasibility and efficiency.", "Keywords": "Moiré pattern ; Shear nuclear norm ; Low rank ; Convex optimization", "DOI": "10.1016/j.ins.2019.12.001", "PubYear": 2020, "Volume": "514", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "Faming <PERSON>", "Affiliation": "Shanghai Key Laboratory of Multidimensional Information Processing, and School of Computer Science and Technology, East China Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Wang", "Affiliation": "Shanghai Key Laboratory of Multidimensional Information Processing, and School of Computer Science and Technology, East China Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Multidimensional Information Processing, and School of Computer Science and Technology, East China Normal University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Multidimensional Information Processing, and School of Computer Science and Technology, East China Normal University, Shanghai, China;Corresponding author."}], "References": []}, {"ArticleId": 78781014, "Title": "Questaal: A package of electronic structure methods based on the linear muffin-tin orbital technique", "Abstract": "This paper summarises the theory and functionality behind Questaal , an open-source suite of codes for calculating the electronic structure and related properties of materials from first principles. The formalism of the linearised muffin-tin orbital (LMTO) method is revisited in detail and developed further by the introduction of short-ranged tight-binding basis functions for full-potential calculations. The LMTO method is presented in both Green’s function and wave function formulations for bulk and layered systems. The suite’s full-potential LMTO code uses a sophisticated basis and augmentation method that allows an efficient and precise solution to the band problem at different levels of theory, most importantly density functional theory, LDA + U , quasi-particle self-consistent GW and combinations of these with dynamical mean field theory. This paper details the technical and theoretical bases of these methods, their implementation in Questaal, and provides an overview of the code’s design and capabilities. Program summary Program Title: Questaal Program Files doi: http://dx.doi.org/10.17632/35jxxtzpdn.1 Code Ocean Capsule: https://doi.org/10.24433/CO.3778701.v1 Licensing provisions: GNU General Public License, version 3 Programming language: Fortran, C, Python, Shell Nature of problem: Highly accurate ab initio calculation of the electronic structure of periodic solids and of the resulting physical, spectroscopic and magnetic properties for diverse material classes with different strengths and kinds of electronic correlation. Solution method: The many electron problem is considered at different levels of theory: density functional theory, many body perturbation theory in the GW approximation with different degrees of self consistency (notably quasiparticle self-consistent GW ) and dynamical mean field theory. The solution to the single-particle band problem is achieved in the framework of an extension to the linear muffin-tin orbital (LMTO) technique including a highly precise and efficient full-potential implementation. An advanced fully-relativistic, non-collinear implementation based on the atomic sphere approximation is used for calculating transport and magnetic properties.", "Keywords": "Questaal ; Linear muffin tin orbital ; Screening transformation ; Density functional theory ; Many-body perturbation theory", "DOI": "10.1016/j.cpc.2019.107065", "PubYear": 2020, "Volume": "249", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, King’s College London, Strand, London WC2R 2LS, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, King’s College London, Strand, London WC2R 2LS, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Physics, Case Western Reserve University, Cleveland, OH 44106, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Scientific Computing Department, STFC Daresbury Laboratory, Warrington WA4 4AD, United Kingdom;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics and Astronomy and Nebraska Center for Materials and Nanoscience, University of Nebraska-Lincoln, Lincoln, NE 68588, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "American Physical Society, 1 Research Road, Ridge, NY 11961, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, King’s College London, Strand, London WC2R 2LS, United Kingdom"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Physics, King’s College London, Strand, London WC2R 2LS, United Kingdom"}], "References": []}, {"ArticleId": 78781068, "Title": "Utilizing multiple scattering effect for highly sensitive optical refractive index sensing", "Abstract": "The sensitivity of random optical diffraction to refractive index inhomogeneity is usually considered as a challenging obstacle for imaging or controlling light through scattering media. Here, on the opposite side, we utilize the natural randomness of strongly scattering media and their sensitivity to refractive index inhomogeneity to develop a novel optical sensor. Unlike various sensing technologies with trade-off between complexity and sensitivity, we demonstrate a very simple sensing technique, which uses just ground glass optical diffusers to achieve very high sensitivity. Light propagating through a scattering medium is scattered multiple times at multiple interfaces between ground glass and the surrounding environment creating a speckle pattern, which is sensitive to the environment’s refractive index. The correlation of speckle patterns indicates the change of refractive index around the scattering medium. Simply placing the rough surface of ground glasses in contact with sensing solution, we are able to detect the solution’s refractive index change at resolution of 3.87  ×  10<sup>−6</sup> RIU, which is equivalent to the glucose concentration change of 12 ppm. More interestingly, the sensitivity of the proposed approach could be improved simply by adding more scattering surfaces in contact with the target medium. Our simple technique could be very useful for prominent applications in refractive index sensing such as measuring concentration of solution or gas.", "Keywords": "Optical sensor ; Refractive index sensing ; Ground glass optical diffuser ; Glucose concentration", "DOI": "10.1016/j.sna.2019.111776", "PubYear": 2020, "Volume": "301", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "Vinh Tran", "Affiliation": "Centre for Optoelectronics and Biophotonics (COEB), School of Electrical and Electronic Engineering, The Photonics Institute (TPI), Nanyang Technological University Singapore, 50 Nanyang Avenue, Singapore, 639798, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Optoelectronics and Biophotonics (COEB), School of Electrical and Electronic Engineering, The Photonics Institute (TPI), Nanyang Technological University Singapore, 50 Nanyang Avenue, Singapore, 639798, Singapore;School of Electrical Science, Indian Institute of Technology Goa, At Goa College of Engineering Campus, Farmagudi, Ponda, Goa, 403401, India;Corresponding authors."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Optoelectronics and Biophotonics (COEB), School of Electrical and Electronic Engineering, The Photonics Institute (TPI), Nanyang Technological University Singapore, 50 Nanyang Avenue, Singapore, 639798, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Optoelectronics and Biophotonics (COEB), School of Electrical and Electronic Engineering, The Photonics Institute (TPI), Nanyang Technological University Singapore, 50 Nanyang Avenue, Singapore, 639798, Singapore;Corresponding authors."}], "References": []}, {"ArticleId": 78781587, "Title": "Perception‐aware autonomous mast motion planning for planetary exploration rovers", "Abstract": "<p>Highly accurate real‐time localization is of fundamental importance for the safety and efficiency of planetary rovers exploring the surface of Mars. Mars rover operations rely on vision‐based systems to avoid hazards as well as plan safe routes. However, vision‐based systems operate on the assumption that sufficient visual texture is visible in the scene. This poses a challenge for vision‐based navigation on Mars where regions lacking visual texture are prevalent. To overcome this, we make use of the ability of the rover to actively steer the visual sensor to improve fault tolerance and maximize the perception performance. This paper answers the question of where and when to look by presenting a method for predicting the sensor trajectory that maximizes the localization performance of the rover. This is accomplished by an online assessment of possible trajectories using synthetic, future camera views created from previous observations of the scene. The proposed trajectories are quantified and chosen based on the expected localization performance. In this study, we validate the proposed method in field experiments at the Jet Propulsion Laboratory (JPL) Mars Yard. Furthermore, multiple performance metrics are identified and evaluated for reducing the overall runtime of the algorithm. We show how actively steering the perception system increases the localization accuracy compared with traditional fixed‐sensor configurations.</p>", "Keywords": "automation;motion planning;space robotics;vision‐based navigation", "DOI": "10.1002/rob.21925", "PubYear": 2020, "Volume": "37", "Issue": "5", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, West Virginia University, Morgantown, West Virginia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jet Propulsion Laboratory, California Institute of Technology, Pasadena, California"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>moham<PERSON>i", "Affiliation": "Jet Propulsion Laboratory, California Institute of Technology, Pasadena, California"}], "References": []}, {"ArticleId": 78781626, "Title": "A bio-inspired quaternion local phase CNN layer with contrast invariance and linear sensitivity to rotation angles", "Abstract": "Deep learning models have been particularly successful with image recognition using Convolutional Neural Networks (CNN). However, the learning of a contrast invariance and rotation equivariance response may fail even with very deep CNNs or by large data augmentations in training. We were inspired by the V1 visual features of the mammalian visual system to emulate as much as possible the early visual system and add more invariant capacities to the CNN. We present a new quaternion local phase convolutional neural network layer encoding three local phases. We present two experimental setups: An image classification task with four contrast levels, and a linear regression task that predicts the rotation angle of an image. In sum, we obtain new patterns and feature representations for deep learning, which capture illumination invariance and a linear response to rotation angles.", "Keywords": "41A05 ; 41A10 ; 65D05 ; 65D17", "DOI": "10.1016/j.patrec.2019.12.001", "PubYear": 2020, "Volume": "131", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Gobierno del Estado de Jalisco y, Universidad Autónoma de Guadalajara, Independencia 55, Guadalajara 44100, Jalisco, México;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>Descamps", "Affiliation": "Universitat Politècnica de Catalunya and Barcelona Supercomputer Centre, Jordi Girona 1–3, Omega, 08034, Barcelona, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Gobierno del Estado de Jalisco, Independencia 55, Guadalajara, 44100 Jalisco, México"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>-Colores", "Affiliation": "Facultad de informática, Universidad Autónoma de Querétaro, Querétaro, México"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gobierno del Estado de Jalisco, Independencia 55, Guadalajara, 44100 Jalisco, México"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Cortés", "Affiliation": "Universitat Politècnica de Catalunya and Barcelona Supercomputer Centre, Jordi Girona 1–3, Omega, 08034, Barcelona, Spain"}], "References": []}, {"ArticleId": 78781683, "Title": "Safety of intrastromal injection of polyhexamethylene biguanide and propamidine isethionate in a rabbit model", "Abstract": "<p><i>Acanthamoeba</i> keratitis (AK) is difficult to treat, especially when the corneal deep stroma is involved. Intrastromal injection of antimicrobial agents is an effective adjuvant therapy for deep recalcitrant microbial keratitis; however, it has not been used to treat AK due to suspected drug toxicity. The purpose of this study was to evaluate the toxicity of corneal intrastromal injection of polyhexamethylene biguanide (PHMB) and propamidine isethionate (Brolene®, Sanofi) in New Zealand white rabbits. We performed intrastromal injections of PHMB (0.02 or 0.01%) and propamidine isethionate (0.1 or 0.05%) into the rabbits' right corneas. The left corneas were injected with phosphate-buffered saline as controls. The rabbits were sacrificed on the 7th day after injection, and the corneal buttons were harvested for further evaluation by slit lamp microscopy, specular microscopy, hematoxylin and eosin staining, scanning electron microscopy, terminal deoxynucleotidyl transferase (TdT) dUTP nick-end labeling assays, and WST-1 assays. We found that intrastromal injection of 0.02% PHMB or 0.1% propamidine isethionate resulted in corneal epithelial erosion, corneal edema, and severe neovascularization. However, 0.01% PHMB or 0.05% propamidine isethionate did not induce obvious cornea toxicity. In conclusion, intrastromal injection of 0.01% PHMB or 0.05% propamidine isethionate may be promising adjunctive treatments for deep stromal AK.</p><p>© 2020 THE AUTHORS. Published by Elsevier BV on behalf of Cairo University.</p>", "Keywords": "Acanthamoeba keratitis;Corneal intrastromal injection;Corneal toxicity;Polyhexamethylene biguanide (PHMB);Propamidine isethionate", "DOI": "10.1016/j.jare.2019.11.012", "PubYear": 2020, "Volume": "22", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Ophthalmology, National Cheng Kung University Hospital, College of Medicine, National Cheng Kung University, Tainan, Taiwan."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, National Cheng Kung University, Tainan, Taiwan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Ophthalmology, National Cheng Kung University Hospital, College of Medicine, National Cheng Kung University, Tainan, Taiwan. ;Institute of Clinical Medicine, College of Medicine, National Cheng Kung University, Tainan, Taiwan."}], "References": []}, {"ArticleId": 78782140, "Title": "Noise-tolerant Z-type neural dynamics for online solving time-varying inverse square root problems: A control-based approach", "Abstract": "Assuming that the solving process is free of measurement noises, the zeroing neural dynamics (Z-type) based on different zeroing dynamics for online solving time-varying inverse square root problems (TVISRPs) are revisited from the perspective of control technique and unified into a control framework. However, noises are ubiquitous and unavoidable in the application of the real-time system. Therefore, the modified Z-type models (MZTMs) with different measurement noises are needed for online solving TVISRPs. In this study, the MZTMs are first developed, analyzed and verified for online solution of the TVISRPs with different measurement noises. Furthermore, theoretical analyses infer that the MZTMs globally and exponentially converge to the theoretical solution. Compared with the traditional Z-type neural dynamics model (ZTNDM) and gradient neural dynamics model (GNDM), two illustrative examples are described and investigated to substantiate the efficiency and superiority of the developed MZTMs. Finally, a systematic method is proposed via exploring control framework to construct MZTMs for online solving TVISRPs with great robustness and accuracy.", "Keywords": "Modified Z-type models (MZTMs) ; Time-varying inverse square root problems (TVISRPs) ; Online solution ; Nonlinear control system ; Control viewpoint", "DOI": "10.1016/j.neucom.2019.11.035", "PubYear": 2020, "Volume": "382", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Technology, Jilin Agricultural University, Changchun 130118, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Technology, Jilin Agricultural University, Changchun 130118, PR China"}, {"AuthorId": 3, "Name": "Zhongbo Sun", "Affiliation": "Department of Control Engineering, Changchun University of Technology, Changchun 130012, PR China;Key Laboratory of Bionic Engineering of Ministry of Education, Jilin University, Changchun 130025, PR China;Corresponding author at: Department of Control Engineering, Changchun University of Technology, Changchun 130012, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Control Engineering, Changchun University of Technology, Changchun 130012, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, PR China;Corresponding author at: School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, PR China"}], "References": []}, {"ArticleId": 78782154, "Title": "Data-based stable value iteration optimal control for unknown discrete-time systems with time delays", "Abstract": "In this study, a novel data-based stable value iteration (SVI) optimal control scheme is presented in order to tackle with the linear discrete-time (DT) system with multiple time delays. Due to the difficulty in acquiring the knowledge of system dynamics, the optimal control strategies could be computed with only history input and output database by employing an estimator on the basis of adaptive dynamic programming (ADP) technology. By analyzing features of time delay systems, a homologous equivalent notion of delay-free systems is proposed so that the optimal control policy against systems with multiple time delays could be designed indirectly. Moreover, four equivalent conditions are deduced between the two related systems. The convergence of the rising SVI algorithm with discount factor is discussed according to optimal control principles. The proposed SVI algorithm is proved to converge to optimal values with proper discount factor. In the end, two numerical examples are given, and simulation results illustrate that the presented data-based SVI method is effective.", "Keywords": "Adaptive dynamic programming ; Data-based control ; Multiple time delays ; Stable value iteration ; Optimal control", "DOI": "10.1016/j.neucom.2019.11.047", "PubYear": 2020, "Volume": "382", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "He Ren", "Affiliation": "School of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110004, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Synthetical Automation for Process Industries, Northeastern University, Shenyang, Liaoning 110004, PR China;School of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110004, PR China;Corresponding author at: State Key Laboratory of Synthetical Automation for Process Industries, Northeastern University, Shenyang, Liaoning 110004, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110004, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110004, PR China"}], "References": []}, {"ArticleId": 78782250, "Title": "Probabilistic physical search on general graphs: approximations and heuristics", "Abstract": "<p id=\"Par1\" class=\"Para\">We consider an agent seeking to obtain an item, potentially available at different locations in a physical environment. The traveling costs between locations are known in advance, but there is only probabilistic knowledge regarding the possible prices of the item at any given location. Given such a setting, the problem is to find a plan that maximizes the probability of acquiring the good while minimizing both travel and purchase costs. Sample applications include agents in search-and-rescue or exploration missions, e.g., a rover on Mars seeking to mine a specific mineral. These probabilistic physical search problems have been previously studied, but we present the first approximation and heuristic algorithms for solving such problems on general graphs. We establish an interesting connection between these problems and classical graph-search problems, which led us to provide the approximation algorithms and hardness of approximation results for our settings. We further suggest several heuristics for practical use, and demonstrate their effectiveness with simulation on a real graph structure.</p>", "Keywords": "Graph search ; Planning under uncertainty ; Stochastic search", "DOI": "10.1007/s10458-019-09423-z", "PubYear": 2020, "Volume": "34", "Issue": "1", "JournalId": 21187, "JournalTitle": "Autonomous Agents and Multi-Agent Systems", "ISSN": "1387-2532", "EISSN": "1573-7454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ariel University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ariel University"}], "References": []}, {"ArticleId": 78782266, "Title": "A study of periodic operation in bioprocess systems: Internal and external oscillations", "Abstract": "For many dynamic processes, improved time-average performance could be achieved by periodic operations, which introduce periodic variations to the state-variables deliberately. In this paper, a bioreactor is adopted as case-study where periodic operations are realized through both external and internal oscillations. Since the mechanism for generating external and internal oscillations vary significantly, different mathematical tools are discussed. Specifically, to understand the influence of external oscillations, a nonlinear analysis method is proposed which relies on La<PERSON>–Bo<PERSON> transform; then, numerical solution of a self-oscillator using a collocation technique is detailed to study internal oscillations. Next, scenarios that elucidate the integration of internal and external oscillations are offered. Finally, the potential elimination of oscillations through a membrane bioreactor is explored. Through this study, the analysis methods offer an integrated framework which could be used to analyze a number of periodic operation scenarios, facilitating the implementation of novel operational strategies for the process industries.", "Keywords": "La<PERSON><PERSON><PERSON><PERSON> transforms ; Self-oscillator ; Integration of oscillations ; The membrane bioreactor", "DOI": "10.1016/j.compchemeng.2019.106661", "PubYear": 2020, "Volume": "133", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Beijing Key Lab of Membrane Science and Technology, College of Chemical Engineering, Beijing University of Chemical Technology, 100029 Beijing, China;Department of Chemical Engineering, University of California, Davis, CA 95616, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, University of California, Davis, CA 95616, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing Key Lab of Membrane Science and Technology, College of Chemical Engineering, Beijing University of Chemical Technology, 100029 Beijing, China;Corresponding author."}], "References": []}, {"ArticleId": 78782335, "Title": "Image-based face beauty analysis via graph-based semi-supervised learning", "Abstract": "<p>Automatic facial beauty analysis has become an emerging research topic. Despite some achieved advances, current methods and systems suffer from at least two limitations. Firstly, many developed systems rely on the use of ad-hoc hand-crafted features that were designed for generic pattern recognition problems. Secondly, while Deep Convolutional Neural Nets (DCNN) have been recently demonstrated to be a promising area of research in statistical machine learning, their use for automatic face beauty analysis may not guarantee optimal performances due to the use of a limited amount of face images with beauty scores. In this paper, we attempt to overcome these two main limitations by jointly exploiting two tricks. First, instead of using hand-crafted face features we use deep features of a pre-trained DCNN able to generate a high-level representation of a face image. Second, we exploit manifold learning theory and deploy three graph-based semi-supervised learning methods in order to enrich model learning without the need of additional labeled face images. These schemes perform graph-based score propagation. The proposed schemes were tested on three public datasets for beauty analysis: SCUT-FBP, M<sup>2</sup>B, and SCUT-FBP5500. These experiments, as well as many comparisons with supervised schemes, show that the scheme coined Kernel Flexible Manifold Embedding compares favorably with many supervised schemes. They also show that its performances in terms of error prediction and Pearson Correlation are better than those reported for the used datasets.</p>", "Keywords": "Image-based face beauty analysis; Semi-supervised learning; Graph-based label propagation; Deep face features", "DOI": "10.1007/s11042-019-08206-8", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of the Basque Country UPV/EHU, San Sebastian, Spain;IKERBASQUE, Basque Foundation for Science, Bilbao, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of the Basque Country UPV/EHU, San Sebastian, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of the Basque Country UPV/EHU, San Sebastian, Spain;Northwestern Polytechnic University, Xian, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of the Basque Country UPV/EHU, San Sebastian, Spain;IKERBASQUE, Basque Foundation for Science, Bilbao, Spain;Donostia International Physics Center (DIPC), San Sebastian, Spain"}], "References": []}, {"ArticleId": 78782354, "Title": "Integrated feature set using aggregate channel features and histogram of sparse codes for human detection", "Abstract": "<p>The scientific community witnessed revolutionary changes with algorithms and data sets aiming for precise human detection from images and videos which are largely driven by the quality of features extracted. Regardless of the labyrinth of the existing detectors, featured human detection with near accuracy from complicated real-time data sets remains a major challenge. Here we propose an improved feature set by merging the fast and accurate aggregate channel features (ACF) and the data specific dictionary learned histogram of sparse codes (HSC) for human detection. This integrated feature set efficiently fuses the first-order information from the histogram of oriented gradient channels embedded in the ACF detector and the data specific intelligence contained in the HSC channels. The proposed detector outperforms the state-of-the-art ACF detector in terms of miss rate and average precision on challenging datasets. It is worth to be noted that there is a decrease with the miss rate by a factor of 13% and 5% for INRIA and Caltech pedestrian datasets respectively in comparison with baseline detector. Along with the detection of more instances, our detector reduced the number of false positives compared to other existing detectors. Although further modifications are warranted, our proposed detector could produce a tangible and palpable response with human detection in the vast arena of computer vision.</p>", "Keywords": "Human detection; Aggregate channel features; Histogram of sparse codes; Dictionary learning", "DOI": "10.1007/s11042-019-08498-w", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Blossom <PERSON><PERSON>", "Affiliation": "College of Engineering Trivandrum, Kerala, India"}, {"AuthorId": 2, "Name": "Jiji C.V.", "Affiliation": "College of Engineering Trivandrum, Kerala, India"}], "References": []}, {"ArticleId": 78782434, "Title": "SAPMS: a secure and anonymous parking management system for autonomous vehicles", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2020.10025661", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 78782443, "Title": "Destructiveness of lexicographic parsimony pressure and alleviation by a concatenation crossover in genetic programming", "Abstract": "For theoretical analyses there are two specifics distinguishing GP from many other areas of evolutionary computation: the variable size representations, in particular yielding a possible bloat (i.e. the growth of individuals with redundant parts); and also the role and the realization of crossover, which is particularly central in GP due to the tree-based representation. Whereas some theoretical work on GP has studied the effects of bloat, crossover had surprisingly little share in this work. We analyze a simple crossover operator in combination with randomized local search, where a preference for small solutions minimizes bloat (lexicographic parsimony pressure); we denote the resulting algorithm Concatenation Crossover GP. We consider three variants of the well-studied Majority test function, adding large plateaus in different ways to the fitness landscape and thus giving a test bed for analyzing the interplay of variation operators and bloat control mechanisms in a setting with local optima. We show that the Concatenation Crossover GP can efficiently optimize these test functions, while local search cannot be efficient for all three variants independent of employing bloat control.", "Keywords": "Genetic programming ; Mutation ; Theory ; Run time analysis", "DOI": "10.1016/j.tcs.2019.11.036", "PubYear": 2020, "Volume": "816", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>so <PERSON>ner Institute, University of Potsdam, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>so <PERSON>ner Institute, University of Potsdam, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETH Zürich, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON>so <PERSON>ner Institute, University of Potsdam, Germany"}], "References": []}, {"ArticleId": 78782523, "Title": "An improved key pre-distribution scheme based on the security level classification of keys for wireless sensor networks", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2020.10025662", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 78782777, "Title": "A vertical structure photodetector based on all‐inorganic perovskite quantum dots", "Abstract": "<p>In this work, the vertical structure photodetector based on CsPbBr<sub>3</sub> quantum dots (QDs) with a structure of indium tin oxide (ITO)/zinc oxide (ZnO)/CsPbBr<sub>3</sub> QDs/Au is reported. In this device, CsPbBr<sub>3</sub> QDs film works as the light‐harvesting layer, and ZnO QDs film acts as the electron transport channel, which can extract the electron efficiently and improve the quality of CsPbBr<sub>3</sub> QDs film. As a result, the on/off ratio, detectivity and rise time (decay time) of CsPbBr<sub>3</sub>/ZnO hybrid photodetector are measured to be 2.4 × 10<sup>6</sup>, 2.25 × 10<sup>11</sup>, and 62 milliseconds (82 ms) under 0‐V bias. This work inspires the development of vertical structure photodetectors based on the all‐inorganic perovskite QDs.</p>", "Keywords": "all‐inorganic perovskite quantum dot;vertical structure photodetector;zno quantum dot", "DOI": "10.1002/jsid.853", "PubYear": 2020, "Volume": "28", "Issue": "1", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "Shikai Yan", "Affiliation": "Joint International Research Laboratory of Information Display and Visualization, School of Electronic Science and Engineering, Southeast University, Nanjing, Jiangsu, China"}, {"AuthorId": 2, "Name": "Qing Li", "Affiliation": "Joint International Research Laboratory of Information Display and Visualization, School of Electronic Science and Engineering, Southeast University, Nanjing, Jiangsu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Joint International Research Laboratory of Information Display and Visualization, School of Electronic Science and Engineering, Southeast University, Nanjing, Jiangsu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Joint International Research Laboratory of Information Display and Visualization, School of Electronic Science and Engineering, Southeast University, Nanjing, Jiangsu, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Joint International Research Laboratory of Information Display and Visualization, School of Electronic Science and Engineering, Southeast University, Nanjing, Jiangsu, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Joint International Research Laboratory of Information Display and Visualization, School of Electronic Science and Engineering, Southeast University, Nanjing, Jiangsu, China"}], "References": []}, {"ArticleId": 78782811, "Title": "THE CLIMATE-CHANGE MOVEMENT ACCORDING TO CHANGE SCIENCE", "Abstract": "The study of political movements can be a fascinating means to understanding how change works. However, with their bias for drama, the press and social media tend to cloud our understanding. This article filters out the drama and examines the climate-change movement through the lens of change science. The article explores the change dynamics in play and their significance to the movement’s likelihood of success.", "Keywords": "", "DOI": "10.1080/07366981.2020.1698173", "PubYear": 2020, "Volume": "61", "Issue": "1", "JournalId": 3790, "JournalTitle": "EDPACS", "ISSN": "0736-6981", "EISSN": "1936-1009", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 78783037, "Title": "Gait feature extraction and gait classification using two-branch CNN", "Abstract": "<p>As a promising biometric identification method, gait recognition has many advantages, such as suitable for human identification at a long distance, requiring no contact and hard to imitate. However, due to the complex external factors in the gait data sampling process and the clothing changes of the person to be identified, gait recognition still faces numerous challenges in practical applications. In this paper, we present a novel solution for gait feature extraction and gait classification. Firstly, two kinds of Two-branch Convolution Neural Network (TCNN), i.e., middle-fusion TCNN and last-fusion TCNN, to improve the correct recognition rate of gait recognition are presented. Secondly, we construct Multi-Frequency Gait Energy Images (MF-GEIs) to train the proposed TCNNs networks and then extract refined gait features using the trained TCNNs. Finally, the output of each TCNN is utilized to train an SVM gait classifier separately which will be used for gait classification and recognition. In addition, the proposed solution is measured on CASIA dataset B and OU-ISIR LP dataset. Both experimental results show that our solution outperforms various existing methods.</p>", "Keywords": "Gait classification; Deep learning; Convolution neural network (CNN); Support vector machine (SVM); Gait energy image (GEI); Multi-frequency gait energy image (MF-GEI)", "DOI": "10.1007/s11042-019-08509-w", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Electromagnetic Wave Information Technology and Metrology of Zhejiang Province, College of Information Engineering, China Jiliang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Electromagnetic Wave Information Technology and Metrology of Zhejiang Province, College of Information Engineering, China Jiliang University, Hangzhou, China"}], "References": []}, {"ArticleId": 78783071, "Title": "A parallel implicit hole-cutting method based on background mesh for unstructured Chimera grid", "Abstract": "As an efficient simulation method for complex configurations and moving boundary problems in computational fluid dynamics, Chimera or overset grid techniques have been widely used in many aspects of aeronautics and astronautics. However, there are still some bottlenecks for the current hole-cutting method to handle large scale mesh, such as memory storage limitation and parallel efficiency. In this paper, a parallel implicit hole-cutting method based on unstructured background mesh is presented. The method is based on the parallel searching of donor cells for all grid nodes. In order to reduce the memory consumption of the searching procedure for the large-scale grids, a global-to-local (GTL) searching strategy as well as the background grid approach is developed. To improve the connectivity of overset domains, a parallel front advancing method is adopted to automatically distinguish the active regions. Finally, the efficiency and effectiveness of the present Chimera grid method are validated by some test cases and applications, including multi-store separation from a fighter and a missile pitching-up maneuvering with rudder deflection under a control command. The numerical results demonstrate the potential for steady and unsteady CFD simulations for complex geometries.", "Keywords": "Implicit hole-cutting ; Background mesh ; Unstructured moving Chimera grid ; Unsteady flow ; Large scale mesh ; Parallel computing", "DOI": "10.1016/j.compfluid.2019.104403", "PubYear": 2020, "Volume": "198", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Aerodynamics, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China;Computational Aerodynamics Institute, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computational Aerodynamics Institute, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Aerodynamics, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China;Computational Aerodynamics Institute, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computational Aerodynamics Institute, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Aerodynamics, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China;Computational Aerodynamics Institute, China Aerodynamics Research and Development Center, Mianyang, Sichuan 621000, China;Corresponding author at: State Key Laboratory of Aerodynamics, China Aerodynamics Research and Development Center, Mianyang Sichuan 621000, China."}], "References": [{"Title": "A parallel algorithm for chimera grid with implicit hole cutting method", "Authors": "Xiaodong Hu; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2", "Page": "169", "JournalTitle": "The International Journal of High Performance Computing Applications"}]}, {"ArticleId": 78783084, "Title": "Characterization of single-ended 9T SRAM cell", "Abstract": "<p>We present a circuit-level technique of designing a lower write-power along with variability-resistant 9-MOFTET static random-access memory cell. Our proposed bitcell exhibits lower write-power consumption owing to reduction of activity factor and breakup of feedback path between the cross-coupled inverters during write operation. It exhibits higher read static noise margin (by 3.09 ×) compared with standard 6T SRAM cell @ minimum-area. LP9T shows higher static margin for write operation (by 41%) compared with 8T (S6T) @ iso-area (minimum-area). These improvements are achieved due to breakup of feedback path during the process of writing a bit on to the storage node. The paper investigates in detail the influence of variation in process related parameters, environmental parameters such as supply voltage and temperature on most of the important design parameters of the bitcell and compares the obtained simulation results with conventional 6-MOSFET (6T) and 8-MOSFET (8T) bitcells. It demonstrates its invariableness by showing 1.5 × tighter disperse in read time variability with a cost of 1.41 × higher read time compared with S6T @ minimum-area. It also exhibits 39% narrower disperse in read time variability in comparison to 8T @ iso-area. It draws lower power (2.06 ×) from supply voltage while flipping of stored data during write mode compared with standard 8T SRAM cell @ iso-area. It also compares key design metrics of LP9T with those of few other 9T SRAM cells found in the literature. This work also realizes the proposed design using CNFET. The CNFET-based design outperforms its CMOS counterpart in all respect.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04700-z", "PubYear": 2020, "Volume": "26", "Issue": "5", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, Madanapalle Institute of Technology and Science, Madanapalle, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, BIT, Mesra, Ranchi, India"}], "References": []}, {"ArticleId": 78783210, "Title": "Hybrid particle swarm optimization for rule discovery in the diagnosis of coronary artery disease", "Abstract": "<p>Coronary artery disease (CAD) is one of the major causes of mortality worldwide. Knowledge about risk factors that increase the probability of developing CAD can help to understand the disease better and assist in its treatment. Recently, modern computer‐aided approaches have been used for the prediction and diagnosis of diseases. Swarm intelligence algorithms like particle swarm optimization (PSO) have demonstrated great performance in solving different optimization problems. As rule discovery can be modelled as an optimization problem, it can be mapped to an optimization problem and solved by means of an evolutionary algorithm like PSO. An approach for discovering classification rules of CAD is proposed. The work is based on the real‐world CAD data set and aims at the detection of this disease by producing the accurate and effective rules. The proposed algorithm is a hybrid binary‐real PSO, which includes the combination of categorical and numerical encoding of a particle and a different approach for calculating the velocity of particles. The rules were developed from randomly generated particles, which take random values in the range of each attribute in the rule. Two different feature selection methods based on multi‐objective evolutionary search and PSO were applied on the data set, and the most relevant features were selected by the algorithms. The accuracy of two different rule sets were evaluated. The rule set with 11 features obtained more accurate results than the rule set with 13 features. Our results show that the proposed approach has the ability to produce effective rules with highest accuracy for the detection of CAD.</p>", "Keywords": "classification;coronary artery disease (CAD);hybrid particle swarm optimization;rule discovery", "DOI": "10.1111/exsy.12485", "PubYear": 2021, "Volume": "38", "Issue": "1", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>moghadam", "Affiliation": "Department of Computer Engineering, Ferdowsi University of Mashhad, Mashhad, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Département d'Informatique, Université du Québec à Montré al, Montré al, Quebec, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, PNU University, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management and Enterprise, University of Southern Queensland, Queensland, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Telecomputing, Faculty of Physics, Mathematics and Computer Science, Cracow University of Technology, Krakow, Poland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Computer Engineering, Ngee Ann Polytechnic, Singapore, Singapore"}], "References": [{"Title": "A new nested ensemble technique for automated diagnosis of breast cancer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "123", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 78783370, "Title": "Strict Lyapunov–Krasovskiĭ Functionals for undirected networks of Euler–Lagrange systems with time-varying delays", "Abstract": "For an undirected network of nonidentical interconnected Euler–Lagrange systems whose communication is affected by varying time-delays that may not be differentiable, we consider the problem of establishing leaderless and leader–follower consensus via the simplest Proportional plus damping decentralized controller. The main contribution of this work is to prove that the agents’ positions and velocities converge uniformly , globally, and asymptotically to a common non-specified position in the leaderless case, and to a given reference in the leader–follower case. The main results are established via <PERSON><PERSON><PERSON><PERSON>’s direct method; a Strict L<PERSON>nov–Krasovskiĭ Functional is constructed, to the best of our knowledge, for the first time in the literature. It is shown that the resulting closed-loop system is Input-to-State Stable with regards to external additive inputs (perturbations). In turn, the separation principle applies to a certainty-equivalence controller, implemented with any globally convergent velocity estimator, such as the Immersion and Invariance observer.", "Keywords": "<PERSON><PERSON>r–Lagrange dynamics ; Multi-agent systems ; Time-delays ; Strict Lya<PERSON>nov functions", "DOI": "10.1016/j.sysconle.2019.104579", "PubYear": 2020, "Volume": "135", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, CUCEI, University of Guadalajara, Guadalajara, Mexico;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DTIS, ONERA, Université Paris-Saclay, F-91123 Palaiseau, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratoire des Signaux et Systèmes, CNRS, Gif-sur-Yvette, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, University of California Santa Cruz, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, CUCEI, University of Guadalajara, Guadalajara, Mexico"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Laboratoire des Signaux et Systèmes, CNRS, Gif-sur-Yvette, France;ITMO University, Kronverkskiy av. 49, Saint Petersburg, 197101, Russia"}], "References": []}, {"ArticleId": 78783712, "Title": "Relationship between UAVs and Ambient objects with threat situational awareness through grid map-based ontology reasoning", "Abstract": "Autonomous threat situational awareness for unmanned aerial vehicles (UAVs) is required in various fields. Although several approaches have been proposed for autonomous threat situational awareness, most of them involved reasoning of the semantic information of objects. Therefore, in this paper, we propose a method to achieve threat situation awareness in UAVs based on reasoning of the relationship among objects. In this study, there are three main ways to recognize a threat to a UAV. First, information of a recognized objects is expressed using a level of detail-based grid map. Second, the concepts of objects around UAV are defined as an ontology, and the relationship and situations between objects are defined as SWRL. Third, through the ontology reasoning, the simulator visualizes recognizing the relationships of objects and threat situations for the UAV. To validate the proposal, a data generator was constructed to perform reasoning for a virtual threat situation. The generated data ensured that the expected relationships between objects and situations were properly recognized. Four scenarios were analyzed and the relationships and situations that might be encountered in each scenario were set. The accuracy of the overall situational awareness averaged 95%, and the reasoning speed averaged 583 ms.", "Keywords": "UAV ; threat situation awareness ; grid map ; ontology ; SWRL", "DOI": "10.1080/1206212X.2019.1698694", "PubYear": 2022, "Volume": "44", "Issue": "2", "JournalId": 4962, "JournalTitle": "International Journal of Computers and Applications", "ISSN": "1206-212X", "EISSN": "1925-7074", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, SoongSil University, Seoul, Republic of Korea"}, {"AuthorId": 2, "Name": "Hyun-Kyu Park", "Affiliation": "Department of Computer Science, SoongSil University, Seoul, Republic of Korea"}, {"AuthorId": 3, "Name": "Batselem Jagvaral", "Affiliation": "Department of Computer Science, SoongSil University, Seoul, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Agency for Defense Development, Seoul, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Agency for Defense Development, Seoul, Republic of Korea"}, {"AuthorId": 6, "Name": "Young-Tack <PERSON>", "Affiliation": "Department of Computer Science, SoongSil University, Seoul, Republic of Korea"}], "References": []}, {"ArticleId": ********, "Title": "A comprehensive and systematized review of energy-efficient routing protocols in wireless sensor networks", "Abstract": "In the past decade, research community has paid a good attention towards energy-efficient data routing in wireless sensor networks (WSNs). WSNs have left an imprint in various areas, but sensor nodes are battery powered having limited lifetime. So, while designing the routing protocols, the prime concern is reducing the energy consumption and elongating the lifetime of the network. In this review, we have done comprehensive review on routing protocols focusing on prolonging the network lifetime. We have proposed different classifications of routing protocols to have the in-depth knowledge about the literature. These are operation based, environment based, objectives based and a combined form of these along with discussed papers. Thereafter, strength and weakness of the papers are found by doing a comparative analysis and further, a feature-based analysis is done as well to get a quick insight about the network models. Subsequently, future directions are discussed. This review article is written with a clear objective, which is to enable the beginners who want to do research on energy efficiency of the WSNs, by equipping them with understanding of different methods of achieving the same objective and grouping them with the similarity of their utility.", "Keywords": "Wireless sensor networks ; routing ; single path routing ; multi-path routing ; network lifetime ; delay ; fault tolerance", "DOI": "10.1080/1206212X.2019.1697513", "PubYear": 2022, "Volume": "44", "Issue": "1", "JournalId": 4962, "JournalTitle": "International Journal of Computers and Applications", "ISSN": "1206-212X", "EISSN": "1925-7074", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, National Institute of Technology Kurukshetra, Kurukshetra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, National Institute of Technology Kurukshetra, Kurukshetra, India"}], "References": []}, {"ArticleId": ********, "Title": "Automatic identification of acute arthritis from ayurvedic wrist pulses", "Abstract": "Traditional ayurvedic doctors examine the state of the body by analysing the wrist pulse from the patient. Mysteriously, the characteristics of the pulses vary corresponding to the various changes in the body. The three pulses acquired from the wrist are named as vata, pitta and kapha. Ayurveda says that when there is imbalance in these three doshas, one will have disease. Two different diseases will have different patterns in their pulse characteristics. Thus, the wrist pulse signal serves as a tool to analyse the health status of a patient. In the earlier work, we have standardised the signals for normal persons and then classified the diabetic cases using approximate entropy (ApEn) (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2011) and later enhanced the results using sample entropy. In the present work, sample entropy (SampEn) is being used to classify for the acute arthritis cases.", "Keywords": "vata ; pitta ; kapha ; approximate entropy; ApEn ; sample entropy ; SampEn", "DOI": "10.1504/IJCAET.2020.103840", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Engineering, SASTRA University, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, NSN College of Engineering, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, SASTRA University, India"}], "References": []}, {"ArticleId": 78784081, "Title": "Hand-drawn electronic component recognition using deep learning algorithm", "Abstract": "Hand-drawn circuit recognition plays an increasingly important role in circuit design work and electrical knowledge teaching. Hand-drawn electronic component recognition is an indispensable part of hand-drawn circuit recognition. Accurate electronic component recognition ensures accurate circuit recognition. In this paper, a hand-drawn electronic component recognition method using a Convolutional Neural Network (CNN) and a softmax classifier is proposed. The CNN composed of a convolutional layer, an activation layer and an average-pooling layer is designed to extract features of a hand-drawn electronic component image. The kernel function for the CNN is obtained by a sparse auto-encoder method. A softmax classifier is trained for classification based on the features extracted by the CNN. The recognition method can identify rotating electronic components because of the added rotated image and achieve 95% recognition accuracy.", "Keywords": "electronic component recognition ; CNN ; convolutional neural network ; aparse auto-encoder", "DOI": "10.1504/IJCAT.2020.103905", "PubYear": 2020, "Volume": "62", "Issue": "1", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Anhui University, Hefei, Anhui 230601, China; School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, Jiangsu 212013, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Anhui University, Hefei, Anhui 230601, China; School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, Jiangsu 212013, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, Jiangsu 212013, China"}], "References": []}, {"ArticleId": 78784111, "Title": "Improved automatic age estimation algorithm using a hybrid feature selection", "Abstract": "Age estimation (AE) is one of the significant biometric behaviours for emphasising the identity authentication. In facial image, automatic-AE is an actively researched topic, which is also an important but challenging study in the field of face recognition. This paper explores several algorithms utilised to improve AE and the combination of features and classifiers are associated. Initially, the facial image databases are trained and then the features are extracted by employing several algorithms like histogram of oriented gradients (HOG), binary robust invariant scalable keypoints (BRISK), and local binary pattern (LBP). Here, the AE is done in three various age groups from 20 to 30, 31 to 50 and above 50. The age groups are classified by utilising Naïve Bayes classifier (NBC). AE model is calculated by employing the Indian face age database (IFAD) and labelled Wikipedia face (LWF) aging databases obtaining optimistic result with success rate.", "Keywords": "age estimation ; binary robust invariant scalable keypoints ; histogram of oriented gradients ; local binary pattern ; Naïve Bayes classifier", "DOI": "10.1504/IJCAET.2020.103837", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "East West Institute of Technology, No. 63, Anjananagar, Off Magadi Road, Near BEL Layout, Bengaluru, Karnataka 560091, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bangalore Institute of Technology, KR Road, VV Puram, Bengaluru, Karnataka 560004, India"}], "References": []}, {"ArticleId": 78784119, "Title": "Improved indoor location tracking system for mobile nodes", "Abstract": "The solutions to the problem of the tracking a wireless node is approached conventionally by: 1) proximity detection; 2) triangulation; 3) scene analysis methods. In these, scene analysis method is simple, accurate and less expensive. Indoor localisation technologies need to address the existing inaccuracy and inadequacy of global positioning-based systems (GPS) in indoor environments (such as urban canyons, inside large buildings, etc.). This paper presents a novel indoor Wi-Fi tracking system with minimal error in the presence of barrier using Bayesian inference method. The system integrates an android app and python scripts (that run on server) to identify the position of the mobile node within an indoor environment. The received signal strength indicator (RSSI) method is used for tracking. Experimental results presented to illustrate the performance of the system comparing with other methods. From the tracked nodes, a theoretical solution is proposed for finding shortest path using Steiner nodes.", "Keywords": "location tracking ; global positioning-based systems ; GPS ; MANETs ; mobile nodes ; Wi-Fi access points ; wireless local area networks ; WLAN ; Bayesian inference ; received signal strength indicator ; RSSI ; shortest paths ; Steiner nodes", "DOI": "10.1504/IJCAET.2020.103835", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, 632014, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "WIPRO, Chennai, 600119, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, 632014, India"}], "References": []}, {"ArticleId": 78784550, "Title": "Spectral classification of ecological spatial polarization SAR image based on target decomposition algorithm and machine learning", "Abstract": "<p>With the development of science and technology, the classification of polarimetric SAR images has become an important part of the research of target recognition and image interpretation. However, for the research method is relatively simple and the accuracy is low, this paper carries out the work from the two aspects of feature extraction and feature classification of the ground object, and analyzes and studies the application and value of the polarimetric SAR system. The basic algorithm of polarization SAR image classification is proposed. A polarimetric SAR image feature classification method based on polarization target decomposition and support vector machine is proposed. Four kinds of scattering features and Freeman decomposition are obtained by Cloude decomposition. The simulation results show that the accuracy of using combined features is about 6.5% higher than that of single features. A polarization classification model based on polarization target decomposition and limit learning method is proposed. The simulation experiment shows ELM learning. The algorithm is indeed much faster than SVM learning. In this paper, a polarimetric SAR image classification method based on improved scattering mechanism coefficients is proposed, and the effectiveness of the polarimetric SAR image classification method based on improved scattering mechanism coefficients is verified. Experimental results show that after feature selection, the method of combining Freeman decomposition and Wishart classifier can get better classification results.</p>", "Keywords": "Polarimetric SAR; Target decomposition; Machine learning; Image feature classification; Extreme learning", "DOI": "10.1007/s00521-019-04624-9", "PubYear": 2020, "Volume": "32", "Issue": "10", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Spatial Data Mining and Big Data Integration for Ecology and Environment, Rongzhi College of Chongqing Technology and Business University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Shandong University of Science and Technology, Tai’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, Jouf University, Sakaka, Kingdom of Saudi Arabia"}], "References": []}, {"ArticleId": 78784647, "Title": "Evaluating <PERSON><PERSON><PERSON> forces within non-local pseudopotentials", "Abstract": "A new approach for evaluating <PERSON><PERSON> forces within a non-local potential is introduced. Particularly, the case of <PERSON><PERSON> theorem applied within density functional theory in combination with nonlocal ab-initio pseudopotentials, discretized by the finite-element method, is discussed in detail. The validity of the new approach is verified using test calculations on simple molecules and the convergence properties (w.r.t. the DFT loop) are analyzed. A comparison to other previously published approaches to Hellmann–<PERSON> forces calculations is shown to document that the new approach mitigates, for l -dependent as well as for separable forms of nonlocal pseudopotentials, the efficiency and/or accuracy problems arising in the methods published so far.", "Keywords": "<PERSON><PERSON> forces ; Separable pseudopotentials ; Density functional theory ; Convergence ; Real-space ; Finite element method", "DOI": "10.1016/j.cpc.2019.107034", "PubYear": 2020, "Volume": "250", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics, Czech Academy of Sciences, v.v.i, Czech Republic;New Technologies Research Centre, University of West Bohemia, Czech Republic;Faculty of Applied Sciences, University of West Bohemia, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics, Czech Academy of Sciences, v.v.i, Czech Republic"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "New Technologies Research Centre, University of West Bohemia, Czech Republic;Corresponding author"}], "References": []}, {"ArticleId": 78784743, "Title": "Ant colony optimization edge selection for support vector machine speed optimization", "Abstract": "<p> Support vector machine (SVM) is a widely used and reliable machine learning algorithm. It has been successfully applied to many real-world problems, with remarkable results. However, it has also been observed that SVM computational complexity increases with the increase in data size. Although many SVM speed optimization techniques have been proposed in the literature, there is still need for further improvement on the performance speed and accuracy of this algorithm. In this paper, a boundary detection algorithm for SVM speed optimization called ant colony optimization instance selection algorithm (ACOISA) is proposed. ACOISA is inspired by edge selection in ant colony optimization (ACO) algorithm, and it performs two primary functions: boundary detection and boundary instance selection. In the algorithm, ACO is used for boundary detection and k -nearest neighbor algorithm is used for boundary instance selection. Different sets of experiments are carried out to validate the efficiency of the proposed technique. All the experiments were performed on 35 datasets containing three well-known e-fraud types (credit card fraud, email spam and phishing email) and 31 other datasets available at UCI data repository. The experimental results showed that the proposed technique efficiently improved SVM training speed in 100% of the datasets used for evaluation, without significantly affecting SVM classification quality. Furthermore, the Freidman’s and <PERSON><PERSON>’s post hoc tests were conducted to statistically validate the credibility of the results. The statistical test results revealed that the proposed technique is significantly faster, compared to the standard SVM and some existing instance selection techniques, in all cases.</p>", "Keywords": "Machine learning; Support vector machine; Instance selection; Speed optimization; Ant colony optimization", "DOI": "10.1007/s00521-019-04633-8", "PubYear": 2020, "Volume": "32", "Issue": "15", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Informatics, University of the Free State, Bloemfontein, South Africa"}, {"AuthorId": 2, "Name": "A<PERSON>alo<PERSON>", "Affiliation": "School of Mathematics, Statistics, and Computer Science, University of KwaZulu-Natal, Pietermaritzburg, South Africa"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Statistics and Computer Science, University of KwaZulu-Natal, Durban, South Africa"}], "References": []}, {"ArticleId": 78784806, "Title": "RocaSec: a standalone GUI-based package for robust co-evolutionary analysis of proteins", "Abstract": "Abstract Summary <p>Patterns of mutational correlations, learnt from protein sequences, have been shown to be informative of co-evolutionary sectors that are tightly linked to functional and/or structural properties of proteins. Previously, we developed a statistical inference method, robust co-evolutionary analysis (RoCA), to reliably predict co-evolutionary sectors of proteins, while controlling for statistical errors caused by limited data. RoCA was demonstrated on multiple viral proteins, with the inferred sectors showing close correspondences with experimentally-known biochemical domains. To facilitate seamless use of RoCA and promote more widespread application to protein data, here we present a standalone cross-platform package ‘RocaSec’ which features an easy-to-use GUI. The package only requires the multiple sequence alignment of a protein for inferring the co-evolutionary sectors. In addition, when information on the protein biochemical domains is provided, RocaSec returns the corresponding statistical association between the inferred sectors and biochemical domains.</p> Availability and implementation <p>The RocaSec software is publicly available under the MIT License at https://github.com/ahmedaq/RocaSec.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz890", "PubYear": 2020, "Volume": "36", "Issue": "7", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, The Hong Kong University of Science and Technology, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Electronics, Communications and Information Technology, Queen’s University Belfast, NI Science Park, Queens Road, Belfast BT3 9DT, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, The Hong Kong University of Science and Technology, Hong Kong, China;Department of Chemical and Biological Engineering, The Hong Kong University of Science and Technology, Hong Kong, China"}], "References": []}, {"ArticleId": 78784893, "Title": "Cooperative particle swarm optimization with reference-point-based prediction strategy for dynamic multiobjective optimization", "Abstract": "Dynamic multiobjective optimization problems (DMOPs) have received increasing attention in the evolutionary community in recent years. The problem environment of a DMOP dynamically changes over time, causing the movement of the Pareto front (PF). It is critical but challenging to find the new PF in a new environment by reusing historical information of past environments since the successive environments are often relevant. Thus, we propose a new cooperative particle swarm optimization with a reference-point-based prediction strategy to solve DMOPs. In the proposed method, multiple swarms cooperate to approximate the whole PF with a new learning strategy in dynamic environments. Specially, when the environment is changed, the outdated particles are relocated based on the PF subparts they belong to using the novel reference-point-based prediction strategy. The proposed algorithm has been evaluated on the very recent scalable dynamic problem test suite with different numbers of objectives and different change severity. Experimental results show that the proposed algorithm is competitive to other typical state-of-the-art dynamic multiobjective algorithms and can find well-diversified and well-converged solution sets in dynamic environments.", "Keywords": "Particle swarm optimization (PSO) ; Dynamic multiobjective optimization problems (DMOPs) ; Information reuse ; Coevolutionary ; Prediction", "DOI": "10.1016/j.asoc.2019.105988", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-sen University, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-sen University, Guangzhou 510006, China;The Key Laboratory of Machine Intelligence and Advanced Computing, Sun Yat-sen University, Ministry of Education, China;Corresponding author at: School of Data and Computer Science, Sun Yat-sen University, Guangzhou 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-sen University, Guangzhou 510006, China"}], "References": []}, {"ArticleId": 78784920, "Title": "Multi-Gigabit/s OFDM real-time based transceiver engine for emerging 5G MIMO systems", "Abstract": "This paper presents a highly scalable multi-Gigabit/s Real-Time (RT) Wideband (WB) Orthogonal Frequency Division Multiplexing (OFDM) processing chain for 5G applications. It is aimed to significantly reduce the Hardware (HW) footprint of Multiple Input Multiple Output (MIMO) systems. In this context, implementation complexity results for MIMO configurations of 2 × 2, 4 × 4 and 8 × 8, enabled at sampling rates of 245.76, 122.88, 61.44 and 30.72 MHz, respectively, are compared with the ones from conventional MIMO architectures. For example, for a 8 × 8 MIMO–OFDM configuration implementation, savings of up to 87% are achieved when compared with conventional design methods, in terms of DSP48E1 slices. Moreover, in a Xilinx Virtex 7 XC7VX485T, it is shown that a second processing branch might be implemented, leading to a 5 Gbps (using 1024 Quadrature Amplitude Modulation (QAM)) Single-Input Single-Output (SISO) link or a 16 × 16 MIMO configuration. Finally, in order to validate the proposed architecture, the impact of its inclusion on a complete Field Programmable Gate Array (FPGA) 8 × 8 OFDM system, at LTE’s highest sampling rate of 30.72 MHz, is evaluated. Subsequently, it is demonstrated that this does not affect the performance of OFDM, even in the presence of Carrier Frequency Offset (CFO).", "Keywords": "MIMO ; FPGA design ; OFDM ; Real-time processing ; MultiGigabit/s", "DOI": "10.1016/j.phycom.2019.100957", "PubYear": 2020, "Volume": "38", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Telecomunicações, Leiria, and Polytechnic of Leiria, Leiria, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of South Wales, School of Engineering, Treforest, United Kingdom;Instituto de Telecomunicações, Leiria, and Polytechnic of Leiria, Leiria, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Telecomunicações, Leiria, and Polytechnic of Leiria, Leiria, Portugal;University of Aveiro, Aveiro, Portugal"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of South Wales, School of Engineering, Treforest, United Kingdom"}, {"AuthorId": 5, "Name": "Rafael F.S. Caldeirinha", "Affiliation": "University of South Wales, School of Engineering, Treforest, United Kingdom;Instituto de Telecomunicações, Leiria, and Polytechnic of Leiria, Leiria, Portugal;Corresponding author at: Instituto de Telecomunicações, Leiria, and Polytechnic of Leiria, Leiria, Portugal."}], "References": []}, {"ArticleId": 78784975, "Title": "An empirically evaluated checklist for surveys in software engineering", "Abstract": "Context: Over the past decade Software Engineering research has seen a steady increase in survey-based studies, and there are several guidelines providing support for those willing to carry out surveys. The need for auditing survey research has been raised in the literature. Checklists have been used both to conduct and to assess different types of empirical studies, such as experiments and case studies. Objective: To operationalize the assessment of survey studies by means of a checklist. To fulfill such goal, we aim to derive a checklist from standards for survey research and further evaluate the appropriateness of the checklist in the context of software engineering research. Method: We systematically aggregated knowledge from 12 methodological studies supporting survey-based research in software engineering. We identified the key stages of the survey process and its recommended practices through thematic analysis and vote counting. We evaluated the checklist by applying it to existing surveys and analyzed the results. Thereafter, we gathered the feedback of experts (the surveys’ authors) on our analysis and used the feedback to improve the survey checklist. Results: The evaluation provided insights regarding limitations of the checklist in relation to its understanding and objectivity. In particular, 19 of the 38 checklist items were improved according to the feedback received from experts. Conclusion: The proposed checklist is appropriate for auditing survey reports as well as a support tool to guide ongoing research with regard to the survey design process. A discussion on how to use the checklist and what its implications are for research practice is also provided.", "Keywords": "Checklist ; Assessment ; Survey ; Methodology", "DOI": "10.1016/j.infsof.2019.106240", "PubYear": 2020, "Volume": "119", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "BTH - Blekinge Tekniska Högskola, Valhallavägen 1, Karlskrona 37141 Sweden;Simula Metropolitan Center for Digital Engineering, Pilestredet 52, Oslo 0167 Norway;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "BTH - Blekinge Tekniska Högskola, Valhallavägen 1, Karlskrona 37141 Sweden;Hochschule Flensburg, Flensburg 24943 Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "BTH - Blekinge Tekniska Högskola, Valhallavägen 1, Karlskrona 37141 Sweden"}], "References": []}, {"ArticleId": 78785215, "Title": "Global Attractor of Reaction–Diffusion Gene Regulatory Networks with S-Type Delay", "Abstract": "<p>This paper studies the existence of the global attractor for the reaction–diffusion gene regulatory networks with s-type delay. Firstly, two Hanalay inequalities are proposed and proved, then the uniform boundedness theorem is proved by using these inequalities and semigroup theory and functional analysis technique, the operator splitting technique based on the superposition principle is also utilized to deal with the asymptotic compact of the semigroup. Then some sufficient conditions on existence of global attractor are established, which are easily verifiable and have a wider application range. At last, two examples are discussed to validate the effectiveness of our results. Moreover, the simulation is given by using Matlab.</p>", "Keywords": "Global attractor; Improved <PERSON>alay inequality; Gene regulatory networks; S-type delay; MRNA; Protein", "DOI": "10.1007/s11063-019-10164-z", "PubYear": 2020, "Volume": "51", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mathematics and System Science, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and System Science, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Applied Physics and Computational Mathematics, Beijing, China"}], "References": []}, {"ArticleId": 78785229, "Title": "Clustering-based method for large group decision making with hesitant fuzzy linguistic information: Integrating correlation and consensus", "Abstract": "Aiming at the large-scale experts and the lower consensus in large group decision making, a novel clustering-based method integrating correlation and consensus of hesitant fuzzy linguistic information is proposed. Firstly, develop a new hesitant degree function for hesitant fuzzy linguistic element considering its scale. Secondly, put forward the correlation measure and consensus measure models combining the hesitant degree. And then present a clustering method integrating the correlation and consensus to divide the large-scale experts into several clusters. The clustering method simultaneously ensures the cohesion of clusters and the gradual increasing of the collective consensus level. After clustering, activate the selection process to update the weights of clusters combining the number of experts in clusters and the consensus level of clusters and use the score function considering the hesitant degree to rank the alternatives. Finally, a case and some comparisons are studied and analyzed to verify the rationality and effectiveness of the method.", "Keywords": "Large group decision making (LGDM) ; Clustering ; Correlation ; Consensus ; Hesitant fuzzy linguistic term sets (HFLTSs)", "DOI": "10.1016/j.asoc.2019.105973", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Central South University, Changsha, Hunan, 410083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Central South University, Changsha, Hunan, 410083, China;Corresponding author"}], "References": []}, {"ArticleId": 78785251, "Title": "Predicting ICD-9 code groups with fuzzy similarity based supervised multi-label classification of unstructured clinical nursing notes", "Abstract": "In hospitals, caregivers are trained to chronicle the subtle changes in the clinical conditions of a patient at regular intervals, for enabling decision-making. Caregivers’ text-based clinical notes are a significant source of rich patient-specific data, that can facilitate effective clinical decision support, despite which, this treasure-trove of data remains largely unexplored for supporting the prediction of clinical outcomes. The application of sophisticated data modeling and prediction algorithms with greater computational capacity have made disease prediction from raw clinical notes a relevant problem. In this paper, we propose an approach based on vector space and topic modeling, to structure the raw clinical data by capturing the semantic information in the nursing notes. Fuzzy similarity based data cleansing approach was used to merge anomalous and redundant patient data. Furthermore, we utilize eight supervised multi-label classification models to facilitate disease (ICD-9 code group) prediction. We present an exhaustive comparative study to evaluate the performance of the proposed approaches using standard evaluation metrics. Experimental validation on MIMIC-III, an open database, underscored the superior performance of the proposed T erm weighting of unstructured notes AG gregated using fuzzy S imilarity ( TAGS ) model, which consistently outperformed the state-of-the-art structured data based approach by 7.79% in AUPRC and 1.24% in AUROC.", "Keywords": "Clinical decision support systems ; Disease prediction ; Healthcare analytics ; ICD-9 code group prediction ; Machine learning ; Natural language processing", "DOI": "10.1016/j.knosys.2019.105321", "PubYear": 2020, "Volume": "190", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amazon.com, Inc., Bangalore, Karnataka, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Healthcare Analytics and Language Engineering (HALE) Lab, Department of Information Technology, National Institute of Technology Karnataka, Surathkal, Mangaluru, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Healthcare Analytics and Language Engineering (HALE) Lab, Department of Information Technology, National Institute of Technology Karnataka, Surathkal, Mangaluru, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> S.", "Affiliation": "Healthcare Analytics and Language Engineering (HALE) Lab, Department of Information Technology, National Institute of Technology Karnataka, Surathkal, Mangaluru, India;http://infotech.nitk.ac.in/faculty/sowmya-kamath-s"}], "References": []}, {"ArticleId": 78785267, "Title": "Augmented reality in support of intelligent manufacturing – A systematic literature review", "Abstract": "Industry increasingly moves towards digitally enabled ‘smart factories’ that utilise the internet of things (IoT) to realise intelligent manufacturing concepts like predictive maintenance or extensive machine to machine communication. A core technology to facilitate human integration in such a system is augmented reality (AR), which provides people with an interface to interact with the digital world of a smart factory. While AR is not ready yet for industrial deployment in some areas, it is already used in others. To provide an overview of research activities concerning AR in certain shop floor operations, a total of 96 relevant papers from 2011 to 2018 are reviewed. This paper presents the state of the art, the current challenges, and future directions of manufacturing related AR research through a systematic literature review and a citation network analysis. The results of this review indicate that the context of research concerning AR gets increasingly broader, especially by addressing challenges when implementing AR solutions.", "Keywords": "Augmented Reality, AR ; Intelligent manufacturing ; Systematic literature review ; Challenges ; Technology ; Visualisation ; Cyber-physical systems ; Industry 4.0 ; Internet of Things, IoT ; Smart factory ; Industrial digitalisation ; AR augmented reality ; AV augmented virtuality ; CPPS cyber-physical production system ; DOI digital object identifier ; HHD hand-held device ; HMD head-mounted device ; IoT internet of things ; MR mixed reality ; RV reality-virtuality ; SCADA supervisory control and data acquisition ; TAM technology acceptance model ; TLX task load index ; VR virtual reality", "DOI": "10.1016/j.cie.2019.106195", "PubYear": 2020, "Volume": "140", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Manufacturing, University of Cambridge, 17 Charles Babbage Road, Cambridge CB3 0FS, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Manufacturing, University of Cambridge, 17 Charles Babbage Road, Cambridge CB3 0FS, UK;Corresponding author"}], "References": [{"Title": "Exploring the potential of Operator 4.0 interface and monitoring", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "105600", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Adopting augmented reality in the age of industrial digitalisation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "115", "Issue": "", "Page": "103112", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 78785272, "Title": "Nonlinear dynamic analysis of piezoelectric-bonded FG-CNTR composite structures using an improved FSDT theory", "Abstract": "<p>In the present work, a geometrically nonlinear finite shell element is first presented to predict nonlinear dynamic behavior of piezolaminated functionally graded carbon nanotube-reinforced composite (FG-CNTRC) shell, to enrich the existing research results on FG-CNTRC structures. The governing equations are developed via an improved first-order shear deformation theory (FSDT), in which a parabolic distribution of the transverse shear strains across the shell thickness is assumed and a zero condition of the transverse shear stresses on the top and bottom surfaces is imposed. Using a micro-mechanical model on the foundation of the developed rule of mixture, the effective material properties of the FG-CNTRC structures, which are strengthened by single-walled carbon nanotubes (SWCNTs), are scrutinized. The effectiveness of the present method is demonstrated by validating the obtained results against those of other studies from literature considering shell structures. Furthermore, some novel numerical results, including the nonlinear transient deflection of smart FG-CNTRC spherical and cylindrical shells, will be presented and can be considered for future structure design.</p>", "Keywords": "Nonlinear dynamics; Functionally graded carbon nanotubes; Improved FSDT; Smart shell; Piezoelectric materials", "DOI": "10.1007/s00366-019-00891-1", "PubYear": 2021, "Volume": "37", "Issue": "2", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Electromechanical Systems (LASEM), National Engineering School of Sfax, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Electromechanical Systems (LASEM), National Engineering School of Sfax, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, College of Engineering, King Khalid University, Abha, Saudi Arabia;Laboratory of Electromechanical Systems (LASEM), National Engineering School of Sfax, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Electromechanical Systems (LASEM), National Engineering School of Sfax, University of Sfax, Sfax, Tunisia"}], "References": []}, {"ArticleId": 78785280, "Title": "A context-aware robust intrusion detection system: a reinforcement learning-based approach", "Abstract": "<p>Detection and prevention of intrusions in enterprise networks and systems is an important, but challenging problem due to extensive growth and usage of networks that are constantly facing novel attacks. An intrusion detection system (IDS) monitors the network traffic and system-level applications to detect malicious activities in the network. However, most of the existing IDSs are incapable of providing higher accuracy and less false positive rate (FPR). Therefore, there is a need for adaptive techniques to detect network intrusions that maintain a balance between accuracy and FPR. In this paper, we present a context-adaptive IDS that uses multiple independent deep reinforcement learning agents distributed across the network for accurate detection and classification of new and complex attacks. We have done extensive experimentation using three benchmark datasets including NSL-KDD, UNSW-NB15 and AWID on our model that shows better accuracy and less FPR compared to the state-of-the-art systems. Further, we analysed the robustness of our model against adversarial attack and observed only a small decrease in accuracy as compared to the existing models. To further improve the robustness of the system, we implemented the concept of denoising autoencoder. Also, we have shown the usability of our system in real-life application with changes in the attack pattern.</p>", "Keywords": "Adversarial attack; Context; Denoising autoencoder; FPR; IDS; Deep reinforcement learning (DRL) agent; NSL-KDD; AWID; UNSW-NB15", "DOI": "10.1007/s10207-019-00482-7", "PubYear": 2020, "Volume": "19", "Issue": "6", "JournalId": 25892, "JournalTitle": "International Journal of Information Security", "ISSN": "1615-5262", "EISSN": "1615-5270", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Bhubaneswar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Bhubaneswar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Bhubaneswar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Bhubaneswar, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Bhubaneswar, India"}], "References": []}, {"ArticleId": 78785304, "Title": "An augmented reality-based training system with a natural user interface for manual milling operations", "Abstract": "<p>This study developed an augmented reality (AR)-based training system for conventional manual milling operations. An Intel RealSense R200 depth camera and a Leap Motion controller were mounted on an HTC Vive head-mounted display to allow users freely walk around in a room-size AR environment to operate a full-size virtual milling machine with their barehands, using their natural operation behaviors, as if they were operating a real milling machine in the real world, without additional worn or handheld devices. GPU parallel computing was used to handle dynamic occlusions and accelerate the machining simulation to achieve a real-time simulation. Using the developed AR-based training system, novices can receive a hands-on training in a safe environment, without any injury or damage. User test results showed that using the developed AR-based training resulted in lower failure rates and inquiry times than using video training. Users also commented that the AR-based training was interesting and helpful for novices to learn the basic manual milling operation techniques.</p>", "Keywords": "Augmented reality; Natural operation behavior; Manual milling operation; Occlusion", "DOI": "10.1007/s10055-019-00415-8", "PubYear": 2020, "Volume": "24", "Issue": "3", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University, Taipei, Taiwan"}], "References": []}, {"ArticleId": 78785323, "Title": "The Unmanned Aerial Vehicle Benchmark: Object Detection, Tracking and Baseline", "Abstract": "<p>With the increasing popularity of Unmanned Aerial Vehicles (UAVs) in computer vision-related applications, intelligent UAV video analysis has recently attracted the attention of an increasing number of researchers. To facilitate research in the UAV field, this paper presents a UAV dataset with 100 videos featuring approximately 2700 vehicles recorded under unconstrained conditions and 840k manually annotated bounding boxes. These UAV videos were recorded in complex real-world scenarios and pose significant new challenges, such as complex scenes, high density, small objects, and large camera motion, to the existing object detection and tracking methods. These challenges have encouraged us to define a benchmark for three fundamental computer vision tasks, namely, object detection, single object tracking (SOT) and multiple object tracking (MOT), on our UAV dataset. Specifically, our UAV benchmark facilitates evaluation and detailed analysis of state-of-the-art detection and tracking methods on the proposed UAV dataset. Furthermore, we propose a novel approach based on the so-called Context-aware Multi-task Siamese Network (CMSN) model that explores new cues in UAV videos by judging the consistency degree between objects and contexts and that can be used for SOT and MOT. The experimental results demonstrate that our model could make tracking results more robust in both SOT and MOT, showing that the current tracking and detection methods have limitations in dealing with the proposed UAV benchmark and that further research is indeed needed.</p>", "Keywords": "UAV; Object detection; Single object tracking; Multiple object tracking", "DOI": "10.1007/s11263-019-01266-1", "PubYear": 2020, "Volume": "128", "Issue": "5", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "Hongyang Yu", "Affiliation": "Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Weihai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Harbin, China;University of Chinese Academy of Sciences, Beijing, China;Key Laboratory of Big Data Mining and Knowledge Management CAS, Beijing, China;Key Laboratory of Intelligent Information Processing (IIP), Institute of Computing Technology CAS, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Texas at San Antonio, San Antonio, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "University of Trento, Trento, Italy"}], "References": []}, {"ArticleId": 78785345, "Title": "A “Maximal Exclusion” Approach to Structural Underspecification in Dynamic Syntax", "Abstract": "<p>‘Case’ and ‘grammatical relations’ are central to syntactic theory, but rigorous treatments of these concepts in surface-oriented grammars such as Dynamic Syntax are pending. In this respect, Japanese is worthy of mention; in this language, the nominative case particle ga , which typically marks a subject, may mark an object in certain syntactic contexts, and more than one instance of ga may be present within a single clause. These patterns cannot be captured if we simply assume that ga marks a subject. In the present article, we aim to advance formal aspects of the framework, especially the mechanism of ‘structural underspecification,’ by proposing that the parse of a case particle maximally excludes potential landing sites of an unfixed node at the time of parsing the case particle, delaying the resolution of the unfixed node until a subsequent stage of structure building. This maximal exclusion approach to structural underspecification accounts for a range of case marking patterns and their connections with grammatical relations.</p>", "Keywords": "Case; Grammatical relation; Parsing; Incrementality; Japanese", "DOI": "10.1007/s10849-019-09308-0", "PubYear": 2021, "Volume": "30", "Issue": "2", "JournalId": 5283, "JournalTitle": "Journal of Logic, Language and Information", "ISSN": "0925-8531", "EISSN": "1572-9583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hankuk University of Foreign Studies, Yongin, Korea"}], "References": []}, {"ArticleId": ********, "Title": "Experimental investigation on mechanical drilling of a newly developed CFRP/Al co-cured material", "Abstract": "<p>The use of material combination composed of carbon fiber reinforced polymer (CFRP) and metals is getting increased attention in many industries. The newly developed CFRP/metal co-cured material is one of the most popular compound structures and is mainly applied to the attitude control flywheel (ACL) in the military light agile satellite. Drilling is still a challenge for manufacturers in the assembly chain of ACL because of the anisotropy of CFRP and difficult-to-cut metals. In this work, the drilling machinability of a newly developed co-cured material consisting of M30/AG80 CFRP and 7050Al is evaluated by a standard twist drill. The significance of this paper aims to reveal the key cutting phenomena including thrust forces, drilling temperatures, and hole quality during drilling the assigned co-cured material. The mechanism controlling the parametric effects on the interfacial cutting response was also revealed. The results highlight the impact of tool–work contact ratio on the evolution of thrust forces and drilling temperatures. Besides, grooves and matrix degradation are two typical defects on the hole surface of CFRP layers while chip adhesion is the case for Al layer. The mechanical action plays a predominant role in determining the cutting response of the upper interface whereas the thermal effect does greatly affect the condition of the lower interface.</p>", "Keywords": "CFRP/Al co-cured material; Thrust forces; Temperatures; Hole quality; Drilling; Tool–work contact ratio", "DOI": "10.1007/s00170-019-04659-1", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "Xiaojiang Cai", "Affiliation": "Shanghai Aerospace Control Technology Institute, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Ming", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}], "References": []}, {"ArticleId": 78785356, "Title": "Molybdenum disulfides nanoflowers anchoring iron-based metal organic framework: A synergetic catalyst with superior peroxidase-mimicking activity for biosensing", "Abstract": "A novel nanostructure MoS<sub>2</sub> nanoflowers anchoring iron(III)-based metal-organic framework MIL-101(Fe) [Fe<sub>3</sub>O(H<sub>2</sub>O)<sub>2</sub>Cl(O<sub>2</sub>C–C<sub>6</sub>H<sub>4</sub>−CO<sub>2</sub>)<sub>3</sub>], named MoS<sub>2</sub>-MIL-101(Fe), was prepared by growing MIL-101(Fe) crystals with as-prepared MoS<sub>2</sub>. Then, MoS<sub>2</sub>-MIL-101(Fe) was characterized through SEM, TEM, BET, XRD, DLS, and XPS. MoS<sub>2</sub>-MIL-101(Fe) possesses synergetic peroxidase-mimicking activity over individual MIL-101(Fe) and MoS<sub>2</sub>, which can catalyze the oxidation of TMB by H<sub>2</sub>O<sub>2</sub> with a much stronger Vis-absorption. MIL-101(Fe) has a high porosity and large specific surface area for MoS<sub>2</sub> to absorb H<sub>2</sub>O<sub>2</sub> and TMB. Hydrophilic MIL-101(Fe) could prevent the aggregation of hydrophobic MoS<sub>2</sub> in aqueous solution, which is beneficial for catalysis. In addition, anchored MoS<sub>2</sub> brings mesopore and abundant exposed active units to the MIL-101(Fe) surface. The synergetic effects result in a highly enhanced catalytic performance. The Michaelis constant (0.008 mmol/L) of MoS<sub>2</sub>-MIL-101(Fe) for H<sub>2</sub>O<sub>2</sub> is 462 times lower than that of HRP, showing its strong affinity with H<sub>2</sub>O<sub>2</sub>. On the basis, a sensitive method for H<sub>2</sub>O<sub>2</sub> detection was proposed with a linear range of 0.01−20 μmol/L and a detection limit of 10 nmol/L. Considering H<sub>2</sub>O<sub>2</sub> as product in the reaction of glucose catalyzed by glucose oxidase, a sensitive and selective method for glucose detection was proposed. The method can be used in blood glucose detection with good accuracy.", "Keywords": "Molybdenum disulfides nanoflower ; Metal organic framework ; Peroxidase mimetic ; Glucose detection", "DOI": "10.1016/j.snb.2019.127530", "PubYear": 2020, "Volume": "305", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Key Laboratory of Chongqing Inorganic Special Functional Materials, College of Chemistry and Chemical Engineering, Yangtze Normal University, Chongqing, 408100, China;The Key Laboratory of Luminescence and Real-Time Analytical Chemistry (Ministry of Education), College of Pharmaceutical Sciences, Southwest University, Chongqing, 400716, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The Key Laboratory of Chongqing Inorganic Special Functional Materials, College of Chemistry and Chemical Engineering, Yangtze Normal University, Chongqing, 408100, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Key Laboratory of Luminescence and Real-Time Analytical Chemistry, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Key Laboratory of Luminescence and Real-Time Analytical Chemistry, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, China"}, {"AuthorId": 5, "Name": "Wenbing Shi", "Affiliation": "The Key Laboratory of Chongqing Inorganic Special Functional Materials, College of Chemistry and Chemical Engineering, Yangtze Normal University, Chongqing, 408100, China;Corresponding authors."}, {"AuthorId": 6, "Name": "Zhifeng Fu", "Affiliation": "The Key Laboratory of Luminescence and Real-Time Analytical Chemistry (Ministry of Education), College of Pharmaceutical Sciences, Southwest University, Chongqing, 400716, China;Corresponding authors."}], "References": []}, {"ArticleId": 78785365, "Title": "Weight-and-Universum-based semi-supervised multi-view learning machine", "Abstract": "<p>Semi-supervised multi-view learning machine is developed to process the corresponding semi-supervised multi-view data sets which consist of labeled and unlabeled instances. But in real-world applications, for a multi-view data set, only few instances are labeled with the limitation of manpower and cost. As a result, few prior knowledge which is necessary for the designing of a learning machine is provided. Moreover, in practice, different views and features play diverse discriminant roles while traditional learning machines treat these roles equally and assign the same weight just for convenience. In order to solve these problems, we introduce Universum learning to obtain more prior knowledge and assign different weights for views and features to reflect their diverse discriminant roles. The proposed learning machine is named as weight-and-Universum-based semi-supervised multi-view learning machine (WUSM). In WUSM, we first obtain weights of views and features. Then, we construct Universum set to obtain more prior knowledge on the basis of these weights. Different from traditional construction ways, the used construction way makes full use of the information of all labeled and unlabeled instances rather than only a pair of positive and negative training instances. Finally, we design the machine with the usage of the Universum set along with original data set. Our contributions are given as follows. (1) With the usage of all (labeled, unlabeled) instances of the data set, the Universum set provides more useful prior knowledge. (2) WUSM considers the diversities of views and features. (3) WUSM advances the development of semi-supervised multi-view learning machines. Experiments on bipartite ranking, feature selection, dimensionality reduction, classification, clustering, etc. validate the advantages of WUSM and draw a conclusion that with the introduction of Universum learning, view weights, and feature weights, the performance of a semi-supervised multi-view learning machine is boosted.</p>", "Keywords": "Semi-supervised learning; Multi-view learning; View weights; Feature weights; Universum learning", "DOI": "10.1007/s00500-019-04572-5", "PubYear": 2020, "Volume": "24", "Issue": "14", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Shanghai Maritime University, Shanghai, People’s Republic of China;Department of Computer Science and Technology, Tongji University, Shanghai, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Tongji University, Shanghai, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Shanghai Maritime University, Shanghai, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Shanghai Maritime University, Shanghai, People’s Republic of China"}], "References": []}, {"ArticleId": 78785637, "Title": "Mobile phone use impairs stair gait: A pilot study on young adults", "Abstract": "Human movement control requires attention to accurately tune motor commands in response to environmental changes. Dual task paradigms are used to test the role of attention on motor performance. Usually the tasks used have little resemblance with every day experience. Here we ask: Does a common cognitive task, such as a mobile phone conversation, compromise motor performance on stairs? Eight young participants negotiated an instrumented seven-step staircase. Stair negotiation while talking on a mobile phone was compared to normal stair negotiation. Stepping parameters, jerk cost (measure of smoothness of locomotion) and step clearance were measured. When talking on a mobile phone, participants’ overall body velocity (mean(sd): Ascent 0.534(0.026) vs 0.511(0.024) m/s, Descent 0.642(0.026) vs 0.511(0.024) m/s, No phone/Phone respectively) and cadence decreased significantly (Ascent 75.8(5.8) vs 65.6(4.4) steps/min, Descent 117.4(4.2) vs 108.6(6.0) steps/min, No Phone/Phone respectively). Pelvis and feet jerk cost also changed significantly, mostly decreasing with phone use. Foot clearance did not show significant changes between No Phone and Phone conditions. These pilot results show that, even for young, healthy and cognitively intact individuals, talking on a mobile phone whilst negotiating a staircase induces measurable changes in motor performance. Participants moved slowly but more smoothly, reducing the motor control cost, possibly at the expense of movement accuracy. The reduction in motor performance is likely to be due to the difficulty in integrating the two sub-tasks. These results suggest that even young, healthy individuals show stair gait impairment when simultaneously negotiating stairs and performing another cognitive task, such as talking on the phone.", "Keywords": "Control cost;Dual task;Information processing interference;Locomotor performance", "DOI": "10.1016/j.apergo.2019.103009", "PubYear": 2020, "Volume": "84", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Human & Applied Physiological Sciences, King's College London, London, UK;Corresponding author."}, {"AuthorId": 2, "Name": "Bradford J. <PERSON>en", "Affiliation": "Centre for Interdisciplinary Research in Rehabilitation and Social Interaction/Université Laval, Quebec City, Canada, G1M 2S8"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Interdisciplinary Research in Rehabilitation and Social Interaction/Université Laval, Quebec City, Canada, G1M 2S8;Memory and Cognition Laboratory, INSERM UMR 894, Centre of Psychiatry and Neuroscience and Institute of Psychology, Université Paris Descartes, Sorbonne Paris Cité, Paris, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Research Centre for Musculoskeletal Science & Sports Medicine, Manchester Metropolitan University, Manchester, M1 5GD, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Research Institute for Sport and Exercise Sciences, Liverpool John Moores University, Liverpool, UK"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Research Institute for Sport and Exercise Sciences, Liverpool John Moores University, Liverpool, UK"}], "References": []}, {"ArticleId": 78786035, "Title": "Pythagorean fuzzy linguistic Muirhead mean operators and their applications to multiattribute decision‐making", "Abstract": "<p>Pythagorean fuzzy sets, as an extension of intuitionistic fuzzy sets to deal with uncertainty, have attracted much attention since their introduction, in both theory and application aspects. In this paper, we investigate multiple attribute decision‐making (MADM) problems with Pythagorean linguistic information based on some new aggregation operators. To begin with, we present some new Pythagorean fuzzy linguistic Muirhead mean (PFLMM) operators to deal with MADM problems with Pythagorean fuzzy linguistic information, including the PFLMM operator, the Pythagorean fuzzy linguistic‐weighted Muirhead mean operator, the Pythagorean fuzzy linguistic dual Muirhead mean operator and the Pythagorean fuzzy linguistic dual‐weighted Muirhead mean operator. The main advantages of these aggregation operators are that they can capture the interrelationships of multiple attributes among any number of attributes by a parameter vector P and make the information aggregation process more flexible by the parameter vector P . In addition, some of the properties of these new aggregation operators are proved and some special cases are discussed where the parameter vector takes some different values. Moreover, we present two new methods to solve MADM problems with Pythagorean fuzzy linguistic information. Finally, an illustrative example is provided to show the feasibility and validity of the new methods, to investigate the influences of parameter vector P on decision‐making results, and also to analyze the advantages of the proposed methods by comparing them with the other existing methods.</p>", "Keywords": "Muirhead mean operators;multiattribute decision making;Pythagorean fuzzy set", "DOI": "10.1002/int.22212", "PubYear": 2020, "Volume": "35", "Issue": "2", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Data Recovery Key Laboratory of Sichuan Province, Neijiang Normal University, Neijiang, Sichuan, China; School of Mathematics and Information Sciences, Neijiang Normal University, Neijiang, Sichuan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing and Mathematics, Ulster University, Northern Ireland, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Information Sciences, Neijiang Normal University, Neijiang, Sichuan, China"}], "References": []}]