[{"ArticleId": 105714762, "Title": "VIBRATION UNIT CONTROL OF PASSING THROUGH RESONANCE WITH CONSIDERATION OF THE DYNAMICS OF ELECTRIC DRIVES AND ELASTIC CONNECTIONS", "Abstract": "", "Keywords": "", "DOI": "10.22250/18142400_2022_74_4_117", "PubYear": 2022, "Volume": "", "Issue": "4", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105714853, "Title": "Context-sensitive graph representation learning", "Abstract": "<p>Graph representation learning, which maps high-dimensional graphs or sparse graphs into a low-dimensional vector space, has shown its superiority in numerous learning tasks. Recently, researchers have identified some advantages of context-sensitive graph representation learning methods in functions such as link predictions and ranking recommendations. However, most existing methods depend on convolutional neural networks or recursive neural networks to obtain additional information outside a node, or require community algorithms to extract multiple contexts of a node, or focus only on the local neighboring nodes without their structural information. In this paper, we propose a novel context-sensitive representation method, C ontext- S ensitive G raph R epresentation L earning (CSGRL), which simultaneously combines attention networks and a variant of graph auto-encoder to learn weighty information about various aspects of participating neighboring nodes. The core of CSGRL is to utilize an asymmetric graph encoder to aggregate information about neighboring nodes and local structures to optimize the learning goal. The main benefit of CSGRL is that it does not need additional features and multiple contexts for the node. The message of neighboring nodes and their structures spread through the encoder. Experiments are conducted on three real datasets for both tasks of link prediction and node clustering, and the results demonstrate that CSGRL can significantly improve the effectiveness of all challenging learning tasks compared with 14 state-of-the-art baselines.</p>", "Keywords": "Context-sensitive; Graph Representation learning; Graph auto-encoder", "DOI": "10.1007/s13042-022-01755-9", "PubYear": 2023, "Volume": "14", "Issue": "6", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "Jisheng Qin", "Affiliation": "Institute of Intelligence Science and Technology, Hohai University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Intelligence Science and Technology, Hohai University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Ulster University, Belfast, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Intelligence Science and Technology, Hohai University, Nanjing, China"}], "References": [{"Title": "E-GCN: graph convolution with estimated labels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "7", "Page": "5007", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 105714990, "Title": "Iridescent polymeric film with tunable color responses to ultra-trace Staphylococcus aureus enterotoxin B", "Abstract": "Colorimetric sensing materials have a rising demand in detection applications, especially in the field of naked-eye detection. Herein, a novel iridescent polymeric film that can respond to different Staphylococcus aureus enterotoxin B (SEB) concentrations in the visible range is reported. This film is fabricated by polymerization of methacrylic acid (MAA) and ethylene dimethacrylate (EGDMA) owing an inverse opal array (IOA) structure, and gold nanoparticles (AuNPs) are enchased on the polymer skeleton. This composite structure facilitates the film to respond to SEB at a LOD of 4.29 fg mL<sup>−1</sup>, making it the best performing colorimetric material to date. Cryo-Scanning Electron Microscopy and 3D AFM were used to observe the morphology change of the iridescent polymeric film after SEB binding. This film possesses great advantages for ultrasensitive visual detection of real samples. In addition, the iridescent polymeric film has the potential to become a universal sensing material, realizing the ultrasensitive visual detection of hazardous substances without equipment in the field by replacing the recognition element.", "Keywords": "Naked-eye detection ; Iridescent polymeric film ; Ultrasensitive ; Gold nanoparticle ; Staphylococcus aureus enterotoxin B", "DOI": "10.1016/j.snb.2023.133318", "PubYear": 2023, "Volume": "380", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "College of Food Science and Engineering, Shandong Agricultural University, Tai’an 271018, PR China;Tianjin Key Laboratory of Risk Assessment and Control Technology for Environment and Food Safety, Tianjin Institute of Environmental and Operational Medicine, Tianjin 300050, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Risk Assessment and Control Technology for Environment and Food Safety, Tianjin Institute of Environmental and Operational Medicine, Tianjin 300050, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Tianjin Key Laboratory of Risk Assessment and Control Technology for Environment and Food Safety, Tianjin Institute of Environmental and Operational Medicine, Tianjin 300050, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Shen", "Affiliation": "College of Food Science and Engineering, Shandong Agricultural University, Tai’an 271018, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Food Science and Engineering, Shandong Agricultural University, Tai’an 271018, PR China"}, {"AuthorId": 6, "Name": "Yujing <PERSON>n", "Affiliation": "College of Food Science and Engineering, Shandong Agricultural University, Tai’an 271018, PR China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Risk Assessment and Control Technology for Environment and Food Safety, Tianjin Institute of Environmental and Operational Medicine, Tianjin 300050, PR China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Tianjin Key Laboratory of Risk Assessment and Control Technology for Environment and Food Safety, Tianjin Institute of Environmental and Operational Medicine, Tianjin 300050, PR China;Corresponding authors"}], "References": [{"Title": "LSPR-enhanced photonic crystal allows ultrasensitive and label-free detection of hazardous chemicals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "310", "Issue": "", "Page": "127671", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105715102, "Title": "An efficient Apriori algorithm for frequent pattern in human intoxication data", "Abstract": "<p>Addicts and non-addicts can be distinguished by analyzing social behaviors and activities. An attempt was made in the study to discover the main rules that cause people to get hooked. We utilized an open-source dataset with 474 total instances and 212 total addicted individuals. They asked 50 questions during the data collection process. All of the questions were created using the Index of Addiction Severity and with the assistance of drug addiction psychologists. In this study, we utilized the Apriori algorithm to extract the most important rules from the dataset. By following this guideline, it will be clear whether or not someone is hooked based on their social conduct. The Apriori algorithm was used to find rules from the dataset, and eight significant rules were discovered, with a confidence level of 95% and a support level of 45%.</p>", "Keywords": "Machine learning; Drug; Addiction; Apriori; Association rule mining", "DOI": "10.1007/s11334-022-00523-w", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 18069, "JournalTitle": "Innovations in Systems and Software Engineering", "ISSN": "1614-5046", "EISSN": "1614-5054", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, North Western University, Khulna, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, North Western University, Khulna, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Northern University of Business and Technology, Khulna, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Bangladesh University of Business and Technology, Dhaka, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, North Western University, Khulna, Bangladesh"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chitkara University Institute of Engineering and Technology, Chitkara University, Rajpura, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jain (Deemed-to-be University), Bengaluru, India"}], "References": [{"Title": "A survey of evolutionary computation for association rule mining", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "524", "Issue": "", "Page": "318", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105715225, "Title": "A novel DCNN-ELM hybrid framework for face mask detection", "Abstract": "The Coronavirus disease (2019) has caused massive destruction of human lives and capital around the world. The latest variant Omicron is proved to be the most infectious of all its previous counterparts – Alpha, Beta and Delta. Various measures are identified, tested and implemented to minimize the attack on humans. Face masks are one of those measures that are shown to be very effective in containing the infection. However, it requires continuous monitoring for law enforcement. In the present manuscript, a detailed research investigation using different ablation studies is carried out to develop the framework for face mask recognition using pre-trained deep convolution neural networks (DCNN) models used in conjunction with a fast single layer feed-forward neural network (SLFNN) commonly known as Extreme Learning Machine (ELM) as classification technique. The ELM is well known for its real time data processing capabilities and has been successfully applied both for regression and classification problems of image processing and biomedical domain. It is for the first time that in this paper we have proposed the use of ELM as classifier for face mask detection. As a precursor to this, for feature selection, six pre-trained DCNNs such as <PERSON>ception, Vgg16, Vgg19, ResNet50, ResNet 101 and ResNet152 are tested for this purpose. The best testing accuracy is obtained in case of ResNet152 transfer learning model used with ELM as the classifier. The performance evaluation through different ablation studies on testing accuracy explicitly proves that ResNet152 - ELM hybrid architecture is not only the best among the selected transfer learning models but also proves so when it is compared with several other classifiers used for the face mask detection operation. Through this investigation, novelty of the use of ResNet152 + ELM for face mask detection framework in real time domain is established.", "Keywords": "COVID-19 ; Face mask detector ; Masked face ; Transfer learning ; Convolution neural network", "DOI": "10.1016/j.iswa.2022.200175", "PubYear": 2023, "Volume": "17", "Issue": "", "JournalId": 89889, "JournalTitle": "Intelligent Systems with Applications", "ISSN": "2667-3053", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON> Engineering College, Ghaziabad, Uttar Pradesh 201009, India"}, {"AuthorId": 2, "Name": "Pran<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON> Engineering College, Ghaziabad, Uttar Pradesh 201009, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, Deendayal Upadhyay College, University of Delhi, Delhi 110078, India;Corresponding author"}], "References": [{"Title": "Deep learning approaches for COVID-19 detection based on chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114054", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105715287, "Title": "Improving the accuracy and the estimation time of inter-area modes in power system based on <PERSON><PERSON><PERSON><PERSON> algorithm", "Abstract": "In this paper, a new method based on the <PERSON><PERSON>–<PERSON> (TK) algorithm is used to process nonstationary signals and extract system modes. This new algorithm for mode estimation is proposed to improve estimation accuracy under dynamic conditions. The key contribution in TK is the use of multiple orthogonal sliding windows rather than a single pair of sliding windows. Simulation results under various scenarios, such as different types of faults and load changes are used to evaluate the performance of the proposed method. The proposed method will accurately extract system modes. This method will also reduce the required memory and the calculation time of estimation in nonstationary signals. In complement to successful applications in studying the dynamic behavior of the power system, identifying and analyzing low-frequency electromechanical oscillations, and removing signal noise, this method can accurately estimate the modes of a power system. To validate the accuracy of the proposed method, the Wavelet and the Prony methods have been used for comparison. The proposed method is implemented using simulated ringdown data of standard two-area power system and real measurement data of the WSCC system breakup on 10 August 1996.", "Keywords": "Tufts–<PERSON><PERSON> (TK) ; estimation mode ; nonstationary ; wavelet", "DOI": "10.1080/02286203.2022.2161443", "PubYear": 2024, "Volume": "44", "Issue": "3", "JournalId": 4075, "JournalTitle": "International Journal of Modelling and Simulation", "ISSN": "0228-6203", "EISSN": "1925-7082", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Zanjan, Zanjan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Zanjan, Zanjan, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Zanjan, Zanjan, Iran"}], "References": [{"Title": "Unified Smith predictor‐based loop‐shaping H <sub>∞</sub> damping controller for mitigating inter‐area oscillations in power system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "4", "Page": "366", "JournalTitle": "IET Cyber-Physical Systems: Theory & Applications"}]}, {"ArticleId": 105715425, "Title": "Artificial Intelligence Approach for Diabetic Retinopathy Severity Detection", "Abstract": "Identifying the diabetic retinopathy (DR) severity in the retina images taken under a variety of imaging conditions is more challenging. There are five classes that are commonly classified on retinal images based on severity of DR disease such as No DR, mild NPDR (non proliferative diabetic retinopathy), moderate NPDR, severe NPDR and PDR (proliferative diabetic retinopathy). Artificial intelligence is an emerging area in the medical diagnosis industry, in specific, deep learning algorithms are used for classifying retina images for accurate diagnosis of disease. The proposed work acquired retina images from publicly available Kaggle repository and loaded into improved grid search Convolutional Neural Network model for accurate diagnosis on retina images with five different classes. This novel model helps ophthalmologists to classify DR as five stages based on severity from No DR to PDR. The Experimental study showed that the proposed model has performed better than the existing Convolutional Neural Network model with the accuracy of 89%.", "Keywords": "Convolutional Neural Network; cross-validation; Deep learning algorithm; Diabetic retinopathy; hyperparameter tuning; medical diagnosis", "DOI": "10.31449/inf.v46i8.4425", "PubYear": 2022, "Volume": "46", "Issue": "8", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Madras"}, {"AuthorId": 2, "Name": "S Sasikala", "Affiliation": "University of Madras"}], "References": []}, {"ArticleId": 105715427, "Title": "Lightweight authentication scheme based on modified EAP security for CoAP protocol-based IoMT applications", "Abstract": "The medical data generated from the patients that are communicated and stored on servers are highly sensitive, and also the IoMT network creates open spaces for an adversary. The proposed work designs a lightweight authentication scheme to support the extensible authentication protocol (EAP) called lightweight EAP (L-EAP). The proposed L-EAP modifies the EAP model and dynamically changes the security service as per healthcare application requirements. The L-EAP selectively applies the data encryption and integrity without frequent re-handshaking with the server using one-bit epoch field in the EAP message header. The L-EAP performs such a key generation process as a part of the authentication phase and enlarges the lifetime of the IoMT network. The advanced encryption standard (AES) is improved for providing data confidentiality in L-EAP. The L-EAP improves the confusion property of cipher text in AES and applies shift row and XOR operations to all the words. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "dynamic service change; improved AES-based encryption; internet of medical things; IoMT; lightweight mutual authentication; modified EAP", "DOI": "10.1504/IJICS.2023.128026", "PubYear": 2023, "Volume": "20", "Issue": "1/2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "Pritam S<PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Energy Acres, BIDHOLI, via, Prem Nagar, Uttarakhand, Dehradun, 248007, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Energy Acres, BIDHOLI, via, Prem Nagar, Uttarakhand, Dehradun, 248007, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>berger, South Delhi, India"}], "References": []}, {"ArticleId": 105715431, "Title": "Video conferencing as a teaching mode in higher educational institutions in Uganda: teacher perception", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSMARTTL.2022.128008", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 46975, "JournalTitle": "International Journal of Smart Technology and Learning", "ISSN": "2056-404X", "EISSN": "2056-4058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105715494, "Title": "Automated mucilage extraction index (AMEI): a novel spectral water index for identifying marine mucilage formations from Sentinel-2 imagery", "Abstract": "Marine mucilage that threatens marine habitats is one of the natural disasters, mainly resulting from global warming and marine pollution. Monitoring sea surface mucilage formations and mapping their spatial distributions provide valuable information to the local authorities and decision-makers in developing prevention and rehabilitation strategies. This study proposes a new spectral index called Automated Mucilage Extraction Index (AMEI) that allows effective and accurate detection of surface mucilage aggregates using Sentinel-2 satellite imagery. The index uses four bands of Sentinel-2 Level-2A imagery (Bands 3, 4, 8, and 12) covering visible, near-infrared, and shortwave infrared regions. The index was formulated considering the image acquired on 19 May 2021, when mucilage formations were most intensively observed in the Sea of Marmara. The performance of the developed index was then evaluated using the images acquired on 14 and 24 May and 13 June 2021. The Jenks Natural Breaks (JNB) algorithm was applied to estimate the threshold values for separating mucilage formations from the water surface background and classify index maps into two classes: ‘mucilage’ and ‘others’. To statistically analyse the effectiveness of the indices in distinguishing water background and mucilage formations, the proposed index was compared with 21 widely used water indices by applying Bhattacharyya Distance, Jeffries-Matusita Distance, and M-Statistic measures. Results confirm the robustness of the proposed spectral index, offering superior separation performance (above 1.5 in terms of M-Statistic) compared to other water indices on both cloud-free images and images with cumulus clouds. Visual interpretations also verified that boundaries of the mucilage formations in the cloudless and thin cloudy images were accurately identified by the proposed index, and different types of mucilage (i.e. yellow and white) can be identified when an appropriate histogram thresholding is applied.", "Keywords": "Sentinel-2 ; marine mucilage ; sea snot ; spectral index ; water index", "DOI": "10.1080/01431161.2022.2158049", "PubYear": 2023, "Volume": "44", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geomatics Engineering, Gebze Technical University, Gebze, Kocaeli, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geomatics Engineering, Gebze Technical University, Gebze, Kocaeli, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geomatics Engineering, Gebze Technical University, Gebze, Kocaeli, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geomatics Engineering, Gebze Technical University, Gebze, Kocaeli, Turkey"}], "References": [{"Title": "A Survey on Global Thresholding Methods for Mapping Open Water Body Using Sentinel-2 Satellite Imagery and Normalized Difference Water Index", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1335", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Augmented Normalized Difference Water Index for improved surface water monitoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "105030", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "A spectral index for the detection of algal blooms using Sentinel-2 Multispectral Instrument (MSI) imagery: a case study of Hulun Lake, China", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "12", "Page": "4514", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Review of deep learning: concepts, CNN architectures, challenges, applications, future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "53", "JournalTitle": "Journal of Big Data"}, {"Title": "Remote detection of marine debris using satellite observations in the visible and near infrared spectral range: Challenges and potentials", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "259", "Issue": "", "Page": "112414", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Spectral characteristics of sea snot reflectance observed from satellites: Implications for remote sensing of marine debris", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "269", "Issue": "", "Page": "112842", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 105715538, "Title": "Optimized ensemble learning‐based student's performance prediction with weighted rough set theory enabled feature mining", "Abstract": "<p>Currently, educational data mining act as a major part of student performance prediction approaches and their applications. However, more ensemble methods are needed to improve the student performance prediction, and also which helps increase the learning quality of the Student's performance. The usage of an ensemble classifier with rule mining to predict students' academic success is proposed. In response to this need, this research mainly concentrated on an ensemble classifier with rule mining to predict students' academic success. The feature mining is performed using the weighted Rough Set Theory method, in which the proposed meta-heuristic algorithm optimizes the weight function. The variable optimization of the ensemble classifier is accomplished with the help of a combination of Harris Hawks Optimization (HHO), and Krill Herd Algorithm (KHA) known as Escape Energy Searched Krill Herd-Harris Hawks Optimization (EES-KHHO) for maximizing the prediction rate. Extensive tests are carried out on various datasets, and the findings show that our technique outperforms conventional approaches. Throughout the result analysis, the offered method attains a 92.77% accuracy rate, and also it attains a sensitivity rate of 94.87%. Therefore, the offered student performance prediction model achieves better effectiveness regarding various performance metrics.</p>", "Keywords": "feature mining;optimization algorithms;optimized ensemble learning;student's performance prediction", "DOI": "10.1002/cpe.7601", "PubYear": 2023, "Volume": "35", "Issue": "7", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Jawaharlal Nehru Technological University Kakinada  Kakinada India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering MVGR College of Engineering  Vizianagaram India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University College of Engineering Vizianagaram Jawaharlal Nehru Technological University Kakinada  Kakinada India"}], "References": [{"Title": "A comprehensive survey on symbiotic organisms search algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "2265", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An overview and comparison of supervised data mining techniques for student exam performance prediction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "103676", "JournalTitle": "Computers & Education"}, {"Title": "Multi-task MIML learning for pre-course student performance prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "5", "Page": "145313", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Prediction of cardiovascular diseases using weight learning based on density information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "566", "JournalTitle": "Neurocomputing"}, {"Title": "A feature weighted support vector machine and artificial neural network algorithm for academic course performance prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "16", "Page": "11517", "JournalTitle": "Neural Computing and Applications"}, {"Title": "African vultures optimization algorithm: A new nature-inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107408", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Advances in Spotted Hyena Optimizer: A Comprehensive Survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "3", "Page": "1569", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Artificial gorilla troops optimizer: A new nature‐inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "10", "Page": "5887", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Advances in Tree Seed Algorithm: A Comprehensive Survey", "Authors": "<PERSON><PERSON> Gharehchopogh", "PubYear": 2022, "Volume": "29", "Issue": "5", "Page": "3281", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Advances in Tree Seed Algorithm: A Comprehensive Survey", "Authors": "<PERSON><PERSON> Gharehchopogh", "PubYear": 2022, "Volume": "29", "Issue": "5", "Page": "3281", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Mutation and dynamic objective-based farmland fertility algorithm for workflow scheduling in the cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "164", "Issue": "", "Page": "69", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 105715726, "Title": "Hand grip strength for the working-age population in South Korea: Development of an estimation and evaluation model", "Abstract": "Although grip strength is frequently measured in clinical settings, methods for evaluating individual grip strength considering physical characteristics are limited. We attempted to develop an easily applicable statistical model to estimate and evaluate the grip strength of Korean workers according to their age, sex, and anthropometric data. Data were collected from the KNHANES (2014–2019). The data were divided into the test and training sets. Potential regression models for estimating grip strength have been suggested based on sex and hand dominance. The performance of each model was compared, and the best model was selected. The estimated grip strength was calculated for each participant. The distribution of the measured to estimated value ratios was presented. The ratios between the dominant and non-dominant hand grip strengths were also calculated. Overall, 21,807 (9652 men and 12,155 women) individuals were included in the dataset. The selected predictors were age, age^2, height, body mass index (BMI), and body mass-to-waist ratio for men and age, age^2, height, BMI, and waist circumference for women. The measured estimated values were 100.0 ± 16.2%, 100.0 ± 16.3% for dominant and non-dominant hands in men and 100.0 ± 18.9% for dominant and non-dominant hands in women. The 95% confidence interval of the dominant to non-dominant hand grip ratio was 84.4–126.7% for men and 82.4–131.3% for women. Grip strength in workers can be screened in comparison to that in the Korean population using the suggested models. This model is an effective method for identifying abnormalities in the upper extremities of Korean workers.", "Keywords": "", "DOI": "10.1016/j.ergon.2022.103398", "PubYear": 2023, "Volume": "94", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Occupational and Environmental Medicine, Yeungnam University Hospital, 170, Hyeonchung-ro, Daegu, Republic of Korea"}, {"AuthorId": 2, "Name": "Jong-Tae Park", "Affiliation": "Department of Occupational and Environmental Medicine, Korea University Ansan Hospital, 123, Jeokgeum-ro, Danwon-gu, Ansan-si, Gyeonggi-do, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiology, Keimyung University Dongsan Hospital, 1035, Dalgubeol-daero, Daegu, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Occupational and Environmental Medicine, Korea University Ansan Hospital, 123, <PERSON><PERSON>geum-ro, Danwon-gu, Ansan-si, Gyeonggi-do, Republic of Korea;Corresponding author. Department of Occupational and Environmental Medicine, Korea University Ansan Hospital, 123, Jeokgeum-ro, Danwon-gu, Ansan-si, Gyeonggi-do, 15355, Republic of Korea"}], "References": [{"Title": "Age, gender and side-stratified grip strength norms and related socio-demographic factors for 20–80 years Iranian healthy population: Comparison with consolidated and international norms", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "", "Page": "103003", "JournalTitle": "International Journal of Industrial Ergonomics"}]}, {"ArticleId": 105715767, "Title": "Indicators of employee phishing email behaviours: Intuition, elaboration, attention, and email typology", "Abstract": "Employees’ behaviour to phishing emails can strengthen or undermine business organisations’ cyber security. This phishing simulation and survey study explored the relationship between sociodemographic, cyber security training, phishing email typology and information processing factors and risky and secure email response behaviours. Participants ( N  = 590) were employees of a large financial institution who received one of four types of phishing emails. Participants who engaged in risky cyber email behaviour clicked on the link in the phishing email whereas those who engaged in secure cyber email behaviour reported the email to the institutions cyber security team. Our findings show that the likelihood of clicking on a link in a phishing email was lower for participants who had greater faith in their intuition and paid more attention to the sender&#x27;s email address. The likelihood of clicking on a link in a phishing email was greater for participants who received the ‘Undelivered package’ email relative to the ‘Received PDF’. The likelihood of reporting a phishing email was greater for participants who engaged in greater elaborative processing to evaluate the email than those who used less elaboration. Theoretical and practical implications as well as future directions are discussed.", "Keywords": "", "DOI": "10.1016/j.ijhcs.2023.102996", "PubYear": 2023, "Volume": "172", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Psychology, The University of Auckland, Private Bag, Auckland 92019, New Zealand;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, The University of Auckland, Private Bag, Auckland 92019, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Psychology, The University of Auckland, Private Bag, Auckland 92019, New Zealand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Psychology, The University of Auckland, Private Bag, Auckland 92019, New Zealand"}], "References": [{"Title": "Which Phish Is on the Hook? Phishing Vulnerability for Older Versus Younger Adults", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "5", "Page": "704", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Email phishing and signal detection: How persuasion principles and personality influence response patterns and accuracy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "103084", "JournalTitle": "Applied Ergonomics"}, {"Title": "Don’t click: towards an effective anti-phishing training. A comparative literature review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Human-centric Computing and Information Sciences"}, {"Title": "How personal characteristics impact phishing susceptibility: The mediating role of mail processing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "103526", "JournalTitle": "Applied Ergonomics"}, {"Title": "IBM: Cost of a Data Breach Report", "Authors": "", "PubYear": 2021, "Volume": "2021", "Issue": "8", "Page": "4", "JournalTitle": "Computer Fraud & Security"}, {"Title": "Human Factors in Phishing Attacks: A Systematic Literature Review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 105715771, "Title": "MODELING THE PACKING DENSITY OF THE SIMPLEST HEXAGONAL LATTICE. III", "Abstract": "", "Keywords": "", "DOI": "10.22250/18142400_2022_74_4_42", "PubYear": 2022, "Volume": "", "Issue": "4", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105715773, "Title": "Robust deep neural network surrogate models with uncertainty quantification via adversarial training", "Abstract": "Surrogate models have been used to emulate mathematical simulators of physical or biological processes for computational efficiency. High-speed simulation is crucial for conducting uncertainty quantification (UQ) when the simulation must repeat over many randomly sampled input points (aka the <PERSON> method). A simulator can be so computationally intensive that UQ is only feasible with a surrogate model. Recently, deep neural network (DNN) surrogate models have gained popularity for their state-of-the-art emulation accuracy. However, it is well-known that DNN is prone to severe errors when input data are perturbed in particular ways, the very phenomenon which has inspired great interest in adversarial training. In the case of surrogate models, the concern is less about a deliberate attack exploiting the vulnerability of a DNN but more of the high sensitivity of its accuracy to input directions, an issue largely ignored by researchers using emulation models. In this paper, we show the severity of this issue through empirical studies and hypothesis testing. Furthermore, we adopt methods in adversarial training to enhance the robustness of DNN surrogate models. Experiments demonstrate that our approaches significantly improve the robustness of the surrogate models without compromising emulation accuracy. © 2023 Wiley Periodicals LLC.", "Keywords": "adversarial training; robustness; simulator; surrogate model; uncertainty quantification", "DOI": "10.1002/sam.11610", "PubYear": 2023, "Volume": "16", "Issue": "3", "JournalId": 5556, "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal", "ISSN": "1932-1864", "EISSN": "1932-1872", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics The Pennsylvania State University  University Park Pennsylvania USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics The Pennsylvania State University  University Park Pennsylvania USA"}], "References": []}, {"ArticleId": 105715775, "Title": "Model selection with bootstrap validation", "Abstract": "Model selection is one of the most central tasks in supervised learning. Validation set methods are the standard way to accomplish this task: models are trained on training data, and the model with the smallest loss on the validation data is selected. However, it is generally not obvious how much validation data is required to make a reliable selection, which is essential when labeled data are scarce or expensive. We propose a bootstrap-based algorithm, bootstrap validation (BSV), that uses the bootstrap to adjust the validation set size and to find the best-performing model within a tolerance parameter specified by the user. We find that BSV works well in practice and can be used as a drop-in replacement for validation set methods or k-fold cross-validation. The main advantage of BSV is that less validation data is typically needed, so more data can be used to train the model, resulting in better approximations and efficient use of validation data. © 2023 The Authors. Statistical Analysis and Data Mining published by Wiley Periodicals LLC.", "Keywords": "bootstrap; model selection", "DOI": "10.1002/sam.11606", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 5556, "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal", "ISSN": "1932-1864", "EISSN": "1932-1872", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Helsinki, Helsinki, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Helsinki, Helsinki, Finland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Helsinki and Institute for Atmospheric and Earth System Research, University of Helsinki, Helsinki, Finland"}], "References": [{"Title": "Interactive visual data exploration with subjective feedback: an information-theoretic approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "1", "Page": "21", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Detecting virtual concept drift of regressors without ground truth values", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "3", "Page": "726", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Low-Cost Outdoor Air Quality Monitoring and Sensor Calibration", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}]}, {"ArticleId": 105715782, "Title": "The friction mechanics model within chip-tool-workpiece dual interfaces for cutting nickel-based superalloy at the cooling and lubrication conditions of the three forms oil-on-water mist in wide temperature range", "Abstract": "<p>Nickel-Based superalloy has been extensively applied in the critical mechanical bearing components, such as aeroengine owing to its excellent performance at high temperature, but its machinability has always been dissatisfied. In order to improve processing efficiency, reduce cost, and decrease environmental pollution, in this paper, the friction mechanics model within chip-tool-workpiece dual interfaces was established in turning of S-type difficult-to-machine nickel-based superalloy Inconel 718 utilizing the PVD TiAlN coated carbide tool under the cooling and lubrication conditions of three forms oil-on-water mist in wide temperature range (− 30 ~ 150 ℃) (high temperature oil-on-water mist (150 ℃), normal temperature oil-on-water mist (20 ℃), and low temperature oil-on-water mist (− 30 ℃)). Meanwhile, the influence law and mechanism of the cutting speed, depth of cut, and feed on the three-way cutting forces, friction coefficient between chip-tool-workpiece dual interfaces, and normal stress and tangential stress in chip-tool interface have been studied. The results showed that the lowest and highest friction coefficient within chip-tool interface was obtained at the conditions of the high temperature oil-on-water mist (HTOoW) and the normal temperature oil-on-water mist (NTOoW), respectively. The most notable influence of the low temperature oil-on-water mist (LTOoW) on the strain hardening, strain rate hardening, and thermal softening of the materials occurred.</p>", "Keywords": "Friction mechanics model; Friction coefficient; Stress; Chip-tool-workpiece dual interfaces; Oil-on-water", "DOI": "10.1007/s00170-022-10630-4", "PubYear": 2023, "Volume": "125", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province (University), Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province (University), Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Northwest Minzu University, Lanzhou, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province (University), Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 5, "Name": "Baodong Li", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province (University), Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province (University), Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}], "References": [{"Title": "Environmentally conscious machining of Inconel 718: surface roughness, tool wear, and material removal rate assessment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "1-2", "Page": "303", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Comparative evaluation of soybean oil–based MQL flow rates and emulsion flood cooling strategy in high-speed face milling of Inconel 718", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "9-10", "Page": "3779", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105715847, "Title": "A Study on Personal Information Protection amid the COVID-19 Pandemic", "Abstract": "COVID-19, a highly infectious disease, has affected the globe tremendously since its outbreak during late 2019 in Wuhan, China. In order to respond to the pandemic, governments around the world introduced a variety of public health measures including contact-tracing, a method to identify individuals who may have come into contact with a confirmed COVID-19 patient, which usually leads to quarantine of certain individuals. Like many other governments, the South Korean health authorities adopted public health measures using latest data technologies. Key data technology-based quarantine measures include:(1) Electronic Entry Log; (2) Self-check App; and (3) COVID-19 Wristband, and heavily relied on individual's personal information for contact-tracing and self-isolation. In fact, during the early stages of the pandemic, South Korea's strategy proved to be highly effective in containing the spread of coronavirus while other countries suffered significantly from the surge of COVID-19 patients. However, while the South Korean COVID-19 policy was hailed as a success, it must be noted that the government achieved this by collecting and processing a wide range of personal information. In collecting and processing personal information, the data minimum principle - one of the widely recognized common data principles between different data protection laws - should be applied. Public health measures have no exceptions, and it is even more crucial when government activities are involved. In this study, we provide an analysis of how the governments around the world reacted to the COVID-19 pandemic and evaluate whether the South Korean government's digital quarantine measures ensured the protection of its citizen's right to privacy. Copyright © 2022 KSII.", "Keywords": "COVID-19 pandemic; Data Minimization Principle; Personal Information; Privacy", "DOI": "10.3837/tiis.2022.12.016", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105715947, "Title": "The development of an ANN surface roughness prediction system of multiple materials in CNC turning", "Abstract": "<p>As one of the critical output parameters in the machining process, surface roughness quality must be constantly monitored, including predictions. Many scholars have researched sensing technology to monitor surface roughness. However, most of the research applied in a single-material model; this research intended to explore the intelligent combination prediction system between two materials, namely stainless steel (SUS304) and aluminum (Al6061). The machining process used computer numerical control (CNC) turning and applied sensing technology to collect signals as input factors in the prediction model. The prediction model used an artificial neural network (ANN) with the learning curve to find a good fitting of root mean square error ( (RMSE) ) in training and validation. This research obtains the best accuracy prediction in each material and multi-material model by developing a backpropagation neural network prediction model, in which the surface roughness ( R <sub> a </sub>) was output, and the signal factors were inputs. The precise prediction of the multi-material model was higher (96.74%) than the accurate predictions of the SUS304 model (93.75%) and Al6061 model (89.81%). An appropriate t-test was used to compare the error prediction results of every single-material and multi-material models. From the t-test result of the error model, there were significant differences between the single and multi-material. This result was highly recommended to be practically applied in the manufacturing industry with various materials. Further research was proposed for improvement.</p>", "Keywords": "Surface roughness prediction; CNC turning; Artificial neural network development; Multi-material", "DOI": "10.1007/s00170-022-10709-y", "PubYear": 2023, "Volume": "125", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "PoTsang B. Huang", "Affiliation": "Department of Industrial and Systems Engineering, Chung Yuan Christian University, Taoyuan City, Taiwan, Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Chung Yuan Christian University, Taoyuan City, Taiwan, Republic of China; Department of Industrial Engineering, Atma Jaya Catholic University of Indonesia, Tangerang, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Chung Yuan Christian University, Taoyuan City, Taiwan, Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Atma Jaya Catholic University of Indonesia, Tangerang, Indonesia"}], "References": [{"Title": "An automatic and accurate method for tool wear inspection using grayscale image probability algorithm based on bayesian inference", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "102079", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "An autoencoder wavelet based deep neural network with attention mechanism for multi-step prediction of plant growth", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "35", "JournalTitle": "Information Sciences"}, {"Title": "Joint production, inspection and maintenance control policies for deteriorating system under quality constraint", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "585", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Development of a vision based pose estimation system for robotic machining and improving its accuracy using LSTM neural networks and sparse regression", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102262", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Development of a real-time failure detection system for stamping die", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "7-8", "Page": "5623", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105715962, "Title": "Motivation of Trainee Teachers in Conducting Online Learning Using Digital Games Based on Arcs Motivation Model", "Abstract": "The endemic situation of Covid-19 plaguing the world today has huge implications for education. This requires teachers to be more creative and innovative in delivering knowledge in the classroom via online learning. However, trainee teachers lack experience and skills compared to professional teachers, which affects the consistency of their motivation. Therefore, this paper discusses the perceived improvement of motivation of trainee teachers based on the ARCS motivation model toward a game-based learning approach through a digital game called StayFit as a teaching tool. This study focuses on four components of ARCS motivational constructs;- i) attention; ii) relevance; iii) confidence; and iv) satisfaction. The research design is quantitative and based on a survey using a questionnaire to collect the data. 56 respondents were involved in this study and the data were analysed using descriptive statistics. The results of this study show that the trainee teachers gave positive feedback through the implementation of digital game-based learning in their online classes especially in terms of enhancing their motivation in the teaching process. The implication of the study shows that the use of digital game as a teaching aid in online learning can increase the motivation of trainee teachers and make the learning process more effective, which meet with the needs of remote learning that applied during this endemic situation.", "Keywords": "", "DOI": "10.17576/apjitm-2022-1102-03", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 43050, "JournalTitle": "Asia-Pacific Journal of Information Technology and Multimedia", "ISSN": "", "EISSN": "2289-2192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105716032, "Title": "Learning Reliable Neural Networks with Distributed Architecture Representations", "Abstract": "<p>Neural architecture search (NAS) has shown the strong performance of learning neural models automatically in recent years. But most NAS systems are unreliable due to the architecture gap brought by discrete representations of atomic architectures. In this paper, we improve the performance and robustness of NAS var narrowing the gap between architecture representations. More specifically, we apply a general contraction mapping to model neural networks with distributed representations (call it ArchDAR). Moreover, for a better search result, we present a joint learning approach to integrating distributed representations with advanced architecture search methods. We implement our ArchDAR in a differentiable architecture search model and test learned architectures on the language modeling task. On the PTB data, it outperforms a strong baseline significantly by 1.8 perplexity scores. Also, the search process with distributed representations is more stable which yields a faster structural convergence when it works with the DARTS model.</p>", "Keywords": "neural architecture search; neural networks; language modeling; natural language processing", "DOI": "10.1145/3578709", "PubYear": 2023, "Volume": "22", "Issue": "4", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> He", "Affiliation": "Tencent, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University and also NiuTrans Research, China"}, {"AuthorId": 5, "Name": "Jing<PERSON> Zhu", "Affiliation": "School of Computer Science and Engineering, Northeastern University and also NiuTrans Research, China"}], "References": [{"Title": "Progressive DARTS: Bridging the Optimization Gap for NAS in the Wild", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "3", "Page": "638", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}]}, {"ArticleId": 105716084, "Title": "Inequidades laborales en la pandemia: algunas consideraciones para reflexionar la dignidad y la exclusión en repartidores de comida en la ciudad de Pachuca de Soto, Hidalgo", "Abstract": "<p>En el marco de la crisis sanitaria Covid-19, la Administración contemporánea requiere examinar con cautela los alcances de sus contenidos teórico conceptuales más importantes. Para ello, recuperar conceptos desde las Ciencias Sociales permite equilibrar su relevancia como una disciplina. La incertidumbre, como una variable contingente del análisis organizacional, en relieve con la conexión del contexto para identificar la brecha entre teoría y manifestaciones cotidianas del trabajo. La vulnerabilidad se agravo, a través de que se desarrollan en el trabajo actividades operativas, al visibilizarse distintos elementos que estaban ocultos, que al manifestarse dan como resultado un modelo de precarización caracterizado por la exclusión. El hilo que entreteje estas narrativas se encuentra en el imaginario colectivo. Que aloja un complejo marco de significados que reproducen inequidades. El artículo de investigación reflexiona alrededor de dos conceptos centrales: dignidad y exclusión. El trabajo se divide en dos apartados. El primero discute cómo la dignidad de las personas se tensa cuando existe un alto nivel de incertidumbre laboral. Se esboza el significado del concepto de dignidad en y para el trabajo en la pandemia. Posteriormente se expone el concepto exclusión que cruza la percepción del esfuerzo para desempeñar una tarea y denotarlo en el contexto actual y como consecuencia de la crisis financiera neoliberal, que ha llevado a miles de personas a perder un empleo y vivir en un estado de precariedad socioeconómica. El trabajo se centra en los operarios repartidores de comida, en la ciudad de Pachuca de Soto, Hidalgo.  \r  \r  \r  </p>", "Keywords": "Dignidad laboral;exclusión;Administración critica;Covid-19;Operarios de repartidores de comida", "DOI": "10.29057/est.v8i16.9048", "PubYear": 2023, "Volume": "8", "Issue": "16", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma Metropolitana Unidad-Xochimilco"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}], "References": []}, {"ArticleId": 105716191, "Title": "Learning the dynamics of particle-based systems with Lagrangian graph neural networks", "Abstract": "<p>Physical systems are commonly represented as a combination of particles, the individual dynamics of which govern the system dynamics. However, traditional approaches require the knowledge of several abstract quantities such as the energy or force to infer the dynamics of these particles. Here, we present a framework, namely, Lagrangian graph neural network (LGnn), that provides a strong inductive bias to learn the Lagrangian of a particle-based system directly from the trajectory. We test our approach on challenging systems with constraints and drag—LGnn outperforms baselines such as feed-forward Lagrangian neural network (Lnn) with improved performance. We also show the zero-shot generalizability of the system by simulating systems two orders of magnitude larger than the trained one and also hybrid systems that are unseen by the model, a unique feature. The graph architecture of LGnn significantly simplifies the learning in comparison to Lnn with ~25 times better performance on ~20 times smaller amounts of data. Finally, we show the interpretability of LGnn, which directly provides physical insights on drag and constraint forces learned by the model. LGnn can thus provide a fillip toward understanding the dynamics of physical systems purely from observable quantities.</p>", "Keywords": "", "DOI": "10.1088/2632-2153/acb03e", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 72803, "JournalTitle": "Machine Learning: Science and Technology", "ISSN": "", "EISSN": "2632-2153", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Indian Institute of Technology Delhi, Hauz Khas, New Delhi 110016, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Delhi, Hauz Khas, New Delhi 110016, India; Yardi School of Artificial Intelligence, Indian Institute of Technology Delhi, Hauz Khas, New Delhi 110016, India"}, {"AuthorId": 3, "Name": "<PERSON> <PERSON>", "Affiliation": "Department of Civil Engineering, Indian Institute of Technology Delhi, Hauz Khas, New Delhi 110016, India; Yardi School of Artificial Intelligence, Indian Institute of Technology Delhi, Hauz Khas, New Delhi 110016, India"}], "References": [{"Title": "Modeling System Dynamics with Physics-Informed Neural Networks Based on Lagrangian Mechanics", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "9195", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105716364, "Title": "User Bias Drift Social Recommendation Algorithm based on Metric Learning", "Abstract": "Social recommendation algorithm can alleviate data sparsity and cold start problems in recommendation system by integrated social information. Among them, matrix-based decomposition algorithms are the most widely used and studied. Such algorithms use dot product operations to calculate the similarity between users and items, which ignores user's potential preferences, reduces algorithms' recommendation accuracy. This deficiency can be avoided by a metric learning-based social recommendation algorithm, which learns the distance between user embedding vectors and item embedding vectors instead of vector dot-product operations. However, previous works provide no theoretical explanation for its plausibility. Moreover, most works focus on the indirect impact of social friends on user's preferences, ignoring the direct impact on user's rating preferences, which is the influence of user rating preferences. To solve these problems, this study proposes a user bias drift social recommendation algorithm based on metric learning (BDML). The main work of this paper is as follows: (1) the process of introducing metric learning in the social recommendation scenario is introduced in the form of equations, and explained the reason why metric learning can replace the click operation; (2) a new user bias is constructed to simultaneously model the impact of social relationships on user's ratings preferences and user's preferences; Experimental results on two datasets show that the BDML algorithm proposed in this study has better recommendation accuracy compared with other comparison algorithms, and will be able to guarantee the recommendation effect in a more sparse dataset. © 2022 Korean Society for Internet Information. All rights reserved.", "Keywords": "Artificial Intelligence; Matrix decomposition; Metric learning; Recommender system; Social recommendation", "DOI": "10.3837/tiis.2022.12.001", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105716482, "Title": "Low-cost electronic system for monitoring vapor pressure deficit and sunlight using a Raspberry Pi Pico board", "Abstract": "<p>Se presenta el desarrollo de un sistema electrónico para monitorear las condiciones atmosféricas en un invernadero experimental. El núcleo del sistema consiste en una tarjeta Raspberry Pi Pico recientemente lanzada en 2021, programada con el lenguaje Micropython. Se seleccionó un módulo DHT22 y una resistencia dependiente de luz como sensor de luz solar para monitorear las condiciones ambientales. La prueba de rendimiento del sistema se llevó a cabo durante 3 días durante la etapa de germinación de 6 lechugas red salad bowl y 6 semillas de Amaranto; se seleccionó espuma agrícola como sustrato mediante el método de riego por goteo manual. Los datos de temperatura, humedad relativa, déficit de presión de vapor y lectura del sensor de luz solar se capturaron, registraron y accesaron utilizando el IDE de Thonny en un archivo de texto con formato de columnas almacenado en la memoria del microcontrolador RP2040. El costo total del sistema electrónico utilizado para diseñar el sistema fue de alrededor de US $ 11, lo que lo convierte en una alternativa atractiva para actividades académicas y proyectos de investigación.</p>", "Keywords": "Monitoreo;Déficit de presión de vapor;microcontrolador;bajo costo", "DOI": "10.29057/est.v8i16.9651", "PubYear": 2023, "Volume": "8", "Issue": "16", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Tecnológica de México - UNITEC MÉXICO - Campus Atizapán"}, {"AuthorId": 2, "Name": "<PERSON>-Villa", "Affiliation": "Postgrado Edafología. Colegio de Posgraduados – Campus Montecillo"}], "References": []}, {"ArticleId": 105716497, "Title": "Numerical Analysis for the Effect of Irresponsible Immigrants on HIV/AIDS Dynamics", "Abstract": "The human immunodeficiency viruses are two species of Lentivirus that infect humans. Over time, they cause acquired immunodeficiency syndrome, a condition in which progressive immune system failure allows life-threatening opportunistic infections and cancers to thrive. Human immunodeficiency virus infection came from a type of chimpanzee in Central Africa. Studies show that immunodeficiency viruses may have jumped from chimpanzees to humans as far back as the late 1800s. Over decades, human immunodeficiency viruses slowly spread across Africa and later into other parts of the world. The Susceptible-Infected-Recovered (SIR) models are significant in studying disease dynamics. In this paper, we have studied the effect of irresponsible immigrants on HIV/AIDS dynamics by formulating and considering different methods. Euler, Runge <PERSON>, and a Non-standard finite difference (NSFD) method are developed for the same problem. Numerical experiments are performed at disease-free and endemic equilibria points at different time step sizes ‘ℎ’. The results reveal that, unlike Euler and Runge <PERSON>, which fail for large time step sizes, the proposed Non-standard finite difference (NSFD) method gives a convergence solution for any time step size. Our proposed numerical method is bounded, dynamically consistent, and preserves the positivity of the continuous solution, which are essential requirements when modeling a prevalent disease.", "Keywords": "existence analysis; numerical methods; SIR model; stability analysis", "DOI": "10.32604/iasc.2023.033157", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Barani Institute of Sciences, Burewala, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Cankaya University, Balgat, Ankara, 06530, Turkey; Department of Medical Research, China Medical University, Taichung, 40402, Taiwan; Institute of Space Sciences, Magurele, Bucharest, 077125, Romania"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Sciences, University of Central Punjab, Lahore, 54000, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Automation, Biomechanics and Mechatronics, Lodz University of Technology, 1/15 Stefanowskiego St, Lodz, 90-924, Poland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, University of Lahore, Lahore, 54590, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Govt. <PERSON><PERSON><PERSON> Graduate College Wazirabad, Punjab Higher Education Department (PHED), Lahore, 54000, Pakistan"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "H&BS, MCS, National University of Science and Technology (NUST), H-12, Islamabad, Humayun Road, Rawalpindi, 46000, Pakistan"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Statistics, University of Lahore, Lahore, 54590, Pakistan"}], "References": [{"Title": "Stochastic Numerical Analysis for Impact of Heavy Alcohol Consumption on Transmission Dynamics of Gonorrhoea Epidemic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "3", "Page": "1125", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Numerical Analysis of Stochastic Vector Borne Plant Disease Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "3", "Page": "65", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105716498, "Title": "Reinforcement Learning to Improve QoS and Minimizing Delay in IoT", "Abstract": "Machine Learning concepts have raised executions in all knowledge domains, including the Internet of Thing (IoT) and several business domains. Quality of Service (QoS) has become an important problem in IoT surrounding since there is a vast explosion of connecting sensors, information and usage. Sensor data gathering is an efficient solution to collect information from spatially disseminated IoT nodes. Reinforcement Learning Mechanism to improve the QoS (RLMQ) and use a Mobile Sink (MS) to minimize the delay in the wireless IoT s proposed in this paper. Here, we use machine learning concepts like Reinforcement Learning (RL) to improve the QoS and energy efficiency in the Wireless Sensor Network (WSN). The MS collects the data from the Cluster Head (CH), and the RL incentive values select CH. The incentives value is computed by the QoS parameters such as minimum energy utilization, minimum bandwidth utilization, minimum hop count, and minimum time delay. The MS is used to collect the data from CH, thus minimizing the network delay. The sleep and awake scheduling is used for minimizing the CH dead in the WSN. This work is simulated, and the results show that the RLMQ scheme performs better than the baseline protocol. Results prove that RLMQ increased the residual energy, throughput and minimized the network delay in the WSN.", "Keywords": "Internet of things; mobile sink; quality of service parameters; reinforcement learning; Wireless sensor network", "DOI": "10.32604/iasc.2023.032396", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, Velalar College of Engineering and Technology, Tamil Nadu, Erode, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sathyabama Institute of Science and Technology, Tamil Nadu, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science, King Khalid University, Abha, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, Panimalar Institute of Technology, Tamil Nadu, Chennai, India"}], "References": [{"Title": "Hybrid metaheuristic algorithm for optimal cluster head selection in wireless sensor network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "79", "Issue": "", "Page": "101504", "JournalTitle": "Pervasive and Mobile Computing"}]}, {"ArticleId": 105716502, "Title": "New Ranking of Generalized Quadrilateral Shape Fuzzy Number Using Centroid Technique", "Abstract": "The output of the fuzzy set is reduced by one for the defuzzification procedure. It is employed to provide a comprehensible outcome from a fuzzy inference process. This page provides further information about the defuzzification approach for quadrilateral fuzzy numbers, which may be used to convert them into discrete values. Defuzzification demonstrates how useful fuzzy ranking systems can be. Our major purpose is to develop a new ranking method for generalized quadrilateral fuzzy numbers. The primary objective of the research is to provide a novel approach to the accurate evaluation of various kinds of fuzzy integers. Fuzzy ranking properties are examined. Using the counterexamples of <PERSON> and <PERSON> demonstrates the fallacy of the ranking technique. So, a new approach has been developed for dealing with fuzzy risk analysis, risk management, industrial engineering and optimization, medicine, and artificial intelligence problems: the generalized quadrilateral form fuzzy number utilizing centroid methodology. As you can see, the aforementioned scenarios are all amenable to the solution provided by the generalized quadrilateral shape fuzzy number utilizing centroid methodology. It’s laid out in a straightforward manner that’s easy to grasp for everyone. The rating method is explained in detail, along with numerical examples to illustrate it. Last but not least, stability evaluations clarify why the Generalized quadrilateral shape fuzzy number obtained by the centroid methodology outperforms other ranking methods.", "Keywords": "Fuzzy numbers; fuzzy risk analysis; quadrilateral fuzzy number; ranking methods", "DOI": "10.32604/iasc.2023.033870", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Sathyabama Institute of Science and Technology, Tamilnadu, Chennai, 600119, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Mathematics, Sathyabama Institute of Science and Technology, Tamilnadu, Chennai, 600119, India"}], "References": []}, {"ArticleId": 105716523, "Title": "Clustered Wireless Sensor Network in Precision Agriculture via Graph Theory", "Abstract": "Food security and sustainable development is making a mandatory move in the entire human race. The attainment of this goal requires man to strive for a highly advanced state in the field of agriculture so that he can produce crops with a minimum amount of water and fertilizer. Even though our agricultural methodologies have undergone a series of metamorphoses in the process of a present smart-agricultural system, a long way is ahead to attain a system that is precise and accurate for the optimum yield and profitability. Towards such a futuristic method of cultivation, this paper proposes a novel method for monitoring the efficient flow of a small quantity of water through the conventional irrigation system in cultivation using Clustered Wireless Sensor Networks (CWSN). The performance measure is simulated the creation of edge-fixed geodetic clusters using Mat lab’s Cup-carbon tool in order to evaluate the suggested irrigation process model’s performance. The findings of blocks 1 and 2 are assessed. Each signal takes just a little amount of energy to communicate, according to the performance. It is feasible to save energy while maintaining uninterrupted communication between nodes and cluster chiefs. However, the need for proper placement of a dynamic control station in WSN still exists for maintaining connectivity and for improving the lifetime fault tolerance of WSN. Based on the minimum edge fixed geodetic sets of the connected graph, this paper offers an innovative method for optimizing the placement of control stations. The edge-fixed geodetic cluster makes the network fast, efficient and reliable. Moreover, it also solves routing and congestion problems.", "Keywords": "agriculture; cluster; control station; edge fixed geodetic set; precision agriculture; Wireless sensor networks", "DOI": "10.32604/iasc.2023.030591", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Science and Humanities, Mar <PERSON>ph<PERSON>m College of Engineering and Technology, Tamil Nadu629171, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Science and Humanities, University College of Engineering Nagercoil, Anna University Constituent College, Konam, Nagercoil, 629 004, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Mar <PERSON>ph<PERSON>m College of Engineering and Technology, Tamil Nadu629171, India"}], "References": [{"Title": "Edge Computing-Enabled Wireless Sensor Networks for Multiple Data Collection Tasks in Smart Agriculture", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Journal of Sensors"}, {"Title": "Clustering objectives in wireless sensor networks: A survey and research direction analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "180", "Issue": "", "Page": "107376", "JournalTitle": "Computer Networks"}, {"Title": "A Big Data Text Coverless Information Hiding Based on Topic Distribution and TF-IDF: ", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "4", "Page": "40", "JournalTitle": "International Journal of Digital Crime and Forensics"}, {"Title": "A Robust Text Coverless Information Hiding Based on Multi-Index Method", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "899", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105716564, "Title": "Recommending Relevant Services in Electronic and Mobile Health Platforms", "Abstract": "Electronic and mobile health (EMH) is becoming an integrated part of healthcare as we move in the future. The opportunity in bringing closer healthcare services with the advent of the internet is growing larger. This is why it is important to adequately provide those services to the people that need them and to also further improve them. Regarding electronic and mobile healthcare systems, it is fairly easy for users to get lost while searching for some information due to the vast amount of data that is present for different illnesses, healthcare institutions and healthcare services. In this paper we present a platform that provides various healthcare services to people, namely the Insieme platform (ISE-EMH). Knowing the difficulty of finding relevant information on platforms and that user preferences vary to a great extent, we additionally give an overview of an implementation of the recommendation system that is part of the Insieme platform which helps users pick services that might be relevant to them.", "Keywords": "Electronic and mobile health; embeddings; recommendation systems", "DOI": "10.31449/inf.v46i4.4161", "PubYear": 2023, "Volume": "46", "Issue": "4", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jožef <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jožef <PERSON>"}], "References": []}, {"ArticleId": 105716630, "Title": "Spectrum Sensing Using Optimized Deep Learning Techniques in Reconfigurable Embedded Systems", "Abstract": "The exponential growth of Internet of Things (IoT) and 5G networks has resulted in maximum users, and the role of cognitive radio has become pivotal in handling the crowded users. In this scenario, cognitive radio techniques such as spectrum sensing, spectrum sharing and dynamic spectrum access will become essential components in Wireless IoT communication. IoT devices must learn adaptively to the environment and extract the spectrum knowledge and inferred spectrum knowledge by appropriately changing communication parameters such as modulation index, frequency bands, coding rate etc., to accommodate the above characteristics. Implementing the above learning methods on the embedded chip leads to high latency, high power consumption and more chip area utilisation. To overcome the problems mentioned above, we present DEEP HOLE Radio systems, the intelligent system enabling the spectrum knowledge extraction from the unprocessed samples by the optimized deep learning models directly from the Radio Frequency (RF) environment. DEEP HOLE Radio provides (i) an optimized deep learning framework with a good trade-off between latency, power and utilization. (ii) Complete Hardware-Software architecture where the SoC’s coupled with radio transceivers for maximum performance. The experimentation has been carried out using GNURADIO software interfaced with Zynq-7000 devices mounting on ESP8266 radio transceivers with inbuilt Omni directional antennas. The whole spectrum of knowledge has been extracted using GNU radio. These extracted features are used to train the proposed optimized deep learning models, which run parallel on Zynq-SoC 7000, consuming less area, power, latency and less utilization area. The proposed framework has been evaluated and compared with the existing frameworks such as RFLearn, Long Term Short Memory (LSTM), Convolutional Neural Networks (CNN) and Deep Neural Networks (DNN). The outcome shows that the proposed framework has outperformed the existing framework regarding the area, power and time. Moreover, the experimental results show that the proposed framework decreases the delay, power and area by 15%, 20% 25% concerning the existing RFlearn and other hardware constraint frameworks.", "Keywords": "cognitive radio; GNU radio; Internet of things; optimized deep learning framework; RF learn; spectrum sharing", "DOI": "10.32604/iasc.2023.030291", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>ra College of Engineering, Salem, 636106, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>ra College of Engineering, Salem, 636106, India"}], "References": [{"Title": "Intelligent cognitive spectrum collaboration: Convergence of spectrum sensing, spectrum access, and coding technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "79", "JournalTitle": "Intelligent and Converged Networks"}]}, {"ArticleId": 105716631, "Title": "A Broker-Based Task-Scheduling Mechanism Using Replication Approach for Cloud Systems", "Abstract": "The reliability and availability of cloud systems have become major concerns of service providers, brokers, and end-users. Therefore, studying fault-tolerance mechanisms in cloud computing attracts intense attention in industry and academia. The task-scheduling mechanisms can improve the fault-tolerance level of cloud systems. A task-scheduling mechanism distributes tasks to a group of instances to be executed. Much work has been undertaken in this direction to improve the overall outcome of cloud computing, such as improving service quality and reducing power consumption. However, little work on task scheduling has studied the problem of lost tasks from the broker’s perspective. Task loss can happen due to virtual machine failures, server crashes, connection interruption, etc. The broker-based concept means that the backup task can be allocated by the broker on the same cloud service provider (CSP) or a different CSP to reduce costs, for example. This paper proposes a novel fault-tolerant mechanism that employs the primary backup (PB) model of task scheduling to address this issue. The proposed mechanism minimizes the impact of failure events by reducing the number of lost tasks. The mechanism is further improved to shorten the makespan time of submitted tasks in cloud systems. The experiments demonstrated that the proposed mechanism decreased the number of lost tasks by about 13%–15% compared with other mechanisms in the literature.", "Keywords": "brokerbased; Cloud computing; fault tolerance; replication; task scheduling", "DOI": "10.32604/iasc.2023.033703", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Prince <PERSON> University, AlKharj, Saudi Arabia"}], "References": []}, {"ArticleId": 105716644, "Title": "Sustainable Learning of Computer Programming Languages Using Mind Mapping", "Abstract": "In the current era of information technology, students need to learn modern programming languages efficiently. The art of teaching/learning programming requires many logical and conceptual skills. So it’s a challenging task for the instructors/learners to teach/learn these programming languages effectively and efficiently. Mind mapping is a useful visual tool for establishing ideas and connecting them to solve problems. This research proposed an effective way to teach programming languages through visual tools. This experimental study uses a mind mapping tool to teach two programming environments: Text-based Programming and Blocks-based Programming. We performed the experiments with one hundred and sixty undergraduate students of two public sector universities in the Asia Pacific region. Four different instructional approaches, including block-based language (BBL), text-based languages (TBL), mind map with text-based language (MMTBL) and mind mapping with block-based (MMBBL) are used for this purpose. The results show that instructional approaches using a mind mapping tool to help students solve given tasks in their critical thinking are more effective than other instructional techniques.", "Keywords": "blocks programming; novice programmer; Text programming", "DOI": "10.32604/iasc.2023.032494", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, National Textile University, Faisalabad, 37610, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, National Textile University, Faisalabad, 37610, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science, University of the Punjab, Lahore, 54590, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Sargodha, Sargodha, 40100, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing, The Islamia University of Bahawalpur, Bahawalpur, 63100, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Information Technology, Al Baha University, Al Baha, 1988, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Technology, Al Baha University, Al Baha, 1988, Saudi Arabia"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, 38541, South Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Science, Zagazig University, Zagazig, 44511, Egypt"}], "References": []}, {"ArticleId": 105716665, "Title": "Weighted cluster-level social emotion classification across domains", "Abstract": "<p>Social emotion classification is important for better capturing the preferences and perspectives of individual users to monitor public opinion and edit news. However, news reports have a strong domain dependence. Moreover, training data in the target domain are usually insufficient and only a small amount of training data may be labeled. To address these problems, we develop a cluster-level method for social emotion classification across domains. By discovering both source and target clusters and weighting the cluster in the source domain according to the similarity between its distribution and that of the target cluster, we can discover common patterns between the source and target domains, thus using both source and target data more effectively. Extensive experiments involving 12 cross-domain tasks conducted by using the ChinaNews dataset show that our model outperforms existing methods.</p>", "Keywords": "Emotion classification; Document clustering; Cross domain", "DOI": "10.1007/s13042-022-01769-3", "PubYear": 2023, "Volume": "14", "Issue": "7", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Science and Technology, Hong Kong Metropolitan University, Kowloon, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Information Technology, The Education University of Hong Kong, Tai Po, New Territories, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing and Decision Sciences, Lingnan University, New Territories, China"}], "References": [{"Title": "Sentiment strength detection with a context-dependent lexicon-based convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "389", "JournalTitle": "Information Sciences"}, {"Title": "User group based emotion detection and topic discovery over short text", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "1553", "JournalTitle": "World Wide Web"}, {"Title": "Information fusion for affective computing and sentiment analysis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "97", "JournalTitle": "Information Fusion"}, {"Title": "Word-level emotion distribution with two schemas for short text emotion classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107163", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A constrained optimization approach for cross-domain emotion distribution learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107160", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "EmoChannel-SA: exploring emotional dependency towards classification task with self-attention mechanism", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "6", "Page": "2049", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 105716679, "Title": "Optimal scale generation in two-class dominance decision tables with sequential three-way decision", "Abstract": "Optimal scale selection is studied widely at present to obtain scale rules in multi-scale decision tables. However, one limitation of this method is that it cannot directly extract scale rules from single-scale decision tables. In this study, we determine how to generate an optimal scale in two-class dominance decision tables with sequential three-way decision (S3WD). First, we use the variance of each attribute to describe its importance in a single-scale two-class dominance decision table. Then, we arrange all attributes in descending order in accordance with their importance. Second, we examine each attribute by following the aforementioned order. Thereafter, we construct different object granules on the basis of the numerical size of attribute value and different decision values and then expand them individually until their limit is reached. Third, we label and delete objects that are already in the object granules. Then, we continue to construct object granules for the remaining objects by following the preceding method until all the objects are labeled (if the information system is inconsistent, then it should be labeled with ”until it cannot be marked”). In the process above, we adopt the idea of S3WD and successively classify the objects into positive, boundary, and negative regions in accordance with the three states of objects (i.e., object classification has been confirmed, to be confirmed, and cannot be confirmed). We perform experiments on some UCI datasets and demonstrate the effectiveness of our method. In summary, our work provides a method for generating an optimal scale in single-scale decision tables and extracting scale rules from the generated tables.", "Keywords": "", "DOI": "10.1016/j.ins.2022.12.097", "PubYear": 2023, "Volume": "624", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Nanjing Audit University, Nanjing 211815, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Nanjing Audit University, Nanjing 211815, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Nanjing Audit University, Nanjing 211815, PR China"}], "References": [{"Title": "Inclusion measure-based multi-granulation decision-theoretic rough sets in multi-scale intuitionistic fuzzy information tables", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; Jinjiang Yan", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "421", "JournalTitle": "Information Sciences"}, {"Title": "A novel three-way decision method in a hybrid information system with images and its application in medical diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103651", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Object similarity measures and <PERSON><PERSON><PERSON>’s indiscernibility on decision tables", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "104", "JournalTitle": "Information Sciences"}, {"Title": "A new rough set model based on multi-scale covering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "243", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Optimal scale combination selection for multi-scale decision tables based on three-way decision", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "281", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Three-way decision with co-training for partially labeled data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "500", "JournalTitle": "Information Sciences"}, {"Title": "An investigation on Wu-Leung multi-scale information systems and multi-expert group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "114542", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Entropy based optimal scale combination selection for generalized multi-scale information tables", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "1427", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "On selection of optimal cuts in complete multi-scale decision tables", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "8", "Page": "6125", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 105716759, "Title": "Mitigation of Shift Rotation Effects on Workers’ Health and Sleep Quality in Manufacturing Companies in Latvia", "Abstract": "<p>This study aims to analyze the impact of rotating and night shifts on workers’ sleep quality and health. To conduct the study authors use data from foreign and Latvian studies on the effects of shift, night shift, and rotating shift work on workers’ sleep and health; survey data from four manufacturing companies in Latvia and work absence data from one of the surveyed companies. The results of the study indicate that rotating shifts and night work affect the quality of sleep of employees. In turn, poor sleep quality in the long term can result in serious health consequences for the employees involved in shift and night work. Rotating shifts and night work can be a contributing factor to other work environment risks as well as non-work-related health problems. Rotating shifts and night shifts can also be associated with more frequent work absences. Public institutions, employers, and employees need to be involved to reduce the negative impact. To mitigate the negative effects, the authors recommend changes in legislation, health-promoting measures on the part of employers, and responsible action from employees regard to preventive measures.</p>", "Keywords": "Health; Night shifts; Rotating shifts; Sleep quality", "DOI": "10.37394/23203.2022.17.63", "PubYear": 2022, "Volume": "17", "Issue": "", "JournalId": 73207, "JournalTitle": "WSEAS TRANSACTIONS ON SYSTEMS AND CONTROL", "ISSN": "1991-8763", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Occupational Safety and Civil Defence, Riga Technical University, Ķīpsalas iela 6a, Rīga, LV-1048, LATVIA"}, {"AuthorId": 2, "Name": "Guna Ba<PERSON>", "Affiliation": "Department of Occupational Safety and Civil Defence, Riga Technical University, Ķīpsalas iela 6a, Rīga, LV-1048, LATVIA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Occupational Safety and Civil Defence, Riga Technical University, Ķīpsalas iela 6a, Rīga, LV-1048, LATVIA"}], "References": []}, {"ArticleId": 105716792, "Title": "Adaptive Sliding-Mode Path-Following Control of Cart-Pendulum Robots with False Data Injection Attacks", "Abstract": "<p>This paper addresses the displacement path-following problem for a class of disturbed cart-pendulum systems under the fake data injection (FDI) actuator attacks. A filter operator is proposed to estimate the weight vector caused by unknown attacks and disturbances, so that the actuator attacks can be parameterized using neural networks. Then, combined with filter signals and based on adaptive neural network and integral sliding-mode techniques, robust path-following control schemes are proposed to withdraw the impacts of disturbances and FDI attacks. The uniformly ultimately bounded stability results of the closed-loop cart-pendulum system with neural network weight estimations and sliding functions are achieved based on Lyapunov stability theory. Finally, a simulation model of a material robot is used to verify the proposed control strategy.</p>", "Keywords": "cart-pendulum systems; adaptive neural networks; path-following control; FDI attacks; filter operators; sliding-mode control cart-pendulum systems ; adaptive neural networks ; path-following control ; FDI attacks ; filter operators ; sliding-mode control", "DOI": "10.3390/act12010024", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Qilu University of Technology (Shandong Academy of Sciences), Jinan 250353, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Jin", "Affiliation": "School of Computer Science and Technology, Qilu University of Technology (Shandong Academy of Sciences), Jinan 250353, China; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Advanced Technology, Nanjing University of Posts and Telecommunications, Nanjing 210042, China"}, {"AuthorId": 4, "Name": "Weiwei Che", "Affiliation": "Institute of Complexity Science, Qingdao University, Qingdao 266071, China"}], "References": [{"Title": "An improved neural network tracking control strategy for linear motor-driven inverted pendulum on a cart and experimental study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "5161", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 105716835, "Title": "Scheduling non-permutation flowshop with finite buffers and two competitive agents", "Abstract": "Flowshop scheduling is a popular and valuable optimisation model in academia. Application of the scheduling model for industrial problems has to consider real-world scenarios, such as non-permutation scheduling, finite buffers and individual competition from different customers. These non-negligibly factors can lead to a huge solution space, leaving it barely able to be effectively solved. Therefore, this study investigates a non-permutation bi-agent flowshop scheduling problem with limited buffers, where tasks are released over time and minimisation of a linear criterion ( i.e. weighted combination of makespans) improves customer satisfaction. Mixed integer programming model is established for this strong NP-hardness problem that the optimum cannot be found in polynomial time. For small-scale instances, a branch and bound (B&B) algorithm is provided to achieve exact solutions, where effective non-delay pruning rules, well-designed lower and upper bounds are utilised to eliminate invalid nodes during searching. For medium-scale instances, a hybrid particle swarm optimisation (HPSO) algorithm is proposed to seek high-quality solutions, where a two-stage collaborative evolution promotes deep exploration, an efficient sequence-based rule amends infeasible solutions and several validated mechanisms enhances convergence. Evaluation of extensive computational experiments, including components evaluation and nonparametric test, demonstrate the effectiveness of the presented algorithms. Introduction Manufacturing contributes considerably to national economy, and its development reveals the productivity level of a country. The moving assembly line developed by Ford Motor Company kicked off mass production which is the mark of modern manufacturing. The advantages of high efficiency and low cost make flowshop operation become a popular production mode in many key industries, such as equipment manufacturing, steel production, and port logistics. Since the pioneer article published by <PERSON> (1954), flowshop scheduling problems have been a popular topic in academia (Neufeld et al., 2016, Fernandez-Viagas et al., 2017, Tomazella and Nagano, 2020, Lv and Wang, 2021). For the convenience of study, most research hypothesises that ( i ) the buffer to store work-in-process is infinite between adjacent processors; ( ii ) the sequences in front of each processor operate in accordance with the first-come-first-served (FCFS) rule, which implies the order (or permutation) that tasks go through the first machine is maintained throughout the system. However, scheduling activities in many real-world factories is much more complicated: ( i ) the immediate storage is limited by product volume or storage capacity, where a task must remain blocked on the current processor when the buffer is stuffed. ( ii ) tasks sequence changes are tended to be allowed on different processors (non-permutation), which is significantly harder than that of permutation presetting when searching for an optimal schedule. For example, the business field of Fisher, a subsidiary of Emerson Process Management, includes the manufacturing of a large double flanged butterfly valve with DN1200-2000 ( i.e. the diameter is from 1200 mm to 2000 mm), a key part in offshore oil production platforms, metallurgical engineering and power plants. The process of the valve body can be briefly summarised as seven main procedures: casting blank, marking inspection, milling surface, welding flange sealing surface, rough and precision turning, flow detection and hydrostatic test, which follows an identical manufacturing route for different specifications, but processing sequences are allowed to be change in different procedures. Additionally, Fisher factories cannot provide infinite buffer to store work-in-processes because of the huge volume of the valve. Generally, Fisher has to supply for more than one demander (agent) with various parameters or sizes of valves (tasks), in which competition exists because they share manufacturing equipment (resource) while pursuing profits of their own. To gain maximum benefits, manufacturers strive to schedule orders from different demanders optimally for satisfying each customer, which cannot be modelled as a classical scheduling problem. Thus, a multi-agent scheduling (MAS) model is more suitable for the global optimisation problem, in which competitive agents share productive resource in common. In various industrial productions, buffer limit, non-permutation setting and customer satisfaction have been paid increasing attention by manufacturing enterprises. Nevertheless, to the best of the authors’ knowledge, no study has integrated these vital factors simultaneously, despite all of which have been shown to exist in many real applications. For in-depth research, a non-permutation bi-agent flowshop scheduling problem with finite buffers (NBFSP<sub>FB</sub>) is proposed, in which tasks belong to two competitive agents, and release dates are introduced to simulate the dynamic manufacturing circumstance. The optimal criterion is to capture a feasible schedule for minimisation of a weighted combination of makespans (WCM), where the weight indicates the priority of a client. The main contributions of this research are mainly in three aspects. ( i ) The bi-agent mode, limited intermediate storage environment and non-permutation scheduling setting are introduced into the flowshop model to describe the real-word manufacturing, where each task is released at a certain date for simulation of a dynamic production process. ( ii ) For small-scale instances, a B&B algorithm is provided to explore the optimal solution, where invalid nodes are effectively cut by non-delay pruning rules, a preemption-based lower bound (LB), and a heuristic-based initial upper bound (IUB). ( iii ) For medium-scale instances, an HPSO algorithm is proposed to obtain near-optimal solutions in reasonable time, in which a two-stage evolution contributes collaborative exploration; a sequence-based rule corrects infeasible solutions; several validated mechanisms avoid local optimum and accelerate algorithm convergence. The structure of the rest of this article is organised as follows. Section 2 provides a comprehensive review of the recent studies. Section 3 describes the proposed NBFSP<sub>FB</sub> and establishes mixed integer programming (MIP) model for obtaining an exact solution. A well-designed B&B algorithm and an effective HPSO metaheuristic are presented in 4 Branch and bound algorithm, 5 Hybrid particle swarm optimisation algorithm, respectively. Section 6 conducts extensive numerical experiments after calibration of parameters. Conclusions are drawn in Section 7. Section snippets Literature survey Given that few publications are found for the NBFSP<sub>FB</sub>, the following survey is undertaken for the flowshop scheduling problem with finite buffers (FSP<sub>FB</sub>), non-permutation flowshop scheduling, and MAS problems independently. The standard notation with three-field (Miyata and Nagano, 2019) is used to describe the scheduling problems formally for conciseness. Problem description and MIP model The studied NBFSP<sub>FB</sub> schedules a group of n tasks N = {1 <sup>x</sup> , 2 <sup>x</sup> , …, j<sup>x</sup> , …, n<sup>x</sup> } that belong to two agents (demanders) x = { a , b } on m processors M = { M <sub>1</sub>, M <sub>2</sub>, …, M<sub>m</sub> } in the same route. The processing of task j<sup>x</sup> (1 ≤ j ≤ n ) on processor M<sub>i</sub> (1 ≤ i ≤ m ) is denoted as operation O i , j x , which must be finished without preemption. Correspondingly, processing time p i , j x indicates the required execution duration of O i , j x , and release date r j x is the earliest available time when task j<sup>x</sup> can be processed on M <sub>1</sub> Branch and bound algorithm B&B is an algorithm framework of intelligent enumeration typically used to solve NP-hard problems, which seeks exact solutions for small-scale problems by systematically searching the candidate solutions in the search tree. However, given the huge search space of non-permutation schedules, B&B would cost unbearable running time for the exploration of optimal solutions. Therefore, this section designs an effective B&B algorithm to handle the simplified NBFSP<sub>FB</sub> with restriction of permutation and Hybrid particle swarm optimisation algorithm Based on the enumeration nature, business solvers consume exponentially computation time with the enlargement of problem scale. To achieve near-optimal schedule in a reasonable time, the HPSO algorithm is elaborated for problems in industrial scale. Particle swarm optimisation (PSO) is a population-based metaheuristic proposed by Kennedy and Eberhart (1995) on the basis of the simulation of birds, which has been successfully adopted in considerable literature to solve combinatorial optimisation Numerical verification A series of simulation numerical experiments are designed in this section to demonstrate the effectiveness of MIP models, as well as the advantages of the proposed B&B method and the HPSO algorithm. For all tested instances, the weight of agent b ( i.e. θ ), and the task amount of agent a ( i.e. n<sup>a</sup> ), are randomly determined. The procedures of the tested algorithms are coded by C++, and executed on a cloud server with an Intel(R) Xeon(R) Gold 6278C CPU (2.60 GHz) and 8 GB RAM. The specific Conclusions The presented NBFSP<sub>FB</sub> are investigated to optimise the WCM for two agents, in which each task has a release date for dynamic simulation, providing competitive scheduling frameworks for industries with limitation in intermediate storage and allowance of non-permutation. For a small number of tasks, the MIP models can directly obtain solutions by a commercial optimiser, but the running time is too long to find optimum due to the massive number of feasible solutions. A B&B framework is established Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This research is partly supported by the National Natural Science Foundation of China (grant number 61873173) and 111 Project of China (grant no. B20082). References (55) H. Zohali et al. The economic lot scheduling problem in limited-buffer flexible flow shops: Mathematical models and a discrete fruit fly algorithm Applied Soft Computing (2019) G. Zhang et al. Differential evolution metaheuristics for distributed limited-buffer flowshop scheduling with makespan criterion Computers & Operation Research (2019) S. Ye et al. Efficient heuristic for solving non-permutation flow-shop scheduling problems with maximal and minimal time lags Computers & Industrial Engineering (2017) J.-B. Wang et al. Minimizing makespan in three-machine flow shops with deteriorating jobs Computers & Operations Research (2013) C.P. Tomazella et al. A comprehensive review of Branch-and-Bound algorithms: Guidelines and directions for further research on the flowshop scheduling problem Expert Systems with Applications (2020) E. Taillard Benchmarks for basic scheduling problems European Journal of Operational Research (1993) Z. Shao et al. An efficient discrete invasive weed optimization for blocking flow-shop scheduling problem Engineering Applications of Artificial Intelligence (2019) Z. Shao et al. A novel multi-objective discrete water wave optimization for solving multi-objective blocking flow-shop scheduling problem Knowledge-Based Systems (2019) S.S. Panwalkar et al. On the dominance of permutation schedules for some ordered and proportionate flow shop problems Computers & Industrial Engineering (2017) Q.K. Pan et al. A chaotic harmony search algorithm for the flow shop scheduling problem with limited buffers Applied Soft Computing (2011) J.S. Neufeld et al. A comprehensive review of flowshop group scheduling literature Computers of the Operational Research (2016) H.H. Miyata et al. The blocking flow shop scheduling problem: A comprehensive and conceptual review Expert Systems with Applications (2019) J.Q. Li et al. Solving the large-scale hybrid flow shop scheduling problem with limited buffers by a hybrid artificial bee colony algorithm Information Sciences (2015) V. Fernandez-Viagas et al. A new vision of approximate methods for the permutation flowshop to minimise makespan: State-of-the-art and computational evaluation European Journal of Operational Research (2017) A.J. Benavides et al. Fast heuristics for minimizing the makespan in non-permutation flow shops Computers & Operations Research (2018) A.J. Benavides et al. Two simple and effective heuristics for minimizing the makespan in non-permutation flow shops Computers & Operations Research (2016) D.Y. Bai et al. Permutation flow shop scheduling problem to minimize nonlinear objective function with release dates Computers & Industrial Engineering (2017) I. Abu Doush et al. Flow shop scheduling with blocking using modified harmony search algorithm with neighboring heuristics methods Applied Soft Computing (2019) A. Agnetis et al. Multiagent scheduling: Models and algorithms (2014) A. Agnetis et al. Scheduling problems with two competing agents Operations Research (2004) S. Aminzadegan et al. Multi-agent supply chain scheduling problem by considering resource allocation and transportation Computers & Industrial Engineering (2019) S. Aqil et al. On a bi-criteria flow shop scheduling problem under constraints of blocking and sequence dependent setup time Annals of Operations Research (2021) D.Y. Bai et al. Competitive bi-agent flowshop scheduling to minimise the weighted combination of makespans International Journal of Production Research. (2021) K.R. Baker et al. A multiple-criterion model for machine scheduling Journal of Scheduling (2003) P. Cha et al. Nash equilibrium solutions in multi-agent project scheduling with milestones European Journal of Operational Research (2021) R.X. Chen et al. Multi-agent scheduling in a no-wait flow shop system to maximize the weighted number of just-in-time jobs Engineering Optimization (2018) S. Chen et al. Production scheduling for blocking flowshop in distributed environment using effective heuristics and iterated greedy algorithm Robotics and Computer-Integrated Manufacturing (2021) View more references Cited by (0) Recommended articles (6) Research article New Benchmark Algorithm for Minimizing Total Completion Time in blocking flowshops with sequence-dependent setup times Applied Soft Computing, Volume 104, 2021, Article 107229 Show abstract Just-in-time production in large enterprises along with the factory’s limited space highlights the need for scheduling tools that consider blocking conditions. This study contributes to the scheduling literature by developing an effective metaheuristic to address the Blocking Flowshop Scheduling Problems with Sequence-Dependent Setup-Times (BFSP with SDSTs). Including a new constructive heuristic and a local search mechanism customized for the blocking and setup time features, the Extended Iterated Greedy (EIG) algorithm effectively solves this highly intractable scheduling extension. The performance of the EIG algorithm is compared with that of the best-performing algorithms in the literature developed to solve the BFSP with SDSTs. Extensive numerical tests and statistical analyses verify EIG’s superiority over the benchmark algorithms and show that EIG performs steadily over various operational situations. Applications of the improved algorithm in this study are worthwhile topics to solve other complex scheduling problems. Research article Tabu search exploiting local optimality in binary optimization European Journal of Operational Research, 2023 Show abstract A variety of strategies have been proposed for overcoming local optimality in metaheuristic search. This paper examines characteristics of moves that can be exploited to make good decisions about steps that lead away from a local optimum and then lead toward a new local optimum. We introduce strategies to identify and take advantage of useful features of solution history with an adaptive memory metaheuristic, to provide rules for selecting moves that offer promise for discovering improved local optima. Our approach uses a new type of adaptive memory based on a construction called exponential extrapolation. The memory operates by means of threshold inequalities that ensure selected moves will not lead to a specified number of most recently encountered local optima. Associated thresholds are embodied in choice rule strategies that further exploit the exponential extrapolation concept and open a variety of research possibilities for exploration. The considerations treated in this study are illustrated in an implementation to solve the Quadratic Unconstrained Binary Optimization (QUBO) problem. We show that the AA algorithm obtains an average objective gap of 0.0315% to the best known values for the 21 largest Palubeckis instances. This solution quality is considered to be quite attractive because less than 20 s on average are taken by AA, which is 1 to 2 orders of magnitude less than the time required by most algorithms reporting the best known results. Research article A large neighborhood search algorithm and lower bounds for the variable-Sized bin packing problem with conflicts European Journal of Operational Research, 2023 Show abstract In this paper, we study the Variable-Sized Bin Packing Problem with Conflicts (VSBPPC). In VSBPPC, a set of items each with a certain size has to be packed into bins of various types. Bin types differ in terms of their capacity and cost, and certain pairs of items cannot be packed into the same bin due to conflicts. The goal is to pack the items into the bins such that the total cost of the used bins is minimized. VSBPPC generalizes both the Variable-Sized Bin Packing Problem (VSBPP) and Bin Packing Problem with Conflicts (BPPC). We propose new lower bounds and develop a large neighborhood search algorithm for the problem. In the proposed solution approach, we destroy the solution by unpacking some of the bins and then repair the solution by a greedy method considering the unit cost of packing each item followed by a local search procedure. In the local search phase, we improve the repaired solution by (i) transferring items from its current bin to another bin, and (ii) swapping the items between bins. We evaluate the performance of the proposed solution approach not only against a lower bound but also against the benchmark algorithms from the literature. The proposed solution approach outperforms the benchmark algorithms with at least a margin of 4.39% on average. Moreover, the solutions obtained by the proposed approach have an average optimality gap of 2.77% with respect to the lower bound. Research article Exact and heuristic methods for a workload allocation problem with chain precedence constraints European Journal of Operational Research, 2022 Show abstract Industrial manufacturing is often organized in assembly lines where a product is assembled on a sequence of stations, each of which executes some of the assembly tasks. A line is balanced if the maximum total execution time of any station is minimal. Commonly, the task execution order is constrained by precedences, and task execution times are independent of the station performing the task. Here, we consider a recent variation, called the “(Calzedonia) Workload Allocation Problem” (WAP), where the precedences form a chain, and the execution time of a task depends on the worker executing it. This problem was recently proposed by Battarra et al. (2020) and it is a special case of the Assembly Line Worker Assignment and Balancing Problem Miralles et al. (2007) where precedence relations are arbitrary. In this paper we consider the computational complexity of the problem and prove its NP-hardness. To solve the problem, we provide different lower bounds and exact and heuristic procedures. The performance of the proposed methods is tested on previously proposed instances and on new, larger instances with the same characteristics. The results show that the proposed methods can solve instances with up to about 4000 tasks and 29 workers, doubling the size of the instances that previously could be solved to optimality. Research article Solving multistage stochastic linear programming via regularized linear decision rules: An application to hydrothermal dispatch planning European Journal of Operational Research, 2022 Show abstract The solution of multistage stochastic linear problems (MSLPs) represents a challenge for many applications. Long-term hydrothermal dispatch planning (LHDP) materializes this challenge in a real-world problem that affects electricity markets, economies, and natural resources worldwide. No closed-form solutions are available for MSLP and the definition of non-anticipative policies with high-quality out-of-sample performance is crucial. Linear decision rules (LDRs) provide an interesting simulation-based framework for finding high-quality policies for MSLP through two-stage stochastic models. In practical applications, however, the number of parameters to be estimated when using an LDR may be close to or higher than the number of scenarios of the sample average approximation problem, thereby generating an in-sample overfit and poor performances in out-of-sample simulations. In this paper, we propose a novel regularized LDR to solve MSLP based on the AdaLASSO (adaptive least absolute shrinkage and selection operator). The goal is to use the parsimony principle, as largely studied in high-dimensional linear regression models, to obtain better out-of-sample performance for a LDR applied to MSLP. Computational experiments show that overfitting is a non-negligible threat when using the classical non-regularized LDR to solve the LHDP, one of the most studied MSLP with relevant applications in industry. Our analysis highlights the following benefits of the proposed framework in comparison to the non-regularized benchmark: 1) significant reductions in the number of non-zero coefficients (model parsimony), 2) substantial cost reductions in out-of-sample evaluations, and 3) improved spot-price profiles. Research article A state-of-the-art survey on multi-scenario scheduling European Journal of Operational Research, 2022 Show abstract In classical deterministic scheduling models, it is common to assume that each of the parameters has only a single predefined value. However, in many applications this is not the case, and there is a certain level of uncertainty in the values of the parameters. The multi-scenario approach is one of the most common approaches to model uncertainty. In this approach, each scenario defines a different possible realization of the parameters and the scheduler has to decide upon the exact schedule prior to knowing the exact scenario realization. Different scenarios may arise for a large number of reasons, such as: ( i ) the assignment of different workers to the processing operations, where the identity of the worker is beyond the scheduler’s control; ( i i ) unpredictable changes in weather conditions; or ( i i i ) a possible strike at the port, which may affect job release dates. The purpose of this paper is to offer a unified framework for scheduling in a multi-scenario environment by presenting an up-to-date survey of results in this field. We also included some new results, presented here for the first time, and suggest many challenges for future research. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108939", "PubYear": 2023, "Volume": "177", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Danyu Bai", "Affiliation": "School of Maritime Economics & Management, Dalian Maritime University, Dalian 116026, China;Collaborative Innovation Center for Transport Studies, Dalian Maritime University, Dalian 116026, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Maritime Economics & Management, Dalian Maritime University, Dalian 116026, China;Collaborative Innovation Center for Transport Studies, Dalian Maritime University, Dalian 116026, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Maritime Economics & Management, Dalian Maritime University, Dalian 116026, China"}, {"AuthorId": 4, "Name": "Tao Ren", "Affiliation": "Software College, Northeastern University, Shenyang 110819, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Tsinghua University, Beijing 100084, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>qiang Dong", "Affiliation": "Software College, Northeastern University, Shenyang 110819, China"}], "References": [{"Title": "A comprehensive review of Branch-and-Bound algorithms: Guidelines and directions for further research on the flowshop scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "158", "Issue": "", "Page": "113556", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-temperature simulated annealing for optimizing mixed-blocking permutation flowshop scheduling problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113837", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A discrete whale swarm algorithm for hybrid flow-shop scheduling problem with limited buffers", "Authors": "Chun<PERSON> Zhang; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "102081", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Effective constructive heuristics and discrete bee colony optimization for distributed flowshop with setup times", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "104016", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Production scheduling for blocking flowshop in distributed environment using effective heuristics and iterated greedy algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102155", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Scheduling with competing agents, total late work and job rejection", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "133", "Issue": "", "Page": "105329", "JournalTitle": "Computers & Operations Research"}, {"Title": "Optimization for integrated scheduling of intelligent handling equipment with bidirectional flows and limited buffers at automated container terminals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "145", "Issue": "", "Page": "105863", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 105716843, "Title": "Vehicular Networks Dynamic Grouping and Re-Orchestration Scenarios", "Abstract": "<p>The topological structure in vehicular communication networks presents challenges for sustaining network connectivity on the road. Highway dynamics, for example, encourage the need for an adaptive and flexible structure to handle the rapid events of vehicles joining and leaving the road. Such demand aligns with the advancement made in software-defined networks and related dynamic network re-orchestration. This paper discusses the development of a virtual model that represents the operation of an autonomous vehicular network. It also investigates the ability to re-orchestrate the topology through software definition while running the various operational phases. Network self-formation, network expansion, retraction via vehicular members joining and leaving, and network self-healing when a topological rupture occurs as a result of a key member leaving the network are the key grouping phases. The communication approach is analyzed based on the status of network members and their ability to assume the various network roles. The concept is tested using both a Contiki–Cooja network simulator and a MATLAB analytical modeling tool to reflect the operation and performance of the grouping approach under various road scenarios. The outcome of the analysis reflects the ability of the group to be formulated within a measured latency considering the various network parameters such as communication message rate. The approach offers tools for managing the dynamic connectivity of vehicular groups and may also be extended to assume the function of an on-road network digital twin during the lifetime of a given group.</p>", "Keywords": "", "DOI": "10.3390/info14010032", "PubYear": 2023, "Volume": "14", "Issue": "1", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Computer and Mathematical Sciences, Auckland University of Technology (AUT), Auckland 1010, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Computer and Mathematical Sciences, Auckland University of Technology (AUT), Auckland 1010, New Zealand; Corresponding author"}], "References": [{"Title": "T-Coin: Dynamic Traffic Congestion Pricing System for the Internet of Vehicles in Smart Cities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "149", "JournalTitle": "Information"}, {"Title": "Street-centric routing scheme using ant colony optimization-based clustering for bus-based vehicular ad-hoc network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106736", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Clustering in vehicular ad hoc network: Algorithms and challenges", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106851", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": *********, "Title": "Identifying Influential Communities Using IID for a Multilayer Networks", "Abstract": "In online social networks (OSN), they generate several specific user activities daily, corresponding to the billions of data points shared. However, although users exhibit significant interest in social media, they are uninterested in the content, discussions, or opinions available on certain sites. Therefore, this study aims to identify influential communities and understand user behavior across networks in the information diffusion process. Social media platforms, such as Facebook and Twitter, extract data to analyze the information diffusion process, based on which they cascade information among the individuals in the network. Therefore, this study proposes an influential information diffusion model that identifies influential communities across these two social media sites. Moreover, it addresses site migration by visualizing a set of overlapping communities using hyper-edge detection. Thus, the overlapping community structure is used to identify similar communities with identical user interests. Furthermore, the community structure helps in determining the node activation and user influence from the information cascade model. Finally, the Fraction of Intra/Inter-Layer (FIL) diffusion score is used to evaluate the efficiency of the influential information diffusion model by analyzing the trending influential communities in a multilayer network. However, from the experimental result, it observes that the FIL diffusion score for the proposed method achieves better results in terms of accuracy, precision, recall and efficiency of community detection than the existing methods.", "Keywords": "community detection; influential communities; Influential information diffusion model; social network", "DOI": "10.32604/iasc.2023.034019", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, College of Engineering, Guindy, Anna University, Tamilnadu, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, College of Engineering, Guindy, Anna University, Tamilnadu, Chennai, India"}], "References": [{"Title": "A two-hop neighbor preference-based random network graph model with high clustering coefficient for modeling real-world complex networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "3", "Page": "389", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "The individual dynamics of affective expression on social media", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "EPJ Data Science"}, {"Title": "Community Detection in Complex Networks Using Nonnegative Matrix Factorization and Density-Based Clustering Algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "2", "Page": "1731", "JournalTitle": "Neural Processing Letters"}, {"Title": "Measuring the effect of node aggregation on community detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "EPJ Data Science"}, {"Title": "A Dynamic Programming Framework for Large-Scale Online Clustering on Graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "2", "Page": "1613", "JournalTitle": "Neural Processing Letters"}, {"Title": "Estimating tie strength in social networks using temporal communication data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "EPJ Data Science"}, {"Title": "Employing Lexicalized Dependency Paths for Active Learning of Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "1415", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105717021, "Title": "Algorithm Sensemaking: How Platform Workers Make Sense of Algorithmic Management", "Abstract": "Algorithmic management can create work environment tensions that are detrimental to workplace well-being and productivity. One specific type of tension originates from the fact that algorithms often exhibit limited transparency and are perceived as highly opaque, which impedes workers’ understanding of their inner workings. While algorithmic transparency can facilitate sensemaking, the algorithm’s opaqueness may aggravate sensemaking. By conducting an empirical case study in the context of the Uber platform, we explore how platform workers make sense of the algorithms managing them. Drawing on <PERSON><PERSON>’s enactment theory, we theorize a new form of sensemaking— algorithm sensemaking—and unpack its three sub-elements: (1) focused enactment, (2) selection modes, and (3) retention sources. The sophisticated, multistep process of algorithm sensemaking allows platform workers to keep up with algorithmic instructions systematically. We add to previous literature by theorizing algorithm sensemaking as a mediator linking workers’ perceptions about tensions in their work environment and their behavioral responses. © 2023 by the Association for Information Systems.", "Keywords": "Algorithmic Management; Algorithmic Opacity; Algorithmic Transparency; Sensemaking", "DOI": "10.17705/1jais.00774", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 24874, "JournalTitle": "Journal of the Association for Information Systems", "ISSN": "", "EISSN": "1536-9323", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bentley University"}, {"AuthorId": 2, "Name": "Carolina Alves de Lima Salge", "Affiliation": "University of Georgia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bentley University, United States"}], "References": []}, {"ArticleId": 105717053, "Title": "Research on Stress Reduction Model Based on Transformer", "Abstract": "People are constantly exposed to stress and anxiety environment, which could contribute to a variety of psychological and physical health problems. Therefore, it is particularly important to identify psychological stress in time and to find a feasible and universal method of stress reduction. This research investigated the influence of different music, such as relaxation music and natural rhythm music, on stress relief based on Electroencephalogram signals. Mental arithmetic test was implemented to create a stressful environment. 23 participants performed the mental arithmetic test with and without music respectively, while their Electroencephalogram signal was recorded. The effect of music on stress relief was verified through stress test questionnaires, including Trait Anxiety Inventory (STAI-6) and Self-Stress Assessment. There was a significant change in the stress test questionnaire values with and without music according to paired t-test (p<0.01). Furthermore, a model based on Transformer for stress level classification from Electroencephalogram signal was proposed. Experimental results showed that the method of listening to relaxation music and natural rhythm music achieved the effect of reducing psychological stress and the proposed model yielded a promising accuracy in classifying the Electroencephalogram signal of mental stress. Copyright © 2022 KSII.", "Keywords": "EEG; mental stress; music; self-attention; Transformer", "DOI": "10.3837/tiis.2022.12.009", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105717088, "Title": "Application of maximum rank distance codes in designing of STBC-OFDM system for next-generation wireless communications", "Abstract": "Space-Time Block Coded (STBC) Orthogonal Frequency Division Multiplexing (OFDM) satisfies higher data-rate requirements while maintaining signal quality in a multipath fading channel. However, conventional STBCs, including Orthogonal STBCs (OSTBCs), Non-Orthogonal (NOSTBCs), and Quasi-Orthogonal STBCs (QOSTBCs), do not provide both maximal diversity order and unity code rate simultaneously for more than two transmit antennas. This paper targets this problem and applies Maximum Rank Distance (MRD) codes in designing STBC-OFDM systems. By following the direct-matrix construction method, we can construct binary extended finite field MRD-STBCs for any number of transmitting antennas. Work uses MRD-STBCs built over Phase-Shift Keying (PSK) modulation to develop an MRD-based STBC-OFDM system. The MRD-based STBC-OFDM system sacrifices minor error performance compared to traditional OSTBC-OFDM but shows improved results against NOSTBC and QOSTBC-OFDM. It also provides 25% higher data-rates than OSTBC-OFDM in configurations that use more than two transmit antennas. The tradeoffs are minor increases in computational complexity and processing delays.", "Keywords": "Bit error rate (BER); G<PERSON><PERSON> field; Maximum rank distance (MRD) codes; Orthogonal frequency division multiplexing (OFDM); Primitive polynomials; Space-time block codes (STBC)", "DOI": "10.1016/j.dcan.2022.12.022", "PubYear": 2024, "Volume": "10", "Issue": "4", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Computer and Communication Technology, Sirindhorn International Institute of Technology, Thammasat University, Pathumthani, 12000, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Computer and Communication Technology, Sirindhorn International Institute of Technology, Thammasat University, Pathumthani, 12000, Thailand;Corresponding author"}], "References": [{"Title": "Analysis of space time block codes and cosets for 5G and its applications", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "189", "JournalTitle": "Computer Communications"}, {"Title": "Service-aware 6G: An intelligent and open network based on the convergence of communication, computing and caching", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "3", "Page": "253", "JournalTitle": "Digital Communications and Networks"}]}, {"ArticleId": 105717100, "Title": "QM/MM study of N501 involved intermolecular interaction between SARS-CoV-2 receptor binding domain and antibody of human origin", "Abstract": "Intermolecular interaction between key residue N501 of the epitope on SARS-CoV-2 RBD and screening antibody B38 was studied using the QM/MM and QM approach. The QM/MM optimized geometry shows that angle X-H---Y is 165° for O-H---O between mAb light chain S30 and RBD N501. High level MP2 calculations indicated the interaction between RBD N501 and S30 of B38 Fab light chain provide a relatively strong attractive force of − 3.32 kcal/mol, whereas the hydrogen bond between RBD Q498 and S30 was quantified as 0.10 kcal/mol. The decrease in ESP partial charge on hydrogen atom of hydroxyl group on S30 drops from 0.38 a.u. to 0.31 a.u., exhibiting the sharing of 0.07 a.u. from the lone pair electron oxygen of N501 due to hydrogen bond formation. The NBO occupancy of hydrogen atom also decreases from 25.79 % to 22.93 % in the hydroxyl H-O NBO bond of S30. However, the minor change of NBO hybridization of hydroxyl oxygen of S30 from sp3.00 to sp3.05 implies the rigidity of hydrogen bond tetrahedral geometry in the relative dynamic protein complex. The O-H---O angle is 165° which is close but not exactly linear. The structural requirement for sp3 hybridization of oxygen for hydroxyl group on S30 and dimension of protein likely prevent O-H---O from adopting linear geometry. The hydrogen bond strengths were also calculated using a variety of DFT methods, and the result of − 3.33 kcal/mol from the M06L method is the closest to that of the MP2 calculation. Results of this work may aid in the COVID-19 vaccine and drug screening.", "Keywords": "Asparagine 501;Intermolecular interaction;Monoclonal antibody;QM/MM;Receptor binding domain;Spike glycoprotein", "DOI": "10.1016/j.compbiolchem.2023.107810", "PubYear": 2023, "Volume": "102", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, Prairie View A&M University, Prairie View, TX 77446, the United States of America; Department of Chemistry, Rice University, Houston, TX 77005, the United States of America. Electronic address:  ."}, {"AuthorId": 2, "Name": "Hana F<PERSON>", "Affiliation": "Department of Chemistry, Prairie View A&M University, Prairie View, TX 77446, the United States of America."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, Rice University, Houston, TX 77005, the United States of America."}, {"AuthorId": 4, "Name": "Rulong Ma", "Affiliation": "Department of Biology and Biochemistry, University of Houston, Houston, TX 77004, the United States of America."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, Prairie View A&M University, Prairie View, TX 77446, the United States of America."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, Prairie View A&M University, Prairie View, TX 77446, the United States of America."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, Prairie View A&M University, Prairie View, TX 77446, the United States of America."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, Prairie View A&M University, Prairie View, TX 77446, the United States of America."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemical Engineering, Sichuan University Science and Engineering, Zigong, Sichuan 643000, PR China."}, {"AuthorId": 10, "Name": "Heaven N<PERSON> Ingram", "Affiliation": "Department of Chemistry, Prairie View A&M University, Prairie View, TX 77446, the United States of America."}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Biology and Biochemistry, University of Houston, Houston, TX 77004, the United States of America."}], "References": []}, {"ArticleId": 105717122, "Title": "SNCDM: Spinal Tumor Detection from MRI Images Using Optimized Super-Pixel Segmentation", "Abstract": "Conferring to the American Association of Neurological Surgeons (AANS) survey, 85% to 99% of people are affected by spinal cord tumors. The symptoms are varied depending on the tumor’s location and size. Up-to-the-minute, back pain is one of the essential symptoms, but it does not have a specific symptom to recognize at the earlier stage. Numerous significant research studies have been conducted to improve spine tumor recognition accuracy. Nevertheless, the traditional systems are consuming high time to extract the specific region and features. Improper identification of the tumor region affects the predictive tumor rate and causes the maximum error-classification problem. Consequently, in this work, Super-pixel analytics Numerical Characteristics Disintegration Model (SNCDM) is used to segment the tumor affected region. Estimating the super-pixels of the affected region by this method reduces the variance between the identified pixels. Further, the super-pixels are selected according to the optimized convolution network that effectively extracts the vertebral super-pixels features. Derived super-pixels improve the network learning and training process, which minimizes the maximum error classification problem also the efficiency of the system was evaluated using experimental results and analysis.", "Keywords": "Maximum error-classification problem; optimized convolution network; super-pixel analytics numerical characteristics disintegration model (SNCDM)", "DOI": "10.32604/iasc.2023.031202", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Engineering, Saveetha Engineering College, Tamilnadu, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Engineering, Saveetha Engineering College, Tamilnadu, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Tagore Engineering College, Tamilnadu, Chennai, India"}], "References": [{"Title": "Electrocardiogram soft computing using hybrid deep learning CNN-ELM", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105778", "JournalTitle": "Applied Soft Computing"}, {"Title": "Parameters Compressing in Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "1", "Page": "321", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Using CFW-Net Deep Learning Models for X-Ray Images to Detect COVID-19 Patients", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "199", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Small Object Detection via Precise Region-Based Fully Convolutional Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "2", "Page": "1503", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Deformation Expression of Soft Tissue Based on BP Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "1041", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105717133, "Title": "Drone for Dynamic Monitoring and Tracking with Intelligent Image Analysis", "Abstract": "Traditional monitoring systems that are used in shopping malls or community management, mostly use a remote control to monitor and track specific objects; therefore, it is often impossible to effectively monitor the entire environment. When finding a suspicious person, the tracked object cannot be locked in time for tracking. This research replaces the traditional fixed-point monitor with the intelligent drone and combines the image processing technology and automatic judgment for the movements of the monitored person. This intelligent system can effectively improve the shortcomings of low efficiency and high cost of the traditional monitor system. In this article, we proposed a TIMT (The Intelligent Monitoring and Tracking) algorithm which can make the drone have smart surveillance and tracking capabilities. It combined with Artificial Intelligent (AI) face recognition technology and the OpenPose which is able to monitor the physical movements of multiple people in real time to analyze the meaning of human body movements and to track the monitored intelligently through the remote control interface of the drone. This system is highly agile and could be adjusted immediately to any angle and screen that we monitor. Therefore, the system could find abnormal conditions immediately and track and monitor them automatically. That is the system can immediately detect when someone invades the home or community, and the drone can automatically track the intruder to achieve that the two significant shortcomings of the traditional monitor will be improved. Experimental results show that the intelligent monitoring and tracking drone system has an excellent performance, which not only dramatically reduces the number of monitors and the required equipment but also achieves perfect monitoring and tracking.", "Keywords": "deep learning; Drone; equidistant track; face detection; human pose intention; remote monitoring", "DOI": "10.32604/iasc.2023.034488", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chinese Culture University, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Soochow University, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Chinese Culture University, Taipei, Taiwan"}], "References": [{"Title": "ModPSO-CNN: an evolutionary convolution neural network with application to visual recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "2165", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 105717140, "Title": "Deep Learning for Depression Detection Using Twitter Data", "Abstract": "Today social media became a communication line among people to share their happiness, sadness, and anger with their end-users. It is necessary to know people’s emotions are very important to identify depressed people from their messages. Early depression detection helps to save people’s lives and other dangerous mental diseases. There are many intelligent algorithms for predicting depression with high accuracy, but they lack the definition of such cases. Several machine learning methods help to identify depressed people. But the accuracy of existing methods was not satisfactory. To overcome this issue, the deep learning method is used in the proposed method for depression detection. In this paper, a novel Deep Learning Multi-Aspect Depression Detection with Hierarchical Attention Network (MDHAN) is used for classifying the depression data. Initially, the Twitter data was preprocessed by tokenization, punctuation mark removal, stop word removal, stemming, and lemmatization. The Adaptive Particle and grey Wolf optimization methods are used for feature selection. The MDHAN classifies the Twitter data and predicts the depressed and non-depressed users. Finally, the proposed method is compared with existing methods such as Convolutional Neural Network (CNN), Support Vector Machine (SVM), Minimum Description Length (MDL), and MDHAN. The suggested MDH-PWO architecture gains 99.86% accuracy, more significant than frequency-based deep learning models, with a lower false-positive rate. The experimental result shows that the proposed method achieves better accuracy, precision, recall, and F1-measure. It also minimizes the execution time.", "Keywords": "deep learning; Depression detection; multi-aspect depression detection; prediction; swarm intelligence; tweets; twitter data", "DOI": "10.32604/iasc.2023.033360", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh, 11671, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational Intelligence, SRM Institute of Science and Technology, Kattankulathur, Chennai, 603203, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, M<PERSON> of Engineering, Karur, 639113, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Mansoura University, Mansoura, 35516, Egypt; Department of Computational Mathematics, Science, and Engineering (CMSE), College of Engineering, Michigan State University, East Lansing, MI  48824, United States"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh, 11671, Saudi Arabia"}], "References": [{"Title": "Multimodal depression detection on instagram considering time interval of posts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "56", "Issue": "1", "Page": "25", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "A collaborative healthcare framework for shared healthcare plan with ambient intelligence", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Human-centric Computing and Information Sciences"}, {"Title": "Cross corpus multi-lingual speech emotion recognition using ensemble learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "4", "Page": "1845", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A hierarchical depression detection model based on vocal and emotional cues", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "441", "Issue": "", "Page": "279", "JournalTitle": "Neurocomputing"}, {"Title": "A hybrid approach for stock trend prediction based on tweets embedding and historical prices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "3", "Page": "849", "JournalTitle": "World Wide Web"}, {"Title": "Deep learning for prediction of depressive symptoms in a large textual dataset", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "721", "JournalTitle": "Neural Computing and Applications"}, {"Title": "User Centric Social Opinion and Clinical Behavioural Model for Depression Detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Famutimi; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "4", "Page": "69", "JournalTitle": "International Journal of Intelligent Information Systems"}]}, {"ArticleId": 105717164, "Title": "Moth Flame Optimization Based FCNN for Prediction of Bugs in Software", "Abstract": "The software engineering technique makes it possible to create high-quality software. One of the most significant qualities of good software is that it is devoid of bugs. One of the most time-consuming and costly software procedures is finding and fixing bugs. Although it is impossible to eradicate all bugs, it is feasible to reduce the number of bugs and their negative effects. To broaden the scope of bug prediction techniques and increase software quality, numerous causes of software problems must be identified, and successful bug prediction models must be implemented. This study employs a hybrid of Faster Convolution Neural Network and the Moth Flame Optimization (MFO) algorithm to forecast the number of bugs in software based on the program data itself, such as the line quantity in codes, methods characteristics, and other essential software aspects. Here, the MFO method is used to train the neural network to identify optimal weights. The proposed MFO-FCNN technique is compared with existing methods such as AdaBoost (AB), Random Forest (RF), K-Nearest Neighbour (KNN), K-Means Clustering (KMC), Support Vector Machine (SVM) and Bagging Classifier (BC) are examples of machine learning (ML) techniques. The assessment method revealed that machine learning techniques may be employed successfully and through a high level of accuracy. The obtained data revealed that the proposed strategy outperforms the traditional approach.", "Keywords": "AdaBoost (AB); Faster convolution neural network; Moth Flame Optimization (MFO); software bug prediction; Support Vector Machine (SVM)", "DOI": "10.32604/iasc.2023.029678", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Noorul Islam Center for Higher Studies, Tamil Nadu629180, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of CSE, Noorul Islam Center for Higher Studies, Tamil Nadu629180, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Noorul Islam Center for Higher Studies, Tamil Nadu629180, India"}], "References": [{"Title": "Machine Learning Techniques for Software Bug Prediction: A Systematic Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "11", "Page": "1558", "JournalTitle": "Journal of Computer Science"}, {"Title": "RETRACTED ARTICLE: Developing an energy-efficient ubiquitous agriculture mobile sensor network-based threshold built-in MAC routing protocol (TBMP)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "18", "Page": "12333", "JournalTitle": "Soft Computing"}]}, {"ArticleId": *********, "Title": "The role of pulse timing in cardiac defibrillation", "Abstract": "<p>Life-threatening cardiac arrhythmias require immediate defibrillation. For state-of-the-art shock treatments, a high field strength is required to achieve a sufficient success rate for terminating the complex spiral wave (rotor) dynamics underlying cardiac fibrillation. However, such high energy shocks have many adverse side effects due to the large electric currents applied. In this study, we show, using 2D simulations based on the Fenton-Karma model, that also pulses of relatively low energy may terminate the chaotic activity if applied at the right moment in time. In our simplified model for defibrillation, complex spiral waves are terminated by local perturbations corresponding to conductance heterogeneities acting as virtual electrodes in the presence of an external electric field. We demonstrate that time series of the success rate for low energy shocks exhibit pronounced peaks which correspond to short intervals in time during which perturbations aiming at terminating the chaotic fibrillation state are (much) more successful. Thus, the low energy shock regime, although yielding very low temporal average success rates, exhibits moments in time for which success rates are significantly higher than the average value shown in dose-response curves. This feature might be exploited in future defibrillation protocols for achieving high termination success rates with low or medium pulse energies.</p>", "Keywords": "cardiac arrhythmias; defibrillation; Electrophysiology; Biophysical methods; Dose response curve; spiral waves; Spatiotemporal chaos; Nonlinear Dynamics", "DOI": "10.3389/fnetp.2022.1007585", "PubYear": 2023, "Volume": "2", "Issue": "", "JournalId": 89151, "JournalTitle": "Frontiers in Network Physiology", "ISSN": "", "EISSN": "2674-0109", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Max Planck Institute for Dynamics and Self-Organization, Germany; nstitute for the Dynamics of Complex Systems, Germany; Institute of Biomedical Engineering, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Max Planck Institute for Dynamics and Self-Organization, Germany; Faculty for Applied Mathematics, Physics, and General Science, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Max Planck Institute for Dynamics and Self-Organization, Germany; nstitute for the Dynamics of Complex Systems, Germany; German Center for Cardiovascular Research (DZHK), Germany; Institute of Pharmacology and Toxicology, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Max Planck Institute for Dynamics and Self-Organization, Germany; nstitute for the Dynamics of Complex Systems, Germany; German Center for Cardiovascular Research (DZHK), Germany"}], "References": []}, {"ArticleId": 105717248, "Title": "FUZZ<PERSON> SYSTEM FOR CLUTCH ENGAGEMENT AND VIBRATION CONTROL IN PARALLEL HYBRID ELECTRIC VEHICLE", "Abstract": "", "Keywords": "", "DOI": "10.2316/J.2022.201-0299", "PubYear": 2022, "Volume": "50", "Issue": "", "JournalId": 55091, "JournalTitle": "Mechatronic Systems and Control (formerly Control and Intelligent Systems)", "ISSN": "2561-178X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105717357, "Title": "Adapting Recommendations on Environmental Education Programs", "Abstract": "<p>Stakeholders in Environmental Education (EE) often face difficulties identifying and selecting programs that best suit their needs. This is due, in part, to the lack of expertise in evaluation knowledge and practice, as well as to the absence of a unified database of Environmental Education Programs (EEPs) with a defined structure. This article presents the design and development of a web application for evaluating and selecting EEPs. The certified users of the application can insert, view, and evaluate the registered EEPs. At the same time, the application creates and maintains for each user an individual and dynamic user model reflecting their personal preferences. Finally, using all the above information and applying a combination of Multi-Criteria Decision-Making Methods (MCDM), the application provides a comparative and adaptive evaluation in order to help each user to select the EEPs that best suit his/her needs. The personalized recommendations are based on the information about the user stored in the user model and the results of the EEPs evaluations by the users that have applied them. As a case study, we used the EEPs from the Greek Educational System.</p>", "Keywords": "environmental education; environmental education programs; evaluation; multi-criteria decision-making methods; comparative evaluation; adaptive evaluation environmental education ; environmental education programs ; evaluation ; multi-criteria decision-making methods ; comparative evaluation ; adaptive evaluation", "DOI": "10.3390/fi15010028", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Environment, Ionian University, 29100 Zakynthos, Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Master in Information Systems, School of Science and Technology, Hellenic Open University, 26335 Patras, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Environment, Ionian University, 29100 Zakynthos, Greece; Corresponding author"}], "References": []}, {"ArticleId": 105717482, "Title": "Estimation of Parameters in Regression Analysis Based on QR Decomposition of Rectangular Matrices by Householder Reflections", "Abstract": "An approach to eliminate multicollinearity problems in regression analysis using QR decomposition of rectangular matrices by Household<PERSON> reflection has been proposed. The reliability of this computational procedure has been proved. © 2022 Slovene Society Informatika. All rights reserved.", "Keywords": "Householder reflection; QR decomposition; regression analysis", "DOI": "10.31449/inf.v46i4.3984", "PubYear": 2023, "Volume": "46", "Issue": "4", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Public Economics and Policy, University of Tarty, Narva mnt 18 Tarty51009, Estonia"}, {"AuthorId": 2, "Name": "Lyu<PERSON><PERSON><PERSON>", "Affiliation": "Department of Higher Mathematics and Economic and Mathematical Methods, Simon <PERSON> Kharkiv National University of Economics, Nauka Ave 9A, Kharkiv, 61166, Ukraine"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Public Economics and Policy, University of Tarty, Narva mnt 18 Tarty51009, Estonia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, Simon <PERSON> National University of Economics, Nauka Ave 9A, Kharkiv, 61166, Ukraine"}], "References": []}, {"ArticleId": 105717504, "Title": "Hybrid Convolutional Neural Network for Plant Diseases Prediction", "Abstract": "Plant diseases prediction is the essential technique to prevent the yield loss and gain high production of agricultural products. The monitoring of plant health continuously and detecting the diseases is a significant for sustainable agriculture. Manual system to monitor the diseases in plant is time consuming and report a lot of errors. There is high demand for technology to detect the plant diseases automatically. Recently image processing approach and deep learning approach are highly invited in detection of plant diseases. The diseases like late blight, bacterial spots, spots on Septoria leaf and yellow leaf curved are widely found in plants. These are the main reasons to affects the plants life and yield. To identify the diseases earliest, our research presents the hybrid method by combining the region based convolutional neural network (RCNN) and region based fully convolutional networks (RFCN) for classifying the diseases. First the leaf images of plants are collected and preprocessed to remove noisy data in image. Further data normalization, augmentation and removal of background noises are done. The images are divided as testing and training, training images are fed as input to deep learning architecture. First, we identify the region of interest (RoI) by using selective search. In every region, feature of convolutional neural network (CNN) is extracted independently for further classification. The plants such as tomato, potato and bell pepper are taken for this experiment. The plant input image is analyzed and classify as healthy plant or unhealthy plant. If the image is detected as unhealthy, then type of diseases the plant is affected will be displayed. Our proposed technique achieves 98.5% of accuracy in predicting the plant diseases.", "Keywords": "deep learning; Disease detection; image classification; people detection; region based convolutional neural network", "DOI": "10.32604/iasc.2023.024820", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Si<PERSON> Nadar College of Engineering, Chennai, 603110, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Si<PERSON> Nadar College of Engineering, Chennai, 603110, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics and Operations Research, College of Science, King Saud University, Riyadh, 11451, Saudi Arabia"}, {"AuthorId": 4, "Name": "S. S. Askar", "Affiliation": "Department of Statistics and Operations Research, College of Science, King Saud University, Riyadh, 11451, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Mansoura University, Mansoura, 35516, Egypt; Department of Computational Mathematics, Science, and Engineering (CMSE), Michigan State University, East Lansing, MI  48824, United States"}], "References": [{"Title": "A smooth proximity measure for optimality in multi-objective optimization using <PERSON>s method", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "117", "Issue": "", "Page": "104900", "JournalTitle": "Computers & Operations Research"}, {"Title": "Optimization of Cognitive Radio System Using Self-Learning Salp Swarm Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "2", "Page": "3821", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "HWOA: A hybrid whale optimization algorithm with a novel local minima avoidance method for multi-level thresholding color image segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116145", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105717505, "Title": "Intelligent Risk-Identification Algorithm with Vision and 3D LiDAR Patterns at Damaged Buildings", "Abstract": "Existing firefighting robots are focused on simple storage or fire suppression outside buildings rather than detection or recognition. Utilizing a large number of robots using expensive equipment is challenging. This study aims to increase the efficiency of search and rescue operations and the safety of firefighters by detecting and identifying the disaster site by recognizing collapsed areas, obstacles, and rescuers on-site. A fusion algorithm combining a camera and three-dimension light detection and ranging (3D LiDAR) is proposed to detect and localize the interiors of disaster sites. The algorithm detects obstacles by analyzing floor segmentation and edge patterns using a mask regional convolutional neural network (mask R-CNN) features model based on the visual data collected from a parallelly connected camera and 3D LiDAR. People as objects are detected using you only look once version 4 (YOLOv4) in the image data to localize persons requiring rescue. The point cloud data based on 3D LiDAR cluster the objects using the density-based spatial clustering of applications with noise (DBSCAN) clustering algorithm and estimate the distance to the actual object using the center point of the clustering result. The proposed artificial intelligence (AI) algorithm was verified based on individual sensors using a sensor-mounted robot in an actual building to detect floor surfaces, atypical obstacles, and persons requiring rescue. Accordingly, the fused AI algorithm was comparatively verified.", "Keywords": "damaged building; risk identification; robot; Three-dimension light detection and ranging; vision", "DOI": "10.32604/iasc.2023.034394", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Information Technology, Korea National University of Transportation, Chungju, 27469, South Korea"}, {"AuthorId": 2, "Name": "Ji<PERSON>ung Min", "Affiliation": "Computer Information Technology, Korea National University of Transportation, Chungju, 27469, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Information Technology, Korea National University of Transportation, Chungju, 27469, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Railway Vehicle System Engineering, Korea National University of Transportation, Uiwang, 16106, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Information Technology, Korea National University of Transportation, Chungju, 27469, South Korea"}], "References": [{"Title": "On building a CNN-based multi-view smart camera for real-time object detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "77", "Issue": "", "Page": "103177", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Loop detection for 3D LiDAR SLAM using segment-group matching", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "23", "Page": "1530", "JournalTitle": "Advanced Robotics"}, {"Title": "A LiDAR–Camera Fusion 3D Object Detection Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "169", "JournalTitle": "Information"}, {"Title": "Vision-based Recognition Algorithm for Up-To-Date Indoor Digital Map Generations at Damaged Buildings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "2", "Page": "2765", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105717509, "Title": "THE EFFECTIVENESS OF AUGMENTATION REALITY TECHNOLOGY IN PATTERN LEARNING TOPICS", "Abstract": "Early Mathematics is one of the critical subjects where the concept of learning needs to be introduced to early-stage students so that there is no perception of this subject. In the 1970s, teaching methods for Mathematical concepts have traditionally been more on memorization. The purpose of this research was to increase students ?understanding of the topic of patterns in Early Mathematics by integrating Augmented Reality applications. The study of the development of this module was conducted using the TUP model (Technology, Usability, Pedagogy) from the Bednarik TUP Model was implemented to evaluate the technology, usability, and pedagogy to evaluate the effectiveness of the modules produced. The study found that students are more interested and show their interest in the class, parents are also involved in student learning, and teachers are able to implement teaching sessions based on discussion better when they are given more opportunities to be actively involved in the classroom. In addition to active learning in the classroom, the combination of technology also plays an important role in the flow of current learning methods.", "Keywords": "", "DOI": "10.17576/apjitm-2022-1102-05", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 43050, "JournalTitle": "Asia-Pacific Journal of Information Technology and Multimedia", "ISSN": "", "EISSN": "2289-2192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105717597, "Title": "Application of sophisticated sensors to advance the monitoring of machining processes: analysis and holistic review", "Abstract": "<p>Response measurement of various functionality states of machines is an inevitable part of smooth production. An effectively efficient measurement and control system of the machinery helps the inspection engineers to detect failures. In the recent age, the concept of industry 5.0, which focuses on the interaction between humans and machines, has increased the importance of sensors in the industry. Various sensing devices may aid and support the machining process, making it more efficient. These sensing devices support machine tools and enhance productivity by reducing failures. The application of an online monitoring system that includes vibration measurement and tool wear measurement, and the electrical energy consumption is getting fame in industry and academia. This paper mainly presents a holistic review of various sensors and their application in the manufacturing processes. Advancements in the sensor for quality measurement, cutting force measurement, and tool wear measurement are discussed. Furthermore, the adoption of the Internet of Things (IoT) in machining processes and conversion of conventional manufacturing processes into modern digitalized systems are discussed. Recent trends of research to improve the sensor technology have been improved. This study provides fundamental guidelines for using and adopting the various types of sensors in machining processes.</p>", "Keywords": "Measurement; Sensor device; Cutting force measurement; Internet of Things; Mechanical systems; Cutting processes; Machine tools", "DOI": "10.1007/s00170-022-10771-6", "PubYear": 2023, "Volume": "125", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, Tandon School of Engineering, New York University, Brooklyn, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering, University of Chakwal (UoC), Chakwal, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Integrated Technologies, Universiti Brunei Darussalam, Gadong, Brunei"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Engineering and Technology, Narowal, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Faculty of Integrated Technologies, Universiti Brunei Darussalam, Gadong, Brunei"}], "References": [{"Title": "Software defined solutions for sensors in 6G/IoE", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "42", "JournalTitle": "Computer Communications"}, {"Title": "Topology optimization against cascading failures on wireless sensor networks using a memetic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "177", "Issue": "", "Page": "107327", "JournalTitle": "Computer Networks"}, {"Title": "Industrial Internet of Things for smart manufacturing applications using hierarchical trustful resource assignment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "423", "JournalTitle": "Computer Communications"}, {"Title": "Artificial neural network-based online defect detection system with in-mold temperature and pressure sensors for high precision injection molding", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "7-8", "Page": "2023", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Prediction of the remaining useful life of cutting tool using the Hurst exponent and CNN-LSTM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> Li", "PubYear": 2021, "Volume": "112", "Issue": "7-8", "Page": "2277", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Heterogeneous sensors-based feature optimisation and deep learning for tool wear prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "9-10", "Page": "2651", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A state-of-the-art review on sensors and signal processing systems in mechanical machining processes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "9-10", "Page": "2711", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Sensor-based leakage detection in vacuum bagging", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "7-8", "Page": "2413", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Sensor data anomaly detection and correction for improving the life prediction of cutting tools in the slot milling process", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "1-2", "Page": "463", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Online cutting temperature prediction using ink-jet printed sensors and model order reduction method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "3-4", "Page": "1989", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Artificial Intelligence in Underwater Digital Twins Sensor Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}]}, {"ArticleId": 105717706, "Title": "Computing Connected Resolvability of Graphs Using Binary Enhanced Harris Hawks Optimization", "Abstract": "In this paper, we consider the NP-hard problem of finding the minimum connected resolving set of graphs. A vertex set <i>B</i> of a connected graph <i>G</i> resolves <i>G</i> if every vertex of <i>G</i> is uniquely identified by its vector of distances to the vertices in <i>B</i>. A resolving set <i>B</i> of <i>G</i> is connected if the subgraph induced by <i>B</i> is a nontrivial connected subgraph of <i>G</i>. The cardinality of the minimal resolving set is the metric dimension of <i>G</i> and the cardinality of minimum connected resolving set is the connected metric dimension of <i>G</i>. The problem is solved heuristically by a binary version of an enhanced Harris Hawk Optimization (BEHHO) algorithm. This is the first attempt to determine the connected resolving set heuristically. BEHHO combines classical HHO with opposition-based learning, chaotic local search and is equipped with an <i>S</i>-shaped transfer function to convert the continuous variable into a binary one. The hawks of BEHHO are binary encoded and are used to represent which one of the vertices of a graph belongs to the connected resolving set. The feasibility is enforced by repairing hawks such that an additional node selected from <i>V</i>\\<i>B</i> is added to <i>B</i> up to obtain the connected resolving set. The proposed BEHHO algorithm is compared to binary Harris Hawk Optimization (BHHO), binary opposition-based learning Harris Hawk Optimization (BOHHO), binary chaotic local search Harris Hawk Optimization (BCHHO) algorithms. Computational results confirm the superiority of the BEHHO for determining connected metric dimension.", "Keywords": "binary optimization; Connected resolving set; harris hawks algorithm", "DOI": "10.32604/iasc.2023.032930", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematics and Computer Science Department, Faculty of Science, Menoufia University, Shebin Elkom, 32511, Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Computer and Information Technology, King Abdulaziz University, Jeddah, 21589, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Mathematics and Computer Science Department, Faculty of Science, Menoufia University, Shebin Elkom, 32511, Egypt"}], "References": [{"Title": "The connected metric dimension at a vertex of a graph", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "806", "Issue": "", "Page": "53", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Boosted binary Harris hawks optimizer and feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3741", "JournalTitle": "Engineering with Computers"}, {"Title": "A self-adaptive Harris Hawks optimization algorithm with opposition-based learning and chaotic local search strategy for global optimization and feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "2", "Page": "309", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 105717707, "Title": "Women Entrepreneurship Index Prediction Model with Automated Statistical Analysis", "Abstract": "Recently, gender equality and women’s entrepreneurship have gained considerable attention in global economic development. Prior to the design of any policy interventions to increase women’s entrepreneurship, it is significant to comprehend the factors motivating women to become entrepreneurs. The non-understanding of the factors can result in the endurance of low living standards and the design of expensive and ineffectual policies. But female involvement in entrepreneurship becomes higher in developing economies compared to developed economies. Women Entrepreneurship Index (WEI) plays a vital role in determining the factors that enable the flourishment of high potential female entrepreneurs which enhances economic welfare and contributes to the economic and social fabric of society. Therefore, it is needed to design an automated and accurate WEI prediction model to improve women’s entrepreneurship. In this view, this article develops an automated statistical analysis enabled WEI predictive (ASA-WEIP) model. The proposed ASA-WEIP technique aims to effectually determine the WEI. The proposed ASA-WEIP technique encompasses a series of sub-processes such as pre-processing, WEI prediction, and parameter optimization. For the prediction of WEI, the ASA-WEIP technique makes use of the Deep Belief Network (DBN) model, and the parameter optimization process takes place using Squirrel Search Algorithm (SSA). The performance validation of the ASA-WEIP technique was executed using the benchmark dataset from the Kaggle repository. The experimental outcomes stated the better outcomes of the ASA-WEIP technique over the other existing techniques.", "Keywords": "decision making; gender equality; learning and development; Predictive model; statistical models; women entrepreneurship; work-life balance", "DOI": "10.32604/iasc.2023.034038", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management Studies, Easwari Engineering College, Ramapuram, 600089, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management Studies, Easwari Engineering College, Ramapuram, 600089, India"}], "References": [{"Title": "Lexicalized Dependency Paths Based Supervised Learning for Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "3", "Page": "861", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Employing Lexicalized Dependency Paths for Active Learning of Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "1415", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105717803, "Title": "Chi-Square and PCA Based Feature Selection for Diabetes Detection with Ensemble Classifier", "Abstract": "Diabetes mellitus is a metabolic disease that is ranked among the top 10 causes of death by the world health organization. During the last few years, an alarming increase is observed worldwide with a 70% rise in the disease since 2000 and an 80% rise in male deaths. If untreated, it results in complications of many vital organs of the human body which may lead to fatality. Early detection of diabetes is a task of significant importance to start timely treatment. This study introduces a methodology for the classification of diabetic and normal people using an ensemble machine learning model and feature fusion of Chi-square and principal component analysis. An ensemble model, logistic tree classifier (LTC), is proposed which incorporates logistic regression and extra tree classifier through a soft voting mechanism. Experiments are also performed using several well-known machine learning algorithms to analyze their performance including logistic regression, extra tree classifier, AdaBoost, Gaussian naive Bayes, decision tree, random forest, and k nearest neighbor. In addition, several experiments are carried out using principal component analysis (PCA) and Chi-square (Chi-2) features to analyze the influence of feature selection on the performance of machine learning classifiers. Results indicate that Chi-2 features show high performance than both PCA features and original features. However, the highest accuracy is obtained when the proposed ensemble model LTC is used with the proposed feature fusion framework-work which achieves a 0.85 accuracy score which is the highest of the available approaches for diabetes prediction. In addition, the statistical T-test proves the statistical significance of the proposed approach over other approaches.", "Keywords": "chi-square; Diabetes mellitus prediction; ensemble classifier; feature fusion; principal component analysis", "DOI": "10.32604/iasc.2023.028257", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Information Sciences, Florida International University, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Khwaja <PERSON>eed University of Engineering and Information Technology, <PERSON><PERSON>, 64200, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Khwaja <PERSON>eed University of Engineering and Information Technology, <PERSON><PERSON>, 64200, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Broward College, Broward County, FL, United States"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan-si, 38541, South Korea"}], "References": [{"Title": "Sentiment analysis of tweets using a unified convolutional neural network‐long short‐term memory network model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "409", "JournalTitle": "Computational Intelligence"}, {"Title": "Robust Reversible Audio Watermarking Scheme for Telemedicine and Privacy Protection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "2", "Page": "3035", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105717827, "Title": "Applications of the Insieme Platform: A Case Study", "Abstract": "The information society has significantly changed the field of medicine. Several decades ago when a person got sick, a doctor examined a patient and prescribed some medicine with a patient more or less unaware of the true nature of the problem. Even if explained, many patients did not understand much due to the lack of medical knowledge. Today, knowledge is widely accessible through the web and many patients try to self-diagnose or at least get another opinion using popular search engines and specialized web applications. However, many of them provide misinformation due to the lack of proper education of the provider or the lack of understanding of the user. To overcome these issues, we developed a verified medical platform (Insieme platform) that includes medical data, applications and services. This article provides a list of applications in the Insieme platform, their descriptions and how to use them.", "Keywords": "Android application; ASPO; electronic and mobile health; EMH; HEMS; HEP-Y; prostate problems; time series", "DOI": "10.31449/inf.v46i4.4210", "PubYear": 2023, "Volume": "46", "Issue": "4", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jožef <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jožef <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Robotina d.o.o"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Robotina d.o.o"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jožef <PERSON>"}], "References": []}, {"ArticleId": 105717828, "Title": "RDNN: Rumor Detection Neural Network for Veracity Analysis in Social Media Text", "Abstract": "A widely used social networking service like Twitter has the ability to disseminate information to large groups of people even during a pandemic. At the same time, it is a convenient medium to share irrelevant and unverified information online and poses a potential threat to society. In this research, conventional machine learning algorithms are analyzed to classify the data as either non-rumor data or rumor data. Machine learning techniques have limited tuning capability and make decisions based on their learning. To tackle this problem the authors propose a deep learning-based Rumor Detection Neural Network model to predict the rumor tweet in real-world events. This model comprises three layers, AttCNN layer is used to extract local and position invariant features from the data, AttBi-LSTM layer to extract important semantic or contextual information and HPOOL to combine the down sampling patches of the input feature maps from the average and maximum pooling layers. A dataset from Kaggle and ground dataset #gaja are used to train the proposed Rumor Detection Neural Network to determine the veracity of the rumor. The experimental results of the RDNN Classifier demonstrate an accuracy of 93.24% and 95.41% in identifying rumor tweets in real-time events. © 2022 Korean Society for Internet Information. All rights reserved.", "Keywords": "Attention mechanism; Bidirectional Long Short Term Memory (Bi-LSTM); Convolution Neural Network (CNN); Deep Learning; Natural Language Processing", "DOI": "10.3837/tiis.2022.12.005", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105717930, "Title": "Improved Optimization Algorithm in LSTM to Predict Crop Yield", "Abstract": "<p>Agriculture is the main occupation across the world with a dependency on rainfall. Weather changes play a crucial role in crop yield and were used to predict the yield rate by considering precipitation, wind, temperature, and solar radiation. Accurate early crop yield prediction helps market pricing, planning labor, transport, and harvest organization. The main aim of this study is to predict crop yield accurately. The incorporation of deep learning models along with crop statistics can predict yield rates accurately. We proposed an improved optimizer function (IOF) to get an accurate prediction and implemented the proposed IOF with the long short-term memory (LSTM) model. Manual data was collected between 1901 and 2000 from local agricultural departments for training, and from 2001 to 2020 from government websites of Andhra Pradesh (India) for testing purposes. The proposed model is compared with eight standard methods of learning, and outcomes revealed that the training error is small with the proposed IOF as it handles the underfitting and overfitting issues. The performance metrics used to compare the loss after implementing the proposed IOF were r, RMSE, and MAE, and the achieved results are r of 0.48, RMSE of 2.19, and MAE of 25.4. The evaluation was performed between the predicted crop yield and the actual yield and was measured in RMSE (kg/ha). The results show that the proposed IOF in LSTM has the advantage of crop yield prediction with accurate prediction. The reduction of RMSE for the proposed model indicates that the proposed IOFLSTM can outperform the CNN, RNN, and LSTM in crop yield prediction.</p>", "Keywords": "crop yield; deep learning; LSTM; model performance; optimizer crop yield ; deep learning ; LSTM ; model performance ; optimizer", "DOI": "10.3390/computers12010010", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Koneru Lakshmaiah Education Foundation, Vaddeswaram 522502, Andhra Pradesh, India; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Medical informatics Centre, School of Medicinal and Health Science Products, University of Camerino, 62032 Macerata, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Medical informatics Centre, School of Medicinal and Health Science Products, University of Camerino, 62032 Macerata, Italy"}], "References": [{"Title": "A reinforced random forest model for enhanced crop yield prediction by integrating agrarian parameters", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "10009", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Fuzzy deep learning-based crop yield prediction model for sustainable agronomical frameworks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "20", "Page": "13205", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 105717965, "Title": "Parallel Iterative FEM Solver with Initial Guess for Frequency Domain Electromagnetic Analysis", "Abstract": "The finite element method is a key player in computational electromagnetics for designing RF (Radio Frequency) components such as waveguides. The frequency-domain analysis is fundamental to identify the characteristics of the components. For the conventional frequency-domain electromagnetic analysis using FEM (Finite Element Method), the system matrix is complex-numbered as well as indefinite. The iterative solvers can be faster than the direct solver when the solver convergence is guaranteed and done in a few steps. However, such complex-numbered and indefinite systems are hard to exploit the merit of the iterative solver. It is also hard to benefit from matrix factorization techniques due to varying system matrix parts according to frequency. Overall, it is hard to adopt conventional iterative solvers even though the system matrix is sparse. A new parallel iterative FEM solver for frequency domain analysis is implemented for inhomogeneous waveguide structures in this paper. In this implementation, the previous solution of the iterative solver of Matlab (Matrix Laboratory) employing the preconditioner is used for the initial guess for the next step’s solution process. The overlapped parallel stage using Matlab’s Parallel Computing Toolbox is also proposed to alleviate the cold starting, which ruins the convergence of early steps in each parallel stage. Numerical experiments based on waveguide structures have demonstrated the accuracy and efficiency of the proposed scheme.", "Keywords": "Computational electromagnetics; finite element method; iterative solvers; numerical simulation; parallel processing", "DOI": "10.32604/iasc.2023.033112", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Incheon National University, Incheon, 22012, South Korea"}, {"AuthorId": 2, "Name": "Woobin Park", "Affiliation": "Department of Electrical Engineering, Incheon National University, Incheon, 22012, South Korea"}, {"AuthorId": 3, "Name": "Jaeyoung Park", "Affiliation": "Department of Computer Engineering, Hongik University, Seoul, 04066, South Korea"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Gachon University, Seongnam, 13120, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of IT Convergence Software, Seoul Theological University, Bucheon, 14754, South Korea"}], "References": []}, {"ArticleId": *********, "Title": "A Novel Agent-Based Multipath Routing Protocol to Extend Lifetime and Enhancing Reliability of Clustered Wireless Sensor Networks", "Abstract": "Over the clustered wireless network systems, development in wireless technology has had a more substantial influence. Entities need to communicate with one another in order to create a sustainable ecosystem. Clustering methods help connect and organise the sensor nodes by load balancing and extending the network lifetime. Only now, various techniques have been developed for solving routing problems but have yet to focus on routing reliability with avoidance of data collision in real scenarios. This research is carried out for the reliability of routing by multi-objective optimization in static and dynamic environments through agent-based analysis with avoidance of data collision and depletion of energy. This study introduces a fuzzy-based multipath clustering technique that exhibits both static and dynamic clustering formation properties. The designated region starts the clustering process once the sensor nodes are ready to begin the data transmission procedure. The proposed technique works in two steps: a) fuzzy cluster head selection; and b) multi-objective agent-based multipath routing protocols for shortest route path discovery. The enhancement made in cluster creation and selection is the critical feature. A well-organized sensor ecosystem has lessened the negative impacts of network collision and energy exhaustion. The packet delivery ratio, communication overhead, and energy consumption are the performance metrics examined when simulating the specified protocol using the computer language NS2. The devised fuzzy-based multi-path routing (FC-MRP) clustering technique outperforms the AODV (Ad-hoc on-demand distance vector routing) protocol, according to the results. The average percentage of improvement concerning PDR, Throughput, end-to-end latency, Overhead, Energy utilised, Energy efficiency, Network lifetime, and PLR is found to be +2.53, +2.23, -18.58, -22.46, -17.95, +23.00, +4.11, -18.09 respectively. © 2022 The Korean Society for Vascular Surgery.", "Keywords": "AODV; Clustering Approach; FC-MRP; Fuzzy Logic System; Multi-Objective Optimization; Routing Protocols and Multipath Agents", "DOI": "10.22247/ijcna/2022/217702", "PubYear": 2022, "Volume": "9", "Issue": "6", "JournalId": 39456, "JournalTitle": "International Journal of Computer Networks And Applications", "ISSN": "", "EISSN": "2395-0455", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering and applications, Indira Gandhi Institute of Technology, Odisha, Sarang, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering and applications, Indira Gandhi Institute of Technology, Odisha, Sarang, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering and applications, Indira Gandhi Institute of Technology, Odisha, Sarang, India"}], "References": []}, {"ArticleId": *********, "Title": "A V2V Identity Authentication and Key Agreement Scheme Based on Identity-Based Cryptograph", "Abstract": "<p>Cellular vehicle to everything (C-V2X) is a technology to achieve vehicle networking, which can improve traffic efficiency and traffic safety. As a special network, the C-V2X system faces many security risks. The vehicle to vehicle (V2V) communication transmits traffic condition data, driving path data, user driving habits data, and so on. It is necessary to ensure the opposite equipment is registered C-V2X equipment (installed in the vehicle), and the data transmitted between the equipment is secure. This paper proposes a V2V identity authentication and key agreement scheme based on identity-based cryptograph (IBC). The C-V2X equipment use its vehicle identification (VID) as its public key. The key management center (KMC) generates a private key for the C-V2X equipment according to its VID. The C-V2X equipment transmit secret data encrypted with the opposite equipment public key to the other equipment, they authenticate each other through a challenge response protocol based on identity-based cryptography, and they negotiate the working key used to encrypt the communication data. The scheme can secure the V2V communication with low computational cost and simple architecture and meet the lightweight and efficient communication requirements of the C-V2X system.</p>", "Keywords": "", "DOI": "10.3390/fi15010025", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing 100876, China"}], "References": []}, {"ArticleId": 105718303, "Title": "The biggest business process management problems to solve before we die", "Abstract": "It may be tempting for researchers to stick to incremental extensions of their current work to plan future research activities. Yet there is also merit in realizing the grand challenges in one’s field. This paper presents an overview of the nine major research problems for the Business Process Management discipline. These challenges have been collected by an open call to the community, discussed and refined in a workshop setting, and described here in detail, including a motivation why these problems are worth investigating. This overview may serve the purpose of inspiring both novice and advanced scholars who are interested in the radical new ideas for the analysis, design, and management of work processes using information technology.", "Keywords": "Business process management ; Grand challenges ; Process mining ; Business process redesign ; Digital twins ; Process modeling", "DOI": "10.1016/j.compind.2022.103837", "PubYear": 2023, "Volume": "146", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Utrecht University, Heidelberglaan 8, Utrecht, 3584 CS, Netherlands;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Sapienza University of Rome, Viale Regina Elena 295, Rome, 00161, Italy;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Utrecht University, Heidelberglaan 8, Utrecht, 3584 CS, Netherlands;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Technical University of Munich, Arcisstraße 21, Munich, 80333, Germany;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Bandara", "Affiliation": "Queensland University of Technology, 2 George Street, Brisbane, 4000, Queensland, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Anker Engelunds Vej 1, Lyngby, 2800, Denmark"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Free University of Bozen-Bolzano, Piazza Università, 1, Bozen-Bolzano, 39100, Italy"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Queensland, St Lucia, Brisbane, 4072, Queensland, Australia"}, {"AuthorId": 9, "Name": "Izack Cohen", "Affiliation": "Bar-Ilan University, Ramat Gan, 590002, Israel"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hasselt University, Martelarenlaan 42, <PERSON><PERSON><PERSON>, 3500, Belgium"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Anker Engelunds Vej 1, Lyngby, 2800, Denmark"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tartu, Ülikooli 18, Tartu, 50090, Estonia"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "University of Bayreuth, Universitätsstraße 30, Bayreuth, 95447, Germany"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "University of Augsburg, Universitätsstraße 2, Augsburg, 86159, Germany"}, {"AuthorId": 15, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Bayreuth, Universitätsstraße 30, Bayreuth, 95447, Germany"}, {"AuthorId": 16, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Technion – Israel Institute of Technology, Haifa, 3200003, Israel"}, {"AuthorId": 17, "Name": "<PERSON>", "Affiliation": "The University of Queensland, St Lucia, Brisbane, 4072, Queensland, Australia"}, {"AuthorId": 18, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBM Research, Cambridge, MA, United States"}, {"AuthorId": 19, "Name": "<PERSON>", "Affiliation": "Capgemini Invent, Sydney, New South Wales, Australia"}, {"AuthorId": 20, "Name": "<PERSON>", "Affiliation": "Augsburg University of Applied Sciences, An d. Hochschule 1, Augsburg, 86161, Germany"}, {"AuthorId": 21, "Name": "<PERSON>", "Affiliation": "Kühne Logistics University, Großer Grasbrook 17, Hamburg, 20457, Germany"}, {"AuthorId": 22, "Name": "<PERSON>", "Affiliation": "Ghent University, Tweekerkenstraat 2, Ghent, 9000, Belgium"}, {"AuthorId": 23, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Anker Engelunds Vej 1, Lyngby, 2800, Denmark"}, {"AuthorId": 24, "Name": "<PERSON><PERSON>", "Affiliation": "Vienna University of Technology, Wiedner Hauptstraße, Vienna, 1040, Austria"}, {"AuthorId": 25, "Name": "<PERSON>", "Affiliation": "Humboldt-Universität zu Berlin, Unter den Linden 6, Berlin, 10117, Germany"}, {"AuthorId": 26, "Name": "<PERSON>", "Affiliation": "Powerlink Queensland, Virginia, Queensland, Australia"}, {"AuthorId": 27, "Name": "<PERSON>", "Affiliation": "University of Bayreuth, Universitätsstraße 30, Bayreuth, 95447, Germany"}, {"AuthorId": 28, "Name": "<PERSON>", "Affiliation": "Free University of Bozen-Bolzano, Piazza Università, 1, Bozen-Bolzano, 39100, Italy"}, {"AuthorId": 29, "Name": "<PERSON><PERSON>", "Affiliation": "IBM Research, Cambridge, MA, United States"}, {"AuthorId": 30, "Name": "<PERSON>", "Affiliation": "Ulm University, Helmholtzstraße 16, Ulm, 89081, Germany"}, {"AuthorId": 31, "Name": "<PERSON><PERSON>", "Affiliation": "IBM Research, Cambridge, MA, United States"}, {"AuthorId": 32, "Name": "<PERSON>", "Affiliation": "Queensland University of Technology, 2 George Street, Brisbane, 4000, Queensland, Australia"}, {"AuthorId": 33, "Name": "<PERSON>", "Affiliation": "University of Bayreuth, Universitätsstraße 30, Bayreuth, 95447, Germany"}, {"AuthorId": 34, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Queensland, St Lucia, Brisbane, 4072, Queensland, Australia"}, {"AuthorId": 35, "Name": "<PERSON><PERSON>", "Affiliation": "University of St. Gallen, Dufourstrasse 50, St. Gallen, 9000, Switzerland"}, {"AuthorId": 36, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Copenhagen University, Nørregade 10, Copenhagen, 1165, Denmark"}, {"AuthorId": 37, "Name": "Mantas Simkus", "Affiliation": "Vienna University of Technology, Wiedner Hauptstraße, Vienna, 1040, Austria"}, {"AuthorId": 38, "Name": "<PERSON>", "Affiliation": "The University of Queensland, St Lucia, Brisbane, 4072, Queensland, Australia"}, {"AuthorId": 39, "Name": "<PERSON>", "Affiliation": "University of St. Gallen, Dufourstrasse 50, St. Gallen, 9000, Switzerland"}, {"AuthorId": 40, "Name": "<PERSON><PERSON>", "Affiliation": "Technical University of Munich, Arcisstraße 21, Munich, 80333, Germany;Fraunhofer-Gesellschaft, Hansastraße 27c, Munich, 80686, Germany"}, {"AuthorId": 41, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>, Prof.-Dr.-Helmert-Straße 2-3, Potsdam, 14482, Germany"}, {"AuthorId": 42, "Name": "<PERSON>", "Affiliation": "University of St. Gallen, Dufourstrasse 50, St. Gallen, 9000, Switzerland"}], "References": [{"Title": "Customer-centric prioritization of process improvement projects", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "113286", "JournalTitle": "Decision Support Systems"}, {"Title": "An Exploration into Future Business Process Management Capabilities in View of Digitalization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "2", "Page": "83", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Every apprentice needs a master: Feedback-based effectiveness improvements for process model matching", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "95", "Issue": "", "Page": "101612", "JournalTitle": "Information Systems"}, {"Title": "Business Process Management: The evolution of a discipline", "Authors": "<PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "126", "Issue": "", "Page": "103404", "JournalTitle": "Computers in Industry"}, {"Title": "Beyond arrows in process models: A user study on activity dependences and their rationales", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "101762", "JournalTitle": "Information Systems"}, {"Title": "Multi-Dimensional Event Data in Graph Databases", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1-2", "Page": "109", "JournalTitle": "Journal on Data Semantics"}]}, {"ArticleId": 105718392, "Title": "Multi-level Cross-attention Siamese Network For Visual Object Tracking", "Abstract": "Currently, cross-attention is widely used in Siamese trackers to replace traditional correlation operations for feature fusion between template and search region. The former can establish a similar relationship between the target and the search region better than the latter for robust visual object tracking. But existing trackers using cross-attention only focus on rich semantic information of high-level features, while ignoring the appearance information contained in low-level features, which makes trackers vulnerable to interference from similar objects. In this paper, we propose a Multi-level Cross-attention Siamese network(MCSiam) to aggregate the semantic information and appearance information at the same time. Specifically, a multi-level cross-attention module is designed to fuse the multi-layer features extracted from the backbone, which integrate different levels of the template and search region features, so that the rich appearance information and semantic information can be used to carry out the tracking task simultaneously. In addition, before cross-attention, a target-aware module is introduced to enhance the target feature and alleviate interference, which makes the multi-level cross-attention module more efficient to fuse the information of the target and the search region. We test the MCSiam on four tracking benchmarks and the result show that the proposed tracker achieves comparable performance to the state-of-the-art trackers. Copyright © 2022 KSII.", "Keywords": "Computer vision; Cross-attention; Object tracking; Self-attention; Siamese network", "DOI": "10.3837/tiis.2022.12.011", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105718393, "Title": "GCNXSS: An Attack Detection Approach for Cross-Site Scripting Based on Graph Convolutional Networks", "Abstract": "Since machine learning was introduced into cross-site scripting (XSS) attack detection, many researchers have conducted related studies and achieved significant results, such as saving time and labor costs by not maintaining a rule database, which is required by traditional XSS attack detection methods. However, this topic came across some problems, such as poor generalization ability, significant false negative rate (FNR) and false positive rate (FPR). Moreover, the automatic clustering property of graph convolutional networks (GCN) has attracted the attention of researchers. In the field of natural language process (NLP), the results of graph embedding based on GCN are automatically clustered in space without any training, which means that text data can be classified just by the embedding process based on GCN. Previously, other methods required training with the help of labeled data after embedding to complete data classification. With the help of the GCN auto-clustering feature and labeled data, this research proposes an approach to detect XSS attacks (called GCNXSS) to mine the dependencies between the units that constitute an XSS payload. First, GCNXSS transforms a URL into a word homogeneous graph based on word co-occurrence relationships. Then, GCNXSS inputs the graph into the GCN model for graph embedding and gets the classification results. Experimental results show that GCNXSS achieved successful results with accuracy, precision, recall, F1-score, FNR, FPR, and predicted time scores of 99.97%, 99.75%, 99.97%, 99.86%, 0.03%, 0.03%, and 0.0461ms. Compared with existing methods, GCNXSS has a lower FNR and FPR with stronger generalization ability. Copyright © 2022 KSII.", "Keywords": "Cross-site Scripting; Graph Convolutional Networks(GCN); Web security", "DOI": "10.3837/tiis.2022.12.013", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105718442, "Title": "Improving gain of real time PI controller using particle swarm optimization in active power filter", "Abstract": "This paper incorporates the PSO based PI control for systematic switching function of APF topology. The proportional and integral (PI) gain tuning of active power filter (APF) using the particle swam optimization (PSO) method for reactive power compensation and harmonics mitigation. A conventional PI controller required more computation time and has less accuracy. The harmonic load currents are extracted using an instantaneous real and reactive power scheme. The performance indices including total harmonic distortion, reactive power, power factor and capacitor voltage regulation using PSO trained PI controller is compared with convention PI controller. PSO has quick convergence, minimal adjusting parameters and fast execution to solve non-linear problems. The traditional PI controller is substituted by an online PSO trained PI controller for the objectives of enhancing the dc voltage tracking in APF under non-linear load conditions. The proposed work is developed in sim-power system tool box which is a software package in Matlab/ Simulink.", "Keywords": "", "DOI": "10.1016/j.micpro.2023.104760", "PubYear": 2023, "Volume": "97", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of EEE, KoneruLakshmaiah Education Foundation, Vaddeswaram, A.P, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of EEE, KoneruLakshmaiah Education Foundation, Vaddeswaram, A.P, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of EEE, RGM College of Engineering & Technology, Nandyal, A.P, India;Corresponding author"}], "References": [{"Title": "Fuzzy Logic Control of SLMMC-Based SAPF Under Nonlinear Loads", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "428", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "RETRACTED ARTICLE: Computational intelligence based control of cascaded H-bridge multilevel inverter for shunt active power filter application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "S1", "Page": "95", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 105718452, "Title": "Stochastic dynamic programming solution to transmission scheduling: Multi sensor-multi process with wireless noisy channel", "Abstract": "We investigate sensor scheduling for remote estimation when multiple smart sensors monitor multiple stochastic dynamical systems. The sensors transmit their measurements to a remote estimator through a noisy wireless communication channel. Such a remote estimator can receive multiple packets simultaneously sent by local sensors. Sensors transmit their measurements if their Signal Interference and Noise Ratio (SINR) is above a threshold. We compute the optimal policy for sensor scheduling by minimizing expected error covariance subject to total signal transmissions from all sensors. We model this problem as Markov Decision Process (MDP) with discounted cost per stage in the finite time horizon framework, then we employ stochastic Dynamic Programming as the optimization method. A novel algorithm based on sampling and machine learning techniques is proposed as the approximation. At each phase of the DP algorithm, samples are collected using a uniform probability distribution. The data is used to feed Neural Network (NN) and Random Forest (RF) models for cost function and policy approximation. The results of the proposed framework are supported by simulation examples comparing RF and NN as Approximate DP (ADP). Note that this idea builds a bridge among the recent advances in the area of data science, Machine Learning, and the ADP.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108573", "PubYear": 2023, "Volume": "106", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hamilton Institute, Maynooth University, Co., Kildare W23 F2K8, Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Engineering, University of Sannio, 82100, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology, University of Naples Federico II, Napoli, 80125, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Bu-Ali Sina University, Hamedan, Iran;Corresponding author"}], "References": [{"Title": "Transmission Scheduling for Remote Estimation with Multi-packet Reception under Multi-Sensor Interference", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "2628", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Economic Operation and Management of Microgrid System Using Deep Reinforcement Learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "100", "Issue": "", "Page": "107879", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 105718455, "Title": "Feature selection and cascade dimensionality reduction for self-supervised visual representation learning", "Abstract": "Self-supervised visual representation learning focuses on capturing comprehensive features via exploiting the unlabeled datasets. However, existing contrastive learning based SSL frameworks are subjected to higher computational consumption and unsatisfactory performance. To handle these issues, we present a novel single-branch SSL method that incorporates an adaptive feature selection and activation module and a progressive cascade dimensionality reduction module, called APNet. Specifically, our method first fully exploits the unlabeled datasets and extracts intra- and inter-image information via introducing montage image. In addition, a novel adaptive feature selection and activation module is designed to generate the most comprehensive features. Besides, a progressive cascade dimensionality reduction module is proposed to capture the most representative features from latent vectors through cascade dimensionality increasing–decreasing operations. Extensive experiments have demonstrated the robustness and effectiveness of APNet. Specifically, APNet exceeds MoCo-v3 by 3.1% on the ImageNet-100 dataset, and consumes only half of the calculation. Code is available at https://github.com/AI-TYQ/APNet .", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108570", "PubYear": 2023, "Volume": "106", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Henan Institute of Science and Technology, Xinxiang, 453003, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Jin", "Affiliation": "School of Information Engineering, Henan Institute of Science and Technology, Xinxiang, 453003, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Henan Institute of Science and Technology, Xinxiang, 453003, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Henan Institute of Science and Technology, Xinxiang, 453003, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Henan Institute of Science and Technology, Xinxiang, 453003, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "School of Information Engineering, Henan Institute of Science and Technology, Xinxiang, 453003, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, 100876, China"}, {"AuthorId": 8, "Name": "Xipeng Pan", "Affiliation": "School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, 541004, China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, 100876, China;Corresponding authors"}], "References": [{"Title": "Single-branch self-supervised learning with hybrid tasks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108168", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "LESSL: Can LEGO sampling and collaborative optimization contribute to self-supervised learning?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "615", "Issue": "", "Page": "475", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105718457, "Title": "Privacy preserving Federated Learning framework for IoMT based big data analysis using edge computing", "Abstract": "The current industrial scenario has witnessed the application of several artificial intelligence-based technologies for mining and processing IoMT-based big data. An emerging distributed machine learning paradigm, Federated Learning (FL), has been widely applied in IoMT-based systems as a measure to overcome the issues associated with incorporating AI into such lightweight distributed computing systems while tackling privacy issues as well. However, extensive research has identified that classical FL is still prone to privacy threats due to data leakage and the chances of adversarial attacks during gradient transfer operations. Inspired by these issues, we propose a privacy-preserving framework ( F e d _ s e l e c t ) that ensures user anonymity in IoMT-based environments for big data analysis under the FL scheme. Fed_Select utilizes alternative minimization to limit gradients and participants in system training to decrease points of system vulnerability. The framework works on an edge computing-based architecture which ensures user anonymity via the employment of hybrid encryption techniques along with added benefits of load reduction at the central server. Also, a Laplacian noise-based differential privacy is employed on the shared attributes for security enhancement that adds confidentiality to the transferred data even during adversarial scenarios. Experimental results on standard datasets showcase that the change in the volume of gradients shared and the number of participants is not proportional to the variation in various system performance parameters. Specifically, an idealistic range of client and gradient-sharing fractions along with the appropriate value of noise for differential privacy implementation is determined. Additionally, we analyze the system from a security perspective as well as compare it with other schemes.", "Keywords": "Federated learning ; Edge computing ; IoMT ; Privacy preservation ; Anonymity ; Encryption", "DOI": "10.1016/j.csi.2023.103720", "PubYear": 2023, "Volume": "86", "Issue": "", "JournalId": 739, "JournalTitle": "Computer Standards & Interfaces", "ISSN": "0920-5489", "EISSN": "1872-7018", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology, Kottayam, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology, Kottayam, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology, Kottayam, India"}], "References": [{"Title": "Efficient privacy preservation of big data for accurate data mining", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "420", "JournalTitle": "Information Sciences"}, {"Title": "Novel trajectory privacy-preserving method based on clustering using differential privacy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113241", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Analysis of healthcare big data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "103", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A Systematic Review of Hidden Markov Models and Their Applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1429", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Security and privacy of electronic health records: Concerns and challenges", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "2", "Page": "177", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "DeepThin: A novel lightweight CNN architecture for traffic sign recognition without GPU requirements", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114481", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Privacy-preserving Federated Deep Learning for Wearable IoT-based Biomedical Monitoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Blockchain-assisted handover authentication for intelligent telehealth in multi-server edge computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "102024", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "6G-enabled Edge Intelligence for Ultra -Reliable Low Latency Applications: Vision and Mission", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "", "Page": "103521", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "A Comprehensive Survey of Privacy-preserving Federated Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Privacy preservation in federated learning: An insightful survey from the GDPR perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102402", "JournalTitle": "Computers & Security"}, {"Title": "Toward secure distributed data storage with error locating in blockchain enabled edge computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "79", "Issue": "", "Page": "103560", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Privacy-preserving Byzantine-robust federated learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "103561", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Privacy-Preserving federated learning in medical diagnosis with homomorphic re-Encryption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "103583", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "An adaptive access control scheme based on trust degrees for edge computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "82", "Issue": "", "Page": "103640", "JournalTitle": "Computer Standards & Interfaces"}]}, {"ArticleId": 105718461, "Title": "Cross-domain multi-style merge for image captioning", "Abstract": "Multi-style image captioning has attracted wide attention recently. Existing approaches mainly rely on style synthetics within a single domain. They cannot deal with multiple styles combination since various styles naturally cannot be included in a uniform dataset. This paper is the first one to investigate the cross-domain multi-style merge for image captioning. Specifically, we propose a novel image caption model with a multi-style gated transformer block to fit the cross-domain caption generation task. Conventional generative adversarial learning for language methods may suffer from the distribution distortion problem, since real datasets do not contain captions with style combinations. Therefore, we devise a multi-stage self-learning framework for the proposed image caption model to exploit real corpus with pseudo styles gradually. Comprehensive experiments and ablation studies demonstrate the effectiveness of our proposed method on the multi-style merge for image captioning.", "Keywords": "", "DOI": "10.1016/j.cviu.2022.103617", "PubYear": 2023, "Volume": "228", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CIBCI lab, Australian Artificial Intelligence Institute, School of Computer Science, University of Technology Sydney, Ultimo, NSW 2007, Australia;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, FIET, The University of Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, The Hong Kong University of Science and Technology, Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Engineering Research Center of Intelligent Vision and Imaging, School of Information Science and Technology, ShanghaiTech University, Shanghai, 201210, China;Corresponding authors"}], "References": [{"Title": "GPT-3: What’s it good for?", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "113", "JournalTitle": "Natural Language Engineering"}, {"Title": "Position-aware image captioning with spatial relation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "497", "Issue": "", "Page": "28", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": *********, "Title": "Envejecimiento demográfico: oportunidad de participación en la economía plateada a través del turismo", "Abstract": "<p>La presente investigación analiza el fenómeno del envejecimiento de la población, con énfasis en México, lo cual permite identificar los retos que se plantean en diversos ámbitos. Ante esos retos, una oportunidad interesante que se abre es acerca de la economía plateada, por ello se introduce el término y se identifica su aplicación en el turismo para este grupo de personas. La investigación es eminentemente teórica, realizada mediante una revisión bibliográfica que tiene como objetivo indagar sobre el fenómeno para tener una mayor comprensión del mismo y con ello generar futuras líneas de investigación. </p>", "Keywords": "Envejecimiento de la población;economía plateada;turismo", "DOI": "10.29057/est.v8i16.9856", "PubYear": 2023, "Volume": "8", "Issue": "16", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Escuela Superior de Tizayuca"}, {"AuthorId": 2, "Name": "Elba Mariana Pedraza Amador", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Escuela Superior de Tizayuca"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Escuela Superior de Tizayuca"}], "References": []}, {"ArticleId": 105718556, "Title": "Privacy-Preserving Mechanism to Secure IoT-Enabled Smart Healthcare System in the Wireless Body Area Network", "Abstract": "The IoT has been a subclass of Industry 4.0 standards that is under research from the perspective of quality of service (QoS) &amp; security. Due to the pandemic situations like novel coronavirus smart healthcare monitoring gained growing interest in detection. In IoT data is communicated from Intra WBAN (Wireless Body Area Network) to inter-WBAN and then beyond WBAN. While transferring data from one layer to the other end-to-end data privacy is the challenge to focus on. The privacy-preserving of patients' sensitive data is difficult due to their open nature and resource-constrained sensor nodes. The proposed research design based on routing protocols achieves the patient’s sensitive data privacy preservation along with minimum computation efforts and energy consumption. The proposed model is Secure Communication-Elliptic Curve Cryptography (SCECC) WBAN-assisted networks in presence of attackers is evaluated using NS2. The proposed privacy preservation algorithm uses efficient cryptographic solutions using hash, digital signature, and the optimization of the network.", "Keywords": "Authentication; Cryptography; ECC; Encryption;  Wireless Body Area Network; Routing Protocol.", "DOI": "10.22247/ijcna/2022/217707", "PubYear": 2022, "Volume": "9", "Issue": "6", "JournalId": 39456, "JournalTitle": "International Journal of Computer Networks And Applications", "ISSN": "", "EISSN": "2395-0455", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Department, <PERSON>rdar Patel Institute of Technology, Mumbai, Maharashtra"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Computer Department, <PERSON>rdar Patel Institute of Technology, Mumbai, Maharashtra, India"}], "References": []}, {"ArticleId": *********, "Title": "Power-Sharing Enhancement Using Harmonized Membership Fuzzy Logic Droop Control Based Micro-Grid", "Abstract": "The contribution of Renewable Energy Resources (RER) in the process of power generation is significantly high in the recent days since it paves the way for overcoming the issues like serious energy crisis and natural contamination. This paper deals with the renewable energy based micro-grid as it is regarded as the apt solution for integrating the RER with the electrical frameworks. As the fixed droop coefficients in conventional droop control approaches have caused various limitations like low power-sharing and sudden drops of grid voltage in the Direct Current (DC) side, the Harmonized Membership Fuzzy Logic (MFL) droop control is employed in this present study. This proposed droop control for the hybrid PV-wind-battery system with MFL assists in achieving proper power-sharing and minimizing Total Harmonic Distortion (THD) in the emergency micro-grid. It eradicates the deviations in voltage and frequency with its flexible and robust operation. The THD is reduced and attains the value of 3.1% compared to the traditional droop control. The simulation results of harmonized MFL droop control are analogized with the conventional approaches to validate the performance of the proposed method. In addition, the experimental results provided by the Field Programmable Gate Array (FPGA) based laboratory setup built using a solar photovoltaic (PV) and wind Permanent Magnet Synchronous Generator (PMSG) reaffirms the design.", "Keywords": "fuzzy; harmonized droop control; Micro-grid; power-sharing; total harmonic distortion", "DOI": "10.32604/iasc.2023.028970", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Vellore Institute of Technology, Tamilnadu, Vellore, 632014, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Vellore Institute of Technology, Tamilnadu, Vellore, 632014, India"}], "References": []}, {"ArticleId": 105718573, "Title": "The Impact of Hydrogen Energy Storage on the Electricity Harvesting", "Abstract": "The economics, infrastructure, transportation, and level of living of a country are all influenced by energy. The gap between energy usage and availability is a global issue. Currently, all countries rely on fossil fuels for energy generation, and these fossil fuels are not sustainable. The hydrogen proton exchange membrane fuel cell (PEMFC) power system is both clean and efficient. The fuel delivery system and the PEMFC make up the majority of the PEMFC power system. The lack of an efficient, safe, and cost-effective hydrogen storage system is still a major barrier to its widespread use. Solid hydrogen storage has the large capacity, safety and good reversibility. As a hydrogen source system, the hydrogen supply characteristics affect the characteristics of the PEMFC at the output. In this paper, a mathematical model of a hydrogen source reactor and PEMFC based on chemical absorption/desorption of solid hydrogen storage is established, and a simulation model of a PEMFC power system coupled with solid hydrogen storage is established using MATLAB/SIMULINK software, and the hydrogen supply of the reactor is analyzed in detail. The influence of prominent factors is evaluated. The research results show that the proposed method improved the system performance. At the same time, increasing the PEMFC temperature, increasing the area of the proton exchange membrane and the oxygen supply pressure can increase the output power of the power system.", "Keywords": "electrical energy harvesting; fuel cell; Hydrogen energy; power system optimization", "DOI": "10.32604/iasc.2023.033627", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Faculty of Engineering, K.C.KARE Energy Research and Innovation Centre, King Abdulaziz University, Jeddah, 21589, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, College of Engineering, Taif University, P. O. Box 11099, Taif, 21944, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Engineering & Technology, Peshawar, Pakistan"}, {"AuthorId": 4, "Name": "Dag 豬vind <PERSON>", "Affiliation": "University of South-Eastern Norway, Bredalsveien 14, <PERSON><PERSON><PERSON><PERSON><PERSON>, 3511, Norway"}], "References": [{"Title": "Optimal Parameter Estimation of Proton Exchange Membrane Fuel Cells", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "2", "Page": "619", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105718582, "Title": "An Intelligent Deep Neural Sentiment Classification Network", "Abstract": "A Deep Neural Sentiment Classification Network (DNSCN) is developed in this work to classify the Twitter data unambiguously. It attempts to extract the negative and positive sentiments in the Twitter database. The main goal of the system is to find the sentiment behavior of tweets with minimum ambiguity. A well-defined neural network extracts deep features from the tweets automatically. Before extracting features deeper and deeper, the text in each tweet is represented by Bag-of-Words (BoW) and Word Embeddings (WE) models. The effectiveness of DNSCN architecture is analyzed using Twitter-Sanders-Apple2 (TSA2), Twitter-Sanders-Apple3 (TSA3), and Twitter-DataSet (TDS). TSA2 and TDS consist of positive and negative tweets, whereas TSA3 has neutral tweets also. Thus, the proposed DNSCN acts as a binary classifier for TSA2 and TDS databases and a multiclass classifier for TSA3. The performances of DNSCN architecture are evaluated by F1 score, precision, and recall rates using 5-fold and 10-fold cross-validation. Results show that the DNSCN-WE model provides more accuracy than the DNSCN-BoW model for representing the tweets in the feature encoding. The F1 score of the DNSCN-BW based system on the TSA2 database is 0.98 (binary classification) and 0.97 (three-class classification) for the TSA3 database. This system provides better a F1 score of 0.99 for the TDS database.", "Keywords": "bag-of-words; Deep neural network; sentiment analysis; text classification; word embeddings", "DOI": "10.32604/iasc.2023.032108", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Engineering, SRM Valliammai Engineering College, Tamilnadu, Kattankulathur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Tamilnadu, Kattankulathur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Tamilnadu, Kattankulathur, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, SRM Valliammai Engineering College, Tamilnadu, Kattankulathur, India"}], "References": []}, {"ArticleId": 105718592, "Title": "Enhanced <PERSON>a (Ersa): An Advanced Mechanism for Improving the Security", "Abstract": "Cloud computing has become ubiquitous in our daily lives in recent years. Data are the source of technology that is generated hugely by various sources. Big data is dealing with huge data volumes or complex data. The major concern in big data is security threats. Security concerns create a negative impact on the user on the aspect of trust. In big data still, security threats exist as commonly known as DDOS (Distributed-Denial-of-Service) attacks, data loss, Inadequate Data Backups, System Vulnerabilities, and Phishing as well as Social Engineering Attacks. In our work, we have taken the data loss and Inadequate Data Backups issues into consideration. We analyze that RSA (Rivest, Shamir, & Adleman) is the most secure cryptography mechanism. In cloud computing, user authentication is the weaker section to be secured. Generally, the cryptography mechanism is done in the authentication section only. We implemented our new idea of registration with selected images and pins for processing RSA. By valid authentication approval earned by the proposed mechanism, the user is allowed to use the cloud database, encryption, decryption, etc. To prove the efficiency level of our proposed system, a comparison work is conducted between DSSE (Digital Signature Standard Encryption) and EFSSA (Efficient framework for securely sharing a file using asymmetric key distribution management). The experimental work is carried out and the performance evaluation is done using encryption time and decryption time analysis, throughput, and processing time. On this observation, the security level attained by ERSA is far better in comparison to DSSE and EFSSA with the maximum throughput attained by the proposed E-RSA being 500 Mb/Min and encryption time of 3.2 s, thus ensuring the user trust in using the cloud environment.", "Keywords": "Cloud computing; decryption; encryption; file sharing; key generation; RSA", "DOI": "10.32604/iasc.2023.032222", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Karpagam College of Engineering, Tamil Nadu, Coimbatore, 641032, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "PSNA College of Engineering And Technology, Tamil Nadu, Dindigul, 624622, India"}], "References": []}, {"ArticleId": 105718601, "Title": "Early Detection Glaucoma and <PERSON><PERSON><PERSON>’s Disease Using Deep Learning Techniques", "Abstract": "Retinal fundus images are used to discover many diseases. Several Machine learning algorithms are designed to identify the Glaucoma disease. But the accuracy and time consumption performance were not improved. To address this problem Max Pool Convolution Neural Kuan Filtered Tobit Regressive Segmentation based Radial Basis Image Classifier (MPCNKFTRS-RBIC) Model is used for detecting the Glaucoma and <PERSON><PERSON><PERSON>’s disease by early period using higher accuracy and minimal time. In MPCNKFTRS-RBIC Model, the retinal fundus image is considered as an input which is preprocessed in hidden layer 1 using weighted adaptive Kuan filter. Then, preprocessed retinal fundus is given for hidden layer 2 for extracting the features like color, intensity, texture with higher accuracy. After extracting these features, the Tobit Regressive Segmentation process is performed by hidden layer 3 for partitioning preprocessed image within more segments by analyzing the pixel with the extracted features of the fundus image. Then, the segmented image was given to output layer. The radial basis function analyzes the testing image region of a particular class as well as training image region with higher accuracy and minimum time consumption. Simulation is performed with retinal fundus image dataset with various performance metrics namely peak signal-to-noise ratio, accuracy and time, error rate concerning several retina fundus image and image size.", "Keywords": "Glaucoma detection; kuan filter; max pool convolution neural network; radial basis function", "DOI": "10.32604/iasc.2023.033200", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Shakthi Institute of Engineering and Technology, Tamilnadu, Coimabatore, 641062, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Shakthi Institute of Engineering and Technology, Tamilnadu, Coimabatore, 641062, India"}], "References": [{"Title": "Hard exudate detection in retinal fundus images using supervised learning", "Authors": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "17", "Page": "13079", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Automated detection of Glaucoma using deep learning convolution network (G-net)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "15531", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "RETRACTED ARTICLE: A metaheuristic segmentation framework for detection of retinal disorders from fundus images using a hybrid ant colony optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "17", "Page": "13347", "JournalTitle": "Soft Computing"}, {"Title": "RETRACTED: Accurate disease detection quantification of iris based retinal images using random implication image classifier technique", "Authors": "<PERSON>; Shan <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "", "Page": "103350", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 105718660, "Title": "Improved deep depth estimation for environments with sparse visual cues", "Abstract": "<p>Most deep learning-based depth estimation models that learn scene structure self-supervised from monocular video base their estimation on visual cues such as vanishing points. In the established depth estimation benchmarks depicting, for example, street navigation or indoor offices, these cues can be found consistently, which enables neural networks to predict depth maps from single images. In this work, we are addressing the challenge of depth estimation from a real-world bird’s-eye perspective in an industry environment which contains, conditioned by its special geometry, a minimal amount of visual cues and, hence, requires incorporation of the temporal domain for structure from motion estimation. To enable the system to incorporate structure from motion from pixel translation when facing context-sparse, i.e., visual cue sparse, scenery, we propose a novel architecture built upon the structure from motion learner, which uses temporal pairs of jointly unrotated and stacked images for depth prediction. In order to increase the overall performance and to avoid blurred depth edges that lie in between the edges of the two input images, we integrate a geometric consistency loss into our pipeline. We assess the model’s ability to learn structure from motion by introducing a novel industry dataset whose perspective, orthogonal to the floor, contains only minimal visual cues. Through the evaluation with ground truth depth, we show that our proposed method outperforms the state of the art in difficult context-sparse environments. </p>", "Keywords": "Monocular depth; Deep learning; Visual SLAM; Computer vision", "DOI": "10.1007/s00138-022-01364-0", "PubYear": 2023, "Volume": "34", "Issue": "1", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Helsinki, Helsinki, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Nanoengineering, Aalto University, Espoo, Finland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Helsinki, Helsinki, Finland"}], "References": []}, {"ArticleId": 105718664, "Title": "An efficient hybrid swarm intelligence optimization algorithm for solving nonlinear systems and clustering problems", "Abstract": "<p>This article proposes a new hybrid swarm intelligence optimization algorithm called monarch butterfly optimization (MBO) algorithm with cuckoo search (CS) algorithm, named MBOCS, for optimization problems. MBO algorithm is known for its disability to discover feasible solutions over different runs because it may trap in the local minima. Also, CS is a recent powerful algorithm, while it may consume a large number of function evaluations to get the optimal solution, and this is one of the disadvantages of this algorithm. MBOCS can circumvent the disadvantages of MBO and CS algorithms. In this work, we integrate MBO with CS to improve the quality of solutions to solve various optimization problems, namely unconstraint benchmark functions, nonlinear systems and clustering problems. We solve fifteen of CEC’15 benchmark functions and compare our results with various algorithms such as group search algorithm, harmony search, particle swarm optimization and other hybrid algorithms in the literature. Moreover, we apply MBOCS on ten known nonlinear systems and eight real-world data from UCI. The results of MBOCS were compared with other known algorithms in the literature. The experimental results show that the proposed hybrid algorithm is a competitive and promising method for solving such optimization problems and outperforms other compared algorithms.</p>", "Keywords": "Hybrid meta-heuristic; Cuckoo search; Monarch butterfly optimization; Nonlinear system of equations; Clustering problem", "DOI": "10.1007/s00500-022-07780-8", "PubYear": 2023, "Volume": "27", "Issue": "13", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Statistics, Faculty of Science, Thompson Rivers University, Kamloops, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, Faculty of Science, Thompson Rivers University, Kamloops, Canada; Department of Mathematics, Faculty of Science, Al-Azhar University, Assiut Branch, Assiut, Egypt"}], "References": [{"Title": "Feature selection based on rough set approach, wrapper approach, and binary whale optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "573", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "An improved artificial algae algorithm integrated with differential evolution for job-shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "4", "Page": "1763", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Chaotic electromagnetic field optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "9", "Page": "9989", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 105718677, "Title": "LPW: an efficient data-aware cache replacement strategy for Apache Spark", "Abstract": "<p>Caching is one of the most important techniques for the popular distributed big data processing framework Spark. For this big data parallel computing framework, which is designed to support various applications based on in-memory computing, it is not possible to cache every intermediate result due to the memory size limitation. The arbitrariness of cache application programming interface (API) usage, the diversity of application characteristics, and the variability of memory resources constitute challenges to achieving high system execution performance. Inefficient cache replacement strategies may cause different performance problems such as long application execution time, low memory utilization, high replacement frequency, and even program execution failure resulting from out of memory. The cache replacement strategy currently adopted by Spark is the least recently used (LRU) strategy. Although LRU is a classical algorithm and has been widely used, it lacks consideration for the environment and workloads. As a result, it cannot achieve good performance under many scenarios. In this paper, we propose a novel cache replacement algorithm, least partition weight (LPW). LPW takes comprehensive consideration of different factors affecting system performance, such as partition size, computational cost, and reference count. The LPW algorithm was implemented in Spark and compared against the LRU as well as other state-of-the-art mechanisms. Our detailed experiments indicate that LPW obviously outperforms its counterparts and can reduce the execution time by up to 75% under typical workloads. Furthermore, the decreasing eviction frequency also shows the LPW algorithm can generate more reasonable predictions.</p>", "Keywords": "Spark; memory; cache replacement; least partition weight; data-aware", "DOI": "10.1007/s11432-021-3406-5", "PubYear": 2023, "Volume": "66", "Issue": "1", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Science, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Science, Beijing, China; Nanjing Institute of Software Technology, Nanjing, China; University of Chinese Academy of Sciences, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Science, Beijing, China; Nanjing Institute of Software Technology, Nanjing, China; University of Chinese Academy of Sciences, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Science, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "State Key Lab of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China"}], "References": []}, {"ArticleId": 105718678, "Title": "Pattern-based learning and control of nonlinear pure-feedback systems with prescribed performance", "Abstract": "<p>This article presents a novel pattern-based intelligent control scheme with prescribed performance (PP) for uncertain pure-feedback systems operating in multiple control situations (patterns). Based on PP, an observer-based adaptive neural network (NN) control approach, which not only achieves system stability and prescribed tracking control performance but also realizes accurate identification/learning of the unknown closed-loop dynamics via deterministic learning and uses only one NN unit, is proposed. Subsequently, the knowledge learned is utilized to construct high-performance candidate controllers for each control situation. Based on the transformed system and observer technique, accurate classification of the n th order systems under different control situations is achieved by requiring only one set of dynamic estimators, thereby significantly reducing the complexity of pattern recognition. Thus, sudden changes in the control situation can be rapidly recognized based on the minimum residual principle, with which the correct candidate controller is selected to achieve superior control performance. The simulation results verify the efficacy of the proposed scheme.</p>", "Keywords": "adaptive neural control (ANC); deterministic learning; neural networks (NNs); pattern-based control; high gain observer; pure-feedback systems", "DOI": "10.1007/s11432-021-3434-9", "PubYear": 2023, "Volume": "66", "Issue": "1", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Center for Intelligent Medical Engineering, School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Intelligent Medical Engineering, School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Intelligent Medical Engineering, School of Control Science and Engineering, Shandong University, Jinan, China"}], "References": [{"Title": "Rapid dynamical pattern recognition for sampling sequences", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Adaptive neural inverse optimal tracking control for uncertain multi-agent systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "584", "Issue": "", "Page": "31", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105718683, "Title": "Superconvergent Postprocessing of the Continuous Galerkin Time Stepping Method for Nonlinear Initial Value Problems with Application to Parabolic Problems", "Abstract": "<p>We introduce and analyze a very simple but efficient postprocessing technique for improving the accuracy of the continuous Galerkin (CG) time stepping method for nonlinear first-order initial value problems with smooth and singular solutions. The key idea of the postprocessing technique is to add a higher order Lobatto polynomial of degree \\(k+1\\) to the CG solution of degree k . We first establish global superconvergent error bounds for the postprocessed CG approximations over arbitrary time partitions, and as a by-product, it is shown that the convergence rates of the \\(L^2\\) -, \\(H^1\\) - and \\(L^\\infty \\) -estimates for the CG method with quasi-uniform meshes for smooth solutions are improved by one order. Moreover, for solutions with initial singularities, we prove that the optimal global error estimates and nodal superconvergent estimate can be obtained for the CG method with graded meshes, and after postprocessing, the convergence rates of the \\(L^2\\) -, \\(H^1\\) - and \\(L^\\infty \\) -estimates are also improved by one order. As an application, we apply the superconvergent postprocessing technique to the CG time discretization of nonlinear parabolic equations. Numerical examples are presented to verify the theoretical results.</p>", "Keywords": "Continuous Galerkin method; Initial value problem; Parabolic problem; Postprocessing technique; Superconvergence; 65L05; 65L60; 65L70; 65M60", "DOI": "10.1007/s10915-022-02086-1", "PubYear": 2023, "Volume": "94", "Issue": "2", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Shanghai Normal University, Shanghai, China"}], "References": []}, {"ArticleId": 105718688, "Title": "A hybrid model of bald eagle search and relevance vector machine for dam safety monitoring using long-term temperature", "Abstract": "Accurate simulation of temperature effect is a major challenge for computational (intelligent) prediction models used for monitoring health of high concrete dams, especially in regions with long freezing periods and distinct seasons, occasional extreme weather. A Hydrostatic-Temperature <sub>long-term</sub>-Time (HT<sub>L</sub>T) model was proposed for better temperature effect simulation using long-term measured environment temperatures and explored the influence of temperatures data sets of different time lengths on dam displacement prediction accuracy with the traditional Hydrostatic-Season-Time model as control. The Bald Eagle Search algorithm was coupled with the Relevance Vector Machine to form a novel hybrid model (BES-RVM) for predicting concrete gravity dam displacement response and comparison of nonlinear mapping capability between different kernel functions was conducted. Further optimized by Successive Projections Algorithm (SPA) for feature selection, sensitive features were selected from the proposed HT<sub>L</sub>T variable sets to improve the prediction model’s accuracy. The prediction model was experimented on a real concrete dam with results showing that the BES-RVM model gave excellent prediction performance. Using the HT<sub>L</sub>T variable sets significantly reduced the prediction errors and the best performed result came from variables of the two-year long temperatures data. The SPA optimized BES-RVM model largely brought down the dimension and the collinearity of the input variables and further improved the prediction performance.", "Keywords": "Structural health monitoring ; Concrete dam displacement prediction ; Bald eagle search algorithm ; Relevance vector machine regression ; Successive projections algorithm ; Temperature simulation", "DOI": "10.1016/j.aei.2022.101863", "PubYear": 2023, "Volume": "55", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Hydraulic Engineering, Faculty of Infrastructure Engineering, Dalian University of Technology, Dalian 116024, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Hydraulic Engineering, Faculty of Infrastructure Engineering, Dalian University of Technology, Dalian 116024, PR China;College of Water Conservancy and Hydropower Engineering, Hohai University, Nanjing 210098, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Hydraulic Engineering, Faculty of Infrastructure Engineering, Dalian University of Technology, Dalian 116024, PR China;Corresponding author"}], "References": [{"Title": "Novel meta-heuristic bald eagle search optimisation algorithm", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; B. <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "2237", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Multi-kernel optimized relevance vector machine for probabilistic prediction of concrete dam displacement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "1943", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 105718706, "Title": "Data for “Oxidative stress is inhibited by plant-based supplements: a quantitative lipidomic analysis of antioxidant activity and lipid compositional change”", "Abstract": "Raw data obtained by ultra-high pressure liquid chromatography–mass spectrometry, and processed lipid compositional data are presented alongside detailed methodology. Data were obtained as bovine liver lipid extract oxidizes, initiated by 2,2′-Azobis(2-amidinopropane) dihydrochloride, at 0, 6 and 24 h post initiation. Lipid oxidation data in the presence and absence of some supplements with antioxidant properties was obtained. The supplements used were grape seed extract, pine bark extract, milk thistle extract, hawthorn extract and turmeric extract.", "Keywords": "Oxidative stress ; Membrane curvature ; Antioxidant study ; Grape seed extract ; Pine bark extract ; Milk thistle extract ; Hawthorn extract ; Turmeric extract", "DOI": "10.1016/j.dib.2022.108879", "PubYear": 2023, "Volume": "46", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Stress and Age-Related Disease, University of Brighton, BN2 4GL"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centre for Stress and Age-Related Disease, University of Brighton, BN2 4GL"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Stress and Age-Related Disease, University of Brighton, BN2 4GL;Corresponding author"}], "References": []}, {"ArticleId": 105718707, "Title": "An efficient heterogeneous authenticated key agreement scheme for unmanned aerial vehicles", "Abstract": "Unmanned aerial vehicle (UAV) technology is becoming more and more popular recently due to the rapid development of Internet of things (IoT) and network technology. It has gradually expanded from military field to civil field because of the convenience brought by UAVs. However, the communication of UAV is based on open wireless network, which makes it vulnerable to varieties of attacks. Besides, UAVs are generally considered as mobile devices with limited resources. It is necessary to ensure the security of UAV communication and reduce the computation overhead and communication cost on UAV’s side as much as possible. Authenticated key agreement (AKA) scheme is a proper way to meet the above requirements. It enables an UAV and a ground station (GS) to share a session key. Then they can use the session key as a symmetric key and communicate securely through symmetric encryption, which is much less expensive than asymmetric encryption. In this paper, we propose a heterogeneous authenticated key agreement (HAKA) scheme for an UAV to communicate with a GS, in which the UAV belongs to identity-based cryptosystem (IBC) and the GS belongs to public key infrastructure (PKI). Through rigorous security analysis, we show that the proposed scheme is provably secure. Moreover, the comparative experimental results show that our scheme is the most efficient and suitable for UAVs with limited resources.", "Keywords": "Authenticated key agreement ; Heterogeneous cryptosystem ; Unmanned aerial vehicle ; IBC ; PKI", "DOI": "10.1016/j.sysarc.2022.102821", "PubYear": 2023, "Volume": "136", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "Xiangyu Pan", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Second Research Institute of Civil Aviation Administration of China (CAAC), Chengdu 610041, China"}, {"AuthorId": 3, "Name": "Fagen Li", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China;Corresponding author"}], "References": [{"Title": "A lightweight authentication and key agreement scheme for Internet of Drones", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "455", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 105718747, "Title": "An application of optimal control in medical systems: optimal investment strategy in doctors", "Abstract": "<p>Health care is ever more important with the aging population and with the increased awareness of the importance of the medical systems due to the corona crisis that showed the capacity of the health care infrastructure, especially in terms of numbers of health care personnel such as doctors, was not sufficient. Assuming that the number of doctors per patient is one of the determinants of patient satisfaction, optimal investments in new doctors, specialist doctors and foreign doctors are analyzed. Optimal Control Theory is employed to determine the optimal investment strategy for new doctors (new graduates), specialists and foreign doctors to maximize the net (of costs) patient satisfaction over a fixed time horizon. It is found that a nation with an insufficient number of total doctors and specialist doctors at the beginning of the planning horizon should increase the investment in new doctors as a quadratic function of time, increase the local specialist doctors linearly, while employing foreign doctors as to equate their cost to the marginal satisfaction of patients.</p><p>© The Author(s), under exclusive licence to Springer-Verlag GmbH Austria, part of Springer Nature 2023, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Health care applications;Investment in doctors;Medical systems", "DOI": "10.1007/s13721-022-00408-9", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25861, "JournalTitle": "Network Modeling Analysis in Health Informatics and Bioinformatics", "ISSN": "2192-6662", "EISSN": "2192-6670", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Management, Halic University, Beyoğlu, Istanbul, Turkey."}, {"AuthorId": 2, "Name": "Ebru Geçici", "Affiliation": "Industrial Engineering Department, Yildiz Technical University, Beşiktaş, Istanbul, Turkey."}], "References": [{"Title": "An optimal control policy in fighting COVID-19 and infectious diseases", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "109289", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105718813, "Title": "The DNN-based DBP scheme for nonlinear compensation and longitudinal monitoring of optical fiber links", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.dcan.2022.12.020", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Yuyuan Gao", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105718830, "Title": "Sum-throughput maximization for UAV-aided energy-harvesting WBANs: Hovering altitude design, power control and time allocation", "Abstract": "UAV-aided energy-harvesting WBANs where sensor nodes can harvest RF energy transmitted by a UAV have attracted considerable attention. However, how to maximize the sum-throughput of the sensor nodes with cross-tier interferences from satellite and cellular systems has not been addressed. In order to bridge this gap, we devote this paper to developing two schemes with the fixed or variable duration of the energy-harvesting phase. Specifically, we formulate two non-convex optimization problems corresponding to the two schemes, and transform them to tractable subproblems, and accordingly two algorithms are proposed named HA-PC-TA and UAVPC-TA. In particular, we derive the closed-form optimum transmit power and time allocation for the sensor nodes and obtain the optimum UAV's hovering altitude in HA-PC-TA. We reveal the relationship between the optimum parameters and the allowed maximum transmit power of the VAU in UAVPC-TA. Finally, the simulations are conducted to validate the effectiveness of our proposed solutions.", "Keywords": "Power control; Time allocation; Energy harvesting", "DOI": "10.1016/j.dcan.2022.12.021", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Capital Normal University, Beijing, 100048, China;Contributed equally to this work. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "College of Information Engineering, Capital Normal University, Beijing, 100048, China;Contributed equally to this work. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Capital Normal University, Beijing, 100048, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Capital Normal University, Beijing, 100048, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Zhao", "Affiliation": "College of Information Engineering, Capital Normal University, Beijing, 100048, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "China Academy of Launch Vehicle Technology, Beijing, 100076, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Academy of Launch Vehicle Technology, Beijing, 100076, China"}], "References": []}, {"ArticleId": 105718913, "Title": "Novel algorithms for pair and pixel selection and atmospheric error correction in multitemporal InSAR", "Abstract": "Entering the SAR&#x27;s golden era began with the launch of Sentinel-1A/B satellites in 2014 and 2016 with 6–12 day revisit time, much larger stacks of high-resolution SAR images are available over a given area to perform time series analysis. Algorithms that deal with large stack sizes face several challenges, including interferometric phase quality degradation due to signal decorrelations, phase closure error caused by applied multilooking, and tropospheric phase delay. Here, we present an improved SBAS-type algorithm suitable for processing a large stack of SAR images at an arbitrary resolution. We develop a new pair selection strategy that applies dyadic downsampling combined with widely used Delaunay Triangulation to identify an optimal set of interferometric pairs that minimize systematic errors due to short-lived signals and closure errors. We develop and apply a novel statistical framework that selects elite pixels accounting for distributed and permanent scatterers. Also, we implement a new tropospheric error correction that takes advantage of smooth 2D splines to identify and remove error components with fractal-like structures. We demonstrate the effectiveness of the algorithms by applying them to 3 large datasets of Sentinel-1 SAR images measuring non-linear surface deformation over various terrains. Compared with independent GNSS observations, we find that over the rural/natural terrains adjacent to San Andreas fault in southern California, our approach yields a standard deviation of 0.48 cm for time series differences in both ascending and descending tracks. While in urban areas, such as Los Angeles, standard deviation difference with GNSS time series is 0.30 cm.", "Keywords": "SBAS ; Sentinel-1 ; Atmospheric delay ; InSAR time series ; Pair selection ; Pixel selection ; Smooth 2D splines ; Delaunay triangulation", "DOI": "10.1016/j.rse.2022.113447", "PubYear": 2023, "Volume": "286", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Geosciences, Virginia Polytechnic Institute and State University, Blacksburg, VA, USA;Corresponding author"}, {"AuthorId": 2, "Name": "Man<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geosciences, Virginia Polytechnic Institute and State University, Blacksburg, VA, USA"}], "References": []}, {"ArticleId": 105718952, "Title": "A prospective evaluation of breast thermography enhanced by a novel machine learning technique for screening breast abnormalities in a general population of women presenting to a secondary care hospital", "Abstract": "Objective <p>Artificial intelligence-enhanced breast thermography is being evaluated as an ancillary modality in the evaluation of breast disease. The objective of this study was to evaluate the clinical performance of Thermalytix, a CE-marked, AI-based thermal imaging test, with respect to conventional mammography.</p> Methods <p>A prospective, comparative study performed between 15 December 2018 and 06 January 2020 evaluated the performance of Thermalytix in 459 women with both dense and nondense breast tissue. Both symptomatic and asymptomatic women, aged 30–80 years, presenting to the hospital underwent Thermalytix followed by 2-D mammography and appropriate confirmatory investigations to confirm malignancy. The radiologist interpreting the mammograms and the technician using the Thermalytix tool were blinded to the others' findings. The statistical analysis was performed by a third party.</p> Results <p> A total of 687 women were recruited, of whom 459 fulfilled the inclusion criteria. Twenty-one malignancies were detected (21/459, 4.6%). The overall sensitivity of Thermalytix was 95.24% (95% CI, 76.18–99.88), and the specificity was 88.58% (95% CI, 85.23–91.41). In women with dense breasts ( n = 168, 36.6%), the sensitivity was 100% (95% CI, 69.15–100), and the specificity was 81.65% (95% CI, 74.72–87.35). Among these 168 women, 37 women (22%) were reported as BI-RADS 0 on mammography; in this subset, the sensitivity of Thermalytix was 100% (95% CI, 69.15–100), and the specificity was 77.22% (95% CI, 69.88–83.50). </p> Conclusion <p>Thermalytix showed acceptable sensitivity and specificity with respect to mammography in the overall patient population. Thermalytix outperformed mammography in women with dense breasts and those reported as BI-RADS 0.</p>", "Keywords": "breast density; Mammography; Prospective Studies; artificial intelligence; Thermography", "DOI": "10.3389/frai.2022.1050803", "PubYear": 2023, "Volume": "5", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Radiology Services, Max Super Speciality Hospital, Saket, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Clinical Affairs, Niramai Health Analytix Pvt. Ltd., India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Clinical Affairs, Niramai Health Analytix Pvt. Ltd., India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Radiology Services, Max Super Speciality Hospital, Saket, India"}, {"AuthorId": 5, "Name": "Mathukumalli Vidyasagar", "Affiliation": "SERB National Science Chair, Indian Institute of Technology Hyderabad, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Affairs, Niramai Health Analytix Pvt. Ltd., India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Affairs, Niramai Health Analytix Pvt. Ltd., India"}], "References": [{"Title": "Automatic region of interest segmentation for breast thermogram image classification", "Authors": "<PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "72", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 105719325, "Title": "Correction to: Advantages and disadvantages of (dedicated) model transformation languages", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10664-022-10264-w", "PubYear": 2023, "Volume": "28", "Issue": "2", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ulm University, Ulm, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ulm University, Ulm, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ulm University, Ulm, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ulm University, Ulm, Germany"}], "References": []}, {"ArticleId": 105719330, "Title": "The swarm within the labyrinth: planar construction by a robot swarm", "Abstract": "<p>We propose an approach to guide simple robots through arbitrary environments while engaged in a planar construction task. The intent is to mitigate spatial interference between robots which often occurs in distributed robots operating on the same set of objects. Information can also be encoded within the structure of the labyrinth to provide auxiliary guidance, such as the direction in which objects should be incrementally moved to reach their goals. We show results in a simulation environment and study the impact of the number of robots deployed. We also provide validation on physical robots.</p>", "Keywords": "Swarm robotics; Collective construction; Planar construction", "DOI": "10.1007/s10015-022-00849-5", "PubYear": 2023, "Volume": "28", "Issue": "1", "JournalId": 4137, "JournalTitle": "Artificial Life and Robotics", "ISSN": "1433-5298", "EISSN": "1614-7456", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Department of Electrical and Computer Engineering, Memorial University of Newfoundland, St. John’s, Canada"}], "References": [{"Title": "Repulsive Curves", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 105719360, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0890-5401(23)00004-4", "PubYear": 2023, "Volume": "290", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [], "References": []}, {"ArticleId": 105719500, "Title": "Simultaneous Laplacian embedding and subspace clustering for incomplete multi-view data", "Abstract": "In many real multi-view data, some views often lose some information, resulting in incomplete views. It is a challenging task to extract and fuse valuable information from the multi-view data with incomplete views for improving clustering performance. Incomplete multi-view clustering (IMC) is designed to fully mine patterns in data to reduce the negative impact of missing views. Many previous IMC methods are committed to discovers a consensus representation shared by all views. Nevertheless, this representation could severely deviate from the inherent representation of original data due to the loss of information caused by incomplete views. Besides, most existing spectral clustering-based multi-view subspace clustering independently performs similarity graph learning, Laplacian embedding, and discrete indicator matrix learning. This multi-step strategy might result in sharp performance degradation. In this paper, we propose a novel IMC method, referred to as Simultaneous Laplacian Embedding and Subspace Clustering (SLESC), to address the above issues. Specifically, in the paradigm for data self-representation, the proposed SLESC method learns a similarity graph for each view instead of learning a consensus graph for invoking traditional spectral clustering. Similarity graph learning, Laplacian embedding learning, weighting for each view, and discrete indicator matrix learning are seamlessly incorporated into the unified framework. The joint optimal clustering outcomes are therefore possible. Experimental results on real-world datasets in the IMC task demonstrate the effectiveness of the proposed method compared with state-of-the-art baselines.", "Keywords": "", "DOI": "10.1016/j.knosys.2022.110244", "PubYear": 2023, "Volume": "262", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Guangdong University of Foreign Studies, Guangzhou 510000, China;Department of Computer and Information Science, University of Macau, Macao Special Administrative Region of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, University of Macau, Macao Special Administrative Region of China;Corresponding author"}], "References": [{"Title": "Nonnegative self-representation with a fixed rank constraint for subspace clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "127", "JournalTitle": "Information Sciences"}, {"Title": "Tensorized Multi-view Subspace Representation Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "Page": "2344", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "One-step multi-view subspace clustering with incomplete views", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "438", "Issue": "", "Page": "290", "JournalTitle": "Neurocomputing"}, {"Title": "Slime Mould Algorithm-Based Tuning of Cost-Effective Fuzzy Controllers for Servo Systems", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>-<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1042", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Multi-view spectral clustering by simultaneous consensus graph learning and discretization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107632", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Auto-weighted collective matrix factorization with graph dual regularization for multi-view clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Lingjiang Li", "PubYear": 2023, "Volume": "260", "Issue": "", "Page": "110145", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 105719503, "Title": "Simultaneous quantification of Zr, Mo, U, Np and Pu in technological solutions of spent nuclear fuel reprocessing with a potentiometric multisensor system", "Abstract": "The chemical analysis of spent nuclear fuel (SNF) reprocessing is a very challenging research field due to the high radioactivity of process streams and their complex composition. The process managing requires thorough chemical analysis to ensure safe and efficient performance. Traditional ways of chemical control of SNF reprocessing are based on sampling methods and involve long and tedious procedures, failing to provide immediate information on the process status. Recent literature suggests that potentiometric multisensor systems can be employed for on-line control. However, up to now these systems were only studied in very simple model media. This work reports on the potentiometric multisensor array applied for the first time for the simultaneous quantification of the content of actinides (uranium, plutonium, neptunium) and several other analytes (zirconium, molybdenum and nitric acid) in the complex samples obtained from the pilot extraction unit for SNF reprocessing. The same samples were also analyzed by UV-Vis spectrometry (another prospective method for on-line control) and the analytical performance of these two methods was compared using established analytical figures of merit (sensitivity and analytical sensitivity metrics) for multivariate calibration. In spite of the extremely challenging analytical task, both simple methods provide the characteristics suitable for real industrial applications.", "Keywords": "Spent nuclear fuel ; Process analysis ; Actinides ; Potentiometric sensor array ; Optical spectrometry", "DOI": "10.1016/j.snb.2023.133315", "PubYear": 2023, "Volume": "380", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Khlopin Radium Institute, 2 Murinsky Prospect, 28, Saint‑Petersburg 194021, Russia"}, {"AuthorId": 2, "Name": "Marina Agafonova-Moroz", "Affiliation": "Khlopin Radium Institute, 2 Murinsky Prospect, 28, Saint‑Petersburg 194021, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Khlopin Radium Institute, 2 Murinsky Prospect, 28, Saint‑Petersburg 194021, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Khlopin Radium Institute, 2 Murinsky Prospect, 28, Saint‑Petersburg 194021, Russia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Khlopin Radium Institute, 2 Murinsky Prospect, 28, Saint‑Petersburg 194021, Russia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Chemistry, Saint-Petersburg State University, Peterhof, Universitetsky Prospect, 26, Saint‑Petersburg 198504, Russia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Chemistry, Saint-Petersburg State University, Peterhof, Universitetsky Prospect, 26, Saint‑Petersburg 198504, Russia"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Analytical Chemistry, IQUIR – CONICET, Universidad Nacional de Rosario, Suipacha 531, Rosario 2000, Argentina"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, Sharif University of Technology, P.O. Box 11155–9516, Tehran, Iran"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Institute of Chemistry, Saint-Petersburg State University, Peterhof, Universitetsky Prospect, 26, Saint‑Petersburg 198504, Russia;Corresponding author"}], "References": [{"Title": "Development of polyvinyl chloride (PVC)-based highly efficient potentiometric sensors containing two benzene-centered tripodal diglycolamides as ionophores", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "320", "Issue": "", "Page": "127961", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105719565, "Title": "Reliable Monte Carlo localization for mobile robots", "Abstract": "<p>Reliability is a key factor for realizing safety guarantee of fully autonomous robot systems. In this paper, we focus on reliability in mobile robot localization. Monte Carlo localization (MCL) is widely used for mobile robot localization. However, it is still difficult to guarantee its safety because there are no methods determining reliability for MCL estimate. This paper presents a novel localization framework that enables robust localization, reliability estimation, and quick relocalization, simultaneously. The presented method can be implemented using a similar estimation manner to that of MCL. The method can increase localization robustness to environment changes by estimating known and unknown obstacles while performing localization; however, localization failure of course occurs by unanticipated errors. The method also includes a reliability estimation function that enables a robot to know whether localization has failed. Additionally, the method can seamlessly integrate a global localization method via importance sampling. Consequently, quick relocalization from a failure state can be realized while mitigating noisy influence of global localization. We conduct three types of experiments using wheeled mobile robots equipped with a two-dimensional LiDAR. Results show that reliable MCL that performs robust localization, self-failure detection, and quick failure recovery can be realized.</p>", "Keywords": "localization;mobile robots;modeling;probabilistic;reliability", "DOI": "10.1002/rob.22149", "PubYear": 2023, "Volume": "40", "Issue": "3", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate Department of Aerospace Engineering, Graduate School of Engineering Nagoya University Nagoya Japan;LOCT Co., Ltd. Nagoya Japan"}], "References": []}, {"ArticleId": 105719642, "Title": "Bayesian-optimized Gaussian process-based fault classification in industrial processes", "Abstract": "The integration of data-driven modeling techniques in machine learning applications, such as multiclass classification, has resulted in robust classifier designs. However, one of the main drawbacks of this approach has been the rising complexity of modeling as the number of classes in the system increases, which may eventually make the overall design of the classifier unfavorable regardless of the expected performance. In this paper, we will discuss the design of a novel logic-based Bayesian-optimized Gaussian process (BOGP) classifier that aims to minimize the number of independent empirical models needed to accurately diagnose multiple distinct fault classes in industrial process. Moreover, the fault classification accuracy of the BOGP classifier is compared to the respective performances of other methods published in literature, and the Tennessee Eastman process is used as a benchmark case study for all methods.", "Keywords": "", "DOI": "10.1016/j.compchemeng.2022.108126", "PubYear": 2023, "Volume": "170", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chemical Engineering Department, Texas A&M University at Qatar, 23874, Education City, Doha, Qatar;Artie <PERSON> Department of Chemical Engineering, Texas A&M University, College Station, TX 77843, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>ie <PERSON>rin Department of Chemical Engineering, Texas A&M University, College Station, TX 77843, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Texas A&M University at Qatar, 23874, Education City, Doha, Qatar"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Chemical Engineering Department, Texas A&M University at Qatar, 23874, Education City, Doha, Qatar;Corresponding author"}], "References": [{"Title": "Multiclass data classification using fault detection-based techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "106786", "JournalTitle": "Computers & Chemical Engineering"}]}]