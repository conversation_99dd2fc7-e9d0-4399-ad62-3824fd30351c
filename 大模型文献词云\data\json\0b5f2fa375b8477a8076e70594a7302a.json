[{"ArticleId": 118820551, "Title": "A unified Jacobi-Ritz-spectral BEM for vibro-acoustic behavior of spherical shell", "Abstract": "In order to solve the vibration and acoustic characteristics of spherical shell in light fluid environment, based on Jacobi-Ritz-spectral BEM, a unified analysis formula for acoustic vibration of spherical shell under arbitrary boundary conditions is established. Based on the First-order shear deformation theory (FSDT) and domain decomposition method (DDM), the theoretical model of spherical shell structure is established. The improved <PERSON><PERSON> polynomial is innovatively used to construct the displacement tolerance function of the spherical shell. Based on the spectral Ki<PERSON>hoff<PERSON> integral formula, the theoretical model of the acoustic fluid outside the spherical shell is established. The special form of Jacobi polynomial <PERSON><PERSON><PERSON><PERSON><PERSON> polynomial is used to describe the excitation sound pressure on the spherical shell segment, so as to ensure that the generalized coordinates of the structure can be perfectly matched with the acoustic boundary element nodes. In addition, the integration along the meridian of the shell is used to control the movement of the fluid, which simplifies the surface integration. The CHIEF method is used to solve the problem of non-unique solution of acoustic variables of rotary structure. Compared with the published literature, numerical simulation results and experimental results, the proposed method has higher calculation accuracy. In addition, based on this method, the influence of boundary conditions, geometric dimensions and other factors on the acoustic and vibration characteristics of spherical shells is discussed, which accumulates data for analyzing the acoustic and vibration behavior of spherical shells.", "Keywords": "", "DOI": "10.1016/j.camwa.2024.10.031", "PubYear": 2024, "Volume": "176", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "Haichao Li", "Affiliation": "College of Shipbuilding Engineering, Harbin Engineering University, Harbin 150001, PR China;Naval Research Institute, Beijing 100161, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Shipbuilding Engineering, Harbin Engineering University, Harbin 150001, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Pang", "Affiliation": "College of Shipbuilding Engineering, Harbin Engineering University, Harbin 150001, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Shipbuilding Engineering, Harbin Engineering University, Harbin 150001, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Shipbuilding Engineering, Harbin Engineering University, Harbin 150001, PR China"}], "References": [{"Title": "A study on the dynamic characteristics of the stiffened coupled plate with the effect of the dynamic vibration absorbers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "168", "Issue": "", "Page": "120", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 118820559, "Title": "Utilizing AI for Health Promotion and Disease Prevention", "Abstract": "<p>The emergence of advanced AI technologies such as expert systems, fuzzy logic, neural networks, and genetic algorithms can directly address people's health needs by providing personalized advice to users while at the same time collecting valuable data related to aspects of diseases and treatments. These technologies have been in use for quite some time and have evolved to the point where they have matured into practical decision support systems that can be used to assist people in health management and promotion efforts. This chapter presents a survey of the current progress and ongoing research in the area of health-related AI technology and end-user products. With the ever-growing trajectory of technology and the increasing demand to reduce healthcare costs while providing better quality healthcare, employing AI as part of or adjunct to medical processes will become increasingly important from both economic and social perspectives. The classical problems of the healthcare industry include providing people with new and better treatments, more effective ways of preventing, detecting, and combating multifaceted chronic diseases, and ways of caring for the ever-increasing numbers of senior citizens.  </p>", "Keywords": "Advanced AI technologies; Expert systems; Fuzzy logic; Neural networks; Genetic algorithms; Personalized health advice; Disease treatment data; Decision support systems; Health management; Health promotion; AI in healthcare; End-user health products; Healthcare technology; Reducing healthcare costs; Quality healthcare; Medical processes; Chronic disease prevention; Senior citizen care; Multifaceted disease detection; Economic and social healthcare perspectives", "DOI": "10.18535/ijecs/v10i12.4643", "PubYear": 2021, "Volume": "10", "Issue": "12", "JournalId": 17925, "JournalTitle": "International Journal Of Engineering And Computer Science", "ISSN": "", "EISSN": "2319-7242", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Architect II- Software Testing"}], "References": []}, {"ArticleId": *********, "Title": "Measuring Epistemic Trust: Towards a New Lens for Democratic Legitimacy, Misinformation, and Echo Chambers", "Abstract": "<p>Trust is crucial for the functioning of complex societies, and an important concern for CSCW. Our purpose is to use research from philosophy, social science, and CSCW to provide a novel account of trust in the 'post-truth' era. Testimony, from one speaker to another, underlies many social systems. Epistemic trust, or testimonial credibility, is the likelihood to accept a speaker's claim due to beliefs about their competence or sincerity. Epistemic trust is closely related to several 'pathological epistemic phenomena': democratic (il)legitimacy, the spread of misinformation, and echo chambers. To the best of our knowledge, this theoretical contribution is novel in the field of social computing. We further argue that epistemic trust is no philosophical novelty: it is measurable. Weakly supervised text classification approaches achieve F_1 scores of around 80 to 85 per cent on detecting epistemic distrust. This is also, to the best of our knowledge, a novel task in natural language processing. We measure expressions of epistemic distrust across 954 political communities on Reddit. We find that expressions of epistemic distrust are relatively rare, although there are substantial differences between communities. Conspiratorial communities and those focused on controversial political topics tend to express more distrust. Communities with strong epistemic norms enforced by moderation are likely to express low levels. While we find users to be an important potential source of contagion of epistemic distrust, community norms appear to dominate. It is likely that epistemic trust is more useful as an aggregated risk factor. Finally, we argue that policymakers should be aware of epistemic trust considering their reliance on legitimacy underwritten by testimony.</p>", "Keywords": "", "DOI": "10.1145/3687001", "PubYear": 2024, "Volume": "8", "Issue": "CSCW2", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Illinois Urbana-Champaign, Urbana, Illinois, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Illinois Urbana-Champaign, Urbana, Illinois, USA"}], "References": [{"Title": "\"I run the world's largest historical outreach project and it's on a cesspool of a website.\" Moderating a Public Scholarship Site on Reddit: A Case Study of r/AskHistorians", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Political Ecologies of Participation", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "<PERSON><PERSON><PERSON>", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Exploring Lightweight Interventions at Posting Time to Reduce the Sharing of Misinformation on Social Media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "A Transformer-based Framework for Neutralizing and Reversing the Political Polarity of News Articles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "'Walking Into a Fire Hoping You Don't Catch': Strategies and Designs to Facilitate Cross-Partisan Online Discussions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Others Are to Blame: Whom People Consider Responsible for Online Misinformation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "The Effects of AI-based Credibility Indicators on the Detection and Spread of Misinformation under Social Influence", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Human and Technological Infrastructures of Fact-checking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Leveraging Structured Trusted-Peer Assessments to Combat Misinformation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Help Me #DebunkThis: Unpacking Individual and Community's Collaborative Work in Information Credibility Assessment", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Adherence to Misinformation on Social Media Through Socio-Cognitive and Group-Based Processes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Who's in the Crowd Matters: Cognitive Factors and Beliefs Predict Misinformation Assessment Accuracy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Reviewing Interventions to Address Misinformation: The Need to Expand Our Vision Beyond an Individualistic Focus", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 118820582, "Title": "Note Extraction and Recognition Analysis Based on Music Melody Features", "Abstract": "This paper extracted and recognized the notes in the music based on the features of the music melody. Firstly, the melodic features and Mel-frequency cepstral (MFCC) features were extracted from the music signal and then combined. A convolutional neural network (CNN) was used as a classifier for note classification and recognition in the music signal. The CNN adopted a structure with three convolutional layers and three pooling layers. The melody features and MFCC features were used to extract convolutional features through convolution kernels in the convolutional layers, followed by compressing these features in the pooling layers. Finally, the note recognition results were outputted in the output layer. Then, simulation experiments were performed using a self-built music library. The performance of the algorithm was tested under different MFCC feature dimensions and CNN activation functions. The algorithm was also compared with the dynamic time warping (DTW) algorithm and the CNN algorithm without music melody features. The results showed that the proposed algorithm had the best performance when the MFCC feature dimension was set to 24, and the CNN activation function was sigmoid; under such conditions, the F-measure was 96.7%.  The performance of the proposed algorithm was the best, regardless of whether it recognized the single-note or multi-note music. The precision for recognizing single notes was 98.3%, the recall rate was 96.5%, and the F-measure was 97.4%. For recognizing multiple notes, the corresponding values were 93.0%, 92.5%, and 92.7%, respectively. However, the performance of the three algorithms was reduced when recognizing multi-note music.", "Keywords": "", "DOI": "10.31449/inf.v48i18.6184", "PubYear": 2024, "Volume": "48", "Issue": "18", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118820584, "Title": "Analyzing the Strategy of Propaganda using Inverse Reinforcement Learning: Evidence from the 2022 Russian Invasion of Ukraine", "Abstract": "<p>The 2022 Russian invasion of Ukraine was accompanied by a large-scale, pro-Russian propaganda campaign on social media. However, the strategy behind the dissemination of propaganda has remained unclear, particularly how the online discourse was strategically shaped by the propagandists' community. Here, we analyze the strategy of the Twitter/X community using an inverse reinforcement learning (IRL) approach. Specifically, IRL allows us to model online behavior as a Markov decision process, where the goal is to infer the underlying reward structure that guides propagandists when interacting with users with a supporting or opposing stance toward the invasion. Thereby, we aim to understand empirically whether and how between-user interactions are strategically used to promote the proliferation of Russian propaganda. For this, we leverage a large-scale dataset with 349,455 posts with pro-Russian propaganda from 132,131 users. We show that bots and humans follow a different strategy: bots respond predominantly to pro-invasion messages, suggesting that they seek to drive virality; in contrast, messages indicating opposition primarily elicit responses from humans, suggesting that they tend to engage in critical discussions. To the best of our knowledge, this is the first study analyzing the strategy behind propaganda from the 2022 Russian invasion of Ukraine through the lens of IRL.</p>", "Keywords": "", "DOI": "10.1145/3686930", "PubYear": 2024, "Volume": "8", "Issue": "CSCW2", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LMU Munich & Munich Center for Machine Learning (MCML), Munich, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LMU Munich & Munich Center for Machine Learning (MCML), Munich, Germany"}], "References": [{"Title": "How disinformation operations against Russian opposition leader <PERSON> influence the international audience on Twitter", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "80", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Russian propaganda on social media during the 2022 invasion of Ukraine", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "EPJ Data Science"}]}, {"ArticleId": 118820661, "Title": "GauLoc: 3D Gaussian Splatting‐based Camera Relocalization", "Abstract": "<p> 3D Gaussian Splatting (3DGS) has emerged as a promising representation for scene reconstruction and novel view synthesis for its explicit representation and real‐time capabilities. This technique thus holds immense potential for use in mapping applications. Consequently, there is a growing need for an efficient and effective camera relocalization method to complement the advantages of 3DGS. This paper presents a camera relocalization method, namely GauLoc, in a scene represented by 3DGS. Unlike previous methods that rely on pose regression or photometric alignment, our proposed method leverages the differential rendering capability provided by 3DGS. The key insight of our work is the proposed implicit featuremetric alignment, which effectively optimizes the alignment between rendered keyframes and the query frames, and leverages the epipolar geometry to facilitate the convergence of camera poses conditioned explicit 3DGS representation. The proposed method significantly improves the relocalization accuracy even in complex scenarios with large initial camera rotation and translation deviations. Extensive experiments validate the effectiveness of our proposed method, showcasing its potential to be applied in many real‐world applications. Source code will be released at https://github.com/xinzhe11/GauLoc . </p>", "Keywords": "", "DOI": "10.1111/cgf.15256", "PubYear": 2024, "Volume": "43", "Issue": "7", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Automation Chinese Academy of Sciences  China"}, {"AuthorId": 2, "Name": "Chengkai Dai", "Affiliation": "College of Mechanical Engineering and Automation Huaqiao University  China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering Beijing Institute of Technology  China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Baidu Inc."}], "References": [{"Title": "NeRF", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "1", "Page": "99", "JournalTitle": "Communications of the ACM"}, {"Title": "3D Gaussian Splatting for Real-Time Radiance Field Rendering", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 118820665, "Title": "Isotherm and kinetic data for adsorption of butylparaben onto biochars derived from fique bagasse", "Abstract": "<p>Parabens are a family of p-hydroxybenzoic acid esters used as preservatives in food, cosmetics, and pharmaceuticals products, parabens are pollutants that have been detected in the environment at low concentrations. Recent research describes that parabens can cause health alterations. Alternatives for contaminant removal include the adsorption process, which can use materials that are abundant, inexpensive and susceptible to modifications. In this sense, biochar can be an option for obtaining materials that can be used for pollutants removal. In this report, we present the use of biochar derived from fique bagasse as an adsorbent for the removal of butylparaben (BP) from water. This paper examines how pH variations influence the adsorption capacity of butylparaben onto fique bagasse biochar, it was determined that the highest adsorption capacity occurred at pH 6. Furthermore, pseudo-first order, pseudo-second order, <PERSON> and <PERSON>, and Boyd models were used to study the adsorption kinetics. As a result, the pseudo-first order model was favorable for six carbonaceous materials studied. Finally, Fr<PERSON>ndlich, Langmuir, and Sips adsorption isotherm models were investigated; the Langmuir model was the best for adsorption isotherm data. The maximum adsorption capacity of BP for FB-3Na was 20.9520 mg g<sup>-1</sup>.</p><p>© 2024 The Author(s).</p>", "Keywords": "Agro-industrial residue;Biomass;Carbonaceous material;Emerging pollutant;Remotion", "DOI": "10.1016/j.dib.2024.111113", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Química, Facultad de Ciencias Exactas y Naturales, Grupo de Estudios Ambientales en Agua y Suelo, Universidad de Caldas, Calle 65 No. 26 - 10, Manizales, Caldas 170004, Colombia."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Química, Facultad de Ciencias Exactas y Naturales, Grupo de Estudios Ambientales en Agua y Suelo, Universidad de Caldas, Calle 65 No. 26 - 10, Manizales, Caldas 170004, Colombia."}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON>", "Affiliation": "Departamento de Química, Facultad de Ciencias Exactas y Naturales, Grupo de Estudios Ambientales en Agua y Suelo, Universidad de Caldas, Calle 65 No. 26 - 10, Manizales, Caldas 170004, Colombia."}], "References": []}, {"ArticleId": 118820918, "Title": "A new cause-mechanism independence estimation based cross-domain learning method for machining deformation prediction", "Abstract": "Monitoring data-based machining deformation prediction is fundamental for accurate deformation control and product quality guarantee. For problems where involved unobservable variables like residual stress that can lead to data distribution bias, causal cross-domain learning methods have prominent advantages over other pure data-driven methods by shifting cause distributions and mechanisms. However, existing causal methods are based on the hypothesis that cause and mechanism are independent, which ignores the corresponding changes of mechanism across domains and can limit accuracies. This paper proposes a new causal cross-domain learning method based on cause-mechanism independence estimation, where the hypothesis is broken by taking the dependence of cause and mechanism into consideration. A cause-mechanism independence estimator is established by introducing the structural integral of mechanism derivative multiplies cause distribution, and the estimation value can measure the cross-domain changes of mechanism. As a result, the proposed method based predicting model can make efficient distribution shifts according to the estimation. The machining of aero-engine casings is taken as a case study, and experimental results show that the proposed method could predict the deformation well with limited target domain data. Besides, the proposed method can be readily extended to other cross-domain regression problems involved with unobservable variables.", "Keywords": "", "DOI": "10.1016/j.jmsy.2024.11.002", "PubYear": 2024, "Volume": "77", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China"}, {"AuthorId": 2, "Name": "Yingguang Li", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Liu", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Nanjing Tech University, Nanjing 211816, China"}], "References": [{"Title": "Deep neural network and meta-learning-based reactive sputtering with small data sample counts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "703", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Cloud-edge-device collaboration mechanisms of deep learning models for smart robots in mass personalization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "102351", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Using meta-learning for automated algorithms selection and configuration: an experimental framework for industrial big data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "A mechanism informed neural network for predicting machining deformation of annular parts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101661", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Intelligent tool wear monitoring based on multi-channel hybrid information and deep transfer learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; Dongbo Hong", "PubYear": 2023, "Volume": "69", "Issue": "", "Page": "31", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Online visual end-to-end detection monitoring on surface defect of aluminum strip under the industrial few-shot condition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "70", "Issue": "", "Page": "31", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Cross-domain tool wear condition monitoring via residual attention hybrid adaptation network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "72", "Issue": "", "Page": "406", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 118820930, "Title": "A new way to use nonlocal symmetries to determine first integrals of second-order nonlinear ordinary differential equations", "Abstract": "Finding first integrals of second-order nonlinear ordinary differential equations (nonlinear 2ODEs) is a very difficult task. In very complicated cases, where we cannot find Darboux polynomials (to construct an integrating factor) or a Lie symmetry (that allows us to simplify the equations), we sometimes can solve the problem by using a nonlocal symmetry. In [1] , [2] , [3] we developed (and improved) a method (S-function method) that is successful in finding nonlocal Lie symmetries to a large class of nonlinear rational 2ODEs. However, even with the nonlocal symmetry, we still need to solve a 1ODE (which can be very difficult to solve) to find the first integral. In this work we present a novel way of using the nonlocal symmetry to compute the first integral with a very efficient linear procedure. <b >New version program summary</b> Program Title: InSyDE – Invariants and Symmetries of (rational second order ordinary) Differential Equations. CPC Library link to program files: https://doi.org/10.17632/4ytft6zgk7.3 Licensing provisions: CC by NC 3.0 Programming language: Maple Supplementary material: Theoretical results and revision of the S-function method. Journal reference of previous version: Comput. Phys. Comm. Volume 234, January 2019, Pages 302-314 - https://doi.org/10.1016/j.cpc.2018.05.009 Does the new version supersede the previous version?: Yes. Nature of problem: Determining first integrals of rational second order ordinary differential equations. Solution method: The method is explained in the Summary of revisions and Supplementary material. Reasons for the new version: The InSyDE package after determining the S-function still needs to solve a first-order ordinary differential equation (1ODE) associated with the nonlocal symmetry (the so-called associated 1ODE – see [2] ). The problem is that, for very complicated 1ODEs, this may not be practically feasible. We have developed an new and more efficient method that uses the nonlocal symmetry to (for a large class of 1ODEs) determine the first integral in a linear way. Summary of revisions: In order to implement the new method just mentioned above we have made modifications to the command ( Sfunction ) and introduced a new one: command ( Darlin ).", "Keywords": "", "DOI": "10.1016/j.cpc.2024.109426", "PubYear": 2025, "Volume": "307", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade do Estado do Rio de Janeiro, Instituto de Física, Depto. de Física Teórica, 20559-900 Rio de Janeiro – RJ, Brazil"}, {"AuthorId": 2, "Name": "L.G.S. Duarte", "Affiliation": "Universidade do Estado do Rio de Janeiro, Instituto de Física, Depto. de Física Teórica, 20559-900 Rio de Janeiro – RJ, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universidade do Estado do Rio de Janeiro, Instituto de Física, Depto. de Física Teórica, 20559-900 Rio de Janeiro – RJ, Brazil"}, {"AuthorId": 4, "Name": "A.C.S. Guabiraba", "Affiliation": "Universidade do Estado do Rio de Janeiro, Instituto de Física, Depto. de Física Teórica, 20559-900 Rio de Janeiro – RJ, Brazil"}, {"AuthorId": 5, "Name": "L.A.C.P. da <PERSON>", "Affiliation": "Universidade do Estado do Rio de Janeiro, Instituto de Física, Depto. de Física Teórica, 20559-900 Rio de Janeiro – RJ, Brazil;Corresponding author"}, {"AuthorId": 6, "Name": "I.S.S. Nascimento", "Affiliation": "Universidade do Estado do Rio de Janeiro, Instituto de Física, Depto. de Física Teórica, 20559-900 Rio de Janeiro – RJ, Brazil"}], "References": [{"Title": "An efficient way to determine <PERSON><PERSON><PERSON><PERSON> first integrals of rational second order ordinary differential equations", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; L.A.C.P<PERSON>", "PubYear": 2024, "Volume": "298", "Issue": "", "Page": "109088", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 118820948, "Title": "Exploring Parent's Needs for Children-Centered AI to Support Preschoolers' Interactive Storytelling and Reading Activities", "Abstract": "<p>Interactive storytelling is vital for preschooler development. While children's interactive partners have traditionally been their parents and teachers, recent advances in artificial intelligence (AI) have sparked a surge of AI-based storytelling and reading technologies. As these technologies become increasingly ubiquitous in preschoolers' lives, questions arise regarding how they function in practical storytelling and reading scenarios and, how parents, the most critical stakeholders, experience and perceive these technologies. This paper investigates these questions through a qualitative study with 17 parents of children aged 3-6. Our findings suggest that even though AI-based storytelling and reading technologies provide more immersive and engaging interaction, they still cannot meet parents' expectations due to a series of interactive and algorithmic challenges. We elaborate on these challenges and discuss the possible implications of future AI-based interactive storytelling technologies for preschoolers.</p>", "Keywords": "", "DOI": "10.1145/3687035", "PubYear": 2024, "Volume": "8", "Issue": "CSCW2", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "Yuling Sun", "Affiliation": "East China Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "East China Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Yao", "Affiliation": "Rensselaer Polytechnic Institute, Troy, New York, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "East China Normal University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, Boston, Massachusetts, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, Hong Kong"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, Boston, Massachusetts, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Harvard University, Boston, Massachusetts, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "East China Normal University, Shanghai, China"}], "References": [{"Title": "Same benefits, different communication patterns: Comparing Children's reading with a conversational agent vs. a human partner", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "161", "Issue": "", "Page": "104059", "JournalTitle": "Computers & Education"}, {"Title": "Designing for oral storytelling practices at home: A parental perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "", "Page": "100214", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "Parental Acceptance of Children’s Storytelling Robots: A Projection of the Uncanny Valley of AI", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "49", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Interactive storytelling for children: A case-study of design and development considerations for ethical conversational AI", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "", "Page": "100403", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "'Don't make assumptions about me!': Understanding Children's Perception of Datafication Online", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Artificial Intelligence (AI) Literacy in Early Childhood Education: The Challenges and Opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "", "Page": "100124", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "\"I see it as a wellspring for my positive and upward journey in life.\": Understanding Current Practices of Assistive Technology's Customized Modification in China", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 118820992, "Title": "Neural network recognition system for video transmitted through a binary symmetric channel", "Abstract": "", "Keywords": "", "DOI": "10.18287/2412-6179-CO-1388", "PubYear": 2024, "Volume": "48", "Issue": "4", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "North-Caucasus Center for Mathematical Research, North-Caucasus Federal University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "North-Caucasus Center for Mathematical Research, North-Caucasus Federal University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "North-Caucasus Center for Mathematical Research, North-Caucasus Federal University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematical Modeling, North-Caucasus Federal University"}], "References": []}, {"ArticleId": 118821008, "Title": "Unfolder: fast localization and image rectification of a document with a crease from folding in half", "Abstract": "", "Keywords": "", "DOI": "10.18287/2412-6179-CO-1406", "PubYear": 2024, "Volume": "48", "Issue": "4", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Smart Engines Service LLC"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute for Information Transmission Problems of RAS (Kharkevich Institute)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Smart Engines Service LLC"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Federal Research Center \"Computer Science and Control\" of the Russian Academy of Sciences"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Smart Engines Service LLC"}], "References": []}, {"ArticleId": 118821014, "Title": "Polarization Möbius strip at the tight focus of a generalized Poincaré beam", "Abstract": "", "Keywords": "", "DOI": "10.18287/2412-6179-CO-1487", "PubYear": 2024, "Volume": "48", "Issue": "4", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "A.M. Telegin", "Affiliation": "Samara National Research University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Image Processing Systems Institute, NRC \"Kurchatov Institute\""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University"}], "References": []}, {"ArticleId": 118821032, "Title": "An algorithm for the incorporation of relevant FVM boundary conditions in the Eulerian SPH framework", "Abstract": "The finite volume method (FVM) is widely recognized as a computationally efficient and accurate mesh-based technique. However, it has notable limitations, particularly in mesh generation and handling complex boundary interfaces or conditions. In contrast, the smoothed particle hydrodynamics (SPH) method, a popular meshless alternative, inherently circumvents the challenges of mesh generation and yields smoother numerical outcomes. Nevertheless, this approach comes at the cost of reduced computational efficiency. Consequently, researchers have strategically combined the strengths of both methods to investigate complex flow phenomena, producing precise and computationally efficient outcomes. However, algorithms involving the weak coupling of these two methods tend to be intricate and face challenges regarding versatility, implementation, and mutual adaptation to hardware and coding structures. Thus, achieving a robust and strong coupling of FVM and SPH within a unified framework is essential. A mesh-based FVM has recently been integrated into the SPH-based library SPHinXsys. However, due to the differing boundary algorithms between these methods, the crucial step for establishing a strong coupling of both methods within a unified SPH framework is to incorporate the FVM boundary algorithm into the Eulerian SPH method. In this paper, we propose a straightforward algorithm within the Eulerian SPH method, which is algorithmically equivalent to that in FVM and based on the principle of zero-order consistency. Moreover, several numerical examples, including compressible and incompressible flows with various boundary conditions in the Eulerian SPH method, demonstrate the stability and accuracy of the proposed algorithm.", "Keywords": "Boundary algorithm; Finite volume method; Smoothed particle hydrodynamics; Strong coupling; Zero-order consistency; SPHinXsys", "DOI": "10.1016/j.cpc.2024.109429", "PubYear": 2025, "Volume": "307", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "TUM School of Engineering and Design, Technical University of Munich, Garching, 85748, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "TUM School of Engineering and Design, Technical University of Munich, Garching, 85748, Germany"}, {"AuthorId": 3, "Name": "Xiangyu Hu", "Affiliation": "TUM School of Engineering and Design, Technical University of Munich, Garching, 85748, Germany;Corresponding author"}], "References": [{"Title": "SPHinXsys: An open-source meshless, multi-resolution and multi-physics library", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "100033", "JournalTitle": "Software Impacts"}, {"Title": "SPHinXsys: An open-source multi-physics and multi-resolution library based on smoothed particle hydrodynamics", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "267", "Issue": "", "Page": "108066", "JournalTitle": "Computer Physics Communications"}, {"Title": "Eulerian incompressible smoothed particle hydrodynamics on multiple GPUs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "273", "Issue": "", "Page": "108263", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 118821422, "Title": "SAR-LtYOLOv8: A Lightweight YOLOv8 Model for Small Object Detection in SAR Ship Images", "Abstract": "", "Keywords": "", "DOI": "10.32604/csse.2024.056736", "PubYear": 2024, "Volume": "48", "Issue": "6", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Han", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Zhongdai Wu", "Affiliation": ""}], "References": [{"Title": "New SAR target recognition based on YOLO and very deep multi-canonical correlation analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "15-16", "Page": "5800", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "A deep learning approach for intrusion detection in Internet of Things using focal loss function", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "", "Page": "100699", "JournalTitle": "Internet of Things"}, {"Title": "Shuffled-Xception-DarkNet-53: A content-based image retrieval model based on deep learning algorithm", "Authors": "<PERSON><PERSON><PERSON>; U.S.<PERSON><PERSON>", "PubYear": 2023, "Volume": "107", "Issue": "", "Page": "108647", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Fruit ripeness identification using YOLOv8 model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "9", "Page": "28039", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel multi-label pest image classifier using the modified Swin Transformer and soft binary cross entropy loss", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107060", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "NAS-YOLOX: a SAR ship detection using neural architecture search and multi-scale attention", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "1", "JournalTitle": "Connection Science"}, {"Title": "FastPFM: a multi-scale ship detection algorithm for complex scenes based on SAR images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Chen", "PubYear": 2024, "Volume": "36", "Issue": "1", "Page": "", "JournalTitle": "Connection Science"}, {"Title": "FE-YOLO: YOLO ship detection algorithm based on feature fusion and feature enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "21", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "LSKNet: A Foundation Lightweight Backbone for Remote Sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "133", "Issue": "3", "Page": "1410", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 118821462, "Title": "Advancing buffet onset prediction: a deep learning approach with enhanced interpretability for aerodynamic engineering", "Abstract": "<p>The interaction between the shock wave and boundary layer of transonic wings can trigger periodic self-excited oscillations, resulting in transonic buffet. Buffet severely restricts the flight envelope of civil aircraft and is directly related to their aerodynamic performance and safety. Developing efficient and reliable techniques for buffet onset prediction is crucial for the advancement of civil aircraft. In this study, utilizing a comprehensive database of supercritical airfoils generated through numerical simulations, a convolutional neural network (CNN) model is firstly developed to perform buffet classification based on the flow fields. After that, employing explainable machine learning techniques, including Gradient-weighted Class Activation Mapping (Grad-CAM), random forest algorithms, and statistical analysis, the research investigates the correlations between supervised CNN features and key physical characteristics related with the separation region, shock wave, leading edge suction peak, and post-shock loading. Finally, physical buffet onset metric is established with good generalization and accuracy, providing valuable guidance for engineering design in civil aircraft.</p>", "Keywords": "Transonic buffet; Deep learning; Explainable machine learning; Aerodynamic design", "DOI": "10.1007/s40747-024-01612-y", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shanghai Aircraft Design and Research Institute, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Satellite Digitalization Technology, Innovation Academy for Microsatellites of CAS, Shanghai, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Aircraft Design and Research Institute, Shanghai, China"}], "References": [{"Title": "A repository of real-world datasets for data-driven evolutionary multiobjective optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "189", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Evolutionary generative design of supercritical airfoils: an automated approach driven by small data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "1", "Page": "1167", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Parametric generative schemes with geometric constraints for encoding and synthesizing airfoils", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "128", "Issue": "", "Page": "107505", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Parametric generative schemes with geometric constraints for encoding and synthesizing airfoils", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "128", "Issue": "", "Page": "107505", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118821657, "Title": "TGPO-WRHNN: Two-stage Grad-CAM-guided PMRS Optimization and weighted-residual hypergraph neural network for pneumonia detection", "Abstract": "Recent studies based on chest X-ray images have shown that pneumonia can be effectively detected using deep convolutional neural network methods. However, these methods tend to introduce additional noise and extract only local feature information, making it difficult to express the relationship between data objects. This study proposes a Two-stage Grad-CAM-guided pre-trained model and removal scheme (PMRS) Optimization and weighted-residual hypergraph neural network model (TGPO-WRHNN). First, our model extracts high-dimensional features using the TGPO module to capture both global and local information from an image. Second, we propose a new distance-based hypergraph construction method (DBHC) to amplify the difference between distances and better distinguish the relation between nearby and distant neighbors. Finally, we introduce a weighted-residual hypergraph convolution module (WRHC) to ensure the model maintains excellent performance, even at deeper levels. Our model was tested on a dataset of chest X-ray images of pediatric patients aged 1 to 5 years at the Guangzhou Women and Children’s Medical Centre by 10-fold cross-validation. The results showed that the method achieved a maximum accuracy of 98.97%, precision of 98.86%, recall of 98.43%, F1 score of 98.64%, and AUC of 99.78%. Compared to other existing models, our model demonstrated improvements of 0.87%, 0.86%, 0.16%, and 0.38% in terms of accuracy, precision, F1 score, and AUC, respectively.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112708", "PubYear": 2024, "Volume": "306", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 3, "Name": "Junding Sun", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biological Sciences, Xi’an Jiaotong-Liverpool University, Suzhou, Jiangsu 215123, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China;School of Computing and Mathematical Sciences, University of Leicester, LE1 7RH, UK;Department of Information Systems, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, 21589, Saudi Arabia;Corresponding author at: School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan, 454000, China"}], "References": [{"Title": "Hypergraph membrane system based F 2 fully convolutional neural network for brain tumor segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106454", "JournalTitle": "Applied Soft Computing"}, {"Title": "Scalable Graph Neural Network Training", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "1", "Page": "68", "JournalTitle": "ACM SIGOPS Operating Systems Review"}, {"Title": "Pneumonia classification using quaternion deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "2", "Page": "1743", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Graph neural network with feature enhancement of isolated marginal groups", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "14", "Page": "16962", "JournalTitle": "Applied Intelligence"}, {"Title": "FC–HAT: Hypergraph attention network for functional brain network classification", "Authors": "Junzhong Ji; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1301", "JournalTitle": "Information Sciences"}, {"Title": "Hypergraph-based spiking neural P systems for predicting the overall survival time of glioblastoma patients", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON> Gong", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119234", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Deep Learning based model for the Detection of Pneumonia from Chest X-Ray Images using VGG-16 and Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "357", "JournalTitle": "Procedia Computer Science"}, {"Title": "One-step unsupervised clustering based on information theoretic metric and adaptive neighbor manifold regularization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105880", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Hypergraph and cross-attention-based unsupervised domain adaptation framework for cross-domain myocardial infarction localization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "633", "Issue": "", "Page": "245", "JournalTitle": "Information Sciences"}, {"Title": "Multi-semantic hypergraph neural network for effective few-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "109677", "JournalTitle": "Pattern Recognition"}, {"Title": "Hypergraphx: a library for higher-order network analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "3", "Page": "", "JournalTitle": "Journal of Complex Networks"}, {"Title": "POI recommendation for occasional groups Based on hybrid graph neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121583", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Collaborative contrastive learning for hypergraph node classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "146", "Issue": "", "Page": "109995", "JournalTitle": "Pattern Recognition"}, {"Title": "Clustering by sparse orthogonal NMF and interpretable neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "6", "Page": "3341", "JournalTitle": "Multimedia Systems"}, {"Title": "Dynamic hypergraph convolutional network for multimodal sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "565", "Issue": "", "Page": "126992", "JournalTitle": "Neurocomputing"}, {"Title": "Forecasting wholesale prices of yellow corn through the Gaussian process regression", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "15", "Page": "8693", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 118821658, "Title": "Who uses personas in requirements engineering: The practitioners’ perspective", "Abstract": "<b >Context:</b> Personas are commonly employed in software projects to better understand end-users needs. Despite their frequent usage, there is a limited understanding of their practical application and effectiveness. <b >Objective:</b> This paper aims to investigate the current practices, methods, and challenges associated with using personas in software development. <b >Methods:</b> A two-step investigation was conducted, comprising interviews with 26 software developers, UI/UX designers, business analysts, and product managers, along with a survey of 203 practitioners. <b >Results:</b> The findings reveal variations in the frequency and effectiveness of personas across different software projects and IT companies. Additionally, the study highlights the challenges practitioners face when using personas and the reasons for not using them. Notably, the research shows that some human aspects (e.g., the needs of users with disabilities), often assumed to be a key feature of personas, are frequently not considered for various reasons in requirements engineering. <b >Conclusions:</b> The study provides actionable insights for practitioners to overcome challenges in using personas during the requirements engineering stages. Furthermore, it identifies areas for future research to enhance the effectiveness of personas in software development.", "Keywords": "Requirements engineering; Personas; Human aspects; Survey; Interviews", "DOI": "10.1016/j.infsof.2024.107609", "PubYear": 2025, "Volume": "178", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Deakin University, Geelong, VIC, Australia"}, {"AuthorId": 2, "Name": "Chetan <PERSON>ra", "Affiliation": "Monash University, Melbourne, VIC, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Deakin University, Geelong, VIC, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Deakin University, Geelong, VIC, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Monash University, Melbourne, VIC, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Deakin University, Geelong, VIC, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Monash University, Melbourne, VIC, Australia"}], "References": [{"Title": "A Survey of 15 Years of Data-Driven Persona Development", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "18", "Page": "1685", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Use of personas in Requirements Engineering: A systematic mapping study", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "162", "Issue": "", "Page": "107264", "JournalTitle": "Information and Software Technology"}, {"Title": "How do software practitioners perceive human-centric defects?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "176", "Issue": "", "Page": "107549", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": *********, "Title": "Memory-enhanced hierarchical transformer for video paragraph captioning", "Abstract": "Video paragraph captioning aims to describe a video that contains multiple events with a paragraph of generated coherent sentences. Such a captioning task is full of challenges since the high requirements for visual–textual relevance and semantic coherence across the captioning paragraph of a video. In this work, we introduce a memory-enhanced hierarchical transformer for video paragraph captioning. Our model adopts a hierarchical structure, where the outer layer transformer extracts visual information from a global perspective and captures the relevancy between event segments throughout the entire video, while the inner layer transformer further mines local details within each event segment. By thoroughly exploring both global and local visual information at the video and event levels, our model can provide comprehensive visual feature cues for promising paragraph caption generation. Additionally, we design a memory module to capture similar patterns among event segments within a video, which preserves contextual information across event segments and updates its memory state accordingly. Experimental results on two popular datasets, ActivityNet Captions and YouCook2, demonstrate that our proposed model can achieve superior performance, generating higher quality caption while maintaining consistency in the content of video.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.128835", "PubYear": 2025, "Volume": "615", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an 710072, China;School of Artificial Intelligence, Optics and Electronics (iOPEN), Northwestern Polytechnical University, Xi’an 710072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Optics and Electronics (iOPEN), Northwestern Polytechnical University, Xi’an 710072, China;Shanghai Artificial Intelligence Laboratory, Shanghai 200232, China"}, {"AuthorId": 3, "Name": "Yuan Yuan", "Affiliation": "School of Artificial Intelligence, Optics and Electronics (iOPEN), Northwestern Polytechnical University, Xi’an 710072, China;Corresponding author"}], "References": [{"Title": "Group multi-scale attention pyramid network for traffic sign detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "FeatInter: Exploring fine-grained object features for video-text retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "496", "Issue": "", "Page": "178", "JournalTitle": "Neurocomputing"}, {"Title": "Query-aware video encoder for video moment retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> Sun; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "483", "Issue": "", "Page": "72", "JournalTitle": "Neurocomputing"}, {"Title": "CLIP4Clip: An empirical study of CLIP for end to end video clip retrieval and captioning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "508", "Issue": "", "Page": "293", "JournalTitle": "Neurocomputing"}, {"Title": "A multi-layer memory sharing network for video captioning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109202", "JournalTitle": "Pattern Recognition"}, {"Title": "MPP-net: Multi-perspective perception network for dense video captioning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "552", "Issue": "", "Page": "126523", "JournalTitle": "Neurocomputing"}, {"Title": "Abnormal event detection for video surveillance using an enhanced two-stream fusion method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "553", "Issue": "", "Page": "126561", "JournalTitle": "Neurocomputing"}, {"Title": "Collaborative three-stream transformers for video captioning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "235", "Issue": "", "Page": "103799", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 118821671, "Title": "Machine Learning Model for Early Prediction of Survival in Gallbladder Adenocarcinoma: A comparison Study", "Abstract": "<p>The prognosis for gallbladder adenocarcinoma (GBAC), a highly malignant cancer, is not good. In order to facilitate individualized risk stratification and improve clinical decision-making, this study set out to create and validate a machine learning model that could accurately predict early survival outcomes in GBAC patients. Five models-RSF, Cox regression, GBM, XGBoost, and Deepsurv-were compared using data from the SEER database (2010-2020). The dataset was divided into training (70%) and validation (30%) sets, and the C-index, ROC curves, calibration curves, and decision curve analysis (DCA) were used to assess the model's performance. At 1, 2, and 3-year survival intervals, the RSF model performed better than the others in terms of calibration, discrimination, and clinical net benefit. The most important predictor of survival, according to SHAP analysis, is AJCC stage. Patients were divided into high, medium, and low-risk groups according to RSF-derived risk scores, which revealed notable variations in survival results. These results demonstrate the RSF model's potential as an early survival prediction tool for GBAC patients, which could enhance individualized treatment and decision-making.</p><p>Copyright © 2024. Published by Elsevier Inc.</p>", "Keywords": "gallbladder adenocarcinoma;machine learning model;prognosis;the Surveillance; Epidemiology; and End Results Program (SEER)", "DOI": "10.1016/j.slast.2024.100220", "PubYear": 2024, "Volume": "29", "Issue": "6", "JournalId": 19198, "JournalTitle": "SLAS TECHNOLOGY: Translating Life Sciences Innovation", "ISSN": "2472-6303", "EISSN": "2472-6311", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Oncology, Qilu Hospital of Shandong University Dezhou Hospital (Dezhou People's Hospital), Dezhou City 253000, Shandong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiotherapy, Qilu Hospital of Shandong University Dezhou Hospital (Dezhou People's Hospital), Dezhou City 253000, Shandong, China"}, {"AuthorId": 3, "Name": "Haiyuan Yu", "Affiliation": "Department of Quality Management Office, Qilu Hospital of Shandong University Dezhou Hospital (Dezhou People's Hospital), Dezhou City 253000, Shandong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cancer Prevention Center, Key Laboratory of Cancer Prevention and Therapy, Tianjin Medical University Cancer Institute and Hospital(TMUCIH), National Clinical Research Center for Cancer, Tianjin 300060, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Department of Oncology, Qilu Hospital of Shandong University Dezhou Hospital (Dezhou People's Hospital), Dezhou City 253000, Shandong, China"}], "References": []}, {"ArticleId": 118821674, "Title": "Selfied: Sybil defense in permissionless blockchains via in-protocol bandwidth consumption", "Abstract": "Mechanisms for defending against sybil attacks play a central role in permissionless blockchains. An important class of sybil defenses are based on resource challenge , which challenges the resources held by the nodes. For example, Proof-of-Work challenges nodes’ computation power. Such resource challenge, however, can cause significant resource waste. This work explores sybil defense based on in-protocol resource consumption. Here in-protocol consumption refers to the resources consumed to sustain the normal operation of the system itself. Specifically, we propose a novel sybil defense mechanism based on in-protocol bandwidth consumption , in a permissionless blockchain Selfied that we have designed. In Selfied , based on the amount of in-protocol bandwidth consumption, every node forms a view . These views are then used to generates new blocks in the blockchain. We present formal security analysis of our design, showing that our design remains secure despite sybil attacks, as long as the adversary’s bandwidth is limited. We also implement a research prototype of <PERSON><PERSON> , and our experiments (with up to 10 000 nodes) show that our blockchain delivers practically-usable end-to-end performance.", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110890", "PubYear": 2025, "Volume": "256", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, National University of Singapore, 13 Computing Drive, Singapore, 117417, Republic of Singapore"}, {"AuthorId": 2, "Name": "Haifeng Yu", "Affiliation": "School of Computing, National University of Singapore, 13 Computing Drive, Singapore, 117417, Republic of Singapore;Corresponding author"}, {"AuthorId": 3, "Name": "Yucheng Sun", "Affiliation": "School of Computing, National University of Singapore, 13 Computing Drive, Singapore, 117417, Republic of Singapore"}], "References": []}, {"ArticleId": 118821698, "Title": "Causal discovery and analysis of global city carbon emissions based on data-driven and hybrid intelligence", "Abstract": "The unclear causal links of carbon emissions among global cities challenge policy development. This study develops two causal discovery algorithms to aid in this understanding. The first, scalable causal discovery, excels in unraveling complex causal relationships within extensive non-Euclidean networks encompassing thousands of nodes. The second, knowledge-injection causal discovery, merges expert expertise with artificial intelligence's data mining capabilities, employing a human-computer interaction approach for precise causal analysis. The proposed algorithms outperform leading causal discovery methods in the Granger causality test and causal structural consistency. This study investigates the emission causal networks across global cities and key international organizations, including the Organization for Economic Cooperation and Development, the Commonwealth, G20, the Belt and Road Initiative, and the Asia-Pacific Economic Cooperation. The analysis encompasses networks, countries, cities, and emission sources, providing insights for developing collaborative urban emission reduction policies. It underscores the tightly interconnected nature of the worldwide emission network, where the effects are rapidly disseminated. Furthermore, sub-networks reveal consistency and variability in their causal patterns, with core cities exerting significant influence over various dynamics. It is essential to leverage the unique structural characteristics inherent in each sub-network to enhance the effectiveness of coordinated emission reduction initiatives.", "Keywords": "", "DOI": "10.1016/j.compenvurbsys.2024.102206", "PubYear": 2025, "Volume": "115", "Issue": "", "JournalId": 1688, "JournalTitle": "Computers, Environment and Urban Systems", "ISSN": "0198-9715", "EISSN": "1873-7587", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610000, China;Faculty of Business, Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Liang", "Affiliation": "Business School, Sichuan University, Chengdu 610000, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610000, China"}], "References": [{"Title": "Multi-objective optimization of urban environmental system design using machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "94", "Issue": "", "Page": "101796", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "A dominance tree approach to systems of cities", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "97", "Issue": "", "Page": "101856", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "Carbon emission causal discovery and multi-step forecasting using spatiotemporal information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "665", "Issue": "", "Page": "120372", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 118821729, "Title": "Revolutionizing the Future of Hydrological Science: Impact of Machine Learning and Deep Learning amidst Emerging Explainable AI and Transfer Learning", "Abstract": "Artificial Intelligence (AI), Machine Learning (ML), and Deep Learning (DL) are revolutionizing hydrology, driving significant advancements in water resource management, modeling, and prediction. This review synthesizes cutting-edge developments, methodologies, and applications of AI-ML-DL across key hydrological processes. By critically evaluating these techniques against traditional models, we highlight their superior ability to capture complex, nonlinear relationships and adapt to diverse environments. We further explore AI applications in precipitation forecasting, evapotranspiration estimation, groundwater dynamics, and extreme event prediction (floods, droughts, and compound events), showcasing their timely potential in addressing critical water-related challenges. A particular emphasis is placed on Explainable AI (XAI) and transfer learning as essential tools for improving model transparency and applicability, enabling broader stakeholder trust and cross-regional adaptability. The review also addresses persistent challenges, including data limitations, computational demands, and model interpretability, proposing solutions that integrate emerging technologies like quantum computing, the Internet of Things (IoT), and interdisciplinary collaboration. This review charts a strategic course for future research and practice by bridging AI advancements with practical hydrological applications. Our findings highlight the importance of embracing AI-driven approaches for next-generation hydrological modeling and provide actionable understandings for researchers, practitioners, and policymakers. As hydrology faces escalating challenges due to human-induced climate change and growing water demands, the continued evolution of AI-integrated models and innovations in data handling and stakeholder engagement will be imperative. In conclusion, the findings emphasize the critical role of AI-driven hydrological modeling in addressing global water challenges, including climate change adaptation, sustainable water resource management, and disaster risk reduction.", "Keywords": "Climate change; Water resources management; Engineering hydrology; Hydrological modeling; Real-time forecasting; Disaster risk reduction", "DOI": "10.1016/j.acags.2024.100206", "PubYear": 2024, "Volume": "24", "Issue": "", "JournalId": 70097, "JournalTitle": "Applied Computing and Geosciences", "ISSN": "2590-1974", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Indian Institute of Technology Kharagpur, Kharagpur, 721302, West Bengal, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Indian Institute of Technology Kharagpur, Kharagpur, 721302, West Bengal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Indian Institute of Technology Kharagpur, Kharagpur, 721302, West Bengal, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Indian Institute of Technology Kharagpur, Kharagpur, 721302, West Bengal, India"}], "References": [{"Title": "Evolution of evapotranspiration models using thermal and shortwave remote sensing data", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111594", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "Developing the artificial neural network–evolutionary algorithms hybrid models (ANN–EA) to predict the daily evaporation from dam reservoirs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "2", "Page": "1375", "JournalTitle": "Engineering with Computers"}, {"Title": "AI for next generation computing: Emerging trends and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "100514", "JournalTitle": "Internet of Things"}, {"Title": "Scrutinizing XAI using linear ground-truth data with suppressor variables", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "111", "Issue": "5", "Page": "1903", "JournalTitle": "Machine Learning"}, {"Title": "Using artificial intelligence and data fusion for environmental monitoring: A review and future perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "86-87", "Issue": "", "Page": "44", "JournalTitle": "Information Fusion"}, {"Title": "Explainable AI (XAI): A systematic meta-survey of current challenges and future opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "263", "Issue": "", "Page": "110273", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A twenty-year dataset of soil moisture and vegetation optical depth from AMSR-E/2 measurements using the multi-channel collaborative algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "292", "Issue": "", "Page": "113595", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A top-down deep learning model for predicting spatiotemporal dynamics of groundwater recharge", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "167", "Issue": "", "Page": "105778", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Modeling climate change impacts on vector-borne disease using machine learning models: Case study of Visceral leishmaniasis (Kala-azar) from Indian state of Bihar", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121490", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Surface soil moisture retrieval based on transfer learning using SAR data on a local scale", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "45", "Issue": "7", "Page": "2374", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Machine Learning-Based Water Management Strategies for Sustainable Groundwater Resources", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Integrating machine learning with thermal-driven analytical energy balance model improved terrestrial evapotranspiration estimation through enhanced surface conductance", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "311", "Issue": "", "Page": "114308", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 118821746, "Title": "Prediction of Postoperative Mechanical Complications in ASD Patients Based on Total Sequence and Proportional Score of Spinal Sagittal Plane", "Abstract": "<p>This article aimed to predict the occurrence of postoperative mechanical complications in adult spinal deformity (ASD) patients through the total sequence and proportional score of the spinal sagittal plane, to improve the quality of life of patients after surgery. The study adopted a comprehensive evaluation and data analysis method, including data collection and preprocessing, feature selection, model construction and training, and constructed a prediction model based on the Random Forest (RF) algorithm. The experimental results showed that the model significantly reduced the risk of complications in randomized controlled trials. The incidence of mechanical complications in the experimental group was 10%, while that in the control group was 25%, with statistical significance (P<0.05). In addition, in retrospective data analysis, the accuracy of the article's model on five datasets ranged from 89% to 93%, outperforming logistic regression and support vector machine models, and performing well on other performance data. In prospective studies, the model's predictions showed good consistency with the actual occurrence of complications. Sensitivity analysis shows that the model has low sensitivity to changes in key parameters and exhibits stability, indicating that the model proposed in this article is suitable for uncertain medical environments. The expert rating further confirmed the effectiveness and practicality of the model in predicting postoperative mechanical complications in ASD patients, with the highest score reaching 4.9. These data demonstrate the high accuracy and clinical potential of the model in predicting postoperative complications of ASD.</p><p>Copyright © 2024. Published by Elsevier Inc.</p>", "Keywords": "Adult Spinal Deformity;Random Forest;Scale Score;Sensitivity Analysis;Spinal Sagittal Total Sequence", "DOI": "10.1016/j.slast.2024.100222", "PubYear": 2024, "Volume": "29", "Issue": "6", "JournalId": 19198, "JournalTitle": "SLAS TECHNOLOGY: Translating Life Sciences Innovation", "ISSN": "2472-6303", "EISSN": "2472-6311", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Spine Surgery, West 1st Section 132, 1st Ring Road, Chengdu 610041, SiChuan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Spine Surgery, West 1st Section 132, 1st Ring Road, Chengdu 610041, SiChuan, China"}, {"AuthorId": 3, "Name": "<PERSON> Gu", "Affiliation": "Department of Spine Surgery, West 1st Section 132, 1st Ring Road, Chengdu 610041, SiChuan, China"}, {"AuthorId": 4, "Name": "Zonglin Cai", "Affiliation": "Department of Spine Surgery, West 1st Section 132, 1st Ring Road, Chengdu 610041, SiChuan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Spine Surgery, West 1st Section 132, 1st Ring Road, Chengdu 610041, SiChuan, China"}], "References": [{"Title": "AI-based smart prediction of clinical disease using random forest classifier and Naive Bayes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "5", "Page": "5198", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 118821778, "Title": "Analysis of the Arab singer <PERSON><PERSON><PERSON>’s lyrics", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41060-024-00669-9", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 27236, "JournalTitle": "International Journal of Data Science and Analytics", "ISSN": "2364-415X", "EISSN": "2364-4168", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Arabic natural language processing: An overview", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "497", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Part-of-speech tagging for Arabic tweets using CRF and Bi-LSTM", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "", "Page": "101138", "JournalTitle": "Computer Speech & Language"}, {"Title": "A panoramic survey of natural language processing in the Arab world", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "4", "Page": "72", "JournalTitle": "Communications of the ACM"}, {"Title": "Lyrics Analysis of the Arab Singer <PERSON><PERSON>", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Natural language processing: state of the art, current trends and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "3", "Page": "3713", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Arabic natural language processing for Qur’anic research: a systematic review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "7", "Page": "6801", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Large scale analysis of gender bias and sexism in song lyrics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "EPJ Data Science"}]}, {"ArticleId": 118821792, "Title": "Image recognition based on machine learning and wireless sensor networks for predicting material loss in milling cutters", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-024-14779-y", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Milling cutter wear prediction method under variable working conditions based on LRCN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "Page": "2647", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 118821831, "Title": "Variable Resistance Training Combined With Static Traction Computer Vision Applied in Neck and Shoulder Rehabilitation Training for College Students: ", "Abstract": "<p>This study evaluates the effectiveness of static traction combined with variable resistance training in rehabilitating college students with neck and shoulder diseases. Ninety students were randomly assigned to receive either static traction alone or combined therapy. Visual analogue scale (VAS), neck disability index (NDI), cervical range of motion, electromyography (EMG), and soft tissue parameters were assessed pre- and post-treatment. Both groups showed improved VAS and NDI scores post-treatment, with greater enhancements in the combined therapy group. Cervical range of motion, EMG values, and soft tissue parameters also favored the combined therapy. Statistical analysis revealed significant differences (P&lt;0.05) between groups. Static traction combined with variable resistance training effectively improves neck and shoulder function, reduces pain, and enhances rehabilitation outcomes compared to static traction alone. This approach shows promise for optimizing rehabilitation in college students with similar musculoskeletal conditions.</p>", "Keywords": "", "DOI": "10.4018/IJDSST.358616", "PubYear": 2024, "Volume": "16", "Issue": "1", "JournalId": 8327, "JournalTitle": "International Journal of Decision Support System Technology", "ISSN": "1941-6296", "EISSN": "1941-630X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shanghai Lixin University of Accounting and Finance, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Lixin University of Accounting and Finance, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai Lixin University of Accounting and Finance, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai Technical Institute of Electronic and Information, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Technical Institute of Electronic and Information, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Technical Institute of Electronic and Information, China"}, {"AuthorId": 7, "Name": "Guangxia Luo", "Affiliation": "Shanghai Technical Institute of Electronic and Information, China"}], "References": [{"Title": "Toward dynamic rehabilitation management: A novel smart product-service system development approach based on fine-tuned large vision model and Fuzzy-Dematel", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "62", "Issue": "", "Page": "102616", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": *********, "Title": "AI and Database Management for Organizational Transformation With Insights From Twitter Data: ", "Abstract": "<p>This paper explores the role of AI and database management in organizational transformation using insights from Twitter data. By analyzing 30,000 English-language tweets with methods such as word analysis, topic modeling, network analysis, sentiment analysis, and emotion analysis, the study reveals a strong correlation between AI and digital transformation. The findings show positive sentiment and optimism about AI's potential. This research highlights the importance of social influence, perceived trust, and awareness in AI adoption, offering valuable insights for researchers and practitioners. Despite relying on Twitter data, the study provides practical guidance for leveraging AI in digital transformation efforts.</p>", "Keywords": "", "DOI": "10.4018/JDM.359335", "PubYear": 2024, "Volume": "35", "Issue": "1", "JournalId": 28529, "JournalTitle": "Journal of Database Management", "ISSN": "1063-8016", "EISSN": "1533-8010", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Management Indore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Management Indore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Management Indore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Management Department, College of Business Administration, Princess <PERSON><PERSON><PERSON>man University, Riyadh, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, Asia University, Taichung, Taiwan & University of Economics and Human Science, Warsaw, Poland & Symbiosis Centre for Information Technology (SCIT), Symbiosis International University, Pune, India & Center for Interdisciplinary Research, University of Petroleum and Energy Studies (UPES), Dehradun, India"}], "References": [{"Title": "Jumping over the network threshold of information diffusion: testing the threshold hypothesis of social influence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "5", "Page": "1677", "JournalTitle": "Internet Research"}, {"Title": "Coronavirus Pandemic (COVID-19)", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "2", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "Fused Contextual Data With Threading Technology to Accelerate Processing in Home UbiHealth", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Software Science and Computational Intelligence"}, {"Title": "Flesch-<PERSON> as Proxy of Socio-Economic Status on Twitter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "A New Alignment Word-Space Approach for Measuring Semantic Similarity for Arabic Text", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "A Context-Independent Ontological Linked Data Alignment Approach to Instance Matching", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "A Comparative Study of Generative Adversarial Networks for Text-to-Image Synthesis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Software Science and Computational Intelligence"}, {"Title": "A Deep Learning Modified Neural Network(DLMNN) based proficient sentiment analysis technique on Twitter data", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "A comprehensive analysis of blockchain and its applications in intelligent systems based on IoT, cloud and social media", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "12", "Page": "11037", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multidirectional Gradient Feature With Shape Index for Effective Texture Classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "“What Can ChatGPT Do?” Analyzing Early Reactions to the Innovative AI Chatbot on Twitter", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "35", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "RDF(S) Store in Object-Relational Databases", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Database Management"}, {"Title": "Optimal Information Acquisition and Sharing Decisions: Joint Reviews on Crowdsourcing Product Design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Database Management"}, {"Title": "XLM-RoBERTa Based Sentiment Analysis of Tweets on Metaverse and 6G", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "902", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 118821926, "Title": "Pan-Mamba: Effective pan-sharpening with state space model", "Abstract": "Pan-sharpening involves integrating information from low-resolution multi-spectral and high-resolution panchromatic images to generate high-resolution multi-spectral counterparts. While recent advancements in the state space model, particularly the efficient long-range dependency modeling achieved by Mamba, have revolutionized computer vision community, its untapped potential in pan-sharpening motivates our exploration. Our contribution, Pan-Mamba, represents a novel pan-sharpening network that leverages the efficiency of the Mamba model in global information modeling. In Pan-Mamba, we customize two core components: channel swapping Mamba and cross-modal Mamba, strategically designed for efficient cross-modal information exchange and fusion. The former initiates a lightweight cross-modal interaction through the exchange of partial panchromatic and multi-spectral channels, while the latter facilities the information representation capability by exploiting inherent cross-modal relationships. Through extensive experiments across diverse datasets, our proposed approach surpasses state-of-the-art methods, showcasing superior fusion results in pan-sharpening. To the best of our knowledge, this work is the first attempt in exploring the potential of the Mamba model and establishes a new frontier in the pan-sharpening techniques. The source code is available at https://github.com/alexhe101/Pan-Mamba .", "Keywords": "", "DOI": "10.1016/j.inffus.2024.102779", "PubYear": 2025, "Volume": "115", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, 230031, China;University of Science and Technology of China, Hefei, 230026, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, 230031, China;University of Science and Technology of China, Hefei, 230026, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, 230031, China;University of Science and Technology of China, Hefei, 230026, China;Correspondence to: 350 Shushanhu Road, Hefei 230031, Anhui, China; Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, 230031, China;University of Science and Technology of China, Hefei, 230026, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Xiamen University, Xiamen, 361102, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, 230031, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, 230031, China"}, {"AuthorId": 8, "Name": "Danfeng Hong", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing, 100094, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "University of Science and Technology of China, Hefei, 230026, China;Corresponding author"}], "References": []}, {"ArticleId": 118821960, "Title": "Multi-Cloud Automation : A Strategic Approach to Cloud Infrastructure Management", "Abstract": "This article examines the evolution and impact of multi-cloud automation strategies in modern enterprise environments, supported by comprehensive industry research and case studies. Drawing from multiple industry reports, including Flexera's 2024 State of the Cloud Report and PwC's Cloud and AI Business Survey, the research reveals that 89% of enterprises now employ multi-cloud strategies, with the global multi-cloud management market projected to grow at a CAGR of 27.3% through 2030. The article analyzes key components of successful multi-cloud automation, including deployment automation, operational excellence through monitoring, and incident management, while providing detailed metrics on their effectiveness. Through examination of real-world implementations across retail, financial services, and SaaS sectors, the research demonstrates how organizations achieve significant improvements in operational efficiency, cost optimization, and business agility through comprehensive automation strategies.", "Keywords": "Multi-Cloud Automation;Cloud Infrastructure Management;DevOps Integration;Cloud Performance Optimization;Enterprise Digital Transformation", "DOI": "10.32628/CSEIT24106167", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Worcester Polytechnic Institute (WPI), Worcester MA, USA"}], "References": []}, {"ArticleId": 118822167, "Title": "Hack-and-leak operations in Latin America: the case of Guacamaya", "Abstract": "", "Keywords": "", "DOI": "10.1080/23738871.2024.2419509", "PubYear": 2024, "Volume": "9", "Issue": "3", "JournalId": 23731, "JournalTitle": "Journal of Cyber Policy", "ISSN": "2373-8871", "EISSN": "2373-8898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Information Security, Royal Holloway, University of London, Egham, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Security, Royal Holloway, University of London, Egham, UK"}], "References": [{"Title": "A sliding scale of secrecy: toward a better understanding of the role of publicity in offensive cyber operations", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "275", "JournalTitle": "Journal of Cyber Policy"}]}, {"ArticleId": 118822192, "Title": "Multiobjective Hybrid Al-Biruni Earth Namib Beetle Optimization and Deep Learning based Task Scheduling in Cloud Computing", "Abstract": "With the rapid development of computing networks, cloud computing (CC) enables the deployment of large-scale applications and meets the increased rate of computational demands. Moreover, task scheduling is an essential process in CC. The tasks must be effectually scheduled across the Virtual Machines (VMs) to increase resource usage and diminish the makespan. In this paper, the multi-objective optimization called Al-Biruni Earth Namib Beetle Optimization (BENBO) with the Bidirectional-Long Short-Term Memory (Bi-LSTM) named as BENBO+ Bi-LSTM is developed for Task scheduling. The user task is subjected to the multi-objective BENBO, in which parameters like makespan, computational cost, reliability, and predicted energy are used to schedule the task. Simultaneously, the user task is fed to Bi-LSTM-based task scheduling, in which the VM parameters like average computation cost, Earliest Starting Time (EST), task priority, and Earliest Finishing Time (EFT) as well as the task parameters like bandwidth and memory capacity are utilized to schedule the task. Moreover, the task scheduling outcomes from the multi-objective BENBO and Bi-LSTM are fused for obtaining the final scheduling with less makespan and resource usage. Moreover, the predicted energy, resource utilization and makespan are considered to validate the BENBO+ Bi-LSTM-based task scheduling, which offered the optimal values of 0.669 J, 0.535 and 0.381.", "Keywords": "", "DOI": "10.1016/j.suscom.2024.101053", "PubYear": 2024, "Volume": "44", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, GITAM Visakhapatnam, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, GITAM Visakhapatnam, India"}], "References": [{"Title": "RETRACTED ARTICLE: Dynamic resource allocation with optimized task scheduling and improved power management in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "4147", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A many-objective optimized task allocation scheduling model in cloud computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "3293", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi-objective hybrid genetic algorithm for task scheduling problem in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "19", "Page": "13075", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A bidirectional LSTM deep learning approach for intrusion detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115524", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Namib beetle optimization algorithm: A new meta‐heuristic method for feature selection and dimension reduction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "e6524", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "DSTS: A hybrid optimal and deep learning for dynamic scalable task scheduling on container cloud environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Al-Biruni <PERSON>dius (BER) Metaheuristic Search Optimization Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "45", "Issue": "2", "Page": "1917", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 118822232, "Title": "Design and implementation of EventsKG for situational monitoring and security intelligence in India: An open-source intelligence gathering approach", "Abstract": "This paper presents a method to construct and implement an Events Knowledge Graph (EventsKG) for security-related open-source intelligence gathering, focusing on event exploration for situation monitoring in India. The EventsKG is designed to process news articles, extract events of national security significance, and represent them in a consistent and intuitive manner. This method utilizes state-of-the-art natural language understanding techniques and the capabilities of graph databases to extract and organize events. A domain-specific ontology is created for effective storage and retrieval. In addition, we provide a user-friendly dashboard for querying and a complete visualization of events across India. The effectiveness of the EventsKG is assessed through a human evaluation of the information retrieval quality. Our approach contributes to rapid data availability and decision-making through a comprehensive understanding of events, including local events, from every part of India in just a few clicks. The system is evaluated against a manually annotated dataset and by involving human evaluators through a feedback survey, and it has shown good retrieval accuracy. The EventsKG can also be used for other applications such as threat intelligence, incident response, and situational awareness.", "Keywords": "Knowledge graphs; Ontology; Domain specific KGs; EventsKG; Open-source intelligence gathering", "DOI": "10.1016/j.iswa.2024.200458", "PubYear": 2024, "Volume": "24", "Issue": "", "JournalId": 89889, "JournalTitle": "Intelligent Systems with Applications", "ISSN": "2667-3053", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Cochin University of Science and Technology, Kerala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Cochin University of Science and Technology, Kerala, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, Cochin University of Science and Technology, Kerala, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Département de Mathémathiques, Université de Caen-Normandie, Caen, France;Corresponding author"}], "References": [{"Title": "Domain-specific knowledge graphs: A survey", "Authors": "Bilal Abu-Salih", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "103076", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "An intelligent approach for mining knowledge graphs of online news", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "9", "Page": "838", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": 118822236, "Title": "A consortium blockchain-edge enabled authentication scheme for underwater acoustic network (UAN)", "Abstract": "The Internet of Things (IoT) allows for automated operations in diverse fields, such as agriculture monitoring, pollution monitoring, health care, and underwater monitoring. The Internet of Underwater Things (IoUT) observes the underwater environment, assists in exploration, mitigates disasters, and monitors some factors including temperature, pressure, and pollution. The IoUT relies on a network of intelligent underwater sensors that send data to surface base stations and IoT devices for storage and analysis. Nevertheless, these systems face security risks as they operate in unattended environments. Many authentication methods depend on a centralized third party, leading to higher computation costs and energy usage. To mitigate security risks, autonomous underwater devices need secure connections and authentication. This paper suggests a decentralized authentication mechanism for UAN to safeguard against unauthorized access and ensure secure data storage in the cloud. The proposed mechanism prioritizes robustness, transparency, and energy efficiency. The suggested solution incorporates an architecture based on edge and cloud layers, utilizing customized blockchain technology for secure storage and processing of data. The security of the proposed solution has been thoroughly examined through formal analysis utilizing the Real or Random (ROR) Oracle model and Scyther tool. Informal analysis further confirms the solution’s resilience against various malicious attacks. Additionally, performance and comparative analysis demonstrate that the proposed solution surpasses existing schemes.", "Keywords": "", "DOI": "10.1016/j.iot.2024.101426", "PubYear": 2024, "Volume": "28", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Scientific Computing, National Institute of Technology Hamirpur, Himachal Pradesh 177005, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Scientific Computing, National Institute of Technology Hamirpur, Himachal Pradesh 177005, India"}], "References": [{"Title": "GUARDIAN: Blockchain-Based Secure Demand Response Management in Smart Grid System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "613", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "A lightweight remote user authentication scheme for IoT communication using elliptic curve cryptography", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "2", "Page": "1114", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Efficient Message Authentication with Revocation Transparency Using Blockchain for Vehicular Networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106721", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A secure and efficient authentication and data sharing scheme for Internet of Things based on blockchain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "102112", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Security mechanism for maritime territory and frontier surveillance in naval operations using wireless sensor networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "17", "Page": "e6300", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Trusted node selection in clusters for underwater wireless acoustic sensor networks using fuzzy logic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101388", "JournalTitle": "Physical Communication"}, {"Title": "Private blockchain-envisioned drones-assisted authentication scheme in IoT-enabled agricultural environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "103567", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Blockchained service provisioning and malicious node detection via federated learning in scalable Internet of Sensor Things networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "108691", "JournalTitle": "Computer Networks"}, {"Title": "A smart contract-based robotic surgery authentication system for healthcare using 6G-Tactile Internet", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "110133", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 118822388, "Title": "THE APPLICATION OF A LARGE LANGUAGE MODEL FOR REDUCING FALSE POSITIVES IN ANOMALY DETECTION TASKS IN NETWORK TRAFFIC", "Abstract": "Every year, network threats become more sophisticated and complex, which requires researchers in the field of network security to seek and develop new and more advanced methods for detecting security threats. Despite the fact that constant research is being conducted in this area and researchers are improving machine learning algorithms, false positive triggers of intrusion detection systems remain a significant problem. In this regard, the development of methods and approaches to reduce the number of false positive positives is one of the most urgent tasks. Aim. The purpose of the study is to study the effectiveness and possibilities of using large language models to reduce false positive responses of intrusion detection systems. Materials and methods. The main focus of the article is on training recurrent neural networks to detect anomalies using a training sample of CIS-IDS2017 network traffic. Special attention was paid to algorithms for selecting key values in the training sample to improve the accuracy of the training model. The paper examines the architecture of recurrent networks, as well as their advantages and disadvantages in the specifics of the task being solved. Further research was conducted using a large language model, as a result of which a comparison was made of the number of false positives with and without this solution. Results. A basic neural network model based on the LSTM algorithm for the initial classification of network threats was built, and a large language model was trained. A comparative analysis of the results of anomaly detection with and without a large linguistic model is carried out. Experiments confirm the effectiveness of the proposed solution. Conclusion. The obtained research results can be used in the development of new, modern intelligent intrusion detection systems to improve the accuracy of threat detection or increase the effectiveness of existing intrusion detection algorithms.", "Keywords": "large language models; neural networks; intrusion detection systems; network traffic; machine learning;большие языковые модели; нейронные сети; системы обнаружения вторжений; сетевой трафик; машинное обучение", "DOI": "10.14529/ctcr240401", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 38337, "JournalTitle": "Bulletin of the South Ural State University. Ser. Computer Technologies, Automatic Control & Radioelectronics", "ISSN": "1991-976X", "EISSN": "2409-6571", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Orenburg State University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Orenburg State University"}], "References": []}, {"ArticleId": 118822450, "Title": "Survey of CPU and memory simulators in computer architecture: A comprehensive analysis including compiler integration and emerging technology applications", "Abstract": "In computer architecture studies, simulators are crucial for design verification, reducing research and development time and ensuring the high accuracy of verification results. Several studies have developed and employed a verification environment by designing a custom in-house simulator that verifies the performance improvement of the proposed architecture for each research purpose by integrating multiple simulators or modifying existing ones. Recent advancements in deep neural networks and increased computational complexity have prompted research into emerging technologies, such as GPUs, processing-in-memory (PIM), and neural processing units (NPUs). Recently, custom in-house simulators were developed and actively employed for performance verification. However, constructing proper custom in-house simulators requires a greater grasp of the support functions and characteristics of current architectural simulators, which constitute the backbone of simulator creation. To meet these criteria, a comprehensive comparative analysis was conducted by examining the structures and output metrics supported across various architecture simulators, including representative CPU and memory simulators. Furthermore, it analyzes actual case studies of in-house simulators developed in recent emerging technology research ( i.e. , GPUs, NPUs, PIM). Additionally, we examine the characteristics of compilers that support optimization for various recent workloads and simulators by analyzing case studies in which the integration of simulators and compilers has contributed to optimizing the overall simulator operations. Analyzing the overall verification process using these simulators, comparing each component, and confirming actual cases can provide essential insights for selecting and developing simulators suitable for computer architecture research. Consequently, this study contributes to maximizing the accuracy and efficiency of computer architecture research.", "Keywords": "Architecture simulator; Central-processing-unit simulator; Memory simulator; Simulator friendly compiler; Processing-in-memory simulator; Neural-processing-unit simulator", "DOI": "10.1016/j.simpat.2024.103032", "PubYear": 2025, "Volume": "138", "Issue": "", "JournalId": 1087, "JournalTitle": "Simulation Modelling Practice and Theory", "ISSN": "1569-190X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Seoul National University of Science and Technology, 01811 Seoul, Republic of Korea;These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Seoul National University of Science and Technology, 01811 Seoul, Republic of Korea;These authors contributed equally to this work"}, {"AuthorId": 3, "Name": "Huibeom Kang", "Affiliation": "Department of Electrical and Information Engineering, Seoul National University of Science and Technology, 01811 Seoul, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Seoul National University of Science and Technology, 01811 Seoul, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Information Engineering, Seoul National University of Science and Technology, 01811 Seoul, Republic of Korea;Corresponding author"}], "References": [{"Title": "A survey of FPGA-based accelerators for convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>l", "PubYear": 2020, "Volume": "32", "Issue": "4", "Page": "1109", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A Survey of Cache Simulators", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "DRAMSys4.0: An Open-Source Simulation Framework for In-depth DRAM Analyses", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "50", "Issue": "2", "Page": "217", "JournalTitle": "International Journal of Parallel Programming"}, {"Title": "Survey of convolutional neural network accelerators on field-programmable gate array platforms: architectures and optimization techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "21", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Real-Time Image Processing"}]}, {"ArticleId": 118822554, "Title": "The numerical analysis of complete and partial electrocoalescence in the droplet-layer system employing the sharp interface technique for multiphase-medium simulation", "Abstract": "In this paper, the coalescence of a drop of water suspended in oil with a layer of water under the influence of a constant electric field is numerically investigated. Unlike most existing studies, the calculations are based on the application of the arbitrary Lagrangian-Eulerian method (ALEM), also called the moving mesh method, which belongs to the class of methods for modeling two-phase liquids with a sharp interface. Using this approach made it possible to avoid a false \"escape\" of the surface charge from the interface, which often occurs when using methods involving a diffuse interface. Despite the fact that ALEM does not allow describing topology changes by default, a numerical model was implemented in which the calculation is divided into three parts: the convergence of the drop and the layer before the moment of touch; the manual construction of the bridge at the moment of touch; the union of the drop and the layer. The developed model allowed us to obtain three possible modes of this process: complete coalescence, partial coalescence and a mode of stretching which has not practically been considered yet. The dependence of the volume of the separated secondary droplet on the size of the initial droplet and the average intensity of the applied electric field is obtained. The model showed good quantitative agreement with experimental studies. It has been shown that generally, the spots where the bridge and the neck are formed in case of partial coalescence do not coincide. A map of coalescence modes was obtained, i.e., the dependence of the transition threshold from coalescence to partial coalescence and from partial coalescence to stretching regime in a wide range of radii of initial droplets and electric field strengths. It has been shown that there is a maximum field strength at which droplets of any size merge with the layer. This map makes it possible to predict the coalescence regime in electrocoalescer. The proposed modeling technique can be used to calculate electrocoalescence modes at various values of the main parameters, which will help to optimize electrocoalescers at the design stage.", "Keywords": "Electrocoalescence; Numerical simulation; Partial coalescence; Droplet-layer; Water-in-oil emulsion; Two-phase liquid", "DOI": "10.1016/j.compfluid.2024.106478", "PubYear": 2025, "Volume": "286", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "St. Petersburg State University, St. Petersburg 199034, Russia;Corresponding author: St. Petersburg State University, St. Petersburg 199034, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "St. Petersburg State University, St. Petersburg 199034, Russia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "St. Petersburg State University, St. Petersburg 199034, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "St. Petersburg State University, St. Petersburg 199034, Russia"}], "References": [{"Title": "Moving mesh methods for two-phase flow systems: Assessment, comparison and analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "105053", "JournalTitle": "Computers & Fluids"}, {"Title": "A conservative sharp interface method for incompressible viscous two-phase flows with topology changes and large density ratio", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "274", "Issue": "", "Page": "106212", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 118822702, "Title": "SCIENTIFIC AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SUPPORT IN LIFE CYCLE MANAGEMENT OF CAPITAL CONSTRUCTION PROJECTS", "Abstract": "Currently, the complexity of construction projects is increasing, the conditions for their implementation are changing, and the requirements are becoming more stringent. At the same time, errors occur, often very significant at various stages of the life cycle of a capital construction project. Accordingly, new approaches and models are needed to manage construction projects and capital construction projects. The research objective is to analyze the possibilities and prospects for the use of scientific and technical support (STS) for capital construction projects of various classes and at different stages of their life cycle. The theoretical and methodological basis of the study is the work of domestic and foreign researchers devoted to the issues of scientific and technical support for the construction and management of capital construction projects at various stages of their life cycle. Results. The paper examines the need and feasibility of expanding the use of scientific and technical support (STS) both in relation to objects of various classes and in relation to the stages of the life cycle of capital construction projects. In this regard, the definition of scientific and technical support has been clarified, taking into account modern needs and capabilities. The importance and prospects of using scientific and technical support for capital construction projects are noted, for which it is not mandatory. Work on scientific and technological progress has been systematized according to various classification criteria. A classification of objects for which it is advisable to use STS is given. Explanations are given about the capabilities of scientific and technical support at various stages of the life cycle of a capital construction project. In particular, about the creation and application, support of virtual models (digital twins, shadows, models) within the framework of scientific and technical progress. It is proposed to consider scientific and technical developments to improve the management efficiency of capital construction projects at various stages of the life cycle. A SWOT analysis was performed for scientific and technical suport, during which the advantages and disadvantages of carrying out STS for capital construction projects at different stages of the life cycle were identified, and opportunities and threats for the implementation of scientific and technical support at the present time were identified. Conclusion. A conclusion is made about the advisability of using scientific and technical support at various stages of the life cycle of capital construction projects, not only to ensure the quality, reliability and safety of capital construction projects, but also to improve management efficiency and, as a consequence, the economic efficiency of objects, optimal disclosure and use of the potential.", "Keywords": "management; scientific and technical support; digitalization; virtual models; efficiency;управление; научно-техническое сопровождение строительства; цифровизация; виртуальные модели; эффективность", "DOI": "10.14529/ctcr240406", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 38337, "JournalTitle": "Bulletin of the South Ural State University. Ser. Computer Technologies, Automatic Control & Radioelectronics", "ISSN": "1991-976X", "EISSN": "2409-6571", "Authors": [{"AuthorId": 1, "Name": "T.<PERSON>. <PERSON>", "Affiliation": "Voronezh State Technical University"}], "References": []}, {"ArticleId": 118822709, "Title": "Predictive modeling and anomaly detection in large-scale web portals through the CAWAL framework", "Abstract": "This study presents an approach that uses session and page view data collected through the CAWAL framework, enriched through specialized processes, for advanced predictive modeling and anomaly detection in web usage mining (WUM) applications. Traditional WUM methods often rely on web server logs, which limit data diversity and quality. Integrating application logs with web analytics, the CAWAL framework creates comprehensive session and page view datasets, providing a more detailed view of user interactions and effectively addressing these limitations. This integration enhances data diversity and quality while eliminating the preprocessing stage required in conventional WUM, leading to greater process efficiency. The enriched datasets, created by cross-integrating session and page view data, were applied to advanced machine learning models, such as Gradient Boosting and Random Forest, which are known for their effectiveness in capturing complex patterns and modeling non-linear relationships. These models achieved over 92% accuracy in predicting user behavior and significantly improved anomaly detection capabilities. The results show that this approach offers detailed insights into user behavior and system performance metrics, making it a reliable solution for improving large-scale web portals’ efficiency, reliability, and scalability.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112710", "PubYear": 2024, "Volume": "306", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Özkan <PERSON>", "Affiliation": "Sakarya University of Applied Sciences, Vocational School of Sakarya, Dept. of Computer Tech., 54290, Sakarya, Turkiye;Sakarya University, Institute of Natural Sciences, Dept. of Computer and IT Engineering, 54050, Sakarya, Turkiye;Corresponding author at: Sakarya University of Applied Sciences, Vocational School of Sakarya, Dept. of Computer Tech., 54290, Sakarya, Turkiye"}, {"AuthorId": 2, "Name": "Ümit Kocabıçak", "Affiliation": "Turkish Higher Education Quality Council, 06800, Ankara, Turkiye;Sakarya University, Faculty of Computer and IT Engineering, Dept. of Computer Eng., 54050, Sakarya, Turkiye"}], "References": [{"Title": "The multi-demeanor fusion based robust intrusion detection system for anomaly and misuse detection in computer networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "303", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "The Effect of Preprocessing Techniques, Applied to Numeric Features, on Classification Algorithms’ Performance", "Authors": "<PERSON><PERSON><PERSON><PERSON>a <PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "11", "JournalTitle": "Data"}, {"Title": "Finding Suitable Membership Functions for Mining Fuzzy Association Rules in Web Data Using Learning Automata", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "7", "Page": "2159026", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}, {"Title": "Maximal paths recipe for constructing Web user sessions", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "6", "Page": "2455", "JournalTitle": "World Wide Web"}, {"Title": "Look back, look around: A systematic analysis of effective predictors for new outlinks in focused Web crawling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Berk Atil", "PubYear": 2023, "Volume": "260", "Issue": "", "Page": "110126", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Web page prediction using adaptive deer hunting with chicken swarm optimization based neural network model", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "", "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing"}, {"Title": "A hybrid anomaly detection method for high dimensional data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1199", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Phishing Detection using Gradient Boosting Classifier", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "230", "Issue": "", "Page": "120", "JournalTitle": "Procedia Computer Science"}, {"Title": "Semantically Enriched Keyword Prefetching Based on Usage and Domain Knowledge", "Authors": "<PERSON>;  <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "341", "JournalTitle": "Journal of Web Engineering"}, {"Title": "A Picture Fuzzy CIMAS-ARTASI Model for Website Performance Analysis in Human Resource Management", "Authors": "<PERSON><PERSON>; Galip <PERSON>; Esra Gökçen Kaygısız", "PubYear": 2024, "Volume": "162", "Issue": "", "Page": "111826", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 118822721, "Title": "DETERMINING THE LEVEL OF PROTECTION OF A DEVELOPER'S BRAND", "Abstract": "The residential real estate construction market is increasing in volume but remains the most risky industry in terms of investment due to the long lead time for product creation and realization. Brand can play an important role in reducing the risk of real estate developers. There is a relationship between the concepts of image, reputation, brand and goodwill. Hence, a real estate developer's goodwill is affected by its image and reputation. Research Objective. It is necessary to identify approaches for assessing the reputation of developers in regional residential real estate markets in order to maintain their brand and, consequently, competitiveness at the required level. Materials and Methods. By analogy with the method of solving the problem of choosing a shopping center by a client, we propose a method for assessing the level of protection of the developer's brand, which is understood as the reputation of the developer in relation to the regional market. Initial data for assessing the level of protection of the developer's brand are consumer reviews in the Internet. It should take into account the Ganning Nebula Index of reviews about the developer, the average volume of words in reviews about the developer, the number of negative reviews about the developer, the total number of reviews about the developer and the degree of competitiveness of the residential real estate market in the region. Results. As a demonstration example of using the proposed method of assessing the level of protection of the developer's brand, a conditional market represented by 9 conditional developers was modeled. For each developer conditional individual parameters were selected and on their basis the level of the developer's brand protection was calculated. A comparative analysis of parameters and the level of brand protection of real estate developers was carried out. In particular, it is shown which parameters and their combinations positively or negatively affect the level of brand protection of the developer. Conclusion. Based on the results of the given demonstration example, it can be stated that the proposed approach to determining the level of protection of the developer's brand in the Internet, at least, does not contradict common sense, and, at most, is a relevant and useful tool for an objective assessment of the developer's business reputation.", "Keywords": "brand; reputation; residential real estate market; reviews in the Internet; level of protection of the developer's brand;бренд; репутация; рынок жилой недвижимости; отзывы в сети интернет; уровень защищенности бренда застройщика", "DOI": "10.14529/ctcr240407", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 38337, "JournalTitle": "Bulletin of the South Ural State University. Ser. Computer Technologies, Automatic Control & Radioelectronics", "ISSN": "1991-976X", "EISSN": "2409-6571", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Perm National Research Polytechnic University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Perm National Research Polytechnic University"}], "References": []}, {"ArticleId": 118822789, "Title": "A new era for stress research: supporting user performance and experience in the digital age", "Abstract": "<p>Stress is both a driver of objective performance impairments and a source of negative user experience of technology. This review addresses future directions for research on stress and ergonomics in the digital age. The review is structured around three levels of analysis. At the individual user level, stress is elicited by novel technologies and tasks including interaction with AI and robots, working in Virtual Reality, and operating autonomous vehicles. At the organisational level, novel, potentially stressful challenges include maintaining cybersecurity, surveillance and monitoring of employees supported by technology, and addressing bias and discrimination in the workplace. At the sociocultural level, technology, values and norms are evolving symbiotically, raising novel demands illustrated with respect to interactions with social media and new ethical challenges. We also briefly review the promise of neuroergonomics and emotional design to support stress mitigation. We conclude with seven high-level principles that may guide future work.</p>", "Keywords": "Stress;artificial intelligence;emotion;technology;trust;workload", "DOI": "10.1080/00140139.2024.2425953", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, George Mason University, Fairfax, VA, USA"}, {"AuthorId": 2, "Name": "Ryon Cumings", "Affiliation": "Department of Psychology, George Mason University, Fairfax, VA, USA"}, {"AuthorId": 3, "Name": "Erika P. <PERSON>", "Affiliation": "Department of Psychology, George Mason University, Fairfax, VA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Psychology, George Mason University, Fairfax, VA, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychology, George Mason University, Fairfax, VA, USA"}], "References": [{"Title": "Reality-based interaction affecting mental workload in virtual reality mental arithmetic training", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "10", "Page": "1062", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Use of virtual reality simulators for training programs in the areas of security and defense: a systematic review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "5-6", "Page": "3495", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Usability: Adoption, Measurement, Value", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "6", "Page": "956", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Emotion recognition using multi-modal data and machine learning techniques: A tutorial and review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "103", "JournalTitle": "Information Fusion"}, {"Title": "Artificial Intelligence (AI) Ethics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "74", "JournalTitle": "Journal of Database Management"}, {"Title": "Driver Emotion Recognition for Intelligent Vehicles", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "State of science: the future of work – ergonomics and human factors contributions to the field", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "4", "Page": "427", "JournalTitle": "Ergonomics"}, {"Title": "RETRACTED ARTICLE: Advanced artificial intelligence in heart rate and blood pressure monitoring for stress management", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "3329", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "How to Increase Automated Vehicles’ Acceptance through In-Vehicle Interaction Design: A Review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "308", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "The Creation and Detection of Deepfakes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Utility of Functional Transparency and Usability in UAV Supervisory Control Interface Design", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "7", "Page": "1761", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Powered by virtual realities: promoting emotional recovery through technology-based recovery interventions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "10", "Page": "1351", "JournalTitle": "Ergonomics"}, {"Title": "Social-Emotional-Sensory Design Map for Affective Computing Informed by Neurodivergent Experiences", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "The rise of artificial intelligence – understanding the AI identity threat at the workplace", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "1", "Page": "73", "JournalTitle": "Electronic Markets"}, {"Title": "A Pilot Study Exploring Age Differences in Presence, Workload, and Cybersickness in the Experience of Immersive Virtual Reality Environments", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "129", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "Robot Autonomy vs. Human Autonomy: Social Robots, Artificial Intelligence (AI), and the Nature of Autonomy", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "4", "Page": "595", "JournalTitle": "Minds and Machines"}, {"Title": "Online social connections as surrogates of face-to-face interactions: A longitudinal study under Covid-19 isolation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "107102", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The digital workplace and its dark side: An integrative review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "107118", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Mind over body: A neuroergonomic approach to assessing motor performance under stress in older adults", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "103691", "JournalTitle": "Applied Ergonomics"}, {"Title": "Driver Vigilance Decrement is More Severe During Automated Driving than Manual Driving", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "2", "Page": "574", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Mental stress and safety awareness during human-robot collaboration - Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "105", "Issue": "", "Page": "103832", "JournalTitle": "Applied Ergonomics"}, {"Title": "A narrative review of immersive virtual reality’s ergonomics and risks at the workplace: cybersickness, visual fatigue, muscular fatigue, acute stress, and mental overload", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "1", "Page": "19", "JournalTitle": "Virtual Reality"}, {"Title": "An Overview of Artificial Intelligence Ethics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "4", "Page": "799", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Algorithmic Management", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "6", "Page": "825", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Mild simulator sickness can alter heart rate variability, mental workload, and learning outcomes in a 360° virtual reality application for medical education: a post hoc analysis of a randomized controlled trial", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "4", "Page": "3345", "JournalTitle": "Virtual Reality"}, {"Title": "Why people keep falling for phishing scams: The effects of time pressure and deception cues on the detection of phishing emails", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "102937", "JournalTitle": "Computers & Security"}, {"Title": "Integration of sex and gender in interventions by students in ergonomics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "11", "Page": "1578", "JournalTitle": "Ergonomics"}, {"Title": "Attribution and Obfuscation of Neural Text Authorship: A Data Mining Perspective", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "1", "Page": "1", "JournalTitle": "ACM SIGKDD Explorations Newsletter"}, {"Title": "Affective Responses to Trust Violations in a Human-Autonomy Teaming Context: Humans Versus Robots", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "1", "Page": "23", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Examining technostress and its impact on worker well-being in the digital gig economy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "7", "Page": "206", "JournalTitle": "Internet Research"}, {"Title": "Toward Workload-Based Adaptive Automation: The Utility of fNIRS for Measuring Load in Multiple Resources in the Brain", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "22", "Page": "7404", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Trusting information systems in everyday work events – effects on cognitive resources, performance, and well-being", "Authors": "<PERSON>; <PERSON>", "PubYear": 2025, "Volume": "68", "Issue": "1", "Page": "19", "JournalTitle": "Ergonomics"}]}, {"ArticleId": 118822797, "Title": "Student Literacy Through Library Visits and Gemini AI Programs at SD Negeri Potrobangsan 2", "Abstract": "<p>People often say that literacy is the foundation for a person's lifelong education. Improving students' literacy skills is one of the main focuses in today's education world. Literacy, which includes reading, writing, and understanding information well, is an essential foundation in the learning process at all levels of education. Good literacy skills help students succeed academically and equip them with the critical and analytical thinking skills needed in everyday life. We need to consider literacy development because it is a fundamental ability that every individual must possess to navigate life in the future. This study aims to develop a strategy to improve the literacy of fifth-grade students at SD Negeri Potrobangsan 2 through a combination of library visits and the Gemini AI program. We expect library visits to cultivate students' interest in reading physical books, while the Gemini AI program serves as an interactive digital literacy medium to enhance students' learning experiences. This research approach uses a qualitative method with observation, in-depth interviews, and focus group discussions (FGD) as the primary instruments for data collection. The study results indicate that integrating traditional literacy learning with digital technology can improve students' literacy skills holistically. Students showed increased reading comprehension, higher involvement in literacy activities, and mastery of digital literacy skills. Implementing this strategy also increased motivation among students to read and utilize the library optimally. Therefore, elementary schools can apply this strategy as an effective literacy learning model.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.631", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Sri Purnami", "Affiliation": ""}], "References": []}, {"ArticleId": 118822799, "Title": "News Popularity Prediction in West Sumatera Using Autoregressive Integrated Moving Average", "Abstract": "<p>The increasing public interest in reading online news is undoubtedly a challenge for news portals as online news providers. Therefore, this research was conducted to predict news popularity in West Sumatra through the FajarSumbar.com news portal using the Autoregressive Integrated Moving Average (ARIMA) model. This research aims to develop a forecasting model that can assist in estimating the popularity of each news category so that news portals can devise more effective content strategies. The data used in this study includes the number of monthly news impressions from March 2021 to June 2024, which are grouped into various categories such as Religion Culture, Industrial Economics, Criminal Law, etc. Using the ARIMA method, which can handle time series data and overcome data non-stationarity problems through differencing and the use of grid search in optimization to find the best parameters based on the lowest evaluation metric. The results show that the ARIMA model can provide reasonably accurate predictions, although the level of accuracy varies between categories. The Mean Absolute Percentage Error (MAPE) values obtained are as follows: Religion Culture 26%, Industrial Economy 29%, Criminal Law 29%, Health 40%, Sports 38%, Tourism Entertainment 26%, Education 27%, Government Politics 31%, Social Environment 27%, and Technology 51%. The Technology and Health news categories show higher error rates than others, while Religion Culture and Tourism Entertainment have better accuracy rates. Thus, the ARIMA model can be used to predict future trends in news popularity, helping editors plan content strategies that are more relevant and interesting to readers. However, improvements are needed for news categories that have high variability.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.615", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118822801, "Title": "Comparison of Rigid Pavement Planning Using PD T-14-2003 and NAASRA 1987 Methods in Industrial Areas", "Abstract": "<p>As vital infrastructure, roads are a means of transportation access and function as a distribution route for goods and services. Road pavement plays a crucial role in highway construction, necessitating proper planning according to Indonesian standards and planning criteria to ensure smooth land transportation and provide comfort and safety for its users. This study aims to provide information on road conditions, drainage dimensions, and construction costs. Planning is crucial in every road construction design, particularly for rigid pavement, based on survey data collected from the road section. Numerous methods exist for calculating the design value of rigid pavement. We use the Pd T-14-2003 method and the NAASRA 1987 method (National Association of Australian State Road Authorities) to plan rigid pavement thickness in the industrial area of PT. Bukit Muria Jaya Kudus. The Pd T-14-2003 method yielded a JSKN of 4x107 and a CBR of 35% for the subgrade, resulting in a plate thickness of 150 mm. While the Naasra 1987 planning method achieved a JSKN of 6x107 and a CBR of 35% for the base soil, a plate thickness of 160 mm was obtained with a value of K = 80 kPa/mm. The reinforcement planning using the Pd T-14-2003 and Naasra 1987 methods involves longitudinal reinforcement D10 mm at a distance of 150 mm and transverse reinforcement D10 mm at 250 mm. The drainage dimensions at the cross-section location are width x height (1 m x 1 m), and the guard's height is 0.2 m.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.613", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118822803, "Title": "Exploring of Canva in Improving Writing Skills in English Subjects", "Abstract": "<p>The advancement of technology and information has brought significant changes in various aspects of life, including education. One of the impacts of globalization is the advancement of technology and information. Globalization cannot be separated from the rapid development of information and communication technology, the primary supporting factor. Appropriate technology can increase student learning motivation, increase interaction between students and teachers, and facilitate access to information. Educators must consider technology in learning. To present engaging and innovative education, teachers are required to follow technological developments. The intention is to use technology to facilitate the learning process. This study aims to describe the implementation of Canva and analyze its impact on improving writing skills in English subjects at SMP PGRI Saptosari. This study uses a qualitative descriptive method. Data was obtained through observation, questionnaire completion, and documentation. Observations were carried out twice. The study's results indicate improved writing skills, particularly in creating procedure texts for English learning. The obtained data concluded that the creativity assessment indicator increased from the previous average of 64.68 to 85.10. The score jumped from the lesser to the excellent category by 20.42 points. The second increase in diction or word choice resulted in an average value increase from 61.31 to 78.4, moving from the less category to the sufficient one. The last is in the organizational text, which was initially 71.81 and increased to 79.40.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.629", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Sri Purnami", "Affiliation": ""}], "References": []}, {"ArticleId": 118822939, "Title": "Evolving Paradigms of Investor Safeguards: An Analytical Review of Diplomatic Protection in the Era of Contemporary International Investment Agreements", "Abstract": "This paper examines the enduring relevance of diplomatic protection in the context of international investment law, juxtaposing its role against contemporary International Investment Agreements (IIAs), such as bilateral investment treaties (BITs) and investor-state dispute settlement (ISDS) mechanisms. Despite the proliferation of BITs and the growing reliance on ISDS, diplomatic protection remains an indispensable tool, offering unique benefits not fully replicated by modern mechanisms. This study explores how diplomatic protection continues to serve as a critical recourse for investors, particularly where BITs and ISDS systems are inapplicable or ineffective. The analysis underscores diplomatic protection's flexibility and its ability to navigate diverse legal systems, arguing that its significance persists amidst evolving investment protection frameworks. The paper concludes that diplomatic protection not only complements the landscape of international investment law but also provides a necessary safeguard for investors, reinforcing the need for a nuanced understanding of its advantages in safeguarding foreign investments.", "Keywords": "", "DOI": "10.23977/infse.2024.050421", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 93764, "JournalTitle": "Information Systems and Economics", "ISSN": "2523-6407", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 118822955, "Title": "A Survey on Machine Learning, Its Approaches and Challenges in Health Care", "Abstract": "The area of machine learning research is constantly growing, offering several opportunities for investigation and application. ML is widely utilized in various applications like finance, life science, health care, transportation, education, security etc. The health care sector has long been an early adopter of and benefited greatly from technological advances. These days, machine learning plays a key role in many health related realms, including the development of new medical procedures, the handling of patient data and records, the treatment of chronic diseases, the findings of effects of various medicines, to discover patterns from medical data sources and provide excellent capabilities to predict diseases etc. An overview of machine learning-based approaches, learning algorithms, and applications in several healthcare domains is given in this study.", "Keywords": "Machine Learning;Healthcare;Artificial Intelligence;Applications;Approaches", "DOI": "10.32628/CSEIT2410438", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Apeejay College of Fine Arts, Jalandhar, Punjab, India"}], "References": [{"Title": "Machine Learning: Algorithms, Real-World Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 118822957, "Title": "State Management in React: Redux vs Zustand - A Comprehensive Guide", "Abstract": "This comprehensive article examines the current state of React state management solutions, focusing on Redux and Zustand as primary implementation options. Through empirical data and real-world metrics, the study reveals that while Redux maintains dominance in enterprise applications with 59.6% developer adoption and serves 72% of large-scale applications, Zustand has emerged as a compelling alternative with a 66.7% satisfaction rate, particularly in medium-sized projects. The article covers performance implications, best practices, and potential pitfalls of both solutions, supported by statistical evidence showing that proper state management implementation can reduce render times by up to 42% and improve development velocity by 40%. The article demonstrates that choosing between these solutions significantly impacts project success, with factors such as application scale, team size, and complexity requirements playing crucial roles in the decision-making process.", "Keywords": "State Management;React;Redux;Zustand;Performance Optimization", "DOI": "10.32628/CSEIT24106172", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " Veeranjaneyulu Veeri", "Affiliation": "Broadcom, USA"}], "References": []}, {"ArticleId": 118822959, "Title": "Artificial Intelligence in SAP S/4HANA: Transforming Enterprise Resource Planning through Intelligent Automation", "Abstract": "This comprehensive article investigates the transformative impact of AI-enhanced SAP S/4HANA Finance across healthcare, manufacturing, scientific research, auto, food & Oil &Gas sectors, focusing on human-AI collaboration patterns and implementation outcomes. Through a mixed-methods approach analyzing 15 organizations over 18 months, the research examines how AI integration transforms traditional ERP functionalities into intelligent financial management systems. The article collected data from 450 end-users and 45 key stakeholders, employing both quantitative metrics and qualitative assessments to evaluate implementation patterns, challenges, and success factors. The findings reveal significant improvements across all sectors: healthcare organizations achieved 40% reduction in billing processing time and 15% improvement in collection rates; manufacturing entities realized 35% reduction in unplanned downtime and 22% decrease in working capital requirements; while research institutions demonstrated 45% faster grant processing and 35% improved budget forecasting accuracy. The article introduces the Adaptive Financial Intelligence Framework (AFIF) for conceptualizing human-AI collaboration in financial management, contributing to both theoretical understanding and practical implementation strategies. The article concludes that successful AI integration depends on industry-specific adaptations, comprehensive training programs, and robust governance frameworks while highlighting the critical role of human expertise in maximizing system benefits. These findings provide valuable insights for organizations pursuing AI-enhanced financial management solutions while offering a roadmap for future developments in human-AI collaboration within enterprise systems.", "Keywords": "Human-AI Collaboration in ERP;AI-Enhanced Financial Management Systems;SAP S/4HANA Implementation;Cross-Industry Digital Transformation;Adaptive Financial Intelligence Framework", "DOI": "10.32628/CSEIT24106169", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " Poornachandar Pokala", "Affiliation": "Tachyon Technologies, USA"}], "References": []}, {"ArticleId": 118823035, "Title": "Computational insights into optimal household portfolio decisions: a stochastic approach with heston model and finite difference scheme", "Abstract": "<p>This work develops and solves an intertemporal household portfolio problem to address the fundamentals of contemporary asset pricing theory. The model incorporates stochastic market volatility according to the <PERSON><PERSON> model and assumes constant wage income. The problem formulation makes use of sophisticated mathematical techniques, particularly stochastic calculus, and the stochastic control framework. The partial differential equation (PDE) is developed, and a finite difference scheme (FDS) combined with the <PERSON><PERSON><PERSON> theorem yields optimum controls. Python is used to build the numerical solution, which offers insights into the best portfolio selections for households dealing with erratic market situations. In the framework of stochastic market dynamics, this study advances our knowledge of portfolio decision problems by fusing computational methods with mathematical rigour to produce workable answers.</p>", "Keywords": "<PERSON><PERSON><PERSON><PERSON> theorem; <PERSON>ston model; Household portfolio problem; Partial differential equation; Stochastic control", "DOI": "10.1007/s41870-024-02251-9", "PubYear": 2025, "Volume": "17", "Issue": "2", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Aurora, USA; Corresponding author."}], "References": [{"Title": "EXPRL: experience and prediction based load balancing strategy for multi-controller software defined networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "4", "Page": "2155", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Using Machine Learning to Quantify the Multimedia Risk Due to Fuzzing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "25", "Page": "36685", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "LGBM: a machine learning approach for Ethereum fraud detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "3321", "JournalTitle": "International Journal of Information Technology"}, {"Title": "StockGAN: robust stock price prediction using GAN algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "5", "Page": "2309", "JournalTitle": "International Journal of Information Technology"}, {"Title": "An analysis of the robustness of UAV agriculture field coverage using multi-agent reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "4", "Page": "2317", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Enhancing the predictive capability of a mathematical model for pseudomonas aeruginosa through artificial neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "4", "Page": "2025", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Empowering Network Security through Advanced Analysis of Malware Samples: Leveraging System Metrics and Network Log Data for Informed Decision-Making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "12", "Issue": "2", "Page": "250", "JournalTitle": "International Journal of Networked and Distributed Computing"}, {"Title": "Alternative agriculture land-use transformation pathways by partial-equilibrium agricultural sector model: a mathematical approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 118823229, "Title": "Pharmacophore-guided in-silico discovery of SIRT1 inhibitors for targeted cancer therapy", "Abstract": "<p>Epigenetic modifier, Sirtuin (SIRTs) is a family of seven isoforms (SIRT1-7) and nicotinamide adenine dinucleotide (NAD+) dependent class III histone deacetylase (HDACs) protein. SIRT1 in association with the p53 protein can regulate crucial cell processes such as glucose metabolism, lipid metabolism, mitochondrial biogenesis, DNA repair, oxidative stress, apoptosis, and inflammation through the process of deacetylation. When SIRT1 deacetylates p53, it loses its tumor suppression property. To promote apoptosis and decrease cell proliferation by inhibiting SIRT1 protein and ultimately raising the acetylation of p53 to regain its tumor suppressor function. Though we have many SIRT1 protein inhibitors, they exhibited off-target effects and inefficiency at the clinical trial stage. This study has been executed to identify more potentially effective and reliable SIRT1 inhibitors that can perform better than the existing options. To do so, pharmacophore-based screening of compound libraries followed by virtual screening, pharmacokinetic, drug-likeness, and toxicity studies were conducted which gave 42 compounds to evaluate further. Subsequently, exhaustive molecular docking and molecular dynamics simulation predicted four potential hits to inhibit the SIRT1 protein better than the reference compound. Further studies such as principal components analysis, free energy landscape, and estimation of binding free energy were done which concluded Hit4 (PubChem ID: 55753455) to be a novel and potent SIRT1 small molecule inhibitor among the others. The total binding free energy for Hit4 was found to be -44.68 kcal/mol much better than the reference complex i.e., -29.38 kcal/mol.</p><p>Copyright © 2024 Elsevier Ltd. All rights reserved.</p>", "Keywords": "Anticancer;Deacetylation;Epigenetic modification;Molecular docking;Molecular dynamics simulation;Sirtuin", "DOI": "10.1016/j.compbiolchem.2024.108275", "PubYear": 2024, "Volume": "113", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Bio-Sciences and Technology, Vellore Institute of Technology, Vellore, Tamil Nadu 632014, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Bio-Sciences and Technology, Vellore Institute of Technology, Vellore, Tamil Nadu 632014, India"}], "References": []}, {"ArticleId": 118823300, "Title": "How can AR-enhanced books support early readers? Exploring literacy development through AR design principles", "Abstract": "Early readers go through critical literacy development that have a significant impact on their future reading, academic performance, and lifelong learning. Previous studies have found that AR books designed for literacy development are effective, especially with improved vocabulary and motivation (<PERSON><PERSON> et al.,2022). Much of AR-enhanced books are not designed with considerations for literacy development. This study extends previous studies by examining whether and how AR-enhanced books may support such critical literacy development through informal, recreational reading. Young readers were observed as they read a set of AR-enhanced books. Their literacy behavior consisting of literacy skills and motivation were analyzed in relation to AR design principles to explore how and when the design principles engaged literacy behavior. Findings show that AR books, even if they are not designed for literacy development, may provide opportunities for engagement and development of literacy skills and motivation. A pattern of relations between literacy behavior, including print concept, vocabulary, interest, and attentional control, and AR design principles were observed. The implications for design and implementation of AR design principles and AR-enhanced books to support different readers is discussed.", "Keywords": "", "DOI": "10.1016/j.ijcci.2024.100701", "PubYear": 2024, "Volume": "42", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "New York University, 7 East 12th Street, New York, NY, 10003, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Teachers College, Columbia University, 525 W 120th Street, Box 8, New York, NY, 10027, USA"}, {"AuthorId": 3, "Name": "Jordan Burkland", "Affiliation": "Teachers College, Columbia University, 525 W 120th Street, Box 8, New York, NY, 10027, USA"}], "References": [{"Title": "Comparing reading comprehension between children reading augmented reality and print storybooks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "103900", "JournalTitle": "Computers & Education"}, {"Title": "Augmented reality applications for K-12 education: A systematic review from the usability and user experience perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "", "Page": "100321", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "The impact of augmented reality on cognitive load and performance: A systematic review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "285", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Augmented reality technology in language learning: A\n meta‐analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "4", "Page": "929", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Ten years of augmented reality in education: A meta-analysis of (quasi-) experimental studies to investigate the impact", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "104641", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 118823321, "Title": "Performance evaluation of NLP and CNN models for disaster detection using social media data", "Abstract": "<p>The use of social media data for disaster-type identification has been turning progressively important in recent years. With the extensive dependency on social networking sites, people can share real-time information and updates about disasters, making it a valuable source of information for disaster management organizations. The use of natural language processing (NLP) and computer vision techniques can help process and examine large amounts of social media data to gain valuable insights into the nature and extent of a disaster. In this study, NLP, and convolutional neural networks (CNN) models were applied to social media data for disaster-type recognition. The language models used were BERT-Base-Uncased , DistilBERT-Base-Uncased , Twitter-RoBERTa-Base , and FinBERT . Two convolutional neural network (CNN) models, Inception v3 and DenseNet were also applied. The models were evaluated on the CrisisMMD dataset. The outcome proved that the language models achieved a uniform accuracy of 94% across disaster-related tweet classification tasks, while DistilBERT-Base-Uncased demonstrated the fastest training and testing time which is important for prompt response systems. In terms of the CNN models, DenseNet outperformed Inception v3 just by a small margin of 1 or 2% in terms of accuracy, recall, precision, and F1 score. This entails that the DistilBERT-Base-Uncased and DenseNet model has the potential to be better suited for disaster-type recognition using social media data in terms of accuracy and time.</p>", "Keywords": "Disaster response; Natural language processing (NLP); Convolutional Neural Networks (CNN); Text Classification; Image Classification", "DOI": "10.1007/s13278-024-01374-y", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Aerospace Engineering, University at Buffalo, Buffalo, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Management Department, College of Engineering and Computer Science, Arkansas State University, Jonesboro, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Management Department, College of Engineering and Computer Science, Arkansas State University, Jonesboro, USA; Corresponding author."}], "References": [{"Title": "ConvLSTMConv network: a deep learning approach for sentiment analysis in cloud computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "The Impact of Artificial intelligence and Robotics on the Future Employment Opportunities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "050", "JournalTitle": "Trends in Computer Science and  Information Technology"}, {"Title": "A novel domain and event adaptive tweet augmentation approach for enhancing the classification of crisis related tweets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>.", "PubYear": 2021, "Volume": "135", "Issue": "", "Page": "101913", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "AI-Landslide: Software for acquiring hidden insights from global landslide data using Artificial Intelligence", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "", "Page": "100177", "JournalTitle": "Software Impacts"}, {"Title": "MBiLSTMGloVe: Embedding GloVe knowledge into the corpus using multi-layer BiLSTM deep learning model for social media sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117581", "JournalTitle": "Expert Systems with Applications"}, {"Title": "VictimFinder: Harvesting rescue requests in disaster response from social media with BERT", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "95", "Issue": "", "Page": "101824", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "A novel method for improving the robustness of deep learning-based malware detectors against adversarial attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105461", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep attention based optimized Bi-LSTM for improving geospatial data ontology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "102123", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "MBi-GRUMCONV: A novel Multi Bi-GRU and Multi CNN-Based deep learning model for social media sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "A novel deep learning-based approach for malware detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106030", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A novel machine learning approach for detecting first-time-appeared malware", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "131", "Issue": "", "Page": "107801", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118823518, "Title": "RESwinT: enhanced pollen image classification with parallel window transformer and coordinate attention", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00371-024-03701-y", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "336", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "An improved local binary pattern method for pollen image classification and recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "106983", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "CSwin-PNet: A CNN-Swin Transformer combined pyramid network for breast lesion segmentation in ultrasound images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119024", "JournalTitle": "Expert Systems with Applications"}, {"Title": "SwinE-UNet3+: swin transformer encoder network for medical image segmentation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "99", "JournalTitle": "Progress in Artificial Intelligence"}, {"Title": "TransMRSR: transformer-based self-distilled generative prior for brain MRI super-resolution", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "8", "Page": "3647", "JournalTitle": "The Visual Computer"}, {"Title": "SwinT-SRNet: Swin transformer with image super-resolution reconstruction network for pollen images classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "108041", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118823592, "Title": "Refined feature enhancement network for object detection", "Abstract": "<p>Convolutional neural networks-based object detection techniques have achieved positive performances. However, due to the limitations of local receptive field, some existing object detection methods cannot effectively capture global information in feature extraction phases, and thus lead to unsatisfactory detection performance. Moreover, the feature information extracted by the backbone network may be redundant. To alleviate these problems, in this paper we propose a refined feature enhancement network (RFENet) for object detection. Specifically, we first propose a feature enhancement module (FEM) to capture more global and local information from feature maps with certain long-range dependencies. We further propose a multi-branch dilated attention mechanism (MDAM) to refine the extracted features in a weighted form, which can select more important spatial and channel information and broaden the receptive field of the network. Finally, we validate RFENet on MS-COCO2017, PASCAL VOC2012, and PASCAL VOC07+12 datasets, respectively. Compared to the baseline network, our RFENet improves by 2.4 AP on MS-COCO2017 dataset, 3.4 mAP on PASCAL VOC2012 dataset, and 2.7 mAP on PASCAL VOC07+12 dataset. Extensive experiments show that our RFENet can perform competitively on different datasets. The code is available at https://github.com/object9detection/RFENet .</p>", "Keywords": "Convolutional neural networks; Object detection; Global information; Long-range dependencies", "DOI": "10.1007/s40747-024-01622-w", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Henan University of Science and Technology, Luoyang, China"}, {"AuthorId": 2, "Name": "Yongsheng Dong", "Affiliation": "School of Information Engineering, Henan University of Science and Technology, Luoyang, China; Corresponding author."}], "References": [{"Title": "Automated bridge crack detection method based on lightweight vision models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "2", "Page": "1639", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "HRCTNet: a hybrid network with high-resolution representation for object detection in UAV image", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "6", "Page": "6437", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Computer vision-based hand gesture recognition for human-robot interaction: a review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "1", "Page": "1581", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Actor-critic objective penalty function method: an adaptive strategy for trajectory tracking in autonomous driving", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "2", "Page": "1715", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Adaptive learning point cloud and image diversity feature fusion network for 3D object detection", "Authors": "<PERSON><PERSON> Yan; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "2", "Page": "2825", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 118823636, "Title": "Adaptive representation learning and sample weighting for low-quality 3D face recognition", "Abstract": "3D face recognition (3DFR) algorithms have advanced significantly in the past two decades by leveraging facial geometric information, but they mostly focus on high-quality 3D face scans, thus limiting their practicality in real-world scenarios. Recently, with the development of affordable consumer-level depth cameras, the focus has shifted towards low-quality 3D face scans. In this paper, we propose a method for low-quality 3DFR. On one hand, our approach employs the normalizing flow to model an adaptive-form distribution for any given 3D face scan. This adaptive distributional representation learning strategy allows for more robust representations of low-quality 3D face scans (which may be caused by the scan noises, pose or occlusion variations, etc.). On the other hand, we introduce an adaptive sample weighting strategy to adjust the importance of each training sample by measuring both the difficulty of being recognized and the data quality. This adaptive sample weighting strategy can further enhance the robustness of the deep model and meanwhile improve its performance on low-quality 3DFR. Through comprehensive experiments, we demonstrate that our method can significantly improve the performance of low-quality 3DFR. For example, our method achieves competitive results on both the IIIT-D database and the Lock3DFace datasets, underscoring its effectiveness in addressing the challenges associated with low-quality 3D faces.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.111161", "PubYear": 2025, "Volume": "159", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Cuican Yu", "Affiliation": "School of Mathematics and Statistics, Xi’an Jiaotong University, Xi’an, Shaanxi, China;These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>un Sun", "Affiliation": "School of Mathematics and Statistics, Xi’an Jiaotong University, Xi’an, Shaanxi, China;These authors contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xi’an Jiaotong University, Xi’an, Shaanxi, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Ecole Centrale de Lyon and Institut Universitaire de France, Lyon, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xi’an Jiaotong University, Xi’an, Shaanxi, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xi’an Jiaotong University, Xi’an, Shaanxi, China"}], "References": [{"Title": "LOW: Training deep neural networks by learning optimal sample weights", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107585", "JournalTitle": "Pattern Recognition"}, {"Title": "Depth Map Denoising Network and Lightweight Fusion Network for Enhanced 3D Face Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "145", "Issue": "", "Page": "109936", "JournalTitle": "Pattern Recognition"}, {"Title": "The Florence multi-resolution 3D facial expression dataset", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "175", "Issue": "", "Page": "23", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "PointSurFace: Discriminative point cloud surface feature extraction for 3D face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "156", "Issue": "", "Page": "110858", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 118823752, "Title": "Gas-Kinetic Unified Algorithm for Aerodynamics Covering Various Flow Regimes by Computable Modeling of Boltzmann Equation", "Abstract": "The gas-kinetic unified algorithm (GKUA), to solve the modeling of the <PERSON><PERSON>mann equation, has been developed to study the aerothermodynamics problems with the effects of wall activation energy covering various flow regimes. The unified velocity distribution function equation could be accordingly presented on the basis of the <PERSON><PERSON><PERSON><PERSON> model. To remove the dependence of the distribution function on velocity space, the conservational discrete velocity ordinate method has been developed for hypervelocity flows. The gas-kinetic finite difference scheme is constructed to directly solve the discrete velocity distribution functions by the operator splitting technique. The discrete velocity numerical integration method with the Gauss-type weight function has been developed to evaluate the macroscopic flow variables. Specially, to model the real physical process between gas molecules and the surface, the Maxwell-type gas-surface interaction model has been presented by the MD (molecular dynamic) simulation to obtain the energy adaptability coefficient. The multi-processing domain decomposition strategy and parallel implementation of high parallel efficiency and expansibility designed for the gas-kinetic numerical method is presented with good load balance and data communication efficiency. To validate the accuracy and feasibility of the present algorithm, the supersonic flows past two-dimensional circular cylinder are simulated covering various flow regimes. The results are in good agreement with the related theoretical, DSMC (Direct Simulation Monte Carlo), N–S (Navier–Stokes), and experimental data. The hypersonic reentry flows with the effects of wall activation energy around the Tianzhou-5 cargo spacecraft are simulated by the present GKUA and the massive parallel strategy. It has been confirmed that the present algorithm from the gas-kinetic point of view probably provides a promising approach to resolve the hypersonic aerothermodynamic problems with the complete spectrum of flow regimes during the re-entry and disintegration of the large-scale spacecraft.", "Keywords": "", "DOI": "10.1016/j.compfluid.2024.106472", "PubYear": 2025, "Volume": "287", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "China Aerodynamics Research and Development Center, P.O.Box 211, Mianyang 621000, China;National Laboratory for Computational Fluid Dynamics, BUAA, Beijing 100191, China;Corresponding author at: National Lab. for CFD, No.37 Xueyuan Road, Haidian District, Beijing 100191, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Aerodynamics Research and Development Center, P.O.Box 211, Mianyang 621000, China;National Laboratory for Computational Fluid Dynamics, BUAA, Beijing 100191, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "China Aerodynamics Research and Development Center, P.O.Box 211, Mianyang 621000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Aerodynamics Research and Development Center, P.O.Box 211, Mianyang 621000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Spacecraft System Engineering, Beijing 100094, China"}], "References": [{"Title": "Three-dimensional third-order gas-kinetic scheme on hybrid unstructured meshes for Euler and Navier–<PERSON> equations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "255", "Issue": "", "Page": "105834", "JournalTitle": "Computers & Fluids"}, {"Title": "Well-balanced kinetic schemes for two-phase flows", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "268", "Issue": "", "Page": "106106", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 118823791, "Title": "Effects of control parameters of wearable robotics on muscle activity during assisted elbow flexion", "Abstract": "One way to provide assistance in a dynamic lifting task is to pre-emptively move the exoskeleton based on a predicted reference trajectory. However, the level of aggressiveness in the prediction (i.e., how far ahead in time) and the exoskeleton's degree of adherence to the reference trajectory (stiffness) are not yet fully understood. This study investigated the effects of stiffness and pre-emptive offset parameters in an impedance-controlled robotic arm on muscle activation and perceived exertion of the user. Thirteen participants were instructed to lift a load equivalent to 15% of their maximal voluntary contracted force in collaboration with a robotic arm with 40°–135° of elbow flexion in 1.12 s. Three levels of stiffness (lower: 0.1 N m deg<sup>−1</sup>, medium: 0.2 N m deg<sup>−1</sup>, and higher: 0.31 N m deg<sup>−1</sup>) and two levels of pre-emptive offsets (shorter: 0.1 s and longer: 0.4 s) were investigated. We found that (1) during 0–0.5 s (acceleration stage) of elbow flexion, a higher stiffness level and a longer pre-emptive offset decreased muscle activity; (2) during 0.5–1 s (deceleration stage) of elbow flexion, medium and higher stiffness with a shorter pre-emptive offset decreased muscle activity; (3) the perceived exertion and assistance of participants were improved with a higher stiffness and a longer pre-emptive offset, whereas cooperation was rated higher at a shorter pre-emptive offset under higher stiffness. This study reveals that the optimal parameters for stiffness and pre-emptive offsets for predictive impedance controls are different for different stages of elbow flexion.", "Keywords": "Wearable robotics; Human-robot collaboration; Compliance; Assistive effectiveness; Impedance control", "DOI": "10.1016/j.ergon.2024.103660", "PubYear": 2024, "Volume": "104", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Design, Graduate School of Design, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Science and Engineering, Saga University, Saga, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Management Engineering, Dong-A University, Busan, South Korea"}, {"AuthorId": 4, "Name": "Teerapapa Luecha", "Affiliation": "Department of Human Life Design and Science, Faculty of Design, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Human Life Design and Science, Faculty of Design, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Human Life Design and Science, Faculty of Design, Kyushu University, Fukuoka, Japan;Corresponding author"}], "References": [{"Title": "Motor performance patterns between unilateral mechanical assistance and bilateral muscle contraction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "", "Page": "103056", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Variable Impedance Control and Learning—A Review", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "177", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "How adaptation, training, and customization contribute to benefits from exoskeleton assistance", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "58", "Page": "eabf1078", "JournalTitle": "Science Robotics"}, {"Title": "The role of user preference in the customized control of robotic exoskeletons", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "64", "Page": "eabj3487", "JournalTitle": "Science Robotics"}, {"Title": "From sensing to control of lower limb exoskeleton: a systematic review", "Authors": "Yuan<PERSON> Sun; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "83", "JournalTitle": "Annual Reviews in Control"}, {"Title": "Insights into evaluating and using industrial exoskeletons: Summary report, guideline, and lessons learned from the interdisciplinary project “Exo@Work”", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "97", "Issue": "", "Page": "103494", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Sensemaking, adaptation and agency in human-exoskeleton synchrony", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "1207052", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 118823799, "Title": "Expanding Digital Literacies Beyond the Digital: Infusing Computational Thinking into Unplugged Pedagogical Tools - Two Case Studies from Mathematics Education", "Abstract": "Computational Thinking (CT), the conceptual foundations required for solving complex problems effectively and efficiently, is an imperative skill for today's learners, across disciplines and across ages. As for its importance in the wide network of digital literacies, CT has been implemented in various educational contexts, however mostly via digital artifacts, specifically in programming-related activities. This approach does not fulfill the potential of such integration for promoting learning. In this paper, we argue that digital literacies should not be bound to the digital realm, and present a novel approach to fully integrate CT into learning and teaching by the development of CT-infused “unplugged” pedagogical tools that are fully embedded in the subject matter. We demonstrate the advantages to student learning of these tools in two case studies from K-12 mathematics education: 1) An assistive tool for function investigation in high-school calculus – in this case, we point out to how CT skills that were implemented in the tool promoted students' problem solving; 2) A teaching unit for pattern sequences in elementary school – in this case, we show how students in the CT research group outperformed students in the other groups, and how they implemented CT to solve pattern sequence problems effectively and efficiently. We highlight that besides enhancing learning, this approach can help address the important issue of equity in education, and suggest ways to promote it via dedicated professional development.", "Keywords": "", "DOI": "10.1016/j.ijcci.2024.100703", "PubYear": 2024, "Volume": "42", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Education, Tel Aviv University, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Education, Tel Aviv University, Israel;Corresponding author"}], "References": [{"Title": "Assessing computational thinking: A systematic review of empirical studies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "103798", "JournalTitle": "Computers & Education"}, {"Title": "Learning to code and the acquisition of computational thinking by young children", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "104222", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 118823828, "Title": "Bias and Its Consequences : A Study of Machine Learning Performance", "Abstract": "This paper addresses the concern about bias affecting the results of machine learning models. For this purpose, it uses the Adult Income dataset from OpenML for income classification. The conditions for bias are induced by underrepresenting people that earn <= $50K in training data, thus checking the behavior of different models when encountering such a skewed distribution. Key metrics, namely accuracy and specificity (True Negative Rate), were analyzed for unbiased and biased training scenarios. The results show that Naive Bayes and Random Forest models were resistant to bias, but others, including SVM and Logistic Regression, suffered major performance drops. This study throws light on the robustness of different classifiers when exposed to biased data, requiring further bias mitigation strategies in real-world applications. This paper actually examines critically how bias in training data can significantly affect the performance of prediction, fairness, and model selection in income classification tasks.", "Keywords": "Machine Learning;Fairness in AI;Class imbalance and Bias Impact Analysis", "DOI": "10.32628/CSEIT241051088", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "BTech CSE, D.Y. Patil International University, Pune, Maharashtra, India"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": "BTech CSE, D.Y. Patil International University, Pune, Maharashtra, India"}], "References": [{"Title": "Bias and Unfairness in Machine Learning Models: A Systematic Review on Datasets, Tools, Fairness Metrics, and Identification and Mitigation Methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Fernanda V. N. Lisboa", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "15", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Survey on Machine Learning Biases and Mitigation Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Digital"}]}, {"ArticleId": 118823858, "Title": "Optimization of False Alarm Rate and Misdetection Rate for a Desired Threshold Voltage in Cooperative Communication", "Abstract": "<p>Cooperative communication system (CCS) involves collaboration among sensor nodes to transmit data more effectively, especially in scenarios with limited resources or challenging environmental conditions. Optimizing the total error rate (TER) for cooperative communication in wireless sensor networks (WSN) is a critical task to enhance the reliability and efficiency of data transmission. The link quality of a WSN can be improved by cooperative relaying with a relatively low TER. In this paper, real-coded genetic algorithm (RGA) and particle swarm optimization (PSO) are used in WSN to reduce TER. The number of nodes are varied from 1 to 16, SNR is varied from 0 dB to 20 dB, threshold is varied from 25 mV to 35 mV and mutation rate is 0.1. Minimum TER is obtained for a threshold of 25 mV to 35 mV compared to TER obtained without optimization. The optimization method provides significant improvements to achieve the desired threshold voltage with minimum false alarm rate and misdetection rate which enhances the overall performance of the CCS in WSNs.</p>", "Keywords": "", "DOI": "10.5455/jjcit.71-1721045403", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 40478, "JournalTitle": "Jordanian Journal of Computers and Information Technology", "ISSN": "2413-9351", "EISSN": "2415-1076", "Authors": [{"AuthorId": 1, "Name": "SATISH GANNAMANENI", "Affiliation": ""}, {"AuthorId": 2, "Name": "JIBENDU ROY", "Affiliation": ""}], "References": []}, {"ArticleId": 118823896, "Title": "How to maintain trust, respect sovereignty and protect privacy: a new generation of international agreements on cross-border data access", "Abstract": "", "Keywords": "", "DOI": "10.1080/23738871.2024.2422264", "PubYear": 2024, "Volume": "9", "Issue": "1", "JournalId": 23731, "JournalTitle": "Journal of Cyber Policy", "ISSN": "2373-8871", "EISSN": "2373-8898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Law Enforcement, National Security and Telecom Policy and Compliance, Microsoft, Redmond, WA, USA"}], "References": []}, {"ArticleId": 118823979, "Title": "BLOCK KALMAN FILTER WITH LINEAR COMPUTATIONAL COMPLEXITY FOR INTEGRATED VISUAL-INERTIAL NAVIGATION SYSTEMS", "Abstract": "As the use of computer vision in vehicle navigation systems increases, there is a growing need for computationally efficient optimal filtering algorithms that can jointly process measurements from inertial sensors and a large number of visual information sources. Contribution. This paper proposes a new modification of the Kalman filter (the block Kalman filter) with linear computational complexity in the number of measurement information sources. A numerically stable version of the algorithm is derived using LDL-factorization of covariance matrices. Purpose of the study. The aim is to develop an LDL-factorized block Kalman filter algorithm with linear computational complexity with respect to the number of information sources in the measurement system and demonstrate its applicability in a integrated visual-inertial navigation system. Materials and Methods. This research demonstrates the algebraic equivalence of the block Kalman filter to the standard one in a specific case. A method is proposed for approximating the estimates of the block filter to those of the standard one with desired accuracy by expanding the state vector. The computational complexity of the block Kalman filter is compared to the computational complexity of standard one within a numerical experiment. Numerical modeling of the block filter operation within a navigation system is conducted for comparison with the standard filter. Results. The equations of the block LDL-factorized Kalman filter are obtained. Its linear computational complexity with respect to the number of information sources is verified. A method for approximating the estimates of the block Kalman filter to those of the standard filter by expanding the state vector is proposed and verified within the framework of navigation system simulation. Conclusion. The main theoretical properties of the block Kalman filter were confirmed in numerical experiments. Further research will explore alternative methods of forming the extended state vector of the filter; the Kalman block filter will be tested within more complex scenarios.", "Keywords": "Kalman filter; numerical efficiency; computer vision; integrated navigation system;фильтр Калмана; численная эффективность; техническое зрение; комплексированная навигационная система", "DOI": "10.14529/ctcr240404", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 38337, "JournalTitle": "Bulletin of the South Ural State University. Ser. Computer Technologies, Automatic Control & Radioelectronics", "ISSN": "1991-976X", "EISSN": "2409-6571", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South Ural State University"}], "References": []}, {"ArticleId": 118823982, "Title": "Knowledge and data integrated paradigm for industrial operation completion time prediction", "Abstract": "<p>Accurate operation completion time prediction is of significant value for industrial production, improving the plan rationality and production efficiency. Numerous data-driven approaches have been introduced for this task. However, there are a lot of prior knowledge and domain expertise in the production environment, which may greatly facilitate the training progress and improve the model’s performance. How to integrate domain knowledge into the model learning process is a pressing issue. We explore a paradigm for the autonomous integration of data and knowledge in industrial big data, which integrate symbolic business logic and domain knowledge into the data-driven neural model. Based on this paradigm, we propose a method for the operation completion time prediction in automated container terminals. We construct a terminal relational graph, organizing multi-source data into a unified graph structure. The associated features are obtained through traversal searches under the guidance of domain knowledge. A heterogeneous graph neural network is devised to learn data patterns and their underlying symbolic domain knowledge. Experiments conducted on actual terminal industrial data containing over 880,000 records shows the effectiveness of our proposed method compared with other regression models. The results demonstrate that our method can capture associated features and integrates symbolic domain knowledge into the neural model, thereby enhancing the data’s learning ability.</p>", "Keywords": "Relational graph; Operation completion time prediction; Graph neural network", "DOI": "10.1007/s11280-024-01313-z", "PubYear": 2024, "Volume": "27", "Issue": "6", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic and Information Engineering, Tongji University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Chen", "Affiliation": "College of Electronic and Information Engineering, Tongji University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic and Information Engineering, Tongji University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Electronic and Information Engineering, Tongji University, Shanghai, China; Corresponding author."}], "References": [{"Title": "Analysis of production cycle-time distribution with a big-data approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "8", "Page": "1889", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Product Completion Time Prediction Using A Hybrid Approach Combining Deep Learning and System Model", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "311", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "KSPMI: A Knowledge-based System for Predictive Maintenance in Industry 4.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102281", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Enhancing Industry 4.0 standards interoperability via knowledge graphs with natural language processing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "140", "Issue": "", "Page": "103676", "JournalTitle": "Computers in Industry"}, {"Title": "From knowledge-based to big data analytic model: a novel IoT and machine learning based decision support system for predictive maintenance in Industry 4.0", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "1", "Page": "107", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A knowledge graph empowered online learning framework for access control decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "2", "Page": "827", "JournalTitle": "World Wide Web"}, {"Title": "Multi-temporal heterogeneous graph learning with pattern-aware attention for industrial chain risk detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Xin <PERSON><PERSON>", "PubYear": 2024, "Volume": "27", "Issue": "4", "Page": "1", "JournalTitle": "World Wide Web"}]}, {"ArticleId": *********, "Title": "The min max multi-trip drone location arc routing problem", "Abstract": "This paper studies the Min Max Multi-Trip drone Location Arc Routing Problem (MM-MT-dLARP), an arc routing problem that combines trucks and drones. We have a set of lines (usually curved) that have to be flown over by drones to perform a service (inspection, for example). There is a depot from which the trucks leave, each one carrying a drone, and a set of potential launching points where the truck can launch and pick up the drone. Drones have limited autonomy, but they can make several flights. We consider a min–max objective, in which the makespan, or time necessary to complete the service, must be minimized. Using aerial drones instead of ground vehicles allows to travel off the network: drones can enter a line through any of its points, service only a portion of that line and then exit through another of its points, without following the lines of the network. This allows for finding better solutions but also increases the difficulty of the problem. This issue can be addressed by digitizing the MM-MT-dLARP instances, approximating each line by a polygonal chain with a finite number of intermediate points, and requiring that drones can only enter and exit a line through those intermediate points. Thus, an instance of a discrete Min Max Multi-Trip Location Arc Routing Problem (MM-MT-LARP) is obtained. Here, an integer formulation for the MM-MT-LARP is proposed, some families of valid inequalities are proved to be facet-inducing of a relaxed polyhedron, and a branch-and-cut algorithm based on the strengthened formulation is developed. This algorithm has only been applied to small instances without intermediate points on the lines. In addition, we have developed a matheuristic algorithm for the MM-MT-dLARP that combines a construction phase, four local search procedures integrated into a Variable Neighborhood Descent (VND) algorithm, and a set of rules for selecting intermediate points to improve the solutions. We present the results obtained on a set of randomly generated instances involving up to 6 launching points and 88 original lines.", "Keywords": "", "DOI": "10.1016/j.cor.2024.106894", "PubYear": 2025, "Volume": "174", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departament d’Estadística i Investigació Operativa, Universitat de València, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departament de Matemàtiques per a l’Economia i l’Empresa, Universitat de València, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institut Universitari de Matemàtica Pura i Aplicada, Universitat Politècnica de València, Spain;Corresponding author"}], "References": [{"Title": "Optimization for drone and drone-truck combined operations: A review of the state of the art and future directions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "105004", "JournalTitle": "Computers & Operations Research"}, {"Title": "Coordinating drones with mothership vehicles: The mothership and drone routing problem with graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>o <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "105445", "JournalTitle": "Computers & Operations Research"}, {"Title": "A GV-drone arc routing approach for urban traffic patrol by coordinating a ground vehicle and multiple drones", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "77", "Issue": "", "Page": "101246", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A multiple-drone arc routing and mothership coordination problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>o <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "159", "Issue": "", "Page": "106322", "JournalTitle": "Computers & Operations Research"}, {"Title": "The multi‐purpose <i>K</i> ‐drones general routing problem", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "4", "Page": "437", "JournalTitle": "Networks"}]}, {"ArticleId": *********, "Title": "An Efficient DDoS Attack Detection and Prevention Model using fusion Heuristic Enhancement of Deep Learning Approach in FANET Sector", "Abstract": "<b >Problem overview</b> A Flying Ad-hoc Network (FANET) is a decentralized communication network formed by Unmanned Aerial Vehicles (UAVs). However, it faces significant security challenges due to its decentralized nature and mobility. One of the most critical threats is the Distributed Denial-of-Service (DDoS) attack, which aims to overcome the network by flooding it with malicious actions, disrupting communication, and causing system failures. The primary challenge lies in detecting and mitigating such attacks in real-time, given the dynamic and complex nature of FANETs, where the rapid movement of UAVs complicates monitoring and defending mechanisms. <b >Methodology</b> A novel detection and prevention model has been proposed to address the issue of DDoS attacks utilizing a hybrid heuristic-based machine learning approach tailored for FANET environments. The model begins by collecting necessary information using benchmark datasets to train and enhance the detection performance. The collected data is used for detecting the DDoS attack using Hybrid Deep Learning (HDL). The HDL model developed by combining Deep Temporal Convolutional Networks (DTCN) and Long Short-Term Memory (LSTM) networks, offers a powerful solution. DTCNs excel at detecting short-term, rapidly changing patterns in network traffic, which is essential for recognizing the instant effects of topology changes in FANETs. At the same time, LSTMs are designed to handle long-term dependencies and can used to the evolving patterns of UAV movement and traffic behavior over time. Here, the hyper-parameters are optimized by proposing the Hybrid of Water Strider and Cuckoo Search (HWSCS). Water Strider optimization (WSA) and Cuckoo Search Optimizer (CSO) are combined in the HWSCS algorithm. This algorithm is a versatile and powerful tool for optimizing multifaceted systems across different fields. Its combination of local and global search abilities allows it to find optimal solutions in complicated environments, making it invaluable for tasks ranging from machine learning to network security and beyond. In network security, HWSCS is effective in optimizing the parameters of intrusion detection systems, particularly in detecting complex cyber threats like DDoS attacks, ensuring more accuracy while reducing false positives. After detecting the DDoS attack, it is prevented from communication by establishing the routing process. This routing process involves rerouting the network traffic away from the affected areas and towards secure servers, effectively isolating the attack and minimizing its impact on the network. Additionally, it allows for greater flexibility and adaptability in responding to evolving threats in realtime. Here, the attack mitigation is accomplished by finding the Optimal Link State Routing (OLSR), estimated by the hybrid HWSCS algorithm. This algorithm determines the most efficient path for redirecting traffic away from the targeted servers, preventing overload, and maintaining network stability. The integration of HWSCS with OLSR not only improves network security but also proves the importance of innovative solutions in protecting malicious activities. Lastly, the model's performance is validated and measured with different metrics. <b >Results</b> The proposed model demonstrated superior performance compared to traditional models. It achieved a recall of 93.87, significantly higher than other approaches like 89.22 for MobileNet, 89.40 for DTCN, 88.44 for LSTM, and 88.35 for DTCN-LSTM. This improved detection accuracy, combined with the effective routing mechanism, ensures better prevention and mitigation of DDoS attacks in FANETs. The results confirm the model's ability to not only detect attacks but also minimize network disruption, providing a robust solution for maintaining secure and stable communication in FANET environments.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112438", "PubYear": 2024, "Volume": "167", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "SP Priyadharshini", "Affiliation": "Department of Networking and Communications, SRM Institute of Science and Technology, Kattankulathur, Chengalpattu, Tamil Nadu 603203, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Networking and Communications, SRM Institute of Science and Technology, Kattankulathur, Chengalpattu, Tamil Nadu 603203, India;Corresponding author"}], "References": [{"Title": "Enhanced Equilibrium Optimizer algorithm applied in job shop scheduling problem", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "4", "Page": "1639", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Smart defense against distributed Denial of service attack in IoT networks using supervised learning classifiers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "107726", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "High-performance intrusion detection system for networked UAVs via deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10885", "JournalTitle": "Neural Computing and Applications"}, {"Title": "High-performance intrusion detection system for networked UAVs via deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10885", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Game theoretic solution for an Unmanned Aerial Vehicle network host under DDoS attack", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "211", "Issue": "", "Page": "108962", "JournalTitle": "Computer Networks"}, {"Title": "An intelligent DDoS attack detection tree-based model using Gini index feature selection method", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "98", "Issue": "", "Page": "104823", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 118824039, "Title": "Corruption-Based Anomaly Detection and Interpretation in Tabular Data", "Abstract": "Recent advances in self-supervised learning (SSL) have proven crucial in effectively learning representations of unstructured data, encompassing text, images, and audio. Although the applications of these advances in anomaly detection have been explored extensively, applying SSL to tabular data presents challenges because of the absence of prior information on data structure. In response, we propose a framework for anomaly detection in tabular datasets using variable corruption. Through selective variable corruption and assignment of new labels based on the degree of corruption, our framework can effectively distinguish between normal and abnormal data. Furthermore, analyzing the impact of corruption on anomaly scores aids in the identification of important variables. Experimental results obtained from various tabular datasets validate the precision and applicability of the proposed method. The source code can be accessed at https://github.com/mokch/CAIT .", "Keywords": "", "DOI": "10.1016/j.patcog.2024.111149", "PubYear": 2025, "Volume": "159", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial and Management Engineering Korea University, Seoul, Republic of Korea, 02841"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial and Management Engineering Korea University, Seoul, Republic of Korea, 02841;Present/Permanent Address: 801B Innovation Hall, Korea University, 145 Anam-ro, Seongbuk-gu, Seoul, 02841, Republic of Korea;Corresponding author at: School of Industrial and Management Engineering, 145 Anam-ro, Seongbuk-gu, Korea University, Seoul, 02841, Republic of Korea"}], "References": [{"Title": "Towards explaining anomalies: A deep Taylor decomposition of one-class models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "107198", "JournalTitle": "Pattern Recognition"}, {"Title": "DenMune: Density peak based clustering using mutual nearest neighbors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107589", "JournalTitle": "Pattern Recognition"}, {"Title": "LPCL: Localized prominence contrastive learning for self-supervised dense visual pre-training", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109185", "JournalTitle": "Pattern Recognition"}, {"Title": "Interpretable Anomaly Detection with DIFFI: Depth-based feature importance of Isolation Forest", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105730", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "MF-Net: Multi-frequency intrusion detection network for Internet traffic data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "146", "Issue": "", "Page": "109999", "JournalTitle": "Pattern Recognition"}, {"Title": "Anomaly detection via gating highway connection for retinal fundus images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110167", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep anomaly detection on set data: Survey and comparison", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "151", "Issue": "", "Page": "110381", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 118824092, "Title": "Secure fuzzy retrieval protocol for multiple datasets", "Abstract": "With the diversification of data sources and the massive growth of datasets, data retrieval has become increasingly complex and time-consuming. In the traditional retrieval method, if a user wants to query multiple datasets, the general approach is to retrieve them one by one in order, which may lead to duplication of work and waste of resources. Private set intersection is a specific issue in secure multi-party computation. It allows several participants, each holding different sets, to jointly calculate the intersection of their sets without revealing any information other than the intersection. This method is naturally suitable for data fusion. In this work, we propose a secure fuzzy retrieval protocol for multiple datasets. First, we use private set intersection technology to fuse multiple datasets. Then, we perform secure retrieval based on this fused dataset, effectively avoiding the waste of resources caused by separate retrievals, thereby maximizing resource efficiency. It is worth mentioning that the protocol proposed in this paper can also be used for fuzzy retrieval to improve the user’s search experience. More importantly, the protocol can maximize privacy protection during the retrieval process, including strict protection of sensitive information such as retrieval keywords, ensuring that user data and query intentions will not be leaked during the entire retrieval process. Finally, we provide a rigorous security proof and demonstrate the effectiveness of the protocol through simulation experiments.", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110891", "PubYear": 2024, "Volume": "255", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu 610039, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu 610039, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu 610039, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu 610039, China;Department of Information Engineering, Gingko College of Hospitality Management, Chengdu 611743, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu 610039, China"}], "References": [{"Title": "Similarity query support in big data management systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "101455", "JournalTitle": "Information Systems"}, {"Title": "RETRACTED ARTICLE: FSS-SDD: fuzzy-based\n semantic search for secure data discovery from outsourced cloud data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "16", "Page": "12633", "JournalTitle": "Soft Computing"}, {"Title": "SETJoin: a novel top-k similarity join algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "19", "Page": "14577", "JournalTitle": "Soft Computing"}, {"Title": "Data fusion approach for eucalyptus trees identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "11", "Page": "4087", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "On the use of information fusion techniques to improve information quality: Taxonomy, opportunities and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "102", "JournalTitle": "Information Fusion"}, {"Title": "Toward Efficient Similarity Search under Edit Distance on Hybrid Architectures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "10", "Page": "452", "JournalTitle": "Information"}, {"Title": "Privacy-preserving and efficient data sharing for blockchain-based intelligent transportation systems", "Authors": "Shan <PERSON>; <PERSON><PERSON><PERSON>; Hanqing Wu", "PubYear": 2023, "Volume": "635", "Issue": "", "Page": "72", "JournalTitle": "Information Sciences"}, {"Title": "Multimodal data fusion for geo-hazard prediction in underground mining operation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "193", "Issue": "", "Page": "110268", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 118824175, "Title": "A circuit area optimization of MK-3 S-box", "Abstract": "<p>In MILCOM 2015, <PERSON> et al. proposed the authentication encryption algorithm MK-3, which applied the 16-bit S-box. This paper aims to implement the 16-bit S-box with less circuit area. First, we classified the irreducible polynomials over \\(\\mathbb {F}_{2^n}\\) into three kinds. Then we compared the logic gates required for multiplication over the finite field constructed by the three types of irreducible polynomials. According to the comparison result, we constructed the composite fields, \\(\\mathbb {F}_{(2^4)^2}\\) and \\(\\mathbb {F}_{(2^8)^2}\\) . Based on the isomorphism of finite fields, the operations over \\(\\mathbb {F}_{2^{16}}\\) can be conducted over \\(\\mathbb {F}_{(2^8)^2}\\) . Similarly, elements over \\(\\mathbb {F}_{2^8}\\) can be mapped to the corresponding elements over \\(\\mathbb {F}_{(2^4)^2}\\) . Next, the SAT solver was used to optimize the operations over smaller field \\(\\mathbb {F}_{2^4}\\) . At last, the architecture of the optimized MK-3 S-box was worked out. Compared with the implementation proposed by the original designer, the circuit area of the MK-3 S-box in this paper is reduced by at least 55.9%.</p>", "Keywords": "Computer Science; general;Cybercrime;Computer Applications", "DOI": "10.1186/s42400-024-00207-x", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 5427, "JournalTitle": "Cybersecurity", "ISSN": "", "EISSN": "2523-3246", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Industry Information Security Evaluation Center, The 15th Research Institute of China Electronic Technology Group Corporation, Beijing, China; Henan Key Laboratory of Network Cryptography Technology, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Electronic Science and Technology Institute, Fengtai Distric, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Electronic Science and Technology Institute, Fengtai Distric, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Information Industry Information Security Evaluation Center, The 15th Research Institute of China Electronic Technology Group Corporation, Beijing, China"}], "References": []}, {"ArticleId": 118824212, "Title": "CSSANet: A Channel Shuffle Slice-Aware Network for Pulmonary Nodule Detection", "Abstract": "Lung cancer stands as the leading cause of cancer related mortality worldwide. Precise and automated identification of lung nodules through 3D Computed Tomography (CT) scans is an essential part of screening for lung cancer effectively. Due to the small size of pulmonary nodules and the close correlation between neighboring slices of 3D CT images, most of the existing methods only consider the characteristics of a single slice, thus easily lead to insufficient detection accuracy of pulmonary nodules. To solve this problem, this paper proposes a Channel Shuffle Slice-Aware Network (CSSANet), which aims to fully exploit the spatial correlation between slices and effectively utilize the intra-slice features and inter-slice contextual information to achieve accurate detection of lung nodules. Specifically, we design a Group Shuffle Attention module (GSA module) to fuse the inter-slice feature in order to enhance the discrimination and extraction of corresponding shape information of distinct nodules in the same group of slices. Experiments and ablation study on a publicly available LUNA16 dataset demonstrate that the proposed method can enhance the detection sensitivity effectively. The Competition Performance Metric (CPM) score of 89.8 % is superior over other representative detection models.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.128827", "PubYear": 2025, "Volume": "615", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University of Finance and Economics, Jinan, China;School of Information Science and Engineering, Linyi University, Linyi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University of Finance and Economics, Jinan, China;He contributes equally to this work and shares first authorship"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University of Finance and Economics, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University of Finance and Economics, Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "cSCAN, University of Glasgow, Glasgow, G12 8QB, United Kingdom;Corresponding author"}], "References": [{"Title": "A novel randomised particle swarm optimizer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "529", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Visual saliency detection by integrating spatial position prior of object with background cues", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114219", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep learning for monocular depth estimation: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "438", "Issue": "", "Page": "14", "JournalTitle": "Neurocomputing"}, {"Title": "An improved particle swarm optimization algorithm with adaptive weighted delay velocity", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "188", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Deep multi-view learning methods: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "448", "Issue": "", "Page": "106", "JournalTitle": "Neurocomputing"}, {"Title": "A review on the attention mechanism of deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}, {"Title": "Integrating object proposal with attention networks for video saliency detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "819", "JournalTitle": "Information Sciences"}, {"Title": "Visual saliency detection via combining center prior and U-Net", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "5", "Page": "1689", "JournalTitle": "Multimedia Systems"}, {"Title": "ConvUNeXt: An efficient convolution neural network for medical image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "253", "Issue": "", "Page": "109512", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Face hallucination using multisource references and cross‐scale dual residual fusion mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "11", "Page": "9982", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 118824219, "Title": "Facility location and restoration games", "Abstract": "Effective recovery of interdependent infrastructure systems after natural disasters requires coordination between multiple infrastructure owners, such as power and telecommunications utilities. If infrastructure owners make restoration decisions in isolation from one another, then recovery may be piecemeal. A fundamental understanding of these interdependencies can provide insights to incentivize shared restoration that benefit all infrastructure users, with the goal to maximize the social welfare even in a non-cooperative setting. We introduce a non-cooperative facility location and restoration game on a layered network, where each layer belongs to a player, to model the recovery of interdependent infrastructure systems after disasters. The goal of the model is to plan short term post-disaster recovery. The players want to minimize the cost to satisfy their own demand by restoring network components, and each player can serve the other players’ demands if they are paid a fee to do so. We propose exact and approximate algorithms to set incentives (fees) so that the players’ actions at equilibrium are aligned with a social optimum of the system, which minimizes the total cost. We present a case study in which we consider the recovery efforts of telecommunication infrastructure companies and provide results for the facility location and restoration games. The models and proposed algorithms can be used to set policy, inform the structure of inter-agency mutual aid partnerships to support disaster recovery, and negotiate inter-agency usage fees prior to a disaster to ease shared recovery efforts.", "Keywords": "", "DOI": "10.1016/j.cor.2024.106896", "PubYear": 2025, "Volume": "174", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Amazon Robotics, North Reading, MA 01864, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, University of Wisconsin-Madison, 1513 University Avenue, Madison, WI 53706, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, University of Wisconsin-Madison, 1513 University Avenue, Madison, WI 53706, United States;Corresponding author"}], "References": []}, {"ArticleId": 118824229, "Title": "Hyperparameter recommendation via automated meta-feature selection embedded with kernel group Lasso learning", "Abstract": "Hyperparameter recommendation via meta-learning relies on the characterization and quality of meta-features. These meta-features provide critical information about the underlying datasets but are often selected manually based on the practitioner’s experience and preference, which can be inefficient and ineffective in many applications. In this paper, we propose a novel hyperparameter recommendation approach that integrates with a Lasso-based multivariate kernel group (KGLasso) model. The developed KGLasso model automatically identifies primary meta-features through model training. By selecting the most explanatory meta-features for a specific meta-learning task, the recommendation performance becomes much more effective. Our KGLasso model builds on a group-wise generalized multivariate Lasso approach. Within this framework, we establish a minimization algorithm using a corresponding auxiliary function, which is mathematically proven to be convergent and robust. As an application, we develop a hyperparameter recommendation system using our built KGLasso model on 120 UCI datasets for the well-known support vector machine (SVM) algorithm. This system efficiently provides competent hyperparameter recommendations for new tasks. Extensive experiments, including comparisons with popular meta-learning baselines and search algorithms, demonstrate the superiority of our proposed approach. Our results highlight the benefits of integrating model learning and feature selection to construct an automated meta-learner for hyperparameter recommendation in meta-learning.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112706", "PubYear": 2024, "Volume": "306", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of California Riverside, Riverside, CA, 92521, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical and Statistical Sciences, Southern Illinois University Carbondale, IL, 62901, USA;Corresponding author.;Research is supported in part by the , NSF-DMS, , 1854638, of the United States"}], "References": [{"Title": "Fast hyperparameter tuning using Bayesian optimization with directional derivatives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106247", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "How to tune the RBF SVM hyperparameters? An empirical evaluation of 18 search algorithms", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "4771", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Meta-features for meta-learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "240", "Issue": "", "Page": "108101", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "HEBO: An Empirical Study of Assumptions in Bayesian Optimisation", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "1269", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Latent feature learning via autoencoder training for automatic classification configuration recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "261", "Issue": "", "Page": "110218", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Classifier selection using geometry preserving feature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "28", "Page": "20955", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 118824235, "Title": "Dynamic feature and context enhancement network for faster detection of small objects", "Abstract": "While traditional object detection methods have achieved significant success in recent years, their performance in detecting small objects in aerial remote sensing images remains unsatisfactory. Small objects often occupy only a few pixels, leading to a loss of fine-grained information. This paper proposes a dynamic feature and context enhancement network (DFCE) to address noise and pixel-level region weighting issues in small object detection. The DFCE network effectively detects small objects by dynamically selecting features within regions and establishing connections between local and global contextual information. The introduced dynamic multi-dimensional attention (DMA) module selects key information via a crossover mechanism and assigns different weights to highlight important features. Based on DMA, the regional feature processing (RFP) module and multi-dimensional pool transformer (MPT) module are developed to capture key information and contextual information, respectively. Experimental results demonstrate that the DFCE network improves average precision (AP) by 3.1%, 9%, and 2.2% on two remote sensing datasets and one conventional dataset, achieving an inference speed of 30 frames per second (FPS). Given these advancements, the DFCE model’s powerful key feature extraction and contextual association capabilities show strong potential for broader applications, including sign language recognition, defect detection in industrial settings, and more.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125732", "PubYear": 2025, "Volume": "265", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan 250061, China;0009-0005-2770-5945"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan 250061, China;0009-0009-1454-968X"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University, Linyi 276000, China;0000-0001-9015-5505"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan 250061, China;0000-0002-1312-5828"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan 250061, China;0000-0001-5504-2951"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan 250061, China;Department of Information Science and Engineering, Shandong Research Institute of Industrial Technology, Jinan 250100, China;School of Information Science and Engineering, Linyi University, Linyi 276000, China;0000-0003-1618-8493.;Corresponding author at: School of Control Science and Engineering, Shandong University, Jinan 250061, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University, Linyi 276000, China;0000-0001-8788-3894"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Inspur Group Co., Ltd, Jinan 250101, China;0009-0009-6075-1221"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Macau University of Science and Technology, Macau 999078, China;0000-0002-8134-0538"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yancheng Institute of Technology, Yancheng 224051, China;0000-0002-5990-224X"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Computer Science, Nantong University, Nantong, 226019, China;0000-0002-8072-9007"}], "References": [{"Title": "Novel up-scale feature aggregation for object detection in aerial images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "411", "Issue": "", "Page": "364", "JournalTitle": "Neurocomputing"}, {"Title": "A lightweight vehicles detection network model based on YOLOv5", "Authors": "Xudong Dong; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "104914", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Tiny object detection with context enhancement and feature purification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118665", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Info-FPN: An Informative Feature Pyramid Network for object detection in remote sensing images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "119132", "JournalTitle": "Expert Systems with Applications"}, {"Title": "FDLR-Net: A feature decoupling and localization refinement network for object detection in remote sensing images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "120068", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Synthetic Aperture Radar image analysis based on deep learning: A review of a decade of research", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106305", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Cross-city matters: A multimodal remote sensing benchmark dataset for cross-city semantic segmentation using high-resolution domain adaptation networks", "Authors": "<PERSON><PERSON> Hong; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "299", "Issue": "", "Page": "113856", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Bi-AFN++CA: Bi-directional adaptive fusion network combining context augmentation for small object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "1", "Page": "614", "JournalTitle": "Applied Intelligence"}, {"Title": "Ship imaging trajectory extraction via an aggregated you only look once (YOLO) model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "130", "Issue": "", "Page": "107742", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "CasFormer: Cascaded transformers for fusion-aware computational hyperspectral imaging", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "108", "Issue": "", "Page": "102408", "JournalTitle": "Information Fusion"}, {"Title": "Feature aggregation network for small object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "255", "Issue": "", "Page": "124686", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dual-path aggregation transformer network for super-resolution with images occlusions and variability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "139", "Issue": "", "Page": "109535", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118824240, "Title": "A Novel Multi-Omics approach for Identifying Key Genes in Intervertebral Disc Degeneration", "Abstract": "<p>Many different cell types and complex molecular pathways are involved in intervertebral disc degeneration (IDD). We used a multi-omics approach combining single-cell RNA sequencing, differential gene expression analysis, and Mendelian randomization to clarify the underlying genetic architecture of IDD. We identified 1,164 differentially expressed genes (DEGs) across four important cell types associated with IDD using publicly available single-cell datasets. A thorough gene network analysis identified 122 genes that may be connected to programmed cell death, a crucial route in the etiology of IDD. SLC40A1, PTGS2, and GABARAPL1 have been identified as noteworthy regulatory genes that may impede the advancement of IDD. Furthermore, distinct cellular subpopulations and dynamic gene expression patterns were revealed by functional enrichment analysis and pseudo-temporal ordering of chondrocytes. Our results highlight the therapeutic potential of GABARAPL1, PTGS2, and SLC40A1 targeting in the treatment of IDD.</p><p>Copyright © 2024. Published by Elsevier Inc.</p>", "Keywords": "Intervertebral disc degeneration;Mendelian randomization;Programmed cell death;Single cell analysis", "DOI": "10.1016/j.slast.2024.100223", "PubYear": 2024, "Volume": "29", "Issue": "6", "JournalId": 19198, "JournalTitle": "SLAS TECHNOLOGY: Translating Life Sciences Innovation", "ISSN": "2472-6303", "EISSN": "2472-6311", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Orthopedics, Xuanwu Hospital, Capital Medical University, Beijing, China; National Clinical Research Center for Geriatric Diseases, Beijing, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Orthopedics, Xuanwu Hospital, Capital Medical University, Beijing, China; National Clinical Research Center for Geriatric Diseases, Beijing, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Orthopedics, Xuanwu Hospital, Capital Medical University, Beijing, China; National Clinical Research Center for Geriatric Diseases, Beijing, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Orthopedics, Xuanwu Hospital, Capital Medical University, Beijing, China; National Clinical Research Center for Geriatric Diseases, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Orthopedics, Xuanwu Hospital, Capital Medical University, Beijing, China; National Clinical Research Center for Geriatric Diseases, Beijing, China"}, {"AuthorId": 6, "Name": "Shibao Lu", "Affiliation": "Department of Orthopedics, Xuanwu Hospital, Capital Medical University, Beijing, China; National Clinical Research Center for Geriatric Diseases, Beijing, China"}], "References": []}, {"ArticleId": 118824267, "Title": "Improved exploration-exploitation trade-off through adaptive prioritized experience replay", "Abstract": "Experience replay is an indispensable part of deep reinforcement learning algorithms that enables the agent to revisit and reuse its past and recent experiences to update the network parameters. In many baseline off-policy algorithms, such as deep Q-networks (DQN), transitions in the replay buffer are typically sampled uniformly. This uniform sampling is not optimal for accelerating the agent’s training towards learning the optimal policy. A more selective and prioritized approach to experience sampling can yield improved learning efficiency and performance. In this regard, this work is devoted to the design of a novel prioritizing strategy to adaptively adjust the sampling probabilities of stored transitions in the replay buffer. Unlike existing sampling methods, the proposed algorithm takes into consideration the exploration–exploitation trade-off (EET) to rank transitions, which is of utmost importance in learning an optimal policy. Specifically, this approach utilizes temporal difference and Bellman errors as criteria for sampling priorities. To maintain balance in EET throughout training, the weights associated with both criteria are dynamically adjusted when constructing the sampling priorities. Additionally, any bias introduced by this sample prioritization is mitigated through assigning importance-sampling weight to each transition in the buffer. The efficacy of this prioritization scheme is assessed through training the DQN algorithm across various OpenAI Gym environments. The results obtained underscore the significance and superiority of our proposed algorithm over state-of-the-art methods. This is evidenced by its accelerated learning pace, greater cumulative reward, and higher success rate.", "Keywords": "Deep learning; Machine learning; Prioritized experience replay; Reinforcement learning", "DOI": "10.1016/j.neucom.2024.128836", "PubYear": 2025, "Volume": "614", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Western University, London, N6A 3K7, ON, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Western University, London, N6A 3K7, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Western University, London, N6A 3K7, ON, Canada"}], "References": [{"Title": "Prioritized Experience Replay based on Multi-armed Bandit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "116023", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multiadvisor Reinforcement Learning for Multiagent Multiobjective Smart Home Energy Control", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "4", "Page": "581", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Survey on reinforcement learning for language processing", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "2", "Page": "1543", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Hybrid attention-oriented experience replay for deep reinforcement learning and its application to a multi-robot cooperative hunting problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "523", "Issue": "", "Page": "44", "JournalTitle": "Neurocomputing"}, {"Title": "A deep actor critic reinforcement learning framework for learning to rank", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "547", "Issue": "", "Page": "126314", "JournalTitle": "Neurocomputing"}, {"Title": "Interpretable machine learning assessment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "561", "Issue": "", "Page": "126891", "JournalTitle": "Neurocomputing"}, {"Title": "Meta-learning for efficient unsupervised domain adaptation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "574", "Issue": "", "Page": "127264", "JournalTitle": "Neurocomputing"}, {"Title": "Supervised contrastive learning for graph representation enhancement", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "588", "Issue": "", "Page": "127710", "JournalTitle": "Neurocomputing"}, {"Title": "Mixed experience sampling for off-policy reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "251", "Issue": "", "Page": "124017", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Traffic navigation via reinforcement learning with episodic-guided prioritized experience replay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "137", "Issue": "", "Page": "109147", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118824396, "Title": "Effects of In-vehicle Auditory Interactions on Takeover Performance in SAE L2 Semi-automated Vehicles", "Abstract": "Commercially available automated vehicles require drivers to maintain focus on their driving environment and be prepared to fully control their vehicles (i.e., perform a takeover) when critical incidents occur (e.g., sudden automation failures). Therefore, drivers are discouraged from engaging in non-driving tasks that cause visual or manual distractions. Auditory interactions, despite being considered a safe alternative, can consume the attentional resources of drivers, causing them to respond poorly to critical situations. This study investigates (1) how varying levels of auditory interactions affect takeover performance and (2) what physiological contexts are related to the takeover performance in SAE Level 2 automated driving. For the investigation, 50 drivers wore wearable devices that collected various physiological signals and performed six different auditory tasks during L2 automated driving in a simulator-based experiment. The results showed that auditory interactions could degrade the takeover performance and that the task demand for auditory interactions nonlinearly affected the takeover performance, possibly owing to behavior changes intended to prevent the task difficulty from becoming excessively high. Additionally, physiological contexts such as pupil diameter, dispersion of eye movements, and inter-beat interval, were found to be related to the takeover performance. Subsequently, we discussed drivers’ behavior changes, practical deployment of in-situ physiological measures, and design implications for mitigating the degradation of takeover performance due to auditory tasks.", "Keywords": "", "DOI": "10.1016/j.ijhcs.2024.103401", "PubYear": 2025, "Volume": "196", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kangwon National University, 1 Gangwondaehakgil, Chuncheon 24341, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kangwon National University, 1 <PERSON><PERSON><PERSON>, Chuncheon 24341, South Korea;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Kangwon National University, 1 <PERSON><PERSON><PERSON>, Chuncheon 24341, South Korea;Corresponding authors"}], "References": [{"Title": "Interruptibility for In-vehicle Multitasking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Effects of Distraction in On-Road Level 2 Automated Driving: Impacts on Glance Behavior and Takeover Performance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "8", "Page": "1485", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Manipulating music to communicate automation reliability in conditionally automated driving: A driving simulator study", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "102518", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "iMon", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; JeongG<PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Exploring the influence of driver affective state and auditory display urgency on takeover performance in semi-automated vehicles: Experiment and modelling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "171", "Issue": "", "Page": "102979", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Boring But Demanding: Using Secondary Tasks to Counter the Driver Vigilance Decrement for Partially Automated Driving", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "6", "Page": "1798", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "From Driver to Supervisor: Comparing Cognitive Load and EEG-Based Attentional Resource Allocation Across Automation Levels", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "182", "Issue": "", "Page": "103169", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 118824412, "Title": "Artificial Intelligence and Employment Transformation: A Multi-Sector Analysis of Workforce Disruption and Adaptation", "Abstract": "This academic investigation examines the bifurcated impact of artificial intelligence (AI) on contemporary labor markets, analyzing both displacement effects and employment generation across multiple sectors (n=327) during 2020-2024. Through a mixed-methods approach combining econometric analysis of industry-level data, semi-structured interviews with key stakeholders (n=142), and longitudinal case studies of AI-implementing firms (n=47), we demonstrate that while AI automation has led to a 23.4% reduction in traditional middle-skill jobs across manufacturing, logistics, and administrative sectors, it has simultaneously generated a 31.7% increase in new employment categories, particularly in AI development, human-AI collaboration, and digital transformation roles. The findings reveal significant sectoral variations in job displacement rates (ranging from 8.2% to 37.6%) and identify critical factors influencing successful workforce transition, including the timing of reskilling initiatives, the nature of institutional support, and the elasticity of labor market responses. Notably, organizations that implemented proactive reskilling programs achieved a 64% higher retention rate of displaced workers compared to those utilizing reactive approaches. The article also uncovers an emerging \"adaptation gap\" wherein 42% of displaced workers face significant barriers to transitioning into new roles, primarily due to misaligned skill development programs and insufficient support infrastructure. These findings have important implications for policymakers, business leaders, and educational institutions in developing targeted interventions to facilitate effective workforce adaptation in an AI-driven economy.", "Keywords": "Artificial Intelligence Automation;Labor Market Transformation;Workforce Reskilling;Employment Displacement;Digital Skills Adaptation", "DOI": "10.32628/CSEIT24106170", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Sara Software Systems LLC., USA"}], "References": []}, {"ArticleId": 118824597, "Title": "Multi-objective cooperation search algorithm based on decomposition for complex engineering optimization and reservoir operation problems", "Abstract": "This study introduces a novel multi-objective cooperation search algorithm based on decomposition (MOCSA/D) to address multi-objective competitive challenges in engineering problem. Inspired by the optimization strategy of single-objective Cooperation Search Algorithm (CSA) and the decomposition framework of MOEA/D, MOCSA/D algorithm randomly generates initial solutions in the optimization space, and then repeatedly executes four search strategies until the end of iteration: Cooperative updating strategy gathers high-quality information to update solutions with balanced distribution. Reflective adjustment strategy expands the exploration range of the population, enabling the acquisition of solutions with strong optimization capabilities. Internal competition strategy selects superior individuals with better performance for subsequent optimization. Density updating strategy improves the competitiveness of optimized individuals within the population, fostering a more diverse solution set. Three numerical experiments (including DTLZ, WFG unconstrained test problems, ZXH_CF constrained test problems and RWMOP real-world multi-objective optimization problems) are tested to further comprehensively evaluate the dominant performance of MOCSA/D. The test results in different problem scenarios show that compared with the existing excellent evolutionary algorithms, MOCSA/D can always obtain a better, stable and uniform distribution of non-dominated solutions, and has higher solving efficiency and optimization quality under different performance evaluation metrics with the increasing difficulty of solving problems. Finally, the proposed algorithm is applied to the multi-objective reservoir engineering optimization problem to verify the feasibility of the decision scheme and the comprehensive benefit optimization of MOCSA/D. Overall, MOCSA/D can simplify the problem optimization difficulty based on decomposition mechanism, and improve the global optimization of population, path diversity and individual competition through different search strategies, which provides an advantageous tool for addressing multi-objective competitive challenges.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112442", "PubYear": 2024, "Volume": "167", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China;College of Hydrology and Water Resources, Hohai University, Nanjing 210098, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China;College of Hydrology and Water Resources, Hohai University, Nanjing 210098, China;Key Laboratory of Soil and Water Processes in Watershed, Hohai University, Nanjing 210098, China;Corresponding author at: The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China;College of Hydrology and Water Resources, Hohai University, Nanjing 210098, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bureau of Hydrology, ChangJiang Water Resources Commission, Wuhan 430010, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Hydrology and Water Resources, Hohai University, Nanjing 210098, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Environmental Science and Engineering, Suzhou University of Science and Technology, Suzhou 215009, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China"}], "References": [{"Title": "Push and pull search embedded in an M2M framework for solving constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100651", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Scalable and customizable benchmark problems for many-objective optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106139", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-objective metaheuristics for discrete optimization problems: A review of the state-of-the-art", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106382", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Benchmark-Suite of real-World constrained multi-objective optimization problems and some baseline results", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "100961", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A multi-objective particle swarm optimization algorithm based on two-archive mechanism", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "", "Page": "108532", "JournalTitle": "Applied Soft Computing"}, {"Title": "Self-adaptive multi-objective evolutionary algorithm for flexible job shop scheduling with fuzzy processing time", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "108099", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A reinforcement learning based RMOEA/D for bi-objective fuzzy flexible job shop scheduling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117380", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Efficient multi-objective meta-heuristic algorithms for energy-aware non-permutation flow-shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119077", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Evaluation of autonomous underwater vehicle motion trajectory optimization algorithms", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "276", "Issue": "", "Page": "110722", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A comprehensive survey on NSGA-II for multi-objective optimization and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "12", "Page": "15217", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A multi-objective cooperation search algorithm for cascade reservoirs operation optimization considering power generation and ecological flows", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "111085", "JournalTitle": "Applied Soft Computing"}, {"Title": "A two-stage evolutionary algorithm assisted by multi-archives for constrained multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "162", "Issue": "", "Page": "111840", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Coevolutionary algorithm using Self-organizing map approach for multimodal multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "164", "Issue": "", "Page": "111954", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 118824618, "Title": "A safety realignment framework via subspace-oriented model fusion for large language models", "Abstract": "To improve the performance of large language models (LLMs) on specific tasks, task-specific instruction fine-tuning is essential. However, this process can easily compromise the safety of a task-specific model, making it susceptible to obeying malicious instructions and generating harmful content. Current methods against fine-tuning attack usually interfere with the original fine-tuning objectives or require substantial amounts of data to realign the compromised model. To address these two major challenges, we propose reusing the initial aligned model and realigning task-specific model in the safety subspace. In this paper, we introduce a safety realignment framework through subspace-oriented model fusion (SOMF), aiming to transfer the safeguard capabilities of an initially aligned model into the current task-specific model. Our approach begins by disentangling all task vectors from the parameters of each task-specific model. We then identify safety-critical regions within these vectors by subspace masking techniques. Finally, we fuse the initial safely aligned LLM with all task vectors based on the identified safety subspace to restore the model’s safety properties. Our experiments confirm that our safety realignment framework satisfies the safety requirements of an independent task-specific model as well as traditional multitask models during their fusion. Our findings confirm that SOMF preserves safety without notably compromising performance on specific tasks while exhibiting higher data efficiency. The code is publicly available at https://github.com/xinykou/safety_realignment .", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112701", "PubYear": 2024, "Volume": "306", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Lab of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;Shanghai Institute of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;School of Computer Science and Technology, East China Normal University, Shanghai 200062, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, East China Normal University, Shanghai 200062, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Lab of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;Shanghai Institute of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;School of Computer Science and Technology, East China Normal University, Shanghai 200062, China;Corresponding author at: School of Computer Science and Technology, East China Normal University, Shanghai 200062, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Lab of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;Shanghai Institute of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;School of Computer Science and Technology, East China Normal University, Shanghai 200062, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Lab of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;Shanghai Institute of Artificial Intelligence for Education, East China Normal University, Shanghai 200062, China;School of Computer Science and Technology, East China Normal University, Shanghai 200062, China"}], "References": []}, {"ArticleId": 118824660, "Title": "The Role of Principal Leadership Management as a Driving Force in Implementing the Independent Curriculum Using AI", "Abstract": "<p>In the millennial era, characterized by constant change and progress, schools that thrive possess specific qualities and characteristics. Education not only functions as a place to transfer knowledge but also as a place to develop students' skills, character, and critical thinking abilities. In this increasingly complex world, educational institutions must adapt curricula and teaching methods that are relevant and able to meet students' diverse needs. Schools that innovate and provide captivating learning experiences will be better able to attract the attention of students and parents. In addition, educational institutions must realize the importance of building an identity and characteristics that distinguish them from other schools. This includes developing superior programs, adequate facilities, and a positive and supportive learning environment. This study investigates the role of principal leadership in implementing the Merdeka Curriculum while integrating artificial intelligence (AI) in a kindergarten/early childhood education environment. Through a qualitative approach, this study explores the experiences of a principal and staff at TK IT Tunas Mulia. Findings indicate that principals play a critical role in fostering a culture of innovation and adaptation to AI, providing the necessary training and resources for teachers to utilize AI tools effectively, and collaborating with stakeholders to ensure successful implementation. However, the study has identified challenges such as limited teacher proficiency in AI and the need for more comprehensive AI-integrated curriculum materials. While principal leadership plays a critical role, the study concludes that further support and professional development are necessary to fully harness the potential of AI in improving early childhood education.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.623", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118824661, "Title": "Implementation of Committee Partnerships on AI-Based School Policies at Public Junior High School", "Abstract": "<p>In the increasingly sophisticated digital era, artificial intelligence (AI) technology has significantly changed various fields, including education. In Indonesia, secondary schools have begun implementing AI technology to improve the quality of teaching and school management. Implementing this technology includes using AI-based systems for personalized learning, student data analysis, and more efficient school administration management. Implementing AI-based policies requires a strong partnership between the school committee and the school. This includes monitoring technology implementation, providing teacher training, and involving parents in the technology-based learning process. This study aims to identify and analyze the role of partnerships between school committees and schools in implementing AI-based policies at SMP Negeri 3 Tanjungsari. To ensure effective policy implementation, various stakeholders, including school committees, must support advancing AI technology in education. This study employs a qualitative approach, incorporating a case study method. It obtained the collected data through in-depth interviews, participant observation, and document analysis. The study results indicate that strong partnerships between school committees and schools play a significant role in supporting the implementation of AI policies. However, some challenges are still faced, such as the committee's lack of understanding of AI technology. This study also offers strategies to improve the effectiveness of these partnerships, including training and enhancing communication between the committee and the school. These findings significantly contribute to developing educational policies that are more adaptive to technological advances.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.627", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118824721, "Title": "Modified fractional power allocation for downlink cell-free massive MIMO systems", "Abstract": "Cell-free massive multiple-input multiple-output (mMIMO) significantly improves the spectral efficiency (SE) performance compared to conventional centralized mMIMO through its distributed antenna architecture. Fractional power allocation (FPA) algorithm is widely used for scalable power control with good performance in downlink (DL) of cell-free mMIMO. In this paper, we propose modified FPA (MFPA) and generalized FPA (GFPA) strategies for centralized and distributed precoding in the DL of cell-free networks, respectively. For the former, we abandon the traditional normalization of precoding vectors and introduce three adjustment parameters, which can dynamically adjust the power allocation of the DL according to the actual channel conditions. Regarding the latter, the GFPA strategy finds effective channel factors suitable for various distributed precoding schemes and correlates them with the power allocation coefficients of each user equipment (UE), enabling power allocation to adapt to multiple precoding schemes. Analysis and simulation results demonstrate that, under the MFPA strategy, UEs with poorer channel conditions can achieve higher SE, but at the expense of other UEs with better channel conditions. Under the GFPA strategy, UEs with better channel conditions experience significant SE improvements without sacrificing UEs performance with poorer channel conditions.", "Keywords": "", "DOI": "10.1016/j.phycom.2024.102537", "PubYear": 2024, "Volume": "67", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Henan University of Science and Technology, Changsha 410073, China;Correspondence to: School of Information Engineering, Henan University of Science and Technology, Luoyang, 471000, China.; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Henan University of Science and Technology, Changsha 410073, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Henan University of Science and Technology, Changsha 410073, China"}, {"AuthorId": 4, "Name": "Honghai Wu", "Affiliation": "School of Information Engineering, Henan University of Science and Technology, Changsha 410073, China"}], "References": [{"Title": "A survey on user-centric cell-free massive MIMO systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "5", "Page": "695", "JournalTitle": "Digital Communications and Networks"}]}, {"ArticleId": 118824735, "Title": "Exploring The Role of Artificial Intelligence in Library Management at Public Primary School", "Abstract": "<p>The rapid development of technology has brought significant changes in various areas of life, including the world of education. AI is a rapidly developing technology with enormous potential to improve the efficiency and effectiveness of resource management. Library management, which plays a crucial role in supporting teaching and learning activities in schools, employs AI in education. However, the increasing challenges in managing collections, the demand for rapid and accurate services, and the need for effective data management have increased the need for innovation in library management. This study aimed to explore the application of artificial intelligence (AI) in library management at SDN Piyaman 2 Wonosari, emphasizing enhancing operational efficiency, accessibility, and service quality. This study employs a qualitative descriptive approach, collecting data through interviews, observations, and questionnaires from students, teachers, and library staff. The researchers will then process, analyze, and discuss the collected data to conclude. The study results indicate that using artificial intelligence (AI) can improve library operational efficiency by reducing borrowing time, returning each book, and searching from five minutes to one minute. In addition, AI also increases user satisfaction, with an average increase from 60% to 85%. Despite the technical challenges and user adjustments, the results of this study indicate that AI has enormous potential to improve the effectiveness and efficiency of school libraries. Other schools can use this study as a model to implement AI technology in library management, making it more modern and responsive to user needs.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.626", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118824740, "Title": "Performance and Capacity Analysis of Pabringan Road in The City of Yogyakarta", "Abstract": "<p>This study aimed to ascertain the traffic volume, evaluate the performance and degree of saturation, and identify the service level category on Pabringan Road in Yogyakarta City. We surveyed to obtain primary data for the research method. We then analyzed the data using the Indonesian Road Capacity Manual 1997. According to the survey results, Pabringan Road experiences peak volumes of 792.1 pcu/hour in the morning, 979.3 pcu/hour in the afternoon, and 1103.3 pcu/hour at night. The road capacity calculation yielded a maximum capacity of 1185.52 pcu/hour. The speed on Pabringan Road decreased by 43.52% compared to the design flow speed of 23.72 km/hour, which was approximately 0.75. The analysis results indicate that during the morning peak hour, the level of service falls into category C, characterized by stable flow, increasing traffic density, and an increase in internal obstacles. During the afternoon peak hour, the service level falls into category D, indicating an approaching unstable flow and a high traffic volume. Changes in traffic flow conditions significantly impact the speed, even though it remains manageable. Moderate traffic density, fluctuations in traffic volume, internal traffic obstacles, and temporary obstacles can cause a significant speed reduction. High traffic interval obstacles significantly reduce traffic speed during the evening peak hour, causing drivers to experience short-duration congestion.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.612", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118824747, "Title": "Implementation of Artificial Intelligence Technology-Based Learning Media at SD Negeri Kotagede 1 Yogyakarta", "Abstract": "<p>In the digital era, the field of education is witnessing the transformation that occurs through the integration of artificial intelligence (AI). With artificial intelligence's (AI) potential to revolutionize various sectors, it presents itself as a material or tool that can enhance the teaching and learning experience. Its features align with our thinking, enabling us to progress according to our desired content. Artificial intelligence (AI) is today's most important tool in education. Artificial intelligence (AI) plays a crucial role in enabling the use of AI-based learning media, thereby facilitating the learning process in education. Artificial intelligence (AI)--based learning media in education will aid teachers and students in learning. This study aims to provide insight to all of us regarding the implementation of learning media based on artificial intelligence (AI) technology at SD Negeri Kotagede 1 Yogyakarta. This study employs a descriptive-qualitative approach. In the current educational landscape, particularly at SD Negeri Kotagede 1 Yogyakarta, artificial intelligence (AI) plays a crucial role. Artificial intelligence (AI) can facilitate the learning process flow by using learning media based on artificial intelligence technology. Teachers can utilize artificial intelligence to design programs and learning processes, while students can enhance their comprehension of the material and cultivate their critical thinking skills. Implementing learning media based on artificial intelligence (AI) technology can benefit educational institutions in a more interesting, innovative, effective, and creative learning process.</p>", "Keywords": "", "DOI": "10.52088/ijesty.v5i1.628", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 85999, "JournalTitle": "International Journal of Engineering, Science and Information Technology", "ISSN": "", "EISSN": "2775-2674", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118824817, "Title": "Integrating fast iterative filtering and ensemble neural network structure with attention mechanism for carbon price forecasting", "Abstract": "<p>Accurate carbon price forecasts are crucial for policymakers and enterprises to understand the dynamics of carbon price fluctuations, enabling them to formulate informed policies and investment strategies. However, due to the non-linear and non-stationary nature of carbon price, traditional models often struggle to achieve high prediction accuracy. To address this challenge, this study proposes a novel integrated prediction framework designed to enhance forecast accuracy. First, the carbon price series is decomposed into a series of smoother subsequences using fast iterative filtering (FIF). Subsequently, an integrated prediction model, AM-TCN-LSTM, is constructed, incorporating the attention mechanism (AM), temporal convolutional networks (TCN), and long short-term memory (LSTM) neural networks. The attention mechanism adaptively captures complex features from multiple factors, while the TCN-LSTM efficiently extracts temporal features from the sequences. Finally, the results from each subsequence are aggregated to generate the final prediction. Five carbon markets in china: Guangdong, Hubei, Shenzhen, Beijing, and Shanghai were selected to verify the validity of the proposed model. Various comparative models and evaluation metrics were employed to assess performance. The results demonstrate that: (1) the TCN-LSTM model achieves higher prediction accuracy compared to single models. (2) FIF is a more effective decomposition method with superior performance compared to EMD-based methods. (3) The proposed model exhibits the highest predictive capability, with MAE values of 0.0964, 0.1403, 1.9476, 2.0848, and 0.5029 for the five carbon markets, significantly outperforming comparison models. (4) The attention mechanism effectively captures the influence of multiple factors on carbon price, particularly within the short-term components.</p>", "Keywords": "Carbon price; Fast iterative filtering; Temporal convolution neural network; Long-short term memory; Attention mechanism", "DOI": "10.1007/s40747-024-01609-7", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Management Science, Chengdu University of Technology, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Management Science, Chengdu University of Technology, Chengdu, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Management Science, Chengdu University of Technology, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Management Science, Chengdu University of Technology, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "The College of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, China"}], "References": [{"Title": "A new secondary decomposition ensemble learning approach for carbon price forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106686", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A denoising carbon price forecasting method based on the integration of kernel independent component analysis and least squares support vector regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "434", "Issue": "", "Page": "67", "JournalTitle": "Neurocomputing"}, {"Title": "Forecasting China’s sovereign CDS with a decomposition reconstruction strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "107291", "JournalTitle": "Applied Soft Computing"}, {"Title": "A review on the attention mechanism of deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}, {"Title": "Rainfall and runoff time-series trend analysis using LSTM recurrent neural network and wavelet neural network with satellite-based meteorological data: case study of Nzoia hydrologic basin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "1", "Page": "213", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Stock price prediction with optimized deep LSTM network with artificial rabbits optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "227", "Issue": "", "Page": "120346", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Decomposition aided attention-based recurrent neural networks for multistep ahead time-series forecasting of renewable power generation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "", "Page": "e1795", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A multi-feature stock price prediction model based on multi-feature calculation, LASSO feature selection, and Ca-LSTM network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "1", "Page": "", "JournalTitle": "Connection Science"}, {"Title": "Forecasting bitcoin: Decomposition aided long short-term memory based time series modelling and its explanation with shapley values", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "299", "Issue": "", "Page": "112026", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "MDSTF: a multi-dimensional spatio-temporal feature fusion trajectory prediction model for autonomous driving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "5", "Page": "6647", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 118824819, "Title": "MODELS OF GRAPH THEORY AS A TOOL FOR MODELING ORGANIZATIONAL SYSTEMS (CLIQUE PROBLEM)", "Abstract": "The demand for graph theory methods in modeling the process of managing social and economic systems is primarily due to the convenience of graphical display of complex systems, which simplifies their analysis and understanding. At the same time, such a representation allows analyzing the connections between various elements of social and economic systems, which makes it possible to identify key factors that influence the system under study. The aim of the study is to consider the pos¬sibility of using graph theory as a tool for modeling organizational systems using the example of the clique problem. Methods. To solve the problems, it is proposed to use heuristic algorithms based on the use of experience and intuition. In this case, heuristics are used associated with the selection of elements with the greatest weight or the selection of elements covering the largest number of elements. A set of graph theory problems with an algorithmic connection is shown, that is, based on the algorithms for solving one of them, it is possible to obtain a solution for a number of practically important types of problems. The importance of this circumstance lies in the fact that these problems are NP-complete for solving, for which there are no effective solution algorithms. Results. An approximate scheme of application of graph theory problems as a tool for analysis and optimization of social and economic systems is given, allowing to identify the most important elements of the system, determine their interrelations and assess their impact on the overall structure and functioning. Conclusion. As a basic problem allowing to solve several other practically important problems, it is proposed to use the problem of a clique, the solution of which allows to do this. The main problem in this case is to obtain information about whether the graph obtained at the next step is a clique or not. An answer to this question is given, which greatly simplifies the process of finding a clique, and, therefore, solving a whole complex of problems using the concept of an additional graph.", "Keywords": "undirected graph; least covering problem; clique; matching; independent set of vertices; complete graph; additional graph;неориентированный граф; задача о наименьшем покрытии; клика; паросочетание; независимое множество вершин; полный граф; дополнительный граф", "DOI": "10.14529/ctcr240408", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 38337, "JournalTitle": "Bulletin of the South Ural State University. Ser. Computer Technologies, Automatic Control & Radioelectronics", "ISSN": "1991-976X", "EISSN": "2409-6571", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Voronezh State Technical University"}, {"AuthorId": 2, "Name": "P.<PERSON><PERSON>", "Affiliation": "Voronezh State Technical University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Voronezh State Technical University"}], "References": []}, {"ArticleId": 118824820, "Title": "X3D PROTOTYPING OF MANIPULATION ROBOT BODY MODELS", "Abstract": "The aim is toinvestigate the possibilities and methods for separate X3D modeling of the structure of manipulation robots and external X3D prototyping of their body models, without requiring knowledge of X3D programming of virtual worlds for their practical use. The research methods are related to 3D modeling, body system kinematics and robotics. Typical angular manipulative robots consisting of a rotating platform, a shoulder, a forearm, and a three-linked hand with a two-fingered grip at the end are considered. The results of the study contain a description of the developed ways of marking up the struc-ture and geometry of manipulation robots and three ways (simple, simple and typical) of creating a digital code, based on which an X3D model of the body is automatically generated. For each of these methods, advantages, disadvantages and limitations are noted, and cases of their effective use are recommended. Practical application of the methods described in detail will be possible for any user who has mastered digital coding of body models. This is especially important for those who do not know declarative xml-programming and in particular the extensible markup language X3D, for the study of which books and articles in Russian are very few. For those who wish to deepen their knowledge of X3D in practice here are presented and described developed X3D and JS-codes for modeling manipulation robots and their bodies. Examples of X3D layout of the structure of typical manipulation robots with six rotational kinematic pairs, which cover most angular industrial robots, are given. Conclusion. The obtained results are used for external X3D prototyping of manipulation robots and their grippers as part of X3D models of robotic technological complexes of inter-operational transportation, painting, welding, assembly in order to simulate the performance of the corresponding technological processes.", "Keywords": "xml; X3D; JS; external X3D prototyping; simulation; manipulation robot; X3D body models; RTC;xml; X3D; JS; внешнее X3D-прототипирование; симуляция; манипуляционный робот; X3D-модели тел; РТК", "DOI": "10.14529/ctcr240402", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 38337, "JournalTitle": "Bulletin of the South Ural State University. Ser. Computer Technologies, Automatic Control & Radioelectronics", "ISSN": "1991-976X", "EISSN": "2409-6571", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South Ural State University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South Ural State University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South Ural State University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South Ural State University"}], "References": []}, {"ArticleId": 118824843, "Title": "Leveraging AI for Traffic Offense Prediction: A Deep Learning Approach", "Abstract": "", "Keywords": "", "DOI": "10.7753/IJCATR1311.1007", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 17019, "JournalTitle": "International Journal of Computer Applications Technology and Research", "ISSN": "", "EISSN": "2319-8656", "Authors": [], "References": []}, {"ArticleId": 118824957, "Title": "The preparation of SiO2/GO/PVA based hydrogel sensor and its application for rapid and sensitive detection of NH3", "Abstract": "Gas sensors have received significant interest due to their miniaturization, low power consumption and high reliability. In this paper, hydrogel film with high sensing properties toward NH<sub>3</sub> were prepared using silicon dioxide (SiO<sub>2</sub>), porous graphene oxide (GO) and polyvinyl alcohol (PVA). The morphology and structure of hydrogel were characterized by scanning electron microscopy (SEM), Fourier transform infrared spectra (FT-IR) and specific surface area analysis. Compared with the conventional gas sensors based on planar GO sheets, the prepared porous SiO<sub>2</sub>/GO/PVA based hydrogel sensor could be used to detect NH<sub>3</sub> with low concentration (10 ppm) and wide range of 10–1000 ppm. At the same time, a high response rate of 118 % and an ultra-fast recovery (12 s) were achieved. Finally, a sensing mechanism for SiO<sub>2</sub>/GO/PVA based hydrogel film was proposed: NH<sub>3</sub> was adsorbed onto the surface of the film through hydrogen bonding and then reacted with the oxygen negative ions on the surface of the film to form nitrogen oxides. After degassing, oxygen was adsorbed on the surface of the film again to form oxygen negative ions. In addition, the film could monitor the freshness of fish over a period of 0–7 days, where the correlation between the resistance change and TVB-N was as high as 0.975. This work demonstrates the effectiveness of porous SiO<sub>2</sub>/GO/PVA based hydrogel film in improving gas-sensitive properties, providing a viable solution for food freshness detection, contamination tracking, and hazardous substances monitoring, etc.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136885", "PubYear": 2025, "Volume": "424", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of agricultural engineering and food science, Shandong University of Technology, Zibo, Shandong 255049, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Food Science and Biotechnology, Zhejiang Gongshang University, Hangzhou 310018, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of agricultural engineering and food science, Shandong University of Technology, Zibo, Shandong 255049, PR China;School of Materials Science and Engineering, Shandong University of Technology, Zibo, Shandong 255049, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Shandong University of Technology, Zibo, Shandong 255049, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dongying Zhuoxiao New Materials Technology Co., Ltd, Dongying, Shandong 257000, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Dongying Jindao Environmental Engineering Co., Ltd, Dongying, Shandong 257000, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of agricultural engineering and food science, Shandong University of Technology, Zibo, Shandong 255049, PR China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of agricultural engineering and food science, Shandong University of Technology, Zibo, Shandong 255049, PR China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Shandong University of Technology, Zibo, Shandong 255049, PR China;Corresponding authors"}], "References": [{"Title": "Enhanced room-temperature ammonia-sensing properties of polyaniline-modified WO3 nanoplates derived via ultrasonic spray process", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "312", "Issue": "", "Page": "127892", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Cadmium telluride/polypyrrole nanocomposite based Love wave sensors highly sensitive to acetone at room temperature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "321", "Issue": "", "Page": "128573", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Three-dimensional gold nanoparticles-modified graphene hydrogel for high-sensitive NO2 and NH3 detection with enhanced resistance to humidity", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130259", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Mixed potential NH3 sensor based on Ag-Doped La2NiO4+δ sensing electrode", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "401", "Issue": "", "Page": "134970", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Fiber colorimetric sensors with ambient humidity tolerance for NH3 sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "405", "Issue": "", "Page": "135341", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Aminated reduced graphene oxide-carbon nanotube composite gas sensors for ammonia recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "417", "Issue": "", "Page": "136088", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 118824993, "Title": "A Fine-Grained Labeled Dataset for Textual Sentiment Analysis in Technical Education", "Abstract": "<p>This paper presents a meticulously curated dataset tailored for textual sentiment analysis within the realm of technical education, falling under the domain of Natural Language Processing and Pattern Recognition. The dataset, crafted in collaboration with the All India Council for Technical Education (AICTE), encompasses over 14,000 records manually entered by representatives from technical institutes across India over the course of one year. The data, hosted on AICTE's in-house servers, has been categorized into seven distinct labels, including Appreciation, Complaint, Support, Suggestion, among others. Through a detailed data collection process facilitated by an online application, this dataset serves as a cornerstone for sentiment analysis within the domain of technical education. Notably, it is the first publicly available dataset of its kind, providing a rich resource for evaluating existing models and fostering the development of novel ones. The dataset, consisting of 14,272 records, is further enhanced with classification into 10 distinct modules, offering a nuanced understanding across various aspects of technical education. The paper outlines the experimental design, materials, and methods employed in the data collection process, along with its limitations and ethical considerations. Additionally, the paper acknowledges the contributions of the All India Council for Technical Education in facilitating the data collection process. This dataset holds significant value in advancing research and applications in sentiment analysis and related fields within the domain of technical education. Approximately 10,000 technical institutions in India operate under the jurisdiction of the All India Council for Technical Education (AICTE). To gather comprehensive data, an extensive online application with ten modules has been devised and distributed to all 10,000 institutions. Over the course of a year, labeled data has been systematically collected from these technical institutions, categorizable into seven distinct types such as Appreciation, Complaint, Support, Suggestion, and more. This rich dataset holds significant potential for applications in deep learning, including sentiment analysis and classification problems. The data, meticulously entered by representatives from these technical institutions, is stored in a highly accurate and systematic process. This dataset, characterized by its precision and reliability, stands as an excellent resource for both training and testing purposes in various deep-learning models. Its suitability extends to applications such as sentiment analysis, where the quality and authenticity of the data are crucial for robust model development. As best of our knowledge this is first kind of dataset in domain of technical education with 14,000 + samples. Very few Multiclass Multi-labeled dataset are available therefore this dataset is very much useful in applications of Natural Language Processing like Sentiment Analysis etc.</p><p>© 2024 The Author(s).</p>", "Keywords": "Deep learning;Labeled data;Natural language processing;Sentiment analysis", "DOI": "10.1016/j.dib.2024.111120", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSE & IT, Government Engineering College, Bikaner, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSE & IT, Government Engineering College, Bikaner, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "EE, SRMIST, Ghaziabad, India."}], "References": []}, {"ArticleId": 118825000, "Title": "Reliability optimization through soft digital twins", "Abstract": "<p>This paper presents a framework for developing a “soft digital twin” (SDT) for reliability optimization through modeling and simulation. The focus is on demonstrating the effectiveness of the SDT in modeling maintenance task processes within mission-critical facilities, which is particularly essential for reliable operations in critical infrastructure such as nuclear facilities. The need for efficient completion of maintenance tasks (MTs) to ensure high reliability and minimal downtime underscores the challenge faced by managers, who desire a predictive tool akin to a “crystal ball” for optimal resource configuration in the face of uncertainties from equipment failures, staffing issues, and supply chain disruptions. The proposed SDT functions as this predictive tool, leveraging the simulation’s versatility to provide insights into resource configurations and staff planning. This work extends the prior research by incorporating additional model validation, sensitivity testing, and analysis of the impact of various resource changes on the system. It employs a combination of data-driven frameworks and stochastic modeling methods to construct an adaptive SDT capable of accommodating changes in system behavior. The work provides a framework for the construction of SDT, testing and validation of its results, and its application to a case study of a mission-critical facility focused on replication and then optimizing of two key performance indicators based on human resource mixtures. Initial findings suggest that the SDT yields reliable results comparable to retrospective test datasets, offering the potential to minimize MTs’ time in the system while maximizing throughput within specified time frames through scenario experimentation and optimization.</p>", "Keywords": "", "DOI": "10.1177/00375497241288898", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4563, "JournalTitle": "SIMULATION", "ISSN": "0037-5497", "EISSN": "1741-3133", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, Knoxville, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Tennessee, Knoxville, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, Knoxville, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, Knoxville, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tennessee, Knoxville, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, Knoxville, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, Knoxville, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Consolidated Nuclear Security, LLC, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Consolidated Nuclear Security, LLC, USA"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Consolidated Nuclear Security, LLC, USA"}], "References": [{"Title": "Digital Twin for maintenance: A literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "103316", "JournalTitle": "Computers in Industry"}, {"Title": "Digital twin-driven service model and optimal allocation of manufacturing resources in shared manufacturing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "165", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A deep learning-enhanced Digital Twin framework for improving safety and reliability in human–robot collaborative manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "85", "Issue": "", "Page": "102608", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 118825055, "Title": "The influence of social distance perception among gamers on relationships between game motivation and interpersonal competency", "Abstract": "The purpose of the present study was to examine the relationships between game use negative motivation factors and interpersonal competency and explore the moderating effect of social distance on those relationships. For this, the present study collected data from 228 college students in South Korea through a survey method and used hierarchical multiple regression analyses. In the results, first, the more manipulation, customization, or immersion/escape participants pursue in gameplay, the more they decrease interpersonal competency. Second, a positive relationship between immersion/escape motivation and interpersonal competency is weaker for participants with more social exclusion experience rather than low. However, social exclusion was found to have no significance in the relationship between other motivators and interpersonal competency.", "Keywords": "", "DOI": "10.1016/j.entcom.2024.100903", "PubYear": 2025, "Volume": "52", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Interactive Media & Games Division, University of Southern California, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Business School, Sangmyung University, Korea;Corresponding author"}], "References": [{"Title": "Quality of experience (QoE) assessment of games on workstations and mobile", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "", "Page": "100362", "JournalTitle": "Entertainment Computing"}]}, {"ArticleId": *********, "Title": "Brain-targeted autoimmunity is strongly associated with Long COVID and its chronic fatigue syndrome as well as its affective symptoms", "Abstract": "<p><b>INTRODUCTION</b>:Autoimmune responses contribute to the pathophysiology of Long COVID, affective symptoms and myalgic encephalomyelitis/chronic fatigue syndrome (ME/CFS).</p><p><b>OBJECTIVES</b>:To examine whether Long COVID, and its accompanying affective symptoms and CFS are associated with immunoglobulin (Ig)A/IgM/IgG directed at neuronal proteins including myelin basic protein (MBP), myelin oligodendrocyte glycoprotein (MOG), synapsin, α + β-tubulin, neurofilament protein (NFP), cerebellar protein-2 (CP2), and the blood-brain-barrier-brain-damage (BBD) proteins claudin-5 and S100B.</p><p><b>METHODS</b>:IgA/IgM/IgG to the above neuronal proteins, human herpes virus-6 (HHV-6) and Severe Acute Respiratory Syndrome Coronavirus 2 (SARS-CoV-2) were measured in 90 Long COVID patients and 90 healthy controls, while C-reactive protein (CRP), and advanced oxidation protein products (AOPP) in association with affective and CFS ratings were additionally assessed in a subgroup thereof.</p><p><b>RESULTS</b>:Long COVID is associated with significant increases in IgG directed at tubulin (IgG-tubulin), MBP, MOG and synapsin; IgM-MBP, MOG, CP2, synapsin and BBD; and IgA-CP2 and synapsin. IgM-SARS-CoV-2 and IgM-HHV-6 antibody titers were significantly correlated with IgA/IgG/IgM-tubulin and -CP2, IgG/IgM-BBD, IgM-MOG, IgA/IgM-NFP, and IgG/IgM-synapsin. Binary logistic regression analysis shows that IgM-MBP and IgG-MBP are the best predictors of Long COVID. Multiple regression analysis shows that IgG-MOG, CRP and AOPP explain together 41.7 % of the variance in the severity of CFS. Neural network analysis shows that IgM-synapsin, IgA-MBP, IgG-MOG, IgA-synapsin, IgA-CP2, IgG-MBP and CRP are the most important predictors of affective symptoms due to Long COVID with a predictive accuracy of r = 0.801.</p><p><b>CONCLUSION</b>:Brain-targeted autoimmunity contributes significantly to the pathogenesis of Long COVID and the severity of its physio-affective phenome.</p><p>Copyright © 2024. Published by Elsevier B.V.</p>", "Keywords": "Affective disorders;Chronic fatigue syndrome;Depression;Long COVID;Neuroimmune;Oxidative stress", "DOI": "10.1016/j.jare.2024.11.011", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sichuan Provincial Center for Mental Health, Sichuan Provincial People's Hospital, School of Medicine, University of Electronic Science and Technology of China, Chengdu 610072, China; Key Laboratory of Psychosomatic Medicine, Chinese Academy of Medical Sciences, Chengdu, 610072, China; Department of Psychiatry, Faculty of Medicine, Chulalongkorn University, and King <PERSON> Memorial Hospital, the Thai Red Cross Society, Bangkok, Thailand; Medical Laboratory Technology Department, College of Medical Technology, The Islamic University, Najaf, Iraq."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Sichuan Provincial Center for Mental Health, Sichuan Provincial People's Hospital, School of Medicine, University of Electronic Science and Technology of China, Chengdu 610072, China; Key Laboratory of Psychosomatic Medicine, Chinese Academy of Medical Sciences, Chengdu, 610072, China; Department of Psychiatry, Faculty of Medicine, Chulalongkorn University, and King <PERSON>korn Memorial Hospital, the Thai Red Cross Society, Bangkok, Thailand; Cognitive Impairment and Dementia Research Unit, Faculty of Medicine, Chulalongkorn University, Bangkok, Thailand; Department of Psychiatry, Medical University of Plovdiv, Plovdiv, Bulgaria; Research Center, Medical University of Plovdiv, Plovdiv, Bulgaria; Kyung Hee University, 26 Kyungheedae-ro, Dongdaemun-gu, Seoul 02447, Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychiatry, Faculty of Medicine, Chulalongkorn University, and King <PERSON> Memorial Hospital, the Thai Red Cross Society, Bangkok, Thailand; Medical Laboratory Technology Department, College of Medical Technology, The Islamic University, Najaf, Iraq."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, College of Science, University of Kufa, Kufa, Iraq."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Immunosciences Lab, Inc., Los Angeles, CA 90035, USA; Cyrex Laboratories, LLC, Phoenix, AZ 85034, USA."}], "References": []}, {"ArticleId": 118825093, "Title": "Two sides of the same coin? Cross-linguistic sentiment comparison and thematic discovery of reader’s reception of <i>Wolf Totem</i>", "Abstract": "<p>Despite growing attention to the global dissemination of cross-cultural literature, rigorous research on cross-linguistic sentiment analysis remains limited, especially for works like Wolf Totem, which hold both worldwide reach and cultural depth. This study conducted a cross-language sentiment and thematic analysis of online book reviews by Chinese and English readers, utilizing advanced linguistic techniques such as BERT, MAXQDA, and ProtAnt. The analysis reveals that Chinese readers are more critical of the themes “Ethnicity and Culture”, “Society and Politics”, and “Wolf and Wolf Nature”, while English readers respond more positively to “Historical Context and Background”, “Plot and Theme”, and “Emotional Response and Reading Experience”. This study not only highlights these emotional variations but also provides robust methodological insights for cross-linguistic sentiment analysis, showcasing the integration of quantitative and qualitative approaches and enhancing understanding of how cultural identity shapes reader engagement.</p>", "Keywords": "", "DOI": "10.1093/llc/fqae070", "PubYear": 2025, "Volume": "40", "Issue": "1", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Foreign Languages, Guizhou University , Guiyang, 550025,"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Foreign Languages, Guizhou University , Guiyang, 550025,"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Yuan", "Affiliation": "College of Foreign Languages, Guizhou University , Guiyang, 550025,"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Foreign Languages and Literature, Fudan University , Shanghai, 200433,"}], "References": [{"Title": "Readers’ reception of translated literary work: Fortress Besieged in the digital English world", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "1", "Page": "408", "JournalTitle": "Digital Scholarship in the Humanities"}, {"Title": "From sentiment to style: <PERSON>’s rhetoric in the first crisis of the UK", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "2", "Page": "492", "JournalTitle": "Digital Scholarship in the Humanities"}, {"Title": "<PERSON>’s conspicuous negativity and <PERSON>’s hidden positivity: a sentiment comparison and exploration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "1", "Page": "280", "JournalTitle": "Digital Scholarship in the Humanities"}, {"Title": "Mining themes, emotions, and stance in the news coverage of the Russia–Ukraine War from Reuters and Xinhua", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "2", "Page": "609", "JournalTitle": "Digital Scholarship in the Humanities"}]}, {"ArticleId": 118825130, "Title": "AVR (advancing video retrieval): A new framework guided by multi-level fusion of visual and semantic Features for deep learning-based concept detection", "Abstract": "<p>Currently, there is a lack of theoretical studies that approach classification and machine learning from a general perspective, without focusing on specific applications. While these studies tackle real problems like combining low-level and high-level descriptors, they fail to offer a comprehensive approach that effectively addresses the combination issue in all cases. Consequently, several questions arise when implementing such an approach, including: (i) how to model the combination of information from low-level and high-level descriptors; and (ii) how to evaluate the method's robustness across different applications. As these questions remain open and challenging, this study aims to provide answers. In this paper, we propose a new framework called AVR: \"Advancing Video Retrieval \", which consists of three subsystems and is based on an optimized combination of low-level and high-level descriptors to enhance data retrieval accuracy. Performance analysis demonstrates that our AVR system significantly improves accuracy metrics compared to existing systems. For instance, using Corel's dataset, AVR increases average accuracy from 79.5% to 98.12% for the Beach category and from 77.75% to 98.45% for the Mountain category, surpassing the performance of our previous ISE system [38]. Furthermore, AVR achieves an average accuracy improvement of 99.32%, compared to 91.76% for ISE. Our system also outperforms our previous works, VINAS [40] and VISEN [120] systems, in terms of concept detection. For instance, the Car_Racing concept's value increases from 0.03 to 0.45 with AVR, leading to improved search results using the TRECVID dataset and a 98.41% average accuracy improvement compared to VINAS (85%) and VISEN (88%).</p>", "Keywords": "Low-level features; High-level features; Multi-level features; Textual query; Classification; Semantic indexing; Relevance feedback; Query expansion; Concept; Context; Deep learning", "DOI": "10.1007/s11042-024-20112-2", "PubYear": 2025, "Volume": "84", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "XLIM, UMR CNRS 7252, University of Limoges, Limoges, France; iL Ingénieurs, 43 Rue de Sainte Anne, Limoges, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "MIRACL Laboratory, Technopole of Sfax, University of Sfax, Sfax, Tunisia; Albaha University, Alaqiq, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "MIRACL Laboratory, Technopole of Sfax, University of Sfax, Sfax, Tunisia"}], "References": [{"Title": "An efficient bi-layer content based image retrieval system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17731", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Cell phenotype classification using multi threshold uniform local ternary patterns in fluorescence microscope images", "Authors": "Shervan Fekri-Ershad", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "12103", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "CBIR Using Features Derived by Deep Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "ACM/IMS Transactions on Data Science"}, {"Title": "Efficient text-based query based on multi-level and deep-semantic multimedia indexing and retrieval", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "18", "Page": "55811", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 118825133, "Title": "Gesture Recognition with Adaptive-Weight-Based Residual MultiheadCrossAttention Fusion Based on Multi-Level Feature Information", "Abstract": "Deep learning-based gesture recognition has attracted considerable attention owing to its vast potential across numerous applications. Current approaches predominantly focus on processing a single modality of sensor signals separately, either being limited to single temporal feature extraction—failing to capture intricate spatiotemporal dependencies, or relying on fixed time-frequency transformations—unable to flexibly adapt to the dynamic frequency characteristics of the signal. These constraints of data format give rise to reduced generalization capability across varying sensor inputs and complex tasks. In this paper, we propose a novel hybrid network to explore multi-modal representations inherent in the signals from different perspectives, namely LI-TFMNet, which integrates a cross-modal branch LI-Net and cross-domain branch TFM-Net. Specifically, the cross-modal branch (LI-Net) introduces four learnable temporal 2-Dimensionalization methods to model the relationship between temporal features and two-dimensional image-based representation. In parallel, the cross-domain branch (TFM-Net) employs a novel signal-to-image conversion approach, tailored for multi-channel time series data to fully explore the interactions among multiple sequences. Additionally, we construct three multi-scale depth-wise convolution modules to extract features from each modality at multiple scales. An adaptive-weight-based fusion strategy based on the multi-head cross-attention mechanism ARMHCA is designed to not only refine the within-modal representations but also effectively capture and leverage inter-modal correlations. Extensive experiments on multiple gesture recognition datasets demonstrate that LI-TFMNet consistently outperforms all existing methods across varying window lengths. Moreover, it achieves state-of-the-art (SOTA) performance on the UCI-HAR human activity recognition database, further showcasing its strong generalization and robustness. We anticipate that the various methodologies proposed in this study will provide new perspectives and directions for the field of time series classification.", "Keywords": "", "DOI": "10.1016/j.inffus.2024.102789", "PubYear": 2025, "Volume": "115", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Future Intelligent Wear Centre, School of Fashion and Textiles, The Hong Kong Polytechnic University, Hung Hom, Kowloon 999077, Hong Kong, China"}, {"AuthorId": 2, "Name": "Dahua <PERSON>hou", "Affiliation": "Future Intelligent Wear Centre, School of Fashion and Textiles, The Hong Kong Polytechnic University, Hung Hom, Kowloon 999077, Hong Kong, China;Corresponding author"}], "References": [{"Title": "Imaging and fusing time series for wearable sensor-based human activity recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "80", "JournalTitle": "Information Fusion"}, {"Title": "Adaptive feature fusion for time series classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "243", "Issue": "", "Page": "108459", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-level information fusion with motion constraints: Key to achieve high-precision gait analysis using low-cost inertial sensors", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "89", "Issue": "", "Page": "603", "JournalTitle": "Information Fusion"}, {"Title": "Multi-level feature fusion for multimodal human activity recognition in Internet of Healthcare Things", "Authors": "<PERSON><PERSON><PERSON>; Sheikh <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "94", "Issue": "", "Page": "17", "JournalTitle": "Information Fusion"}, {"Title": "A deep learning approach using attention mechanism and transfer learning for electromyographic hand gesture estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "121055", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Transformer-based network with temporal depthwise convolutions for sEMG recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "145", "Issue": "", "Page": "109967", "JournalTitle": "Pattern Recognition"}, {"Title": "CFI-LFENet: Infusing cross-domain fusion image and lightweight feature enhanced network for fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "104", "Issue": "", "Page": "102162", "JournalTitle": "Information Fusion"}, {"Title": "EMG-Based Automatic Gesture Recognition Using Lipschitz-Regularized Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Multi-sensor fusion federated learning method of human posture recognition for dual-arm nursing robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "107", "Issue": "", "Page": "102320", "JournalTitle": "Information Fusion"}, {"Title": "A federated transfer learning approach for surface electromyographic hand gesture recognition with emphasis on privacy preservation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "136", "Issue": "", "Page": "108952", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118825162, "Title": "HORSE-CFR: Hierarchical opponent reasoning for safe exploitation counterfactual regret minimization", "Abstract": "Opponent modeling-based game decision-making algorithms relax the assumption of rationality, having the potential to achieve higher payoffs than Nash equilibrium strategies. For opponent modeling methods, existing work primarily suffers from incompatibility between computational complexity and robustness, leading to difficulties in achieving high payoff decisions from limited historical interactions in imperfect information games. This paper introduces the HORSE-CFR algorithm, which incorporates Hierarchical Opponent Reasoning (HOR) and Safe Exploitation Counterfactual Regret Minimization (SE-CFR) to enhance decision-making robustness in imperfect information games. HOR combines neural networks with Bayesian theory to accelerate reasoning, improve interpretability, and reduce modeling errors. SE-CFR optimizes the balance between profitability and conservatism, integrating opponent modeling-based strategy adaptation into a constrained linear binary optimization framework. In experiments, HORSE-CFR outperformed Nash equilibrium strategies when playing against various opponents, increasing payoffs by 16.4% in Leduc Hold’em and 36.8% in the Transit game, respectively. It also improved payoffs by more than 9.0% compared to the best-known opponent modeling-based safe adaptive algorithm in both games.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125697", "PubYear": 2025, "Volume": "263", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, 110819, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, 110819, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, 110819, China"}], "References": [{"Title": "An opponent-adaptive strategy to increase utility and fairness in agents’ negotiation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "4", "Page": "3587", "JournalTitle": "Applied Intelligence"}, {"Title": "RLCFR: Minimize counterfactual regret by deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115953", "JournalTitle": "Expert Systems with Applications"}, {"Title": "RACP: A network with attention corrected prototype for few-shot speaker recognition using indefinite distance metric", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "490", "Issue": "", "Page": "283", "JournalTitle": "Neurocomputing"}, {"Title": "A Survey of Opponent Modeling in Adversarial Domains", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "277", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Diversifying dynamic difficulty adjustment agent by integrating player state models into Monte-Carlo tree search", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; TaeHwa Park", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117677", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Alternate inference-decision reinforcement learning with generative adversarial inferring for bridge bidding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "26", "Page": "16163", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 118825173, "Title": "A multi-input lightweight convolutional neural network for breast cancer detection considering infrared thermography", "Abstract": "Although deep convolutional neural network (CNN) has been widely used in the breast cancer detection based on thermal imaging technology, this scenario still did not receive enough attention in the mobile devices with limited resource. In addition, there still exists challenge on how to assist front view thermal imaging by side one during breast cancer detection. This study proposes a multi-input lightweight CNN named Multi-light Net in order to achieve more accurate early detection for breast cancer, which combines the thermal image from multiple perspectives with the lightweight CNN on the basis of model performance and scale. In addition, a new weighted label smoothing regularization (WLSR) is proposed for the Multi-light Net with the purpose of increasing the network’s generalization ability and classification accuracy. The experimental results demonstrate that the proposed approach by combining front view with side view can achieve more significant results than the common one using only front view during breast cancer detection, and the proposed Multi-light Net also exhibits an excellent performance with respect to the currently popular lightweight CNN. Furthermore, the proposed WLSR loss function can also lead to both faster convergence rate and more stable training process during network training and ultimately higher diagnostic accuracy for breast cancer.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125738", "PubYear": 2025, "Volume": "263", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Yun<PERSON> Tang", "Affiliation": "College of Physics and Information Engineering, Fuzhou University, Fuzhou 350108, China;0000-0001-9279-0481.;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Physics and Information Engineering, Fuzhou University, Fuzhou 350108, China;0009-0005-1177-1809"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation and Systems Engineering, Universidade Federal de Santa Catarina, Florianópolis 88040-900, SC, Brazil;0000-0001-9536-5835"}, {"AuthorId": 4, "Name": "Tao Jin", "Affiliation": "College of Electrical Engineering and Automation, Fuzhou University, Fuzhou 350108, China;0000-0003-3829-4545"}], "References": [{"Title": "A review of breast boundary and pectoral muscle segmentation methods in computer-aided detection/diagnosis of breast mammography", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "1873", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Deep and machine learning techniques for medical imaging-based breast cancer: A comprehensive review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114161", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A deep learning approach for automatic detection, segmentation and classification of breast lesions from thermal images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118774", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel small-scale pedestrian detection method base on residual block group of CenterNet", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "84", "Issue": "", "Page": "103702", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Transforming Large-Size to Lightweight Deep Neural Networks for IoT Applications", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "11", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Breast cancer detection in thermograms using a hybrid of GA and GWO based deep feature selection method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "219", "Issue": "", "Page": "119643", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Classifying breast lesions in Brazilian thermographic images using convolutional neural networks", "Authors": "Flávia R. S. Brasileiro; Delmiro D. <PERSON>; Telmo M<PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "26", "Page": "18989", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 118825183, "Title": "SD-ABM-ISM: An integrated system dynamics and agent-based modeling framework for information security management in complex information systems with multi-actor threat dynamics", "Abstract": "The increasing complexity and dynamic nature of modern Information Systems (IS) and evolving cybersecurity threats pose significant challenges for organizations in managing information security. Traditional methods often focus on isolated security aspects, failing to capture the intricate interdependencies between internal and external threats, vulnerabilities, and defensive strategies. These limitations necessitate a holistic approach that can comprehensively model and analyze the interactions within IS environments. Motivated to address these research gaps, we developed SD-ABM-ISM, a multi-method framework integrating System Dynamics (SD) and Agent-Based Modeling (ABM). This framework is designed to capture the complex dynamics of IS, incorporating insider and outsider threats and their interactions with defensive measures. SD-ABM-ISM enables an in-depth examination of how various threat actors impact security outcomes and how proactive and reactive investment strategies influence the resilience of the IS. The proposed framework provides a unique approach to understanding multi-actor threat dynamics and their effect on IS over time, facilitating informed decision-making for security investments. The framework offers a robust tool for security decision-makers, enabling organizations to align their security strategies with the evolving threat surface and enhance their resilience against cyberattacks. The detailed simulation and statistical analysis identify the influential elements in the IS over time, highlighting the impact of interactions between insider threats, outsider threats, and the IS itself in an environment characterized by high uncertainty and diverse threat behaviors. The insights from these interactions demonstrate how coordinated threats from multiple actors can amplify vulnerabilities while effective security measures can mitigate these risks. Considering proactive and reactive security investment strategies, SD-ABM-ISM provides a dynamic and cost-effective security investment strategy to protect IS from adversaries with various behaviors.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125681", "PubYear": 2025, "Volume": "263", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial and Systems Engineering Department, University of Wisconsin-Madison, Madison, 53707, WI, USA;Correspondence to: Department of Industrial and Systems Engineering, University of Wisconsin-Madison, Madison, WI 53707, USA.; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Concordia Institute for Information Systems Engineering, Montreal, 1455, QC, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Clemson University, Clemson, 29634, SC, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, Tehran, Tehran, Iran"}], "References": [{"Title": "Insight Into Insiders and IT", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Evolution of information systems research: Insights from topic modeling", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "4", "Page": "103207", "JournalTitle": "Information & Management"}, {"Title": "A comprehensive model of information security factors for decision-makers", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101747", "JournalTitle": "Computers & Security"}, {"Title": "Cybercrime threat intelligence: A systematic multi-vocal literature review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "102258", "JournalTitle": "Computers & Security"}, {"Title": "A dynamic simulation approach to support the evaluation of cyber risks and security investments in SMEs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "113580", "JournalTitle": "Decision Support Systems"}, {"Title": "Self-Bidirectional Decoupled Distillation for Time Series Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "8", "Page": "4101", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "A Survey on Graph Neural Networks for Intrusion Detection Systems: Methods, Trends and Challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "141", "Issue": "", "Page": "103821", "JournalTitle": "Computers & Security"}]}]