[{"ArticleId": 106367724, "Title": "Designing of thiazolidinones against chicken pox, monkey pox, and hepatitis viruses: A computational approach", "Abstract": "Computational designing of four different series ( D-G ) of thiazolidinone was done starting from different amines which was further condensed with various aldehydes. These underwent in silico molecular investigations for density functional theory (DFT), molecular docking, and absorption, distribution metabolism, excretion, and toxicity (ADMET) studies. The different electrochemical parameters of the compounds are predicted using quantum mechanical modeling approach with Gaussian. The docking software was used to dock the compounds against choosing PDB file for chickenpox, human immunodeficiency, hepatitis, and monkeypox virus as 1OSN, 1VZV, 6VLK, 1RTD, 3I7H, 3TYV, 4JU3, and 4QWO, respectively. The molecular interactions were visualized with discovery studio and maximum binding affinity was observed with D8 compounds against 4QWO ( - 13.383 kcal/mol) while for compound D5 against 1VZV which was −12.713 kcal/mol. Swiss ADME web tool was used to assess the drug-likeness of the designed compounds under consideration, and it is concluded that these molecules had a drug-like structure with almost zero violations.", "Keywords": "ADMET;Chickenpox;DFT;Molecular Docking;Monkey Pox;Thiazolidinone", "DOI": "10.1016/j.compbiolchem.2023.107827", "PubYear": 2023, "Volume": "103", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, <PERSON><PERSON><PERSON> Campus, University of Gujrat, Gujrat, Pakistan. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, <PERSON><PERSON><PERSON> Campus, University of Gujrat, Gujrat, Pakistan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, <PERSON><PERSON><PERSON> Campus, University of Gujrat, Gujrat, Pakistan."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, King Khalid University, Abha 61413, Saudi Arabia."}], "References": [{"Title": "QMEANDisCo—distance constraints applied on model quality estimation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "6", "Page": "1765", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 106367982, "Title": "Video Action Recognition by Combining Spatial-Temporal Cues with Graph Convolutional Networks", "Abstract": "<p>Video action recognition relies heavily on the way spatio-temporal cues are combined in order to enhance recognition accuracy. This issue can be addressed with explicit modeling of interactions among objects within or between videos, such as the graph neural network, which has been shown to accurately model and represent complicated spatial- temporal object relations for video action classification. However, the visual objects in the video are diversified, whereas the nodes in the graphs are fixed. This may result in information overload or loss if the visual objects are too redundant or insufficient for graph construction. Segment level graph convolutional networks (SLGCNs) are proposed as a method for recognizing actions in videos. The SLGCN consists of a segment-level spatial graph and a segment-level temporal graph, both of which are capable of simultaneously processing spatial and temporal information. Specifically, the segment-level spatial graph and the segment-level temporal graph are constructed using 2D and 3D CNNs to extract appearance and motion features from video segments. Graph convolutions are applied in order to obtain informative segment-level spatial-temporal features. A variety of challenging video datasets, such as EPIC-Kitchens, FCVID, HMDB51 and UCF101, are used to evaluate our method. In experiments, it is demonstrated that the SLGCN can achieve performance comparable to the state-of-the-art models in terms of obtaining spatial-temporal features.</p>", "Keywords": "", "DOI": "10.1142/S021800142350009X", "PubYear": 2023, "Volume": "37", "Issue": "10", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering, The Open University of Henan, Zhengzhou 450046, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Resource Construction and Management Center, The Open University of Henan, Zhengzhou 450046, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Resource Construction and Management Center, The Open University of Henan, Zhengzhou 450046, P. R. China"}, {"AuthorId": 4, "Name": "Lishen Pei", "Affiliation": "Department of Information Engineering, Henan University of Economics and Law, Zhengzhou 450046, P. R. China"}], "References": [{"Title": "Human Action Recognition Based on a Spatio-Temporal Video Autoencoder", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "11", "Page": "2040001", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}, {"Title": "A new financial data forecasting model using genetic algorithm and long short-term memory network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "425", "Issue": "", "Page": "207", "JournalTitle": "Neurocomputing"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "3D-TDC: A 3D temporal dilation convolution framework for video action recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "450", "Issue": "", "Page": "362", "JournalTitle": "Neurocomputing"}, {"Title": "Spatial–temporal pooling for action recognition in videos", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "451", "Issue": "", "Page": "265", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 106368053, "Title": "Optimal Voltage Distribution on PZT Actuator Pairs for Vibration Damping in Beams with Different Boundary Conditions", "Abstract": "<p>In recent decades, many studies have been conducted on the use of smart materials in order to dampen and control vibrations. Lead zirconate titanate piezoceramics (PZT) are very attractive for such applications due to their ability of delivering high energy strain in the structure. A pair of piezoelectric actuators can actively dampen the resonances of the structure, but the damping effectiveness strongly relies on its location. Damping effectiveness can be substantially increased if the structure is fully covered with PZT actuator pairs and the voltage distribution on each pair is optimized. In this way, each actuator pair contributes to the vibration attenuation and only the driving voltage’s sign, distributed on each actuator pair, needs to be identified for each resonance. This approach is here applied to the case of <PERSON>uler–<PERSON> beams with constant cross-section and the optimal voltage distribution is investigated for several boundary conditions. The theoretical model results were corroborated with finite element simulations, which were carried out considering beams covered by ten PZT actuator pairs. The numerical results agree remarkably well with the theoretical predictions for each examined case (i.e., free-free, pinned-pinned, and fixed-fixed).</p>", "Keywords": "", "DOI": "10.3390/act12020085", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial, Electronic and Mechanical Engineering, Roma Tre University, Via Vito Volterra 62, 00146 Rome, Italy; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial, Electronic and Mechanical Engineering, Roma Tre University, Via Vito Volterra 62, 00146 Rome, Italy"}], "References": [{"Title": "A Review of Piezoelectric Material-Based Structural Control and Health Monitoring Techniques for Engineering Structures: Challenges and Opportunities", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "5", "Page": "101", "JournalTitle": "Actuators"}, {"Title": "High Speed Microactuators for Low Aspect Ratio High Speed Micro Aircraft Surfaces", "Authors": "<PERSON>-<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "10", "Page": "265", "JournalTitle": "Actuators"}, {"Title": "Applications of Magnetorheological Fluid Actuator to Multi-DOF Systems: State-of-the-Art from 2015 to 2021", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "2", "Page": "44", "JournalTitle": "Actuators"}, {"Title": "A Computational Geometric Parameter Optimization of the Thermomechanical Deicing Concept", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "8", "Page": "223", "JournalTitle": "Actuators"}, {"Title": "High-Rate, High-Precision Wing Twist Actuation for Drone, Missile, and Munition Flight Control", "Authors": "<PERSON>-<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "8", "Page": "239", "JournalTitle": "Actuators"}, {"Title": "Variational Reduced-Order Modeling of Thermomechanical Shape Memory Alloy Based Cooperative Bistable Microactuators", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "36", "JournalTitle": "Actuators"}, {"Title": "Effect of the Magnetorheological Damper Dynamic Behaviour on the Rail Vehicle Comfort: Hardware-in-the-Loop Simulation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "2", "Page": "47", "JournalTitle": "Actuators"}]}, {"ArticleId": 106368215, "Title": "Using terms and informal definitions to classify domain entities into top-level ontology concepts: An approach based on language models", "Abstract": "The classification of domain entities into top-level ontology concepts remains an activity performed manually by an ontology engineer. Although some works focus on automating this task by applying machine-learning approaches using textual sentences as input, they require the existence of the domain entities in external knowledge resources, such as pre-trained embedding models. In this context, this work proposes an approach that combines the term representing the domain entity and its informal definition into a single text sentence without requiring external knowledge resources. Thus, we use this sentence as the input of a deep neural network that contains a language model as a layer. Also, we present a methodology used to extract two novel datasets from the OntoWordNet ontology based on Dolce-Lite and Dolce-Lite-Plus top-level ontologies. Our experiments show that by using the transformer-based language models, we achieve promising results in classifying domain entities into 82 top-level ontology concepts, with 94% regarding micro F1-score.", "Keywords": "", "DOI": "10.1016/j.knosys.2023.110385", "PubYear": 2023, "Volume": "265", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, Universidade Federal do Rio Grande do Sul, Porto Alegre, 91501970, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Informatics, Universidade Federal do Rio Grande do Sul, Porto Alegre, 91501970, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Informatics, Universidade Federal do Rio Grande do Sul, Porto Alegre, 91501970, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Informatics, Universidade Federal do Rio Grande do Sul, Porto Alegre, 91501970, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, Universidade Federal do Rio Grande do Sul, Porto Alegre, 91501970, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Informatics, Universidade Federal do Rio Grande do Sul, Porto Alegre, 91501970, Brazil"}], "References": [{"Title": "The GeoCore ontology: A core ontology for general use in Geology", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "104387", "JournalTitle": "Computers & Geosciences"}, {"Title": "ADOL: a novel framework for automatic domain ontology learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "1", "Page": "152", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "GeoReservoir: An ontology for deep-marine depositional system geometry description", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "159", "Issue": "", "Page": "105005", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 106368216, "Title": "Multi-constrained intelligent gliding guidance via optimal control and DQN", "Abstract": "<p>In order to improve the adaptability and robustness of gliding guidance under complex environments and multiple constraints, this study proposes an intelligent gliding guidance strategy based on the optimal guidance, predictor-corrector technique, and deep reinforcement learning (DRL). Longitudinal optimal guidance was introduced to satisfy the altitude and velocity inclination constraints, and lateral maneuvering was used to control the terminal velocity magnitude and position. The maneuvering amplitude was calculated by the analytical prediction of the terminal velocity, and the direction was learned and determined by the deep Q-learning network (DQN). In the direction decision model construction, the state and action spaces were designed based on the flight status and maneuvering direction, and a reward function was proposed using the terminal predicted state and terminal constraints. For DQN training, initial data samples were generated based on the heading-error corridor, and the experience replay pool was managed according to the terminal guidance error. The simulation results show that the intelligent gliding guidance strategy can satisfy various terminal constraints with high precision and ensure adaptability and robustness under large deviations.</p>", "Keywords": "gliding flight; optimal guidance; velocity control; deep reinforcement learning; intelligent decision", "DOI": "10.1007/s11432-022-3543-4", "PubYear": 2023, "Volume": "66", "Issue": "3", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, Xi’an, China; China Aerospace Science and Technology Corporation, Beijing, China"}], "References": []}, {"ArticleId": 106368297, "Title": "A Highly Sensitive Multimodal Tactile Sensing Module with Planar Structure for Dexterous Manipulation of Robots", "Abstract": "<p>Herein, a multimodal tactile sensing module that can improve the dexterous manipulation capabilities of robots with parallel grippers is reported. The tactile sensor consists of a 4 × 4 matrix 3-axis force sensor array (with spacings of 2 mm) and a single-temperature sensor. The tactile sensor uses inorganic silicon and gold as materials for the detection of strain and temperature and a polymer-based elastomer to encapsulate the sensing layer. The sensing module is equipped with a readout circuitry for signal processing. Within the measurable force range (≈1.5 N) of the sensor cell, a prototype module exhibits a repeatability error within 2%, a hysteresis error within 3%, and a resolution as small as 10 mN. Furthermore, each sensor cell independently measures 3-axis forces with a cross-talk error of approximately 3%. The temperature sensor exhibits linear output properties in the approximate range of 5–75 °C. Experiments are performed by mounting the module on a parallel gripper to grasp a paper cup and a reflex hammer toy. According to the experimental results, the sensor can accurately detect the state of contact with an object by analyzing three tactile modalities (i.e., 3-axis force distribution, vibration, and temperature).</p>", "Keywords": "dexterous manipulation;electronic skin;multimodality;robotics;tactile sensor", "DOI": "10.1002/aisy.202200381", "PubYear": 2023, "Volume": "5", "Issue": "6", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "Bo-Gyu Bok", "Affiliation": "Mechanical Metrology Group Korea Research Institute of Standards and Science (KRISS)  267 Gaj<PERSON>g-ro, Yuseong-gu Daejeon 34113 Republic of Korea;Department of Precision Measurement University of Science and Technology (UST)  217, Gajeong-ro, Yuseong-gu Daejeon 34113 Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Metrology Group Korea Research Institute of Standards and Science (KRISS)  267 Gajeong-ro, Yuseong-gu Daejeon 34113 Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Metrology Group Korea Research Institute of Standards and Science (KRISS)  267 Gaj<PERSON>g-ro, Yuseong-gu Daejeon 34113 Republic of Korea;Department of Precision Measurement University of Science and Technology (UST)  217, Gajeong-ro, Yuseong-gu Daejeon 34113 Republic of Korea"}], "References": [{"Title": "Wearable and Stretchable Strain Sensors: Materials, Sensing Mechanisms, and Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "8", "Page": "2000039", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 106368306, "Title": "SimDCL: dropout-based simple graph contrastive learning for recommendation", "Abstract": "Representation learning of users and items is the core of recommendation, and benefited from the development of graph neural network (GNN), graph collaborative filtering (GCF) for capturing higher order connectivity has been successful in the recommendation domain. Nevertheless, the matrix sparsity problem in collaborative filtering and the tendency of higher order embeddings to smooth in GNN limit further performance improvements. Contrastive learning (CL) was introduced into GCF and alleviated these problems to some extent. However, existing methods usually require graph perturbation to construct augmented views or design complex CL tasks, which limits the further development of CL-based methods in the recommendation. We propose a simple CL framework that does not require graph augmentation, but is based on dropout techniques to generate contrastive views to address the aforementioned problem. Specifically, we first added dropout operation to the GNN computation, and then fed the same batch of samples twice into the network for computation. Using the randomness of dropout, a pair of views with random noise was obtained, and maximizing the similarity of the view pairs is set as an auxiliary task to complement the recommendation. In addition, we made a simple modification to the computation of the GNN to alleviate the information loss due to embedding smoothing by means of cross-layer connected graph convolution computation. We named our proposed method as Simple Contrastive Learning Graph Neural Network based on dropout (SimDCL). Extensive experiments on five public datasets demonstrate the effectiveness of the proposed SimDCL, especially on the Amazon Books and Ta-Feng datasets, where our approach achieves 44% and 43% performance gains compared to baseline.", "Keywords": "Recommendation; Collaborative filtering; Graph neural network; Contrastive learning", "DOI": "10.1007/s40747-023-00974-z", "PubYear": 2023, "Volume": "9", "Issue": "5", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University Linyi City, Shandong Province, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University Linyi City, Shandong Province, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University Linyi City, Shandong Province, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University Linyi City, Shandong Province, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University Linyi City, Shandong Province, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electronic Engineering, Linyi University Linyi City, Shandong Province, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University Linyi City, Shandong Province, China"}], "References": [{"Title": "Catalysis of neural activation functions: Adaptive feed-forward training for big data applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "12", "Page": "13364", "JournalTitle": "Applied Intelligence"}, {"Title": "Dissipativity-based finite-time asynchronous output feedback control for wind turbine system via a hidden <PERSON>ov model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "15", "Page": "3177", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Collaborative representation learning for nodes and relations via heterogeneous graph neural network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "255", "Issue": "", "Page": "109673", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Recommendation Algorithm Based on a Self-supervised Learning Pretrain Transformer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "4", "Page": "4481", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 106368321, "Title": "Cyber security for critical energy infrastructures – recommended actions for the implementation of a Zero-Trust-Architecture", "Abstract": "<p lang=\"de\"> Zusammenfassung <p>Kritische Infrastrukturen – wie diejenigen der Sektoren Wasser, Energie und Ernährung – bilden die Grundlage einer funktionierenden, modernen Gesellschaft. Eine Kompromittierung dieser Infrastrukturen kann zu weitreichenden Störungen und Gefahren für Leib und Leben führen. Der Schutz sowie die Sicherstellung des Betriebs kritischer Infrastrukturen sind deshalb von entscheidender Bedeutung. Während in der Vergangenheit hauptsächlich der physische Schutz vor Angriffen im Mittelpunkt stand, entstehen durch die zunehmende Digitalisierung kritischer Infrastrukturen zusätzliche Angriffspunkte und Risiken. Im Gegensatz zu herkömmlichen Ansätzen zur Absicherung kritischer Energieinfrastrukturen kann eine Absicherung mithilfe einer Zero-Trust-Architektur die mit diesen Entwicklungen einhergehenden Anforderungen erfüllen.</p><p>Aufgrund der verhältnismäßig geringen Verbreitung von Zero-Trust-Architekturen im kritischen Energieinfrastruktursektor existiert bisher allerdings nur unzureichend praxisrelevante Literatur zur Entwicklung und Implementierung einer solchen Architektur. Diese Arbeit stellt daher sowohl die Erfahrungen aus einem laufenden Entwicklungs- und Implementierungsprojekt als auch die hiervon abgeleiteten technischen und organisationalen Handlungsempfehlungen im Rahmen eines Action-Design-Forschungsansatzes vor und trägt dadurch zur Schließung dieser Forschungslücke bei.</p></p>", "Keywords": "Cyber-Sicherheit; Zero-Trust-Architektur; Kritische Infrastruktur; Netzwerkarchitektur; Action-design Forschung; Cyber-Security; Zero-Trust-Architecture; Critical infrastructure; Network architecture; Action-design research", "DOI": "10.1365/s40702-023-00944-6", "PubYear": 2023, "Volume": "60", "Issue": "2", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universität Bayreuth, Bayreuth, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universität Bayreuth, Bayreuth, Deutschland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universität Bayreuth, Bayreuth, Deutschland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universität Bayreuth, Bayreuth, Deutschland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "TRUSTEQ GmbH, München, Deutschland"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Universität Bayreuth, Bayreuth, Deutschland"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universität Bayreuth, Bayreuth, Deutschland"}], "References": [{"Title": "Never trust, always verify: A multivocal literature review on current knowledge and research gaps of zero-trust", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102436", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 106368433, "Title": "The Design of Fast and Lightweight Resemblance Detection for Efficient Post-Deduplication Delta Compression", "Abstract": "<p>Post-deduplication delta compression is a data reduction technique that calculates and stores the differences of the very similar but non-duplicate chunks in storage systems, which is able to achieve a very high compression ratio. However, the low throughput of widely-used resemblance detection approaches (e.g., N-Transform) usually becomes the bottleneck of delta compression systems due to introducing high computational overhead. Generally, this overhead mainly consists of two parts: ① calculating the rolling hash byte-by-byte across data chunks and ② applying multiple transforms on all the calculated rolling hash values.</p><p>In this paper, we propose Odess, a fast and lightweight resemblance detection approach, that greatly reduces the computational overhead for resemblance detection while achieving high detection accuracy and high compression ratio. Specifically, Odess first utilizes a novel Subwindow-based Parallel Rolling hash method (called SWPR) using SIMD (i.e., Single Instruction Multiple Data [1]) to accelerate calculation of rolling hashes (corresponding to the first part of the overhead). Moreover, Odess uses a novel Content-Defined Sampling method to generate a much smaller proxy hash set from the whole rolling hash set, and then quickly applies transforms on this small hash set for resemblance detection (corresponding to the second part of the overhead).</p><p>Evaluation results show that during the stage of resemblance detection, the Odess approach is ∼ 31.4 × and ∼ 7.9 × faster than the state-of-the-art N-Transform and Finesse (i.e., a recent variant of N-Transform [39]), respectively. When considering an end-to-end data reduction storage system, Odess-based system’s throughput is about 3.20 × and 1.41 × higher than N-Transform and Finesse-based systems’ throughput, respectively, while maintaining the high compression ratio of N-Transform and achieving ∼ 1.22 × higher compression ratio over Finesse.</p>", "Keywords": "post-deduplication delta compression; resemblance detection; SIMD; parallel rolling hash; content-defined sampling", "DOI": "10.1145/3584663", "PubYear": 2023, "Volume": "19", "Issue": "3", "JournalId": 19571, "JournalTitle": "ACM Transactions on Storage", "ISSN": "1553-3077", "EISSN": "1553-3093", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Harbin Institute of Technology, Shenzhen; Guangdong Provincial Key Laboratory of Novel Security Intelligence Technologies, China"}, {"AuthorId": 2, "Name": "Lifeng Pu", "Affiliation": "Harbin Institute of Technology, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Dell Technologies, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Shenzhen, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Shenzhen, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Shenzhen; Guangdong Provincial Key Laboratory of Novel Security Intelligence Technologies, China"}], "References": [{"Title": "SIMD programming using Intel vector extensions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "83", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 106368461, "Title": "A Direct Data Aware LSTM Neural Network Architecture for Complete Remaining Trace and Runtime Prediction", "Abstract": "Developing LSTM neural networks that can accurately predict the future trajectory of ongoing cases and their remaining runtime is an active area of research in predictive process monitoring. In this work a novel complete remaining trace prediction (CRTP) LSTM is proposed. This model is trained to directly predict the complete remaining trace and runtime of cases in contrast to single event prediction as is considered in previously published research on this topic. This makes the CRTP-LSTM robust in terms of utilizing all available attributes of previously observed events for prediction, consequently it can be considered natively data aware. In an extensive experimental assessment the authors show that CRTP-LSTMs consistently outperform other considered approaches for both remaining trace and runtime prediction. Furthermore, the authors show that including all available information contained in previously observed events has a positive impact on the performance of the CRTP-LSTM model. This indicates that valuable information can be extracted from attributes of events in order to make more accurate trace and runtime predictions. This opens up interesting avenues for future research including the incorporation of inter-case features into a modeling setup when predicting the remaining trace and runtime of cases.", "Keywords": "Process mining;predictive process monitoring;remaining time prediction;remaining trace prediction;long short-term memory networks", "DOI": "10.1109/TSC.2023.3245726", "PubYear": 2023, "Volume": "16", "Issue": "4", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Research Center for Information Systems Engineering (LIRIS), KU Leuven, Leuven, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center for Information Systems Engineering (LIRIS), KU Leuven, Leuven, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center for Information Systems Engineering (LIRIS), KU Leuven, Leuven, Belgium"}], "References": [{"Title": "A Multi-View Deep Learning Approach for Predictive Business Process Monitoring", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "2382", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 106368566, "Title": "Introduction to Special Section on FPT’20", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3579850", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 10055, "JournalTitle": "ACM Transactions on Reconfigurable Technology and Systems", "ISSN": "1936-7406", "EISSN": "1936-7414", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Auckland, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Wisconsin-Madison, USA"}], "References": []}, {"ArticleId": 106368604, "Title": "A federated learning and blockchain framework for physiological signal classification based on continual learning", "Abstract": "A key challenge of physiological signal processing in the Internet of Medical Things is that physiological signals usually have dynamic distribution changes. Another challenge is that patients' data is vulnerable to disclosures. To address these problems, we propose a meta continual learning method, called MetaCL. MetaCL consists of a shared feature extractor, a knowledge base, a micro classifier module and a blockchain module. The shared feature extractor adopts a horizontal federated learning mechanism to prevent data leakage. The knowledge base is built and updated based on a Splite-based method to overcome catastrophic forgetting. The micro classifier module uses a mean-based model transfer method to adapt to the emergence of new classes. It integrates a Kullback-Leibler divergence regularization to the loss function to deal with fuzzy boundaries of classes. The blockchain module is designed based on the Alliance chain to protect the privacy of the classification results. Experiment results show that refinement classification performance of MetaCL is 98.35%, which outperforms the compared state-of-the-art works. The backward transfer under four increment scenarios is all within -2.6%. At last, a blockchain-based engineering application is presented to show that MetaCL can prevent privacy protection in the Internet of Medical Things (IoMT).", "Keywords": "", "DOI": "10.1016/j.ins.2023.02.003", "PubYear": 2023, "Volume": "630", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "Le Sun", "Affiliation": "Engineering Research Center of Digital Forensics, Ministry of Education, Nanjing University of Information Science and Technology, NanJing, 210044, China;Department of Jiangsu Collaborative Innovation Center of Atmospheric Environment and Equipment Technology (CICAEET), Nanjing University of Information Science and Technology, NanJing, 210044, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Engineering Research Center of Digital Forensics, Ministry of Education, Nanjing University of Information Science and Technology, NanJing, 210044, China;Department of Jiangsu Collaborative Innovation Center of Atmospheric Environment and Equipment Technology (CICAEET), Nanjing University of Information Science and Technology, NanJing, 210044, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Engineering Research Center of Digital Forensics, Ministry of Education, Nanjing University of Information Science and Technology, NanJing, 210044, China;Department of Jiangsu Collaborative Innovation Center of Atmospheric Environment and Equipment Technology (CICAEET), Nanjing University of Information Science and Technology, NanJing, 210044, China;Corresponding author at: Engineering Research Center of Digital Forensics, Ministry of Education, Nanjing University of Information Science and Technology, NanJing, 210044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of New Networks, Peng Cheng Laboratory, Shenzhen, 518066, China;Cyberspace Institute of Advanced Technology, Guangzhou University, Guangzhou, 510006, China"}], "References": [{"Title": "A physiological and behavioral feature authentication scheme for medical cloud based on fuzzy-rough core vector machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "143", "JournalTitle": "Information Sciences"}, {"Title": "ECG beat classification using neural classifier based on deep autoencoder and decomposition techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "3", "Page": "333", "JournalTitle": "Progress in Artificial Intelligence"}, {"Title": "The Application of Deep Learning Algorithms for PPG Signal Processing and Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Vale<PERSON>assilenko", "PubYear": 2021, "Volume": "10", "Issue": "12", "Page": "158", "JournalTitle": "Computers"}, {"Title": "Knowledge extraction and retention based continual learning by using convolutional autoencoder-based learning classifier system", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "591", "Issue": "", "Page": "287", "JournalTitle": "Information Sciences"}, {"Title": "Autonomous CNN (AutoCNN): A data-driven approach to network architecture determination", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "638", "JournalTitle": "Information Sciences"}, {"Title": "A quantum blockchain-enabled framework for secure private electronic medical records in Internet of Medical Things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "612", "Issue": "", "Page": "942", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 106368692, "Title": "A novel temporal moment retrieval model for apron surveillance video", "Abstract": "This study addresses the problem of retrieving a specific moment from an untrimmed apron surveillance video through a query sentence. Existing methods cannot tackle it effectively by using sliding windows over the entire video, which suffers from exhaustively enumerated candidates. Here temporal and spatial characters of events are abstracted to corresponding heuristic rules in first-order predicate logic. Temporal characters are exploited to skip unimportant frames while potential time boundaries are deduced from spatial characters. Meanwhile, an object-detection model is designed to obtain spatial characters of potential time boundaries, which is compared with pre-defined heuristic rules for detection. If the obtained spatial characters do not match the heuristic rules, a multiple-time-scales searching module is devised to localize the boundaries. This method is evaluated on TACoS and Apron Surveillance Video (ASV) dataset. Results show that it outperforms the state-of-the-art methods on TACoS and accurately retrieves target events on ASV in one minute.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2023.108616", "PubYear": 2023, "Volume": "107", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Lyu", "Affiliation": "Key Laboratory of Smart Airport Theory and System, Civil Aviation Administration of China, Civil Aviation University of China, Tianjin, China;College of Computer Science and Technology, Civil Aviation University of China, Tianjin, China;Corresponding author at: College of Computer Science and Technology, Civil Aviation University of China, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Smart Airport Theory and System, Civil Aviation Administration of China, Civil Aviation University of China, Tianjin, China;College of Computer Science and Technology, Civil Aviation University of China, Tianjin, China"}], "References": []}, {"ArticleId": 106368695, "Title": "A review of biological targets and therapeutic approaches in the management of triple-negative breast cancer", "Abstract": "<b  >Background</b> Triple-negative breast cancer (TNBC) is a heterogeneous, aggressive phenotype of breast cancer with associated chemoresistance. The development of chemo- or radioresistance could be attributed to diverse tumor microenvironments, overexpression of membrane proteins (transporters), epigenetic changes, and alteration of the cell signaling pathways/genes associated with the development of cancer stem cells (CSCs). <b  >Aim of review</b> Due to the diverse and heterogeneous nature of TNBC, therapeutic response to the existing modalities offers limited scope and thus results in reccurance after therapy. To establish landmark therapeutic efficacy, a number of novel therapeutic modalities have been proposed. In addition, reversal of the resistance that developed during treatment may be altered by employing appropriate therapeutic modalities. This review aims to discuss the plethora of investigations carried out, which will help readers understand and make an appropriate choice of therapy directed toward complete elimination of TNBC. <b  >Key scientific concepts of review</b> This manuscript addresses the major contributory factors from the tumor microenvironment that are responsible for the development of chemoresistance and poor prognosis. The associated cellular events and molecular mechanism-based therapeutic interventions have been explained in detail. Inhibition of ABC transporters, cell signaling pathways associated with CSCs, and epigenetic modification offers promising results in this regard. TNBC progression, invasion, metastasis and recurrence can also be inhibited by blocking multiple cell signaling pathways, targeting specific receptors/epigenetic targets, disrupting bioenergetics and generating reactive oxygen species (ROS).", "Keywords": "Cancer stem cells;Chemoresistance;Epigenetics;Therapeutic modality;Triple-negative breast cancer (TNBC);Tumor microenvironment", "DOI": "10.1016/j.jare.2023.02.005", "PubYear": 2023, "Volume": "54", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmaceutics, JSS College of Pharmacy, JSS Academy of Higher Education & Research, Mysuru-570015, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmaceutics, JSS College of Pharmacy, JSS Academy of Higher Education & Research, Mysuru-570015, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmaceutical Chemistry, JSS College of Pharmacy, JSS Academy of Higher Education & Research, Mysuru-570015, India."}, {"AuthorId": 4, "Name": "SubbaRao V<PERSON>", "Affiliation": "Department of Biochemistry, Centre of Excellence in Molecular Biology & Regenerative Medicine, JSS Medical College, JSS Academy of Higher Education & Research, Mysuru-570015, India."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacology, JSS College of Pharmacy, JSS Academy of Higher Education & Research, Mysuru-570015, India."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Roseman University of Health Sciences, College of Pharmacy, South Jordan, Utah, USA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Roseman University of Health Sciences, College of Pharmacy, Henderson, Nevada, USA."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmaceutics, JSS College of Pharmacy, JSS Academy of Higher Education & Research, Mysuru-570015, India. Electronic address:  ."}], "References": []}, {"ArticleId": 106368713, "Title": "Inventory control model for intermittent demand: a comparison of metaheuristics", "Abstract": "<p>In this era of trying to get more information from data, demand forecasting plays a very important role for companies. Companies implement many of their plans based on these forecasts. However, forecasting demand is not always easy. Demand is divided into many different classes depending on its structure. One of them is the problem of intermittent demand. Estimating intermittent demand is a more difficult problem than estimating series with low variability and no null demand. When working with intermittent demands, either a good demand forecast or a good inventory policy is required. Determining an efficient inventory policy is important in terms of meeting customer demand and customer satisfaction. In this study, an inventory lower bound and upper bound are calculated to balance the inventory cost of intermittent demand and the lost sale cost. For this purpose, 7 different test data with intermittent demand structure were studied. A mathematical model is proposed to calculate the cost with the upper and lower inventories under intermittent demand. A feasible solution could not be obtained with the proposed model, and a fitness function for the relevant model was proposed. This function was run on test data using genetic algorithm (GA) and particle swarm optimization (PSO). The results and solution times of GA and PSO were compared. In this way, the variability of demand and the difference between arrival times of demands were met with minimal cost.</p>", "Keywords": "Intermittent demand; Inventory control; Metaheuristics; Genetic algorithm (GA); Particle swarm optimization (PSO)", "DOI": "10.1007/s00500-023-07871-0", "PubYear": 2023, "Volume": "27", "Issue": "10", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Atatürk University, Erzurum, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Atatürk University, Erzurum, Turkey"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Atatürk University, Erzurum, Turkey"}], "References": []}, {"ArticleId": 106368784, "Title": "Maintainability enhancement based on uncertain model transformations", "Abstract": "Context: Managing uncertainty while expressing model transformations is problematic. Indeed, we are constrained to express various transformation specifications implementing the different possibilities. These possibilities are driven by the need to realize the relevance of each scenario to choose the best one (uncertainty on a transformation scenario) or result from the need to propose other alternatives to a given scenario if it is not feasible (uncertainty on the feasibility of a transformation scenario). In both cases, we face maintainability issues related to handling separated and frequently changed transformation specifications. Objective: This paper gives a global overview of our approach to deal with uncertainty in model transformations while focusing on maintainability aspect. Methods: We have proposed a new approach for dealing with uncertainty in model transformations. Basically, our approach makes use of partiality to allow expressing a transformation specification that covers different possibilities. The current paper focuses on the impact of our proposal to enhance changeability. This has been demonstrated by carrying out comparative experiments involving three other transformation techniques while considering the effort required to implement a change. Results: Our experiments show that our approach has proven useful and effective for implementing changes, mainly for complex ones. Conclusion: This paper provides an overview of our approach for managing uncertainty within model transformations. Mainly, it focuses on the impact of our proposal to enhance changeability. The experiment results reveal that our proposal allows expressing highly changeable specifications. Introduction Model Driven Engineering (MDE) is a research domain promoting an active use of models throughout the software development process, leading to an automatic generation of the final solution. Basically, models represent all artifacts handled by a software development process and can be used as first-class entities in dedicated model management operations (e.g. model transformation, model composition, model validation …). Among these operations, model transformation is a pillar of MDE. It underpins the automatic generation of target models from source ones. The managed models conform to metamodels that define the concepts, properties and relations needed to express them. Besides, a transformation specification includes descriptions of how constructs of source metamodels can be transformed into constructs of target metamodels [1]. Adopting for MDE supposed that the managed models as well as model transformations have to be precisely expressed with respect to the structuring defined by the implied metamodels, target constraints and transformation scenarios. However, sometimes transformation developers are facing vague/incomplete requirements. Managing uncertainty while expressing model transformations is problematic. Indeed, they are constrained to express various transformation specifications implementing the different possibilities. These possibilities are driven by the need to realize the relevance of each scenario to choose the adequate one(s) (uncertainty on a transformation scenario) or result from the need to propose other alternatives to a given scenario if it is not feasible (uncertainty on the feasibility of a transformation scenario). In both cases, they face maintainability issues related to handling separated and frequently changed transformation specifications. To deal with the first limitation, we suggested in [2] the use of partiality [3] when expressing model transformations. In fact, the principle of partial models introduced in [4] is projected to the pattern managed by a transformation specification (mainly in rule-based languages) [5], [6], [7], [8]. Therefore, all the possible transformation scenarios (resulting from varying the possible interpretations of a vague domain rule) are expressed by means of a partial input pattern (i.e. a set of possible concrete patterns). In addition, the production rules, that allow producing target models for the source fragment matched using the input pattern, are expressed with a view to being applicable for all the concrete patterns related to the partial one. A support for handling model transformations with uncertainty is valuable for the developer as it makes it possible to anticipate the specification of the transformation in the absence of relevant decisions. It would be the case when the expression of the transformation purpose and the relevant details are incomplete and will be expanded or when the expression of the transformation scenario(s) is too vague and has to be clarified. An uncertain model transformation specification expresses various possibilities of transformation scenarios to explore them with a view of reducing uncertainty. This paper focuses on maintainability issues. For convenience, we summarize the specific contributions of this paper in three points: • Better illustration of our approach to deal with uncertainty in model transformations [2] by applying it to other transformation scenarios. • Discussion on how our approach for managing uncertainty within model transformations allows expressing highly maintainable specifications. • Results of an empirical study that aims to assess change effort by using two use cases and comparing the proposed approach to three transformation techniques (Java/EMF [9], ATL [5] and Henshin [8]). The remainder of this paper is structured as follows. In Section 2, we give a global overview of our approach for handling uncertainty when expressing model transformations. Section 3 gives a detailed view of the proposed approach, while Section 4 illustrates it through an application case. In Section 5, the evaluation results of maintainability are presented. Section 6 discusses threats to validity. Finally, related work is assessed in Section 7 and Section 8 concludes this paper. Section snippets Conceptual overview This section presents a running example that motivates the need for an approach to deal with uncertainty in model transformations. Besides, it details the basic principles of the proposed approach. Approach to manage uncertainty in model transformations The relevant information related to the pattern specification and production rules is considered as a configuration model. Hence, the following describes first the metamodel that defines all elements required to express a configuration model (considered as a high level specification of an uncertain model transformation) and then details how the transformation infrastructure (viewed as the concrete implementation of the transformation) can be derived from it. Application This section further illustrates the concepts previously presented using a transformation scenario extracted from the Escape it! game. We will first give a global overview of the Escape it! game and the selected transformation scenario. After that, we will present the corresponding configuration model and the derived transformation infrastructure. Evaluation After presenting details on the proposed transformation infrastructure, we introduce in this section the results obtained from an experimental evaluation of our proposal. The main objective is to assess one of the main maintainability sub-characteristics: changeability. Changeability is of major importance and it is considered as a crucial property in model transformation approaches [14]. It is defined in [15] as: “the capability of the transformation approach to enable a specified modification Threats to validity Our finding that the proposed approach has proven useful and effective for implementing changes is threatened by reliability (whether the analysis is dependent on the individual researchers involved). In fact, the transformation scenario of the first use case remains simple. As for the second use case, the different transformation specifications corresponding to the aforementioned changes were implemented by us. Besides, even if the presented changes were proposed by external ASD experts Related work In order to deal with maintainability issues when implementing complex model transformations, several research works propose to split the transformation specification into separate ones with unique intention [17] and within a manageable size. Hence, composition techniques exploit the reduced specifications in order to perform the global transformation purpose. The basic composition approach consists in building a transformation chain that orchestrates the execution of reduced specifications [18] Conclusion In this paper, we provide an overview of our approach for managing uncertainty within model transformations. Basically, the expression of an uncertain model transformation is based on the notion of partial pattern. This allows covering various possibilities of transformation scenarios with a compacted specification. We also describe the tool support implemented for our proposal. Mainly, this paper focuses on the impact of our proposal to enhance changeability. The use of partiality and the CRediT authorship contribution statement Youness Laghouaouta: Conception and design of study, Acquisition of data, Analysis and/or interpretation of data, Writing – original draft, Writing – review & editing. Pierre Laforcade: Conception and design of study, Acquisition of data, Analysis and/or interpretation of data, Writing – original draft, Writing – review & editing. Declaration of Competing Interest No author associated with this paper has disclosed any potential or pertinent conflicts which may be perceived to have impending conflict with this work. For full disclosure statements refer to https://doi.org/10.1016/j.infsof.2023.107177 . Acknowledgment All authors approved version of the manuscript to be published. References (30) de Lara J. et al. Parallel graph transformation for model simulation applied to timed transition petri nets Electron. Notes Theor. Comput. Sci. (2004) Kolahdouz-Rahimi S. et al. Evaluation of model transformation approaches for model refactoring Sci. Comput. Program. (2014) Mens T. et al. A taxonomy of model transformation Electron. Notes Theor. Comput. Sci. (2006) Laghouaouta Y. et al. Dealing with uncertainty in model transformations Salay R. et al. Language independent refinement using partial modeling Famelis M. et al. Partial models: Towards modeling and reasoning with uncertainty Jouault F. et al. Transforming models with ATL Kolovos D.S. et al. The epsilon transformation language Taentzer G. AGG: A graph transformation environment for modeling and validation of software Arendt T. et al. Henshin: advanced concepts and tools for in-place EMF model transformations Budinsky F. et al. Eclipse Modeling Framework: a Developer’s Guide (2004) Kolovos D.S. et al. The epsilon object language (EOL) Kolovos D. et al. The Epsilon Book (2017) (2017) Kolovos D.S. et al. The epsilon pattern language Laforcade P. et al. Supporting the adaptive generation of learning game scenarios with a model-driven engineering framework View more references Cited by (0) Recommended articles (0) View full text © 2023 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.infsof.2023.107177", "PubYear": 2023, "Volume": "158", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Posts and Telecommunications INPT, Rabat, Morocco;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Laboratory of Le Mans University LIUM, Le Mans, France"}], "References": []}, {"ArticleId": 106368802, "Title": "Learners' Acceptability of Adapting the Different Teaching Methodologies for Students", "Abstract": "<p>The learning methodologies used by students are directly proportional to their abilities to learn. Various learning methodologies have been used to gain student acceptance and satisfaction with the module taught by the teacher. In this article, the authors approach the different methods and analyze these methodologies. To determine the impact, they considered both the face-to-face learning process and the online mode of learning to determine the exact effect on the student. So, to address this, a two-way survey was conducted. The first revealed the student satisfaction rate with the course approached through the online mode of learning. Second, a comparative study was made using ANOVA methods between the online way and the face-to-face methodology. A significant observation was made in the test, and it shows that the hybrid model of teaching provides better performance than the face-to-face method.</p>", "Keywords": "", "DOI": "10.4018/IJeC.318335", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 12628, "JournalTitle": "International Journal of e-Collaboration", "ISSN": "1548-3673", "EISSN": "1548-3681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Comprehensive Methods of Evaluation of Distance Learning System Functioning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "3", "Page": "62", "JournalTitle": "International Journal of Computer Network and Information Security"}]}, {"ArticleId": 106368808, "Title": "On robust approximate feedback linearisation with an event-triggered controller", "Abstract": "In this paper, we consider a problem of global stabilisation of approximate feedback linearised systems with perturbed nonlinearity by an event-triggered control. We categorise the perturbed nonlinearity into three cases and obtain a characterisation function for each case. Then we propose an event-triggered controller with a gain-scaling factor along with an event-triggering condition which is developed based on the characterisation function. Utilising the system analysis results, we suggest a control parameter selection rule depending on the priority on either system output regulation speed or size of the interexecution times. Via practical examples, we demonstrate the validity of our control approach.", "Keywords": "Approximate feedback linearisation ; perturbed nonlinearity ; event-triggered control ; gain-scaling factor ; global stabilisation", "DOI": "10.1080/00207721.2023.2177901", "PubYear": 2023, "Volume": "54", "Issue": "7", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "Ji-Sun Park", "Affiliation": "Department of Electrical Engineering, Dong-A University, Busan, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Fire Safety Management, Kyungnam College of Information and Technology, Busan, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Dong-A University, Busan, Korea"}], "References": []}, {"ArticleId": 106368828, "Title": "Video based basketball shooting prediction and pose suggestion system", "Abstract": "<p>Video based motion analysis, which aims to acquire the whole posture data by simple camera and without placing sensors on the body parts, has become the major analysis method in the sport domain. However, most video based motion analysis approaches either work only for some specific domain action recognition, or suffer from low prediction rates for practical applications in the sport domain. This paper presents an effective system to predict basketball shooting and to suggest corrected postures, as based on video based motion analysis with the OpenPose system. Given a basketball shooting video sequences, the proposed system first detects the human joint points acquired from the OpenPose system, and then, the video frames of the shooting period are detected by two important features of the shooting process. Basketball shooting is predicted using the adopted trajectory curves matching method and the K-nearest neighbor classification method. Finally, the wrong shooting posture is corrected and suggested based on the pix2pix conditional GAN (cGAN) model. Experimental results show that our approach can effectively estimate shooting results with high accuracy.</p>", "Keywords": "OpenPose; <PERSON><PERSON><PERSON> curve; KNN; The pix2pix conditional GAN (cGAN) model", "DOI": "10.1007/s11042-023-14490-2", "PubYear": 2023, "Volume": "82", "Issue": "18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, New Taipei City, Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, New Taipei City, Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, New Taipei City, Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Office of Physical Education, Tamkang University, New Taipei City, Republic of China; National Taiwan Normal University, Taipei City, Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Office of Physical Education, Tamkang University, New Taipei City, Republic of China"}], "References": [{"Title": "DRCDN: learning deep residual convolutional dehazing networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "9", "Page": "1797", "JournalTitle": "The Visual Computer"}, {"Title": "A correlative denoising autoencoder to model social influence for top-N recommender system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "3", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Learning social representations with deep autoencoder for recommender system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "2259", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 106368860, "Title": "Learning Equilibria in Matching Markets with Bandit Feedback", "Abstract": "<p>Large-scale, two-sided matching platforms must find market outcomes that align with user preferences while simultaneously learning these preferences from data. Classical notions of stability (<PERSON> and <PERSON>, 1962; <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, 1971) are unfortunately of limited value in the learning setting, given that preferences are inherently uncertain and destabilizing while they are being learned. To bridge this gap, we develop a framework and algorithms for learning stable market outcomes under uncertainty. Our primary setting is matching with transferable utilities, where the platform both matches agents and sets monetary transfers between them. We design an incentive-aware learning objective that captures the distance of a market outcome from equilibrium. Using this objective, we analyze the complexity of learning as a function of preference structure, casting learning as a stochastic multi-armed bandit problem. Algorithmically, we show that “optimism in the face of uncertainty,” the principle underlying many bandit algorithms, applies to a primal-dual formulation of matching with transfers and leads to near-optimal regret bounds. Our work takes a first step toward elucidating when and how stable matchings arise in large, data-driven marketplaces.</p>", "Keywords": "multi-armed bandits; stable matchings; learning equilibria; preference structure", "DOI": "10.1145/3583681", "PubYear": 2023, "Volume": "70", "Issue": "3", "JournalId": 7775, "JournalTitle": "Journal of the ACM", "ISSN": "0004-5411", "EISSN": "1557-735X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UC Berkeley EECS, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "UC Berkeley EECS, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "UC Berkeley EECS, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UC Berkeley EECS and Statistics, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "UC Berkeley Statistics, USA"}], "References": []}, {"ArticleId": 106368878, "Title": "Performance Investigation of Generalized Rain Pattern Absorption Attention Network for Single-Image Deraining", "Abstract": "<p>Rainy weather conditions are challenging issues for many computer vision applications. Rain streaks and rain patterns are two crucial environmental factors that degrade the visual appearance of high-definition images. A deep attention network-based single-image deraining algorithm is more famous for handling the image with the statistical rain pattern. However, the existing deraining network suffers from the false detection of rain patterns under heavy rain conditions and ineffective detection of directional rain streaks. In this paper, we have addressed the above issues with the following contributions. We propose a multilevel shearlet transform-based image decomposition approach to identify the rain pattern on different scales. The rain streaks in various dimensions are enhanced using a residual recurrent rain feature enhancement module. We adopt the Rain Pattern Absorption Attention Network (RaPaat-Net) to capture and eliminate the rain pattern through the four-dilation factor network. Experiments on synthetic and real-time images demonstrate that the proposed single-image attention network performs better than existing deraining approaches.</p>", "Keywords": "Single image deraining; deep convolutional neural network; rain streaks removal; rain absorption attention framework; shearlet multi-scale decomposition", "DOI": "10.1142/S0218126623502316", "PubYear": 2023, "Volume": "32", "Issue": "13", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Electronics, Velalar College of Engineering and Technology, Thindal, Erode, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering, Sona College of Technology, Salem, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, PSG Institute of Technology and Applied Research, Coimbatore, Tamil Nadu, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, R.M.D. Engineering College, Chennai, Tamil Nadu, India"}], "References": [{"Title": "Automatic diagnosis of skin diseases using convolution neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "", "Page": "103074", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Performance analysis of a dual stage deep rain streak removal convolution neural network module with a modified deep residual dense network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "1", "Page": "111", "JournalTitle": "International Journal of Applied Mathematics and Computer Science"}, {"Title": "Performance analysis of a dual stage deep rain streak removal convolution neural network module with a modified deep residual dense network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "1", "Page": "111", "JournalTitle": "International Journal of Applied Mathematics and Computer Science"}]}, {"ArticleId": 106368910, "Title": "A cooperative whale optimization algorithm for energy-efficient scheduling of the distributed blocking flow-shop with sequence-dependent setup time", "Abstract": "The distributed blocking flow-shop sequence-dependent scheduling problem (DBFSDSP) with the production efficiency measures has been extensively concerned as its wide industrial applications. Whereas, energy efficiency indicators are often ignored in the literature. The DBFSDSP considered makespan, total tardiness, and total energy consumption is investigated in this paper. A cooperative whale optimization algorithm (CWOA) is proposed to solve the DBFSDSP as the complexity of the distributed and multi-objective optimization. First, the critical path of DBFSDSP is defined. An energy-saving operation for the non-critical path is designed to reduce the energy consumption of the system. Second, an accelerated operation for the critical path is designed to reduce the makespan and total tardiness. Third, three acceptance criteria for multi-objective optimization are proposed to improve the diversity of the population. The statistical and computational experimentation in an extensive benchmark testified that the CWOA outperforms the state-of-the-art algorithms regarding efficiency and significance in solving DBFSDSP. Introduction Under the pressure of climate change and global warming, manufacturing enterprises have to seek an effective energy-efficient way to reduce energy consumption without extra equipment investment costs. Increasingly, quite a few researchers are conscious that the scheduling could balance these two seemingly conflicting problems and provide an effective scheme for the energy-efficient manufacturing process (<PERSON> & <PERSON>, 2022; <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, 2021). However, the process often becomes an NP-hard problem that is very difficult to solve since the energy criterion should be considered together with the economic criterion (<PERSON>, <PERSON>, & <PERSON>, 2021; <PERSON>, <PERSON>, & <PERSON>, 2017). Therefore, it has important realistic meaning and theoretical value that energy-efficient scheduling is researched to design effective strategies and optimization algorithms (<PERSON>yata & Nagano, 2022; F. <PERSON>, R. Ma, et al., 2021). The flow-shop scheduling problem (FSP) (Araujo, Arroyo, & Fialho, 2020; Lageweg, Lenstra, & Rinnooy Kan, 1978; Liu, Wang, & Jin, 2007) is divided into the permutation flow-shop problem (B.-B. Li, Wang, & Liu, 2008), the no-idle flow-shop problem (Rossi & Nagano, 2021; Zhao et al., 2020), the blocking flow-shop problem (Han, Gong, Jin, & Pan, 2019), the no-wait flow-shop problem (Tonizza Pereira & Seido Nagano, 2022; Zhao et al., 2019), etc. The blocking flow-shop scheduling problem (BFSP) (Han et al., 2019) widely exists in the manufacturing and production systems, such as the non-ferrous metallurgical industry (Q. Chen, Pan, Zhang, Ding, & Li, 2019), chemical industry (Fu, Wang, Wang, & Pu, 2021), and serial manufacturing processes (Jing, Pan, & Gao, 2021). The BFSP can be described as having n job processed on m machines following the same processing route. The assumption is as follows: • the processing sequence of the jobs on all machines is the same; • the machine cannot process multiple jobs at the same time, and the job cannot be processed by multiple machines at the same time; • all jobs can be processed at zero time, and the preparation time of jobs is included in the processing time or may be ignored; • there is no buffer between adjacent machines. The problem is how to arrange the production order of each job to optimize the optimization objective. In recent years, the distributed BFSP (DBFSP) is considerably popular in effectively reducing costs and risks via the cooperation of factories, which shortens the production time and improves production efficiency (F. Zhao, X. He et al., 2021). Based on BFSP's assumption, it adds the hypothesis that once the job is assigned to a specific factory, all processing operations of the job can only be carried out in the factory. Compared with BFSP which scheduled the jobs in a single factory, the excess difficulty of DBFSP is to assign the jobs in certain isomorphic factories. Existing experiments and literature have shown that the DBFSP with more than two machines is a typical NP-hard problem (Zhao, Zhao, Wang, & Song, 2020). With the increasingly serious environmental problems, more and more researchers begin to pay attention to energy efficiency indicators. Chen et al. presented a collaborative optimization algorithm (COA) to address energy-efficient multi-objective distributed no-idle flow-shop scheduling with the objectives of minimizing both makespan and total energy consumption (TEC) (Chen, Wang and Peng, 2019). A knowledge-based cooperative algorithm (KCA) is introduced to address the energy-efficient scheduling of distributed flow-shop (Wang & Wang, 2020). Zeng et al. proposed an improved NSGA-II (INSGA-II) to solve the energy-efficient distributed no-wait flow-shop with sequence-dependent setup time(Zeng et al., 2022). It can be seen that the energy-saving multi-objective scheduling problem is a hot research topic, which has very important significance. Blocking constraints exist in nonferrous metallurgy scheduling, such as in the aluminum industry. However, this kind of industrial process is an industrial process with high pollution and high energy consumption, which causes serious environmental problems and waste of resources. Therefore, it is extremely important to study the blocking scheduling problem with the energy-efficient. A reasonable scheduling process can reduce the time spent in processing, reduce the waste of resources and improve the environmental pollution problem, which has very important practical significance. In the actual production process, certain unproductive operations are often added between two continuous operations, such as machine cleaning, tool replacement, and job transportation (Huang, Pan, & Gao, 2020). These unproductive operations are important conditions for the smooth processing of jobs. The duration of these operations is closely related to the current job being processed and the last job that has been processed. These durations are called sequence-dependent setup times (SDSTs) since different processing sequences produce different durations (Shao, Pi, & Shao, 2018). The setup times are included in processing time in the majority of the existing distributed shop scheduling problems. However, substantial setup times are required between the processing of two jobs in various industrial production systems. The effective processing of setup operation is critical to improving the efficiency of the manufacturing system. Hence, the setup times in the shop schedule are considered independent of processing times in this paper. The SDSTs are contained in 50% of the manufacturing plants. Further, 92% of the order deadlines are affected by these SDSTs (Huang et al., 2020). Hence, it is of great significance to investigate this kind of scheduling problem for actual manufacturing. In this paper, the distributed blocking flow-shop sequence-dependent scheduling problem (DBFSDSP) with the production efficiency measures is considered to minimize the maximum completion times, total tardiness, and total energy consumption. The DBFSP has been proven to be an NP-hard problem (Zhao, Zhao, et al., 2020). As the DBFSDSP is an extension of the DBFSP, the DBFSDSP is an NP-hard problem as well apparently. The DBFSDSP is constrained by SDSTs. The setup times are different between different jobs on different machines, and different sequences of jobs will yield different setup times. The machine will also generate energy consumption during the setup times. This means that setup times affect not only makespan and TTD but also TEC. In DBFSDSP, it is necessary to consider not only the distribution of the jobs to the factories and the processing sequence of the jobs in the factories but also the processing speed of the jobs on the machine. Increasing the processing speed of the machine may reduce makespan and TTD but will increase the TEC. Conversely, decreasing the processing speed may increase makespan and TTD but will reduce TEC. Therefore, it is very important to arrange the processing sequence and machine speed reasonably. The research on DBFSDSP is very challenging because of its large solving space and high difficulty. There are few studies on DBFSDSP with three objectives in the existing papers. Traditional mathematical methods, for instance, integer programming, linear programming, and dynamic programming are efficient methods to solve certain small-scale scheduling problems (Zhang, Mei, Nguyen, & Zhang, 2022). However, the methods have inferior performance to address large-scale distributed scheduling problems with multiple machines, multiple jobs, the SDSTs, and multiple optimization objectives. In recent years, heuristic and metaheuristic approaches have been presented and utilized for addressing scheduling problems effectively (Chen, Yang, Li, Wang, & Cheng, 2021; Gao, Yang, Zhou, Pan, & Suganthan, 2019; Lei, Li, & Wang, 2019; J. Q. Li et al., 2020; Zhao, He, & Wang, 2021, Zhao, Ma, & Wang, 2022, Zhao, Zhang, Cao, & Tang, 2021). A series of decision-making processes need to be implemented in large systems. With rapid economic development, the era of relying only on experience to meet challenges is gone forever. From the perspective of management, especially in the field of operational research optimization, there is an urgent need for a series of efficient optimization algorithms (Duan, Deng, Gharaei, Wu, & Wang, 2018). On the one hand, the optimization algorithm is used to solve the combinatorial optimization problem in production management, improve the utilization of production resources, and reduce inventory and logistics costs; on the other hand, it provides effective decision support for management through the optimization algorithm (Sato, Fukuyama, Iizaka, & Matsui, 2019). Therefore, it is of great practical significance to study optimization algorithms to solve production scheduling problems in management. The whale optimization algorithm (WOA) is a promising swarm-based algorithm, which simulates the foraging behavior of humpback whales in oceans (Mirjalili & Lewis, 2016). The peculiar and collective hunting is patterned into three stages including searching for prey, encircling prey, and bubble-net attacking method (Zhao et al., 2022). The WOA belongs to the swarm intelligence algorithm. Both the swarm intelligence algorithm and evolutionary computation algorithm belong to the meta-heuristic algorithm (Zhao, Bao, et al., 2022). The intelligent algorithm is a heuristic algorithm that simulates the behavior of animal groups such as fish, birds, bees, and wolves in nature, and uses information exchange and cooperation among groups to achieve optimization through simple and limited interaction between individuals. Evolutionary computation is a kind of meta-heuristic algorithm inspired by Darwin's evolutionary theory. Evolutionary computing usually regards the solution of the optimization problem as an individual and simulates the biological evolution process to generate offspring through mutation and recombination among individuals and preserve excellent individuals. With the continuous iteration of the evolution process, the quality of the individual continues to improve, and finally converges to a feasible solution (Zhao et al., 2022). These two algorithms are inspired by natural phenomena, and they are also searching for the optimal solution based on population. The swarm intelligence algorithm is a kind of collective behavior of social insects or animals under few rules. The evolutionary algorithm is an adaptive heuristic search algorithm based on natural selection and genetic evolution (Zhao, Jiang, & Wang, 2022). In the swarm intelligence algorithm, the individual members of a group are identical. Over time, this identity will remain in the form of movement. However, in evolutionary algorithms, population members will die and be replaced by descendants (Pan et al., 2021). Different from other metaheuristics, a cooperative coevolutionary model among individuals with different characteristics is constructed in WOA to ensure that the evolution of individuals is in the most promising direction (Hassan, El Desouky, Elghamrawy, & Sarhan, 2019). In WOA, each of the leaders is regarded as a potential solution or local-optima region. The promising novelty of WOA lies in its switching mode between exploration and exploitation, which is based on the learning mechanism of leaders with different characteristics. In this regard, WOA is similar to the classic swarm-based algorithms, partial swarm optimization (PSO) (Luo, Qiao, Lin, Xu, & Preuss, 2022). In recent years, WOA has been re-developed and applied to certain production scheduling problems (hybrid flow-shop scheduling problems (Zhang et al., 2021), batch scheduling problems (Niu, Liu, & Yin, 2021), and permutation flow-shop scheduling problems (PFSPs) (Xin, Jiang, Li, Gong, & Chen, 2021) owing to its simple structure and outstanding performance. However, the WOA combined with the characteristics of blocking problems with the energy-efficient has not been studied. In this paper, the problem-specific operators in DBFSDSP are embedded in the parallel search framework of WOA based on swarm intelligence and the cooperative whale optimization algorithm (CWOA) is proposed. In the CWOA, a method of initializing populations based on the well-known NEH heuristic was proposed to generate high-quality and individually diverse populations. According to the problem characteristics of DBFSDSP, a neighborhood insertion operation for critical factories is designed to improve the quality of the initial solution in the stage of searching for prey. In the stage of encircling prey, an energy-saving strategy based on critical paths is designed to reduce energy consumption. In the stage of bubble-net attacking prey, the accelerated operation is presented to reduce makespan and TTD, and three acceptance criteria are designed to maintain the diversity of the population. The main contributions of this paper are summarized as follows: 1) According to the characteristics of DBFSDSP, the critical path of DBFSDSP is defined, and the method to determine the critical path is proposed, which saves the calculation and improves the efficiency of the algorithm. Three acceptance criteria for multi-objective optimization are proposed to improve the diversity of the population at the same time. 2) According to the knowledge characteristics of energy-efficient scheduling, an energy-saving operation for the non-critical path is designed to reduce the energy consumption of the system and an accelerated operation for the critical path is designed to reduce the makespan and total tardiness. An insertion operation for the critical factory is introduced to improve the quality of the solution. 3) Through the results of experimental analysis and statistical analysis, CWOA is an effective algorithm for addressing DBFSDSP with three objectives, which is more stable and effective than the state-of-the-art algorithms compared in this paper. The remainder of this paper is structured as follows. The considered DBFSDSP is described in Section 2. In Section 3, the proposed CWOA is elaborated. The experimental results are implemented in Section 4. Finally, the conclusions and outlook for future study are summarized in Section 5. Section snippets Notations The notations and meanings utilized in this paper are listed as follows. C <sub> max </sub> Makespan of the schedule. TTD Total tardiness of the schedule. TEC Total energy consumption of the schedule. n The number of jobs. m The number of machines. δ The number of factories. J The job sequence, J = { J <sub>1</sub>, J <sub>2</sub>, …, J <sub> n </sub>}. M The machine sequence, M = { M <sub>1</sub>, M <sub>2</sub>, …, M <sub> m </sub>}. F The factory sequence, F = { F <sub>1</sub>, F <sub>2</sub>, …, F <sub> δ </sub>}. s The number of processing speed levels. i The index of the job, i = 0, 1, 2, ⋯, n . 0 indicates a dummy job. j The index of The basic whale optimization algorithm The basic operations of the WOA include three phases, searching for prey, encircling prey, and the bubble-net attacking method (Mirjalili & Lewis, 2016). The three basic operations are repeated until a certain termination criterion is satisfied when the population of WOA is initialized. In the WOA, the population is a set of real value vectors x <sub> i </sub> = ( x <sub>1</sub>, …, x <sub> D </sub>), i = 1, …, NP , where D is the dimension of the objective function, and NP is the size of population. Searching for prey: the individual Experimental settings The well-known standard benchmark set of (Naderi & Ruiz, 2010) is utilized to evaluate the performance of the CWOA. The benchmark consists of 120 different problem instances. The instances are categorized into 12 subsets for different combinations of n (number of jobs) and m (number of machines). These combinations range from 20 jobs and 5 machines up to 500 jobs and 20 machines. Naderi and Ruiz extended the benchmark to 720 instances by adding a number of factories for distributed scheduling Conclusion and future work An energy-efficient scheduling problem (DBFSDSP) is investigated to improve the efficiency of production in this paper. An energy-saving operation for the non-critical path is designed to reduce the energy consumption of the system in the encircling prey of the CWOA. An accelerated operation for the critical path is designed to reduce the makespan and total tardiness. Three acceptance criteria for multi-objective optimization are proposed to improve the diversity of the population. The Credit authorship contribution statement Fuqing Zhao: Funding acquisition; Investigation; Supervision. Zesong Xu: Investigation; Software; Writing - original draft; Data curation. Haizhu Bao: Methodology; Resources. Tianpeng Xu: Project administration; Writing -review & editing. Ningning Zhu: Conceptualization; Formal analysis. Jonrinaldi: Visualization. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work was financially supported by the National Natural Science Foundation of China under grant 62063021 . It was also supported by the Key talent project of Gansu Province ( ZZ2021G50700016 ), the Key Research Programs of Science and Technology Commission Foundation of Gansu Province ( 21YF5WA086 ), Lanzhou Science Bureau project (2018-rc-98), Project of Zhejiang Natural Science Foundation ( LGJ19E050001 ), and Project of Gansu Natural Science Foundation ( 21JR7RA204 ), respectively. References (46) F. Zhao et al. A hybrid biogeography-based optimization with variable neighborhood search mechanism for no-wait flow shop scheduling problem Expert Systems with Applications (2019) Z. Shao et al. A novel discrete water wave optimization algorithm for blocking flow-shop scheduling problem with sequence-dependent setup times Swarm and Evolutionary Computation (2018) Z. Shao et al. Self-adaptive discrete invasive weed optimization for the blocking flow-shop scheduling problem to minimize total tardiness Computers & Industrial Engineering (2017) B. Naderi et al. The distributed permutation flowshop scheduling problem Computers & Operations Research (2010) S. Mirjalili et al. The whale optimization algorithm Advances in Engineering Software (2016) X. He et al. A greedy cooperative co-evolution ary algorithm with problem-specific knowledge for multi-objective flowshop group scheduling problems IEEE Transactions on Evolutionary Computation (2021) M.K. Hassan et al. A hybrid real-time remote monitoring framework with NB-WOA algorithm for patients with chronic diseases Future Generation Computer Systems (2019) M. Araujo et al. Tabu search and iterated greedy for a flow shop scheduling problem with worker assignment J.-F. Chen et al. A collaborative optimization algorithm for energy-efficient multi-objective distributed no-idle flow-shop scheduling Swarm and Evolutionary Computation (2019) Q. Chen et al. Effective hot rolling batch scheduling algorithms in compact strip production IEEE Transactions on Automation Science and Engineering (2019) R. Chen et al. An effective multi-population grey wolf optimizer based on reinforcement learning for flow shop scheduling problem with multi-machine collaboration Computers & Industrial Engineering (2021) J. Dong et al. Green scheduling of distributed two-stage reentrant hybrid flow shop considering distributed energy resources and energy storage system Computers & Industrial Engineering (2022) C. Duan et al. Selective maintenance scheduling under stochastic maintenance quality with multiple maintenance actions International Journal of Production Research (2018) Y. Fu et al. Multiobjective modeling and optimization for scheduling a stochastic hybrid flow shop with maximizing processing quality and minimizing total tardiness IEEE Systems Journal (2021) K. Gao et al. Flexible job-shop rescheduling for new job insertion by using discrete Jaya algorithm IEEE Trans Cybern (2019) S. García et al. A study on the use of non-parametric tests for analyzing the evolutionary algorithms’ behaviour: a case study on the CEC’2005 special session on real parameter optimization Journal of Heuristics (2008) Y. Han et al. Evolutionary multiobjective blocking lot-streaming flow shop scheduling with machine breakdowns IEEE Trans Cybern (2019) J.-P. Huang et al. An effective iterated greedy method for the distributed permutation flowshop scheduling problem with sequence-dependent setup times Swarm and Evolutionary Computation (2020) X.-L. Jing et al. Local search-based metaheuristics for the robust distributed permutation flowshop problem Applied Soft Computing (2021) B.J. Lageweg et al. A general bounding scheme for the permutation flow-shop problem Operations Research (1978) D. Lei et al. A two-phase meta-heuristic for multiobjective flexible job shop scheduling problem with total energy consumption threshold IEEE Trans Cybern (2019) B.-B. Li et al. An effective PSO-based hybrid algorithm for multiobjective permutation flow shop scheduling IEEE Transactions on Systems, Man, and Cybernetics - Part A: Systems and Humans (2008) J.Q. Li et al. Hybrid artificial bee colony algorithm for a parallel batching distributed flow-shop problem with deteriorating jobs IEEE Trans Cybern (2020) View more references Cited by (0) Recommended articles (6) Research article A no-tardiness job shop scheduling problem with overtime consideration and the solution approaches Computers & Industrial Engineering, Volume 178, 2023, Article 109115 Show abstract In make-to-order manufacturing environments, overtime work is one of the effective and commonly used resources for expanding production capacity to ensure orders on-time delivery. However, if overtime work is not used reasonably and optimally, not only it cannot expedite the order completion, but also may lead to an increase in manufacturing costs. In order to use overtime work optimally, this paper presents a no-tardiness job shop scheduling problem with overtime work consideration (NTJSSP-OW) to minimize the total earliness inventory and overtime work costs simultaneously. A mathematical model is formulated and a hybrid genetic algorithm with simulated annealing (GASA) is proposed to solve it. Nine algorithms are also selected for performance comparisons. Unlike the traditional job shop scheduling problem, when solving NTJSSP-OW by heuristics and meta-heuristics, no-tardiness constraint is more likely to lead to infeasible solutions. So a multi-stage decoding scheme with a reconstructing rule is developed to ensure feasible solution. In order to extend the search space, a dispatching rule-based population initialization procedure and a repairing mechanism are provided. Comprehensive experiments are conducted on 14 modified benchmark problems, and non-parametric statistical tests like the Friedman test and post-hoc Nemenyi test are performed for the experimental results. Further systematic analyses indicate that GASA has significantly faster convergence on 90% of all test instances, and its global search ability outperforms other competing algorithms for 12 out of 14 instances. Research article Sustainable implementation of the carbon-labeling policy with customer participation and government supervision Computers & Industrial Engineering, Volume 178, 2023, Article 109100 Show abstract China is vigorously pursuing carbon neutrality targets to combat global warming. As a powerful tool for encouraging individuals to adjust their consumption patterns and fostering the advancement of green consumption, the overall planning of carbon-labeling policy is accelerating accordingly. To better comprehend and strengthen the sustainable implementation of carbon-labeling policy, this study constructs a tripartite mainstay game to explore the interactive behavior of carbon-labeled enterprises and customers, with the substantial involvement of governmental regulators. First, the evolutionary stability strategy (ESS) is determined by solving replicated dynamic equations and stability analysis of equilibrium points. Then, the practicability and rationality of the evolutionary game model are assessed ESSs corresponding to various scenarios in the carbon-labeling scheme. Finally, the first Chinese television manufacturer to acquire carbon-labeled certification, TCL Group, is utilized as evidence to validate the theoretical findings and support the subsequent arguments: There are eight equilibrium points and three potential ESSs in the game model, and the selection of each ESS is primarily determined by the trade-off between costs and revenues for each stakeholder; the level of carbon-labeled enterprises' green R&D effort has a beneficial effect on the implementation of carbon-labeling policy, while customers' identification of carbon-labeled products will potentially affect operational orientation; and governments' stringent oversight is a vital assurance that both the carbon-labeled enterprises and customers adhere to green initiatives. The results thus not only elaborate on the efficient approach and insights for the sustainable implementation of carbon-labeling policy with the participation of multiple stakeholders, but also offer suggestions for enhancing incentives to improve regulatory regimes and market outcomes. Research article Bi-objective aggregate production planning for managing plan stability Computers & Industrial Engineering, Volume 178, 2023, Article 109105 Show abstract Uncertainties in demand causes recurrent changes and revisions in a production plan, which may create anxiety in organizations and a state of instability throughout the supply chain. This study proposes a bi-objective aggregate production planning model (BO-APP), where plan stability is incorporated as a second objective alongside the traditional cost objective. The proposed BO-APP model is compared here to a classical single objective aggregate production planning model (APP) as well as to an alternate model called flexible requirements profile-based aggregate production planning model (FRP-APP), which aims to control plan stability by introducing dynamically changing bounds over the planning horizon. Numerical results on five industry-based cases show that both the BO-APP and FRP-APP models consistently generate plans with significantly less variability compared to the traditional APP model, while resulting in comparable planning costs. In addition, the BO-APP shows a better cost and stability performance combination, while the FRP-APP seems to give more flexibility to the planners for controlling the stability level. Research article A review and classification on distributed permutation flowshop scheduling problems European Journal of Operational Research, 2023 Show abstract The Distributed Permutation Flowshop Scheduling (DPFS) problem is one of the fastest-growing topics in the scheduling literature, which in turn is among the most prolific fields in Operational Research (OR). Although the problem has been formally stated only twelve years ago, the number of papers on the topic is growing at a rapid pace, and the rising interest –both from academics and practitioners– on distributed manufacturing paradigms seems to indicate that this trend will continue to increase. Possibly as a side effect of this steady growth, the state-of-the-art on many decision problems within the field is far from being clear, with substantial overlaps in the solution procedures, lack of (fair) comparisons against existing methods, or the use of different denominations for the same problem, among other issues. In this paper, we carry out a review of the DPFS literature aimed at providing a classification and notation for DPFS problems under a common framework. Within this framework, contributions are exhaustively presented and discussed, together with the state-of-the-art of the problems and lines for future research. Research article Big data analytics in flexible supply chain networks Computers & Industrial Engineering, 2023, Article 109098 Show abstract Supply chain responsiveness and Big Data Analytics (BDA) have incited an ample amount of interest in academia and among practitioners. This work is concerned with improving responsiveness in supply chain networks by extending production capacity to cope with changes and variations in demand. BDA helps researchers make sense of the current challenges of data: high volume, high velocity, and high variety. In this work, we will look at sales data and at large warehouses, which envelop all the said three characteristics of Big Data (BD). This is quite important as demand market data is increasingly shared with supply chain managers. Here, a working architecture is introduced to handle the challenges of BD. The work uses a neural network to detect patterns within the demand. The work combines deep learning with nonlinear programming to enable flexibility at supply chain production facilities to respond to the forecasted demand. The parameters in the neural network are analyzed and studied for each different product type. We see significant prediction improvements when the parameters are better tuned. Further, the work introduces a BD architecture that automates the acquisition of the data, data mining, and the storage of input and output files. Overall, the work utilizes a gradient search method, a genetic algorithm, ARIMA, a deep learning algorithm, and a mixed-integer nonlinear program. Research article Proactive in-house part-feeding for mixed-model assembly systems with dynamics Computers & Industrial Engineering, Volume 178, 2023, Article 109101 Show abstract The rich data collected from smart shop floors necessitate proactive decision-making to relieve system nervousness in a dynamic environment. This proactivity takes real-time actions based on disruption-driven prediction but not on disruption-created negative effects. This paper focuses on investigating a proactive in-house part-feeding (PIP) approach by integrating real-time data collected from shop floors with the physical properties of a mixed-model assembly system. An event-driven proactive prediction for replenishments is first implemented after coupling such data as disruptions, job rescheduling, part re-releasing, and real-time lineside inventories with a widely used reorder-point-based replenishment policy. This prediction can provide sufficient prior information about replenishments and, in turn, trigger tow-train rerouting to simultaneously minimize the costs of distribution and lineside inventories. In terms of limited tow train capacity and the just-in-time part-feeding goal, this rerouting problem should address various constraints on multiple trips, time windows, and the real-time working status of tow trains. An adaptive large neighbourhood search (ALNS) is developed to obtain the best solution for rerouting by designing customized removal and insertion operators, as well as local search heuristics. A case study of a real-life car seat assembly system is presented to verify the efficiency of the PIP. Several rerouting benchmark instances are obtained from the case study to evaluate the performance of ALNS, and the computational results indicate that the proposed ALNS has a satisfactory performance in solving the rerouting instances compared with some of the formerly proposed and published methods. The results of the practical case study illustrate that the PIP may increase the lineside inventory cost slightly but can significantly reduce the distribution cost compared with prevailing reactive approaches View full text © 2023 Published by Elsevier Ltd. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109082", "PubYear": 2023, "Volume": "178", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Fu<PERSON> Zhao", "Affiliation": "School of Computer and Communication, Lanzhou University of Technology, Lanzhou 730050, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication, Lanzhou University of Technology, Lanzhou 730050, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication, Lanzhou University of Technology, Lanzhou 730050, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication, Lanzhou University of Technology, Lanzhou 730050, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication, Lanzhou University of Technology, Lanzhou 730050, China"}, {"AuthorId": 6, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Universitas Andalas, Padang 25163, Indonesia"}], "References": [{"Title": "An effective iterated greedy method for the distributed permutation flowshop scheduling problem with sequence-dependent setup times", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "100742", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An ensemble discrete differential evolution for the distributed blocking flowshop scheduling with minimizing makespan criterion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "113678", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A discrete whale swarm algorithm for hybrid flow-shop scheduling problem with limited buffers", "Authors": "Chun<PERSON> Zhang; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "102081", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A cooperative water wave optimization algorithm with reinforcement learning for the distributed assembly no-idle flowshop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "107082", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Local search-based metaheuristics for the robust distributed permutation flowshop problem", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "107247", "JournalTitle": "Applied Soft Computing"}, {"Title": "Local search for weighted sum coloring problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107290", "JournalTitle": "Applied Soft Computing"}, {"Title": "Heuristics and iterated greedy algorithms for the distributed mixed no-idle flowshop with sequence-dependent setup times", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107337", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Adaptive strategy in differential evolution via explicit exploitation and exploration controls", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107494", "JournalTitle": "Applied Soft Computing"}, {"Title": "An Effective Multi-population Grey Wolf Optimizer based on Reinforcement Learning for Flow Shop Scheduling Problem with Multi-machine Collaboration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "162", "Issue": "", "Page": "107738", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A multipopulation cooperative coevolutionary whale optimization algorithm with a two-stage orthogonal learning mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "246", "Issue": "", "Page": "108664", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Green scheduling of distributed two-stage reentrant hybrid flow shop considering distributed energy resources and energy storage system", "Authors": "Jun Dong; Chunming Ye", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "108146", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Hybrid metaheuristics for the integrated and detailed scheduling of production and delivery operations in no-wait flow shop systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "170", "Issue": "", "Page": "108255", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An iterated greedy algorithm for distributed blocking flow shop with setup times and maintenance operations to minimize makespan", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "171", "Issue": "", "Page": "108366", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An offline learning co-evolutionary algorithm with problem-specific knowledge", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101148", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Hybrid enhanced discrete fruit fly optimization algorithm for scheduling blocking flow-shop in distributed environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113147", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid discrete water wave optimization algorithm for the no-idle flowshop scheduling problem with total tardiness criterion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113166", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106368978, "Title": "Increasing awareness of climate change with immersive virtual reality", "Abstract": "<p>Previous research has shown that immersive virtual reality (VR) is a suitable tool for visualizing the consequences of climate change. The aim of the present study was to investigate whether visualization in VR has a stronger influence on climate change awareness and environmental attitudes compared to traditional media. Furthermore, it was examined how realistic a VR experience has to be in order to have an effect. The VR experience consisted of a model of the Aletsch glacier (Switzerland) melting over the course of 220 years. Explicit measurements (new environmental paradigm NEP, climate change scepticism, and nature relatedness) and an implicit measurement (implicit association test) were collected before and after the VR intervention and compared to three different non-VR control conditions (video, images with text, and plain text). In addition, the VR environment was varied in terms of degrees of realism and sophistication (3 conditions: abstract visualization, less sophisticated realistic visualization, more sophisticated realistic visualization). The six experimental conditions (3 VR conditions, three control conditions) were modeled as mixed effects, with VR versus control used as a fixed effect in a mixed effects modeling framework. Across all six conditions, environmental awareness (NEP) was higher after the participants (N = 142) had been confronted with the glacier melting, while no differences were found for nature relatedness and climate change scepticism before and after the interventions. There was no significant difference between VR and control conditions for any of the four measurements. Nevertheless, contrast analyses revealed that environmental awareness increased significantly only for the VR but not for the control conditions, suggesting that VR is more likely to lead to attitude change. Our results show that exposure to VR environments successfully increased environmental awareness independently of the design choices, suggesting that even abstract and less sophisticated VR environment designs may be sufficient to increase pro-environmental attitudes.</p>", "Keywords": "Virtual Reality; Environmental attitude; Changing attitude; Climate Change; realism; IAT; Immersion; presence", "DOI": "10.3389/frvir.2023.897034", "PubYear": 2023, "Volume": "4", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Switzerland; Faculty of Psychology, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Psychology, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Design, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Switzerland; Faculty of Psychology, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Switzerland"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Switzerland; Faculty of Psychology, Switzerland"}], "References": [{"Title": "Level of immersion affects spatial learning in virtual environments: results of a three-condition within-subjects study with long intersession intervals", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "783", "JournalTitle": "Virtual Reality"}, {"Title": "Designing virtual environments for attitudes and behavioral change in plastic consumption: a comparison between concrete and numerical information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "107", "JournalTitle": "Virtual Reality"}, {"Title": "“Empathy machine”: how virtual reality affects human rights attitudes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "5", "Page": "1407", "JournalTitle": "Internet Research"}, {"Title": "Behavioral Framework of Immersive Technologies (BehaveFIT): How and Why Virtual Reality can Support Behavioral Change Processes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "84", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "Systematic Review of Comparative Studies of the Impact of Realism in Immersive Virtual Experiences", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 106368999, "Title": "A novel text sentiment analysis system using improved depthwise separable convolution neural networks", "Abstract": "<p>Human behavior is greatly affected by emotions. Human behavior can be predicted by classifying emotions. Therefore, mining people’s emotional tendencies from text is of great significance for predicting the behavior of target groups and making decisions. The good use of emotion classification technology can produce huge social and economic benefits. However, due to the rapid development of the Internet, the text information generated on the Internet increases rapidly at an unimaginable speed, which makes the previous method of manually classifying texts one-by-one more and more unable to meet the actual needs. In the subject of sentiment analysis, one of the most pressing problems is how to make better use of computer technology to extract emotional tendencies from text data in a way that is both more efficient and accurate. In the realm of text-based sentiment analysis, the currently available deep learning algorithms have two primary issues to contend with. The first is the high level of complexity involved in training the model, and the second is that the model does not take into account all of the aspects of language and does not make use of word vector information. This research employs an upgraded convolutional neural network (CNN) model as a response to these challenges. The goal of this model is to improve the downsides caused by the problems described above. First, the text separable convolution algorithm is used to perform hierarchical convolution on text features to achieve the refined extraction of word vector information and context information. Doing so avoids semantic confusion and reduces the complexity of convolutional networks. Secondly, the text separable convolution algorithm is applied to text sentiment analysis, and an improved CNN is further proposed. Compared with other models, the proposed model shows better performance in text-based sentiment analysis tasks. This study provides great value for text-based sentiment analysis tasks.</p>", "Keywords": "Convolution neural network;Depthwise separable convolution;Emotion analysis system;Text information", "DOI": "10.7717/peerj-cs.1236", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "Xiaoyu Kong", "Affiliation": "Wuxi Vocational Institute of Commerce, Wuxi, Jiangsu, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Wuxi Vocational Institute of Commerce, Wuxi, Jiangsu, China"}], "References": [{"Title": "Generic framework for multilingual short text categorization using convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "9", "Page": "13475", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106369113, "Title": "How to stop undesired propagations by using bi-level genetic algorithms", "Abstract": "In this work we introduce a general model to analyse any type of propagation system, whether it represents the spread of a fire, fake news, a virus, etc. We study the computational complexity of the problem of minimising the impact of a propagation by cutting some of the connections it can spread through. Even limiting the scope of our cutting strategy to be short-term, we show that the problem is Σ 2 P - complete , that is, it is one level above NP - complete in the complexity hierarchy. Intuitively, in Σ 2 P - complete problems a hard search for the value fulfilling some property is tackled, but just evaluating that property for each candidate value requires performing another hard, independent search. This complexity suggests that a good method to deal with the problem under consideration is a two-level genetic algorithm , that is, a genetic algorithm that uses another genetic algorithm as fitness function: the former algorithm searches for good candidates for solving the target optimisation, and for each candidate within its population, its fitness is calculated by running the latter algorithm. We apply this implementation to two case studies, compare its results with those of greedy and minimax algorithms, and report experimental results. Our results indicate that bi-level genetic algorithms are good candidates to deal with Σ 2 P - complete problems.", "Keywords": "Computational complexity ; Σ 2 P - completeness ; Genetic algorithms ; Propagation", "DOI": "10.1016/j.asoc.2023.110094", "PubYear": 2023, "Volume": "136", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Facultad de Informática, Universidad Complutense de Madrid, 28040 Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Tecnología del Conocimiento, Dept. Sistemas Informáticos y Computación, Universidad Complutense de Madrid, Spain;Facultad de Informática, Universidad Complutense de Madrid, 28040 Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto de Tecnología del Conocimiento, Dept. Sistemas Informáticos y Computación, Universidad Complutense de Madrid, Spain;Facultad de Informática, Universidad Complutense de Madrid, 28040 Madrid, Spain;Corresponding author at: Instituto de Tecnología del Conocimiento, Dept. Sistemas Informáticos y Computación, Universidad Complutense de Madrid, Spain"}], "References": [{"Title": "A review on genetic algorithm: past, present, and future", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "8091", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comparison of different metaheuristics for the quadratic assignment problem in accelerated systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106927", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evaluating genetic algorithms through the approximability hierarchy", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "", "Page": "101388", "JournalTitle": "Journal of Computational Science"}]}, {"ArticleId": 106369145, "Title": "Deep Q-learning multiple networks based dynamic spectrum access with energy harvesting for green cognitive radio network", "Abstract": "In cognitive radio (CR), dynamic spectrum access (DSA) can be used for secondary user (SU) to improve its spectrum efficiency under the limited spectrum resources. However, the SU has to consume more energy to sense the state of primary user (PU) before spectrum access. Therefore, high spectrum efficiency and lower energy consumption have become the requirements of CR. In this paper, we propose a sustainable green CR network combining DSA and energy harvesting, which can perform intelligent spectrum sensing, spectrum access and energy harvesting by a reward mechanism of deep Q-learning multiple networks (DQMN). The DQMN model consisting of multiple neural networks is designed to learn multiple learning objectives, including spectrum sensing, channel selection, communication power and access mode in DSA. A sleep mechanism is introduced to optimize the spectrum access mode and further reduce the energy consumption. The simulation results show that the DQMN-based DSA model can autonomously sense the channel state, harvest the energy, and then select the appropriate access strategy. Due to the energy harvesting and sleep mechanism, the proposed system model shows lower energy consumption and higher average effective throughput than the traditional models.", "Keywords": "", "DOI": "10.1016/j.comnet.2023.109630", "PubYear": 2023, "Volume": "224", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Shenzhen Institute of Information Technology, Shenzhen, 518172, China;The South China Academy of Advanced Optoelectronics, South China Normal University, Guangzhou, 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The South China Academy of Advanced Optoelectronics, South China Normal University, Guangzhou, 510006, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Dalian University of Technology, Dalian, 116024, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The South China Academy of Advanced Optoelectronics, South China Normal University, Guangzhou, 510006, China"}], "References": []}, {"ArticleId": 106369244, "Title": "Pedestrian gender classification on imbalanced and small sample datasets using deep and traditional features", "Abstract": "<p>In this manuscript, imbalanced and small sample space (IB-SSS) dataset problems for pedestrian gender classification using fusion of selected deep and traditional features (PGC-FSDTF) are considered. In this regard, data preparation is first done through data augmentation and preprocessing steps to handle imbalanced classification problem and environmental effects, respectively. The proposed approach follows different types of feature extraction schemes, for instance, pyramid histogram of oriented gradients, hue saturation value histogram, deep visual features of DenseNet201 and InceptionResNetV2-based convolutional neural network architectures. The parallel fusion method computes the maximum and average values-based features from the learned features of both deep networks. Features are selected through features selection methods such as entropy and principal component analysis (PCA). The subsets of features are serially fused and provided to multiple classifiers to perform gender classification on IB-SSS datasets. Resultantly, the proposed PGC-FSDTF method shows better results in terms of different accuracies (overall, mean, and balanced), and area under curve on selected datasets. Further, improved results are achieved on applied datasets using PCA-based selected features and medium Gaussian support vector machine (M-SVM) classifier. These results on different datasets confirm that the selected feature combination provides a way to handle IB-SSS issues for PGC effectively.</p>", "Keywords": "IB-SSS datasets; Data augmentation; Deep and traditional features; Features selection and fusion; Gender classification", "DOI": "10.1007/s00521-023-08331-4", "PubYear": 2023, "Volume": "35", "Issue": "16", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Cantt, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Cantt, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Cantt, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Cantt, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Cantt, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, FAST - National University of Computer and Emerging Sciences, Chiniot, Pakistan"}], "References": [{"Title": "Automated glaucoma detection using GIST and pyramid histogram of oriented gradients (PHOG) descriptors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "3", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "SVM-PCA Based Handwritten Devanagari Digit Character Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "48", "JournalTitle": "Recent Patents on Computer Sciencee"}, {"Title": "A deep neural network and classical features based scheme for objects recognition: an application for machine inspection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "5", "Page": "14935", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "J-LDFR: joint low-level and deep neural network feature representations for pedestrian gender classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "361", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Multi-deep features fusion for high-resolution remote sensing image scene classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "2047", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An efficient image retrieval based on an integration of HSV, RLBP, and CENTRIST features using ensemble classifier learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "Page": "24463", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106369265, "Title": "Echo state graph neural networks with analogue random resistive memory arrays", "Abstract": "Recent years have witnessed a surge of interest in learning representations of graph-structured data, with applications from social networks to drug discovery. However, graph neural networks, the machine learning models for handling graph-structured data, face significant challenges when running on conventional digital hardware, including the slowdown of <PERSON>’s law due to transistor scaling limits and the <PERSON> bottleneck incurred by physically separated memory and processing units, as well as a high training cost. Here we present a hardware–software co-design to address these challenges, by designing an echo state graph neural network based on random resistive memory arrays, which are built from low-cost, nanoscale and stackable resistors for efficient in-memory computing. This approach leverages the intrinsic stochasticity of dielectric breakdown in resistive switching to implement random projections in hardware for an echo state network that effectively minimizes the training complexity thanks to its fixed and random weights. The system demonstrates state-of-the-art performance on both graph classification using the MUTAG and COLLAB datasets and node classification using the CORA dataset, achieving 2.16×, 35.42× and 40.37× improvements in energy efficiency for a projected random resistive memory-based hybrid analogue–digital system over a state-of-the-art graphics processing unit and 99.35%, 99.99% and 91.40% reductions of backward pass complexity compared with conventional graph learning. The results point to a promising direction for next-generation artificial intelligence systems for graph learning.", "Keywords": "Computer science;Electrical and electronic engineering;Engineering;general", "DOI": "10.1038/s42256-023-00609-5", "PubYear": 2023, "Volume": "5", "Issue": "2", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Hong Kong, Hong Kong, China; Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; ACCESS – AI Chip Center for Emerging Smart Systems, InnoHK Centers, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Hong Kong, Hong Kong, China; ACCESS – AI Chip Center for Emerging Smart Systems, InnoHK Centers, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Hong Kong, Hong Kong, China; ACCESS – AI Chip Center for Emerging Smart Systems, InnoHK Centers, Hong Kong, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Hong Kong, Hong Kong, China; Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; ACCESS – AI Chip Center for Emerging Smart Systems, InnoHK Centers, Hong Kong, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Frontier Institute of Chip and System, Fudan University, Shanghai, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University, Zhejiang, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Pisa, Pisa, Italy"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; Frontier Institute of Chip and System, Fudan University, Shanghai, China"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ACCESS – AI Chip Center for Emerging Smart Systems, InnoHK Centers, Hong Kong, China; Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong, China"}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Hong Kong, Hong Kong, China; ACCESS – AI Chip Center for Emerging Smart Systems, InnoHK Centers, Hong Kong, China"}, {"AuthorId": 15, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices & Integrated Technology, Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China; Frontier Institute of Chip and System, Fudan University, Shanghai, China"}], "References": [{"Title": "Deep learning incorporating biologically inspired neural dynamics and in-memory computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "6", "Page": "325", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 106369275, "Title": "Improving convergence and practicality of slide-type reductions", "Abstract": "The best lattice reduction algorithm known in theory for approximating the Shortest Vector Problem (SVP) over lattices is the slide reduction algorithm (STOC '08 & CRYPTO '20). In this paper, we first improve the running time analysis of computing slide-reduced bases based on potential functions. This analysis applies to a generic slide reduction algorithm that includes (natural variants of) slide reduction and block-Rankin reduction (ANTS '14). We then present a rigorous dynamic analysis of generic slide reduction using techniques originally applied to a variant of BKZ (CRYPTO '11). This provides guarantees on the quality of the current lattice basis during execution. This dynamic analysis not only implies sharper convergence for these algorithms to find a short nonzero vector (rather than a fully reduced basis), but also allows to heuristically model/trace the practical behaviour of slide reduction. Interestingly, this dynamic analysis inspires us to introduce a new slide reduction variant with better time/quality trade-offs. This is confirmed by both our experiments and simulation, which also show that our variant is competitive with state-of-the-art reduction algorithms. To the best of our knowledge, this work is the first attempt of improving the practical performance of slide reduction beyond speeding up the SVP oracle.", "Keywords": "Lattice reduction ; Slide reduction ; (H)SVP ; Dynamical systems ; Gaussian heuristic", "DOI": "10.1016/j.ic.2023.105012", "PubYear": 2023, "Volume": "291", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inria and DIENS, PSL, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Zama, France"}], "References": []}, {"ArticleId": 106369276, "Title": "Hybrid-triggered and fault-tolerant observer-based control for neural networks under malicious attacks", "Abstract": "Based upon hybrid-triggered mechanism, this article inspects fault-tolerant issue based ( Q , S , R ) dissipative control for neural networks system along with malcious attacks and external disturbances. To be precious, conventional <PERSON><PERSON>er observer is employed to estimate the system state and malicious attack signal, whereas the presence of smoothed signal of malicious attack in the system state will not allow the augmented state to be precisely estimate. Moreover, the hybrid-triggered mechanism which incorporates both time- and event-triggered scheme is initiated to mitigate the redundancy of network transmission and secures the network resources. Preciously, the stochastic switching within the mechanism satisfies the <PERSON><PERSON><PERSON> distribution. The main intention of this study is to develop a hybrid-triggered scheme and a fault-tolerant controller for ensuring the mean-square asymptotic stability for the desired neural networks with ( Q , S , R ) dissipative performance index. Assisted by Lyapunov stability theory and some inequality techniques, an adequate criterion has been attained in the configuration of linear matrix inequalities (LMIs) to guarantee the asymptotic stability of the neural networks system. Further, the anticipated gain matrices are attained with the strength of obtained LMIs. In the final analysis, the applicability and efficacity of the proposed control model are reflected through two numerical examples. Introduction Neural Networks (NNs), a peculiar class of non-linear complex systems has received auspicious attenuation for many decades owing to its significant characteristics like complicated dynamic behaviors and effective chaotic phenomena [1]. NNs has been efficiently used in multidisciplinary engineering fields specifically, clustering, reinforcement learning, control problems, bidirectional association memory, Chua circuit, face and fingerprint detection, fixed point computations and the rest [2]. Notably, the information regarding these application depends mainly on inherent behaviors, such as stability, stabilization and state estimation problems [1], [2], [3], [4], [8]. In practice, applications of NNs are commonly encountered by time delays, which may lead to unpleasant dynamic behaviors, such as unexpected oscillation, performance deterioration of the system and chaos. Hence, the analysis of NNs contingent with time delay components has substantial research sector in theory and practice. To date, quite a lot of remarkable results on NNs with time delays have been revealed in the literature (for example see [5], [6], [7]). Generally, time-triggered scheme has been extensively applied during the analysis and representation of control systems. Notably, time-triggered control scheme plays a crucial role in NNs.By utilizing this method, we effectively trigger the sampling interval and ensure better bandwidth performance [8]. But, in case of limited network resources, it fails to transmit large repetitive signals, which is the main drawback. To overcome this difficulty, an event-triggering communication scheme has been introduced [9], which selects necessary data to transmit over the network. To be precise, event-triggered mechanism faciliates more efficiently while realising the sample data packets into the network than conventional time-triggered scheme. In recent times, several works on event-triggering mechanism have been addressed [9], [10], [11], [12]. Eventhough, event-triggered scheme protects the network resources and enhances the communication efficiency, but it may degrade the system performance when the system state fluctuates wildly at the sampled instant. To conquer this shortcoming, a special triggering scheme called hybrid-triggered scheme [13] has been introduced, which incorporates both time- and event-triggering in a single frame. The authors in [14] firstly introduced the hybrid-triggered scheme for the networked control system to address the issue of stabilization.Based on this, recently, Sathishkumar et al., in [15] studied hybrid-triggered scheme for singular cascade control systems subject to randomly occurring cyber-attacks and actuator saturation. However, only few works have been conducted on NNs under hybrid-triggered control scheme [8], [16], [17], which enthuses us to do the present study. On the flip side, practical systems are consistently experiencing faults like actuators, processes and sensors, which may incur performance deterioration of the system and induce potential damages [18]. In order to secure the safty and realibility of the system, a controller has to be developed to detect and estimate the faults specified. Among several control methods, reliable control attracts most of the researches for the fault estimation strategy due to its relaxed limitations in obtaining the shape and magnitude of the faults (to name a few, see reference [18], [19], [20]). In recent times, malicious attack has been paid considerable attention in control systems, where it vigorously targets the potential weak point of the system and interrupt the transmitting information during fluent transmission.As well, cycer-attacks are the major threats to networked systems, in which the control theory, optimization and game theory approaches are the mainstream methodologies for this defense scenario designs and guarantees the controlled system to restore the nominal operation in the short period of time. In addition, attacks are categorized as deception attacks, denial-of-service(DoS), jamming and false data injection. In [21], an integrated data-driven framework is proposed to improve the cyber security of the industrial data transmitted through networks. Moreover, the design of adaptive secure controller in [22] counteracts denial-of-service (DoS) attacks, whereas the work on [23] utilizes backstepping secure controller to deal the attacks. Furthermore, the authors in [24] investigates discrete-time cyber-physical systems subject to actuator and sensor attacks. Generally, observer technique is used when false data injection attacks takes place [25]. Very recently, some valuable effort has been made by Chen et al., to analyze malicious attack via smoothed signal. For instance, refer to the works [19], [26], [27]. Based on these groundbreaking studies, the analysis and synthesis of NNs with malicious attack has not been studied still yet, which motivates our current study. Still now, only a few works have been done in the past times for NNs under hybrid-triggered control synthesis. But, some vital glitches like malicious attack, actuator fault with the controller have not been addressed for NNs model. This aforesaid challenge tempetate us to explore our research interest towards corroborating hybrid-triggered control configuration for NNs model with respect to actuator fault and malicious attack. Moreover, the main features of this paper are revealed in the upcomming fivefold: • Under the aegis of observer scheme, a novel hybrid-triggered mechanism-based dissipative controller for NNs subject to actuator fault and malicious attack has been proposed as a first venture. • An unconventional smoothed model is considered for setting up the unavailable actuator and sensor fault signals are estimated with the system states through a conventional Luenberger observer scheme. • The hybrid-triggered mechanism characterized by stochastic Bernoulli distribution is engaged to mitigate the frequency utilization of the network resources and allivates the networked transmission burden. • By the choice of Lyapunov–Krasovskii functional (LKF) integrated with some integral inequalities, the awaited asymptotic stability criterion in the sense of mean-square for the commenced NNs is achieved via LMIs. • Ultimately, the ability of the proposed controller design is validated through two numerical examples. Besides the introduction, the formation of this work includes four more sections which are systematized as follows: Section 2 is furnished with NNs model followed by observer and controller descriptions. By employing Lyapunov approach, the main analysis of the considered problem is portrayed in Section 3 under hybrid-triggered mechanism. Simulation results reported in Section 4 verify the feasibility of the developed theritical results in Section 3. In the end, Section 5 concludes this paper with a short note. Notation: The notations used throughout this paper are standard which are unrevealed as follows: R n , R m and R l represents the n, m and l-dimensional Euclidean space; L 2 [ 0 , ∞ ) represents the space of square integrable functions over the interval [ 0 , ∞ ) . P > 0 ( ⩾ 0 ) means that P is real symmetric and positive definite (positive semi-definite); The superscripts “ T ” and “-1” stand for matrix transposition and matrix inverse, respectively. 0 and I denote the zero and identity matrix with corresponding dimension; E is the mathematical expectation; In symmetric block matrices or long matrix expressions, we use an asterisk ( * ) to represent a term that is induced by symmetry and diag { … } stands for a block-diagonal matrix. Section snippets System description In this segment, we pay our concentration towards NNs with mixed delays and external disturbances. Then, the system dynamics can be governed by the following differential equations: x ̇ ( t ) = - Ax ( t ) + N 0 ϑ ( x ( t ) ) + N 1 ϑ ( x ( t - ε ( t ) ) ) + Bu F ( t ) + B a f a ( t ) + Dw ( t ) , y ( t ) = Cx ( t ) + B s f s ( t ) + B n n ( t ) , z ( t ) = Ex ( t ) , where the vector x ( t ) = [ x 1 ( t ) , x 2 ( t ) , … , x n ( t ) ] T ∈ R n denotes the state correlated with n units of neurons; u F ( t ) is the control input; y ( t ) ∈ R m describes the system output; z ( t ) ∈ R l represents the controlled output; ϑ ( x ( t ) ) = [ ϑ 1 ( x 1 ( Main results In this segment, based upon hybrid-triggered strategy via network control system in Fig. 1, we present the fault-tolerant observer-based control scheme at remote side of NNs under malicious attacks. First of all, in accordance with the LKF, the mean-square asymptotic stability and strict ( Q , S , R ) dissipativity for the augmented NNs (13) is enhanced with known fault matrix. Afterwards, the obtained conditions are expanded to the case of unknown fault matrix. Theorem 1 For NNs in ( 1 ) with the fault-tolerant Simulation results In this section, we delineate two numerical examples to showcase the suitability and utility of the proffered hybrid-triggered mechanism based fault-tolerant control scheme, with the use of aforementioned theoretical results. To be specific, the simulation results in this section are attained by using MATLAB LMI toolbox. Example 1 For the sake of brevity, the parameters of NNs model (1) are proffered as follows: A = 1.05 0.0 0.0 1.05 , N 0 = 0.3 - 0.4 - 0.4 0.3 , N 1 = 0.3 - 0.3 - 0.3 0.3 , B = 3.0 0.0 0.0 2.0 , D = 0.1 0.2 , C = 10.0 5.0 9.0 4.0 , E = Conclusion In this study, the issue of hybrid-triggered based fault-tolerant control for NNs model subject to malcious attack and external disturbances has been deliberated. Specifically, the hybrid-triggered scheme in a random manner which satisfies the Bernoulli distribution is exploited to reduces the communication transmission in the network. Under the aegis of Lyapunov functional and wielding linear matrix approach, a set of sufficient conditions is computed in the design of LMIs, which ensures the CRediT authorship contribution statement S.A. Karthick: Conceptualization, Methodology, Software, Validation, Writing - original draft, Writing - review & editing. Bor-Sen Chen: Conceptualization, Validation, Writing - original draft, Writing - review & editing, Methodology. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgment This work was supported by the Ministry of Science and Technology (MOST), Taiwan, under grant MOST 108–2221-E-007. Karthick Arumugam received the B.Sc., M.Sc. and M. Phil degrees in mathematics from Bharathiar University, Coimbatore, India, in 2009, 2011 and 2013, respectively, and the Ph.D. degree in mathematics from Anna University, Chennai, India, in 2020. He is currently a Postdoctoral Researcher with the Department of Electrical Engineering, National Tsing Hua University, Hsinchu, Taiwan. He has authored or coauthored more than 20 research papers. His research interests include Fuzzy system, suspension References (38) A. Kazemy et al. Master-slave synchronization of neural networks subject to mixed-type communication attacks Inf. Sci. (2021) J. Wang et al. Event-triggered dissipative state estimation for Markov jump neural networks with random uncertainties J. Franklin Inst. (2019) L. Zha et al. Event-triggered non-fragile state estimation for delayed neural networks with randomly occurring sensor nonlinearity Neurocomputing (2018) H. Yang et al. Event-triggered state estimation for Markovian jumping neural networks: On mode-dependent delays and uncertain transition probabilities Neurocomputing (2021) S.A. Karthick et al. Observer based guaranteed cost control for Markovian jump stochastic neutral-type neural networks Chaos, Solitons and Fractals (2020) A. Lu et al. Event-triggered secure observer-based control for cyber-physical systems under adversarial attacks Inf. Sci. (2017) D. Zeng et al. Reliable stability and stabilizability for complex-valued memristive neural networks with actuator failures and aperiodic event-triggered sampled-data control Nonlinear Anal.: Hybrid Syst. (2021) X. Mu et al. Reliable observer-based finite-time H ∞ control for networked nonlinear semi-Markovian jump systems with actuator fault and parameter uncertainties via dynamic event-triggered scheme Inf. Sci. (2021) J. Liu et al. Quantized state estimation for neural networks with cyber attacks and hybrid triggered communication scheme Neurocomputing (2018) J. Liu et al. Hybrid-driven-based H<sub>∞</sub> filter design for neural networks subject to deception attacks Appl. Math. Comput. (2018) M. Sathishkumar et al. Hybrid-triggered reliable dissipative control for singular networked cascade control systems with cyber-attacks J. Franklin Inst. (2020) P. Selvaraj et al. Equivalent-input-disturbance estimator-based event-triggered control design for master slave neural networks Neural Networks (2021) Y.Y. Liu et al. Dynamic output feedback control for networked systems with limited communication based on deadband event-triggered mechanism Inf. Sci. (2021) S. Dong et al. Design of H<sub>∞</sub> state estimator for delayed static neural networks under hybrid-triggered control and imperfect measurement strategy J. Franklin Inst. (2020) J. Xiao et al. Finite-time passivity of neural networks with time varying delay J. Franklin Inst. (2020) J. Chen et al. Stability analysis for neural networks with time-varying delay via improved techniques IEEE Trans. Cybern. (2019) S.H. Lee et al. Stability and dissipativity criteria for neural networks with time-varying delays via an augmented zero equality approach Neural Networks (2021) W. Lin et al. Extended dissipativity analysis for Markovian jump neural networks with time-varying delay via delay-product-type functionals IEEE Trans. Neural Networks Learn. Syst. (2019) C. Wang et al. Neural network-based distributed adaptive pre-assigned finite-time consensus of multiple TCP/AQM networks IEEE Trans. Circuits Syst. I Regul. Pap. (2021) View more references Cited by (0) Recommended articles (6) Research article Improving Proximal Policy Optimization with Alpha Divergence Neurocomputing, 2023 Show abstract Proximal policy optimization (PPO) is a recent advancement in reinforcement learning, which is formulated as an unconstrained optimization problem including two terms: accumulative discount return and Kullback-Leibler (KL) divergence. Currently, there are three PPO versions: primary, adaptive, and clipping. The most widely used PPO algorithm is the clipping version, in which the KL divergence is replaced by a clipping function to measure the difference between two policies indirectly. In this paper, we revisit this primary PPO and improve it in two aspects. One is to reformulate it as a linearly combined form to control the trade-off between two terms. The other is to substitute a parametric alpha divergence for KL divergence to measure the difference of two policies more effectively. This novel PPO variant is referred to as alphaPPO in this paper. Experiments on six benchmark environments verify the effectiveness of our alphaPPO, compared with clipping and combined PPOs. Research article An asymmetric Lyapunov-Krasovskii functional approach for event-triggered consensus of multi-agent systems with deception attacks Applied Mathematics and Computation, Volume 439, 2023, Article 127584 Show abstract This study provides an asymmetric Lyapunov–Krasovskii functional (LKF) to address the leader-following consensus of multi-agent systems (MASs) subject to deception attacks. To reduce unwanted signal transmissions between agents, we employ an event-triggering scheme. Also, a stochastic variable following a Bernoulli distribution is used to describe whether the communication among agents are affected by deception attack signals. A connected interaction graph is presented to describe the flow of information among neighboring agents. The considered problem is first modified into a stabilization problem by using the properties of algebraic graph theory. Then, we design a novel asymmetric LKF to show the desired stability conditions in the form of linear matrix inequalities (LMIs), which lead us the consensus of the chosen MAS. The efficacy of the obtained results is validated via illustrative examples. Research article Secure synchronization against link attacks in complex networks with event-triggered coupling Information Sciences, Volume 628, 2023, pp. 291-306 Show abstract This paper concentrates on the secure synchronization problem against link attacks in complex networks with event-triggered coupling. The link attacks for each link are independent, which means that there are multiple attack patterns for coupling communication topology. Then, the relevant model of link attacks with multiple patterns is constructed to analyze the effect on synchronization error system. Furthermore, to reduce communication burden and save coupling resources, a sampled-data-based event-triggered coupling mechanism is established. In this situation, the triggering condition is supervised only at discrete sampling instants. By employing Lyapunov theory and some inequality techniques, sufficient conditions are given to guarantee the synchronization against link attacks with multiple patterns. At last, a simulation example on Chua’s circuit network is provided to demonstrate the effectiveness of the proposed results. Research article Reduced-order filtering for semi-Markovian jump systems against randomly occurring false data injection attacks Applied Mathematics and Computation, Volume 444, 2023, Article 127832 Show abstract This paper describes a mode-dependent reduced-order filtering problem for semi-Markovian jump systems with time-varying delay and external disturbance, where the measurement output is vulnerable to randomly occurring false data injection attacks. To facilitate analysis, the attacks are described by a nonlinear function that meets Lipschitz continuity and the possible attack scenarios are represented by a stochastic parameter that follows the Bernoulli distribution. Based on the information from the considered system and reduced-order filter, an augmented filtering system is constructed. Then, a convex optimization problem is formulated by using Lyapunov-Krasovskii stability theory and stochastic analysis. The filter gain matrices are efficiently derived as a result, ensuring that the augmented filtering system is stochastically stable and strictly ( Q , S , R ) − γ -dissipative. Through numerical examples, the advantages and effectiveness of the developed theoretical findings are clearly demonstrated. Research article Stability of T-S fuzzy system under non-fragile sampled-data H ∞ control using augmented Lyapunov-Krasovskii functional Journal of the Franklin Institute, Volume 360, Issue 4, 2023, pp. 3162-3188 Show abstract This article is concerned with the non-fragile sampled-data control for T-S fuzzy system with parameter uncertainties. Firstly, a novel augmented Lyapunov-Krasovskii functional with sufficient sampled-data information is constructed. And a novel h ( t ) -depended exponential stability criterion with H ∞ performance is gotten by reciprocally convex matrix inequality. Beyond that, compared with the existing methods, the gain matrices for non-fragile sampled-data controller expected are less conservative by linear matrix inequality technique. And numerical examples are provided to support the viability and validity of the results. Research article Affine matched parameterization approach to sampled-data stabilization criteria for T-S fuzzy systems with variable sampling Journal of the Franklin Institute, Volume 358, Issue 7, 2021, pp. 3530-3553 Show abstract This paper presents new parameterized sampled-data stabilization criteria using affine transformed membership functions for T-S fuzzy systems. To deal with the sampled control input having aperiodic sampling intervals, the proposed method adopts new looped functionals, and employs a modified free weighting matrix inequality. A relaxed condition for the controller design is derived by formulating the constraint conditions of the membership functions in the proposed controller with affinely matched weighting parameter vectors. Based on a newly devised lemma for handling affinely matched vectors, the stabilization and guaranteed cost performance criteria are given in terms of linear matrix inequalities (LMIs). The superiority of the presented method is demonstrated via significantly improved results in numerical examples. Karthick Arumugam received the B.Sc., M.Sc. and M. Phil degrees in mathematics from Bharathiar University, Coimbatore, India, in 2009, 2011 and 2013, respectively, and the Ph.D. degree in mathematics from Anna University, Chennai, India, in 2020. He is currently a Postdoctoral Researcher with the Department of Electrical Engineering, National Tsing Hua University, Hsinchu, Taiwan. He has authored or coauthored more than 20 research papers. His research interests include Fuzzy system, suspension system and Neural networks. Bor-Sen Chen (Life Fellow, IEEE) received the B.S. degree in electrical engineering from the Tatung Institute of Technology, Taipei, Taiwan, in 1970, the M.S. degree in geophysics from the National Central University, Chungli, Taiwan, in 1973, and the Ph.D. degree in electrical engineering from the University of Southern California, Los Angeles, CA, USA, in 1982. He was a Lecturer, an Associate Professor, and a Professor at the Tatung Institute of Technology, from 1973 to 1987. Currently, he is the Tsing Hua Distinguished Chair Professor of Electrical Engineering and Computer Science at the National Tsing Hua University, Hsinchu, Taiwan. His current research interests include control engineering, signal processing, and systems biology. He has received the Distinguished Research Award from the National Science Council of Taiwan four times. He is a National Chair Professor of the Ministry of Education of Taiwan. View full text © 2023 Published by Elsevier B.V. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.02.009", "PubYear": 2023, "Volume": "532", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Tsing Hua University, Hsinchu 30013, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Tsing Hua University, Hsinchu 30013, Taiwan;Department of Electrical Engineering, Yuan Ze University, Chung-Li 32003, Taiwan;Corresponding author at: Department of Electrical Engineering, National Tsing Hua University, Hsinchu 30013, Taiwan"}], "References": [{"Title": "Reliable observer-based finite-time H∞ control for networked nonlinear semi-Markovian jump systems with actuator fault and parameter uncertainties via dynamic event-triggered scheme", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "573", "JournalTitle": "Information Sciences"}, {"Title": "Event-triggered state estimation for Markovian jumping neural networks: On mode-dependent delays and uncertain transition probabilities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "424", "Issue": "", "Page": "226", "JournalTitle": "Neurocomputing"}, {"Title": "Master–slave synchronization of neural networks subject to mixed-type communication attacks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "20", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic output feedback control for networked systems with limited communication based on deadband event-triggered mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "817", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 106369307, "Title": "Metagenomic data of bacterial communities associated with Acropora species from Phu Quoc Islands, Vietnam", "Abstract": "Acropora is one of the most common coral genera found in Phu Quoc Islands, Vietnam. However, the presence of marine snails, such as the coralllivorous gastropod <PERSON><PERSON><PERSON> rugosa, was a potential threat to the survival of many scleractinian species, leading to changes in the health status and bacterial diversity of coral reefs in Phu Quoc Islands. Here, we describe the composition of bacterial communities associated with two species of Acropora ( Acropora formosa and Acropora millepora ) using the Illumina sequencing technology. This dataset includes 5 coral samples of each status (grazed or healthy), which were collected in Phu Quoc Islands (9°55′20.6″N 104°01′16.4″E) in May 2020. A total of 19 phyla, 34 classes, 98 orders, 216 families and 364 bacterial genera were detected from 10 coral samples. Overall, Proteobacteria and Firmicutes were the two most common bacterial phyla in all samples. Significant differences in the relative abundances of the genera Fusibacter, Halarcobacter, Malaciobacter, and Thalassotalea between grazed and healthy status were observed. However, there was no differences in alpha diversity indices between the two status. Furthermore, the dataset analysis also indicated that Vibrio and Fusibacter were core genera in the grazed samples, whereas Pseudomonas was the core genus in the healthy samples.", "Keywords": "Bacterial diversity;Coral reefs;Core microbiome;Grazed Acropora;Illumina sequencing", "DOI": "10.1016/j.dib.2023.108977", "PubYear": 2023, "Volume": "47", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Biotechnology (IBT), Vietnam Academy of Science and Technology (VAST), Hanoi, Vietnam."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Biotechnology (IBT), Vietnam Academy of Science and Technology (VAST), Hanoi, Vietnam. ;Graduate University of Science and Technology (GUST), VAST, Hanoi, Vietnam."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "UMR MARBEC IRD-CNRS-IFREMER-Université Montpellier, Montpellier, France."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UMR MARBEC IRD-CNRS-IFREMER-Université Montpellier, Montpellier, France."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UMR MARBEC IRD-CNRS-IFREMER-Université Montpellier, Montpellier, France."}, {"AuthorId": 6, "Name": "<PERSON> Chu", "Affiliation": "Institute of Biotechnology (IBT), Vietnam Academy of Science and Technology (VAST), Hanoi, Vietnam. ;Graduate University of Science and Technology (GUST), VAST, Hanoi, Vietnam."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Biotechnology (IBT), Vietnam Academy of Science and Technology (VAST), Hanoi, Vietnam. ;Graduate University of Science and Technology (GUST), VAST, Hanoi, Vietnam."}], "References": []}, {"ArticleId": 106369347, "Title": "Accurate long-term air temperature prediction with Machine Learning models and data reduction techniques", "Abstract": "In this paper, three customised Artificial Intelligence (AI) frameworks, considering Deep Learning, Machine Learning (ML) algorithms and data reduction techniques, are proposed for a problem of long-term summer air temperature prediction. Specifically, the prediction of the average air temperature in the first and second August fortnights, using input data from previous months, at two different locations (Paris, France) and (Córdoba, Spain), is considered. The target variable, mainly in the first August fortnight, can contain signals of extreme events such as heatwaves, like the heatwave of 2003, which affected France and the Iberian Peninsula. Three different computational frameworks for air temperature prediction are proposed: a Convolutional Neural Network (CNN), with video-to-image translation, several ML approaches including Lasso regression, Decision Trees and Random Forest, and finally a CNN with pre-processing step using Recurrence Plots, which convert time series into images. Using these frameworks, a very good prediction skill has been obtained in both Paris and Córdoba regions, showing that the proposed approaches can be an excellent option for seasonal climate prediction problems.", "Keywords": "Deep Learning ; Temperature prediction ; Recurrence plots ; Data reduction techniques", "DOI": "10.1016/j.asoc.2023.110118", "PubYear": 2023, "Volume": "136", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Signal Processing and Communications, Universidad de Alcalá, 28805, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Signal Processing and Communications, Universidad de Alcalá, 28805, Madrid, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Signal Processing and Communications, Universidad de Alcalá, 28805, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "TECNALIA, Basque Research & Technology Alliance (BRTA), 48160 Derio, Spain;University of the Basque Country (UPV/EHU), 48013 Bilbao, Spain"}, {"AuthorId": 5, "Name": "S. Salcedo-Sanz", "Affiliation": "Department of Signal Processing and Communications, Universidad de Alcalá, 28805, Madrid, Spain"}], "References": [{"Title": "Convolutional neural network: a review of models, methodologies and applications to object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "85", "JournalTitle": "Progress in Artificial Intelligence"}, {"Title": "Machine learning information fusion in Earth observation: A comprehensive review of methods, applications and data sources", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "256", "JournalTitle": "Information Fusion"}, {"Title": "A spatial–temporal graph attention network approach for air temperature forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107888", "JournalTitle": "Applied Soft Computing"}, {"Title": "Randomization-based machine learning in renewable energy prediction problems: Critical literature review, new results and perspectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>-<PERSON>; <PERSON><PERSON>-Bueno", "PubYear": 2022, "Volume": "118", "Issue": "", "Page": "108526", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 106369396, "Title": "Special Issue on the 10th European Conference on Mobile Robots (ECMR 2021)", "Abstract": "Research article Repérer le nœud lymphatique sentinelle au cours de la chirurgie et l’extraire Revue Vétérinaire Clinique, 2023 Research article Électrochimiothérapie en oncologie vétérinaire Revue Vétérinaire Clinique, 2023 Research article Réponse à la lettre de <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON> de Chirurgie Orthopédique et Traumatologique, 2023 Research article Using model checking to formally verify rendezvous algorithms for robots with lights in Euclidean space Robotics and Autonomous Systems, Volume 163, 2023, Article 104378 Show abstract The paper details the first successful attempt at using model checking techniques to verify the correctness of distributed algorithms for robots evolving in a continuous environment. The study focuses on the problem of rendezvous of two robots with lights. There exist many different rendezvous algorithms that aim at finding the minimal number of colors needed to solve rendezvous in various synchrony models ( e.g. , FSYNC, SSYNC, ASYNC). While these rendezvous algorithms are typically very simple, their analysis and proof of correctness tend to be extremely complex, tedious, and error-prone as impossibility results are based on subtle interactions between the activation schedules of the robots. The paper presents a generic verification model that can be concretely expressed in available software model-checkers. In particular, we explain the subtle design decisions that allow to keep the search space finite and tractable, as well as prove several important theorems that support them. As a sanity check, we use the model to verify several known rendezvous algorithms in six different models of synchrony. In each case, we find that the results obtained from the model checker are consistent with the results known in the literature. The model checker outputs a counter-example execution in every case that is known to fail. In the course of developing and proving the validity of the model, we identified several fundamental theorems, including the ability for a well chosen algorithm and ASYNC scheduler to produce an emerging property of memory in a system of oblivious mobile robots, and why it is not a problem when robots executing the gathering algorithms are equipped with lights. View full text © 2023 Published by Elsevier B.V. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.robot.2023.104380", "PubYear": 2023, "Volume": "163", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Bonn, Regina-Pacis-Weg 3, Bonn, 53113, NRW, Germany;University of Bonn, Regina-Pacis-Weg 3, Bonn, 53113, NRW, Germany;University of Padua School of Engineering, via g. <PERSON> 6/A, I-35131, Padova, Italy;Corresponding editor"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Padua School of Engineering, via g. grade<PERSON> 6/A, I-35131, Padova, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Bonn, Regina-Pacis-Weg 3, Bonn, 53113, NRW, Germany"}], "References": [{"Title": "CorAl: Introspection for robust radar and lidar perception in diverse environments using differential entropy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "104136", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "User feedback and remote supervision for assisted living with mobile robots: A field study in long-term autonomy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "104170", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Strategies for large scale elastic and semantic LiDAR reconstruction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "104185", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Enhanced calibration of camera setups for high-performance visual odometry", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "104189", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "A fully integrated system for hardware-accelerated TSDF SLAM with LiDAR sensors (HATSDF SLAM)", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "156", "Issue": "", "Page": "104205", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "A rapidly-exploring random trees approach to combined task and motion planning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "104238", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "ECO-CPP: Energy constrained online coverage path planning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "104242", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Towards real-time forest inventory using handheld LiDAR", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "104240", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "HAPTR2: Improved Haptic Transformer for legged robots’ terrain classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "158", "Issue": "", "Page": "104236", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Mapping beyond what you can see: Predicting the layout of rooms behind closed doors", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "159", "Issue": "", "Page": "104282", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Online pole segmentation on range images for long-term LiDAR localization in urban environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "159", "Issue": "", "Page": "104283", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Static map generation from 3D LiDAR point clouds exploiting ground segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "159", "Issue": "", "Page": "104287", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Real-time multi-modal semantic fusion on unmanned aerial vehicles with label propagation for cross-domain adaptation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "159", "Issue": "", "Page": "104286", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Adaptive path planning for UAVs for multi-resolution semantic segmentation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "159", "Issue": "", "Page": "104288", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Best Axes Composition extended: Multiple gyroscopes and accelerometers data fusion to reduce systematic error", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "160", "Issue": "", "Page": "104316", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Bimanual telemanipulation with force and haptic feedback through an anthropomorphic avatar system", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "161", "Issue": "", "Page": "104338", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Rendering the Directional TSDF for Tracking and Multi-Sensor Registration with Point-To-Plane Scale ICP", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "162", "Issue": "", "Page": "104337", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Lifelong mapping in the wild: Novel strategies for ensuring map stability and accuracy over time evaluated on thousands of robots", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "164", "Issue": "", "Page": "104403", "JournalTitle": "Robotics and Autonomous Systems"}]}, {"ArticleId": 106369478, "Title": "Dataset on microplastic concentrations, characteristics, and chemical composition in the marine surface waters of Latvia – the Eastern Gotland basin and the Gulf of Riga", "Abstract": "The dataset provides information on spectroscopically verified microplastics, both particles and fibers, from 44 marine surface water samples of two Baltic Sea sub-basins – the semi-enclosed Gulf of Riga and the Eastern Gotland Basin. Sampling was performed by using Manta trawl with a mesh size of 300 µm. Thereafter, the organic material was digested with sodium hydroxide, hydrogen peroxide and enzymes. Samples were filtered on glass fiber filters and analyzed visually, registering the shape, size, and color of each item. Where feasible, the polymer type was determined using Attenuated Total Reflection Fourier Transform Infrared (ATR-FTIR) spectroscopy method. The number of plastic particles per m<sup>3</sup> of filtered water was determined. The data presented in this article may be useful for further research on microplastic pollution, meta-analysis and calculation of microplastic flow. Interpretation and analysis of total acquired data on micro debris and microplastics are reported in the article “Occurrence and spatial distribution of microplastics in the surface waters of the Baltic Sea and the Gulf of Riga”.", "Keywords": "Plastic pollution ; Litter ; Baltic sea ; Sea surface ; FTIR", "DOI": "10.1016/j.dib.2023.108992", "PubYear": 2023, "Volume": "47", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Latvian Institute of Aquatic Ecology, 4 Voleru Str., Riga LV-1007, Latvia;Daugavpils University, The Faculty of Natural Sciences and Mathematics, 1 Parades str., LV-5401, Daugavpils, Latvia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Latvian Institute of Aquatic Ecology, 4 Voleru Str., Riga LV-1007, Latvia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Latvian Institute of Aquatic Ecology, 4 Voleru Str., Riga LV-1007, Latvia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Latvian Institute of Aquatic Ecology, 4 Voleru Str., Riga LV-1007, Latvia"}, {"AuthorId": 5, "Name": "Inta Dimante-Deimantovica", "Affiliation": "Latvian Institute of Aquatic Ecology, 4 Voleru Str., Riga LV-1007, Latvia"}], "References": []}, {"ArticleId": 106369519, "Title": "A New Tunable Gyrator-C-Based Active Inductor Circuit for Low Power Applications", "Abstract": "A new CMOS implementation of active inductor circuit based on the gyrator-C which is suitable for low-voltage and RF applications is presented in this paper. One of the critical features of active inductors which directly affect their quality factor is their series-loss resistance. In this regard, a multi-regulated cascade stage is employed in the new structure which decreases loss. Moreover, by cascading input transistor, the transistors which determine the self-resonance frequency and quality factor will be separated from each other. This results in arranging properties of designed active inductor independently. The configuration of two-stage conventional gyrators is improved which gives the proposed design more opportunities in determining and tuning its characteristics. Also by employing common-source configuration, low conductance nodes are achieved which decrease the ohm-loss of AI. Furthermore, main properties of design can be tuned without affecting each other. The power consumption of the circuit remains as low as 0.62[Formula: see text]mW, then the circuit is suitable for low power applications. The results showed that AI is suitable for RF applications over 0.2–11.8[Formula: see text]GHz frequency range. In order to verify theoretical calculations, simulations are carried out using HSPICE and level 49 parameters (BSIM3v3) in 130[Formula: see text]nm CMOS technology. In addition, Corner and Monte Carlo analyses are considered to prove the efficiency of the circuit against the process variation.", "Keywords": "Active inductor; gyrator-C; low power; quality factor; regulated cascode", "DOI": "10.1142/S0218126623502353", "PubYear": 2023, "Volume": "32", "Issue": "13", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "Rasool Gardeshkhah", "Affiliation": "Department of Electrical-Electronics Engineering, Urmia Branch, Islamic Azad University, Urmia, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical-Electronics Engineering, Urmia Branch, Islamic Azad University, Urmia, Iran"}], "References": [{"Title": "Design of High Gain Folded Cascode OTA-Based Transconductance–Capacitance Loop Filter for PLL Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "14", "Page": "2150263", "JournalTitle": "Journal of Circuits, Systems and Computers"}]}, {"ArticleId": 106369624, "Title": "DTBVis: An interactive visual comparison system for digital twin brain and human brain", "Abstract": "The digital twin brain (DTB) computing model from brain-inspired computing research is an emerging artificial intelligence technique, which is realized by a computational modeling approach of hardware and software. It can achieve various cognitive abilities and their synergistic mechanisms in a manner similar to the human brain. Given that the task of the DTB is to simulate the functions of the human brain, comparing the similarities and differences between the two is crucial. However, the visualization study of the DTB is still under-researched. Moreover, the complexity of the datasets (multilevel spatiotemporal granularity and different types of comparison tasks) presents new challenges to the analysis and exploration of visualization. Therefore, in this study, we proposed DTBVis, a visual analytics system that supports comparison tasks for the DTB. DTBVis supports iterative explorations from different levels and at different granularities. Combined with automatic similarity recommendation, and high-dimensional exploration, DTBVis can assist experts in understanding the similarities and differences between the DTB and the human brain, thus helping them adjust their model and enhance its functionality. The highest level of DTBVis shows an overview of the datasets from the brain, which is used for comparison and exploration of the function and structure of the DTB and the human brain. The medium level is used for the comparison and exploration of a designated brain region. The low level can analyze a designated brain voxel. We worked closely with experts of brain science and held regular seminars with them. Feedback from the experts indicates that our approach helps them conduct comparative studies of the DTB and human brain and make modeling adjustments of the DTB through intuitive visual comparisons and interactive explorations.", "Keywords": "Brain ; Digital twin brain ; Visual analytics ; Comparative analysis", "DOI": "10.1016/j.visinf.2023.02.002", "PubYear": 2023, "Volume": "7", "Issue": "2", "JournalId": 47888, "JournalTitle": "Visual Informatics", "ISSN": "2543-2656", "EISSN": "2468-502X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science, Fudan University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "School of Data Science, Fudan University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science, Fudan University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intellignece, Fudan University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Electronic Information, Nanjing Normal University, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intellignece, Fudan University, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Feng", "Affiliation": "School of Data Science, Fudan University, China;Institute of Science and Technology for Brain-Inspired Intellignece, Fudan University, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science, Fudan University, China;Corresponding authors"}], "References": [{"Title": "Metaverse: Perspectives from graphics, interactions and visualization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "56", "JournalTitle": "Visual Informatics"}]}, {"ArticleId": 106369649, "Title": "Actor-Driven Decomposition of Microservices through Multi-level Scalability Assessment", "Abstract": "The microservices architectural style has gained widespread acceptance. However, designing applications according to this style is still challenging. Common difficulties concern finding clear boundaries that guide decomposition while ensuring performance and scalability. With the aim of providing software architects and engineers with a systematic methodology, we introduce a novel actor-driven decomposition strategy to complement the domain-driven design and overcome some of its limitations by reaching a finer modularization yet enforcing performance and scalability improvements. The methodology uses a multi-level scalability assessment framework that supports decision-making over iterative steps. At each iteration, architecture alternatives are quantitatively evaluated at multiple granularity levels. The assessment helps architects to understand the extent to which architecture alternatives increase or decrease performance and scalability. We applied the methodology to drive further decomposition of the core microservices of a real data-intensive smart mobility application and an existing open-source benchmark in the e-commerce domain. The results of an in-depth evaluation show that the approach can effectively support engineers in (\n i \n ) decomposing monoliths or coarse-grained microservices into more scalable microservices and (\n ii \n ) comparing among alternative architectures to guide decision-making for their deployment in modern infrastructures that orchestrate lightweight virtualized execution units.", "Keywords": "Microservices; Decomposition process; Architectural patterns; Performance analysis; Scalability assessment", "DOI": "10.1145/3583563", "PubYear": 2023, "Volume": "32", "Issue": "5", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politecnico di Milano, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Sannio, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Free University of Bozen-Bolzano, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Sannio, Italy"}], "References": [{"Title": "Scalability Assessment of Microservice Architecture Deployment Configurations: A Domain-based Approach Leveraging Operational Profiles and Load Tests", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "165", "Issue": "", "Page": "110564", "JournalTitle": "Journal of Systems and Software"}, {"Title": "DevOpRET: Continuous reliability testing in DevOps", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "From monolithic systems to Microservices: An assessment framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "137", "Issue": "", "Page": "106600", "JournalTitle": "Information and Software Technology"}, {"Title": "Modeling Performance of Microservices Systems with Growth Theory", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "2", "Page": "39", "JournalTitle": "Empirical Software Engineering"}, {"Title": "FOCloud: Feature Model Guided Performance Prediction and Explanation for Deployment Configurable Cloud Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Automated test-based learning and verification of performance models for microservices systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "111225", "JournalTitle": "Journal of Systems and Software"}, {"Title": "PROMENADE: A big data platform for handling city complex networks with dynamic graphs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "129", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Scalability testing automation using multivariate characterization and detection of software performance antipatterns", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "111446", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 106369652, "Title": "Feature extraction and selection from electroencephalogram signals for epileptic seizure diagnosis", "Abstract": "<p>Epilepsy is one of the most common neurological diseases, affecting approximately 50 million people. This illness can be diagnosed by electroencephalogram (EEG), whose analysis depends on human interpretation, which may lead to divergent results and be imprecise and error-prone. Moreover, one estimates more than 80% of the epilepsy examinations return no anomalies at all, wasting the effort of analysis. In this context, machine learning (ML) methods are used to aid in epilepsy detection. An essential task for classifier building is feature extraction, for which there are many measures that can be computed. However, part of these features can be irrelevant or hinder the performance of ML methods. This paper proposes an automatic way to classify EEG segments by using a reduced set of features. The proposed method combines multispectral analysis, feature selection, and classifier building approaches. As our main contribution, a methodology to minimize the number of features for classifier building is proposed. A total of 285 measures are extracted. Afterward, two attribute selection approaches are used: <PERSON>’s correlation coefficient filter and wrapper based on the genetic algorithm. Then, five well-known ML methods ( k -nearest-neighbor, support vector machine, naive Bayes, artificial neural network, and random forest) were used to build 281 different classifiers. As a result, the proposed classifiers reached an accuracy between 87.2 and 90.99% and considerably reduced the number of features from 285 to 30, keeping competitive scores. Additionally, statistical hypothesis tests prove that our proposed approach is as efficient as using the complete feature dataset for classifier building.</p>", "Keywords": "Electroencephalogram; Epilepsy; Feature selection; Multi-class classification", "DOI": "10.1007/s00521-023-08350-1", "PubYear": 2023, "Volume": "35", "Issue": "16", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Program in Electrical Engineering, Federal University of Technology – Paraná, Pato Branco, Brazil"}, {"AuthorId": 2, "Name": "Jefferson Tales Oliva", "Affiliation": "Graduate Program in Electrical Engineering, Federal University of Technology – Paraná, Pato Branco, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate Program in Electrical Engineering, Federal University of Technology – Paraná, Pato Branco, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Program in Electrical Engineering, Federal University of Technology – Paraná, Pato Branco, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of São Paulo, São Carlos, Brazil"}], "References": [{"Title": "A novel hybrid feature selection method based on dynamic feature importance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106337", "JournalTitle": "Applied Soft Computing"}, {"Title": "A comprehensive comparison of handcrafted features and convolutional autoencoders for epileptic seizures detection in EEG signals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "163", "Issue": "", "Page": "113788", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Classification of Alzheimer’s Disease from EEG Signal Using Robust-PCA Feature Extraction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "192", "Issue": "", "Page": "3114", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 106369672, "Title": "Smart Control of Home Appliances Using Hand Gesture Recognition in an IoT-Enabled System", "Abstract": "Recently, with the vigorous development of the Internet of Things (IoT) technology, all kinds of intelligent home appliances in the market are constantly innovating. The public requirements for residential safety and convenience are also increasing. Meanwhile, with the improvement of indigenous medical technology and quality of life, people’s average lifespan is gradually increasing. However, countries around the world are facing the problem of aging societies. Hand gesture recognition is gaining popularity in the fields of gesture control, robotics, or medical applications. Therefore, how to create a convenient and smart control system of home appliances for the elderly or the disabled has become the objective of this study. It aims to use Google MediaPipe to develop a hand tracking system, which detected 21 key points of a hand through the camera lens of a mobile device and used a vector formula to calculate the angle of the intersection of two lines based on four key points. After the angle of bending finger is obtained, users’ hand gesture can be recognized. Our experiments have confirmed that the recognition precision and recall values of hand gesture for numbers 0–9 reached 98.80% and 97.67%, respectively; and the recognition results were used to control home appliances through the low-cost IoT-Enabled system.", "Keywords": "", "DOI": "10.1080/08839514.2023.2176607", "PubYear": 2023, "Volume": "37", "Issue": "1", "JournalId": 7360, "JournalTitle": "Applied Artificial Intelligence", "ISSN": "0883-9514", "EISSN": "1087-6545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Taipei 1, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Ming Chi University of Technology 84, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Ming Chi University of Technology 84, Taipei, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Taipei University 151, New Taipei City TAIWAN;Department of Information Management, Chaoyang University of Technology, Taichung, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Ming Chi University of Technology 84, Taipei, Taiwan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, Fu Jen Catholic University 510, Taipei, Taiwan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Ming Chi University of Technology 84, New Taipei, Taiwan"}], "References": [{"Title": "Path planning for robotic teams based on LTL specifications and Petri net models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "55", "JournalTitle": "Discrete Event Dynamic Systems"}, {"Title": "Gesture and Speech Recognizing <PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "7", "Page": "585", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Hand Gesture Recognition of Methods-Time Measurement-1 Motions in Manual Assembly Tasks Using Graph Convolutional Networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "A review on smart city - IoT and deep learning algorithms, challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "3", "JournalTitle": "International Journal of Engineering Systems Modelling and Simulation"}, {"Title": "A Review of Deep Learning-based Human Activity Recognition on Benchmark Video Datasets", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "2093705", "JournalTitle": "Applied Artificial Intelligence"}]}, {"ArticleId": 106369678, "Title": "Predictive Analytics of Employee Attrition using K-Fold Methodologies", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijmsc.2023.01.03", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 28282, "JournalTitle": "International Journal of Mathematical Sciences and Computing", "ISSN": "2310-9025", "EISSN": "2310-9033", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sreenidhi Institute of Science and Technology, Yamnampet, Ghatkesar, Hyderabad, Telanganga-501301"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106369696, "Title": "Mitigating robust overfitting via self-residual-calibration regularization", "Abstract": "Overfitting in adversarial training has attracted the interest of researchers in the community of artificial intelligence and machine learning in recent years. To address this issue, in this paper we begin by evaluating the defense performances of several calibration methods on various robust models. Our analysis and experiments reveal two intriguing properties: 1) a well-calibrated robust model is decreasing the confidence of robust model; 2) there is a trade-off between the confidences of natural and adversarial images . These new properties offer a straightforward insight into designing a simple but effective regularization, called Self-Residual-Calibration (SRC). The proposed SRC calculates the absolute residual between adversarial and natural logit features corresponding to the ground-truth labels. Furthermore, we utilize the pinball loss to minimize the quantile residual between them, resulting in more robust regularization. Extensive experiments indicate that our SRC can effectively mitigate the overfitting problem while improving the robustness of state-of-the-art models. Importantly, SRC is complementary to various regularization methods. When combined with them, we are capable of achieving the top-rank performance on the AutoAttack benchmark leaderboard.", "Keywords": "Adversarial training ; Adversarial defense ; Robust overfitting ; Self-residual-calibration ; Regularization", "DOI": "10.1016/j.artint.2023.103877", "PubYear": 2023, "Volume": "317", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, 101-8430, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Trento, Via Calepina, 14, <PERSON><PERSON>, 38122, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Trento, Via Calepina, 14, Trento, 38122, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, 101-8430, Japan;The University of Tokyo, Tokyo, 113-8654, Japan"}], "References": [{"Title": "Trustworthy AI: From Principles to Practices", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 106369810, "Title": "An improved lightweight and real-time YOLOv5 network for detection of surface defects on indocalamus leaves", "Abstract": "<p>Indocalamus leaves are widely used in the Chinese food industry. Surface defect detection plays a crucial role in the post-harvest reprocessing of indocalamus leaves. In this study, we constructed a lightweight convolutional neural network model to detect surface defects on indocalamus leaves. We investigated four categories of surface defects, including damage, black spots, insect spots, and holes, to construct a dataset of surface defects on indocalamus leaves, which contained 4124 images for model training and evaluation. We replaced the original path aggregation network (PANet) in YOLOv5 with a cross-layer feature pyramid network (CFPN), which improved the detection performance by fusing feature maps at different levels. We proposed an improved feature fusion module, named the receptive dilated and deformable convolution field block (RDDCFB), which was integrated into the CFPN for learning within larger spatial and semantic contexts. Furthermore, a new CA mechanism was proposed to improve the feature representation capability of the network by appropriately adjusting the structure of the coordinate attention (CA) mechanism. Extensive experiments using the Pascal VOC and CIFAR-100 datasets demonstrated that this new CA block had superior accuracy and integration capabilities. On MSCOCO2017 validation datasets, experiments show that our module is consistently better than various detectors, including Faster R-CNN, YOLOv3, and YOLOv4. Finally, our quantitative results from the dataset of surface defects on indocalamus leaves indicated the effectiveness of the proposed method. The accuracy and recognition efficiency of the improved YOLOv5 model could reach 97.7% and 97 frames per second, respectively.</p>", "Keywords": "Attention mechanism; Defect detection; Feature pyramid network; Multi-scale object; YOLOv5", "DOI": "10.1007/s11554-023-01281-z", "PubYear": 2023, "Volume": "20", "Issue": "1", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Changsha Xiangfeng Intelligent Equipment Co., Ltd., Changsha, China"}], "References": [{"Title": "Fast identification model for coal and gangue based on the improved tiny YOLO v3", "Authors": "Hongguang Pan; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "3", "Page": "687", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Polarized self-attention: Towards high-quality pixel-wise mapping", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "506", "Issue": "", "Page": "158", "JournalTitle": "Neurocomputing"}, {"Title": "Real-time object detection method of melon leaf diseases under complex background in greenhouse", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Shuolin Kong", "PubYear": 2022, "Volume": "19", "Issue": "5", "Page": "985", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Lightweight target detection algorithm based on YOLOv4", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "6", "Page": "1123", "JournalTitle": "Journal of Real-Time Image Processing"}]}, {"ArticleId": 106369813, "Title": "ABOA-CNN: auction-based optimization algorithm with convolutional neural network for pulmonary disease prediction", "Abstract": "<p>Nowadays, deep learning plays a vital role behind many of the emerging technologies. Few applications of deep learning include speech recognition, virtual assistant, healthcare, entertainment, and so on. In healthcare applications, deep learning can be used to predict diseases effectively. It is a type of computer model that learns in conducting classification tasks directly from text, sound, or images. It also provides better accuracy and sometimes outdoes human performance. We presented a novel approach that makes use of the deep learning method in our proposed work. The prediction of pulmonary disease can be performed with the aid of convolutional neural network (CNN) incorporated with auction-based optimization algorithm (ABOA) and DSC process. The traditional CNN ignores the dominant features from the X-ray images while performing the feature extraction process. This can be effectively circumvented by the adoption of ABOA, and the DSC is used to classify the pulmonary disease types such as fibrosis, pneumonia, cardiomegaly, and normal from the X-ray images. We have taken two datasets, namely the NIH Chest X-ray dataset and ChestX-ray8. The performances of the proposed approach are compared with deep learning-based state-of-art works such as BPD, DL, CSS-DL, and Grad-CAM. From the performance analyses, it is confirmed that the proposed approach effectively extracts the features from the X-ray images, and thus, the prediction of pulmonary diseases is more accurate than the state-of-art approaches.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2023, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "ABOA;CNN;DSC;Deep learning;Pulmonary diseases;X-ray images", "DOI": "10.1007/s00521-022-08033-3", "PubYear": 2023, "Volume": "35", "Issue": "10", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing Science and Engineering (SCSE), VIT Bhopal University, Bhopal, MP India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Networking and Communications, School of Computing, SRM Institute of Science & Technology (SRMIST), Kattankulathur, Tamil Nadu India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational Intelligence School of Computing, SRM Institute of Science and Technology (SRMIST), Kattankulathur, Tamil Nadu India."}], "References": [{"Title": "Deep-learning framework to detect lung abnormality – A study with chest X-Ray and lung CT scan images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "271", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Cascaded deep learning classifiers for computer-aided diagnosis of COVID-19 and pneumonia diseases in X-ray scans", "Authors": "<PERSON>; <PERSON><PERSON>, <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "1", "Page": "235", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Detection of tuberculosis from chest X-ray images: Boosting the performance with vision transformer and transfer learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115519", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106369911, "Title": "Exploring Collective Medical Knowledge and Tensions in Online ADHD Communities", "Abstract": "<p>My proposed dissertation work highlights social media as digitally-mediated support for neurodivergent individuals. By adopting a critical disability theory lens, I critique the techno-solutionism currently present in digital mental health care. I argue that existing social media platforms can provide community support for neurodivergent individuals to step away from the individualistic approaches currently promoted by much digital mental health technology. These social media-based communities are providing an important service of care and collective knowledge for individuals going through similar experiences to find validation and a sense of agency regarding treatment options. My research will further explore the relationships neurodivergent individuals have had with diagnostic and care systems, as well as ongoing tensions with healthcare providers in both physical and digital spaces.</p>", "Keywords": "", "DOI": "10.1145/3584732.3584734", "PubYear": 2023, "Volume": "", "Issue": "135", "JournalId": 29302, "JournalTitle": "ACM SIGACCESS Accessibility and Computing", "ISSN": "1558-2337", "EISSN": "1558-1187", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of California Santa Cruz, California, USA"}], "References": [{"Title": "The Purpose of Play", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}]}, {"ArticleId": 106369961, "Title": "The Importance of High-Bandwidth Low-Latency Network Systems in the Modern Age", "Abstract": "This paper investigates the importance of high-bandwidth and low-latency network systems in the modern age. It introduces the concepts of networks systems, bandwidth and latency. Then the paper analyzes factors affecting bandwidth and latency in network systems. The factors analyzed include the distance over which the network connection spans, the router and wireless protocols used, as well as the type of cabling used in wired connections. Finally, the paper uses the aforementioned analysis to investigate the choices and solutions that financial institutions use in order to optimize the speed and capacity of their algorithms, when performing high-frequency trading. Choices analyzed include the proximity of the algorithms to the exchange servers, as well as the type of cabling used in wired connections to facilitate fast data transfers.", "Keywords": "Network Systems;Network Bandwidth;Network Latency;Networks used in High-Frequency Trading", "DOI": "10.14738/tecs.111.13967", "PubYear": 2023, "Volume": "11", "Issue": "1", "JournalId": 29184, "JournalTitle": "Transactions on Machine Learning and Artificial Intelligence", "ISSN": "", "EISSN": "2054-7390", "Authors": [{"AuthorId": 1, "Name": "Ion<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CTO at PostureHealth Inc"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CTO at AiSport"}], "References": []}, {"ArticleId": 106369992, "Title": "A multi-objective evolutionary algorithm with decomposition and the information feedback for high-dimensional medical data", "Abstract": "High-dimensional medical data often leads to a phenomenon known as the ”curse of dimensionality,” which causes additional memory and high training costs, as well as degrading the generalization capacity of learning algorithms. To address this issue, a multi-objective evolutionary algorithm that integrates decomposition and the information feedback model (IFMMOEAD) is proposed for high-dimensional medical data. This algorithm not only considers the number of selected features, but also classification accuracy and correlation measures of features when feature dimensionality reduction is executed. The property of IFMMOEAD is first verified by standard benchmarks DTLZ1–DTLZ7. Then, it is used to develop machine learning algorithms for thirty-five high-dimensional cancer gene expression data sets, showing excellent potential for high-dimensional medical machine learning. Finally, the IFMMOEAD is applied to empirical clinical data of multiple myeloma, significantly outperforming existing algorithms in terms of normalized mutual information and adjusted rand index metrics. We suggest that this algorithm could be implemented in medical information systems as a promising technique for high-dimensional medical problems. Introduction Medical diagnosis and treatment have significantly contributed to human health over thousands of years, particularly in the last few decades [1]. However, clinical trial and cohort study data are often high-dimensional, posing hurdles for conventional approaches. The analysis of high-dimensional illness data is critical in biomedicine and bioinformatics; nevertheless, although treating diseases such as cancer has produced remarkable outcomes, there are still disorders for which appropriate therapy has yet to be developed. High-dimensional data contain many irrelevant or weak correlation features, making it difficult for existing machine learning techniques to effectively determine patterns from these attributes. This phenomenon is known as the ”Curse of Dimensionality”, where ultrahigh dimensionality of real data sets not only incurs additional memory needs and high training costs but also degrades the generalization capacity of learning algorithms. Therefore, it is essential to accurately reduce the dimensionality of empirical data for machine learning models. There are two methods for reducing dimensionality: feature extraction and feature selection. The purpose of feature extraction is to minimize the subspace by creating new features and modifying existing ones; these extra updates should be meaningful and non-redundant. On the other hand, feature selection (FS) is described as recognizing important features and rejecting irrelevant and redundant characteristics to get a subset of features that properly represent a particular situation with minimal performance loss [2]. Both strategies strive to improve the performance of machine learning methods by using simpler models, hence increasing training speed. Feature extraction frequently generates a group of unique updates by combining existing features, which may lead to poor interpretability of new features, especially in practical applications. Feature selection has good interpretability compared with the extraction set, and feature selection has received more attention in the high-dimensional machine learning field. From a structural standpoint, feature selection may be split into three groups: filter, embedded, and wrapper, based on the interaction with the learning technique. Filter approaches execute the feature selection phase as pre-processing before the learning step. The filter is independent of the learning method and is based on underlying data properties. Wrapper approaches employ the learning algorithm as a subroutine, comparing the utility of the features to the learning algorithm’s prediction accuracy over a validation set. The feature selection process is explicitly included in the machine learning technique in embedded methods. Thus, the search is driven by the learning process itself. In recent years, there has been a large number of research work on these three types of feature selection. Regarding Filter, Almuallim et al. [3] designed a boolean learning concept in the presence of many irrelevant features, and experiments also show that the proposed weighted-greedy algorithm performs an excellent approximation. Dash et al. [4] Dash focus on the inconsistency measurement, which states that a feature subset is inconsistent if there are at least two occurrences with the identical attribute values and yet dissimilar labels. Kwak et al. [5] created a novel approach for computing mutual information among input and class variables using the Parzen window, which he then used to develop an algorithm for selecting features for classification tasks. Peng et al. [6] presented the minimal-redundancy–maximal-relevance criterion for feature selection using the maximal statistical dependence condition with mutual info. Pablo et al. [7] proposed a filter-based feature selection technique based on mutual info, referred to as Normalized Mutual Information Feature Selection (NMIFS). The newly developed GAMIFS surpasses the restrictions of incremental search algorithms which are unable to detect correlations between sets of features. The filter method often uses heuristic rules based on information statistics to quickly eliminate noise features with high computing efficiency and strong universality. However, the decision model formed by the filtering method often lacks good generalization ability, which limits its practical application and the primary downside of this method is that it neglects the integration between the selected subset and the performance of the induction algorithm. For the Wrapper method, Kohavi et al. [8] proposed this method firstly for the feature selection problem. Sindhwani et al. [9] presented feature selection algorithms using mutual information between class labels and classifier outputs. Yang et al. [10] developed a wrapper-based feature selection approach for MLP neural networks that evaluates the importance of a feature by calculating the overall difference. This method has shown to be particularly effective in terms of presentation. Cao et al. [11] designed a packaging feature selection method for classifying missing data, starting with missing data in the data set and classifying the missing data, thus significantly improving the testing accuracy of the classifier. Karasu et al. [12] proposed a wrapper-based feature selection method using multi-objective optimization for chaotic crude oil time series data. Hu et al. [13] created a dispersed foraging slime mould method for combinatorial optimization and feature engineering using wrappers. Naveed et al. [14] finally developed a breast cancer diagnostic utilizing wrapper-based feature selection and an artificial neural network. The filter method of feature selection does not take into account the learning algorithm that will be used afterwards, whereas wrapper feature selection is dependent on the particular learner that will be employed. The integration of Filter and Wrapper algorithms into the feature selection process, known as Embedded Feature Selection, links the learner training process to the selection of features [15]. Setiono et al. [16] presented a three-layer feedforward neural network to select the most helpful input features for discriminating classes in a given set of input patterns. The findings clearly indicate that the suggested strategy performs effectively on a broad range of classification tasks. The importance of certain features was assessed by Shen et al. [17] by calculating the sum of the absolute differences between the probabilistic outputs of a support vector machine with and without that feature in the feature space. This led to the development of an embedded feature selection algorithm based on sensitivity analysis of the probabilistic outputs from the support vector machine. Zhang et al. [18] presented a manifold regularization-based embedded feature selection technique, MDFS, for the purpose of identifying discriminative features that are common to multiple class labels. Deng et al. [19] created an integrated feature selection technique to discover optimal information fusion coefficients adaptively. The experimental data has shown that the proposed model is superior to the existing heuristic and embedded feature selection techniques. Filters, which are independent of the learning algorithm, usually produce a smaller subset of features than Embedded methods, but they lack the ability to evaluate how well these features will perform in a classification task. On the other hand, Wrappers assess feature subsets based on their performance in a classification task [20], which often leads to better results than Filters for a given classification algorithm [2]. Wrapper methods show high efficiency; they can process high-dimensional feature subsets with good performance and high efficiency. Though Wrapper methods show apparent superiority to the other two types of feature selection, it severely limits the practical application because machine learning models based on such feature selection are often prone to overfitting [21], [22]. Feature selection can be difficult due to the vast number of possible solutions for a data set with n features, which is equal to 2 n [23], [24]. It is getting more difficult as n increases in various domains due to the development of data-gathering tools and the growing complexity of the associated challenges. Evolutionary computation algorithms have been used widely to construct feature selection because of their special universality; their performance is not limited to the differentiability of the problem [25]. Genetic algorithms (GAs) are likely the first evolutionary computation algorithms widely used to solve problems with feature selection [26]. Derrac et al. [27] made a cooperative coevolutionary algorithm for feature selection using GA with three populations. The first population focused on selected features, the second on selecting instances, and the third on both. Li et al. [28] also came up with a multiple-populations-based GA for selecting features, in which each pair of neighboring populations shared two individuals to share information and make searching easier. Chen et al. [29] proposed utilizing Genetic Algorithms (GAs) for feature clustering in order to address issues with feature selection. A GA was used to optimize the cluster center values of a clustering approach, thereby enabling the grouping of features into distinct clusters. Lin et al. [30] came up with a GA-based feature selection algorithm for predicting financial distress. In addition, Particle Swarm Optimizations (PSOs) were also used for feature selection. Xue et al. [31] developed a novel initialization approach that functions similarly to the conventional forward and backward feature selection techniques in the Particle Swarm Optimization (PSO) exploration. Lane et al. [32] suggested using PSO and statistical clustering to choose features. Chuang et al. [33] came up with a way to reset gbest by using zero features to tell the PSO to search for small subsets of features. Moreover, ant colony optimization (ACOs) was also widely used in feature selection. Ke et al. [34] suggested that limited pheromone values be used in ACO to choose features. Vieira et al. [35] suggested a cooperative ACO algorithm with two colonies for choosing features. The first colony would decide how many features were needed, and the second colony would choose each feature. Santana et al. [36] conducted a comparison of the effectiveness of Ant Colony Optimization (ACO) and a Genetic Algorithm (GA)-based feature selection method when applied to ensemble classifiers. Besides the above GAs, PSO, and ACOs for feature selection, there are still a large number of other evolutionary computation algorithms for this issue, such as differential evolution (DE) [37], [38], artificial bee colony algorithm (ABC) [39], [40], estimation of distribution algorithm (EDA) [41], [42], grey wolf optimization algorithm (GWO) [43], [44], slime mould algorithm et al. (SMA) [13], [45]. Although many existing evolutionary computing methods or variants algorithms have been widely used in feature selection issues, some challenges and their potential have not been thoroughly investigated [2]. In many areas of scientific problems, like gene analysis, the number of features can easily reach thousands or even millions which raises the cost of computing and calls for more advanced search methods, but both have their problems, so increasing computational power alone will not solve the problem. Most methods for selecting features are hard to execute. After all, they require much computation, a big problem in evolutionary computing methods for selecting features because they often involve many evaluations. In addition, among these current research works, few researchers consider multi objectives when designing feature selection algorithms, just thinking about how well the classification works; It is necessary to consider multiple factors, such as the number of features, the correlation between them, and any redundancy between features, in order to make informed decisions in real-world applications. Therefore, proposing efficient and effective approaches to feature selection problems with multiple objectives is still challenging. In this study, a multi-objective evolutionary algorithm is designed using an information feedback model and decomposition (IFMMOEAD) and then evolves a machine learning for high-dimensional medical machine learning. In this proposed IFMMOEAD, an information feedback model is added to the enhanced decomposition multi-objective evolutionary algorithm to ensure rapid and effective feature selection. Initially, the performance of the newly suggested IFMMOEAD is validated using benchmarks (DTLZ1–DTLZ7) [46], and statistical experiment results reveal that the proposed IFMMOEAD can efficiently and accurately approximate the genuine Pareto front face of the test problem. Furthermore, the presented IFMMOEAD has been used to adjust the algorithm for machine learning from three criteria, which are consists of features extracted f 1 , accuracy of classification f 2 , and correlation metrics f 3 , and data analysis findings demonstrate that IFMMOEAD can evolve machine learning techniques satisfactorily for thirty-five cancer gene expression data sources. Subsequently, the IFMMOEAD is used to develop a high-dimensional medical machine-learning algorithm utilizing clinical Multiple myeloma data. When applied to real-world, high-dimensional Multiple myeloma data, the experiment results reveal that IFMMOEAD performs much better than other algorithms. The main contributions of this study are as follows: • A multi-objective evolutionary algorithm is proposed using an information feedback model and decomposition, and the information feedback model is integrated into an enhanced decomposition algorithm. • The effectiveness of IFMMOEAD was tested on seven benchmarks, and it was found to be superior to the original algorithms and other related algorithms. • The IFMMOEAD has been effectively utilized to develop a machine learning system for both 35 cancer gene expression datasets and real-world clinical Multiple myeloma. • The IFMMOEAD can be viewed as a feasible option for medical machine learning involving high-dimensional data. The following is how this paper is organized: Section 2 contains information on the materials and techniques. The IFMMOEAD method is described in Section 3. The experimental designs are shown in Section 4. Section 5 contains numerical results. The conclusion and future work are provided in Section 6. Section snippets Backgrounds and procedures In this section, the definition of a multi objective optimization problem is given first, and then decomposition is also given in this section. In addition, the definition of information feedback models is described at length, respectively. The proposed IFMMOEAD This section proposes a new decomposition multi-objective evolutionary algorithm IFMMOEAD based on the information feedback model. As verified in study [51], the M-R1 can construct the best algorithm among these six information feedback models where it can effectively use the previous adequate historical information in the optimization process on benchmark IEEE CEC2018 which are dynamic multi-objective optimization problems. Therefore, M-R1 is also adopted in this study and introduced into the Designs for experiments There are two experimental portions in this research. In the first experimental portion, the properties of the proposed IFMMOEAD are validated. In the second experimental section, the proposed IFMMOEAD is used to develop machine learning for medical data classification while considering various classification objectives. In the first step, we conduct a comprehensive evaluation of the proposed IFMMOEAD’s efficiency on the DTLZ1–DTLZ7 benchmarks while also comparing it to those mentioned above Results of benchmarks In this section, the effectiveness of IFMMOEAD is tested on the DTLZ1–DTLZ7 benchmarks [46]. These benchmarks are composed of seven benchmarks, where DTLZ1–DTLZ6 are continuous and unimodal, and DTLZ7 has disconnected Pareto-optimal regions in the search space. In addition, a variety of other optimizers, including NSGAIII, NSGAII, RVEA, MOEA/D, MMPSO, MOEADDE, and MOEADD have been chosen to be compared with the IFMMOEAD on DTLZ1-DTLZ7. All of these associated algorithms are each carried out in Conclusion In this study, an improved multi-objective evolutionary decomposition algorithm for high-dimensional medical problems is designed. In the proposed IFMMOEAD, the information feedback model is introduced into the original multi-objective evolutionary decomposition algorithm. Regarding the statistical results on DTLZ1–DTLZ7 benchmarks obtained by IFMMOEAD and other algorithms, the property of the IFMMOEAD performs significant advantages compared to other algorithms and shows great potential for CRediT authorship contribution statement Mingjing Wang: Writing – original draft, Writing – review & editing, Software, Visualization, Investigation. Ali Asghar Heidari: Review & editing, Software, Visualization. Huiling Chen: Conceptualization, Methodology, Formal analysis, Investigation, Writing – review & editing, Funding acquisition, Supervision. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work is supported by the National Natural Science Foundation of China (Nos. 62076185 ). References (63) Ji Yazhou et al. An evolutionary machine learning for multiple myeloma using runge kutta optimizer from multi characteristic indexes Computers in Biology and Medicine (2022) Zhang Shaohong et al. Generalized adjusted rand indices for cluster ensembles Pattern Recognit. (2012) Zhang Yin et al. Enhancing MOEA/D with information feedback models for large-scale many-objective optimization Inform. Sci. (2020) Gu Zi-Min et al. Improving NSGA-III algorithms with information feedback models for large-scale many-objective optimization Future Gener. Comput. Syst. (2020) Wang Mingjing et al. Medical machine learning based on multiobjective evolutionary algorithm using learning decomposition Expert Systems with Applications (2023) Abdel-Basset Mohamed et al. An efficient binary slime mould algorithm integrated with a novel attacking-feeding strategy for feature selection Comput. Ind. Eng. (2021) Emary Eid et al. Binary grey wolf optimization approaches for feature selection Neurocomputing (2016) Shunmugapriya P. et al. A hybrid algorithm using ant and bee colony optimization for feature selection and classification (AC-ABC hybrid) Swarm Evol. Comput. (2017) Hancer Emrah et al. A binary ABC algorithm based on advanced similarity scheme for feature selection Appl. Soft Comput. (2015) Khushaba Rami N. et al. Feature subset selection using differential evolution and a statistical repair mechanism Expert Syst. Appl. (2011) Hancer Emrah et al. Differential evolution for filter feature selection based on information theory and feature ranking Knowl.-Based Syst. (2018) Ke Liangjun et al. An efficient ant colony optimization approach to attribute reduction in rough set theory Pattern Recognit. Lett. (2008) Chuang Li-Yeh et al. Improved binary PSO for feature selection using gene expression data Comput. Biol. Chem. (2008) Xue Bing et al. Particle swarm optimisation for feature selection in classification: Novel initialisation and updating mechanisms Appl. Soft Comput. (2014) Lin Fengyi et al. Novel feature selection methods to financial distress prediction Expert Syst. Appl. (2014) Li Yongming et al. Research of multi-population agent genetic algorithm for feature selection Expert Syst. Appl. (2009) Siedlecki Wojciech et al. A note on genetic algorithms for large-scale feature selection Pattern Recognit. Lett. (1989) Dash Manoranjan et al. Feature selection for classification Intell. Data Anal. (1997) Remeseiro Beatriz et al. A review of feature selection methods in medical applications Comput. Biol. Med. (2019) Deng Tingquan et al. Pointwise mutual information sparsely embedded feature selection Internat. J. Approx. Reason. (2022) Zhang Jia et al. Manifold regularized discriminative feature selection for multi-label learning Pattern Recognit. (2019) Hu Jiao et al. Dispersed foraging slime mould algorithm: continuous and binary variants for global optimization and wrapper-based feature selection Knowl.-Based Syst. (2022) Karasu Seçkin et al. A new forecasting model with wrapper-based feature selection approach using multi-objective optimization technique for chaotic crude oil time series Energy (2020) Kohavi Ron et al. Wrappers for feature subset selection Artificial Intelligence (1997) Dash Manoranjan et al. Consistency-based search in feature selection Artificial Intelligence (2003) Almuallim Hussein et al. Learning boolean concepts in the presence of many irrelevant features Artificial Intelligence (1994) Floyd Katherine et al. The global tuberculosis epidemic and progress in care, prevention, and research: an overview in year 3 of the end TB era Lancet Respir. Med. (2018) Xue Bing et al. A survey on evolutionary computation approaches to feature selection IEEE Trans. Evol. Comput. (2015) Kwak Nojun et al. Input feature selection by mutual information based on Parzen window IEEE Trans. Pattern Anal. Mach. Intell. (2002) Peng Hanchuan et al. Feature selection based on mutual information criteria of max-dependency, max-relevance, and min-redundancy IEEE Trans. Pattern Anal. Mach. Intell. (2005) Estévez Pablo A et al. Normalized mutual information feature selection IEEE Trans. Neural Netw. (2009) View more references Cited by (0) Recommended articles (0) View full text © 2023 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110102", "PubYear": 2023, "Volume": "136", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Southeast University, Nanjing, 211189, China;The Key Laboratory of Computer Network and Information Integration (Southeast University), Ministry of Education, 211189, Nanjing, China;Corresponding author at: School of Computer Science and Engineering, Southeast University, Nanjing, 211189, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Surveying and Geospatial Engineering, College of Engineering, University of Tehran, Tehran, 1439957131, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Chen", "Affiliation": "College of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou, Zhejiang 325035, China;Corresponding author"}], "References": [{"Title": "Improving NSGA-III algorithms with information feedback models for large-scale many-objective optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "49", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Enhancing MOEA/D with information feedback models for large-scale many-objective optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "522", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "An efficient binary slime mould algorithm integrated with a novel attacking-feeding strategy for feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "107078", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Dispersed foraging slime mould algorithm: Continuous and binary variants for global optimization and wrapper-based feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "237", "Issue": "", "Page": "107761", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Medical machine learning based on multiobjective evolutionary algorithm using learning decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "119450", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106370010, "Title": "Correction: Extraction of Semantic Links from a Document-Oriented NoSQL Database", "Abstract": "", "Keywords": "", "DOI": "10.1007/s42979-023-01700-9", "PubYear": 2023, "Volume": "4", "Issue": "2", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CBI²-TRIMANE, Puteaux, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Toulouse Capitole University, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IRIT, Toulouse Capitole University, Toulouse, France"}], "References": []}, {"ArticleId": 106370271, "Title": "A general cost model to assess the implementation of collaborative robots in assembly processes", "Abstract": "Abstract </h3> <p>In assembly processes, collaborative robots (cobots) can provide valuable support to improve production performance (assembly time, product quality, worker wellbeing). However, there is a lack of models capable of evaluating cobot deployment and driving decision-makers to choose the most cost-effective assembly configuration. This paper tries to address this gap by proposing a novel cost model to evaluate and predict assembly costs. The model allows a practical and straightforward comparison of different potential assembly configurations in order to guide the selection towards the most effective one. The proposed cost model considers several cost dimensions, including manufacturing, setup, prospective, retrospective, product quality and wellbeing costs. The cost estimation also considers learning effects on assembly time and quality, particularly relevant in low-volume and mass customised productions. Three real manufacturing case studies accompany the description of the model.</p>", "Keywords": "Collaborative robotics; Cost model; Assembly configuration; Low-volume productions; Learning process", "DOI": "10.1007/s00170-023-10942-z", "PubYear": 2023, "Volume": "125", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DIGEP (Department of Management and Production Engineering), Politecnico di Torino, Turin, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "DIGEP (Department of Management and Production Engineering), Politecnico di Torino, Turin, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DIGEP (Department of Management and Production Engineering), Politecnico di Torino, Turin, Italy"}], "References": [{"Title": "A conceptual framework to evaluate human-robot collaboration", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "3", "Page": "841", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Uncertainty evaluation in the prediction of defects and costs for quality inspection planning in low-volume productions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "11-12", "Page": "3793", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Multi-objective migrating bird optimization algorithm for cost-oriented assembly line balancing problem with collaborative robots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "14", "Page": "8575", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Challenges and opportunities in human robot collaboration context of Industry 4.0 - a state of the art review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "49", "Issue": "2", "Page": "226", "JournalTitle": "Industrial Robot: An International Journal"}]}, {"ArticleId": 106370278, "Title": "Customization of <PERSON><PERSON>’s UCB Strategy for a Gaussian Multiarmed Bandit", "Abstract": "<p> We consider a customization of the UCB strategy first proposed by <PERSON><PERSON> for <PERSON><PERSON><PERSON> two-armed bandit to the case of a Gaussian multiarmed bandit describing batch data processing. This optimal control problem has classical interpretation as a game with nature in which the payment function of the player is the expected loss of total income caused by incomplete information. The goal is stated in minimax setting. For the considered game with nature, we present an invariant description of a control with horizon equal to one; this allows one to perform computations in two ways: using Monte-Carlo simulations and analytically, using dynamic programming technique. For various configurations of the considered game with nature, we have found saddle points that characterize the optimal control and the worst-case distribution of the parameters of the multiarmed bandit. </p>", "Keywords": "multiarmed bandit problem; Gaussian multiarmed bandit; minimax approach; UCB rule; invariant description; Monte-Carlo simulation; dynamic programming", "DOI": "10.1134/S00051179220110108", "PubYear": 2022, "Volume": "83", "Issue": "11", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "S. V. <PERSON>", "Affiliation": "Yaroslav-the-Wise Novgorod State University, Novgorod, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Yaroslav-the-Wise Novgorod State University, Novgorod, Russia"}], "References": []}, {"ArticleId": 106370321, "Title": "An incremental approach to the n-queen problem with polynomial time", "Abstract": "This paper shows that computing the n-queen solution of the chessboard n-by-n from the chessboard (n-1)-by-(n-1) can be used in polynomial time O ( n 2 ) using symbolic computation on the complement of the n queen Graph. Nevertheless, continuing further in an incremental approach, besides the n-1 solution, which represents the maximum cliques of size n-1 on the complement of the graph corresponding to the chessboard n-1, we need the maximum cliques of size n-2,and below. By doing so, we need to eliminate the anti-chain problem, which increases the algorithm’s complexity.", "Keywords": "n queens ; Maximum cliques ; Anti-chain ; Minimum cliques", "DOI": "10.1016/j.jksuci.2023.02.002", "PubYear": 2023, "Volume": "35", "Issue": "3", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Bouneb Zine El Abidine", "Affiliation": "Department of Computer Science and Mathematics, University of Oum el Bouaghi, Algeria"}], "References": []}, {"ArticleId": 106370325, "Title": "High-Order Mesh Morphing for Boundary and Interface Fitting to Implicit Geometries", "Abstract": "We propose a method that morphs high-order meshes such that their boundaries and interfaces coincide/align with implicitly defined geometries. Our focus is particularly on the case when the target surface is prescribed as the zero isocontour of a smooth discrete function. Common examples of this scenario include using level set functions to represent material interfaces in multimaterial configurations, and evolving geometries in shape and topology optimization. The proposed method formulates the mesh optimization problem as a variational minimization of the sum of a chosen mesh-quality metric using the Target-Matrix Optimization Paradigm (TMOP) and a penalty term that weakly forces the selected faces of the mesh to align with the target surface. The distinct features of the method are use of a source mesh to represent the level set function with sufficient accuracy, and adaptive strategies for setting the penalization weight and selecting the faces of the mesh to be fit to the target isocontour of the level set field. We demonstrate that the proposed method is robust for generating boundary- and interface-fitted meshes for curvilinear domains using different element types in 2D and 3D.", "Keywords": "High-order ; Implicit meshing ; Mesh morphing ; r -adaptivity ; Finite elements ; TMOP", "DOI": "10.1016/j.cad.2023.103499", "PubYear": 2023, "Volume": "158", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lawrence Livermore National Laboratory, 7000 East Avenue, Livermore, CA 94550, United States of America"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lawrence Livermore National Laboratory, 7000 East Avenue, Livermore, CA 94550, United States of America"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Lawrence Livermore National Laboratory, 7000 East Avenue, Livermore, CA 94550, United States of America;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Lawrence Livermore National Laboratory, 7000 East Avenue, Livermore, CA 94550, United States of America"}], "References": [{"Title": "VoroCrust", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Simulation-driven optimization of high-order meshes in ALE hydrodynamics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "208", "Issue": "", "Page": "104602", "JournalTitle": "Computers & Fluids"}, {"Title": "Scalability of high-performance PDE solvers", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "562", "JournalTitle": "The International Journal of High Performance Computing Applications"}, {"Title": "MFEM: A modular finite element methods library", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "42", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "Efficient exascale discretizations: High-order finite element methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "6", "Page": "527", "JournalTitle": "The International Journal of High Performance Computing Applications"}, {"Title": "Automatic Penalty and Degree Continuation for Parallel Pre-Conditioned <PERSON><PERSON> on Virtual Geometry", "Authors": "<PERSON><PERSON>; <PERSON>evi <PERSON>", "PubYear": 2022, "Volume": "146", "Issue": "", "Page": "103208", "JournalTitle": "Computer-Aided Design"}, {"Title": "Automatic Penalty and Degree Continuation for Parallel Pre-Conditioned <PERSON><PERSON> on Virtual Geometry", "Authors": "<PERSON><PERSON>; <PERSON>evi <PERSON>", "PubYear": 2022, "Volume": "146", "Issue": "", "Page": "103208", "JournalTitle": "Computer-Aided Design"}]}, {"ArticleId": 106370367, "Title": "An Effective and Adaptable K-means Algorithm for Big Data Cluster Analysis", "Abstract": "Tradition K -means clustering algorithm is easy to fall into local optimum, poor clustering effect on large capacity data and uneven distribution of clustering centroids. To solve these problems, a novel k -means clustering algorithm based on Lévy flight trajectory (Lk-means) is proposed in the paper. In the iterative process of LK-means algorithm, Lévy flight is used to search new positions to avoid premature convergence in clustering. It is also applied to increase the diversity of the cluster, strengthen the global search ability of K -means algorithm, and avoid falling into the local optimal value too early. Nevertheless, the complexity of hybrid algorithm is not increased in the process of Lévy flight optimization. To verify the data clustering effect of LK-means algorithm, experiments are conducted to compare it with the k -means algorithm, XK-means algorithm, DDKmeans algorithm and Canopyk-means algorithm on 10 open source data sets. The results show that LK-means algorithm has better search results and more evenly distributed cluster centroids, which greatly improves the global search ability, big data processing ability and uneven distribution centroids of cluster of K -means algorithm. Introduction In the era of big data, hundreds of millions of data is generated every day. How to effectively manage the accurate clustering of the generated data is crucial, and therefore an effective clustering algorithm can be well applied to enhance big data analysis. At present, a variety of optimized clustering algorithms emerge in endlessly, and the application of clustering algorithm has never stopped. For example, clustering algorithm is applied to graph processing [1], pattern recognition [2], spectral density analysis [3], wind farm data integration [4], climate prediction [5], and so on. The main function of clustering analysis is to aggregate the data with high similarity and separate the data with low similarity, which is called data structure splitting. The primary task of data clustering research is to divide the unlabeled N × D data set ( N is the number of samples, D is the data dimension) into k groups according to the similarity, which can be well handled in general algorithms for low dimensional data sets. As a common unsupervised machine learning algorithm, clustering is widely used in data mining and data analysis. However, it is difficult for traditional methods to efficiently classify large capacity data and to implement in high-dimensional data sets. Therefore, many researchers tend to focus on designing a clustering algorithm with higher stability and smaller deviation. Although clustering algorithm has been developed for decades, K -means algorithm is still widely used due to its simple principle, convenience and high efficiency. For example, Ran X et al proposed an artificial constrained k -means algorithm for GPS automatic lane detection [6], as well as processing data of virtual driving scene [7]. However, initial k -means algorithm has a big problem, i.e., being easy to converge prematurely and then falling into local optimum. To solve the problem, k -means algorithm has been continuously optimized by combining it with other algorithm. sunch as, the hybrid algorithm of particle swarm optimization (PSO) and CNAK [8], [9], Cuckoo algorithm (CS), Particle Swarm Optimization (PSO) and K -means (HCSPSO) [10]. In addition, there are some k -means optimization and application studies, for example, Ghadiri et al. [11] proposed a stable deep k -means clustering algorithm by combining implicit low-level attribute representations. Song et al. [12] used a bilateral weighted optimization k -means clustering algorithm and applied the algorithm to copolymerization and fast spectral clustering studies. Zhu et al. [13] used similarity matrix, spectral representation and transformation matrix mind for joint embedding to optimize k -means and applied it in spectral analysis. Nie et al. [14] clustered the rows and columns of the data matrix separately and proposed a collaborative clustering heart method FMVBK. Kang et al. [15] optimized from global and local structures and proposed a graph learning based k -means optimization method to preserve the integrity of the data. Ma et al. [16] used deep random forest algorithm for k -means and proposed a discrete k -means clustering algorithm and applied it to the study of price prediction. Zhao et al. [17] used Multi-View Visual Words (MVVW) to fuse feature information in the feature matrix and proposed a joint transfer constrained k -means clustering method. However, compared with the original K -means algorithm, optimized algorithm’s complexity, space and computation are increased. The others are to enhance the algorithm’s search ability, for example, Firefly Algorithm (FA) from the inside out enhanced search and composite enhanced search [18] are adopted, or brain storm optimization algorithm and K -means algorithm are combined to enhance the global search ability [19]. Another form is partition iteration, a hybrid approach which can speed up the k -means clustering proposed by Borlea et al. [20]. To speed up the clustering and avoid local optimization, the data can be divided into clusters of different sizes, but the complexity and memory cost are greatly increased. Therefore, a novel clustering algorithm based on the position of cuckoo algorithm and Lévy flight trajectory is proposed in the paper, which is shortly called LK-means. The algorithm was inspired by the XK-means algorithm proposed by Yaying [21]. An exploratory vector was added to the vector of the cluster centroid, so as to jump out of the local optimum in the iterative process. At present, the optimization of K -means algorithm mainly focuses on two directions [22], one is the optimization of the initial cluster centroid k , the other is the optimization of the updated iterative process. The paper will mainly study the centroid iterative update, based on XK-means clustering algorithm, Lévy flight trajectory was added to the search vector to improve the flight trajectory and optimize the K -means clustering algorithm. Levi’s flight step size satisfies a heavy tailed Levi’s stable distribution, which is a random walk mode that follows Levi’s distribution and alternates short distance long distance with occasionally. After several iterations, the distance of random walk tends to be stable. The use of Lévy flight step size enlarges the search range, so that the k -means algorithm can jump out of the local optimum and avoid falling into the local optimum. The small step length in the later stage can make the group converge the global optimal solution in a small range. To validate the clustering efficiency of LK-means, multiple benchmark models are used for comparative analysis to evaluate the advantages of the algorithm in terms of both functionality and different data sets. Our main contributions are as follows: 1. We propose the LK-means clustering algorithm, which has a higher search capability of the clustering algorithm and guarantees the convergence speed of the algorithm. 2. Based on four benchmark algorithms, we applied LK-means to 10 datasets for comparative analysis of clustering effects. The experimental results show that our algorithms show higher accuracy and better clustering centroids. 3. Experimental analysis of the influence factors in the LK-means algorithm is performed to derive the optimal influence factor. The remainder of this paper is structured as follows. Section 2 introduces the related work. Section 3 presents our approach. Section 4 provides our experimental results, which includes three parts: clustering accuracy, clustering centroid and optimal parameter v value. Finally, Section 5 concludes our work. Section snippets K-means clustering algorithm K -means clustering is a vector quantization method derived from signal processing. The goal of K -means clustering is to divide n data into k classes, where each data belongs to the nearest cluster center, as the cluster center. For example, given a set of observations ( x 1 , x 2 , ..., x n ), where each observation is a d -dimensional real vector, k -means clustering aims to partition the n observations into k ( < = n ) sets S = S 1 , S 2 , ..., S k so as to minimize the Within-Cluster Sum of Squares (WCSS) LK-means clustering algorithm Inspired by the XK-means clustering algorithm, a clustering algorithm based on Lévy flying and K -means (LK-means) is proposed to enhance the global exploration of random vectors. The Lévy flight path maximizes the diversity of search agents, which effectively increases the diversity of random search vectors. When the cluster centroid is updated, the Lévy flight trajectory is used to generate the random search vector, which can be expressed by the Eq. (8). θ j = r a n d ( a i , b i ) × μ s i g n [ r a n d − 1 2 ] ⊗ L e v y , i = 1 , 2 , Evaluation index To verify the effectiveness of the proposed algorithm on cluster analysis, four indicators are used to evaluate the algorithm. Mean square error (MSE) [26], Xie Beni index (XB) [27], Davies bouldin index (DB) [28] and Separation index (S) [29] respectively. Specifically as Eqs. (12)–(18). (1) Mean square error (MSE) (as Eq. (12)) M S E = 1 N ∑ j = 1 k ∑ i = 1 N ∥ x i − D j ∥ 2 (2) Xie–Beni index (XB) (as Eq. (13)) X B = M S E d min where d min is the smallest distance between cluster centroids. The larger d min , the better clustering Conclusion To improve the clustering effect of k-mean algorithm, a new hybrid algorithm LK-means for data clustering is proposed in this paper. LK-means is based on Lévy flight and k -means algorithm, and the k -means clustering algorithm is optimized using Lévy flight to improve the k -means clustering effect. In conclusion, the proposed LK-means algorithm can effectively improve the effect of data clustering and has a better iteration speed. LK-means can effectively cluster the data, so it can be applied to Declaration of Competing Interest Authors declare that they have no conflict of interest. Acknowledgment This work was supported by the Natural Science Foundation of China , under grants 61866013 and 61503152 , and the Education Department Key Foundation of Hunan Province in China, under grants 17A173, and the Education Department Foundation of Hunan Province in China, under grants 18C0565. Hu Haize obtained his bachelor’s degree from Hunan Institute of engineering and master’s degree from Changsha University of technology in 2013 and 2016 respectively. After graduation, he has been engaged in teaching in Jishou University for four years and is now studying for a doctor’s degree from Hunan University of science and technology. References (34) R.J. Cho et al. A genome-wide transcriptional analysis of the mitotic cell cycle Mol. Cell (1998) B.M. Ismail et al. Cuckoo inspired fast search algorithm for fractal image encoding J. King Saud University-Computer Inf. Sci. (2018) I.D. Borlea et al. A unified form of fuzzy c-means and k -means algorithms and its partitional implementation Knowledge-Based Syst. (2021) H. Xie et al. Improving k -means clustering with enhanced firefly algorithms Appl. Soft Comput. (2019) C. Zhao et al. Similarity learning with joint transfer constraints for person re-identification Pattern Recognit. (2020) C. Ma et al. Cost-sensitive deep forest for price prediction Pattern Recognit. (2020) Z. Kang et al. Structured graph learning for clustering and semi-supervised classification Pattern Recognit. (2021) F. Nie et al. Auto-weighted multi-view co-clustering via fast matrix factorization Pattern Recognit. (2020) X. Zhu et al. Spectral rotation for deep one-step clustering Pattern Recognit. (2020) K. Song et al. Weighted bilateral k -means algorithm for fast co-clustering and fast spectral clustering Pattern Recognit. (2021) J. Saha et al. Cnak: cluster number assisted k -means Pattern Recognit. (2021) A. Bouyer et al. An efficient hybrid clustering method based on improved cuckoo optimization and modified particle swarm optimization algorithms Appl. Soft Comput. (2018) L. Song et al. Graphr: accelerating graph processing using reRAM 2018 IEEE International Symposium on High Performance Computer Architecture (HPCA) (2018) M. Nedyalkova et al. Combinatorial k -means clustering as a machine learning tool applied to diabetes mellitus type 2 Int. J. Environ. Res. Public Health (2021) O. Vaulina et al. Spectral and structural characteristics for cluster systems of charged Brownian particles J. Exp. Theor. Phys. (2018) O. Sadeghian et al. Data clustering-based approach for optimal capacitor allocation in distribution systems including wind farms IET Gener., Transm. Distrib. (2019) N. Salehnia et al. Climate data clustering effects on arid and semi-arid rainfed wheat yield: a comparison of artificial intelligence and k -means approaches Int. J. Biometeorol. (2019) View more references Cited by (0) Recommended articles (0) Hu Haize obtained his bachelor’s degree from Hunan Institute of engineering and master’s degree from Changsha University of technology in 2013 and 2016 respectively. After graduation, he has been engaged in teaching in Jishou University for four years and is now studying for a doctor’s degree from Hunan University of science and technology. Jianxun Liu , professor and doctoral advisor, obtained his bachelor’s degree, master’s degree and doctor’s degree from Hunan Institute of engineering, Central South University and Shanghai Jiao Tong University in 1989, 1997 and 2003 respectively. Xiangping Zhang , received master’s degree and bachelor’s degree from Hunan University of science and technology in 2016 and 2019 respectively. He is now studying for a doctor’s degree in Hunan University of science and technology. His research interests include code representation and code clone detection. Mengge Fang , received master’s degree and bachelor’s degree from Changsha University of science and technology in 2020 and 2017 respectively. After graduation, she has been working in State Grid Hunan electric power company. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.109404", "PubYear": 2023, "Volume": "139", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Hu", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, Hunan 411100, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, Hunan 411100, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, Hunan 411100, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Yiyang Power Supply Company, Yiyang, Hunan 413000, China"}], "References": [{"Title": "Similarity learning with joint transfer constraints for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "107014", "JournalTitle": "Pattern Recognition"}, {"Title": "Spectral rotation for deep one-step clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107175", "JournalTitle": "Pattern Recognition"}, {"Title": "Auto-weighted multi-view co-clustering via fast matrix factorization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107207", "JournalTitle": "Pattern Recognition"}, {"Title": "Cost-sensitive deep forest for price prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107499", "JournalTitle": "Pattern Recognition"}, {"Title": "Weighted bilateral K-means algorithm for fast co-clustering and fast spectral clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107560", "JournalTitle": "Pattern Recognition"}, {"Title": "CNAK: Cluster number assisted K-means", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107625", "JournalTitle": "Pattern Recognition"}, {"Title": "Structured graph learning for clustering and semi-supervised classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107627", "JournalTitle": "Pattern Recognition"}, {"Title": "A Unified Form of Fuzzy C-Means and K-Means algorithms and its Partitional Implementation", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106731", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 106370425, "Title": "Cement kiln safety and performance improvement based on machine learning predictive analytics", "Abstract": "<p>Occupational health and safety has top priority within the cement industry. The preheating tower with its series of installed cyclones is essential in the cement kiln production process and it is considered among the most dangerous places in a cement plant. Coatings and blockages can often occur in the cyclone preheaters of rotary kiln plants for burning cement clinker. These wall build-ups disturb and/or block the process downward flow of hot kiln feed and the upward flow of hot kiln exhaust gases. Actually, our research aims to use process prediction by operating the digital transformation through a 4.0 tool for monitoring and analyzing temperature and pressure in real time. This tool monitors temperature and pressure using sensors that transform the data into a computer platform for real-time analysis and predicts failures according to a predictive model to prevent the occurrence of preheater cyclone blockages. This new technology will help to further improve occupational safety, increases the efficiency of industrial processes, and increases productivity.</p>", "Keywords": "Process prediction; Machine learning; Industry 4.0; Artificial intelligence; Python; Numpy; Scikit-learn; Artificial neural network; Regression; Predictive model; Database; Supervised learning; Unsupervised learning; Pandas; Data science", "DOI": "10.1007/s00170-023-10813-7", "PubYear": 2023, "Volume": "125", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratory of Innovation and Systems Engineering, Mechanical and Structural Engineering Department, ENSAM, Moulay Ismail University, Meknes, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Innovation and Systems Engineering, Mechanical and Structural Engineering Department, ENSAM, Moulay Ismail University, Meknes, Morocco"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory of Innovation and Systems Engineering, Mechanical and Structural Engineering Department, ENSAM, Moulay Ismail University, Meknes, Morocco"}], "References": [{"Title": "Development and application of ANN model for property prediction of supercritical kerosene", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "209", "Issue": "", "Page": "104665", "JournalTitle": "Computers & Fluids"}, {"Title": "Industry 4.0 tools in lean production: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "394", "JournalTitle": "Procedia Computer Science"}, {"Title": "Encoding resource experience for predictive process monitoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "153", "Issue": "", "Page": "113669", "JournalTitle": "Decision Support Systems"}, {"Title": "The impact of Industry 4.0 on bottleneck analysis in production and manufacturing: Current trends and future perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "174", "Issue": "", "Page": "108801", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 106370469, "Title": "Requirements for the software implementation of the Industrie 4.0 system for creating network enterprises", "Abstract": "", "Keywords": "", "DOI": "10.15827/0236-235X.140.557-571", "PubYear": 2022, "Volume": "25", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Denisov A.A.", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106370645, "Title": "An integrated CRITIC-VIKOR model for overtourism assessment in Spain: post-COVID-19 sustainable actions", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJMCDM.2022.128894", "PubYear": 2022, "Volume": "9", "Issue": "2", "JournalId": 9580, "JournalTitle": "International Journal of Multicriteria Decision Making", "ISSN": "2040-106X", "EISSN": "2040-1078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106370683, "Title": "Experimental Spectroscopic Data of SnO2 Films and Powder", "Abstract": "<p>Powders and films composed of tin dioxide (SnO2) are promising candidates for a variety of high-impact applications, and despite the material’s prevalence in such studies, it remains of high importance that commercially available materials meet the quality demands of the industries that these materials would most benefit. Imaging techniques, such as scanning electron microscopy (SEM), atomic force microscopy (AFM), were used in conjunction with Raman spectroscopy and X-ray photoelectron spectroscopy (XPS) to assess the quality of a variety of samples, such as powder and thin film on quartz with thicknesses of 41 nm, 78 nm, 97 nm, 373 nm, and 908 nm. In this study, the dependencies of the corresponding Raman, XPS, and SEM analysis results on properties of the samples, like the thickness and form (powder versus film) are determined. The outcomes achieved can be regarded as a guide for performing quality checks of such products, and as reference to evaluate commercially available samples.</p>", "Keywords": "", "DOI": "10.3390/data8020037", "PubYear": 2023, "Volume": "8", "Issue": "2", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics and Astronomy, Howard University, Washington, DC 20059, USA"}, {"AuthorId": 2, "Name": "Olasunbo Z. <PERSON>re", "Affiliation": "Department of Physics and Astronomy, Howard University, Washington, DC 20059, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA↑Theiss Research, La Jolla, CA 92037, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Physical Measurement Laboratory, National Institute of Standards and Technology, Gaithersburg, MD 20899, USA"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics and Astronomy, Howard University, Washington, DC 20059, USA; Corresponding author"}], "References": []}, {"ArticleId": 106370781, "Title": "Tailored Ti3C2 MXene/SnS2 nanocomposites to realize both sensitive photoelectrochemical determination and efficient photocatalytic detoxification of Cr(VI)", "Abstract": "We proposed a one-pot hydrothermal method for the synthesis of Ti<sub>3</sub>C<sub>2</sub> MXene/SnS<sub>2</sub> nanocomposites series and explored the possibility of utilizing them for sensitive photoelectrochemical (PEC) determination of Cr(VI) followed by efficient photocatalytic detoxification. Our research demonstrated that the resultant Ti<sub>3</sub>C<sub>2</sub> MXene/SnS<sub>2</sub> nanocomposites with 0.4 wt% of Ti<sub>3</sub>C<sub>2</sub> MXene (labeled as Ti<sub>3</sub>C<sub>2</sub> MXene<sub>0.4%</sub>/SnS<sub>2</sub>) possessed the most excellent PEC performance than the monomers (Ti<sub>3</sub>C<sub>2</sub> MXene and SnS<sub>2</sub> nanoparticles) and other Ti<sub>3</sub>C<sub>2</sub> MXene/SnS<sub>2</sub> nanocomposites with Ti<sub>3</sub>C<sub>2</sub> MXene content of 0.2 wt%, 0.8 wt%, or 1.4 wt%. The developed PEC sensor based on Ti<sub>3</sub>C<sub>2</sub> MXene<sub>0.4%</sub>/SnS<sub>2</sub> nanocomposites showed a good linear response to Cr(VI) in the concentration range of 1.0 pM∼0.1 mM with a low limit of detection of 0.51 pM (S/N = 3). And the Ti<sub>3</sub>C<sub>2</sub> MXene<sub>0.4%</sub>/SnS<sub>2</sub> nanocomposites exhibited the best photocatalytic detoxification efficiency for Cr(VI) after exposure to simulated sunlight for 1 h, reaching as high as 90.2%, whereas 56.9% and 51.7% detoxification efficiency was obtained for pure SnS<sub>2</sub> and Ti<sub>3</sub>C<sub>2</sub> MXene, respectively. Our study provides a new material having broad application prospects that are capable of scaled-up for developing online monitoring sensors for Cr(VI) determination as well as the photocatalytic reduction reactor for detoxification of Cr(VI) in various water matrices.", "Keywords": "Ti<sub>3</sub>C<sub>2</sub> MXene/SnS<sub>2</sub> nanocomposites ; Cr(VI) ; Photoelectrochemical determination ; Photocatalytic detoxification", "DOI": "10.1016/j.snb.2023.133496", "PubYear": 2023, "Volume": "382", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Jiangsu University, Zhenjiang 212013, PR China;Corresponding authors"}], "References": []}, {"ArticleId": 106371009, "Title": "COVID-19 Classification through Deep Learning Models with Three-Channel Grayscale CT Images", "Abstract": "<p>COVID-19, an infectious coronavirus disease, has triggered a pandemic that has claimed many lives. Clinical institutes have long considered computed tomography (CT) as an excellent and complementary screening method to reverse transcriptase-polymerase chain reaction (RT-PCR). Because of the limited dataset available on COVID-19, transfer learning-based models have become the go-to solutions for automatic COVID-19 detection. However, CT images are typically provided in grayscale, thus posing a challenge for automatic detection using pre-trained models, which were previously trained on RGB images. Several methods have been proposed in the literature for converting grayscale images to RGB (three-channel) images for use with pre-trained deep-learning models, such as pseudo-colorization, replication, and colorization. The most common method is replication, where the one-channel grayscale image is repeated in the three-channel image. While this technique is simple, it does not provide new information and can lead to poor performance due to redundant image features fed into the DL model. This study proposes a novel image pre-processing method for grayscale medical images that utilize Histogram Equalization (HE) and Contrast Limited Adaptive Histogram Equalization (CLAHE) to create a three-channel image representation that provides different information on each channel. The effectiveness of this method is evaluated using six other pre-trained models, including InceptionV3, MobileNet, ResNet50, VGG16, ViT-B16, and ViT-B32. The results show that the proposed image representation significantly improves the classification performance of the models, with the InceptionV3 model achieving an accuracy of 99.60% and a recall (also referred as sensitivity) of 99.59%. The proposed method addresses the limitation of using grayscale medical images for COVID-19 detection and can potentially improve the early detection and control of the disease. Additionally, the proposed method can be applied to other medical imaging tasks with a grayscale image input, thus making it a generalizable solution.</p>", "Keywords": "", "DOI": "10.3390/bdcc7010036", "PubYear": 2023, "Volume": "7", "Issue": "1", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia↑Data Technologies and Applications (DaTA) Research Group, Universiti Malaysia Sabah, Kota Kinabalu, Sabah 88400, Malaysia; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia↑Data Technologies and Applications (DaTA) Research Group, Universiti Malaysia Sabah, Kota Kinabalu, Sabah 88400, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia"}, {"AuthorId": 7, "Name": "Florence Sia", "Affiliation": "Faculty of Computing and Informatics, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Universiti Malaysia Sabah, Kota Kinabalu 88400, Malaysia"}], "References": [{"Title": "An optimized deep learning architecture for the diagnosis of COVID-19 disease based on gravitational search optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106742", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning approaches for COVID-19 detection based on chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114054", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A multi-task pipeline with specialized streams for classification and segmentation of infection manifestations in COVID-19 scans", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Automatic detection of COVID-19 from chest CT scan and chest X-Rays images using deep learning, transfer learning and stacking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "2", "Page": "2243", "JournalTitle": "Applied Intelligence"}, {"Title": "DenseNet Convolutional Neural Networks Application for Predicting COVID-19 Using CT Image", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "5", "Page": "389", "JournalTitle": "SN Computer Science"}, {"Title": "Fusion of Moment Invariant Method and Deep Learning Algorithm for COVID-19 Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "74", "JournalTitle": "Big Data and Cognitive Computing"}]}, {"ArticleId": 106371151, "Title": "Quality prediction of friction stir welded joint based on multiple regression: entropy generation analysis", "Abstract": "<p>Due to the outstanding advantages in light alloy material processing, friction stir welding technology is of great significance for obtaining high-quality welding products and accelerating aerospace lightweight. At present, most related studies predict joint quality before welding through computer technology. However, due to the complex thermo-mechanical coupling in the welding process, there may be a large deviation between the predicted results and the actual results, resulting in energy waste. In addition, these prediction models have poor real-time and versatility. Therefore, a new joint quality prediction method based on entropy production analysis is proposed in this paper. Firstly, based on non-equilibrium thermodynamics and extrusion theory, the entropy generation analysis model of the friction stir welding system is deduced. Using <PERSON><PERSON><PERSON><PERSON>’s Formula, the analytic solution of the entropy generation analysis model with unknown parameters is obtained. Secondly, combined with numerical simulation and multiple regression, the unknown parameters of the entropy generation analysis model are determined. Finally, multiple sets of welding experiments are designed to verify the effectiveness of the entropy generation analysis model. The welding process is analyzed by the proposed entropy generation analysis model to achieve the quality prediction of friction stir welded joint.</p>", "Keywords": "Friction stir welding; Joint quality; Multiple regression analysis; Entropy generation analysis", "DOI": "10.1007/s00170-023-10979-0", "PubYear": 2023, "Volume": "125", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao City, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Yanshan University, Qinhuangdao City, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei University of Environmental Engineering, Qinhuangdao City, China"}, {"AuthorId": 4, "Name": "Tao Kong", "Affiliation": "Hebei University of Environmental Engineering, Qinhuangdao City, China"}, {"AuthorId": 5, "Name": "Songtao Mi", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao City, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao City, China"}], "References": []}, {"ArticleId": 106371164, "Title": "THOSE WHO WOULD LEAD…", "Abstract": "<p>This edition of the Buckingham Journal is focussed on Leadership and Management. Whilst curating this edition I have been struck by the realisation that this edition could have been focussed on Change Management. All of the contributors to this edition of the Buckingham Journal are either reporting on or observing seismic changes in the way schools are organised, led and managed. Sometimes change is strategic and well-planned at other times change is chaotic and a response to a situation. I recall in the early 2000s whilst working in school senior leadership I said that I would vote for any party who promised to leave education alone for five years, that teachers would make any system however imperfect work. At the time we had revised OFSTED frameworks, SEAL, ECM, new vocational qualifications, Leadership Incentive Grant, new progress measures and Building Schools for the Future.</p>", "Keywords": "", "DOI": "10.5750/tbje.v3i2.2095", "PubYear": 2023, "Volume": "3", "Issue": "2", "JournalId": 79347, "JournalTitle": "The Buckingham Journal of Education", "ISSN": "2633-4909", "EISSN": "2633-4933", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Open data for assessing habitats degree of conservation at plot level. An example dataset of forest structural attributes in Val d'Agri (Basilicata, Southern Italy)", "Abstract": "Forests supply multiple ecosystem services and host a large proportion of the Earth&#x27;s terrestrial biodiversity. In particular, they provide habitats for many taxonomic groups which can be threatened by forest unsustainable management practices. Type and intensity of forest management are widely recognized as the main drivers of structure and functions in forests ecosystems. However, to better understand the impacts and the benefits deriving from forest management, there is a big need to standardize procedures of field data collection and data analysis. Here, we provide a georeferenced dataset of vertical and horizontal structure of forest types belonging to 4 habitat types, sensu Council Directive 92/43/EEC. The dataset includes structural indicators commonly linked to old-growth forests in Europe, in particular the amount of standing and lying deadwood. We collected data on 32 plots (24 of 225 m<sup>2</sup>, and 8 of 100 m<sup>2</sup>, according to different forests type) during spring and summer of 2022, in Val d&#x27;Agri (Basilicata, Southern Italy). The dataset we provide follows the common national standard for field data collection in forest habitat types, published by ISPRA in 2016 with the aim to promote a greater homogeneity in assessment of habitat conservation status at Country and biogeographical level, as requested by the Habitats Directive.", "Keywords": "Ecosystems assessment;Field survey;Habitat monitoring;Habitats directive;Sustainable forests management", "DOI": "10.1016/j.dib.2023.108986", "PubYear": 2023, "Volume": "47", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Italian Institute for Environmental Protection and Research (ISPRA), Via Vitaliano Brancati, 48, Roma 00144, Italy."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Italian Institute for Environmental Protection and Research (ISPRA), Via Vitaliano Brancati, 48, Roma 00144, Italy."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Regional Agency for the Protection of the Environment of Basilicata (ARPAB), Via della Fisica 18 C/D - via della Chimica 103, Potenza 85100, Italy."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Italian Institute for Environmental Protection and Research (ISPRA), Via Vitaliano Brancati, 48, Roma 00144, Italy."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Italian Institute for Environmental Protection and Research (ISPRA), Via Vitaliano Brancati, 48, Roma 00144, Italy."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Regional Agency for the Protection of the Environment of Basilicata (ARPAB), Via della Fisica 18 C/D - via della Chimica 103, Potenza 85100, Italy."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Regional Agency for the Protection of the Environment of Basilicata (ARPAB), Via della Fisica 18 C/D - via della Chimica 103, Potenza 85100, Italy."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Italian Institute for Environmental Protection and Research (ISPRA), Via Vitaliano Brancati, 48, Roma 00144, Italy."}], "References": []}, {"ArticleId": 106371253, "Title": "Management and sustenance of digital transformations in the Irish microbusiness sector: examining the key role of microbusiness owner-manager", "Abstract": "Despite the economic and societal significance of microbusinesses (MBs), digital transformation (DT) efforts in the MB sector have been rather sporadic. Further, prior DT studies have primarily examined large- and mid-sized organisations, leaving a perceptible void in the literature. In this paper, we leverage the unique context of MBs and recognise the key role of microbusiness owner-managers (MBOMs) for the management and sustenance of DT initiatives. Specifically, we theorise the influence of MBOMs’ DT readiness in terms of their growth and technology mindsets contributing to their DT learning resources and processes. Drawing on qualitative data from a series of structured interviews and focus groups with MBOMs and other key stakeholders in the Irish MB digital ecosystem, we identity three MBOM digital transformer archetypes comprising unique configurations of MBOMs’ growth and technology mindsets, namely: champion digital transformers, emerging digital transformers, and aspiring digital transformers. For each of these archetypes, we explore the different learning capabilities and mechanisms through which MBOMs manage and sustain their digital transformation efforts. Our findings offer theoretical contributions to the fields of digital transformation in microbusinesses, digital leadership, and digital capabilities. Our study also has significant implications for policy and practice.", "Keywords": "Digital transformation ; mindset ; microbusiness ; readiness ; owner-manager ; learning ; digital resilience ; digital inclusion policy ; digital transformation practices", "DOI": "10.1080/0960085X.2023.2166431", "PubYear": 2023, "Volume": "32", "Issue": "3", "JournalId": 3498, "JournalTitle": "European Journal of Information Systems", "ISSN": "0960-085X", "EISSN": "1476-9344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, Univ Evry, IMT-BS, LITEM, Evry-Courcouronnes, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Systems & Operations Management, HEC Paris, Jouy and en Josas Cedex, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business and Management, Royal Holloway University of London, Egham, UK; Norwegian University of Science and Technology, Trondheim, Norway"}], "References": [{"Title": "Digital transformation in family-owned Mittelstand firms: A dynamic capabilities perspective", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "6", "Page": "676", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Uncovering a new form of digitally-enabled agility: an improvisational perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "6", "Page": "681", "JournalTitle": "European Journal of Information Systems"}]}, {"ArticleId": 106371335, "Title": "Spatial and time characteristics of a four-wave radiation converter in a parabolic waveguide with resonant nonlinearity", "Abstract": "Spatial and temporal characteristics of a degenerate four-wave converter in a multimode waveguide with resonant nonlinearity in a scheme with counter-pumping waves are analyzed using the time response function and the point spread function. For single-mode pump waves with equal mode numbers, the dependences of the time response width on the waveguide length, the intensity of the first pump waves, and the mode number in the mode expansion of the object wave amplitude are obtained for the four-wave converter. The greatest contribution to the object wave amplitude is shown to be from the waveguide mode whose number coincides with the mode number of single-mode pump waves. For the stationary model, taking into account the spatial structure of the Gaussian pump wave leads to a monotonous decrease with a decrease in the pump beam width, followed by a constant value of the PSF module width. With single-mode pump waves with equal mode numbers, An increase in the mode number of the pump waves leads to a redistribution of energy concentrated in the side maxima of the point signal image and improvement in the quality of the wavefront reversal for a model with single-mode pump waves with equal mode numbers.", "Keywords": "four-wave converter of radiation; parabolic waveguide; point spread function; resonant nonlinearity; time response", "DOI": "10.18287/2412-6179-CO-1199", "PubYear": 2023, "Volume": "47", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University"}], "References": []}, {"ArticleId": 106371357, "Title": "VCX Version 2023 The latest transparent and objective mobile phone test scheme", "Abstract": "VCX or Valued Camera eXperience is a nonprofit organization dedicated to the objective and transparent evaluation of mobile phone cameras. The members continuously work on the development of a test scheme that can provide an objective score for the camera performance. Every device is tested for a variety of image quality factors while these typically based on existing standards. This paper presents that latest development with the newly released version 2023 and the process behind it. New metric included are extended tests on video dynamics, AE and AWB, dedicated tests on ultra wide modules and adjustments to the metric system based on a large scale subjective study.", "Keywords": "Image Quality;System performance;VCX;Mobile Phone;Objective Test", "DOI": "10.2352/EI.2023.35.8.IQSP-317", "PubYear": 2023, "Volume": "35", "Issue": "8", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106371548, "Title": "A Bilinear Pseudo-spectral Method for Solving Two-asset European and American Pricing Options", "Abstract": "<p>This paper presents a bilinear Chebyshev pseudo-spectral method to compute European and American option prices under the two-asset Black–<PERSON> and Heston models. We expand a function and its derivatives into their Chebyshev series, so the differentiation matrices that act on the Chebyshev coefficients are sparse and better conditioned. First, the equation is spatially discretized using a bilinear pseudo-spectral method created by Chebyshev polynomials and a first–order matrix differential equation (MDE) is obtained. Then, using the Kronecker product, this equation is converted to a system of first–order ODEs. For solving the European options, by using an eigenvalue decomposition method, the arising system will be analytically solved. Therefore, the arising errors are because of spatial discretization and quadrature errors. For solving the American options, the approach is combined with the operator splitting method or penalty method to obtain the temporal discretization. By avoiding some transforms to convert the equation to a constant coefficient equation without mixed derivatives, we will obtain more accurate solutions. Also, transforming the European option into a set of separated ODEs by an eigenvalue decomposition causes to reduce the computational complexity. We also consider the hedge ratios which show the sensitivity of an option to the stock prices. Several numerical examples are included to show the accuracy and efficiency of the proposed approach. The results show that the spectral convergence can be achieved for models with smooth functions.</p>", "Keywords": "Two-dimensional Black<PERSON> equation; <PERSON><PERSON>s model; Options pricing; Bilinear pseudo-spectral method; Kronecker product", "DOI": "10.1007/s10614-023-10364-9", "PubYear": 2024, "Volume": "63", "Issue": "2", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Iran University of Science and Technology, Tehran, Iran; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Iran University of Science and Technology, Tehran, Iran"}], "References": [{"Title": "A Computational Method Based on the Moving Least-Squares Approach for Pricing Double Barrier Options in a Time-Fractional Black–Scholes Model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "1", "Page": "119", "JournalTitle": "Computational Economics"}, {"Title": "An RBF-FD sparse scheme to simulate high-dimensional Black–<PERSON> partial differential equations", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "2", "Page": "426", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 106371562, "Title": "Blockchain Data Availability Scheme with Strong Data Privacy Protection", "Abstract": "<p>Blockchain, with its characteristics of non-tamperability and decentralization, has had a profound impact on various fields of society and has set off a boom in the research and application of blockchain technology. However, blockchain technology faces the problem of data availability attacks during its application, which greatly limits the scope and domain of blockchain applications. One of the most advantageous researches to address this problem is the scalable data availability solution that integrates coding theory design into the Merkle tree promise. Based on this scheme, this paper combines a zero-knowledge accumulator with higher efficiency and security with local repair coding, and proposes a data availability scheme with strong dataset privacy protection. The scheme first encodes the data block information on the blockchain to ensure tamper-proof data, and then uses a zero-knowledge accumulator to store the encoded data block information. Its main purpose is to use zero-knowledge property to protect the accumulation set information stored in the accumulator from being leaked and to ensure that no other information about the accumulation set is revealed during the data transmission. It fundamentally reduces the possibility of attackers generating fraudulent information by imitating block data and further resists data availability attacks.</p>", "Keywords": "", "DOI": "10.3390/info14020088", "PubYear": 2023, "Volume": "14", "Issue": "2", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Research Center of Digital Forensics, Ministry of Education, School of Computer Science, Nanjing University of Information Science and Technology, Nanjing 210044, China"}, {"AuthorId": 2, "Name": "Shan Ji", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Digital Arts, Xi’an University of Posts & Telecommunications, Xi’an 710061, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of Digital Forensics, Ministry of Education, School of Computer Science, Nanjing University of Information Science and Technology, Nanjing 210044, China"}], "References": [{"Title": "Integrity Verification Mechanism of Sensor Data Based on Bilinear Map Accumulator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Multiple cloud storage mechanism based on blockchain in smart homes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "304", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "BSMD:A blockchain-based secure storage mechanism for big spatio-temporal data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "328", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 106371577, "Title": "Evaluation and analysis of teaching quality of university teachers using machine learning algorithms", "Abstract": "<p>In order to better improve the teaching quality of university teachers, an effective method should be adopted for evaluation and analysis. This work studied the machine learning algorithms and selected the support vector machine (SVM) algorithm to evaluate teaching quality. First, the principles of selecting evaluation indexes were briefly introduced, and 16 evaluation indexes were selected from different aspects. Then, the SVM algorithm was used for evaluation. A genetic algorithm (GA)-SVM algorithm was designed and experimentally analyzed. It was found that the training time and testing time of the GA-SVM algorithm were 23.21 and 7.25 ms, both of which were shorter than the SVM algorithm. In the evaluation of teaching quality, the evaluation value of the GA-SVM algorithm was closer to the actual value, indicating that the evaluation result was more accurate. The average accuracy of the GA-SVM algorithm was 11.64% higher than that of the SVM algorithm (98.36 vs 86.72%). The experimental results verify that the GA-SVM algorithm can have a good application in evaluating and analyzing teaching quality in universities with its advantages in efficiency and accuracy.</p>", "Keywords": "", "DOI": "10.1515/jisys-2022-0204", "PubYear": 2023, "Volume": "32", "Issue": "1", "JournalId": 7501, "JournalTitle": "Journal of Intelligent Systems", "ISSN": "0334-1860", "EISSN": "2191-026X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Office of Academic Affairs and Research Administration, Guilin University , No. 3, Yanzhong Road, Yanshan District , Guilin, Guangxi 541006 , China"}], "References": [{"Title": "Genetic Algorithm Coupled with the Krawczyk Method for Multi-Objective Design Parameters Optimization of the 3-UPU Manipulator", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "6", "Page": "1138", "JournalTitle": "Robotica"}, {"Title": "Decision Tree SVM: An extension of linear SVM for non-linear classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "401", "Issue": "", "Page": "153", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 106371811, "Title": "A new BRB model for technical analysis of the stock market", "Abstract": "To predict the trend of stock prices, a belief rule base (BRB) assessment model based on different technical indicators is proposed in this paper. The proposed BRB-based model includes three BRBs, denoted as BRB_1, BRB_2 and BRB_3. BRB_1 is used to capture the relationship between the price trend of the moving average (MA) and buy/sell decisions. BRB_2 is used to investigate the conditions of moving average convergence and divergence (MACD). BRB_3 is employed to represent the stochastic indicator (KD) states. The above three indicators are commonly used in stock analysis, and they usually need to be used together to achieve a more accurate analysis of the stock price trend. In the BRB model, the initial values of some parameters are provided by experts to construct the elementary algorithm logic, but these are unlikely to result in an accurate assessment. Therefore, on the basis of the maximum likelihood (ML) algorithm, an optimal algorithm for training the parameters of the assessment model is further proposed. Taking the trend of the Chinese stock market as the research object, an average MSE of 0.3242 is obtained using this model. The results indicate the potential application of the proposed model in the financial industry.", "Keywords": "Moving average ; Moving average convergence and divergence ; Stochastic indicator ; Belief rule base (BRB) ; Chinese stock market", "DOI": "10.1016/j.iswa.2023.200198", "PubYear": 2023, "Volume": "18", "Issue": "", "JournalId": 89889, "JournalTitle": "Intelligent Systems with Applications", "ISSN": "2667-3053", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Advanced Materials and Technology, University of Science and Technology Beijing, Beijing 10083, China"}, {"AuthorId": 2, "Name": "Jiabing Wu", "Affiliation": "School of Software and Microelectronics, Peking University, Beijing 10086, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "High-Tech Institute of Xi'an, Xi'an 710025, China"}, {"AuthorId": 4, "Name": "Guanyu Hu", "Affiliation": "School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, Guangxi 541004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Engineering, Harbin Normal University, Harbin 150025, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "High-Tech Institute of Xi'an, Xi'an 710025, China;School of Computer Science and Information Engineering, Harbin Normal University, Harbin 150025, China;Corresponding author"}], "References": [{"Title": "A systematic review of fundamental and technical analysis of stock market predictions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "3007", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Tree aggregation for random forest class probability estimation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "2", "Page": "134", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}, {"Title": "An Enhanced Extreme Learning Machine Based on Liu Regression", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "421", "JournalTitle": "Neural Processing Letters"}, {"Title": "Technical analysis strategy optimization using a machine learning approach in stock market indices", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "225", "Issue": "", "Page": "107119", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep Neural Network and Time Series Approach for Finance Systems", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "204", "JournalTitle": "Journal of Organizational and End User Computing"}]}, {"ArticleId": 106371847, "Title": "On using a Particle Image Velocimetry based approach for candidate nodule detection", "Abstract": "<p>Detection and segmentation of candidate lung nodules from diagnostic images are vital steps in any image processing-based Computer-Aided Diagnostic (CAD) system for lung cancer. Computed Tomography (CT) is a commonly used modality for lung cancer screening due to the tissue contrast and anatomical resolution. This work aims to investigate the effectiveness of Particle Image Velocimetry, PIV, as a preprocessing tool for processing the input data frames. This is done by applying PIV processing to the input images and quantifying the nodules detected over a morphology-based image processing pipeline. Further, PIV processed images and images without PIV processing were input to the Convolution-based deep learning framework, and the candidate nodule detection effect was quantified and compared. The results validate the efficacy of the proposed workflow for candidate nodule detection both in the image processing pipeline and in the deep learning-based framework. Further, the work also presents the utility of the proposed preprocessing scheme through its ability to detect candidate nodules comprising the major nodule types, namely juxta-pleural, juxta-vascular, isolated, and ground-glass opacity nodules.</p>", "Keywords": "Particle Image Velocimetry; Candidate nodule detection; Computed Tomography; Dicom slices", "DOI": "10.1007/s11042-023-14493-z", "PubYear": 2023, "Volume": "82", "Issue": "15", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ABV-IIITM, Gwalior, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "RGPV, Bhopal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ABV-IIITM, Gwalior, India"}, {"AuthorId": 4, "Name": "Joydip <PERSON>har", "Affiliation": "ABV-IIITM, Gwalior, India"}], "References": []}, {"ArticleId": 106371879, "Title": "Classification, detection and sentiment analysis using machine learning over next generation communication platforms", "Abstract": "Today, digital news serve a vital function in society by offering a source of news and information to the general public. They serve as a means of communication and can help to educate and inform people about current events and issues. Additionally, newspapers can provide a platform for diverse perspectives and opinions, which can help to promote democracy and freedom of expression. Since a large section of the public now turns to internet sources rather than traditional print or broadcast media for news and information, the trend of digital news has gained major support. There are several issues that are commonly associated with digital news. One of the main issues is the proliferation of fake news and misinformation. Additionally, digital news is often subject to algorithmically driven personalization, which can lead to a lack of diversity and balance in the information that is presented to people. In this research, we provide an integrated strategy with three key aspects to process and analyse the growing digital news articles. The news data is collected, analysed and classified into different categories using machine learning algorithms. Our news classification results demonstrated that Crime, Cure and Treatment, Economy, Communal, and Entertainment are widely reported news category themes across India. Our Fake News Detection Model achieved 87% accuracy and analysis showed that communal theme is most affected in India. Further sentiment analysis model classified news articles into positive and negative news articles and achieved 89% accuracy. Introduction The next generation of communication platforms is likely to include a wide range of new and emerging technologies. The internet of things, virtual and augmented reality, 5 G networks, and artificial intelligence are a few examples of these technologies [1]. The way we communicate with and relate to one another, as well as how we engage with the environment around us, could be completely transformed by these technologies. For example, virtual and augmented reality could enable us to experience and explore new environments and spaces in ways that were previously impossible. Artificial intelligence could help us to analyse and process vast amounts of data more quickly and accurately. The internet of things could connect more devices and objects to the internet, enabling them to communicate and exchange data with each other. And 5 G networks could provide faster, more reliable, and more secure communication capabilities. The next generation of communication platforms is likely to have a significant impact on the way news is produced, distributed, and consumed. For example, new technologies like virtual and augmented reality could enable news organizations to create more immersive and interactive experiences for their audiences. Artificial intelligence could help news organizations to analyse and process large amounts of data more quickly and accurately, allowing them to produce more timely and relevant news stories [2]. The internet of things could enable news organizations to collect and share data from a wide range of sources, providing a more comprehensive and accurate picture of the events and issues they are reporting on. And 5 G networks could provide faster and more reliable communication, allowing news organizations to share their content with a wider audience in real-time [3]. In the digital age, news has become more accessible than ever before thanks to the internet and modern communication platforms like social media. Instead of having to wait for the morning newspaper or a nightly news broadcast, people can now get their news in real-time through a variety of online sources. This has led to an explosion in the amount of news available to the average person, and it has also made it easier for people to access news from a wider range of sources, including smaller, niche outlets. One of the challenges of reading news in the digital age is that it can often be unorganized and overwhelming. It can be challenging to know where to begin and what to read with so much information at our disposal. This can lead to information overload and make it hard for readers to find the news that is most relevant and important to them. To help combat this, many news organizations now use algorithms to personalize the news they provide to individual readers, offering a more curated and organized experience [4]. However, this approach is not without its own challenges and limitations. The issue of fake news is a major one in the journalistic industry. It describes the dissemination of incorrect or deceptive information, frequently with the goal of doing harm or influencing public opinion [5]. There is a great deal of worry about the effects of fake news on society since it is now simpler than ever to produce and disseminate it in the digital era. Fake news can come in a variety of formats, including falsified articles, edited images and videos, and even whole websites. It can be difficult to identify fake news, and it often relies on sensational or provocative headlines to grab people's attention and spread quickly. Many news organisations and technology firms have put mechanisms in place to better locate and battle fake news in reaction to its spread, but the issue still poses a significant barrier. News and sentiments are closely related, as news articles often express the opinions and emotions of the people involved in the events they describe. Sentiment analysis, a method used in natural language processing (NLP), aims to detect and extract subjective information from text, specifically the emotions conveyed in news articles [6,42,43]. This can be useful for understanding how people feel about a particular topic or event, and it can also help news organizations track public opinion on various issues over time. The overwhelming amount of information presents a challenge in sentiment analysis during the digital age of news. With so much news being produced and distributed online, it can be difficult for a sentiment analysis tool to keep up with the volume and variety of information. This can lead to incomplete or inaccurate analysis, and it can make it difficult to track trends and changes in sentiment over time. Another challenge is the increasing use of social media and other online platforms for the distribution of news. These platforms often have their own language and conventions, which can make it difficult for a sentiment analysis tool to accurately interpret the sentiment expressed in a text. In addition, the informal and often opinionated nature of social media posts can make it challenging to distinguish between factual statements and personal opinions, which can affect the accuracy of sentiment analysis. Overall, the digital age of news has brought many benefits, but it has also introduced new challenges for sentiment analysis. To overcome these challenges, it is important to use advanced machine learning algorithms and to carefully curate and validate the data used to train these algorithms. Natural Language Processing (NLP) and its many approaches have earned popularity to handle and assess huge volumes of natural language data. To allow machines to comprehend human or natural language, the fields of artificial intelligence (AI) and linguistics are combined in natural language processing [7]. We now create a lot of unstructured text data each day, which increases the necessity of NLP even more. Named entity recognition [8], Machine Translation [9], Text summarization [10], and Sentiment Analysis [11], are a few of the most widely used and well-liked NLP approaches. Any information that is accessible, collectable, and understandable will help people make the best judgments. Researchers may now use NLP to retrieve data from sources including social media, policy files, scientific literature, and news about subjects like patient demographics, national and international politics, and vaccine development [12]. The following are the primary contributions of the present work: • Classification of news dataset into several categories using ML algorithms. • Fake News Detection using the most crucial characteristics extracted from the news dataset. • Sentiment analysis of the news dataset to classify news articles as positive or negative. • Accuracies of 95.50% by Naïve Bayes for classification, 87.20% by Random Forest for Fake News Detection, and 89.30% by Naïve Bayes for Sentiment Analysis are achieved. The remaining article is structured as below: The Second section goes through the background information and motive for news classification, false news detection, and sentiment analysis. Section 3 discusses similar works concerning the above-mentioned three aspects. Section 4 outlines the proposed architecture and the machine learning techniques used in this work to identify and detect misinformation. Section 5 includes the experiment findings as well as the discussion. Section 6 concludes with a conclusion. Section snippets News classification We come across a lot of text every day in the realm of online news information. People are concerned about their busy lives as a result of information technology improvements; thus, they only want to read news stories that interest them [10]. Finding news that is relevant to a person's interests might be difficult because many news pieces are informative but may not be very important [35]. The interest of a person can vary depending on several aspects, including the news source and the nature Related work The fields of misinformation identification, news categorization, and sentiment analysis have all been greatly advanced by academics, however, integration of all three modules is surprisingly rare. In this section, we'll have a quick discussion of past work on news information. Text mining along with NLP is most frequently used to glean semantically pertinent information from massive amounts of multilingual news data obtained from different social media sites. Authors in [22] conducted mapping System architecture This section goes into great depth on the proposed work architecture. Fig. 2 depicts the phases and steps involved in architecture, and the following is a summary of those phases and steps: • The news dataset utilised in this study was extracted from fact-checking websites in India that are accredited by the IFCN, including altnews [49], The Quint [50], factly.in [51], boomlive [52], indiatoday [53], and newsmobile [54]. • The dataset is subjected to text pre-processing to remove null values, Experimental results Result analysis and validation of news processing models typically involves evaluating its performance using metrics such as accuracy, precision, recall, and F1 score. The accuracy of a model is a gauge of its general performance and is computed as the proportion of cases that are properly categorised to all occurrences. It provides a general understanding of the model's performance, but it can be misleading if the dataset is not balanced. It is commonly represented by the formula in Eq. (1): A c c Discussion The results of our study on News Classification, Fake News Detection and Sentiment Analysis provide valuable insights into the effectiveness of machine learning algorithms in processing and analysing news articles. Our results show that the proposed model outperforms other current models for the tasks of news classification, fake news detection, and sentiment analysis in terms of performance metrics. For instance, our model's validation accuracy in the field of fake news identification was 87%, Conclusion and future work The work has effectively shown that machine learning algorithms may be used for news classification, fake news detection, and sentiment analysis. The outcomes demonstrate that our proposed model is successful in achieving high accuracy rates in each of the three analysis-related areas. News Classification achieved 95.17% accuracy, Fake News Detection achieved 87% accuracy, and Sentiment Analysis achieved 89% accuracy. These results indicate that our models are capable of accurately processing Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements No funding has been received for this research. Jeelani Ahmed pursued Bachelor of Engineering and Master of Technology in Computer Science from Visvesvaraya Technical University Belagavi, India in 2012 and 2015. He is currently pursuing PhD from Maulana Azad National Urdu University, Hyderabad, India since 2017. His-main research work focuses on Big Data Analytics, Semantic Web, Network Security and Cloud Security. He has 6 years of teaching experience and 5 years of Research Experience. He has presented and published several research papers References (54) Ajeet Ram Pathak et al. Topic-level sentiment analysis of social media data using deep learning Appl. Soft Comput. (2021) Sanjeev. Verma Sentiment analysis of public services for smart society: literature review and future research directions Gov. Inf. Q. (2022) T. Ozturk et al. Automated detection of COVID-19 cases using deep neural networks with X-ray images Comput. Biol. Med. (Jun. 2020) F.A. Ozbay et al. Fake news detection within online social media using supervised artificial intelligence algorithms Physica A: Statistical Mech. its Appl. (Feb. 2020) J. Lei et al. Towards building a social emotion detection system for online news Future Generation Comput. Syst. (Jul. 2014) D. Bright et al. Using social network analysis to study crime: navigating the challenges of criminal justice records Soc. Networks (Jul. 2021) C.S. Atodiresei et al. Identifying fake news and fake users on Twitter Procedia Comput. Sci. (Jan. 2018) A. Husen et al. A survey on requirements of future intelligent networks: solutions and future research directions ACM Comput. Surv. (April 2023) Erik Dahlman et al. 5G NR: The next Generation Wireless Access Technology (2020) Ashish Padhy et al. A survey of energy and spectrum harvesting technologies and protocols for next generation wireless networks IEEE Access (2020) Fang Miao et al. Chinese news text classification based on machine learning algorithm S Ishfaq Manzoor et al. Fake news detection using machine learning approaches: a systematic review Dev S et al. Predicting the effects of news sentiments on the stock market K. Krishnan and S.P. Rogers, “Social data analytics : collaboration for the... D. Nadeau et al. A survey of named entity recognition and classification Undefined (Aug. 2007) Y. Wu et al., “Google's neural machine translation system: bridging the gap between human and machine translation,”... J.-Manuel. Torres-Moreno, “Automatic text summarization,” 2014, Accessed: Nov. 26, 2022. [Online]. Available:... V. Rao and J. Sachdev, “A machine learning approach to classify news articles based on location”, pp. 863–867, Jun.... “How natural language processing (NLP) can help us understand the landscape of COVID-19 information | copyright... J.L. Egelhofer and S. Lecheler, “Fake news as a two-dimensional phenomenon: a framework and research agenda,”... S. Mishra et al. Analyzing machine learning enabled fake news detection techniques for diversified datasets Wirel Commun. Mob. Comput. (2022) T.T. Aurpa et al. Abusive Bangla comments detection on Facebook using transformer-based deep learning models Soc. Network Anal. Mining 2021 (Dec. 2021) J.F. de Oliveira et al. Measuring the effects of repeated and diversified influence mechanism for information adoption on Twitter Soc. Network Anal. Mining 2021 (Dec. 2021) J. Reis et al. Breaking the news: first impressions matter on online news G. Sansonetti et al. Unreliable users detection in social media: deep learning techniques for automatic detection IEEE Access (2020) C. v. Meneses Silva, R. Silva Fontes, and M. Colaço Júnior, “Intelligent fake news detection: a systematic mapping,”... S. Bandyopadhyay and S. D.U.T.T.A., “Analysis of fake news in social medias for four months during lockdown in... View more references Cited by (0) Recommended articles (0) Jeelani Ahmed pursued Bachelor of Engineering and Master of Technology in Computer Science from Visvesvaraya Technical University Belagavi, India in 2012 and 2015. He is currently pursuing PhD from Maulana Azad National Urdu University, Hyderabad, India since 2017. His-main research work focuses on Big Data Analytics, Semantic Web, Network Security and Cloud Security. He has 6 years of teaching experience and 5 years of Research Experience. He has presented and published several research papers in national and international conferences and journals. Dr. Muqeem Ahmed working as an Assistant Professor at the Department of Computer Science and Information Technology Hyderabad (India). He received his doctoral degree in computer Science from Jamia Millia Islamia New Delhi India. His-professional experience spans over more than 14 years of teaching, research, and project supervision. He has supervised various students for interdisciplinary research and industrial projects. Over the years, he has published many research papers with national and international journals of repute. In addition to these, he is also in the Editorial Boards and Reviewers’ Panels of various journals. His-primary area of research focuses on semantic web applications, Distributed Database Machine learning and big data Analytics View full text © 2023 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.micpro.2023.104795", "PubYear": 2023, "Volume": "98", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Technology, <PERSON><PERSON><PERSON> National Urdu University, Hyderabad, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Technology, <PERSON><PERSON><PERSON> National Urdu University, Hyderabad, India"}], "References": [{"Title": "Topic-level sentiment analysis of social media data using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107440", "JournalTitle": "Applied Soft Computing"}, {"Title": "A survey on sentiment analysis methods, applications, and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5731", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A Survey on Requirements of Future Intelligent Networks: Solutions and Future Research Directions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": *********, "Title": "Are the motives of holding cash differing between developed and emerging financial markets?", "Abstract": "Purpose This study compares the motives of holding cash between developed (Australian) and developing (Malaysian) financial markets. Design/methodology/approach For the period 2006–2020, the t -test, fixed-effect and generalised method of moment (GMM) model have been applied to a sample of 1878 (1,165 Australian and 713 Malaysian) firms. Findings The empirical results reveal that firms in developed financial markets hold higher cash compared to the developing financial markets. The findings confirm that motives to hold cash differ between developed and developing financial markets. The GMM findings further show that cash holdings (CH) in Australia are higher due to higher ratios of cash flow, research and development (R&D) and return on assets (ROA), and lower due to larger dividend payments. In the Malaysian market, however, cash flows and R&D are ineffectual, ROA falls and dividend payments rise CH. Practical implications The study helps managers, practitioners and investors understand that firms' distinct economic, institutional, accounting and financial environments are important. To attain the desired outcomes, they must thus comprehend and consider these considerations while developing suitable liquidity strategies. Originality/value To the authors' best knowledge, this is the initial research demonstrating how varied cash motives and their ramifications are in developed and developing financial markets. Therefore, this study identifies the importance that CH motives varied among financial markets and that findings from a particular market cannot be generalised to other markets because of the market and financial structural variations.", "Keywords": "Financial determinants;Cash holdings (CH);Developed financial market;Developing financial market;G30;N20", "DOI": "10.1108/K-11-2022-1527", "PubYear": 2024, "Volume": "53", "Issue": "5", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Economics and Finance, College of Business Administration , University of Ha'il , Ha'il, Saudi Arabia"}], "References": []}, {"ArticleId": 106372070, "Title": "Natch: using virtual machine introspection and taint analysis for detection attack surface of the software", "Abstract": "Natch – это инструмент для получения поверхности атаки, то есть поиска исполняемых файлов, динамических библиотек, функций, отвечающих за обработку входных данных (файлов, сетевых пакетов) во время выполнения задачи. Функции из поверхности атаки могут быть причиной уязвимостей, поэтому им следует уделять повышенное внимание. В основе инструмента Natch лежат доработанные методы отслеживания помеченных данных и интроспекции виртуальных машин. Natch построен на базе полносистемного эмулятора QEMU, поэтому позволяет анализировать все компоненты системы, включая ядро ОС и драйверы. Собранные данные визуализируются в графическом интерфейсе SNatch, входящем в поставку инструмента. Построение поверхности атаки может быть встроено в CI/CD для интеграционного и системного тестирования. Уточненная поверхность атаки позволит поднять эффективность технологий функционального тестирования и фаззинга в жизненном цикле безопасного ПО.", "Keywords": "dynamic analysis;introspection;taint analysis;qemu;instrumentation;natch;динамический анализ;интроспекция;анализ помеченных данных;инструментирование", "DOI": "10.15514/ISPRAS-2022-34(5)-6", "PubYear": 2022, "Volume": "34", "Issue": "5", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН, Новгородский государственный университет имени Ярослава Мудрого"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}], "References": []}, {"ArticleId": 106372233, "Title": "All Sliders to the Right", "Abstract": "<p>There are many reasons why this year's model isn't any better than last year's, and many reasons why performance fails to scale, some of which KV has covered in these pages. It is true that the days of upgrading every year and getting a free performance boost are long gone, as we're not really getting single cores that are faster than about 4GHz. One thing that many software developers fail to understand is the hardware on which their software runs at a sufficiently deep level.</p>", "Keywords": "", "DOI": "10.1145/3580505", "PubYear": 2022, "Volume": "20", "Issue": "6", "JournalId": 22497, "JournalTitle": "Queue", "ISSN": "1542-7730", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106372269, "Title": "Towards a Fair Allocation and Effective Utilization of Resource Units in Multi-User WLANs-based OFDMA technology", "Abstract": "To promote the MU (Multi-User) communications in High-Efficiency WLANs (Wireless Local Area Networks), the new IEEE 802.11ax standard introduced the concept of RU (Resource Unit) by means of the OFDMA (Orthogonal Frequency Division Multiple Access) transmission technique. Although the OFDMA MU communications enable multiple users to be served at the same time in both DL (Down-Link) and UL (Up-Link) directions, several challenges remain to be met in order to design a MAC (Medium Access Control) protocol that fairly allocates and effectively utilizes the RUs. The challenges addressed are: (1) define when and how using 802.11ax control frames, (2) establish a fair sending policy of UL needs, (3) adopt a strategy to select a channel structure, and (4) avoid loss in spectral efficiency and timeout BA (Block Acknowledgment). In this paper, we proposed the FAEU-RUs protocol (Fair Allocation and Effective Utilization of RUs) by meeting each of the challenges of OFMDA MU communications. The performance evaluation demonstrates that the FAEU-RUs protocol significantly enhances the overall performance of the network but also the individual performance of each type of traffic (DL and UL) and each category of station. Introduction Today, the wireless connection interface Wi-Fi (Wireless-Fidelity) is omnipresent in smart digital devices, including: computing devices (mobile computers, personal digital assistants, etc.), telecommunication devices (mobile phones, televisions, etc.), multimedia devices (cameras, audio recorders, etc.), etc. Thereby, the number of connection demands has become exceedingly great, especially in dense and mobile environments such as big box stores, airports, stadiums, etc. [1]. Consequently, a tremendous overload is caused at the AP (Access Point), and a poor quality of service is left by the network STAs (Stations). Indeed, a Wi-Fi AP can usually enable only one STA at a time to communicate within a BSS (Basic Service Set), that is what we call SU (Single-User) communications. To intensify the deployment of WLANs (Wireless Local Area Networks), the IEEE 802.11ax standard known as Wi-Fi 6 is recently ratified to succeed the IEEE 802.11ac standard known as Wi-Fi 5 [2]. At the 802.11ax PHY (Physical) layer, an innovative concept called RU (Resource Unit) is introduced by means of OFDMA (Orthogonal Frequency Division Multiple Access) transmission technique, which splitting a conventional transmission channel into multiple non-overlapped RUs of different widths. The obtained RUs are operated by the MAC (Medium Access Control) layer to simultaneously serve multiple users (up to 9 within a 20 MHz channel), that is what we call MU (Multi-User) communications [3]. In particular, the OFDMA MU communications can be enabled in both DL (Down-Link) and UL (Up-Link) directions without interference while using the means available, such as: a single antenna, OFDM (Orthogonal Frequency Division Multiplexing) modulation technique, etc. The implementation of the OFDMA MU communications depends on the following key features provided in the specification of the IEEE 802.11ax standard: (1) OFDMA standard control frames, namely: BSRP (Buffer Status Report Poll), BTF (Basic Trigger Frame), and BSR (Buffer Status Report) for respectively polling the STAs, allocating the RUs, and carrying information about the UL traffic, (2) random and scheduling access modes to RUs for collecting the BSR frames, (3) all technical information about the RUs including their types, numbers, emplacements, and data rates for structuring a transmission channel, and (4) simultaneous sending (resp. receiving) of multiple DL (resp. UL) data streams by the AP towards (resp. from) multiple STAs [4]. However, several challenges remain to be addressed in order to fairly allocate and effectively utilize the RUs: (1) define when and how using the 802.11ax control frames, (2) set up a policy to fairly collect the BSR frames, (3) select the combinations of RUs to be adopted as allocation schemas, and (4) avoid loss in spectral efficiency and timeout BA (Block Acknowledgment). Although numerous interesting and relevant research works have been devoted in the literature for studying and dimensioning the features of the OFDMA MU communications, no study to our knowledge has taken into account at the same time all the challenges mentioned above. The majority of conducted studies are focused on the following features: narrow RUs, random access mode, and UL traffic [5]. The other features, such as: variable width RUs, scheduling access mode, OFDMA standard control frames, DL traffic, and multiple reception of data streams, are not commonly taken into consideration. Yet, the latter are essentials to take advantage of OFDMA transmission technique. In this paper, we aim at designing, implementing and performance evaluating a new OFDMA MAC protocol able to fairly allocate and effectively utilize the RUs. This would be possible only if all challenges of OFDMA MU communications presented and discussed are addressed. The rest of this paper is organized as follows: In Section 2, we describe in detail the OFDMA technique. In Section 3, we outline important research done on OFDMA MU communications. In Section 4, we propose a new protocol for managing OFDMA MU communications. In Section 5, we present and analyze the performance of the proposed OFDMA MAC protocol. In Section 6, we conclude the paper, and we suggest some research perspectives. Section snippets Background and problem statement In this section, we review the key features of OFDMA MU communications, and highlight challenges inherent to their operation. In Section 2.1, we show the benefits and explain the principle of the OFDMA technique. In Section 2.2, we present an OFDMA cycle and the main standard frames. In Section 2.3, we compare the different access modes to sending UL requests. In Section 2.4, we provide all possible combinations of RUs. In Section 2.5, we focus on simultaneous transmission of multiple data Related work and motivations This section covers, on one hand, the most important OFDMA MAC protocols proposed in the literature (see Section 3.1), and, on the other hand, our motivations and objectives for designing and implementing a new OFDMA MAC protocol (see Section 3.2). Proposal In this section, we propose a Fair Allocation and Effective Utilization protocol of RUs (FAEU-RUs) in MU communications-based OFDMA transmission technique designed for IEEE 802.11ax HEW networks. To that end, we define an arrangement of standard control frames in Section 4.1. We set up a policy to collect the UL requests of STAs in Section 4.2. We adopt allocation schemas of RUs in Section 4.3. We elaborate a procedure to synchronize simultaneous reception of multiple data streams in Section 4.4 Performance evaluation This section is dedicated to implementing and performance evaluating the FAEU-RUs protocol. A comparison is then made with two other OFDMA MAC protocols (namely: HMAC [14] and OHCA [19]) to assess the fairness and effectiveness of the FAEU-RUs protocol. To this end, simulation tools and parameters are given in Section 5.1. Thereafter, the discussion of simulation results is provided in Section 5.2. Conclusion and prospects In this research work, we focused on accessing, allocating, and utilizing the RUs introduced in the IEEE 802.11ax standard through the OFDMA transmission technique, which aims at enabling MU communications in HEW networks with high connection demand. Although the OFDMA transmission technique enables multiple data streams (up to 9) to be simultaneously transmitted at low cost and without interference either in DL or UL direction, several issues remain to be addressed in order to fairly and CRediT authorship contribution statement Saloua Brahmi: Methodology, Software, Writing – original draft, Writing – review & editing. Mohand Yazid: Conceptualization, Validation, Writing – original draft, Writing – review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Saloua Brahmi received her Master degree in Computer Science in 2016. She is currently a PhD student at Research Unit LaMOS, University of Bejaia, Algeria. Her research interests are: WLAN networks and performance evaluation. References (25) Al-Mefleh H. et al. Frequency-domain contention and polling MAC protocols in IEEE 802.11 wireless networks: A survey Comput. Commun. (2018) Bellalta B. et al. AP-initiated multi-user transmissions in IEEE 802.11ax WLANs Ad Hoc Netw. (2019) Bellalta B. et al. Next generation IEEE 802.11 wireless local area networks: Current status, future directions and open challenges Comput. Commun. (2016) Ali M.Z. et al. Bridging the transition from IEEE 802.11ac to IEEE 802.11ax: Survival of EDCA in a coexistence environment IEEE Netw. (2019) S. Brahmi, M. Yazid, Towards an efficient allocation of resource units in multi-user OFDMA communications, in:... IEEE std 802.11ax-2021, wireless LAN medium access control (MAC) and physical layer (PHY) specifications amendment 1: Enhancements for high-efficiency WLAN (2021) S. Brahmi, M. Yazid, Towards a hybrid access to resource units in multi-user OFDMA communications, in: Proccedings of... S. Brahmi, M. Yazid, M. Omar, Multiuser access via OFDMA technology in high density IEEE 802.11ax WLANs: A survey, in:... Coleman D.D. et al. Chapter 19: 802.11ax: High efficiency (HE) M. Wu, J. Wang, Y.H. Zhu, J. Hong, High throughput resource unit assignment scheme for OFDMA-based WLAN, in: IEEE... Masiukiewicz A. Throughput comparison between the new HEW 802.11ax standard and 802.11n/ac standards in selected distance windows Int. J. Electr. Telecommun. (2019) T. Mishima, S. Miyamoto, S. Sampei, W. Jiang, Novel DCF-based multi-user MAC protocol and dynamic resource allocation... View more references Cited by (0) Recommended articles (0) Saloua Brahmi received her Master degree in Computer Science in 2016. She is currently a PhD student at Research Unit LaMOS, University of Bejaia, Algeria. Her research interests are: WLAN networks and performance evaluation. Mohand Yazid received his HDR degree in Computer Science in 2017. He is currently a Professor at the department of Computer Science, University of Bejaia, Algeria. His research interests are: modeling, simulation, and performance evaluation of wireless networks. View full text © 2023 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.comnet.2023.109639", "PubYear": 2023, "Volume": "224", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Unit LaMOS (Modeling and Optimization of Systems), Faculty of Exact Sciences, University of Bejaia, 06000 Bejaia, Algeria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Unit LaMOS (Modeling and Optimization of Systems), Faculty of Exact Sciences, University of Bejaia, 06000 Bejaia, Algeria"}], "References": []}, {"ArticleId": 106372298, "Title": "j-Wave: An open-source differentiable wave simulator", "Abstract": "We present an open-source differentiable acoustic simulator, j-Wave, which can solve time-varying and time-harmonic acoustic problems. It supports automatic differentiation, which is a program transformation technique that has many applications, especially in machine learning and scientific computing. j-Wave is composed of modular components that can be easily customized and reused. At the same time, it is compatible with some of the most popular machine learning libraries, such as JAX and TensorFlow. The accuracy of the simulation results for known configurations is evaluated against the widely used k-Wave toolbox and a cohort of acoustic simulation software. j-Wave is available from https://github.com/ucl-bug/jwave .", "Keywords": "Differentiable simulator ; Acoustics ; Machine learning ; GPU acceleration ; Wave equation ; He<PERSON>holtz equation ; JAX", "DOI": "10.1016/j.softx.2023.101338", "PubYear": 2023, "Volume": "22", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Medical Physics and Biomedical Engineering, University College of London, Gower Street, London WC1E 6BT, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University College of London, Gower Street, London WC1E 6BT, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Medical Physics and Biomedical Engineering, University College of London, Gower Street, London WC1E 6BT, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Medical Physics and Biomedical Engineering, University College of London, Gower Street, London WC1E 6BT, UK"}], "References": [{"Title": "A general approach to seismic inversion with automatic differentiation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "104751", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 106372302, "Title": "RCRE: radical-aware causal relationship extraction model oriented in the medical field", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCSE.2023.10054227", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 8309, "JournalTitle": "International Journal of Computational Science and Engineering", "ISSN": "1742-7185", "EISSN": "1742-7193", "Authors": [{"AuthorId": 1, "Name": "Xiaoqing Li", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106372304, "Title": "Parity generators in QCA nanotechnology for nanocommunication systems", "Abstract": "The conventional complementary metal oxide semiconductor (CMOS) technology faces scalability and secondary effects issues in deep nanoscale regime. Therefore, many possible technologies are being explored to boost the current electronic industry. Quantum-dot cellular automata (QCA) is the possible technology to overcome the issues of conventional CMOS technology. QCA technology gives the advantages of area-efficient, low-power, and high-speed logic implementation in deep nanoscale regime. Exclusive-OR (XOR) gate is the fundamental logic required for different applications. Therefore, a reliable 3-input XOR gate using QCA technology is proposed in the paper. In communication system, the XOR gate can be utilized for the generation of parity bits. Hence, the proposed XOR gate is applied to develop the 2, 3, 4, and 5-input even and odd parity generators. The developed designs are more efficient in comparison with the existing designs. Any input parity generator can easily be developed using the proposed XOR gate. The number of cells, cell area, layout area, and design cost are improved for the proposed 3-input XOR gate as compared to the existing designs. The proposed 4-input parity generator consists of only 16 QCA cells and improves 76% design cost as compared to the best-reported work in the literature. Energy dissipation analysis is also presented for the proposed designs using the QCA Designer-E and QCA Pro. The proposed 4-input parity generator reduces 87.97% of total energy dissipation at a 1.5 Kink energy level as compared to the existing work.", "Keywords": "CMOS ; Nanoscale regime ; QCA ; XOR gate ; Nanocommunication ; Parity generator", "DOI": "10.1016/j.nancom.2023.100440", "PubYear": 2023, "Volume": "36", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronics & Communication Engineering, Shri <PERSON> Devi University, Katra, 182320, India"}], "References": [{"Title": "Design of high-performance QCA incrementer/decrementer circuit based on adder/subtractor methodology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "72", "Issue": "", "Page": "102927", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A Novel Low Power Technique for FinFET Domino OR Logic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "7", "Page": "2150117", "JournalTitle": "Journal of Circuits, Systems and Computers"}, {"Title": "Investigating multiple defects on a new fault-tolerant three-input QCA majority gate", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "8", "Page": "8305", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "CNTFET Circuit-Based Wide Fan-In Domino Logic for Low Power Applications", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "2250036", "JournalTitle": "Journal of Circuits, Systems and Computers"}, {"Title": "Ultra-efficient adders and even parity generators in nano scale", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "107548", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Latch and flip-flop design in QCA technology with minimum number of cells", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108186", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 106372346, "Title": "Pengembangan Sistem Pendeteksi Kebocoran LPG Menggunakan Infrastruktur Instant Messaging Berbasis Internet of Things", "Abstract": "AbstrakPerkembangan teknologi yang semakin pesat seiring dengan pekerjaan manusia yang semakin banyak, menjadikan peran teknologi sebagai upaya optimalisasi dalam membantu pekerjaaan manusia sangat dibutuhkan. Salah satu tren teknologi yang menarik dan berkembang adalah teknologi Internet of Things atau biasa disingkat dengan IoT. Pemanfaatan Internet of Things juga sangat membantu dalam sistem pemantauan jarak jauh. Sebagai salah satu bentuk pemanfaatannya adalah sistem pendeteksi jarak jauh terhadap kebocoran Liquified Petroleum Gas (LPG) pada dapur sebuah rumah tangga, restoran maupun industri sehingga pemilik bisa melakukan pencegahan dini apabila terjadi sebuah kebocoran di dalam ruangan dan mampu memberikan peringatan dini kebocoran LPG saat ditinggal bepergian. Sistem dikembangkan melalui 4 tahapan, mulai dari pengumpulan data, perancangan perangkat keras, penulisan kode program dan uji coba sistem. Penelitian ini bertujuan menghasilkan sebuah alat pendeteksi kebocoran LPG berbasis IoT, dimana alat ini mampu mendeteksi serta mengirimkan pesan peringatan dini saat terjadi kebocoran LPG baik melalui pesan telegram maupun buzzer yang terpasang di perangkat. Dengan mengacu pada standarisasi yang dikeluarkan oleh Badan Standardisasi Nasional tentang Nilai Ambang Batas zat kimia di udara tempat kerja, sistem telah bekerja dengan baik dengan mampu memberikan peringatan kebocoran LPG saat Nilai Ambang Batas (NAB) zat kimia mencapai 1000 bds/ppm.Kata kunci: IoT, telegram, LPG, ppm Abstract[Development Of LPG Leak Detection System Using Instant Messaging Infrastructure Based On Internet Of Things]Developments of technology along with human work are growing, making the role of technology as an optimization effort in helping human work needed.  One of the technologies that are developing is the Internet of Things or commonly abbreviated as IoT. Utilization of the Internet of Things is also very helpful in remote monitoring systems. One form of utilization is a remote detection system for Liquefied Petroleum Gas (LPG) leaks in the kitchen of a household, restaurant, or industry so that owners can take early prevention in the event of a leak in the room and are able to provide early warning of LPG leaks when left traveling. The system is developed through four stages, starting from data collection, hardware design, writing program code, and system testing. This study aims to produce an IoT-based LPG leak detector, which a tool that is able to detect and send an early warning message when an LPG leak occurs either through a telegram message or a buzzer installed on the device. By referring to the standard issued by the National Standardization Agency regarding the Threshold Value of chemicals in the workplace air, the system has worked well by being able to provide an LPG leak warning when the Threshold Value of chemical substances reaches 1000 ppm.. Keywords: IoT; telegram, LPG; ppm", "Keywords": "IoT;telegram;LPG;ppm", "DOI": "10.21111/fij.v7i2.6509", "PubYear": 2022, "Volume": "7", "Issue": "2", "JournalId": 42885, "JournalTitle": "Fountain of Informatics Journal", "ISSN": "2541-4313", "EISSN": "2548-5113", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Akademi Komunitas Negeri Putra Sang Fajar Blitar"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Akademi Komunitas Negeri Putra Sang Fajar Blitar"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Akademi Komunitas Negeri Putra Sang Fajar Blitar"}], "References": []}, {"ArticleId": 106372385, "Title": "A survey of clustering large probabilistic graphs: Techniques, evaluations, and applications", "Abstract": "<p>Given the growth of uncertainty in the real world, analysing probabilistic graphs is crucial. Clustering is one of the most fundamental methods of mining probabilistic graphs to discover the hidden patterns in them. This survey examines an extensive and organized analysis of the clustering techniques of large probabilistic graphs proposed in the literature. First, the definition of probabilistic graphs and modelling them are introduced. Second, the clustering of such graphs and their challenges, such as uncertainty of edges, high dimensions, and the impossibility of applying certain graph clustering techniques directly, are expressed. Then, a taxonomy of clustering approaches is discussed in two main categories: threshold-based and possible worlds-based methods. The techniques presented in each category are explained and examined. Here, these methods are evaluated on real datasets, and their performance is compared with each other. Finally, the survey is summarized by describing some of the applications of probabilistic graph clustering and future research directions.</p>", "Keywords": "clustering;possible worlds-based methods;probabilistic graph;threshold-based methods", "DOI": "10.1111/exsy.13248", "PubYear": 2023, "Volume": "40", "Issue": "6", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering University of Science and Technology of Mazandaran  Behshahr Iran;Faculty of Electrical and Computer Engineering Semnan University  Semnan Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering Semnan University  Semnan Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering Semnan University  Semnan Iran"}], "References": [{"Title": "Ensemble-based clustering of large probabilistic graphs using neighborhood and distance metric learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "4", "Page": "4107", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "PC2P: parameter-free network-based prediction of protein complexes", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "73", "JournalTitle": "Bioinformatics"}, {"Title": "Clustering of graphs using pseudo-guided random walk", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "", "Page": "101281", "JournalTitle": "Journal of Computational Science"}, {"Title": "A weighted K-member clustering algorithm for K-anonymization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "10", "Page": "2251", "JournalTitle": "Computing"}, {"Title": "Clustering probabilistic graphs using neighbourhood paths", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "568", "Issue": "", "Page": "216", "JournalTitle": "Information Sciences"}, {"Title": "Clustering uncertain graphs using ant colony optimization (ACO)", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "14", "Page": "11721", "JournalTitle": "Neural Computing and Applications"}, {"Title": "DGCU: A new deep directed method based on Gaussian embedding for clustering uncertain graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "108066", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 106372390, "Title": "Design of Handling Robot Based on Visual Recognition and Path Planning", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2023.132021", "PubYear": 2023, "Volume": "13", "Issue": "2", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "令宇 卜", "Affiliation": ""}], "References": []}, {"ArticleId": 106372470, "Title": "Optimized real-time parking management framework using deep learning", "Abstract": "Today’s modern world has seen massive population growth in the past few decades, causing the automobile industry to expand rapidly. The exponential increase has disrupted the seamless flow of traffic globally and has fashioned significant problems such as traffic congestion and its mismanagement. To tackle these issues, some hardware and data-driven-based solutions have been previously proposed. The hardware-based solutions utilize different types of sensors installed at relevant sites to monitor the status of parking slots which makes them less scalable, complex, costly to install and maintain. In contrast, the data-driven solutions utilize the already available infrastructure of surveillance cameras installed at parking lots, thus overcoming the limitations of sensor-based solutions. However, currently only classification-based approaches are prevalent and adopted to monitor the status of the parking slots which makes the systems less generalized for scalability with slower performance. This paper proposes an intelligent parking management system which employs deep learning to alleviate the limitations in the data driven solutions by leveraging the high performance and fast inference capability of YOLO v5 for vehicles detection instead of parking slot classification. The model was evaluated using the PKLot dataset which is a benchmark for identifying the status of the parking lot with state of the art performance achieved having an accuracy of 99.5%. To augment the performance of the algorithm in real-time, a pretrained model of YOLO v5 on MS COCO dataset was employed to detect and assign vacant parking slots and generate vehicle statistics. The performance of the proposed system was evaluated on our custom dataset with an accuracy of 96.8% achieved and a nearly real-time performance of 45 Fps which makes it more efficient, scalable, and generalized.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.119686", "PubYear": 2023, "Volume": "220", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Center of Artificial Intelligence, University of Engineering and Technology, Peshawar, Pakistan"}, {"AuthorId": 2, "Name": "Saba Gul", "Affiliation": "National Center of Artificial Intelligence, University of Engineering and Technology, Peshawar, Pakistan;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Center of Artificial Intelligence, University of Engineering and Technology, Peshawar, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Center of Artificial Intelligence, University of Engineering and Technology, Peshawar, Pakistan"}], "References": [{"Title": "Real Time IP Camera Parking Occupancy Detection using Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>;  <PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "606", "JournalTitle": "Procedia Computer Science"}, {"Title": "DWCA-YOLOv5: An Improve Single Shot Detector for Safety Helmet Detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Sensors"}]}, {"ArticleId": 106372520, "Title": "Mobile agent path planning under uncertain environment using reinforcement learning and probabilistic model checking", "Abstract": "The major challenge in mobile agent path planning, within an uncertain environment, is effectively determining an optimal control model to discover the target location as quickly as possible and evaluating the control system’s reliability. To address this challenge, we introduce a learning-verification integrated mobile agent path planning method to achieve both the effectiveness and the reliability. More specifically, we first propose a modified Q-learning algorithm (a popular reinforcement learning algorithm), called Q E A − l e a r n i n g algorithm, to find the best Q-table in the environment. We then determine the location transition probability matrix, and establish a probability model using the assumption that the agent selects a location with a higher Q-value. Secondly, the learnt behaviour of the mobile agent based on Q E A − l e a r n i n g algorithm, is formalized as a Discrete-time Markov Chain (DTMC) model. Thirdly, the required reliability requirements of the mobile agent control system are specified using Probabilistic Computation Tree Logic (PCTL). In addition, the DTMC model and the specified properties are taken as the input of the Probabilistic Model Checker PRISM for automatic verification. This is preformed to evaluate and verify the control system’s reliability. Finally, a case study of a mobile agent walking in a grids map is used to illustrate the proposed learning algorithm. Here we have a special focus on the modelling approach demonstrating how PRISM can be used to analyse and evaluate the reliability of the mobile agent control system learnt via the proposed algorithm. The results show that the path identified using the proposed integrated method yields the largest expected reward.", "Keywords": "", "DOI": "10.1016/j.knosys.2023.110355", "PubYear": 2023, "Volume": "264", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu 610031, China;School of Computing, Ulster University, Northern Ireland BT15 1ED, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing, Ulster University, Northern Ireland BT15 1ED, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing, Ulster University, Northern Ireland BT15 1ED, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computing, Ulster University, Northern Ireland BT15 1ED, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mathematics, Southwest Jiaotong University, Chengdu 610031, China;Corresponding author"}], "References": [{"Title": "Research on path planning of mobile robot based on improved ant colony algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "6", "Page": "1555", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Reinforcement based mobile robot path planning with improved dynamic window approach in unknown environment", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "45", "Issue": "1", "Page": "51", "JournalTitle": "Autonomous Robots"}, {"Title": "Model checking agent-based communities against uncertain group commitments and knowledge", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114792", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A data-level fusion model for unsupervised attribute selection in multi-source homogeneous data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "87", "JournalTitle": "Information Fusion"}, {"Title": "A reinforcement learning based artificial bee colony algorithm with application in robot path planning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117389", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106372576, "Title": "Research of Construction Methods for Cloud Services and Overview of the Implementations TOSCA Standard", "Abstract": "В статье рассматриваются и сравниваются различные инструменты автоматизации управления ресурсами в облаке. Изменения в архитектуре программного обеспечения и подходов к разработке требуют автоматизации процессов управления развертывания и дальнейшего сопровождения по в разных средах. В разд. 2 представлен подробный обзор инструментов с примерами конфигураций, а также разбор релевантных статей, рассматривающих различные инструменты автоматизации и эффективность их внедрения. В разд. 3 представлен проект решения по объединению оркестраторов, разработанных в ИСП РАН, для получения инструмента с функционалом, которого нет у конкурентов.", "Keywords": "Orchestration;Software Deployment;TOSCA;оркестрация;развертывание ПО", "DOI": "10.15514//ISPRAS-2022-34(5)-9", "PubYear": 2022, "Volume": "34", "Issue": "5", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Национальный исследовательский университет «Высшая школа экономики»"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}], "References": []}, {"ArticleId": 106372592, "Title": "Evolutionary computation-based multitask learning network for railway passenger comfort evaluation from EEG signals", "Abstract": "As a core indicator of the railway service industry, the comfort of the railway passengers is an important aspect to measure the advancement of railway technology. The accurate evaluation of railway passenger comfort has received much attention from academia and industry. The EEG-based comfort evaluation method has been hailed as the gold standard due to its rich quantity of information and objectivity. In this study, an evolutionary computation-based multitask learning network named EEG-DEMTL is proposed to evaluate railway passenger comfort from EEG signals. In this network, the multitask learning structure fully exploits the relationship between subtasks such as passenger emotion and the main task, which is passenger overall comfort. Furthermore, the weight definition method based on the differential evolution (DE) algorithm is used to select the best weight setting. To verify the validity of our proposed method, field experiments in a high-speed railway (HSR) are designed, and comfort perceptions and EEG signals of 20 passengers are collected. Compared with the baseline models, which include the support vector machine, K-nearest neighbour and decision trees, the proposed EEG-DEMTL model achieves the best performance. In addition, the comparison results between several weight settings of MTL show that the DE weights can improve the evaluation performance by 6.30%. This research proposes a novel EEG-based method to meet the requirements of railway passenger comfort evaluation and offers a neurological explanation for railway passenger comfort.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110079", "PubYear": 2023, "Volume": "136", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Central South University, Changsha, 410083, China;Department of Architecture and Civil Engineering, City University of Hong Kong, Hong Kong, 999077, China"}, {"AuthorId": 2, "Name": "Hanliang Fu", "Affiliation": "School of Management, Laboratory of Neuromanagement in Engineering,Xi’an University of Architecture and Technology, Xi’an, 710055, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Traffic & Transportation Engineering, Central South University, Changsha, 410075, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, 250061, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Central South University, Changsha, 410083, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Traffic & Transportation Engineering, Central South University, Changsha, 410075, China"}, {"AuthorId": 7, "Name": "Hui<PERSON> Chen", "Affiliation": "School of Civil Engineering, Central South University, Changsha, 410083, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Traffic & Transportation Engineering, Central South University, Changsha, 410075, China;Corresponding author"}], "References": [{"Title": "Assessment of passenger long-term vibration discomfort: a field study in high-speed train environments", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "4", "Page": "659", "JournalTitle": "Ergonomics"}, {"Title": "End-to-end multi-task learning for simultaneous optic disc and cup segmentation and glaucoma classification in eye fundus images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "108347", "JournalTitle": "Applied Soft Computing"}, {"Title": "Spatial-frequency convolutional self-attention network for EEG emotion recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108740", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 106372602, "Title": "Expert System for Predictive Maintenance Transformer using J48 Algorithm", "Abstract": "<p>Predictive maintenance can reduce the risk of sudden transformer failure which causes the risk of plant to stop operating. One of transformer predictive maintenance technique is the Dissolved Gas Analysis (DGA) Test Oil Transformer. The gas is interpreted and analyzed to find out and get conclusions about the health condition and also possible problems in the transformer based on IEEE Standards and IEC Standards. To facilitate monitoring, a Decision Support System for Interpretation of Test Results of DGA Oil Immersed Transformer was created to form a database containing transformer data with the amount of main gas from the DGA test results. Next, decision tree was made using the J48 algorithm. The decision tree simplifying and speed up the decision-making process for recommended actions that are displayed on the system. The system also displays a trending graph of the last transformer test and quickly displays a dashboard of transformer status, i.e. normal, alarm, or danger. Engineer will get notification email if any transformer is in danger status. In addition, the system is able to provide information on possible fault types for each transformer. The benefits of this system are that the health condition of the transformer can be monitored properly and corrective action can be taken immediately on a problem based on the results of the decision support system. This will reduce the risk of shutdown and support the reliability of plant operations.</p>", "Keywords": "Transformer;Decision Tree;J48;Predictive Maintenance;DGA Test", "DOI": "10.22219/kinetik.v8i1.1587", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 31102, "JournalTitle": "KINETIK", "ISSN": "2503-2259", "EISSN": "2503-2267", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Negeri Cilacap"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Negeri Cilacap"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Politeknik Negeri Indramayu"}], "References": []}, {"ArticleId": 106372713, "Title": "TERFDA: Tensor Embedding RF Domain Adaptation for varying noise interference", "Abstract": "Radio frequency machine learning (RFML) can be loosely termed as a field that machine learning (ML) and deep learning (DL) techniques to applications related to wireless communications. However, traditional RFML basically assume that the data of training set and test set are independent and identically distributed and only a large number of labeled data can train a classification model which can effectively classify test set data. In other words, without enough training samples, it is impossible to learn an automatic modulation classifier that performs well in varying noise interference environment. Feature-based transfer learning minimizes the distribution difference between historical modulated signal data and new data by learning similarity-maximizing feature spaces. Therefore, in this paper, Dynamic Distribution Adaptation (DDA) is adopted to address the above challenges. We propose a Tensor Embedding RF Domain Adaptation (TERFDA) approach, which learns the latent subspace of the tensors formed by the time–frequency maps of the signals, so that use the multi-dimensional domain information of the signals to jointly learn the shared feature subspace of the source domain and the target domain, then perform DDA in the shared subspace. The experimental results show that under the modulated signal data, compared with the state-of-the-art DA algorithm, TERFDA has less requirements on the number of samples and categories, and has superior performance for confrontation the varying noise interference between source domain and target domain.", "Keywords": "Radio frequency machine learning ; Feature-based transfer learning ; Dynamic distribution adaptation ; Tensor", "DOI": "10.1016/j.phycom.2023.102015", "PubYear": 2023, "Volume": "58", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Harbin Engineering University, Harbin, China"}, {"AuthorId": 4, "Name": "Jiangzhi Fu", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, China"}, {"AuthorId": 5, "Name": "Guangzhen Si", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, China"}], "References": [{"Title": "Cross-domain activity recognition via substructural optimal transport", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "454", "Issue": "", "Page": "65", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 106372729, "Title": "Fake news, disinformation and misinformation in social media: a review", "Abstract": "<p>Online social networks (OSNs) are rapidly growing and have become a huge source of all kinds of global and local news for millions of users. However, OSNs are a double-edged sword. Although the great advantages they offer such as unlimited easy communication and instant news and information, they can also have many disadvantages and issues. One of their major challenging issues is the spread of fake news. Fake news identification is still a complex unresolved issue. Furthermore, fake news detection on OSNs presents unique characteristics and challenges that make finding a solution anything but trivial. On the other hand, artificial intelligence (AI) approaches are still incapable of overcoming this challenging problem. To make matters worse, AI techniques such as machine learning and deep learning are leveraged to deceive people by creating and disseminating fake content. Consequently, automatic fake news detection remains a huge challenge, primarily because the content is designed in a way to closely resemble the truth, and it is often hard to determine its veracity by AI alone without additional information from third parties. This work aims to provide a comprehensive and systematic review of fake news research as well as a fundamental review of existing approaches used to detect and prevent fake news from spreading via OSNs. We present the research problem and the existing challenges, discuss the state of the art in existing approaches for fake news detection, and point out the future research directions in tackling the challenges.</p><p>© The Author(s), under exclusive licence to Springer-Verlag GmbH Austria, part of Springer Nature 2023, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Disinformation;Fake news;Information disorder;Misinformation;Online deception;Online social networks", "DOI": "10.1007/s13278-023-01028-5", "PubYear": 2023, "Volume": "13", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Operations Research (DIRO), University of Montreal, Montreal, Canada."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Operations Research (DIRO), University of Montreal, Montreal, Canada."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Operations Research (DIRO), University of Montreal, Montreal, Canada."}], "References": [{"Title": "Learning to evaluate: An intervention in civic online reasoning", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "103711", "JournalTitle": "Computers & Education"}, {"Title": "Fake news, rumor, information pollution in social media and web: A contemporary survey of state-of-the-arts, challenges and opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "112986", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Automating fake news detection system using multi-level voting model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "12", "Page": "9049", "JournalTitle": "Soft Computing"}, {"Title": "Information verification in social networks based on user feedback and news agencies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Deep neural approach to Fake-News identification", "Authors": "Deepak S; Bhadrachalam Chitturi", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2236", "JournalTitle": "Procedia Computer Science"}, {"Title": "FakeNewsNet: A Data Repository with News Content, Social Context, and Spatiotemporal Information for Studying Fake News on Social Media", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "171", "JournalTitle": "Big Data"}, {"Title": "A Reliable Weighting Scheme for the Aggregation of Crowd Intelligence to Detect Fake News", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "319", "JournalTitle": "Information"}, {"Title": "Data citizenship: rethinking data literacy in the age of disinformation, misinformation, and malinformation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "", "JournalTitle": "Internet Policy Review"}, {"Title": "The Future of False Information Detection on Social Media", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A Survey of Fake News", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Fake News, Investor Attention, and Market Reaction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "35", "JournalTitle": "Information Systems Research"}, {"Title": "Combating disinformation in a social media age", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "6", "Page": "", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Vulnerabilities to Online Social Network Identity Deception Detection Research and Recommendations for Mitigation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "9", "Page": "148", "JournalTitle": "Future Internet"}, {"Title": "Deep learning for misinformation detection on online social networks: a survey and new perspectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "82", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Investigating Differences in Crowdsourced News Credibility Assessment", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Would you notice if fake news changed your behavior? An experiment on the unconscious effects of disinformation", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "106633", "JournalTitle": "Computers in Human Behavior"}, {"Title": "An ensemble machine learning approach through effective feature extraction to classify fake news", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "47", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Detecting fake news with capsule neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "106991", "JournalTitle": "Applied Soft Computing"}, {"Title": "New techniques to measure lie detection using COVID-19 fake news and the Multivariable Multiaxial Suggestibility Inventory-2 (MMSI-2)", "Authors": "Álex Escolà-Gascón", "PubYear": 2021, "Volume": "3", "Issue": "", "Page": "100049", "JournalTitle": "Computers in Human Behavior Reports"}, {"Title": "Use of bot and content flags to limit the spread of misinformation among social networks: a behavior and attitude survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "“ Who is gullible to political disinformation ?” : predicting susceptibility of university students to fake news", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "2", "Page": "165", "JournalTitle": "Journal of Information Technology & Politics"}, {"Title": "Fake news detection based on news content and social contexts: a transformer-based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "335", "JournalTitle": "International Journal of Data Science and Analytics"}, {"Title": "Fake news on the internet: a literature review, synthesis and directions for future research", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "5", "Page": "1662", "JournalTitle": "Internet Research"}, {"Title": "Novel approaches to fake news and fake account detection in OSNs: user social engagement and visual content centric model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "52", "JournalTitle": "Social Network Analysis and Mining"}]}, {"ArticleId": *********, "Title": "Connectedness, local connectedness, and components on bipolar soft generalized topological spaces", "Abstract": "Connectedness represents the most significant and fundamental topological property. It highlights the main characteristics of topological spaces and distinguishes one topology from another. There is a constant study of bipolar soft generalized topological spaces (BSGTSs) by presenting BS ˜g-connected set and BS ˜g-connected space in BSGTSs as well as it is discussing some properties and results for these topics. Additionally, the notion of bipolar soft disjoint sets is put forward, BS ˜g-separation set, ˜g-separated BSSs and BS ˜g-hereditary property. Moreover, there is an extensive study of BS ˜g-locally connected space and BS ˜g-component with some related properties and theorems following them, such as the concepts of BS ˜g-locally connected spaces and BS ˜g-connected are independent of each other; also determined the conditions under which the BS ˜g-connected subsets are BS ˜g-components. © 2023, International Scientific Research Publications. All rights reserved.", "Keywords": "BS ˜g-component; BS ˜g-connected set; BS ˜g-connected space; BS ˜g-locally connected space; BSGTS; ˜g-separated BSSs", "DOI": "10.22436/jmcs.030.04.01", "PubYear": 2023, "Volume": "30", "Issue": "4", "JournalId": 34363, "JournalTitle": "Journal of Mathematics and Computer Science", "ISSN": "", "EISSN": "2008-949X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Mathematics, College of Basic Education, University of Duhok, Duhok, 42001, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Science, Cihan University, Duhok, Iraq; Department of Mathematics, Faculty of Science, University of Zakho, Zakho, 42002, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Mathematics, College of Basic Education, University of Duhok, Duhok, 42001, Iraq"}], "References": []}, {"ArticleId": 106372764, "Title": "Deep learning for detecting and elucidating human T-cell leukemia virus type 1 integration in the human genome", "Abstract": "Human T-cell leukemia virus type 1 (HTLV-1), a retrovirus, is the causative agent for adult T cell leukemia/lymphoma and many other human diseases. Accurate and high throughput detection of HTLV-1 virus integration sites (VISs) across the host genomes plays a crucial role in the prevention and treatment of HTLV-1-associated diseases. Here, we developed DeepHTLV, the first deep learning framework for VIS prediction de novo from genome sequence, motif discovery, and cis -regulatory factor identification. We demonstrated the high accuracy of DeepHTLV with more efficient and interpretive feature representations. Decoding the informative features captured by DeepHTLV resulted in eight representative clusters with consensus motifs for potential HTLV-1 integration. Furthermore, DeepHTLV revealed interesting cis -regulatory elements in regulation of VISs that have significant association with the detected motifs. Literature evidence demonstrated nearly half (34) of the predicted transcription factors enriched with VISs were involved in HTLV-1-associated diseases. DeepHTLV is freely available at https://github.com/bsml320/DeepHTLV .", "Keywords": "deep learning ; HTLV-1 ; viral integration sites ; T cell leukemia/lymphoma ; motif ; DSML 3: Development/pre-production: Data science output has been rolled out/validated across multiple domains/problems", "DOI": "10.1016/j.patter.2022.100674", "PubYear": 2023, "Volume": "4", "Issue": "2", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Precision Health, School of Biomedical Informatics, UTHealth Science Center at Houston, Houston, TX 77030, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Precision Health, School of Biomedical Informatics, UTHealth Science Center at Houston, Houston, TX 77030, USA;MD <PERSON> UTHealth Graduate School of Biomedical Sciences, Houston, TX 77030, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Precision Health, School of Biomedical Informatics, UTHealth Science Center at Houston, Houston, TX 77030, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Precision Health, School of Biomedical Informatics, UTHealth Science Center at Houston, Houston, TX 77030, USA;MD <PERSON> UTHealth Graduate School of Biomedical Sciences, Houston, TX 77030, USA;Department of Biomedical Informatics, Vanderbilt University Medical Center, Nashville, TN 37203, USA;Corresponding author"}], "References": [{"Title": "DeepEBV: a deep learning model to predict Epstein–Barr virus (EBV) integration sites", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "20", "Page": "3405", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 106372816, "Title": "Inhomogeneously polarized light fields: polarization singularity indices derived by analogy with the topological charge", "Abstract": "In this work, we study several different vector and hybrid light fields, including those with multiple polarization singularities. We derive polarization singularity indices by adopting a well-known <PERSON><PERSON><PERSON><PERSON>'s formula, which is commonly used to obtain the topological charge of scalar vortex light fields. It is shown that fields whose polarization state depends only on the polar angle in the beam cross section can have either polarization singularity lines outgoing from the center, or a single polarization singularity in the center of the beam cross section. If the polarization state of the field depends only on the radial variable, then such fields have no polarization singularities and their index is equal to zero. If the polarization state of a vector field depends on both polar coordi-nates, then such a field can have several polarization singularities at different locations in the beam cross section. We also investigate a vector field with high-order radial polarization and with a real parameter. At different values of this parameter, such a field has either several polarization singularity lines outgoing from the center, or a single singular point in the center. The polarization singularity index of such a field for different parameters can be either half-integer, or integer, or zero. © 2022, Institution of Russian Academy of Sciences. All rights reserved.", "Keywords": "inhomogeneous polarization; Poincaré-Hopf; polarization singularity; polarization singularity index; topological charge", "DOI": "10.18287/2412-6179-CO-1126", "PubYear": 2022, "Volume": "46", "Issue": "5", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS"}], "References": []}, {"ArticleId": 106372921, "Title": "On solving simplified diversified top- k s -plex problem", "Abstract": "Finding cohesive groups in a graph, which has been extensively studied by many researchers, is a fundamental and critical problem for various real-world applications, such as community search, motif discovery and anomaly detection. Unfortunately, the cohesive groups rarely appear as cliques and are usually highly overlapping, so few cohesive groups can be found by searching maximum or top- k maximal cliques. In other words, maximum or top- k maximal cliques are too strict for representing cohesive groups. To handle this problem, the DTKSP problem was introduced earlier in the literature to find k maximal s -plexes that cover maximum vertices with the lowest overlapping in a given graph. In this paper, we consider the Simplified Diversified Top- k s -Plex (S-DTKSP) problem, by aiming to find k maximal s -plexes that cover the maximum vertices without considering the size of overlap. We prove that the S-DTKSP problem is NP-hard and propose an integer linear programming for S-DTKSP problem. Then, we propose an iterated local search (ILS) algorithm with a tabu strategy to efficiently find a good solution. The proposed algorithms are evaluated on large real-world instances. The experimental results demonstrate that our approaches can solve the S-DTKSP problem effectively and efficiently than two baseline algorithms.", "Keywords": "Approximation algorithm ; Iterated local search ; Tabu search ; Scoring function ; S-DTKSP problem", "DOI": "10.1016/j.cor.2023.106187", "PubYear": 2023, "Volume": "153", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Nanjing University of Information Science & Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MIS, Université de Picardie, Jules <PERSON>, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Information Science and Technology, Northeast Normal University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Information Science and Technology, Northeast Normal University, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Information Science and Technology, Northeast Normal University, China;Key Laboratory of Applied Statistics of MOE, Northeast Normal University, China;Correspondence to: Information Science and Technology, Northeast Normal University, Key Laboratory of Applied Statistics of MOE, 5268 Renmin Street, Changchun, Jilin Province, 130024, China"}], "References": [{"Title": "Local search for diversified Top-k clique search problem", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "116", "Issue": "", "Page": "104867", "JournalTitle": "Computers & Operations Research"}, {"Title": "Efficient keyword search over graph-structured data based on minimal covered r-cliques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "3", "Page": "448", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": 106372941, "Title": "BFLS: Blockchain and Federated Learning for sharing threat detection models as Cyber Threat Intelligence", "Abstract": "Recently, Cyber Threat Intelligence (CTI) sharing has become an important weapon for cyber defenders to mitigate the increasing number of cyber attacks in a proactive and collaborative manner. However, with the dramatic increase in the deployment of shared communications between organizations, data has been a major priority to detect threats in the CTI sharing platform. In the modern environment, a valuable asset is the user’s threat data. Privacy policies are necessary to ensure the security of user data in the threat intelligence sharing community. Federated learning acts as a special machine learning technique for privacy preservation and offers to contextualize data in a CTI sharing platform. Therefore, this article proposes a new approach to threat intelligence sharing called BFLS (Blockchain and Federated Learning for sharing threat detection models as Cyber Threat Intelligence), where blockchain-based CTI sharing platforms are used for security and privacy. Federated learning technology is adopted for scalable machine learning applications, such as threat detection. Furthermore, users can obtain a well-trained threat detection model without sending personal data to the central server. Experimental results on the ISCX-IDS-2012 and CIC-DDoS-2019 datasets showed that BFLS can securely share CTI and has high accuracy in threat detection. The accuracies of BFLS are 98.92% and 98.56% on the two datasets, respectively. Introduction The idea of strategic defense with the concept of “active defense, traceability, and countermeasures” has been proposed to defend against cyberattacks and mitigate the increasingly severe situation of cybersecurity [1]. CTI collaboratively addresses cyber threats such as Advanced Persistent Threats (APT). CTI consists of information about the cyber attack, such as cyber attacker intelligence, technical intelligence, and device log files. The CTI can help companies identify, evaluate, analyze, and respond to cyber risks and reduce time classified threats [2]. Thus, the sharing platforms of CTI have become a vital security component inside company businesses; moreover, they are progressively paying attention to the exchange of information and expertise such as threat knowledge, indicators of compromise (IoC), detection techniques, and mitigation measures [3]. Sharing and analyzing CTI across corporations can address the issue of information silos utilizing private data to detect cyber threat detection. CTI realizes its full potential in collaborative threat detection and prevention by sharing threat data. Most of the data contain personal information which is private and should be carefully protected [4]. It is extremely important to incentivize companies to proactively share private data in a secure and reliable environment without fear of privacy breaches. Several researchers have developed various standards for threat intelligence exchange that fulfill the requirement of CTI sharing, including STIX [5], TAXII [6], and CybOX [7]. In addition, organizations share the unstructured threat data collected from security devices or system logs and analyze the data to detect threats by machine learning (ML) and deep learning (DL) algorithms [8]. ML/DL algorithms play an important role in analyzing CTI and detecting threats so that organizations can proactively defend against cyber attacks. However, CTI data are derived from sharing across organizations and need centralized storage to train an ML/DL algorithm. The process of sharing and centralized storage can create several problems, including privacy issues and single points of failure. Organizations are frequently hesitant to actively share and exchange cyber threat intelligence containing sensitive information due to the security issues mentioned above. Companies, for example, have suffered financial or reputational losses due to the leaking of privacy information, resulting in a decreased willingness to engage in collaborative sharing [9]. The reason is that threat intelligence contains data regarding privacy and trade secrets, such as corporate asset information and business information [10]. Organizations, thus, have negative reservations about the intelligence sharing process and prefer to acquire rather than share [11]. This significantly impairs the healthy development of the intelligence-sharing ecology and contributes to the problem of data silos for collaborative detection among organizations [12]. Therefore, it is important to provide appropriate privacy without loss of information. We formulate the problem of sharing CTI as the problem of sharing the ML/DL model to indirectly exchange privacy information about organizations. Federated learning (FL) techniques have shed new light on solving the privacy issues of the process of training the ML/DL model by enabling learning in multisource decentralized databases without data sharing or data collection. Hence, FL as a decentralized approach can protect the organization’s privacy. It uses distributed data for training while maintaining robust local learning in local devices. There are some risks of a single server failure if we directly share the model after the FL work is finished. Additionally, an unreliable model could be uploaded by a malicious node, which is defined as a node that submits an incorrect and spurious malicious CTI, leading to tampering with the learning in the FL system. Because the blockchain framework has some advanced features, such as traceability, immutability, and transparency, we adapt the blockchain to share the model to overcome the risks of a single server failure and malicious nodes [13]. We propose a novel CTI-sharing approach based on FL and blockchain (BFLS) to ensure the security of CTI data sharing. We adapt FL to train a threat detection model that avoids organizational privacy leakage. The blockchain is adapted to share the mode to overcome the risks of a single server failure and a malicious node. Specifically, the consensus protocol of the blockchain is improved to filter the better-trained models, i.e., quality CTIs are selected to participate in federated learning, and the models are automatically aggregated and updated through smart contracts. The threat detection model can cover most cybersecurity scenarios because the CTI shared in BFLS is a threat detection model trained collaboratively by organizations. The following are the key contributions of the study. • A novel CTI sharing approach based on FL and blockchain (BFLS) is proposed to ensure the security of CTI data sharing. In this method, FL is adapted to train the threat detection model, and blockchain is adapted to aggregate and share the model in a decentralized way. • The consensus protocol of the blockchain is improved to filter the better-trained models, i.e., quality CTIs are verified and selected to participate in FL, and then models are automatically aggregated and updated through smart contracts. • Experimental results on the ISCX-IDS-2012 and CIC-DDoS-2019 datasets show that BFLS can achieve excellent results in terms of accuracy, recall, precision, and F1-score while ensuring the security of CTI data sharing. The rest of this article is organized as follows. Section 2 gives a brief introduction to the related work. The BFLS proposed in this article is detailed in Section 3. Section 4 presents the experimental results of the ISCX-IDS-2012 and CIC-DDoS-2019 datasets. Finally, Section 5 summarizes the work of this article and future work. Section snippets Related work There are several existing solutions for CTI sharing. In Table 1, we provide a summary of these proposals in comparison to our current work. We classify the solutions for CTI sharing into two types of platforms, centralized and decentralized, and expand on them below. Motivations As mentioned above, analyzing traffic data by centralized ML or DL algorithms to support intrusion detection is widely done in the existing threat intelligence analysis methods. However, this form of analyzing traffic data creates privacy issues. Federated learning [23], first proposed by Google, is designed mainly to ensure information security and protect personal data privacy while safeguarding data exchange [24]. As shown in Fig. 1, FL is a decentralized machine learning framework where Experiment setting The computer used in the experiments used Ubuntu20.0.4 OS. We chose the FISCO-BCOS ( https://github.com/fisco-bcos ) consortium blockchain system, and the smart contract is constructed using a C++ precompiled contract. Deep learning models were trained using TensorFlow 2.14 to complete the CTI sharing task. We simulated a distributed environment for FL. Three parameters k , m and a were defined, where the proportion of nodes involved in training is k % , m % nodes are members of the CTI validation Honest and trustworthy committee members As an approach that requires organizational cooperation to complete global CTI aggregation, the cooperation of malicious nodes is the biggest problem that may be faced by BFLS. Malicious nodes disguised to obtain higher scores become part of the CTI validation committee, and malicious clients mixed into the committee may interfere with the committee’s decision-making. According to the CTI validation committee mechanism, the probability of successful attacks is only significant when the Conclusion This article finds that a CTI sharing method based on FL can be a good solution to the privacy leakage problem caused by sharing local data. Based on this finding, this article proposes a method called BFLS to share threat detection models trained using FL as cyber CTI in a blockchain network. In contrast to traditional CTI sharing solutions, this article proposes an approach to automatically validate and aggregate threat intelligence, filtering out low-quality and false CTI while protecting CRediT authorship contribution statement Tongtong Jiang: Methodology, Software, Writing, Data curation, Writing – original draft. Guowei Shen: Formal analysis, Investigation, Funding acquisition, Writing – review & editing. Chun Guo: Methodology, Investigation, Formal analysis, Writing – review & editing. Yunhe Cui: Investigation, Validation. Bo Xie: Methodology, Validation. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work was supported by the National Natural Science Foundation of China under Grant No. 62062022 , the Guizhou Provincial Science and Technology Project Under Grant No. [2022]071 . Tongtong Jiang was born in Liaoning, China in 1997. She received a bachelor’s degree in Computer Science and Technology of Guizhou University. And now, she is a graduate student in Guizhou University. Her long-established research activities are in the fields of data security and network security, especially in cyber threat intelligence. References (37) Zhang C. et al. A survey on federated learning Knowl.-Based Syst. (2021) Shiravi A. et al. Toward developing a systematic approach to generate benchmark datasets for intrusion detection Comput. Secur. (2012) Ye H. et al. VREFL: Verifiable and reconnection-efficient federated learning in IoT scenarios J. Netw. Comput. Appl. (2022) Wu D. et al. FL-MGVN: Federated learning for anomaly detection using mixed Gaussian variational self-encoding network Inf. Process. Manage. (2022) Casey E. et al. Leveraging CybOX™ to standardize representation and exchange of digital forensic information Digit. Invest. (2015) Ye H. et al. Secure and efficient outsourcing differential privacy data release scheme in cyber–physical system Future Gener. Comput. Syst. (2020) Shin B. et al. A review and theoretical explanation of the ‘cyberthreat-intelligence (CTI) capability’that needs to be fostered in information security practitioners and how this can be accomplished Comput. Secur. (2020) Zhou Y. et al. CTI view: APT threat intelligence analysis system Secur. Commun. Netw. (2022) Zhang H. et al. EX-action: Automatically extracting threat actions from cyber threat intelligence report based on multimodal learning Secur. Commun. Netw. (2021) Barnum S. Standardizing cyber threat intelligence information with the structured threat information expression (stix) Mitre Corp. (2012) E.W. Burger, M.D. Goodman, P. Kampanakis, K.A. Zhu, Taxonomy model for cyber threat intelligence information exchange... Zhao W. et al. Designing a formal model facilitating collaborative information sharing for community cyber security C. Wagner, A. Dulaunoy, G. Wagener, A. Iklody, Misp: The design and implementation of a collaborative threat... Preuveneers D. et al. TATIS: Trustworthy APIs for threat intelligence sharing with UMA and CP-ABE Preuveneers D. et al. Distributed security framework for reliable threat intelligence sharing Secur. Commun. Netw. (2020) Homan D. et al. A new network model for cyber threat intelligence sharing using blockchain technology Marulli F. et al. A security-oriented architecture for federated learning in cloud environments Moubarak J. et al. On the dissemination of cyber threat intelligence through hyperledger View more references Cited by (0) Recommended articles (0) Tongtong Jiang was born in Liaoning, China in 1997. She received a bachelor’s degree in Computer Science and Technology of Guizhou University. And now, she is a graduate student in Guizhou University. Her long-established research activities are in the fields of data security and network security, especially in cyber threat intelligence. Guowei Shen received his Ph.D. degree from Harbin Engineering University. He is currently a professor of Guizhou University, Guiyang, Guizhou, China. His main research interests include big data, computer network and cyber security. Chun Guo received Ph.D. in information security from Beijing University of Posts and Telecommunications. He is currently a professor in the College of Computer Science and Technology, GuiZhou University, PR China. His research interests include data mining, intrusion detection and malware detection. Yunhe Cui received his Ph.D. degree from the Southwest Jiaotong University. He is currently an associate professor of Guizhou University, Guiyang, Guizhou, China. His research interests include DDoS detection and mitigation, intrusion detection and prevention, software-defined networking, traffic engineering, and data centers. BoXie received the M.S. degree in Computer Technology from Guizhou University, Guiyang, China, in 2022. He is currently pursuing the Ph.D. degree in communication engineering from South China Normal University, China. His current research interests include mobile edge computing and optimization, edge intelligence. View full text © 2023 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.comnet.2023.109604", "PubYear": 2023, "Volume": "224", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Public Big Data, School of Computer Science and Technology, Guizhou University, Guiyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Public Big Data, School of Computer Science and Technology, Guizhou University, Guiyang, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Public Big Data, School of Computer Science and Technology, Guizhou University, Guiyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Public Big Data, School of Computer Science and Technology, Guizhou University, Guiyang, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Electronics and Information Engineering, South China Normal University, Foshan, China"}], "References": [{"Title": "Secure and efficient outsourcing differential privacy data release scheme in Cyber–physical system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "1314", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A review and theoretical explanation of the ‘Cyberthreat-Intelligence (CTI) capability’ that needs to be fostered in information security practitioners and how this can be accomplished", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101761", "JournalTitle": "Computers & Security"}, {"Title": "Blockchain Based Information Sharing Mechanism for Cyber Threat Intelligence", "Authors": "Ebubekir BÜBER; Özgür Koray ŞAHİNGÖZ", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "242", "JournalTitle": "Balkan Journal of Electrical and Computer Engineering"}, {"Title": "DEALER: decentralized incentives for threat intelligence reporting and exchange", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "741", "JournalTitle": "International Journal of Information Security"}, {"Title": "A survey on federated learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106775", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Sharing Machine Learning Models as Indicators of Compromise for Cyber Threat Intelligence", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "140", "JournalTitle": "Cybersecurity"}, {"Title": "VREFL: Verifiable and Reconnection-Efficient Federated Learning in IoT scenarios", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "103486", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 106372957, "Title": "<PERSON><PERSON><PERSON><PERSON> type inequalities for nonlinear fractional Hamiltonian systems in the frame of conformable derivatives", "Abstract": "Lyapunov type integral inequality is one of the important inequalities in qualitative analysis of differential equations. In this paper, using properties of conformable derivatives and integral, and the solution of initial value problem for conformable fractional differential equation, some new L<PERSON><PERSON>nov type integral inequalities for higher order nonlinear fractional Hamiltonian systems are obtained, which improve and unify some known results in the literatures.", "Keywords": "<PERSON><PERSON><PERSON><PERSON> type inequality; fractional Hamiltonian system; conformable fractional derivative", "DOI": "10.3934/mfc.2023004", "PubYear": 2024, "Volume": "7", "Issue": "3", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Jining University, Qufu 273155, Shandong, China"}], "References": []}, {"ArticleId": 106373010, "Title": "Preserving Differential Privacy in Deep Learning Based on Feature Relevance Region Segmentation", "Abstract": "In the era of Big Data, deep learning techniques provide intelligent solutions for various problems in real-life scenarios. However, deep neural networks depend on large-scale datasets including sensitive data, which causes the potential risk of privacy leakage. In addition, various constantly evolving attack methods are also threatening the data security in deep learning models. Protecting data privacy effectively at a lower cost has become an urgent challenge. This article proposes an Adaptive Feature Relevance Region Segmentation (AFRRS) mechanism to provide differential privacy preservation. The core idea is to divide the input features into different regions with different relevance according to the relevance between input features and the model output. Less noise is intentionally injected into the region with stronger relevance, and more noise is injected into the regions with weaker relevance. Furthermore, we perturb loss functions by injecting noise into the polynomial coefficients of the expansion of the objective function to protect the privacy of data labels. Theoretical analysis and experiments have shown that the proposed AFRRS mechanism can not only provide strong privacy preservation for the deep learning model, but also maintain the good utility of the model under a given moderate privacy budget compared with existing methods.", "Keywords": "", "DOI": "10.1109/TETC.2023.3244174", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Tan", "Affiliation": "School of Computing, Engineering and the Built Environment, Edinburgh Napier University, Edinburgh, U.K."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, China"}], "References": [{"Title": "Understanding deep learning (still) requires rethinking generalization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "107", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 106373037, "Title": "Tailoring flow behavior and heat transfer in tempering channels for high-pressure die casting—analysis of potentials of commercial static mixers and prospects of additive manufacturing", "Abstract": "Additive manufacturing (AM) opens up manifold possibilities to influence the heat transfer between fluid and die in high-pressure die casting (HPDC), eventually allowing to minimize the total cycle time of the process. AM already has been exploited to establish near-contour temperature control systems in industrial applications. However, AM not only allows to influence the position of tempering channels in dies but it also allows to influence the fluid dynamics itself, e.g., by imprinted static mixers. Up to now, such flow-influencing mixing elements have not been considered in metal AM. In the present work, the impact of such metallic static mixers and most relevant processing conditions is investigated experimentally as well by computational fluid dynamics (CFD) simulation. In a first step, conventional static mixer elements are integrated into straight tempering channels to stimulate turbulences of the flowing tempering medium, finally resulting in an increase of the heat transfer up to 33%. In a second step, laser-based powder bed fusion of metals (PBF-LB/M) is applied to realize static mixers. Results obtained reveal that tempering channels without negative influences on the general flow behavior compared to conventional static mixers in straight tempering channels can be realized. In conclusion, the presented results show a positive impact on heat transfer and, thus, allow to further increase the economic efficiency of the HPDC process.", "Keywords": "High-pressure die casting (HPDC); Die tempering; Tool steel (AISI H13); Heat transfer coefficient (HTC); Selective laser melting (SLM); Conformal cooling", "DOI": "10.1007/s00170-023-10920-5", "PubYear": 2023, "Volume": "125", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Foundry Technology, University of Kassel, Kassel, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Materials Engineering, University of Kassel, Kassel, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Materials Engineering, University of Kassel, Kassel, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Foundry Technology, University of Kassel, Kassel, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Materials Engineering, University of Kassel, Kassel, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Materials Engineering, University of Kassel, Kassel, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Foundry Technology, University of Kassel, Kassel, Germany"}], "References": [{"Title": "On friction factor of fluid channels fabricated using selective laser melting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "4", "Page": "496", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "A machined substrate hybrid additive manufacturing strategy for injection moulding inserts", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "1-2", "Page": "577", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 106373111, "Title": "Introduction to the Special Issue on Vulnerabilities", "Abstract": "", "Keywords": "", "DOI": "10.1145/3580605", "PubYear": 2022, "Volume": "3", "Issue": "4", "JournalId": 74064, "JournalTitle": "Digital Threats: Research and Practice", "ISSN": "2576-5337", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Trento, Trento, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stony Brook University, New York, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Trento, Trento, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "SAP Security Research, Sophia-Antipolis, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Portsmouth, England, UK"}], "References": []}, {"ArticleId": 106373152, "Title": "Service Identification of TLS Flows Based on Handshake Analysis", "Abstract": "Identification of services constituting traffic from given IP network flows is important for many purposes such as management of quality of service, prevention of security problems, and providing a discounting service for customers only in accessing specified services like zero-rating service. The simplest methods for identifying these services are identifications based on IP addresses and port numbers. However, such methods are not sufficiently accurate and thus require improvement. Deep packet inspection (DPI) is an advanced method for improving the accuracy of identification. Many current IP flows are encrypted with the transport layer security (TLS) protocol. Therefore, an identification method cannot analyze almost all the data encrypted by TLS. In the cases of TLS 1.2 or less, some fields, e.g. server name indication (SNI), in the protocol header for the TLS session establishment are not encrypted and then can be analyzed. Thus, we can expect that the service can be identified from IP flows, which are composed of TLS ses-sions, by analyzing these fields. For achieving this, two challenges are mainly required. One is grouping TLS sessions by accesses from many TLS sessions that pass through a network element. The other is the identification of service from TLS sessions grouped in the first challenge. In our work, we mainly focus on the second theme, i.e., service identification from given TLS sessions. In our previous work, we proposed a method for identification by analyzing these non-encrypted data based on DPI and n-gram. However, there is room for improvement in identification accuracy because this method analyzed all the non-encrypted data including random values without protocol analysis. In this paper, we propose a new method for identifying the service from given TLS sessions based on SNI with protocol data unit (PDU) analysis. The proposed method clusters TLS sessions according to the value of SNI and identifies services from the occurrences of all groups. We evaluated the proposed method by identifying services on Google, Yahoo, and MSN sites, and the results showed that the proposed method could identify services more accurately than the existing method. The average ratios of inaccurate identifications were decreased by 65%, 72%, and 41% in our experiments of Google, Yahoo, and MSN services, respectively. © 2023 Information Processing Society of Japan.", "Keywords": "HTTPS; service identification; SNI; TLS", "DOI": "10.2197/ipsjjip.31.131", "PubYear": 2023, "Volume": "31", "Issue": "", "JournalId": 18954, "JournalTitle": "Journal of Information Processing", "ISSN": "", "EISSN": "1882-6652", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kogakuini University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Kogakuini University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kogakuini University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Tokyo"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ochanomizu University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kogakuini University"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Kogakuini University"}], "References": []}, {"ArticleId": 106373164, "Title": "Prevalence and Associated Risk Factors of 'Psychological, Financial and Career Effect' Among Bangladeshi Undergraduate Students in the COVID-19 Pandemic Situation", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijmsc.2022.04.02", "PubYear": 2022, "Volume": "8", "Issue": "4", "JournalId": 28282, "JournalTitle": "International Journal of Mathematical Sciences and Computing", "ISSN": "2310-9025", "EISSN": "2310-9033", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Jashore University of Science and Technology, Jashore-7408, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Sheikh <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106373188, "Title": "Searching for coherence in a fragmented field: Temporal and keywords network analysis in political science", "Abstract": "<p>In this paper, we answer the multiple calls for systematic analysis of paradigms and subdisciplines in political science—the search for coherence within a fragmented field. We collected a large dataset of over seven hundred thousand writings in political science from Web of Science since 1946. We found at least two waves of political science development, from behaviorism to new institutionalism. Political science appeared to be more fragmented than literature suggests—instead of ten subdisciplines, we found 66 islands. However, despite fragmentation, there is also a tendency for integration in contemporary political science, as revealed by co-existence of several paradigms and coherent and interconnected topics of the “canon of political science,” as revealed by the core-periphery structure of topic networks. This was the first large-scale investigation of the entire political science field, possibly due to newly developed methods of bibliometric network analysis: temporal bibliometric analysis and island methods of clustering. Methodological contribution of this work to network science is evaluation of islands method of network clustering against a hierarchical cluster analysis for its ability to remove misleading information, allowing for a more meaningful clustering of large weighted networks.</p>", "Keywords": "bibliometrics; political science", "DOI": "10.1017/nws.2022.39", "PubYear": 2023, "Volume": "11", "Issue": "1", "JournalId": 25796, "JournalTitle": "Network Science", "ISSN": "2050-1242", "EISSN": "2050-1250", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Notre Dame at Tantur, Jerusalem, Israel; Corresponding author."}, {"AuthorId": 2, "Name": "Valentina V. Kuskova", "Affiliation": "Lucy Family Institute for Data & Society, University of Notre Dame, 1220 Waterway Blvd, #H248, Indianapolis, IN 46202, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Lucy Family Institute for Data & Society, Department of Computer Science and Engineering, University of Notre Dame, 384E Nieuwland Science Hall, Notre Dame, IN 46556, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Lucy Family Institute for Data & Society, Department of Computer Science and Engineering, University of Notre Dame, 384E Nieuwland Science Hall, Notre Dame, IN 46556, USA"}], "References": []}, {"ArticleId": 106373219, "Title": "Debugging Complex with Simulation of Conductive Interference for Testing Automated Control System Controllers", "Abstract": "<p>The paper presents a full-featured simulation debugging system designed to test programmable controllers for automated process control systems in the laboratory. This system imitates the operating conditions of the automated process control system, as close as possible to the real operating conditions at the automation object. The presented system can form various levels of interference of input signals, inﬂuencing the automatic process control system with high-intensity network and impulse noise. The system also allows one to vary the parameters of the communication line. The structure of the conducted interference generator and the variator of the communication line parameters have been developed. A description of all elements necessary for modeling the main types of interference is given. In this work, an analysis of the existing electromagnetic interference in the signal circuits of process control systems was carried out, and the most typical interference was identifed. A block diagram of a conducted interference generator and a variator of communication line parameters has been developed. Both functional blocks are part of the modeling and debugging complex. They allow one to simulate the interference environment for the controller under test by introducing the generated interference into the signal communication lines in a conductive way. The structure of the adapter-former of interference for analog signals is worked out in detail with a description of its main components. Recommendations for choosing the element base are given. The practical signifcance of the performed work lies in the fact that it may improve the efciency of the complex of control and laboratory tests of the systems being created. This allows one to achieve a reduction in complexity and in setup time during implementation at the automation facility</p>", "Keywords": "simulation and debugging complex;electromagnetic interference;conducted interference shaper;communication line parameters simulator;interference shaper adapter;имитационный отладочный комплекс;электромагнитная помеха;формирователь кондуктивных помех;имитатор параметров линии связи;адаптер-формирователь помехи", "DOI": "10.25205/1818-7900-2022-20-3-14-28", "PubYear": 2023, "Volume": "20", "Issue": "3", "JournalId": 50054, "JournalTitle": "Vestnik NSU. Series: Information Technologies", "ISSN": "1818-7900", "EISSN": "2410-0420", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Federal Research Center for Information and Computational Technologies"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Federal Research Center for Information and Computational Technologies"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Technological Design Institute of Scientifc Instrument Engineering of Siberian Branch of RAS"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Federal Research Center for Information and Computational Technologies"}], "References": []}]