import urllib.request

url = 'https://www.baidu.com'

# url的组成
# http/htpps     www.baidu.com  80/443    s    wd = 周杰伦     #
#   协议            主机         端口号   路径     参数        锚点

# http       80
# https     443
# ftp        21
# mysql    3306
# oracle   1521

headers = {
    "User-Agent" : 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0'
}

# 因为urlopen方法中不能存储字典，所以需要借助Request方法
# 请求对象的定制
# 注意 因为参数顺序的问题，不能直接写url和headers，中间还有data，需要用关键字传参
request = urllib.request.Request(url=url,headers=headers)

response = urllib.request.urlopen(request)

content = response.read().decode('utf-8')

print(content)