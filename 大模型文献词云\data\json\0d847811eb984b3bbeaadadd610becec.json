[{"ArticleId": 113175869, "Title": "GraphQL response data volume prediction based on Code2Vec and AutoML", "Abstract": "Purpose \nGraphQL is a new Open API specification that allows clients to send queries and obtain data flexibly according to their needs. However, a high-complexity GraphQL query may lead to an excessive data volume of the query result, which causes problems such as resource overload of the API server. Therefore, this paper aims to address this issue by predicting the response data volume of a GraphQL query statement.\n \n \n Design/methodology/approach \nThis paper proposes a GraphQL response data volume prediction approach based on Code2Vec and AutoML. First, a GraphQL query statement is transformed into a path collection of an abstract syntax tree based on the idea of Code2Vec, and then the query is aggregated into a vector with the fixed length. Finally, the response result data volume is predicted by a fully connected neural network. To further improve the prediction accuracy, the prediction results of embedded features are combined with the field features and summary features of the query statement to predict the final response data volume by the AutoML model.\n \n \n Findings \nExperiments on two public GraphQL API data sets, GitHub and Yelp, show that the accuracy of the proposed approach is 15.85% and 50.31% higher than existing GraphQL response volume prediction approaches based on machine learning techniques, respectively.\n \n \n Originality/value \nThis paper proposes an approach that combines Code2Vec and AutoML for GraphQL query response data volume prediction with higher accuracy.", "Keywords": "GraphQL;Data volume prediction;Code2Vec;AutoML", "DOI": "10.1108/IJWIS-12-2023-0246", "PubYear": 2024, "Volume": "20", "Issue": "3", "JournalId": 23225, "JournalTitle": "International Journal of Web Information Systems", "ISSN": "1744-0084", "EISSN": "1744-0092", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology , Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology , Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology , Qingdao, China"}], "References": [{"Title": "Process mining based on patient waiting time: an application in health processes", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "5/6", "Page": "240", "JournalTitle": "International Journal of Web Information Systems"}, {"Title": "Efficient keyword search on graph data for finding diverse and relevant answers", "Authors": "Chang-Sup Park", "PubYear": 2023, "Volume": "19", "Issue": "1", "Page": "19", "JournalTitle": "International Journal of Web Information Systems"}]}, {"ArticleId": 113175905, "Title": "DESReg: Dynamic Ensemble Selection library for Regression tasks", "Abstract": "Nowadays, regression is a very demanded predictive task to solve a wide range of problems belonging to different research and society areas. Examples of applications include industry, economic, medical and energy fields. Ensemble methodology works by merging the output obtained from a set of base methods (learners), achieving successful results in both classification and regression tasks. Traditional ensembles use the output of the whole set of base methods, in a static way, to obtain the result of the ensemble. However, latest studies show that dynamic selection of learners or even dynamic aggregation of their outputs produce better results. Methodologies that integrate these techniques are called dynamic ensembles or dynamic ensemble selection. Although the literature and tools to work with dynamic ensembles for classification tasks is abundant, for regression tasks these resources are scarcer. This paper aims to mitigate these shortcomings by presenting a library for the design, development and execution of dynamic ensembles for regression problems. Specifically, the Python software package DESReg is presented. This library allows us to access to the latest dynamic ensemble techniques in the field, standing out for its high configurability, its support for extending it with user-defined functions or its parallel computation capabilities.", "Keywords": "Dynamic ensemble selection ; Regression ; Software package", "DOI": "10.1016/j.neucom.2024.127487", "PubYear": 2024, "Volume": "580", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Universidad de Jaén, 23071 Jaén, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Universidad de Jaén, 23071 Jaén, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Universidad de Jaén, 23071 Jaén, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Informatics, De Montfort University, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Universidad de Jaén, 23071 Jaén, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Universidad de Jaén, 23071 Jaén, Spain"}], "References": [{"Title": "MINE: A framework for dynamic regressor selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "157", "JournalTitle": "Information Sciences"}, {"Title": "Classifier ensemble methods in feature selection", "Authors": "<PERSON>kan Ez<PERSON>", "PubYear": 2021, "Volume": "419", "Issue": "", "Page": "97", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 113175940, "Title": "Augmented reality headsets for surgical guidance: the impact of holographic model positions on user localisation accuracy", "Abstract": "Novel augmented reality headsets such as HoloLens can be used to overlay patient-specific virtual models of resection margins on the patient’s skin, providing surgeons with information not normally available in the operating room. For this to be useful, surgeons wearing the headset must be able to localise virtual models accurately. We measured the error with which users localise virtual models at different positions and distances from their eyes. Healthy volunteers aged 20–59 years ( n  = 54) performed 81 exercises involving the localisation of a virtual hexagon’s vertices overlaid on a monitor surface. Nine predefined positions and three distances between the virtual hexagon and the users’ eyes (65, 85 and 105 cm) were set. We found that, some model positions and the shortest distance (65 cm) led to larger localisation errors than other positions and larger distances (85 and 105 cm). Positional errors of more than 5 mm and 1–5 mm margin errors were found in 29.8% and over 40% of cases, respectively. Strong outliers were also found (e.g. margin shrinkage of up to 17.4 mm in 4.3% of cases). The measured errors may result in poor outcomes of surgeries: e.g. incomplete tumour excision or inaccurate flap design, which can potentially lead to tumour recurrence and flap failure, respectively. Reducing localisation errors associated with arm reach distances between the virtual models and users’ eyes is necessary for augmented reality headsets to be suitable for surgical purposes. In addition, training surgeons on the use of these headsets may help to minimise localisation errors.", "Keywords": "Image marker; Augmented reality; Surgery; Surgical navigation; Augmented reality headsets; Skin tumour removal", "DOI": "10.1007/s10055-024-00960-x", "PubYear": 2024, "Volume": "28", "Issue": "2", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, Medical Sciences and Nutrition, University of Aberdeen, Aberdeen, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Princess Royal Hospital, Brighton and Sussex University Hospitals NHS Trust, Haywards Heath, UK; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Medicine, Medical Sciences and Nutrition, University of Aberdeen, Aberdeen, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Medicine, Medical Sciences and Nutrition, University of Aberdeen, Aberdeen, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Head and Neck Oncology Unit, Aberdeen Royal Infirmary (NHS Grampian), Aberdeen, UK"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Simulation and Visualisation, Glasgow School of Art, Glasgow, UK"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Medicine, Medical Sciences and Nutrition, University of Aberdeen, Aberdeen, UK"}], "References": []}, {"ArticleId": 113175941, "Title": "Hierarchical vector transformer vehicle trajectories prediction with diffusion convolutional neural networks", "Abstract": "In dynamic and interactive autonomous driving scenarios, accurately predicting the future movements of vehicle agents is crucial. However, current methods often fail to capture trajectory uncertainty, leading to limitations in trajectory prediction performance. To address these limitations, this paper introduces the hierarchical vector transformer diffusion model, a novel trajectory prediction method that prioritizes both speed and accuracy. The proposed model decomposes the traffic scene modeling into local patches and global interactions, allowing for the acquisition of relevant environmental and global information. Moreover, a local diffusion encoder is employed to effectively capture the aleatoric uncertainty. The proposed model utilizes an adaptive graph structure to exploit the spatial and temporal relationships inherent in the trajectory data. By employing a graph diffusion process, the model effectively captures dynamic features from the historical trajectory information. Moreover, the model demonstrates adaptability by dynamically adjusting to diverse trajectory data and scenarios, thereby enabling the generation of predicted trajectories that are uncertainty aware. This approach contributes to more effective and efficient modeling of dynamic autonomous driving scenarios. Experimental results demonstrate the superior speed and accuracy of the proposed method compared to existing approaches for trajectory prediction. The proposed method significantly enhances prediction accuracy, achieving results of ADE 0.68 and FDE 1.02 on the Argoverse dataset. In comparison to the baseline model, there are notable improvements in ADE and FDE by 0.03 and 0.06, respectively. It is noteworthy that this method also reduces the inference time by 7% when compared to the currently fastest method.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127526", "PubYear": 2024, "Volume": "580", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, 100081, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, Beijing, 100081, China"}], "References": [{"Title": "Multiple agents’ spatiotemporal data generation based on recurrent regression dual discriminator GAN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "468", "Issue": "", "Page": "370", "JournalTitle": "Neurocomputing"}, {"Title": "A novel end-to-end model for steering behavior prediction of autonomous ego-vehicles using spatial and temporal attention mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "490", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "An efficient Spatial–Temporal model based on gated linear units for trajectory prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "492", "Issue": "", "Page": "593", "JournalTitle": "Neurocomputing"}, {"Title": "Trajectory prediction for heterogeneous traffic-agents using knowledge correction data-driven model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "375", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 113175957, "Title": "Bayesian Quality-Diversity approaches for constrained optimization problems with mixed continuous, discrete and categorical variables", "Abstract": "Complex system design problems, such as those involved in aerospace engineering, require the use of numerically costly simulation codes in order to predict the performance of the system to be designed. In this context, these codes are often embedded into an optimization process to provide the best design while satisfying the design constraints. Recently, new approaches, called Quality-Diversity, have been proposed in order to enhance the exploration of the design space and to provide a set of optimal diversified solutions with respect to some feature functions. These functions are interesting to assess trade-offs. Furthermore, complex design problems often involve mixed continuous, discrete, and categorical design variables allowing to take into account technological choices in the optimization problem. Existing Bayesian Quality-Diversity approaches suited for intensive high-fidelity simulations are not adapted to mixed variables constrained optimization problems. In order to overcome these limitations, a new Quality-Diversity methodology based on mixed variables Bayesian optimization strategy is proposed in the context of limited simulation budget. Using adapted covariance models and dedicated enrichment strategy for the Gaussian processes in Bayesian optimization, this approach allows to reduce the computational cost up to two orders of magnitude, with respect to classical Quality-Diversity approaches while dealing with discrete choices and the presence of constraints. The performance of the proposed method is assessed on a benchmark of analytical problems as well as on two aerospace system design problems highlighting its efficiency in terms of speed of convergence. The proposed approach provides valuable trade-offs for decision-markers for complex system design.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108118", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ONERA/DTIS, Université Paris – Saclay, Palaiseau, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ONERA/DTIS, Université Paris – Saclay, Palaiseau, France"}], "References": [{"Title": "A particle swarm optimization algorithm for mixed-variable optimization problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100808", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Genetic Algorithms in the Fields of Artificial Intelligence and Data Sciences", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "4", "Page": "1007", "JournalTitle": "Annals of Data Science"}, {"Title": "Particle Swarm Optimization Algorithm and Its Applications: A Systematic Review", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "5", "Page": "2531", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "25 Years of Particle Swarm Optimization: Flourishing Voyage of Two Decades", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "3", "Page": "1663", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 113178183, "Title": "Attentional Tunneling in Pilots During a Visual Tracking Task With a Head Mounted Display", "Abstract": "Objective <p>We examined whether active head aiming with a Helmet Mounted Display (HMD) can draw the pilot’s attention away from a primary flight task. Furthermore, we examined whether visual clutter increases this effect.</p> Background <p>Head up display symbology can result in attentional tunneling, and clutter makes it difficult to identify objects.</p> Method <p>Eighteen military pilots had to simultaneously perform an attitude control task while flying in clouds and a head aiming task in a fixed-base flight simulator. The former consisted of manual compensation for roll disturbances of the aircraft, while the latter consisted of keeping a moving visual target inside a small or large head-referenced circle. A “no head aiming” condition served as a baseline. Furthermore, all conditions were performed with or without visual clutter.</p> Results <p>Head aiming led to deterioration of the attitude control task performance and an increase of the amount of roll-reversal errors (RREs). This was even the case when head aiming required minimal effort. Head aiming accuracy was significantly lower when the roll disturbances in the attitude control task were large compared to when they were small. Visual clutter had no effect on both tasks.</p> Conclusion <p>We suggest that active head aiming of HMD symbology can cause attentional tunneling, as expressed by an increased number of RREs and less accuracy on a simultaneously performed attitude control task.</p> Application <p>This study improves our understanding in the perceptual and cognitive effects of (military) HMDs, and has implications for operational use and possibly (re)design of HMDs.</p>", "Keywords": "attention;flight simulator;head mounted display;spatial disorientation;visual clutter", "DOI": "10.1177/00187208241236395", "PubYear": 2025, "Volume": "67", "Issue": "1", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TNO Human Factors, Soesterberg, The Netherlands;University of Amsterdam, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "TNO Human Factors, Soesterberg, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "TNO Human Factors, Soesterberg, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "TNO Human Factors, Soesterberg, The Netherlands"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "TNO Human Factors, Soesterberg, The Netherlands"}], "References": [{"Title": "Leans Illusion in Hexapod Simulator Facilitates Erroneous Responses to Artificial Horizon in Airline Pilots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "6", "Page": "962", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}]}, {"ArticleId": 113178281, "Title": "Visual Communication Design Method in Folk Art Based on Multimedia Data Transmission Technology: ", "Abstract": "<p>With the rapid development of science and technology and China's economy, the internet, big data, computers, and multimedia technology are widely used in all walks of life to promote the application and improvement of visual communication design concepts, the gradual implementation of multimedia technology, and the continuous improvement of innovation. In this paper, from the multimedia transmission technology based on network coding, some key technologies in the network communication of data and video streams are studied in depth and, based on the transmission-quality assessment model, the working effect of the jitter-buffering algorithm and WebRTC jitter-buffering algorithm is compared in different network environments. The experimental results show that the jitter-buffering algorithm proposed in this paper has better working effect. This research is of great significance for the realization of multimedia transmission technology in next-generation networks.</p>", "Keywords": "", "DOI": "10.4018/IJISSCM.338383", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 25754, "JournalTitle": "International Journal of Information Systems and Supply Chain Management", "ISSN": "1935-5726", "EISSN": "1935-5734", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Xinyang Agriculture and Forestry University, China & Cheongju University, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Cheongju University, South Korea"}], "References": [{"Title": "Fog-Based Delay-Sensitive Data Transmission Algorithm for Data Forwarding and Storage in Cloud Environment for Multimedia Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "2", "Page": "128", "JournalTitle": "Big Data"}, {"Title": "Optimized multimedia data through computationally intelligent algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "5", "Page": "2961", "JournalTitle": "Multimedia Systems"}, {"Title": "An intelligent sustainable efficient transmission internet protocol to switch between User Datagram Protocol and Transmission Control Protocol in IoT computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "5", "Page": "e13129", "JournalTitle": "Expert Systems"}, {"Title": "A Survey on Virtual Network Functions for Media Streaming: Solutions and Future Challenges", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "11", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "An efficient Sheep Flock Optimization-based hybrid deep RaNN for secure and enhanced video transmission quality", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "11", "Page": "8065", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Smart algorithm in wireless networks for video streaming based on adaptive quantization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "9", "Page": "e7633", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Information Visualization Based on Visual Transmission and Multimedia Data Fusion", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "3", "Page": "1", "JournalTitle": "International Journal of Information Technologies and Systems Approach"}, {"Title": "Design of Visual Communication Teaching System Based on Artificial Intelligence and CAD Technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "90", "JournalTitle": "Computer-Aided Design and Applications"}, {"Title": "Analysis of Application and Creation Skills of Story-Based MV Micro Video and Big Multimedia Data in Music Communication", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Information Technology and Web Engineering"}]}, {"ArticleId": 113178327, "Title": "Light-Responsive Hydrogel Microcrawlers, Powered and Steered with Spatially Homogeneous Illumination", "Abstract": "<p>Sub-millimeter untethered locomoting robots hold promise to radically change multiple areas of human activity such as microfabrication/assembly or health care. To overcome the associated hurdles of such a degree of robot miniaturization, radically new approaches are being adopted, often relying on soft actuating polymeric materials. Here, we present light-driven, crawling microrobots that locomote by a single degree of freedom actuation of their light-responsive tail section. The direction of locomotion is dictated by the robot body design and independent of the spatial modulation of the light stimuli, allowing simultaneous multidirectional motion of multiple robots. Moreover, we present a method for steering such robots by reversibly deforming their front section, using ultraviolet (UV) light as a trigger. The deformation dictates the robot locomotion, performing right- or left-hand turning when the UV is turned on or off respectively. The robots' motion and navigation are not coupled to the position of the light sources, which enables simultaneous locomotion of multiple robots, steering of robots and brings about flexibility with the methods to deliver the light to the place of robot operation.</p>", "Keywords": "collective;hydrogel;light-driven;microrobot;photothermal actuation", "DOI": "10.1089/soro.2023.0074", "PubYear": 2024, "Volume": "11", "Issue": "3", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Chemical Engineering, Department of Chemical Engineering, University of Chemistry and Technology Prague, Prague, Czech Republic."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Chemical Engineering, Department of Chemical Engineering, University of Chemistry and Technology Prague, Prague, Czech Republic."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Polytechnic Department of Engineering and Architecture, University of Udine, Udine, Italy.;Department of Decision-Making Theory, Institute of Information Theory and Automation of the Czech Academy of Sciences, Prague, Czech Republic."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Synthetic Nanochemistry, Institute of Organic Chemistry and Biochemistry of the Czech Academy of Sciences, Prague, Czech Republic."}, {"AuthorId": 5, "Name": "Frantisek Stepanek", "Affiliation": "Faculty of Chemical Engineering, Department of Chemical Engineering, University of Chemistry and Technology Prague, Prague, Czech Republic."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Chemical Engineering, Department of Chemical Engineering, University of Chemistry and Technology Prague, Prague, Czech Republic.;Synthetic Nanochemistry, Institute of Organic Chemistry and Biochemistry of the Czech Academy of Sciences, Prague, Czech Republic."}], "References": [{"Title": "Photoresponsive Hydrogel Microcrawlers Exploit Friction Hysteresis to Crawl by Reciprocal Actuation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "10", "JournalTitle": "Soft Robotics"}, {"Title": "Ingenious humidity-powered micro-worm with asymmetric biped from single hydrogel", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "322", "Issue": "", "Page": "128620", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Fundamental Modes of Swimming Correspond to Fundamental Modes of Shape: Engineering I‐, U‐, and S‐Shaped Swimmers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "11", "Page": "2100068", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "An automated platform for assembling light-powered hydrogel microrobots and their subsequent chemical binding", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "", "Page": "101446", "JournalTitle": "Journal of Computational Science"}, {"Title": "Microscopic robots with onboard digital control", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "70", "Page": "eabq2296", "JournalTitle": "Science Robotics"}, {"Title": "Hydrogel Microrobots Self‐Assembled into Ordered Structures with Programmable Actuation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "9", "Page": "2300096", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 113182236, "Title": "FAM: Improving columnar vision transformer with feature attention mechanism", "Abstract": "Vision Transformer has garnered outstanding performance in visual tasks due to its capability for global modeling of image information. However, during the self-attention computation of image tokens, a common issue of attention map homogenization arises, impacting the final performance of the model as attention maps propagate through feature maps layer by layer. In this research, we propose a token-based approach to adjust the output of attention sub-layer, focusing on the feature dimensions, to address the homogenization problem. Furthermore, different network architectures exhibit variations in their approaches to modeling image features. Specifically, Vision Transformers excel at modeling long-range relationships, while convolutional neural networks possess local receptive fields. Therefore, this paper introduces a plug-and-play convolutional operator-based component, integrated into the Vision Transformer, to validate the impact of structural enhancements on model performance. Experimental results on image recognition and adversarial attack tasks respectively demonstrate the effectiveness and robustness of the two proposed methods. Additionally, the analysis of information entropy on the feature maps of the model’s final layer indicates that the improved model exhibits higher information richness, making it more conducive to the classifier’s discriminative capabilities.", "Keywords": "", "DOI": "10.1016/j.cviu.2024.103981", "PubYear": 2024, "Volume": "242", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130000, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, Jilin University, Changchun 130000, China"}, {"AuthorId": 2, "Name": "Xingyu Bai", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130000, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mathematical and Computer Sciences, Heriot-Watt University, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130000, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, Jilin University, Changchun 130000, China;Corresponding author at: College of Computer Science and Technology, Jilin University, Changchun 130000, China"}], "References": []}, {"ArticleId": 113182263, "Title": "A non-probabilistic convex modelling framework for uncertainty quantification of laser powder bed fusion fabricated structures based on limited data", "Abstract": "Laser powder bed fusion (LPBF) is increasingly prominent in essential fields such as aerospace. However, due to the characteristics of the manufacturing process and the high test cost, the performance of fabricated structures is inherently uncertain, leading to a challenging characterisation of their performance. This paper proposes a non-probabilistic convex modelling framework involving confirming uncertainties, processing data, and establishing a non-probabilistic convex model, to quantitatively describe the uncertainties concerning the physical properties of structures fabricated by LPBF. Firstly, the non-probabilistic convex modelling framework is proposed and an efficient uncertainty quantification method is developed utilising the non-probabilistic convex model. Then, criteria are set up for evaluating the performance of the developed method, and computational efficiency and accuracy are illustrated via benchmark numerical examples. Last but not least, two types of representative structures, solid and lattice structures, are fabricated via the LPBF method. The physical properties are tested through tensile and compression experiments. By confirming the necessity of accounting for uncertainties with limited data, we employing the proposed framework to the real representative specimens fabricated via the LPBF method, demonstrating that the structures fabricated by LPBF have substantial uncertainty and the proposed framework is practical.", "Keywords": "Non-probabilistic modelling ; convex model ; uncertainty quantification ; evaluation criterion ; laser powder bed fusion", "DOI": "10.1080/********.2024.2324429", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Anhui Provincial Engineering Research Center of Intelligent Agricultural Machinery, School of Engineering, Anhui Agricultural University, Hefei, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electric Engineering, Guangzhou University, Guangzhou, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electric Engineering, Guangzhou University, Guangzhou, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Innovation Center for Equipment Lightweight Design and Manufacturing, School of Mechanical Engineering, Yanshan University, Qinhuangdao, People’s Republic of China"}], "References": [{"Title": "Multi-output Gaussian process prediction for computationally expensive problems with multiple levels of fidelity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107151", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A review on qualification and certification for metal additive manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "2", "Page": "382", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "A time-variant reliability analysis framework for selective laser melting fabricated lattice structures with probability and convex hybrid models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "4", "Page": "841", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Mechanical properties characterisation of metallic components produced by additive manufacturing using miniaturised specimens", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "1", "Page": "2161400", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Dimensional effect and mechanical performance of node-strengthened hybrid lattice structure fabricated by laser powder bed fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "1", "Page": "e2240306", "JournalTitle": "Virtual and Physical Prototyping"}]}, {"ArticleId": 113182284, "Title": "Intelligent computer technology and its application in environmental art design", "Abstract": "To improve the effect of artistic environment design, this paper applies intelligent computer technology to environmental art design, and combines intelligent computer technology to provide mathematical analysis tools required for pre-computing optical energy transmission technology. Moreover, this paper discusses the diffuse and specular reflections at low frequencies, and conducts a detailed study of the rendering algorithm of the pre-computed radiance transfer (PRT) technology. In order to reconstruct the approximate function, this paper considers the opposite process of projection, and the SH function with scaled coefficients can be accumulated to approximate the original function. In addition, this paper constructs a simulation system for environmental art design. It is not difficult to see from the design evaluation results that the environmental art design system proposed in this paper can effectively improve the effect of environmental art design. This unifies the functionality of the colouring system, enabling people to handle more types of elements and flexibly utilise graphic pipelines. Copyright © The Author(s) 2023. Published by Inderscience Publishers Ltd.", "Keywords": "computer technology; design; environmental art; simulation", "DOI": "10.1504/IJICT.2024.137222", "PubYear": 2024, "Volume": "24", "Issue": "2", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Wuchang Institute of Technology, Hubei Province, Wuhan City, 430065, China"}], "References": []}, {"ArticleId": *********, "Title": "Empirical Analysis for Classification of Fake News through Text Representation", "Abstract": "<p>Fake news refers to inaccurate or deceptive information that is portrayed as legitimate news. It is intentionally generated and disseminated to mislead the public. Fake news takes on multiple forms, including altered visuals, invented narratives, and misrepresented accounts of actual occurrences, although this work focuses solely on textual content. Initially, the focus of this work is to evaluate various pre-processing techniques involved in fake news detection, such as TF-IDF, GloVe, and Integer Encoding. Each of these techniques has its own way of converting text to numerical format. Despite numerous studies in this field, there is still a research gap regarding the comparative analysis of TF_IDF (Term Frequency Inverse Document Frequency), Integer Encoding, and GloVe (Global Vector for Word Representation) specifically for fake news tasks. This study aims to bridge this gap by evaluating and comparing the performance of these three popular preprocessing techniques. Next, three RNN variants are used in this experiment for the classification task. They are SimpleRNN (Simple Recurrent Neural Network), LSTM (Long Short-Term Memory) and GRU (Gated Recurrent Unit). The reason behind choosing RNN variants is RNN is capable of capturing long term dependencies. It is proven to be effective in handling sequential data. It consists of memory that stores the previous important content. GloVe showed high accuracy in GRU model, and it also used only less computational resources, but LSTM took more time and required more computational resources. The results produced by GRU and LSTM for GloVe were better than the rest of the combinations. Integer Encoding also produced good results. But TF-IDF gives poor results when fed to Deep Learning models like RNN, LSTM, and GRU, but when it is fed to Machine Learning Model it gives good accuracy. This is due to sparse matrix generation based on the importance of term frequency. The findings highlight the advantages and limitations of each algorithm, providing valuable guidance for researchers and practitioners in choosing the suitable method for their specific needs. The experimental finding of this work is that GloVe with GRU produces the highest accuracy of 92.15%</p>", "Keywords": "", "DOI": "10.36548/jitdw.2024.1.003", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 71072, "JournalTitle": "Journal of Information Technology and Digital World", "ISSN": "", "EISSN": "2582-418X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Santhi V", "Affiliation": ""}, {"AuthorId": 3, "Name": "Madhumitha N H", "Affiliation": ""}], "References": [{"Title": "An Empirical Comparison of Fake News Detection using different Machine Learning Algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "146", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Linguistic feature based learning model for fake news detection and classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114171", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fake news detection: A hybrid CNN-RNN based deep learning approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100007", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Covid-19 fake news sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> khan", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "107967", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Fake news detection on Pakistani news using machine learning and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118558", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hybrid fake news detection technique with genetic search and deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "108344", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A Fake News Detection System based on Combination of Word Embedded Techniques and Hybrid Deep Learning Model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Bouchaib CHERRADI; Soufiane HAMIDA", "PubYear": 2022, "Volume": "13", "Issue": "10", "Page": "525", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Content-Based Fake News Detection With Machine and Deep Learning: a Systematic Review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "530", "Issue": "", "Page": "91", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 113183553, "Title": "Analysis of performance and trust in load balancing algorithm in cloud computing environment", "Abstract": "Load balancing is a technique to improve the performance of virtual machines in a cloud environment. The load balancing algorithm tries to divide the total workload fairly among all the nodes, to ensure the optimum use of available resources. There exists many load balancing techniques in cloud computing environment. Many researchers have suggested different techniques to solve the load balancing problem. This research paper is a simulated analysis of selected existing load balancing algorithms in a cloud computing environment. In this simulated analysis, various load balancing metric parameters were used to evaluate the performance and trust of existing load balancing algorithms. Ultimate aim of this paper is to help in the design of new algorithms by analysing the merits and demerits of various existing algorithms. Copyright © 2024 Inderscience Enterprises Ltd.", "Keywords": "algorithm; analysis; cloud computing; cloudsim; dynamic; load balancing; performance metrics; trust; virtual machine", "DOI": "10.1504/IJAIP.2024.137185", "PubYear": 2024, "Volume": "27", "Issue": "2", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dr. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Technical University, Uttar Pradesh, Lucknow, 226021, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Engineering and Rural Technology, Uttar Pradesh, Allahabad, 211002, India"}], "References": []}, {"ArticleId": 113183626, "Title": "An interpretable lightweight deep network with ℓ p ( 0 < p < 1 ) model-driven for single image super-resolution", "Abstract": "In order to address the expensive computation cost of deep networks, some Single Image Super-Resolution (SISR) methods tried to design the lightweight networks by means of recursion or expert prior. However, they discuss the theoretical interpretation for the network’s design less. To address this issue, we propose a novel method for constructing an interpretable lightweight deep network for SISR by fusing the idea of model-driven and data-driven. That is, we give a theoretical interpretation for the lightweight network’s design from the optimization model of image degeneration. Considering that ℓ p ( 0 &lt; p &lt; 1 ) -norm is sparser than the ℓ 1 -norm and can describe the noises of image degeneration better, our proposed SISR method firstly deduces an iteration algorithm from the ℓ p ( 0 &lt; p &lt; 1 ) degeneration model. Then according to this theoretical deduction, an effective deep network is designed. Since our proposed deep network is designed according to the iteration algorithm, our network can not only realize the lightweight structure because of the weight sharing decided by the iterative principle, but also show a theoretical interpretation for designing the deep network. Extensive experimental results illustrate that our proposed method is superior to some related popular SISR methods with the lightweight structure.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127521", "PubYear": 2024, "Volume": "580", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, China Jiliang University, Hangzhou, 310018, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, China Jiliang University, Hangzhou, 310018, China;Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Data Science, Zhejiang University of Finance & Economics, Hangzhou 310018, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, China Jiliang University, Hangzhou, 310018, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, China Jiliang University, Hangzhou, 310018, China;Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}], "References": [{"Title": "L1 model-driven recursive multi-scale denoising network for image super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "225", "Issue": "", "Page": "107115", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 113183627, "Title": "Recognizing Geometric Intersection Graphs Stabbed by a Line", "Abstract": "In this paper, we determine the computational complexity of recognizing two graph classes, grounded L -graphs and stabbable grid intersection graphs. An L -shape is made by joining the bottom end-point of a vertical (|) segment to the left end-point of a horizontal (−) segment. The top end-point of the vertical segment is known as the anchor of the L -shape. Grounded L -graphs are the intersection graphs of L -shapes such that all the L -shapes&#x27; anchors lie on the same horizontal line. We show that recognizing grounded L -graphs is NP -complete. This answers an open question asked by <PERSON><PERSON><PERSON><PERSON> &amp; <PERSON> (Electron. J. Comb., 2019). Grid intersection graphs are the intersection graphs of axis-parallel line segments in which two vertical (similarly, two horizontal) segments cannot intersect. We say that a (not necessarily axis-parallel) straight line ℓ stabs a segment s , if s intersects ℓ . A graph G is a stabbable grid intersection graph ( ) if there is a grid intersection representation of G in which the same line stabs all its segments. We show that recognizing graphs is NP -complete, even on a restricted class of graphs. This answers an open question asked by <PERSON><PERSON><PERSON> et al. ( O rder, 2018).", "Keywords": "", "DOI": "10.1016/j.tcs.2024.114488", "PubYear": 2024, "Volume": "995", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, University of Leeds, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Security, Theory & Algorithmic Research (CSTAR), International Institute of Information Technology, Hyderabad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nantes Université, École Centrale Nantes, CNRS, LS2N, UMR 6004, F-44000 Nantes, France;Corresponding author"}], "References": [{"Title": "On the complexity of recognizing <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> Point-Tolerance graphs", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "952", "Issue": "", "Page": "113773", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 113183633, "Title": "Comparing multi-class classifier performance by multi-class ROC analysis: A nonparametric approach", "Abstract": "The area under the Receiver Operating Characteristic (ROC) curve (AUC) is a standard metric for quantifying and comparing binary classifiers. Real world applications often require classification into multiple (more than two) classes. For multi-class classifiers that produce class membership scores, a popular multi-class AUC (MAUC) variant is to average the pairwise AUC values (<PERSON> and <PERSON>, 2001). Due to the complicated correlation patterns, the variance of MAUC is often estimated numerically using resampling techniques. This work is a generalization of <PERSON><PERSON><PERSON>’s nonparameteric approach for binary AUC analysis (<PERSON><PERSON><PERSON> et al., 1988) to MAUC. We first derive the closed-form expression of the covariance matrix of the pairwise AUCs within a single MAUC. Then by dropping higher order terms, we obtain an approximate covariance matrix with a compact, matrix factorization form, which then serves as the basis for variance estimation of a single MAUC. We further extend this approach to estimate the covariance of correlated MAUCs that arise from multiple competing classifiers. For the special case of binary correlated AUCs, our results coincide with that of <PERSON><PERSON><PERSON>. Our numerical studies confirm the accuracy of the variance and covariance estimates. We provide the source code of the proposed covariance estimation of correlated MAUCs on GitHub ( https://tinyurl.com/euj6wvsz ) for its easy adoption by machine learning and statistical analysis packages to quantify and compare multi-class classifiers.", "Keywords": "Ustatistics;area under the ROC curve (AUC);jackknife;multi-class AUC;multi-class classification;receiver operating characteristic (ROC)", "DOI": "10.1016/j.neucom.2024.127520", "PubYear": 2024, "Volume": "583", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiology, Johns Hopkins University, MD, USA"}], "References": [{"Title": "Deep open-set recognition for silicon wafer production monitoring", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108488", "JournalTitle": "Pattern Recognition"}, {"Title": "AUC Maximization in the Era of Big Data and AI: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "On the bias in the AUC variance estimate", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "178", "Issue": "", "Page": "62", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 113183683, "Title": "Development and research of an automatic control system for thermal processes of a biogas installation", "Abstract": "<p>An algorithm for multi-position temperature control at the stage of preheating raw materials supplied to the digestion bioreactor of a biogas installation is considered. Transient processes in the heating process control system using tubular electric heaters with various powers are studied. The purpose of the work is to ensure the specified temperature of the raw material at the stage of its preheating before loading into the bioreactor-methane tank. This purpose achieved by ensuring the requirements relative to quality indicators of thermal process control, the time of temperature rise in the transient mode and the accuracy of temperature stabilization in a steady state under various external disturbing influences. Keywords biogas installation, temperature mode, multi-position controller, tubular electric heater, control system</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2024-78-2-54-60", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 113185542, "Title": "Fuzzy C-Means Algorithm Based on Density Canopy and Manifold Learning", "Abstract": "", "Keywords": "", "DOI": "10.32604/csse.2023.037957", "PubYear": 2024, "Volume": "48", "Issue": "3", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Improved fuzzy C-means algorithm based on density peak", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "545", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Hybrid Approach for Sentiment Analysis of Twitter Posts Using a Dictionary-based Approach and Fuzzy Logic Methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "1", "Page": "116", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "A novel fuzzy clustering algorithm based on rough set and inhibitive factor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "e6078", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Optimization of Fuzzy C-Means Clustering Algorithm with Combination of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> Distance Using Principal Component Analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "1", "Page": "139", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Gaussian Collaborative Fuzzy C-Means Clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "7", "Page": "2218", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Research on the Construction of Intelligent Community Emergency Service Platform Based on Convolutional Neural Network", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "Improved anti-occlusion object tracking algorithm using Unscented Rauch-Tung-Striebel smoother and kernel correlation filter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "6008", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Applications of dynamic feature selection and clustering methods to medical diagnosis", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "109293", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 113185598, "Title": "RETRACTED ARTICLE: Analysis and Research of Psychological Crisis Behavior Model Based on Improved Apriori Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2320981", "PubYear": 2024, "Volume": "40", "Issue": "18", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang College of Security Technology, Wenzhou, PR China"}], "References": [{"Title": "RETRACTED ARTICLE: Hybrid method for mining rules based on enhanced Apriori algorithm with sequential minimal optimization in healthcare industry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10597", "JournalTitle": "Neural Computing and Applications"}, {"Title": "PIRAP: Medical Cancer Rehabilitation Healthcare Center Data Maintenance Based on IoT-Based Deep Federated Collaborative Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Cooperative Information Systems"}]}, {"ArticleId": 113185622, "Title": "A general purpose parallel Fortran code for grid projected concentration reconstruction from multidimensional particle distributions", "Abstract": "Particle-based transport models are widely used for simulating the movement of pollutants in environmental systems. Unlike grid-based methods, particles naturally address advective transport without numerical dispersion. However, these models require concentration reconstruction from the discrete particle information. This is computationally demanding in multidimensional problems, posing a challenge for field-scale models requiring frequent reconstruction. Grid Projected Kernel Density Estimation (GPKDE) is a cell-averaged reconstruction with improvements in computational performance compared to classical KDE. Currently, no programs implementing this method are readily integrated into particle simulators, compatible with three-dimensional domains, and particles with unequal weights. This article introduces a Fortran code for general-purpose GPKDE, with modular functionalities facilitating the integration into external software. The program implements locally adaptive kernel bandwidth selection and alternatives for the reconstruction from particles with non-uniform weights. The code is parallelized with the OpenMP library. Numerical test cases demonstrate the program’s applicability and scalability of the parallel implementation.", "Keywords": "Grid-projected ; Kernel ; Parallel ; Particles ; Fortran", "DOI": "10.1016/j.envsoft.2024.106008", "PubYear": 2024, "Volume": "175", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hydrogeology Group (UPC-CSIC), Barcelona, Spain;Department of Civil and Environmental Engineering (DECA), Universitat Politècnica de Catalunya, Jordi Girona 3-4, 08034, Barcelona, Spain;Corresponding author at: Department of Civil and Environmental Engineering (DECA), Universitat Politècnica de Catalunya, Jordi Girona 3-4, 08034, Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hydrogeology Group (UPC-CSIC), Barcelona, Spain;Department of Civil and Environmental Engineering (DECA), Universitat Politècnica de Catalunya, Jordi Girona 3-4, 08034, Barcelona, Spain"}], "References": [{"Title": "Random-walk simulation of non-conservative pollutant transport in shallow water flows", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "104870", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Aurora: A non-Fickian (and Fickian) particle tracking package for modeling groundwater contaminant transport with MODFLOW", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "104871", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Distributed memory parallel groundwater modeling for the Netherlands Hydrological Instrument", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "143", "Issue": "", "Page": "105092", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 113186276, "Title": "Hybridizing WOA with PSO for coordinating material handling equipment in an automated container terminal considering energy consumption", "Abstract": "Automated container terminals (ACTs) represent state-of-the-art facilities for container handling and are a current development trend. However, enhancing their operational efficiency while minimizing energy consumption remains a challenge. While metaheuristics are helpful in addressing container terminal problems, their capabilities are limited when used alone to tackle complex or integrated problems. Hybrid models can be more effective in facing these challenges. This research proposes a hybrid model, termed WOA + PSO, which combines the Whale Optimization Algorithm (WOA) and Particle Swarm Optimization (PSO) as a novel approach to address the integrated scheduling problem of automated quay cranes (AQCs), automated lift vehicles (ALVs), and automated stacking cranes (ASCs) in an ACT. Additionally, the WOA + PSO collaborates with a simulation model in a framework to become a simulation-based optimization approach. The performance of WOA + PSO is evaluated by comparing it with its base models, WOA and PSO, as well as a genetic algorithm (GA), through extensive experiments. The results show that WOA + PSO outperforms the others in achieving the objective of balancing operational efficiency and energy consumption.", "Keywords": "", "DOI": "10.1016/j.aei.2024.102410", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>sien<PERSON><PERSON><PERSON>", "Affiliation": "Department of Supply Chain Management, National Kaohsiung University of Science and Technology, Kaohsiung 81157, Taiwan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Kaohsiung University of Science and Technology, Kaohsiung 80778, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Logistics and Supply Chain Management, Hong Bang International University, Ho Chi Minh 72320, Viet Nam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Logistics and Supply Chain Management, Hong Bang International University, Ho Chi Minh 72320, Viet Nam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Supply Chain Management, National Kaohsiung University of Science and Technology, Kaohsiung 81157, Taiwan"}], "References": [{"Title": "Quay crane scheduling in automated container terminal for the trade-off between operation efficiency and energy consumption", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101285", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Scheduling of collaborative operations of yard cranes and yard trucks for export containers using hybrid approaches", "Authors": "Hsien<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101292", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Scheduling Optimization for Twin ASC in an Automated Container Terminal Based on Graph Theory", "Authors": "<PERSON><PERSON> Xu; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Advances in Multimedia"}, {"Title": "An improved whale optimization algorithm based on multi-population evolution for global optimization and engineering design problems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119269", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved African vultures optimization algorithm using different fitness functions for multi-level thresholding image segmentation", "Authors": "Farhad <PERSON> Gharehchopogh; Turgay <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "6", "Page": "16929", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A multi-objective mutation-based dynamic Harris Hawks optimization for botnet detection in IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "24", "Issue": "", "Page": "100952", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 113186294, "Title": "Multi-source heterogeneous data storage methods for omnimedia data space", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJGUC.2023.10062784", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 19428, "JournalTitle": "International Journal of Grid and Utility Computing", "ISSN": "1741-847X", "EISSN": "1741-8488", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 113186332, "Title": "MRSLN: A Multimodal Residual Speaker-LSTM Network to alleviate the over-smoothing issue for Emotion Recognition in Conversation", "Abstract": "Multimodal Emotion Recognition in Conversation (ERC) plays a significant role in the field of human–computer intelligent interaction since it enables computers to perceive and infer the emotions expressed by the individuals, thereby intelligently responding to them. Most of current ERC methods pay more attention to modeling the complex interaction between different modalities. However, the features extracted by their unimodal networks are over-smoothed and may contain insufficient intra-speaker contextual information, which results in suboptimal results. In this paper, we focus on the unimodal learning and propose a simple late fusion framework named Multimodal Residual Speaker-LSTM Network (MRSLN), which uses speaker information to directly model inter-speaker and intra-speaker dependency, rather than fuse it into the learned features. MRSLN uses the speaker-LSTM consisting of the inter-speaker LSTM, intra-speaker LSTM, and the residual network between the input and output of the inter-speaker LSTM. Our proposed method can alleviate the issue of over-smoothing in deep Long Short Term Memory (LSTM) network and also incorporate additional intra-speaker contextual information. Extensive experiments conducted on IEMOCAP and MELD datasets demonstrate that MRSLN effectively captures inter-speaker and intra-speaker information and outperforms currently complex state-of-the-art (SOTA) models in efficiency and classification performance.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127467", "PubYear": 2024, "Volume": "580", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, 221000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, 221000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, 221000, China;Corresponding author"}], "References": [{"Title": "GraphMFT: A graph network based multimodal fusion technique for emotion recognition in conversation", "Authors": "<PERSON>; <PERSON><PERSON>; Guoqing Lv", "PubYear": 2023, "Volume": "550", "Issue": "", "Page": "126427", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 113190242, "Title": "Efficient feature redundancy reduction for image denoising", "Abstract": "<p>It is challenging to deploy convolutional neural networks (CNNs) for image denoising on low-power devices which can suffer from computational and memory constraints. To address this limitation, a simple yet effective and efficient feature redundancy reduction-based network (FRRN) is proposed in this paper, which integrates a feature refinement block (FRB), an attention fusion block (AFB), and an enhancement block (EB). Specifically, the FRB distills structural information via two parallel sub-networks, selecting representative feature representations while suppressing spatial-channel redundancy. The AFB absorbs an attentive fusion mechanism to facilitate diverse features extracted from two sub-networks, emphasizing texture and structure details but alleviating harmful features from problematic regions. The subsequent EB further boosts the feature representation abilities. Aiming to enhance denoising performance at both pixel level and semantic level, a multi-loss scheme comprising three popular loss functions is leveraged to improve the robustness of the denoiser. Comprehensive quantitative and qualitative analyses demonstrate the superiority of the proposed FRRN.</p>", "Keywords": "Spatial-channel adaptive; Attentive fusion mechanism; Multi-loss scheme; Image denoising", "DOI": "10.1007/s11280-024-01258-3", "PubYear": 2024, "Volume": "27", "Issue": "2", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, China; Research & Development Institute, Northwestern Polytechnical University, Shenzhen, China; Yangtze River Delta Research Institute, Northwestern Polytechnical University, Taicang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China; Corresponding author."}], "References": [{"Title": "Lightweight image super-resolution with enhanced CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106235", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep recursive network for image denoising with global non-linear smoothness constraint prior", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "426", "Issue": "", "Page": "147", "JournalTitle": "Neurocomputing"}, {"Title": "A robust deformed convolutional neural network (CNN) for image denoising", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "2", "Page": "331", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Multi-stage image denoising with the wavelet transform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109050", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 113190280, "Title": "Design and implementation of a precision levelling composite stage with active passive vibration isolation", "Abstract": "The white-light-interference (WLI) three-dimensional topography fine detection has high requirements for the chip-loading stage on levelling and vibration isolation capacity, to ensure that the micro-meter-level structured chip is stably aligned with the WLI scanning head. To meet the high performance detection requirements, this paper designs a novel three-axis levelling and active-passive vibration isolation (LAPV) composite stage, in order to perform precision tilt levelling and wide-frequency-domain vibration isolation. The proposed LAPV stage integrates a modified quasi-zero-stiffness passive vibration isolation unit with an active actuation unit which consists of three voice coil motors (VCMs). It performs active-passive vibration isolation while ensuring a precision levelling. Considering the load bearing and motion vibration of the chip-loading stage, a miniaturized quasi-zero-stiffness mechanism having a specialized leaf spring structure is then designed to limit the vibration isolation operation to the vertical direction and coordinate the levelling motion of the tilt rotation direction, so as to establish a structural decoupling stage. Based on the dynamic and kinematic modelling analysis, a customized PID-Positive Position Feedback (PID-PPF) control scheme is implemented. Afterwards, an experimental verification is conducted. The obtained results show that the stage can achieve a precise levelling motion, reaching a levelling deviation of 33.2 μrad. Moreover, it also can achieve effective active-passive vibration isolation for various vibrations with different frequencies and amplitudes, and the vibration isolation rate can reach 90 %.", "Keywords": "", "DOI": "10.1016/j.rcim.2024.102744", "PubYear": 2024, "Volume": "89", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Electronic Manufacturing Technology and Equipment, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Electronic Manufacturing Technology and Equipment, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Electronic Manufacturing Technology and Equipment, Guangdong University of Technology, Guangzhou, Guangdong 510006, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Yi", "Affiliation": "State Key Laboratory of Precision Electronic Manufacturing Technology and Equipment, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Guangdong Institute of Science and Technology, Zhuhai, Guangdong 519090, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Precision Electronic Manufacturing Technology and Equipment, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Electronic Manufacturing Technology and Equipment, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}], "References": [{"Title": "Integrated design, fabrication, and experimental study of a parallel micro-nano positioning-vibration isolation stage", "Authors": "Xiaoqing Sun; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "", "Page": "101988", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Robotic grinding of complex components: A step towards efficient and intelligent machining – challenges, solutions, and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101908", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Hybrid active/passive force control strategy for grinding marks suppression and profile accuracy enhancement in robotic belt grinding of turbine blade", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "102047", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Design of a new passive end-effector based on constant-force mechanism for robotic polishing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102278", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Research on the directionality of end dynamic compliance dominated by milling robot body structure and milling vibration suppression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "85", "Issue": "", "Page": "102631", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 113190297, "Title": "Arbitrary 3D stylization of radiance fields", "Abstract": "3D Stylization that creates stylized multi-view images is quite challenging, as it requires not only generating images which align with the desired style but also maintaining consistency across different perspectives. Most previous image style transfer methods focus on the 2D image domain and stylize each view independently, suffering from multi-view inconsistency. To tackle this challenging problem, we build on the neural radiance fields (NeRF) to stylize each 3D scene, as NeRF inherently ensures consistency across multiple perspectives, and has two sub-networks of geometry and appearance where appearance stylization cannot change the geometry. To enable arbitrary style transfer and more explicit and precise style adjustment, we introduce the CLIP model, which allows for style transfer based on either a text prompt or an arbitrary style image. We employ an ensemble of loss functions, of which CLIP loss ensures the similarity between the shared latent embeddings and generated style images, and Mask Loss is to constrain the 3D geometry to avoid non-smooth surface of NeRF. Experimental results demonstrate the effectiveness of our arbitrary 3D stylization generalized across diverse datasets. The proposed method outperforms most image-based and text-based 3D stylization models in terms of style transfer quality, producing pleasing images.", "Keywords": "", "DOI": "10.1016/j.imavis.2024.104971", "PubYear": 2024, "Volume": "145", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China;Corresponding author"}, {"AuthorId": 4, "Name": "Yi Sun", "Affiliation": "Dalian University of Technology, China"}], "References": [{"Title": "TSNeRF: Text-driven stylized neural radiance fields via semantic contrastive learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "116", "Issue": "", "Page": "102", "JournalTitle": "Computers & Graphics"}, {"Title": "Foreground and background separated image style transfer with a single text condition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "143", "Issue": "", "Page": "104956", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 113190314, "Title": "Optimizing University Electronic Book Procurement Strategy with Deep Forest Fusion Model", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2024.142046", "PubYear": 2024, "Volume": "14", "Issue": "2", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [], "References": []}, {"ArticleId": 113190545, "Title": "A Systematic Literature Review of Machine Learning and Deep Learning Approaches for Spectral Image Classification in Agricultural Applications Using Aerial Photography", "Abstract": "Recently, there has been a notable surge of interest in scientific research regarding spectral images. The potential of these images to revolutionize the digital photography industry, like aerial photography through Unmanned Aerial Vehicles (UAVs), has captured considerable attention. One encouraging aspect is their combination with machine learning and deep learning algorithms, which have demonstrated remarkable outcomes in image classification. As a result of this powerful amalgamation, the adoption of spectral images has experienced exponential growth across various domains, with agriculture being one of the prominent beneficiaries. This paper presents an extensive survey encompassing multispectral and hyperspectral images, focusing on their applications for classification challenges in diverse agricultural areas, including plants, grains, fruits, and vegetables. By meticulously examining primary studies, we delve into the specific agricultural domains where multispectral and hyperspectral images have found practical use. Additionally, our attention is directed towards utilizing machine learning techniques for effectively classifying hyperspectral images within the agricultural context. The findings of our investigation reveal that deep learning and support vector machines have emerged as widely employed methods for hyperspectral image classification in agriculture. Nevertheless, we also shed light on the various issues and limitations of working with spectral images. This comprehensive analysis aims to provide valuable insights into the current state of spectral imaging in agriculture and its potential for future advancements. © 2024 Tech Science Press. All rights reserved.", "Keywords": "aerial photography; deep learning; hyperspectral images; image recognition; Machine learning; multi-spectral images; object detection; unmanned aerial vehicles", "DOI": "10.32604/cmc.2024.045101", "PubYear": 2024, "Volume": "78", "Issue": "3", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computing and Information Sciences, Karachi Institute of Economics and Technology, Karachi, 75190, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computing and Information Sciences, Karachi Institute of Economics and Technology, Karachi, 75190, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computing and Information Sciences, Karachi Institute of Economics and Technology, Karachi, 75190, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON> Naveed", "Affiliation": "College of Computing and Information Sciences, Karachi Institute of Economics and Technology, Karachi, 75190, Pakistan; Faculty of Computing and Informatics (FCI), Multimedia University, Cyberjaya, 63100, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Computing and Informatics (FCI), Multimedia University, Cyberjaya, 63100, Malaysia; Faculty of Computing, Riphah International University, Islamabad, 46000, Pakistan; Faculty of Engineering and Information Technology, School of Computer Science, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computing and Information Sciences, Karachi Institute of Economics and Technology, Karachi, 75190, Pakistan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics (FCI), Multimedia University, Cyberjaya, 63100, Malaysia"}], "References": [{"Title": "Applications of Computer Vision in Plant Pathology: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "2", "Page": "611", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Local manifold sparse model for image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "382", "Issue": "", "Page": "162", "JournalTitle": "Neurocomputing"}, {"Title": "Quality evaluation of Mono & bi-Colored Apples with computer vision and multispectral imaging", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> Ban<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7857", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A deep neural network approach towards real-time on-branch fruit recognition for precision horticulture", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113594", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Apple fruit sorting using novel thresholding and area calculation algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "431", "JournalTitle": "Soft Computing"}, {"Title": "Automatic late blight lesion recognition and severity quantification based on field imagery of diverse potato genotypes by deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106723", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "WITHDRAWN: IoT-based Agriculture: Deep Learning in Detecting Apple Fruit Diseases", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "104321", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Support vector machine versus convolutional neural network for hyperspectral image classification: A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "15", "Page": "e6945", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 113190573, "Title": "Probability and natural deduction", "Abstract": "<p>We develop a system of basic probability reasoning founded on two great logical concepts, <PERSON><PERSON><PERSON>’s natural deduction systems and Carnap–Popper probability of sentences. Our system makes it possible to manipulate with probabilized sentences and justify their causal relationships: if probabilities of sentences $A$ and $B$ are in $[r,1]$ and $[s,1]$, respectively, then the probability of sentence $C$ belongs to $[t,1]$, i.e. $A^{r},B^{s}\\vdash C^{t}$, for $r,s,t\\in [0,1]$. We prove that our system is sound and complete with respect to the traditional Carnap–Popper type probability semantics. This approach opens up a new perspective of proof-theoretic treatment of sentence probability, potentially allowing immediate algorithmic use of the pure syntactic convenience of natural deductions in programming.</p>", "Keywords": "", "DOI": "10.1093/logcom/exae007", "PubYear": 2025, "Volume": "35", "Issue": "2", "JournalId": 2581, "JournalTitle": "Journal of Logic and Computation", "ISSN": "0955-792X", "EISSN": "1465-363X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Organizational Sciences , University of Belgrade, Belgrade, 11000, Serbia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics , University of Belgrade, Belgrade, 11000, Serbia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science , University of Kragujevac, Kragujevac, 34000, Serbia"}], "References": []}, {"ArticleId": 113190633, "Title": "Towards high-fidelity facial UV map generation in real-world", "Abstract": "We present a framework for completing high-fidelity 3D facial UV maps from single-face image. Despite the success of Generative Adversarial Networks (GANs) in this area, generating accurate UV maps from in-the-wild images remains challenging. Our approach involves a novel network called “Map and Edit” that combines a 2D generative model and a 3D prior to explicitly control the generation of multi-view faces. We use an indirect method to address domain gap issues between rendered and real images, which improves the identity consistency of the generated multi-view facial images. We also leverage synthesized multi-view images and predicted 3D information to produce texture-rich and high-resolution facial UV maps. Our model is self-supervised and does not require manual annotations or datasets. Experimental results demonstrate the effectiveness of our framework in reconstructing high-fidelity UV maps with accurate, fine details. Overall, our approach provides a promising solution to the challenges of 3D facial UV map completion from in-the-wild images.", "Keywords": "", "DOI": "10.1016/j.patrec.2024.02.023", "PubYear": 2024, "Volume": "180", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Li", "Affiliation": "School of Electrical Engineering, Korea University, Seoul, 02841, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Korea University, Seoul, 02841, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "School of Electrical Engineering, Korea University, Seoul, 02841, South Korea"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering, Drexel University, Philadelphia, 19104, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Korea University, Seoul, 02841, South Korea;Corresponding author"}], "References": [{"Title": "Facial UV map completion for pose-invariant face recognition: a novel adversarial approach based on coupled attention residual UNets", "Authors": "<PERSON> Seop Na; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Human-centric Computing and Information Sciences"}]}, {"ArticleId": 113191728, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-6911(24)00058-6", "PubYear": 2024, "Volume": "185", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [], "References": []}, {"ArticleId": 113192924, "Title": "SpeciFingers: Finger Identification and Error Correction on Capacitive Touchscreens", "Abstract": "<p>The inadequate use of finger properties has limited the input space of touch interaction. By leveraging the category of contacting fingers, finger-specific interaction is able to expand input vocabulary. However, accurate finger identification remains challenging, as it requires either additional sensors or limited sets of identifiable fingers to achieve ideal accuracy in previous works. We introduce SpeciFingers, a novel approach to identify fingers with the capacitive raw data on touchscreens. We apply a neural network of an encoder-decoder architecture, which captures the spatio-temporal features in capacitive image sequences. To assist users in recovering from misidentification, we propose a correction mechanism to replace the existing undo-redo process. Also, we present a design space of finger-specific interaction with example interaction techniques. In particular, we designed and implemented a use case of optimizing the performance in pointing on small targets. We evaluated our identification model and error correction mechanism in our use case.</p>", "Keywords": "", "DOI": "10.1145/3643559", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Human-Computer Interaction, Institute of Software, Chinese Academy of Sciences, Beijing, China, School of Computer Science and Technology and University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Human-Computer Interaction, Institute of Software, Chinese Academy of Sciences, Beijing, China, School of Computer Science and Technology and University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Human-Computer Interaction, Institute of Software, Chinese Academy of Sciences, Beijing, China, School of Computer Science and Technology and University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Human-Computer Interaction, Institute of Software, Chinese Academy of Sciences, Beijing, China, School of Computer Science and Technology and University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Informatics, Cardiff University, Cardiff, Wales, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Human-Computer Interaction, Institute of Software, Chinese Academy of Sciences, Beijing, China, School of Computer Science and Technology and University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Design, Northumbria University, Newcastle, United Kingdom"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, MOE-Key Laboratory of Pervasive Computing, Tsinghua University, Beijing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Human-Computer Interaction, Institute of Software, Chinese Academy of Sciences, Beijing, China, School of Computer Science and Technology and University of Chinese Academy of Sciences, Beijing, China"}], "References": [{"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Estimating 3D Finger Angle via Fingerprint Image", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Jian<PERSON> Feng", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 113192944, "Title": "Adaptive attention fusion network for cross-device GUI element re-identification in crowdsourced testing", "Abstract": "The rapid growth of mobile devices has ushered in an era of different device platforms. Different devices require a consistent user experience, especially with similar graphical user interfaces (GUIs). However, the different code bases of the various operating systems as well as the different GUI layouts and resolutions of the various devices pose a challenge for automated software testing. Crowdsourced software testing (CST) has emerged as a viable solution where crowdsourced workers perform tests on their own devices and provide detailed bug reports. Although CST is cost-effective, it is not very efficient and requires a large number of workers for manual testing. The potential of optimizing CST reproduction testing through computer vision remains largely untapped, especially when considering the uniformity of GUI elements on different devices. In this study, we present a novel deep learning model specifically designed to re-identify GUI elements in CST reproduction test scenarios, regardless of the underlying code changes on different devices. The model features a robust backbone network for feature extraction, an innovative attention mechanism with learnable factors to enhance the features of GUI elements and minimize interference from their backgrounds, and a classifier to determine matching labels for these elements. Our approach was validated on a large GUI element dataset containing 31,098 element images for training, 115,704 element images from real apps for testing, and 67 different background images. The results of our experiments underline the excellent accuracy of the model and the importance of each component. This work is a major step forward in improving the efficiency of reproduction testing in CST. The innovative solutions we propose could further reduce labor costs for CST platforms.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127502", "PubYear": 2024, "Volume": "580", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Software Development Environment, Beihang University, 37 Xueyuan Road, Haidian District, 100191, Beijing, China;Corresponding author"}, {"AuthorId": 2, "Name": "Wei-<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Software Development Environment, Beihang University, 37 Xueyuan Road, Haidian District, 100191, Beijing, China;Arizona State University, 1151 S Forest Ave, Tempe, 85287, AZ, USA"}], "References": []}, {"ArticleId": 113193002, "Title": "On the security of JPEG image encryption with RS pairs permutation", "Abstract": "Recently, a JPEG encryption scheme with RS (run/size) pairs multi-permutation and block permutation has been proposed, which mainly improves security by extracting the expected number of RS pairs for global permutations that need overflow processing. However, it still has the risk of information leakage under the chosen plaintext attack. This is because the limited embedding capacity in overflow processing makes the overflow blocks (OBs) less. On the one hand, a few OBs make the sketch generated by non-zero coefficients count after RS pair multi-permutation visually indistinguishable from the original sketch. On the other hand, a few OBs limit the number of extracted RS pairs that can participate in global permutation, so there are many partially extracted blocks (PEBs) containing unextracted RS pairs. More PEBs make the accuracy of the estimated block permutation sequence high, because the unique plaintext block can be constructed as long as there is an unextracted RS pair in an encrypted block. Simulation results show that the correct ratio of block permutation sequence is not less than 60 %. The highest and average PSNRs between the attacked sketch and the original one reach up to 47.07 dB and 27.42 dB, respectively.", "Keywords": "", "DOI": "10.1016/j.jisa.2024.103722", "PubYear": 2024, "Volume": "82", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "Yuan Yuan", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, 611756, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, 611756, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu, 611756, China;Corresponding author"}, {"AuthorId": 4, "Name": "Ling<PERSON> Qu", "Affiliation": "Cyberspace Institute of Advanced Technology, Guangzhou University, Guangzhou, 510700, China"}], "References": []}, {"ArticleId": *********, "Title": "Matcha: An IDE Plugin for Creating Accurate Privacy Nutrition Labels", "Abstract": "<p>Apple and Google introduced their versions of privacy nutrition labels to the mobile app stores to better inform users of the apps' data practices. However, these labels are self-reported by developers and have been found to contain many inaccuracies due to misunderstandings of the label taxonomy. In this work, we present Matcha, an IDE plugin that uses automated code analysis to help developers create accurate Google Play data safety labels. Developers can benefit from <PERSON>a's ability to detect user data accesses and transmissions while staying in control of the generated label by adding custom Java annotations and modifying an auto-generated XML specification. Our evaluation with 12 developers showed that Matcha helped our participants improved the accuracy of a label they created with Google's official tool for a real-world app they developed. We found that participants preferred Matcha for its accuracy benefits. Drawing on <PERSON><PERSON>, we discuss general design recommendations for developer tools used to create accurate standardized privacy notices.</p>", "Keywords": "", "DOI": "10.1145/3643544", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, Pittsburgh, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}], "References": [{"Title": "How Developers Talk About Personal Data and What It Means for User Privacy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Honeysuckle", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "How statically-typed functional programmers write code", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Documentation Matters: Human-Centered AI System to Assist Data Science Code Documentation in Computational Notebooks", "Authors": "<PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Charting App Developers’ Journey Through Privacy Regulation Features in Ad Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "3", "Page": "33", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "How Usable Are iOS App Privacy Labels?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "4", "Page": "204", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}]}, {"ArticleId": 113198238, "Title": "Dataset of suspicious phishing URL detection", "Abstract": "", "Keywords": "Phishing detection dataset1; Phishing URLs2; Legitimate URLs3; cybersecurity4; classification5", "DOI": "10.3389/fcomp.2024.1308634", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Daffodil International University, Bangladesh; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Graduate Studies, Daffodil International University, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Daffodil International University, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Daffodil International University, Bangladesh"}], "References": [{"Title": "A predictive model for phishing detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "232", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Malicious and Benign Webpages Dataset", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "", "Page": "106304", "JournalTitle": "Data in Brief"}, {"Title": "Datasets for phishing websites detection", "Authors": "<PERSON><PERSON>; Iztok Fister; <PERSON><PERSON>", "PubYear": 2020, "Volume": "33", "Issue": "", "Page": "106438", "JournalTitle": "Data in Brief"}, {"Title": "Phish-armour: phishing detection using deep recurrent neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Soft Computing"}, {"Title": "Moving beyond cyber security awareness and training to engendering security knowledge sharing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "21", "Issue": "1", "Page": "123", "JournalTitle": "Information Systems and e-Business Management"}]}, {"ArticleId": 113198270, "Title": "V-GMR: a variational autoencoder-based heterogeneous graph multi-behavior recommendation model", "Abstract": "<p>Compared to traditional single-behavior models, multibehavior recommendation models incorporate the auxiliary behavior information of users. This integration step addresses the cold-start and data sparsity issues and provides more comprehensive and detailed interaction information for the model. Despite the efforts made by multibehavior recommendation models to analyze user behavior semantics and capture user preferences, challenges remain in terms of effectively modeling the relationships between different types of user feedback. This issue is exacerbated by the heavy reliance on hyperparameters, which leads to overparameterization. In this paper, we propose a variational autoencoder (VAE) and graph-based heterogeneous multibehavior recommendation model (V-GMR), which aims to capture user behavior preferences and mitigate the aforementioned issues. First, we employ VAEs to encode user behaviors and learn feature representations that effectively capture multibehavior information. Second, we develop a preference fusion enhancer based on a VAE to integrate auxiliary user behaviors with the target behavior, effectively addressing the problem concerning sparse interaction data. Third, we design a special behavior decoding layer to handle the latent variables acquired from the preference fusion enhancer. In this layer, we reconstruct the loss function and resolve the issue of optimizing the neural network parameters through backpropagation in the presence of deterministic input values. The effectiveness of V-GMR is validated through experiments conducted on three real-world datasets, and the contributions of the V-GMR model components are verified through ablation experiments.</p>", "Keywords": "Recommendation system; Multibehavior; Variational autoencoder; Heterogeneous graph", "DOI": "10.1007/s10489-024-05360-x", "PubYear": 2024, "Volume": "54", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Shandong University of Technology, Zibo, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Technology, Shandong University of Technology, Zibo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Technology, Shandong University of Technology, Zibo, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Technology, Shandong University of Technology, Zibo, China"}, {"AuthorId": 5, "Name": "Hongzhen Cai", "Affiliation": "Agricultural Engineering and Food Science, Shandong University of Technology, Zibo, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical and Information Engineering, Beijing University of Civil Engineering and Architecture, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Technology, Shandong University of Technology, Zibo, China"}], "References": [{"Title": "Deep Learning Based Recommender System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Movie Recommendation System to Solve Data Sparsity Using Collaborative Filtering Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A survey on deep learning based Point-of-Interest (POI) recommendations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "472", "Issue": "", "Page": "306", "JournalTitle": "Neurocomputing"}, {"Title": "Deep multi-graph neural networks with attention fusion for recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116240", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Graph Neural Networks in Recommender Systems: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep GraphSAGE-based recommendation system: jumping knowledge connections with ordinal aggregation network", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "14", "Page": "11679", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 113198279, "Title": "Brain computer interface with EEG signals to improve feedback system in higher education based on augmentative and alternative communication", "Abstract": "A predictive brain-computer interface is a way to monitor EEG signals in humans currently under the experiment to understand the capability and get proper feedback about the digital education systems. The use of dynamically processed and collected data in a feedback system is unfeasible. Substantial processing delays are caused by a large volume of data utilised by the modern higher educational ideas. An artificial intelligence assisted brain-computer interface feedback system (AI-BCIFS) for augmentative and alternative communication is proposed to improve feedback analysis based on the EEG signals. AI-BCIFS method is proposed to avoid an unwanted and improper understanding of feedback in the higher education systems. An expanded optimisation methodology is introduced based on the feedback analysis, and the enhanced seeking feedback protocol (ESFP) has been developed to describe automatic recognition and storage. The experimental studies show that AI-BCIFAC is preferable to the existing approaches in terms of accuracy. Copyright © 2024 Inderscience Enterprises Ltd.", "Keywords": "augmentation; BCI; brain-computer interface; higher education; signal", "DOI": "10.1504/IJICT.2024.137203", "PubYear": 2024, "Volume": "24", "Issue": "2", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Xingtai University, Hebei, Xingtai, 054001, China"}, {"AuthorId": 2, "Name": "Hongxia Hou", "Affiliation": "Xingtai University, Hebei, Xingtai, 054001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Xingtai University, Hebei, Xingtai, 054001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xingtai University, Hebei, Xingtai, 054001, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Xingtai University, Hebei, Xingtai, 054001, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity University, Uttar Pradesh, Noida, India"}], "References": []}, {"ArticleId": *********, "Title": "Towards Unified Representation Learning for Career Mobility Analysis with Trajectory Hypergraph", "Abstract": "<p> Career mobility analysis aims at understanding the occupational movement patterns of talents across distinct labor market entities, which enables a wide range of talent-centered applications, such as job recommendation, labor demand forecasting, and company competitive analysis. Existing studies in this field mainly focus on a single fixed scale, either investigating individual trajectories at the micro-level or crowd flows among market entities at the macro-level. Consequently, the intrinsic cross-scale interactions between talents and the labor market are largely overlooked. To bridge this gap, we propose UniTRep , a novel unified representation learning framework for cross-scale career mobility analysis. Specifically, we first introduce a trajectory hypergraph structure to organize the career mobility patterns in a low-information-loss manner, where market entities and talent trajectories are represented as nodes and hyperedges, respectively. Then, for learning the market-aware talent representations , we attentively propagate the node information to the hyperedges and incorporate the market contextual features into the process of individual trajectory modeling. For learning the trajectory-enhanced market representations , we aggregate the message from hyperedges associated with a specific node to integrate the fine-grained semantics of trajectories into labor market modeling. Moreover, we design two auxiliary tasks to optimize both intra-scale and cross-scale learning with a self-supervised strategy. Extensive experiments on a real-world dataset clearly validate that UniTRep can significantly outperform state-of-the-art baselines for various tasks. </p>", "Keywords": "", "DOI": "10.1145/3651158", "PubYear": 2024, "Volume": "42", "Issue": "4", "JournalId": 12861, "JournalTitle": "ACM Transactions on Information Systems", "ISSN": "1046-8188", "EISSN": "1558-2868", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Science and Technology of China,  Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Thrust of Artificial Intelligence, HKUST (Guangzhou),  Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Career Science Lab, BOSS Zhipin, Beijing, China and PBC School of Finance, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Baidu Research, Baidu Inc.,  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Science and Technology of China,  Hefei, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Career Science Lab, BOSS Zhipin, Beijing, China and Thrust of Artificial Intelligence, HKUST (Guangzhou), Guangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "University of Science and Technology of China, Hefei, China"}], "References": [{"Title": "A Survey on Trajectory Data Management, Analytics, and Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Joint Representation Learning with Relation-Enhanced Topic Models for Intelligent Job Interview Assessment", "Authors": "<PERSON><PERSON><PERSON> Shen; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Hierarchical Hyperedge Embedding-Based Representation Learning for Group Recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Hyperspherical Variational Co-embedding for Attributed Networks", "Authors": "Jin<PERSON> Fang; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Automatic Skill-Oriented Question Generation and Recommendation for Intelligent Job Interviews", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": 113198343, "Title": "UbiPhysio: Support Daily Functioning, Fitness, and Rehabilitation with Action Understanding and Feedback in Natural Language", "Abstract": "<p>We introduce UbiPhysio, a milestone framework that delivers fine-grained action description and feedback in natural language to support people's daily functioning, fitness, and rehabilitation activities. This expert-like capability assists users in properly executing actions and maintaining engagement in remote fitness and rehabilitation programs. Specifically, the proposed UbiPhysio framework comprises a fine-grained action descriptor and a knowledge retrieval-enhanced feedback module. The action descriptor translates action data, represented by a set of biomechanical movement features we designed based on clinical priors, into textual descriptions of action types and potential movement patterns. Building on physiotherapeutic domain knowledge, the feedback module provides clear and engaging expert feedback. We evaluated UbiPhysio's performance through extensive experiments with data from 104 diverse participants, collected in a home-like setting during 25 types of everyday activities and exercises. We assessed the quality of the language output under different tuning strategies using standard benchmarks. We conducted a user study to gather insights from clinical physiotherapists and potential users about our framework. Our initial tests show promise for deploying UbiPhysio in real-life settings without specialized devices.</p>", "Keywords": "", "DOI": "10.1145/3643552", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "Chongyang Wang", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 2, "Name": "Yuan Feng", "Affiliation": "West China Hospital, Sichuan University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "West China Hospital, Sichuan University, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "West China Hospital, Sichuan University, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University and Qinghai University, China"}], "References": [{"Title": "Learned motion matching", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Leveraging Activity Recognition to Enable Protective Behavior Detection in Continuous Data", "Authors": "<PERSON>ng<PERSON> Wang; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Leveraging Sound and Wrist Motion to Detect Activities of Daily Living with Commodity Smartwatches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Bootstrapping Human Activity Recognition Systems for Smart Homes from Scratch", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "PhysiQ", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 113198827, "Title": "Acceleration model of online educational games based on improved ensemble ML algorithm", "Abstract": "Online educational games are easy to be affected by hardware and network status and have delay problems, which will affect students&#x27; learning interest and enthusiasm. Therefore, in this study, Bayesian optimization method is used to optimize the parameters of the “gradient boosting + category features” algorithm, and an improved “gradient boosting + categorical features” ensemble algorithm is designed using the integration framework of RF. And based on this, an online education game acceleration model is designed. The experimental results showed that the improved “gradient boosting + categorical features” algorithm, which combines Bayesian optimization and RF integration framework, was superior to the partially improved and traditional “gradient boosting + categorical features” algorithm in online education game recognition. The former&#x27;s Accuracy mean and Precision mean in various online education games were 98.9 % and 99.3 %, respectively. Compared with other common ensemble algorithms and deep learning algorithms, the research outcomes expressed that the Accuracy of the algorithm designed in this study in the training phase and the F1 value in the testing phase were 98.6 % and 99.0 %. respectively, which were significantly higher than other intelligent algorithms. The test results of the game acceleration model constructed based on this algorithm and other comparative algorithms indicated that the online education game acceleration model constructed based on the improved “gradient boosting + categorical features” algorithm could minimize the delay phenomenon generated during game operation to the greatest extent. The model designed in this study has the potential to solve the problems of running lag and delay in online education games, which is beneficial for improving students&#x27; user experience and learning absorption effect in such game applications.", "Keywords": "", "DOI": "10.1016/j.entcom.2024.100654", "PubYear": 2024, "Volume": "50", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Guangdong Zhaoqing Aviation Vocational College, Zhaoqing 526000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Academy of Art and Design, Sichuan University of Culture and Arts, Mianyang 621000, China;Corresponding author"}], "References": [{"Title": "Encouraging gameful experience in digital game-based learning: A double-mediation model of perceived instructional support, group engagement, and flow", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "179", "Issue": "", "Page": "104408", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 113198877, "Title": "Personality traits, internet addiction, and phubbing among teens: a mediation analysis", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2024.2323096", "PubYear": 2025, "Volume": "44", "Issue": "3", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Psychology, University of Zielona Góra, Zielona Góra, Poland"}, {"AuthorId": 2, "Name": "<PERSON>-Stronka", "Affiliation": "Institute of Psychology, University of Zielona Góra, Zielona Góra, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Psychology, <PERSON> University of Lublin, Lublin, Poland"}], "References": [{"Title": "Intensity of Facebook use: a personality-based perspective on dependency formation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "1", "Page": "198", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Neuroticism in the digital age: A meta-analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "", "Page": "100026", "JournalTitle": "Computers in Human Behavior Reports"}, {"Title": "Are emotional e-competencies a protective factor against habitual digital behaviors (media multitasking, cybergossip, phubbing) in Spanish students of secondary education?", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "104464", "JournalTitle": "Computers & Education"}, {"Title": "Internet addiction, cognitive, and dispositional factors among US adults", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "", "Page": "100180", "JournalTitle": "Computers in Human Behavior Reports"}, {"Title": "Is socially disruptive smartphone use detrimental to well-being? A systematic meta-analytic review on being phubbed", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "7", "Page": "1283", "JournalTitle": "Behaviour & Information Technology"}]}, {"ArticleId": *********, "Title": "A knowledge-assisted reinforcement learning optimization for road network design problems under uncertainty", "Abstract": "A knowledge-assisted reinforcement learning evolution optimization (KARLEO) is presented for a road network under uncertain demand and capacity. In order to hedge against stochastic link capacity and travel demand, a knowledge-assisted reinforcement learning optimization is proposed. Different from earlier studies, a stochastic link traffic model is first presented to capture time-varying cost incurred by traffic flow when link capacity is uncertain. In order to manage interaction between stochastic environment and traffic dynamics, a knowledge-assisted learnable environment for time-varying traffic flow and stochastic travel demand is proposed. To this end, a knowledge-assisted cooperative coevolution optimization is presented to solve a reinforcement learning-guided network design problem. In order to demonstrate computational performance of proposed model and approach, numerical experiments are performed at a real-world city under various kinds of traffic conditions. To investigate scalability of proposed approach, computational comparisons are made with a stochastic bi-level programming model at large-scale traffic grids under varying level of stochasticity. As it reported, the proposed approach achieved considerable improvement over conventional approaches by effectively reducing total cost at fairly low computational expense in all cases.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.111614", "PubYear": 2024, "Volume": "292", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, National Dong Hwa University, Taiwan"}], "References": [{"Title": "A review of deep learning with special emphasis on architectures, applications and recent trends", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105596", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hierarchical traffic signal optimization using reinforcement learning and traffic prediction with long-short term memory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114580", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Rule-based reinforcement learning methodology to inform evolutionary algorithms for constrained optimization of engineering applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "217", "Issue": "", "Page": "106836", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hybrid algorithms based on combining reinforcement learning and metaheuristic methods to solve global optimization problems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "223", "Issue": "", "Page": "107044", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Quantum differential evolution with cooperative coevolution framework and hybrid mutation strategy for large scale optimization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "224", "Issue": "", "Page": "107080", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep learning-based surrogate modeling via physics-informed artificial image (PiAI) for strongly coupled multidisciplinary engineering systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "232", "Issue": "", "Page": "107446", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep reinforcement learning for transportation network combinatorial optimization: A survey", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "233", "Issue": "", "Page": "107526", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Surrogate-assisted cooperative signal optimization for large-scale traffic networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "234", "Issue": "", "Page": "107542", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A bi-level programming model for the optimal lane reservation problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "116147", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Distributed agent-based deep reinforcement learning for large scale traffic signal control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108304", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Risk-averse stochastic bilevel programming: An application to natural gas markets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "108151", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Deep reinforcement learning in recommender systems: A survey and new perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "264", "Issue": "", "Page": "110335", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep reinforcement learning for traffic signal control with consistent state and reward design approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "267", "Issue": "", "Page": "110440", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Physics-informed deep reinforcement learning-based integrated two-dimensional car-following control strategy for connected automated vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "269", "Issue": "", "Page": "110485", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "VDGCNeT: A novel network-wide Virtual Dynamic Graph Convolution Neural network and Transformer-based traffic prediction model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "275", "Issue": "", "Page": "110676", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Incremental event detection via an improved knowledge distillation based model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "551", "Issue": "", "Page": "126519", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 113199249, "Title": "An analysis of effect of higher order endothermic/exothermic chemical reaction on magnetized casson hybrid nanofluid flow using fuzzy triangular number", "Abstract": "In this study, we considered higher-order endothermic/exothermic chemical reactions with activation energy on Casson hybrid nanofluid flow ( F e 3 O 4 + A l 2 O 3 / C 2 H 6 O 2 ) over a moving wedge under the fuzzy atmosphere, which includes the influence of thermal radiation, thermophoresis and Brownian diffusion. The main purpose of this analysis is to investigate the significance of fuzzy volume percentage on Casson hybrid nanofluid flow over a moving wedge. The transmuted nonlinear coupled fuzzy differential equations (FDEs) through the assistance of triangular fuzzy number (TFNs) are resolved employing numerical simulation of Runge-Ku<PERSON> fourth-fifth order (RKF-45) along with shooting technique. The impact of the various physical parameters on heat and mass transfer are depicted in the form of graphs. The results demonstrate that an increase in solid volume fraction parameters ( φ F e 3 O 4 , φ A l 2 O 3 ) , endothermic/exothermic reaction parameter ( λ 1 ) , chemical reaction parameter ( σ 1 ) , and temperature ratio parameter ( δ ) enhanced the rate of heat transport ( H t x ) of hybrid nanofluid. The rate of mass transport ( M t x ) diminished by raising of Brownian motion parameter ( N b ) , Lewis number ( L e ) , chemical reaction parameter ( σ 1 ) , and temperature ratio parameter ( δ ) . Also, for the comparison of nanofluid ( F e 3 O 4 / C 2 H 6 O 2 ) and hybrid nanofluid ( F e 3 O 4 + A l 2 O 3 / C 2 H 6 O 2 ) through membership function (MF), the solid volume fraction is taken as a TFN [0, 0.05, 0.1]. The MF and TNF are controlled by the α ˜ -cut, which has the range of [0, 1]. The fuzzy analysis indicates that the F e 3 O 4 + A l 2 O 3 / C 2 H 6 O 2 hybrid nanofluid provides a higher rate of heat transfer than F e 3 O 4 / C 2 H 6 O 2 nanofluids.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108119", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Sri <PERSON> Nadar College of Engineering, Chennai, 603 110, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Sri <PERSON> Nadar College of Engineering, Chennai, 603 110, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Rajalakshmi Engineering College, Thandalam, Chennai, 602 105, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics with Oceanology and Computer Programming, Vidyasagar University, Midnapore, 721 102, India;Corresponding author"}], "References": []}, {"ArticleId": 113199260, "Title": "Finite-State Relative Dimension, dimensions of A. P. subsequences and a finite-state <PERSON>'s theorem", "Abstract": "Finite-state dimension, introduced by <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> quantifies the information rate in an infinite sequence as measured by finite-state automata. In this paper, we define a relative version of finite-state dimension. The finite-state relative dimension dim F S Y ( X ) of a sequence X relative to Y is the finite-state dimension of X measured using the class of finite-state gamblers with oracle access to Y . We show its mathematical robustness by equivalently characterizing this notion using the relative block entropy rate of X conditioned on Y . We derive inequalities relating the dimension of a sequence to the relative dimension of its subsequences along any arithmetic progression (A. P.). These enable us to obtain a strengthening of Wall&#x27;s Theorem on the normality of A. P. subsequences of a normal sequence, in terms of relative dimension. In contrast to the original theorem, this stronger version has an exact converse yielding a new characterization of normality. We also obtain finite-state analogues of van Lambalgen&#x27;s theorem on the symmetry of relative normality.", "Keywords": "", "DOI": "10.1016/j.ic.2024.105156", "PubYear": 2024, "Volume": "298", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Kanpur, 208016, Kanpur, Uttar Pradesh, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Kanpur, 208016, Kanpur, Uttar Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Kanpur, 208016, Kanpur, Uttar Pradesh, India"}], "References": [{"Title": "Un<PERSON> <PERSON>'s theorem fails for computable randomness", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "271", "Issue": "", "Page": "104486", "JournalTitle": "Information and Computation"}, {"Title": "Automatic Kolmogorov complexity, normality, and finite-state dimension revisited", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "75", "JournalTitle": "Journal of Computer and System Sciences"}]}, {"ArticleId": 113199263, "Title": "MAPLE: Mobile App Prediction Leveraging Large Language Model Embeddings", "Abstract": "<p>In recent years, predicting mobile app usage has become increasingly important for areas like app recommendation, user behaviour analysis, and mobile resource management. Existing models, however, struggle with the heterogeneous nature of contextual data and the user cold start problem. This study introduces a novel prediction model, Mobile App Prediction Leveraging Large Language Model Embeddings (MAPLE), which employs Large Language Models (LLMs) and installed app similarity to overcome these challenges. MAPLE utilises the power of LLMs to process contextual data and discern intricate relationships within it effectively. Additionally, we explore the use of installed app similarity to address the cold start problem, facilitating the modelling of user preferences and habits, even for new users with limited historical data. In essence, our research presents MAPLE as a novel, potent, and practical approach to app usage prediction, making significant strides in resolving issues faced by existing models. MAPLE stands out as a comprehensive and effective solution, setting a new benchmark for more precise and personalised app usage predictions. In tests on two real-world datasets, MAPLE surpasses contemporary models in both standard and cold start scenarios. These outcomes validate MAPLE's capacity for precise app usage predictions and its resilience against the cold start problem. This enhanced performance stems from the model's proficiency in capturing complex temporal patterns and leveraging contextual information. As a result, MAPLE can potentially improve personalised mobile app usage predictions and user experiences markedly.</p>", "Keywords": "", "DOI": "10.1145/3643514", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "Yonchanok Khaokaew", "Affiliation": "School of Computer Science and Engineering, University of New South Wales, Sydney, NSW, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of New South Wales, Sydney, NSW, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, University of New South Wales, Sydney, NSW, Australia"}], "References": [{"Title": "BioBERT: a pre-trained biomedical language representation model for biomedical text mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1234", "JournalTitle": "Bioinformatics"}, {"Title": "Graph-based Method for App Usage Prediction with Attributed Heterogeneous Network Embedding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "3", "Page": "58", "JournalTitle": "Future Internet"}, {"Title": "Semantic-aware Spatio-temporal App Usage Representation via Graph Convolutional Network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "DeepApp", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Context-aware Target Apps Selection and Recommendation for Enhancing Personal Mobile Assistants", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "What and How long: Prediction of Mobile App Engagement", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "App usage on-the-move: Context- and commute-aware next app prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "87", "Issue": "", "Page": "101704", "JournalTitle": "Pervasive and Mobile Computing"}, {"Title": "What makes virtual intimacy...intimate? Understanding the Phenomenon and Practice of Computer-Mediated Paid Companionship", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 113199288, "Title": "Dynamic constrained evolutionary optimization based on deep Q-network", "Abstract": "Dynamic constrained optimization problems (DCOPs) are common and important optimization problems in real-world, which have great difficulty to solve. Dynamic constrained evolutionary algorithms (DCEAs) are widely used methods for solving DCOPs. However, existing DCEAs often struggle with convergence, particularly for DCOPs with drastic dynamic changes or intricate constraints. To address this issue, this paper proposes a novel DCEA called DCEA-DQN, which leverages the powerful perception and decision-making capabilities of Deep Q-Network (DQN). DCEA-DQN integrates two DQNs to enhance its performance. The first DQN is designed to adaptively respond to dynamic changes, enabling effective handling of DCOPs with various types and degrees of changes. It provides a high-quality re-initialized population for subsequent static optimization, resulting in faster and improved convergence. The second DQN is introduced to guide the mutation direction during offspring generation. It steers the population towards better feasible regions or directs it towards the optimal individual within the current feasible region. Moreover, a penalty mechanism is employed to handle constraints during offspring generation. To evaluate the performance of DCEA-DQN, comprehensive empirical studies are conducted using a new test suite called C-GMPB and a dynamic flexible job-shop scheduling problem. The experimental results, using two commonly used metrics E B and E O in the field of DCOPs, demonstrate that DCEA-DQN outperforms six state-of-the-art DCEAs and achieved optimal performance on 80% and 75% of all 24 test problems, respectively. The source code for DCEA-DQN is available at https://github.com/CIA-SZU/YRT.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.123592", "PubYear": 2024, "Volume": "249", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Industrial Digitalization Solutions, ZTE Corporation, Shenzhen 518000, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Big Data System Computing Technology, Shenzhen University, Shenzhen 518060, China"}], "References": [{"Title": "A self-learning genetic algorithm based on reinforcement learning for flexible job-shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106778", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A decision variable classification-based cooperative coevolutionary algorithm for dynamic multiobjective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "307", "JournalTitle": "Information Sciences"}, {"Title": "A metaheuristic-based framework for index tracking with practical constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "6", "Page": "4571", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 113199309, "Title": "Material-SAM: Adapting SAM for Material XCT", "Abstract": "X-ray Computed Tomography (XCT) enables non-destructive acquisition of the internal structure of materials, and image segmentation plays a crucial role in analyzing material XCT images. This paper proposes an image segmentation method based on the Segment Anything model (SAM). We constructed a dataset of carbide in nickel-based single crystal superalloys XCT images and preprocessed the images using median filtering, histogram equalization, and gamma correction. Subsequently, SAM was fine-tuned to adapt to the task of material XCT image segmentation, resulting in Material-SAM. We compared the performance of threshold segmentation, SAM, U-Net model, and Material-SAM. Our method achieved 88.45% Class Pixel Accuracy (CPA) and 88.77% Dice Similarity Coefficient (DSC) on the test set, outperforming SAM by 5.25% and 8.81%, respectively, and achieving the highest evaluation. Material-SAM demonstrated lower input requirements compared to SAM, as it only required three reference points for completing the segmentation task, which is one-fifth of the requirement of SAM. Material-SAM exhibited promising results, highlighting its potential as a novel method for material XCT image segmentation. © 2024 Tech Science Press. All rights reserved.", "Keywords": "foundation models; Ni-based superalloys; Segment Anything model; U-Net; X-ray computed tomography", "DOI": "10.32604/cmc.2024.047027", "PubYear": 2024, "Volume": "78", "Issue": "3", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 4, "Name": "Yisheng Miao", "Affiliation": "School of Materials Science and Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Simulation Center, Citic Dicastal Co., Ltd., Qinhuangdao, 066010, China"}, {"AuthorId": 7, "Name": "Decai Kong", "Affiliation": "Simulation Center, Citic Dicastal Co., Ltd., Qinhuangdao, 066010, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Simulation Center, Citic Dicastal Co., Ltd., Qinhuangdao, 066010, China"}, {"AuthorId": 9, "Name": "Haibao Qiao", "Affiliation": "Simulation Center, Citic Dicastal Co., Ltd., Qinhuangdao, 066010, China"}], "References": []}, {"ArticleId": 113200983, "Title": "Correction", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2327167", "PubYear": 2025, "Volume": "41", "Issue": "2", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [], "References": []}, {"ArticleId": 113201109, "Title": "Effects of Gamification on Motivations of Elementary School Students: An Action Research Field Experiment", "Abstract": "Background <p>Gamification research and practice have gained popularity, but there is a lack of experimental and field evidence regarding the effectiveness of specific design elements and their use contexts especially in developing countries.</p> Aim <p>This research aimed to measure the effect of gamifying digital learning environments and compare the effects of badges and leaderboards on the development of cognitive and achievement motivation of elementary school students.</p> Method <p>A gamified environment focused on the circulatory system and employing interactive storytelling was developed. It was tested through an in-field experiment at an elementary school in Egypt. The research sample was of 30 students in the 5th grade, divided into two equal experimental groups (badges - leaderboard).</p> Results <p>The results indicated an increase in cognitive and achievement motivation among students in both experimental groups. However, there were no significant differences in the effects of badges and leaderboards on the cognitive and achievement motivations of the participants in the two experimental groups.</p> Conclusions <p>These findings suggest that the effects of gamification are more likely due to the holistic design and novelty, rather than the specific use of a specific game element.</p>", "Keywords": "", "DOI": "10.1177/10468781241237389", "PubYear": 2024, "Volume": "55", "Issue": "4", "JournalId": 3394, "JournalTitle": "Simulation & Gaming", "ISSN": "1046-8781", "EISSN": "1552-826X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Lappeenranta – Lahti University of Technology LUT, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Lappeenranta – Lahti University of Technology LUT, Finland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lappeenranta – Lahti University of Technology LUT, Finland"}], "References": [{"Title": "Can gamification help to improve education? Findings from a longitudinal study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "106392", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Tailored gamification: A review of literature", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "102495", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Serious games in science education: a systematic literature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "3", "Page": "189", "JournalTitle": "Virtual Reality & Intelligent Hardware"}, {"Title": "The effectiveness of gamification in programming education: Evidence from a meta-analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100096", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "How leaderboard positions shape our motivation: the impact of competence satisfaction and competence frustration on motivation in a gamified crowdsourcing task", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "7", "Page": "1", "JournalTitle": "Internet Research"}, {"Title": "Assessing the benefits of gamification in mathematics for student gameful experience and gaming motivation", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "200", "Issue": "", "Page": "104806", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 113201111, "Title": "A Small Sample Pneumonia Diagnosis Algorithm Based on Feature Fusion of GLCM and Deep Neural Network", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2024.142049", "PubYear": 2024, "Volume": "14", "Issue": "2", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [], "References": [{"Title": "Covid19 Identification from Chest X-ray Images using Machine Learning Classifiers with GLCM Features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "3", "Page": "85", "JournalTitle": "ELCVIA Electronic Letters on Computer Vision and Image Analysis"}, {"Title": "Automatic detection of coronavirus disease (COVID-19) using X-ray images and deep convolutional neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "3", "Page": "1207", "JournalTitle": "Pattern Analysis and Applications"}]}, {"ArticleId": 113201125, "Title": "Approximation algorithms for job scheduling with block-type conflict graphs", "Abstract": "The problem of scheduling jobs on parallel machines (identical, uniform, or unrelated), under incompatibility relation modeled as a block graph, under the makespan optimality criterion, is considered in this paper. No two jobs that are in the relation (equivalently in the same block) may be scheduled on the same machine in this model. The presented model stems from a well-established line of research combining scheduling theory with methods relevant to graph coloring. Recently, cluster graphs and their extensions like block graphs were given additional attention. We complement hardness results provided by other researchers for block graphs by providing approximation algorithms. In particular, we provide a 2-approximation algorithm for P | G = block graph | C max and a PTAS for the case when the jobs are unit time in addition. In the case of uniform machines, we analyze two cases. The first one is when the number of blocks is bounded, i.e. Q | G = k − block graph | C max . For this case, we provide a PTAS, improving upon results presented by <PERSON><PERSON> and R. <PERSON>. The improvement is two-fold: we allow richer graph structure, and we allow the number of machine speeds to be part of the input. Due to strong NP-hardness of Q | G = 2 − clique graph | C max , the result establishes the approximation status of Q | G = k − block graph | C max . The PTAS might be of independent interest because the problem is tightly related to the NUMERICAL k -DIMENSIONAL MATCHING WITH TARGET SUMS problem. The second case that we analyze is when the number of blocks is arbitrary, but the number of cut-vertices is bounded and jobs are of unit time. In this case, we present an exact algorithm. In addition, we present an FPTAS for graphs with bounded treewidth and a bounded number of unrelated machines. The paper ends with extensive tests of the selected algorithms.", "Keywords": "", "DOI": "10.1016/j.cor.2024.106606", "PubYear": 2024, "Volume": "166", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Informatics, University of Gdańsk, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Algorithms and System Modeling, Gdańsk University of Technology, Poland"}, {"AuthorId": 3, "Name": "Inka Sokołowska", "Affiliation": "Theoretical Computer Science Department, Jagiellonian University, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Theoretical Computer Science Department, Jagiellonian University, Poland"}], "References": [{"Title": "Makespan minimization on unrelated parallel machines with a few bags", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "821", "Issue": "", "Page": "34", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Scheduling with complete multipartite incompatibility graph on parallel machines: Complexity and algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "309", "Issue": "", "Page": "103711", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 113201352, "Title": "mmArrhythmia: Contactless Arrhythmia Detection via mmWave Sensing", "Abstract": "<p>Arrhythmia is a common problem of irregular heartbeats, which may lead to serious complications such as stroke and even mortality. Due to the paroxysmal nature of arrhythmia, its long-term monitoring and early detection in daily household scenarios, instead of depending on ECG examination only available during clinical visits, are of critical importance. While ambulatory ECG Holter and wearables like smartwatches have been used, they are still inconvenient and interfere with users' daily activities. In this paper, we bridge the gap by proposing mmArrhythmia, which employs low-cost mmWave radar to passively sense cardiac motions and detect arrhythmia, in an unobtrusive contact-less way. Different from previous mmWave cardiac sensing works focusing on healthy people, mmArrhythmia needs to distinguish the minute and transient abnormal cardiac activities of arrhythmia patients. To overcome the challenge, we custom-design an encoder-decoder model that can perform arrhythmia feature encoding, sampling and fusion over raw IQ sensing data directly, so as to discriminate normal heartbeat and arrhythmia. Furthermore, we enhance the robustness of mmArrhythmia by designing multichannel ensemble learning to solve the model bias problem caused by unbalanced arrhythmia data distribution. Empirical evaluation over 79,910 heartbeats demonstrates mmArrhythmia's ability of robust arrhythmia detection, with 97.32% accuracy, 98.63% specificity, and 92.30% sensitivity.</p>", "Keywords": "", "DOI": "10.1145/3643549", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "Langcheng Zhao", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 6, "Name": "Huadong Ma", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Peking University Third Hospital, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Peking University Third Hospital, Beijing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Peking University Third Hospital, Beijing, China"}], "References": [{"Title": "Quantifying the Causal Effect of Individual Mobility on Health Status in Urban Space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Contactless Monitoring of PPG Using Radar", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "ECG-grained Cardiac Monitoring Using UWB Signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 113201383, "Title": "<PERSON><PERSON><PERSON><PERSON>: A Modular Framework for Developing Trusted Artificial Intelligence", "Abstract": "<p>Trustworthy artificial intelligence (Trusted AI) is of utmost importance when learning-enabled components (LECs) are used in autonomous, safety-critical systems. When reliant on deep learning, these systems need to address the reliability, robustness, and interpretability of learning models. In addition to developing strategies to address these concerns, appropriate software architectures are needed to coordinate LECs and ensure they deliver acceptable behavior even under uncertain conditions. This work describes Anunnaki, a model-driven framework comprising loosely-coupled modular services designed to monitor and manage LECs with respect to Trusted AI assurance concerns when faced with different sources of uncertainty. More specifically, the Anunnaki framework supports the composition of independent, modular services to assess and improve the resilience and robustness of AI systems. The design of <PERSON><PERSON><PERSON> was guided by several key software engineering principles (e.g., modularity, composabiilty, and reusability) in order to facilitate its use and maintenance to support different aggregate monitoring and assurance analysis tools for LESs and their respective data sets. We demonstrate Anunnaki on two autonomous platforms, a terrestrial rover and an unmanned aerial vehicle. Our studies show how Anunnaki can be used to manage the operations of different autonomous learning-enabled systems with vision-based LECs while exposed to uncertain environmental conditions.</p>", "Keywords": "", "DOI": "10.1145/3649453", "PubYear": 2024, "Volume": "19", "Issue": "3", "JournalId": 18461, "JournalTitle": "ACM Transactions on Autonomous and Adaptive Systems", "ISSN": "1556-4665", "EISSN": "1556-4703", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Michigan State University, East Lansing, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Michigan State University, East Lansing, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Michigan State University, East Lansing, USA"}], "References": [{"Title": "Mining guidelines for architecting robotics software", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "110969", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Mining guidelines for architecting robotics software", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "110969", "JournalTitle": "Journal of Systems and Software"}, {"Title": "<PERSON><PERSON>", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Autonomous and Adaptive Systems"}, {"Title": "Adversarial Attacks and Defenses", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "1", "Page": "86", "JournalTitle": "ACM SIGKDD Explorations Newsletter"}, {"Title": "Trustworthy AI", "Authors": "<PERSON><PERSON> M<PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "10", "Page": "64", "JournalTitle": "Communications of the ACM"}, {"Title": "An improved Yolov5 real-time detection method for small objects captured by UAV", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "361", "JournalTitle": "Soft Computing"}, {"Title": "A survey of robust adversarial training in pattern recognition: Fundamental, theory, and methodologies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108889", "JournalTitle": "Pattern Recognition"}, {"Title": "A survey of uncertainty in deep neural networks", "Authors": "<PERSON>; Cedr<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "S1", "Page": "1513", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 113201434, "Title": "Toward social media forensics through development of iOS analyzers for evidence collection and analysis", "Abstract": "Social media usage in mobile phones has increased substantially in recent times, and they are a critically important source of a forensics investigation. In this paper, we have developed Python‐based forensic analyzers that are integrated with the open‐source tool Autopsy. The proposed analyzers find forensic artifacts from the three most widely used social media messaging applications, that is, WhatsApp, Instagram, and Facebook Messenger. This research focuses on finding forensic artifacts stored by these social media applications on an iOS device. These analyzers extract data critical for a forensic investigation such as text messages, media attachments, sender and receiver details, timestamps, contact information, and other related forensics data from the full file system image of iOS devices. These Python‐based plugins extract the required data from the social media applications' databases and present the evidential artifacts in a human‐readable format. We integrated these analyzers into the Autopsy Forensics tool and showcased the gathered evidence so that investigators are capable to analyze the extracted information effortlessly. The data integrity is maintained by converting it into readable form without permanently altering the database format. The results prove that the proposed analyzers can successfully extract and analyze forensics data at a low computational overhead.", "Keywords": "autopsy;digital forensics;forensic artifacts;iOS device;mobile forensics;social media", "DOI": "10.1002/cpe.8074", "PubYear": 2024, "Volume": "36", "Issue": "13", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Centre for Cyber Security, Department of Software Engineering NED University of Engineering and Technology  Karachi Pakistan"}, {"AuthorId": 2, "Name": "Saadia A<PERSON>", "Affiliation": "National Centre for Cyber Security, Department of Computer Science and Information Technology NED University of Engineering and Technology  Karachi Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Centre for Cyber Security NED University of Engineering and Technology  Karachi Pakistan"}], "References": []}, {"ArticleId": 113201446, "Title": "An efficient composable 1-out-of-2 oblivious transfer scheme using vector decomposition", "Abstract": "A k-out-of-n oblivious transfer scheme is the interaction between a receiver and a sender where the sender possesses the messages m1, m2, ...mn. The receiver needs to access k messages out of the n messages. The scheme is a mechanism in which the receiver obtains no more than the messages m1, m2, ...mk which the receiver queried for with the indices 1, 2, ..., k being oblivious to the sender. We put forward a 1-out-of-2 oblivious transfer scheme. The security of our scheme is based on a decisional subspace assumption, decisional linear assumption and the computational infeasibility of vector decomposition problem a.k.a vector decomposition problem (VDP) assumption. Our scheme uses points on an elliptic curve as the ciphertext. Further, we prove the security of the proposed construction in the universal composability framework by using FCRS − hybrid model. Copyright © 2024 Inderscience Enterprises Ltd.", "Keywords": "distortion eigenvector space; elliptic curve cryptography; hyperelliptic curves; oblivious transfer; public key cryptography, bilinear pairings; universal composability; vector decomposition problem", "DOI": "10.1504/IJAIP.2024.137190", "PubYear": 2024, "Volume": "27", "Issue": "2", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Amrita School of Physical Sciences, Coimbatore, Amrita Vishwa Vidyapeetham, Tamil Nadu, Coimbatore, 641112, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Amrita School of Physical Sciences, Coimbatore, Amrita Vishwa Vidyapeetham, Tamil Nadu, Coimbatore, 641112, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "TIFAC CORE in Cyber Security, Amrita School of Engineering, Coimbatore, Amrita Vishwa Vidyapeetham, Tamil Nadu, Coimbatore, 641112, India"}], "References": []}, {"ArticleId": 113201497, "Title": "A Novel AI Approach for the Systematic Creation of Empathy Maps", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2323301", "PubYear": 2025, "Volume": "41", "Issue": "4", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Universidad Autónoma de Madrid, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Universidad Autónoma de Madrid, Madrid, Spain"}], "References": [{"Title": "A Supporting Tool for Enhancing User’s Mental Model Elicitation and Decision-Making in User Experience Research", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "1", "Page": "183", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Speaker Identification in Interactions between Mother<PERSON> and Children with Down Syndrome via Audio Analysis: A Case Study in Mexico", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON>-<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "9", "Page": "1922", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "EASIER System. Evaluating a Spanish Lexical Simplification Proposal with People with Cognitive Impairments", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "5", "Page": "1195", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "A Novel Hybrid Machine Learning Model for Analyzing E-Learning Users’ Satisfaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "16", "Page": "4193", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 113202267, "Title": "AquaKey: Exploiting the Randomness of the Underwater Visible Light Communication Channel for Key Extraction", "Abstract": "<p>Underwater Visible Light Communication (UVLC) is promising due to its relatively strong penetration capability in water and large frequency bandwidth. Visible Light Communication (VLC) is also considered a safer wireless communication paradigm as light signals can be constrained within the area of interest with obstacles such as walls, reducing the chance of potential attack. However, this intuitional security assumption is not true anymore in underwater environment. Recent research shows that the eavesdropping risk of UVLC is more severe than we thought, attributed to the divergence and scattering effects of light beams in water. In this paper, we harness the dynamic nature of underwater environments as a true random resource to extract symmetric keys for UVLC. Specifically, the proposed AquaKey system incorporates instantaneous bidirectional channel probing, computation of relative channel characteristics, and an environment-adaptive quantization algorithm. The above design addresses unique challenges caused by the dynamic underwater environment, including self-interference, high-frequency disturbance, and mismatch, ensuring the practicality and applicability of AquaKey. Additionally, AquaKey has negligible impact on communication and has no effect on the illumination function. Through extensive real-world experiments, we show that AquaKey can achieve reliable key extraction with cheap hardware, generating a 512-bit key in just 0.5-1 seconds.</p>", "Keywords": "", "DOI": "10.1145/3643557", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Massachusetts Amherst, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dalian University of Technology, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research Asia, China and University of Massachusetts Amherst, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Dalian University of Technology, China"}], "References": [{"Title": "The Security in Optical Wireless Communication: A Survey", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "14s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 113202292, "Title": "Optimization of open optical communication with multiple UAV over non homogenous turbulent paths", "Abstract": "<p>The article is devoted to optimizing communication through open atmospheric channels with many UAV by using inhomogeneous turbulent paths. A probabilistic assessment of the total signal loss in the communication system between the central communication node and a group of UAV located at the same distance from this node is analyzed. The case is considered when the scintillation indices in the subzones of optical communication with the UAV are different, which leads to unequal signal losses in the corresponding optical channels. The target functional is compiled by using a certain integral of the lognormal probability density function, which includes the newly introduced function of the losses dependence from the scintillation index. An analytical dependence of these indicators, characterizing the worst case when the total losses in the network reach a maximum, is presented. Keywords optical communications, turbulence, UAV, optimization, atmosphere</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2024-78-2-68-71", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 113203115, "Title": "exHAR: An Interface for Helping Non-Experts Develop and Debug Knowledge-based Human Activity Recognition Systems", "Abstract": "<p>Human activity recognition (HAR) is crucial for ubiquitous computing systems. While HAR systems are able to recognize a predefined set of activities established during the development process, they often fail to handle users' unique ways of completing these activities and changes in their behavior over time, as well as different activities. Knowledge-based HAR models have been proposed to help individuals create new activity definitions based on common-sense rules, but little research has been done to understand how users approach this task. To investigate this process, we developed and studied how people interact with an explainable knowledge-based HAR development tool called exHAR. Our tool empowers users to define their activities as a set of factual propositions. Users can debug these definitions by soliciting explanations for model predictions (why and why-not) and candidate corrections for faulty predictions (what-if and how-to). After conducting a study to evaluate the effectiveness of exHAR in helping users design accurate HAR systems, we conducted a think-aloud study to better understand people's approach to debugging and personalizing HAR systems and the challenges they may encounter. Our findings revealed why some participants had inaccurate mental models of knowledge-based HAR systems and inefficient approaches to the debugging process.</p>", "Keywords": "", "DOI": "10.1145/3643500", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Toronto, Toronto, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Toronto, Toronto, ON, Canada"}, {"AuthorId": 3, "Name": "Khai N. <PERSON>", "Affiliation": "University of Toronto, Toronto, ON, Canada"}], "References": [{"Title": "Machine learning explainability via microaggregation and shallow decision trees", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105532", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Liquid Level Sensing Using Commodity WiFi in a Smart Home Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Interpretable Anomaly Prediction: Predicting anomalous behavior in industry 4.0 settings via regularized logistic regression tools", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "101850", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Incremental Real-Time Personalization in Human Activity Recognition Using Domain Adaptive Batch Normalization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "An interpretable knowledge-based decision support system and its applications in pregnancy diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "221", "Issue": "", "Page": "106835", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep Learning for Sensor-based Human Activity Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "SenseCollect", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Examining the Social Context of Alcohol Drinking in Young Adults with Smartphone Sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Rescaling Egocentric Vision: Collection, Pipeline and Challenges for EPIC-KITCHENS-100", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "1", "Page": "33", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Investigating Cross-Modal Approaches for Evaluating Error Acceptability of a Recognition-Based Input Technique", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "DeXAR", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Predicting Performance Improvement of Human Activity Recognition Model by Additional Data Collection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Examining and Promoting Explainable Recommendations for Personal Sensing Technology Acceptance", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Helping Users Debug Trigger-Action Programs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "X-CHAR: A Concept-based Explainable Complex Human Activity Recognition Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "To Err is AI: Imperfect Interventions and Repair in a Conversational Agent Facilitating Group Chat Discussions", "Authors": "<PERSON><PERSON>; Ha-Kyung <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 113203186, "Title": "Deep contrastive representation learning for multi-modal clustering", "Abstract": "Benefiting from the informative expression capability of contrastive representation learning (CRL), recent multi-modal learning studies have achieved promising clustering performance. However, it should be pointed out that the existing multi-modal clustering methods based on CRL fail to simultaneously take the similarity information embedded in inter- and intra-modal levels. In this study, we mainly explore deep multi-modal contrastive representation learning, and present a multi-modal learning network, named trustworthy multi-modal contrastive clustering (TMCC), which incorporates contrastive learning and adaptively reliable sample selection with multi-modal clustering. Specifically, we are concerned with an adaptive filter to learn TMCC via progressing from ‘easy’ to ‘complex’ samples. Based on this, with the highly confident clustering labels, we present a new contrastive loss to learn modal-consensus representation, which takes into account not only the inter-modal similarity but also the intra-modal similarity. Experimental results show that these principles in TMCC consistently help promote clustering performance improvement.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127523", "PubYear": 2024, "Volume": "581", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Telecommunications Engineering, Xidian University, Shaanxi 710071, China;Research Institute of Air Force, China"}, {"AuthorId": 2, "Name": "Qin Li", "Affiliation": "School of Software Engineering, Shenzhen Institute of Information Technology, Shenzhen 518172, China"}, {"AuthorId": 3, "Name": "Xiangdong Zhang", "Affiliation": "School of Telecommunications Engineering, Xidian University, Shaanxi 710071, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Telecommunications Engineering, Xidian University, Shaanxi 710071, China;Corresponding author"}], "References": [{"Title": "Cross-modality deep feature learning for brain tumor segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107562", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 113203237, "Title": "XRF55: A Radio Frequency Dataset for Human Indoor Action Analysis", "Abstract": "<p>Radio frequency (RF) devices such as Wi-Fi transceivers, radio frequency identification tags, and millimeter-wave radars have appeared in large numbers in daily lives. The presence and movement of humans can affect the propagation of RF signals, further, this phenomenon is exploited for human action recognition. Compared to camera solutions, RF approaches exhibit greater resilience to occlusions and lighting conditions, while also raising fewer privacy concerns in indoor scenarios. However, current works have many limitations, including the unavailability of datasets, insufficient training samples, and simple or limited action categories for specific applications, which seriously hinder the growth of RF solutions, presenting a significant obstacle in transitioning RF sensing research from the laboratory to a wide range of everyday life applications. To facilitate the transitioning, in this paper, we introduce and release a large-scale multiple radio frequency dataset, named XRF55, for indoor human action analysis. XRF55 encompasses 42.9K RF samples and 55 action classes of human-object interactions, human-human interactions, fitness, body motions, and human-computer interactions, collected from 39 subjects within 100 days. These actions were meticulously selected from 19 RF sensing papers and 16 video action recognition datasets. Each action is chosen to support various applications with high practical value, such as elderly fall detection, fatigue monitoring, domestic violence detection, etc. Moreover, XRF55 contains 23 RFID tags at 922.38MHz, 9 Wi-Fi links at 5.64GHz, one mmWave radar at 60-64GHz, and one Azure Kinect with RGB+D+IR sensors, covering frequency across decimeter wave, centimeter wave, and millimeter wave. In addition, we apply a mutual learning strategy over XRF55 for the task of action recognition. Unlike simple modality fusion, under mutual learning, three RF modalities are trained collaboratively and then work solely. We find these three RF modalities will promote each other. It is worth mentioning that, with synchronized Kinect, XRF55 also supports the exploration of action detection, action segmentation, pose estimation, human parsing, mesh reconstruction, etc., with RF-only or RF-Vision approaches.</p>", "Keywords": "", "DOI": "10.1145/3643543", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Xi'an Jiaotong University, China"}, {"AuthorId": 2, "Name": "Yizhe Lv", "Affiliation": "Xi'an Jiaotong University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xi'an Jiaotong University, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Xi'an Jiaotong University, China"}, {"AuthorId": 5, "Name": "Jinsong Han", "Affiliation": "Zhejiang University, China"}], "References": [{"Title": "WiFi Sensing with Channel State Information", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A Survey on Renamings of Software Entities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Real-time Arm Gesture Recognition in Smart Home Scenarios via Millimeter Wave Sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "XGest: Enabling Cross-Label Gesture Recognition with RF Signals", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}, {"Title": "UQRCom", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 113205790, "Title": "3D printable PLA spiral phase plate with horn feed for OAM beam generation for 6G communication", "Abstract": "<p>This paper describes the performance characteristics of a spiral phase plate (SPP) made of polylactic acid (PLA) plastic with horn feed for orbital angular momentum (OAM) mode l = + 1 generation in three microwave frequency bands: S, C, and X. When plane waves pass through the SPP, the thickness variation results in an azimuthal variation of the permittivity, which introduces an azimuthal phase delay and generating an OAM beam. An SPP structure can be easily implemented with PLA material using 3D printing technology, which reduces fabrication challenges and costs. The simulated results show that an OAM vortex beam with l = + 1 is successfully generated at various microwave frequencies with a gain of 16–18.5 dBi, high mode purity, and a low divergence angle. The measurement results for PLA at X-band were also presented, showing the generation of the OAM beam and an estimation of the mode purity.</p>", "Keywords": "Orbital angular momentum (OAM); Polylactic acid (PLA); Spiral phase plate (SPP); Vortex beam", "DOI": "10.1007/s41870-024-01764-7", "PubYear": 2024, "Volume": "16", "Issue": "4", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>lal Nehru Technological University Kakinada, Kakinada, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Jawaharlal Nehru Technological University Kakinada, Kakinada, India"}], "References": []}, {"ArticleId": *********, "Title": "Simulation of Routing Protocols for IoV in Real-Time Mobility Model Environment", "Abstract": "<p>Vehicles in the Internet of Vehicles (IoV) create wireless connections and participate in routing by sending information to other nodes. Despite the recent growth in popularity of IoV, there are still issues with high vehicle speeds, frequent interruptions, and a dynamic topology, which makes the creation of efficient routing protocols that are more appropriate to route packets effectively to their final destination extremely difficulty. Traditional mobility models are unsuitable for simulating realistic vehicle trajectories in IoV due to the physical environment and road constructions within. This study explores the interaction between the SUMO (Simulation of Urban Mobility) road traffic simulator and the NS-3 network simulator. It assesses the effectiveness of well-known routing protocols and emphasizes the value of a mobility simulator like SUMO for simulating IoV networks. The protocol performance assessment used in this work is implemented using C++ code that is well integrated into NS-3. The study objectively evaluates parameters such as overhead and Goodput for real-time AODV, real-time OLSR, real-time DSDV, and real-time DSR protocols by utilizing the combined capabilities of network simulator NS-3 and SUMO.</p>", "Keywords": "Simulation; Internet of vehicles; NS-3.29; Open street map", "DOI": "10.1007/s42979-024-02626-6", "PubYear": 2024, "Volume": "5", "Issue": "3", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Baba Ghul<PERSON> Shah Badshah University, Rajouri, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Baba <PERSON> Shah Badshah University, Rajouri, India; Corresponding author."}], "References": [{"Title": "Stochastic modelling and analysis of mobility models for intelligent software defined internet of vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "50", "Issue": "", "Page": "101498", "JournalTitle": "Physical Communication"}, {"Title": "Architecture and routing protocols for internet of vehicles: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1/2/3", "Page": "159", "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing"}, {"Title": "Cloud-edge data encryption in the internet of vehicles using Zeckendorf representation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Hengjin <PERSON>ai", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "On the secure performance of Internet of Vehicles in Nakagami-m fading scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "59", "Issue": "", "Page": "102111", "JournalTitle": "Physical Communication"}]}, {"ArticleId": *********, "Title": "Cutting chatter recognition based on spectrum characteristics and extreme gradient boosting", "Abstract": "<p>Chatter is a common state in milling and turning, which will reduce the machining quality of parts. For taking adequate measures to avoid chatter, chatter detection is necessary. However, chatter recognition still has many difficulties for both turning and milling. Due to the material removal effect, the chatter frequency will change. The existing frequency band extraction methods, such as ensemble empirical mode decomposition (EEMD) and wavelet packet transform, can not fully reflect the chatter information. As a result, a novel online chatter recognition method is proposed based on spectrum characteristics, which can be applied to the chatter recognition for both turning and milling under the combination of multiple cutting parameters black simultaneously. Firstly, we carried out the hammering experiment to obtain the frequency response function and modal frequency of the machining system before and after machining. The signal distribution characteristics of turning and milling signals are analyzed. Four features sensitive to chatter are extracted, including spectral standard deviation, frequency spectral expectation (FSE), spectral skewness, and relative power spectral entropy (RPE). Taking these features as inputs, a chatter recognition model is established based on an extreme gradient boosting (XGBoost) algorithm. Finally, we conducted the turning and milling tests. The experimental results show that the proposed chatter recognition model can be effective under different cutting parameters for turning and milling. Besides, results also show the chatter recognition model has extraterritorial applicability.</p>", "Keywords": "Modal hammering; Spectrum expectation; XGBoost; Variable parameters within and outside the domain; Online chatter detection", "DOI": "10.1007/s00170-024-13203-9", "PubYear": 2024, "Volume": "131", "Issue": "12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National NC System Engineering Research Center, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National NC System Engineering Research Center, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National NC System Engineering Research Center, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National NC System Engineering Research Center, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National NC System Engineering Research Center, Huazhong University of Science and Technology, Wuhan, China; State Key Laboratory of Digital Manufacturing Equipment and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 6, "Name": "Songping He", "Affiliation": "State Key Laboratory of Digital Manufacturing Equipment and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "National NC System Engineering Research Center, Huazhong University of Science and Technology, Wuhan, China; State Key Laboratory of Digital Manufacturing Equipment and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National NC System Engineering Research Center, Huazhong University of Science and Technology, Wuhan, China; Corresponding author."}], "References": [{"Title": "Deep learning for the detection of machining vibration chatter", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "180", "Issue": "", "Page": "103445", "JournalTitle": "Advances in Engineering Software"}]}, {"ArticleId": 113205850, "Title": "WITHDRAWN: Evaluating the generation capabilities of large Chinese language models", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.aiopen.2024.02.002", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 78702, "JournalTitle": "AI Open", "ISSN": "2666-6510", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 113205861, "Title": "A reinforcement learning (RL)-based hybrid method for ground penetrating radar (GPR)-driven buried object detection", "Abstract": "Ground penetrating radar (GPR) systems are effective sensors for discovering various types of objects buried underground, such as military mines, metal objects, and pieces of underground infrastructures. A GPR system can be manually operated by a human or can be an integral part of a host platform. The host platform may be semi- or fully autonomous and may operate in different environments such as land vehicles or more recently air-borne drones. One challenge for the fully or semi-autonomous host platforms in particular is to find an efficient search procedure that would reduce the operation time and optimize resource utilization. Most of the current approaches are based on pre-defined search patterns which, for large and sparse areas, could mean unnecessary waste of time and resources. In this paper, we introduce a method that combines a coarse and therefore relatively low cost initial search pattern with a Reinforcement Learning (RL) driven efficient navigation path for eventual target detection, by exploiting the signal processing pipeline of the onboard GPR. We illustrate the applicability of the method using a well-known, high fidelity GPR simulation environment and a novel RL framework. Our results suggest that combination of a coarse navigation scheme and an RL-based training procedure based on GPR scan returns can lead to a more efficient target discovery procedure for host platforms.", "Keywords": "Coverage path planning; Ground penetrating radar; Machine learning; Reinforcement learning", "DOI": "10.1007/s00521-024-09466-8", "PubYear": 2024, "Volume": "36", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "BİLGEM, TÜBİTAK, Ankara, Türkiye; Department of Software Engineering, Ankara Science University, Ankara, Türkiye; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Electronics Department, İstanbul Ticaret University, Istanbul, Turkey"}], "References": [{"Title": "Tactical UAV path optimization under radar threat using deep reinforcement learning", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "5649", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 113205927, "Title": "Strategic adoption of the recommender system under online retailer competition and consumer search", "Abstract": "In the context of the digital economy, online retailers are faced with increasing competition and seek to improve sales and attract consumers through innovations such as recommender systems. This paper investigates the strategies for adopting recommender systems by online retailers, introducing a theoretical model to assess whether both incumbent and new entrant retailers should adopt these systems. The model conducts a comparative analysis, factoring in consumers’ search costs and the strategic choices of competitors. It establishes equilibrium outcomes for four distinct scenarios, followed by an analysis of the interplay between the strategy selection decisions of the two types of retailers. The results show that if one retailer does not adopt the system, the other retailer should always adopt it. However, when one retailer has already implemented the system, the other retailer should only do so if the search cost is relatively low and the recommendation cost is high, but not when the search cost is moderate. Given two retailers’ strategies, we identify the requirements that form two equilibriums: both retailers adopting the system, or only the incumbent retailer adopting it. Furthermore, the recommendation levels and product prices are not necessarily in a monotonic relationship with the cost of the system.", "Keywords": "", "DOI": "10.1016/j.elerap.2024.101376", "PubYear": 2024, "Volume": "64", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management, Tianjin University of Technology, Tianjin 300384, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Tianjin University of Technology, Tianjin 300384, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Labovitz School of Business and Economics, University of Minnesota Duluth, Duluth, GA 55812, USA;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Management, Tianjin University of Technology, Tianjin 300384, China"}], "References": [{"Title": "Differentiated products pricing with consumer network acceptance in a dual-channel supply chain", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "", "Page": "100915", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "An Economic Analysis of Product Recommendation in the Presence of Quality and Taste-Match Heterogeneity", "Authors": "<PERSON><PERSON> (Michael) <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "399", "JournalTitle": "Information Systems Research"}, {"Title": "Multichannel retail competition with product returns: Effects of restocking fee legislation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "100993", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "The impact of recommender systems and pricing strategies on brand competition and consumer search", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101144", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Manufacture's entry and green strategies with carbon trading policy", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "171", "Issue": "", "Page": "108472", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Whom to benefit? Competing platforms’ strategic investment in recommender systems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "56", "Issue": "", "Page": "101210", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Strategic introduction of live-stream selling in a supply chain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "62", "Issue": "", "Page": "101315", "JournalTitle": "Electronic Commerce Research and Applications"}]}, {"ArticleId": 113206034, "Title": "Efficient FPGA implementation for sound source separation using direction-informed multichannel non-negative matrix factorization", "Abstract": "Sound source separation (SSS) is a fundamental problem in audio signal processing, aiming to recover individual audio sources from a given mixture. A promising approach is multichannel non-negative matrix factorization (MNMF), which employs a Gaussian probabilistic model encoding both magnitude correlations and phase differences between channels through spatial covariance matrices (SCM). In this work, we present a dedicated hardware architecture implemented on field programmable gate arrays (FPGAs) for efficient SSS using MNMF-based techniques. A novel decorrelation constraint is presented to facilitate the factorization of the SCM signal model, tailored to the challenges of multichannel source separation. The performance of this FPGA-based approach is comprehensively evaluated, taking advantage of the flexibility and computational capabilities of FPGAs to create an efficient real-time source separation framework. Our experimental results demonstrate consistent, high-quality results in terms of sound separation.", "Keywords": "Multichannel non-negative matrix factorization; FPGA; Sound source separation; Decorrelation constraint; Real time", "DOI": "10.1007/s11227-024-05945-w", "PubYear": 2024, "Volume": "80", "Issue": "9", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chair of Thermodynamics of Mobile Energy Conversion Systems (TME), RWTH Aachen University, Aachen, Germany; SupMicroTech, Université de Franche-Comté, Besançon, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Telecommunication Engineering, University of Jaén, Linares, Spain"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Department of Telecommunication Engineering, University of Jaén, Linares, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Oviedo, Gijón, Spain"}], "References": [{"Title": "Parallel multichannel music source separation system", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; J. J. <PERSON>-<PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "1", "Page": "619", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Parallel multichannel blind source separation using a spatial covariance model and nonnegative matrix factorization", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "10", "Page": "12143", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Efficient parallel kernel based on <PERSON><PERSON><PERSON> decomposition to accelerate multichannel nonnegative matrix factorization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "18", "Page": "20649", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 113208330, "Title": "Dataflow Optimization with Layer-Wise Design Variables Estimation Method for Enflame CNN Accelerators", "Abstract": "As convolution layers have been proved to be the most time-consuming operation in convolutional neural network (CNN) algorithms, many efficient CNN accelerators have been designed to boost the performance of convolution operations. Previous works on CNN acceleration usually use fixed design variables for diverse convolutional layers, which would lead to inefficient data movements and low utilization of computing resource. We tackle this issue by proposing a flexible dataflow optimization method with design variables estimation for different layers. The optimization method first narrows the design space by the priori constraints, and then enumerates all legal solutions to select the optimal design variables. We demonstrate the effectiveness of the proposed optimization method by implementing representative CNN models (VGG-16, ResNet-18 and MobileNet V1) on Enflame Technology&#x27;s programmable CNN accelerator, General Computing Unit (GCU). The results indicate that our optimization can significantly enhance the throughput of the convolution layers in ResNet, VGG and MobileNet on GCU, with improvement of up to 1.84×. Furthermore, it achieves up to 2.08× of GCU utilization specifically for the convolution layers of ResNet on GCU.", "Keywords": "", "DOI": "10.1016/j.jpdc.2024.104869", "PubYear": 2024, "Volume": "189", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Shanghai Enflame Technology Co. Ltd., Shanghai 201203, China"}, {"AuthorId": 6, "Name": "<PERSON>zhang Li", "Affiliation": "Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}], "References": [{"Title": "Efficient convolution pooling on the GPU", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "222", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Towards a component-based acceleration of convolutional neural networks on FPGAs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "123", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Efficient and portable GEMM-based convolution operators for deep neural network training on multicore processors", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "240", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Optimization of FPGA-based CNN accelerators using metaheuristics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "4", "Page": "4493", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "YaConv: Convolution with Low Cache Footprint", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}, {"Title": "Fusing In-storage and Near-storage Acceleration of Convolutional Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Journal on Emerging Technologies in Computing Systems"}]}, {"ArticleId": 113210268, "Title": "Privacy-Preserving and Cross-Domain Human Sensing by Federated Domain Adaptation with Semantic Knowledge Correction", "Abstract": "<p>Federated Learning (FL) enables distributed training of human sensing models in a privacy-preserving manner. While promising, federated global models suffer from cross-domain accuracy degradation when the labeled source domains statistically differ from the unlabeled target domain. To tackle this problem, recent methods perform pairwise computation on the source and target domains to minimize the domain discrepancy by adversarial strategy. However, these methods are limited by the fact that pairwise source-target adversarial alignment alone only achieves domain-level alignment, which entails the alignment of domain-invariant as well as environment-dependent features. The misalignment of environment-dependent features may cause negative impact on the performance of the federated global model. In this paper, we introduce FDAS, a Federated adversarial Domain Adaptation with Semantic Knowledge Correction method. FDAS achieves concurrent alignment at both domain and semantic levels to improve the semantic quality of the aligned features, thereby reducing the misalignment of environment-dependent features. Moreover, we design a cross-domain semantic similarity metric and further devise feature selection and feature refinement mechanisms to enhance the two-level alignment. In addition, we propose a similarity-aware model fine-tuning strategy to further improve the target model performance. We evaluate the performance of FDAS extensively on four public and a real-world human sensing datasets. Extensive experiments demonstrate the superior effectiveness of FDAS and its potential in the real-world ubiquitous computing scenarios.</p>", "Keywords": "", "DOI": "10.1145/3643503", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The State Key Laboratory of Blockchain and Data Security, Zhejiang University, HangZhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The State Key Laboratory of Blockchain and Data Security, Zhejiang University, HangZhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The State Key Laboratory of Blockchain and Data Security, Zhejiang University, HangZhou, China"}], "References": [{"Title": "Survey of Post-OCR Processing Approaches", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "ColloSSL", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Wi-<PERSON><PERSON>", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "FLAME", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 113210274, "Title": "Image Deblurring Using Feedback Mechanism and Dual Gated Attention Network", "Abstract": "Recently, image deblurring task driven by the encoder-decoder network has made a tremendous amount of progress. However, these encoder-decoder-based networks still have two disadvantages: (1) due to the lack of feedback mechanism in the decoder design, the reconstruction results of existing networks are still sub-optimal; (2) these networks introduce multiple modules, such as the self-attention mechanism, to improve the performance, which also increases the computational burden. To overcome these issues, this paper proposes a novel feedback-mechanism-based encoder-decoder network (namely, FMNet) that is equipped with two key components: (1) the feedback-mechanism-based decoder and (2) the dual gated attention module. To improve reconstruction quality, the feedback-mechanism-based decoder is proposed to leverage the feedback information via the feedback attention module, which adaptively selects useful features in the feedback path. To decrease the computational cost, an efficient dual gated attention module is proposed to perform the attention mechanism in the frequency domain twice, which improves deblurring performance while reducing the computational cost by avoiding redundant convolutions and feature channels. The superiority of FMNet in terms of both deblurring performance and computational efficiency is demonstrated via comparisons with state-of-the-art methods on multiple public datasets.", "Keywords": "Image deblurring; Encoder-decoder network; Feedback mechanism; Gated attention", "DOI": "10.1007/s11063-024-11462-x", "PubYear": 2024, "Volume": "56", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic, Electrical Engineering and Physics, Fujian University of Technology, Fuzhou, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Ye", "Affiliation": "School of Electronic, Electrical Engineering and Physics, Fujian University of Technology, Fuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Ecological Environment and Urban Construction, Fujian University of Technology, Fuzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Johns Hopkins University, Baltimore, USA"}], "References": [{"Title": "Context Module Based Multi-patch Hierarchical Network for Motion Deblurring", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "211", "JournalTitle": "Neural Processing Letters"}, {"Title": "Self-attention negative feedback network for real-time image super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "6179", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Stack-based Scale-recurrent Network for Face Image Deblurring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "4419", "JournalTitle": "Neural Processing Letters"}, {"Title": "CDMC-Net: Context-Aware Image Deblurring Using a Multi-scale Cascaded Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "4", "Page": "3985", "JournalTitle": "Neural Processing Letters"}, {"Title": "Fusing Convolution and Self-Attention Parallel in Frequency Domain for Image Deblurring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "7", "Page": "9811", "JournalTitle": "Neural Processing Letters"}, {"Title": "An Analysis of Multi-stage Progressive Image Restoration Network (MPRNet)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "", "Page": "140", "JournalTitle": "Image Processing On Line"}]}, {"ArticleId": 113210369, "Title": "A satellite-derived dataset on vegetation phenology across Central Asia from 2001 to 2023", "Abstract": "Satellite-observed land surface phenology (LSP) data have helped us better understand terrestrial ecosystem dynamics at large scales. However, uncertainties remain in comprehending LSP variations in Central Asian drylands. In this article, an LSP dataset covering Central Asia (45–100°E, 33–57°N) is introduced. This LSP dataset was produced based on Moderate Resolution Imaging Spectroradiometer (MODIS) 0.05-degree daily reflectance and land cover data. The phenological dynamics of drylands were tracked using the seasonal profiles of near-infrared reflectance of vegetation (NIRv). NIRv time series processing involved the following steps: identifying low-quality observations, smoothing the NIRv time series, and retrieving LSP metrics. In the smoothing step, a median filter was first applied to reduce spikes, after which the stationary wavelet transform (SWT) was used to smooth the NIRv time series. The SWT was performed using the Biorthogonal 1.1 wavelet at a decomposition level of 5. Seven LSP metrics were provided in this dataset, and they were categorized into the following three groups: (1) timing of key phenological events, (2) NIRv values essential for the detection of the phenological events throughout the growing season, and (3) NIRv value linked to vegetation growth state during the growing season. This LSP dataset is useful for investigating dryland ecosystem dynamics in response to climate variations and human activities across Central Asia.", "Keywords": "Land surface phenology ; Ecosystem dynamics ; Drylands ; Climate change ; Remote sensing ; MODIS ; Near-infrared reflectance of vegetation", "DOI": "10.1016/j.dib.2024.110297", "PubYear": 2024, "Volume": "54", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geographic Science, Faculty of Arts and Sciences, Beijing Normal University, Zhuhai 519087, China;Center for Territorial Spatial Planning and Real Estate Studies, Beijing Normal University, Zhuhai 519087, China;Corresponding author at: Department of Geographic Science, Faculty of Arts and Sciences, Beijing Normal University, Zhuhai 519087, China"}], "References": [{"Title": "Satellite solar-induced chlorophyll fluorescence and near-infrared reflectance capture complementary aspects of dryland vegetation productivity dynamics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "270", "Issue": "", "Page": "112858", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 113210385, "Title": "A majority-based learning system for detecting misinformation", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2024.2326562", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Information Systems, National Chengchi University, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Information Systems, National Chengchi University, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON>", "Affiliation": "Department of Information Systems and Computer Science, Georgia College & State University, Milledgeville, GA, USA"}, {"AuthorId": 4, "Name": "Troy Strader", "Affiliation": "Department of Information Management and Business Analytics, Drake University, Des Moines, IA, USA"}], "References": [{"Title": "Fake online reviews: Literature review, synthesis, and directions for future research", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "113280", "JournalTitle": "Decision Support Systems"}, {"Title": "CNMF: A Community-Based Fake News Mitigation Framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "9", "Page": "376", "JournalTitle": "Information"}, {"Title": "DANES: Deep Neural Network Ensemble Architecture for Social and Textual Context-aware Fake News Detection", "Authors": "Ciprian-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "294", "Issue": "", "Page": "111715", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 113211213, "Title": "Local dual-graph discriminant classifier for binary classification", "Abstract": "Graph-based methods mine the potential structural information of data by constructing various graphs that positively affect the classifiers when dealing with classification problems. However, traditional graph-based classifiers are the most common single-graph classifiers and minimize only intra-class compactness, where inter-class separability is replaced by other factors. To consider real inter-class separability, we introduce a novel local dual-graph structure that can fully mine the geometric distribution of data by simultaneously maximizing the inter-class separability and minimizing the intra-class compactness. This local dual-graph structure reflects the relationship between samples and their neighbors and hence avoids the negative impact of outliers on the construction of graphs. Furthermore, a novel classifier called the local dual-graph discriminant classifier (LDGDC) is proposed using a local dual-graph structure. Originally, LDGDC is designed to perform the following optimization: minimization of the 2-norm regularization of model coefficients and intra-class compactness, and maximization of the inter-class separability, which is a non-convex optimization problem. To facilitate the solution, we transform the original non-convex problem of LDGDC into a convex problem. Finally, experiments were conducted on several public datasets, and the results demonstrate the effectiveness and robustness of the proposed LDGDC.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127508", "PubYear": 2024, "Volume": "581", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Soochow University, 215006 Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Soochow University, 215006 Suzhou, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Soochow University, 215006 Suzhou, China"}], "References": [{"Title": "Multi-scale kernel Fisher discriminant analysis with adaptive neuro-fuzzy inference system (ANFIS) in fault detection and diagnosis framework for chemical process systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "13", "Page": "9283", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A survey on graph-based methods for similarity searches in metric spaces", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "95", "Issue": "", "Page": "101507", "JournalTitle": "Information Systems"}, {"Title": "Graph-based semi-supervised learning: A review", "Authors": "<PERSON><PERSON>; <PERSON>; Qing Yan", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "216", "JournalTitle": "Neurocomputing"}, {"Title": "Sparse discriminant twin support vector machine for binary classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "19", "Page": "16173", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 113211333, "Title": "An upper bound on asymptotic repetition threshold of balanced sequences via colouring of the <PERSON><PERSON><PERSON><PERSON> sequence", "Abstract": "We colour the <PERSON><PERSON><PERSON><PERSON> sequence by suitable constant gap sequences to provide an upper bound on the asymptotic repetition threshold of d -ary balanced sequences. The bound is attained for d = 2 , 4 and 8 and we conjecture that it happens for infinitely many even d &#x27;s. Our bound reveals an essential difference in behaviour of the repetition threshold and the asymptotic repetition threshold of balanced sequences. The repetition threshold of d -ary balanced sequences is known to be at least 1 + 1 d − 2 for each d ≥ 3 . In contrast, our bound implies that the asymptotic repetition threshold of d -ary balanced sequences is at most 1 + τ 3 2 d − 3 for each d ≥ 2 , where τ is the golden mean.", "Keywords": "", "DOI": "10.1016/j.tcs.2024.114490", "PubYear": 2024, "Volume": "995", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "L'ubomí<PERSON>", "Affiliation": "Czech Technical University in Prague, Czech Republic;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Czech Technical University in Prague, Czech Republic"}], "References": [{"Title": "On minimal critical exponent of balanced sequences", "Authors": "L'ubom<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "922", "Issue": "", "Page": "158", "JournalTitle": "Theoretical Computer Science"}, {"Title": "On balanced sequences and their critical exponent", "Authors": "<PERSON>; <PERSON>'u<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "939", "Issue": "", "Page": "18", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 113214390, "Title": "Design of an Information System for Management of Organic Waste Bins As a Processor for Fertilizing Compost for Family Medicinal Plants", "Abstract": "<p>During global climate change and the threat of environmental pollution, society is increasingly aware of the crucial role played by nature conservation and waste management. One of the big challenges in this context is waste management, especially organic waste. This research uses a method with a qualitative research approach. Qualitative data collection techniques were carried out using interviews and observations to explore the problems faced. The approach used in this research is experimental research. The data collected is managed to create a design for a website-based information system that is used to provide various information and guidelines for managing organic waste bins. This website was designed with the aim of deepening understanding of the problems faced by the community regarding organic waste management and the use of organic waste bins. The research results provide an overview of the design of an information system that can be used for various activities related to managing organic waste bins. This website was designed with the aim of deepening understanding of the problems faced by the community regarding organic waste management and the use of organic waste bins. It is hoped that these results can be translated into a system that can provide significant support for organic waste management in the community so that the impact can be felt directly in the community with a healthier and more comfortable environment.</p>", "Keywords": "", "DOI": "10.34010/aisthebest.v8i2.11365", "PubYear": 2023, "Volume": "8", "Issue": "2", "JournalId": 66136, "JournalTitle": "@is The Best [Accounting Information System & Information Technology Business Enterprise]", "ISSN": "2252-9853", "EISSN": "2656-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>yi<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> Supriyati", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>a <PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Monostatic Pulsed Ultrawideband Radar Antenna for Studying Biological Rhythms of the Human Body and Internal Inhomogeneities of Dielectric Objects", "Abstract": "Abstract: This paper presents a study of the prerequisites and the original way of transition from a quasi-monostatic antenna system to a monostatic one as applied to ultrawideband pulsed contact radar subsurface sounding at shallow depths. For this purpose, the joint application of ultrawideband electric “Bow-Tie” and magnetic “Bi-Quad” antennas is investigated. The mutual arrangement proposed in this work allows reducing to zero the distance between the centers of the antennas in the plane orthogonal to the direction of signal emission-reception. The results of full-scale tests with the corresponding processing of the obtained ensembles by the example of probing the human body for the detection and determination of the parameters of biological rhythms and a tree trunk 58 cm in diameter showed satisfactory results for the radar with a central frequency of 1.5 GHz. At the same time, the suppression of the useful signal received due to the application of the described design was only 2.3 dB at an acceptable level of the direct signal, which does not lead to overloading of the radar stroboscopic receiver input. © Allerton Press, Inc. 2024. ISSN 0146-4116, Automatic Control and Computer Sciences, 2024, Vol. 58, No. 1, pp. 101–108. Allerton Press, Inc., 2024. Russian Text The Author(s), 2024, published in Avtomatika i Vychislitel’naya Tekhnika, 2024, No. 1, pp. 5–14.", "Keywords": "contact subsurface radar; human body research; Kar<PERSON><PERSON>–Loeve transformation; monostatic antenna; ultrawideband radar", "DOI": "10.3103/S0146411624010036", "PubYear": 2024, "Volume": "58", "Issue": "1", "JournalId": 14245, "JournalTitle": "Automatic Control and Computer Sciences", "ISSN": "0146-4116", "EISSN": "1558-108X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Electronics and Computer Science (EDI), Riga, LV-1006, Latvia"}], "References": [{"Title": "Ultra-Wideband Pulse Radar Mounted on an Unmanned Aerial Vehicle for the Monitoring of Moving Objects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "4", "Page": "396", "JournalTitle": "Automatic Control and Computer Sciences"}]}, {"ArticleId": 113228203, "Title": "‘Three Birds With One Stone’ functionalized metal–organic frameworks for facile and efficient chemiluminescent immunoassays", "Abstract": "The luminol–H<sub>2</sub>O<sub>2</sub> system is crucial in the field of chemiluminescence (CL) analysis. However, luminol exhibits improved solubility and luminescence only in an alkaline environment, and its preparation process is cumbersome. This inconvenience highlights the need for developing a simple and universally applicable method in this regard. This study integrates luminol and a catalyst into metal–organic frameworks (MOFs) for fabrication of functionalised MOF nanoparticles. Further, PEGylated hematin (PEG = polyethylene glycol) was prepared. This modification with PEG enhanced the catalytic properties of hematin and facilitated the formation of zeolitic imidazole framework-8 nanoparticles. Furthermore, the built-in catalyst enhanced the CL in terms of strength and persistence. These MOF nanoparticles were used as signal markers to fabricate a sandwich-type CL immunosensor for detection of carcinoembryonic antigen, which is a disease marker, following which highly satisfactory analytical test results are obtained. This approach of integrating luminophores and catalysts offers a new idea for generating novel materials in the CL analysis field.", "Keywords": "", "DOI": "10.1016/j.snb.2024.135607", "PubYear": 2024, "Volume": "409", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China"}, {"AuthorId": 2, "Name": "Li Dai", "Affiliation": "Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China"}, {"AuthorId": 4, "Name": "Zhongfeng Gao", "Affiliation": "Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China;Department of Chemistry, Sungkyunkwan University, Suwon 16419, Republic of Korea;Corresponding author at: Key Laboratory of Interfacial Reaction & Sensing Analysis in Universities of Shandong, School of Chemistry and Chemical Engineering, University of Jinan, Jinan, Shandong 250022, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> Long", "Affiliation": "School of Chemistry and Materials, Nanning Normal University, Nanning, Guangxi 530100, China;Corresponding authors"}], "References": [{"Title": "Surfactant-assisted synthesis of water-stable cobalt-imidazole metal organic frameworks as signal probes for chemiluminescent immunoassay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "373", "Issue": "", "Page": "132774", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 113228223, "Title": "r-indexing the eBWT", "Abstract": "The extended Burrows-Wheeler Transform (eBWT) [<PERSON><PERSON><PERSON> et al. TCS 2007] is a variant of the BWT, introduced for collections of strings. In this paper, we present the extended r-index , an analogous data structure to the r -index [<PERSON><PERSON><PERSON> et al. JACM 2020]. It occupies O ( r ) words, with r the number of runs of the eBWT, and offers the same functionalities as the r -index. We also show how to efficiently support finding maximal exact matches (MEMs). We implemented the extended r -index and tested it on circular bacterial genomes and plasmids, comparing it to five state-of-the-art compressed text indexes. While our data structure maintains similar time and memory requirements for answering pattern matching queries as the original r -index, it is the only index in the literature that can naturally be used for both circular and linear input collections. This is an extended version of [<PERSON><PERSON> et al., r-indexing the eBWT, SPIRE 2021].", "Keywords": "<PERSON><PERSON>–<PERSON> transform ; Extended BWT ; Prefix-free parsing ; r -index ; MEMs ; Circular strings", "DOI": "10.1016/j.ic.2024.105155", "PubYear": 2024, "Volume": "298", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Florida, Gainesville, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University Ca' Foscari, Venice, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Verona, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Florida, Gainesville, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Palermo, Italy;Corresponding author"}], "References": [{"Title": "Refining the r-index", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "812", "Issue": "", "Page": "96", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Fully Functional Suffix Trees and Optimal Text Searching in BWT-Runs Bounded Space", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "67", "Issue": "1", "Page": "1", "JournalTitle": "Journal of the ACM"}, {"Title": "Grammar-compressed indexes with logarithmic search time", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "53", "JournalTitle": "Journal of Computer and System Sciences"}]}, {"ArticleId": 113228284, "Title": "Development of Monoclonal Antibodies and Immunochromatographic Test Strips Applying Indirect Competitive Method for Daidzein Detection", "Abstract": "In this study, we aimed to detect daidzein secreted in the soybean rhizosphere using an indirect competitive immunochromatographic assay. A specific anti-daidzein monoclonal antibody was obtained through the establishment of a hybridoma. The antibody exhibited a detection limit of 5 ppb daidzein and showed strong cross-reactivity with daidzein, as determined by an indirect competitive method using a surface plasmon resonance sensor. Through the findings, a unique binding pattern differing from those of genistein and other saponins was observed. Enhanced sensitivity with the ability to detect daidzein concentrations as low as 10 nM (2.5 ng/mL) was achieved using immunochromatographic test strips with modified gold nanoparticles. © 2024 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "daidzein; immunochromatography; indirect competitive method; monoclonal antibody; surface plasmon resonance", "DOI": "10.18494/SAM4811", "PubYear": 2024, "Volume": "36", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Science and Electrical Engineering, Kyushu University, 744 Motooka, Fukuoka, 819-0395, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information Science and Electrical Engineering, Kyushu University, 744 Motooka, Fukuoka, 819-0395, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Science and Electrical Engineering, Kyushu University, 744 Motooka, Fukuoka, 819-0395, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Kyushu University, 744 Motooka, Fukuoka, 819-0395, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bioinformation and DDBJ Center, National Institute of Genetics, 1111 Yata <PERSON>, Shizuoka, 411-8540, Japan; Department of Frontier Research and Development, Kazusa DNA Research Institute, 2-6-7 Kazusa-kamatari, Kisarazu, Chiba, 292-0818, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Research Institute for Sustainable Humanosphere (RISH), Kyoto University, Uji, Kyoto, 611-0011, Japan"}], "References": []}, {"ArticleId": 113228288, "Title": "From global challenges to local solutions: A review of cross-country collaborations and winning strategies in road damage detection", "Abstract": "Monitoring road conditions is crucial for safe and efficient transportation infrastructure, but developing effective models for automatic road damage detection is challenging requiring large-scale annotated datasets. Cross-country collaboration provide access to diverse datasets and insights into factors affecting road damage detection models. This paper presents a review of winning strategies of the Crowdsensing-based Road Damage Detection Challenge (CRDDC) held in 2022 as a Big Data Cup, with 90+ teams from 20+ countries proposing solutions for six countries: India, Japan, the Czech Republic, Norway, the United States, and China. The best solution achieved an F1-score of 77 % for all six countries, which is 2.7 % better than the 2<sup>nd</sup> ranked solution. This study explores the impact of factors influencing dataset and model selection by CRDDC winners. The study’s insights can guide future research in making data-related choices and developing more effective road damage detection models accounting for the diverse road conditions across different countries.", "Keywords": "", "DOI": "10.1016/j.aei.2024.102388", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Spatial Information Science, The University of Tokyo, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UrbanX Technologies, Inc., Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Spatial Information Science, The University of Tokyo, Japan"}], "References": [{"Title": "Deep Learning for Generic Object Detection: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "261", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Survey on performance of deep learning models for detecting road damages using multiple dashcam image resources", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101182", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A framework of pavement management system based on IoT and big data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101226", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "RDD2020: An annotated image dataset for automatic road damage detection using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "", "Page": "107133", "JournalTitle": "Data in Brief"}, {"Title": "RDD2020: An annotated image dataset for automatic road damage detection using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "", "Page": "107133", "JournalTitle": "Data in Brief"}, {"Title": "Delving deep into spatial pooling for squeeze-and-excitation networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108159", "JournalTitle": "Pattern Recognition"}, {"Title": "Attention mechanisms in computer vision: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "331", "JournalTitle": "Computational Visual Media"}, {"Title": "Object detection using YOLO: challenges, architectural successors, datasets and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "6", "Page": "9243", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "DenseSPH-YOLOv5: An automated damage detection model based on DenseNet and Swin-Transformer prediction head-enabled YOLOv5 with attention mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "102007", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 113230584, "Title": "Towards a multi-criteria decision support system for heating and cooling equipment selection in residential buildings", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDSS.2024.137240", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 31430, "JournalTitle": "International Journal of Decision Support Systems", "ISSN": "2050-6988", "EISSN": "2050-6996", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 113230655, "Title": "Comparative study of low resource Digaru language using SMT and NMT", "Abstract": "<p>In recent years, the field of natural language processing has witnessed the widespread adoption of Neural Machine Translation (NMT) as a sophisticated system known for its high computational accuracy in machine translation. However, the efficacy of NMT relies heavily on the availability of substantial functional corpora, which is readily accessible in languages like English. For low-resourced languages, particularly those with minimal or nonexistent literature, the impact of NMT systems is considerably limited. This study addresses this gap by introducing the manually curated Digaru–English Corpus, comprising 50,000 sentence pairs derived from English sentences found in the Tatoeba Corpus collection. Notably, Digaru and English exhibit significant morphological and syntactic differences, with Digaru having scarce ready-to-use corpora. Our study includes a quality analysis employing baseline machine translation models, specifically the Phrase-Based Statistical Machine Translation (PBSMT) model, and NMT utilizing Recurrent Neural Network (RNN) architectures. To evaluate the performance of the low-resource Digaru language in the machine translation domain, we conducted further assessments. Automatic evaluation metrics were employed to gauge the outputs, and manual evaluations were carried out by native human translators. The assessment criteria included Adequacy and Fluency of sentence pairs from the test set, with comparisons to their references in Digaru.</p>", "Keywords": "NMT; SMT; BLEU; Human evaluation; Adequacy; Fluency", "DOI": "10.1007/s41870-024-01769-2", "PubYear": 2024, "Volume": "16", "Issue": "4", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "NIT Arunachal Pradesh: National Institute of Technology Arunachal Pradesh, Jote, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NIT Arunachal Pradesh: National Institute of Technology Arunachal Pradesh, Jote, India"}], "References": [{"Title": "Low resource machine translation of english–manipuri: A semi-supervised approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "209", "Issue": "", "Page": "118187", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Isolated words recognition of Adi, a low-resource indigenous language of Arunachal Pradesh", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "6", "Page": "3079", "JournalTitle": "International Journal of Information Technology"}, {"Title": "An empirical analysis on statistical and neural machine translation system for English to Mizo language", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "8", "Page": "4021", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Transforming legal text interactions: leveraging natural language processing and large language models for legal support in Palestinian cooperatives", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "1", "Page": "551", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Integrating prior knowledge to build transformer models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "3", "Page": "1279", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 113230685, "Title": "Harmonious Mutual Learning for Facial Emotion Recognition", "Abstract": "Facial emotion recognition in the wild is an important task in computer vision, but it still remains challenging since the influence of backgrounds, occlusions and illumination variations in facial images, as well as the ambiguity of expressions. This paper proposes a harmonious mutual learning framework for emotion recognition, mainly through utilizing attention mechanisms and probability distributions without utilizing additional information. Specifically, this paper builds an architecture with two emotion recognition networks and makes progressive cooperation and interaction between them. We first integrate self-mutual attention module into the backbone to learn discriminative features against the influence from emotion-irrelevant facial information. In this process, we deploy spatial attention module and convolutional block attention module for the two networks respectively, guiding to enhanced and supplementary learning of attention. Further, in the classification head, we propose to learn the latent ground-truth emotion probability distributions using softmax function with temperature to characterize the expression ambiguity. On this basis, a probability distribution distillation learning module is constructed to perform class semantic interaction using bi-directional KL loss, allowing mutual calibration for the two networks. Experimental results on three public datasets show the superiority of the proposed method compared to state-of-the-art ones.", "Keywords": "Facial emotion recognition; Mutual learning; Attention mechanisms; Probability distributions", "DOI": "10.1007/s11063-024-11566-4", "PubYear": 2024, "Volume": "56", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>ling Gan", "Affiliation": "School of Computer Science and Engineering, Guangxi Normal University, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Guangxi Normal University, Guilin, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Guangxi Normal University, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Guangxi Normal University, Guilin, China"}], "References": [{"Title": "LBAN-IL: A novel method of high discriminative representation for facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "432", "Issue": "", "Page": "159", "JournalTitle": "Neurocomputing"}, {"Title": "Dynamic multi-channel metric network for joint pose-aware and identity-invariant facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "195", "JournalTitle": "Information Sciences"}, {"Title": "Weighted contrastive learning using pseudo labels for facial expression recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "10", "Page": "5001", "JournalTitle": "The Visual Computer"}, {"Title": "Joint spatial and scale attention network for multi-view facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "109496", "JournalTitle": "Pattern Recognition"}, {"Title": "Learn from each other to Classify better: Cross-layer mutual attention learning for fine-grained visual classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "109550", "JournalTitle": "Pattern Recognition"}, {"Title": "A framework for facial expression recognition using deep self-attention network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "7", "Page": "9543", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 113230708, "Title": "A new community business model for a free, open, and neutral network: Considering the wireless to fiber transition", "Abstract": "Free, open, and neutral network (FONN) is an alternative to the prevailing proprietary ISP networks on the Internet. From the FONN manifesto, we conceive two main goals: to give freedom to users and to cooperate in building and maintaining the FONN infrastructure. However, the manifesto avoids talking about the business model of this infrastructure and neglects to give the appropriate role to each member of the community in building and maintaining this infrastructure. Moreover, current research misplaces FONN’s cooperation in the avoidance of the tragedy of the commons and also overlooks ownership. The move from wireless to fiber has amplified the consequences of not considering ownership or member roles. In order to fill the previous gaps, in this article we will first relate the objectives of this manifesto to the field of free software. Next we will argue that FONN’s cooperation should be placed in the avoidance of the tragedy of the anticommons. Inspired by free software we define a new business model for FONN besides discussing different ownership licenses. Furthermore, we state the roles of members and rigorously model the economic flow of construction and maintenance costs. The main implication of the proposed business model is that future FONNs will avoid the economic and organizational collapse of some current FONN communities, mainly because funding cannot be based solely on crowdfunding or volunteering. Yet, as an overall benefit, FONN avoids the duplication of infrastructure. This new business model has been used successfully in the fiber deployment of a FONN section.", "Keywords": "Free; Open and neutral network ; Fiber/wireless network ; Free software ; Ownership and business models ; Tragedy of the anticommons", "DOI": "10.1016/j.iot.2024.101157", "PubYear": 2024, "Volume": "26", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON>eix <PERSON>", "Affiliation": "Universitat Politècnica de Catalunya (UPC) Department of Mining, Industrial and ICT Engineering (EMIT), Av. Bases de Manresa 61–73, 08242 Manresa, Catalonia, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitat Politècnica de Catalunya (UPC) Department of Mining, Industrial and ICT Engineering (EMIT), Av. Bases de Manresa 61–73, 08242 Manresa, Catalonia, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Politècnica de Catalunya (UPC) Department of Mining, Industrial and ICT Engineering (EMIT), Av. Bases de Manresa 61–73, 08242 Manresa, Catalonia, Spain"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Universitat Politècnica de Catalunya (UPC) Department of Mining, Industrial and ICT Engineering (EMIT), Av. Bases de Manresa 61–73, 08242 Manresa, Catalonia, Spain"}], "References": [{"Title": "On the Guifi.net community network economics", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "107067", "JournalTitle": "Computer Networks"}, {"Title": "Archetypes of open-source business models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "727", "JournalTitle": "Electronic Markets"}]}, {"ArticleId": 113234733, "Title": "A deep learning-based framework for object recognition in ecological environments with dense focal loss and occlusion", "Abstract": "<p>In precision agricultural analysis, the remote sensing of geospatial data holds substantial potential for multi-purpose crop surveys, targeting automatic crop area delineation, health monitoring, and yield estimation. Advanced remote sensing methods, when integrated with machine learning techniques, have significantly advanced agricultural analyses. This study introduces a three-tiered framework. Firstly, orchard areas are delineated using the ESA Sentinel-2 multispectral instrument (MSI) at 10 m resolution, employing the normalized differential vegetation index (NDVI). In the second stage, mango tree canopies are detected from hand-annotated true color composite imagery using two variants of a convolutional neural network. The first variant, CanopyNet-1, is built directly over RetinaNet foundational layers, achieving a mean average precision (mAP) of 0.79, a precision of 0.80, and a recall of 0.76. The second variant, CanopyNet-2, builds upon DeepForest, a generalized tree canopy trained model, also using RetinaNet at its base. CanopyNet-2 demonstrates superior performance, achieving a mAP of 0.83, a precision of 0.98, and a recall of 0.96, notably surpassing conventional models such as YOLOv5 and Faster R-CNN. Lastly, the health of the orchard is characterized using 3-m-resolution multispectral imagery. Cumulatively, our framework, with its tiered approach, exhibits high accuracy in both tree canopy delineation and health characterization, suggesting it as a comprehensive solution for large-scale orchard monitoring and yield optimization.</p>", "Keywords": "Deep learning; Remote sensing; Precision agriculture; Crop health; Yield estimation", "DOI": "10.1007/s00521-024-09582-5", "PubYear": 2024, "Volume": "36", "Issue": "16", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Geographical Information Systems, National University of Sciences and Technology, Islamabad, Pakistan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Military College of Signals, National University of Science and Technology, Rawalpindi, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Geographical Information Systems, National University of Sciences and Technology, Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Geographical Information Systems, National University of Sciences and Technology, Islamabad, Pakistan"}], "References": [{"Title": "Remote sensing for agricultural applications: A meta-review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "236", "Issue": "", "Page": "111402", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Deep learning-based object detection in low-altitude UAV datasets: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "104046", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 113234738, "Title": "A Literature Review for Detection and Projection of Cardiovascular Disease Using Machine Learning", "Abstract": "<p>The heart is a vital organ that is indispensable in ensuring the general health and welfare of individuals. Cardiovascular diseases (CVD) are the major health concern worldwide and a leading cause of death, leaving behind diabetes and cancer. To deal with the problem, it is essential for early detection and prediction of CVDs, which can significantly reduce morbidity and mortality rates. Computer-aided techniques facilitate physicians in the diagnosis of many heart disorders, such as valve dysfunction, heart failure, etc. Living in an \"information age,\" every day million bytes of data are generated, and we can turn these data into knowledge for clinical investigation using the technique of data mining. Machine learning algorithms have shown promising results in predicting heart disease based on different risk parameter. In this study, for the purpose of predicting CVDs, our aim is to appraise and examine the outputs generated by machine learning algorithms including support vector machines, artificial neural network, logistic regression, random forest and decision trees.This literature survey highlights the correctness of different machine learning algorithms in forecasting heart problem and can be used as a basis for building a Clinical decision-making aid to detect and prevent heart disease at an early stage.</p>", "Keywords": "Support Vector Machine;SVM;<PERSON><PERSON>;K-Nearest Neighbor;Coronary artery disease;Arterial pressure;Data Mining;Decision tree", "DOI": "10.4108/eetiot.5326", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 46302, "JournalTitle": "EAI Endorsed Transactions on Internet of Things", "ISSN": "", "EISSN": "2414-1399", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sri Sri University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Siksha O Anusandhan University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Trident Academy of Creative Technology"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Trident Academy of Creative Technology"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sri Sri University"}], "References": [{"Title": "Heart Disease Prediction using Machine Learning Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "6", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 113237850, "Title": "Deep graph-level clustering using pseudo-label-guided mutual information maximization network", "Abstract": "<p>In this work, we study the problem of partitioning a set of graphs into different groups such that the graphs in the same group are similar while the graphs in different groups are dissimilar. This problem was rarely studied previously, although there has been a lot of work on node clustering and graph classification. The problem is challenging because it is difficult to measure the similarity or distance between graphs. One feasible approach is using graph kernels to compute a similarity matrix for the graphs and then performing spectral clustering, but the effectiveness of existing graph kernels in measuring the similarity between graphs is very limited. To solve the problem, we propose a novel method called Deep Graph-Level Clustering (DGLC). DGLC utilizes a graph isomorphism network to learn graph-level representations by maximizing the mutual information between the representations of entire graphs and sub-structures, under the regularization of a clustering module that ensures discriminative representations via pseudo-labels. DGLC achieves graph-level representation learning and graph-level clustering in an end-to-end manner. The experimental results on six benchmark datasets of graphs show that our DGLC has state-of-the-art performance in comparison to many baselines.</p>", "Keywords": "Graph neural network; Graph-level clustering; Graph kernel", "DOI": "10.1007/s00521-024-09575-4", "PubYear": 2024, "Volume": "36", "Issue": "16", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, China; Shenzhen Research Institute of Big Data, Shenzhen, China"}, {"AuthorId": 2, "Name": "Yi Han", "Affiliation": "School of Data Science, The Chinese University of Hong Kong, Shenzhen, China; Shenzhen Research Institute of Big Data, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, China; Corresponding author."}, {"AuthorId": 4, "Name": "Jicong Fan", "Affiliation": "School of Data Science, The Chinese University of Hong Kong, Shenzhen, China; Shenzhen Research Institute of Big Data, Shenzhen, China; Corresponding author."}], "References": [{"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Graph convolutional network with multi-similarity attribute matrices fusion for node classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "18", "Page": "13135", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Unsupervised deep clustering via contractive feature representation and focal loss", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108386", "JournalTitle": "Pattern Recognition"}, {"Title": "End-to-end variational graph clustering with local structural preservation", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "3767", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Graph alternate learning for robust graph neural networks in node classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "11", "Page": "8723", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Spatial-temporal dynamic semantic graph neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "19", "Page": "16655", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep multi-view subspace clustering via structure-preserved multi-scale features fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>un Su", "PubYear": 2023, "Volume": "35", "Issue": "4", "Page": "3203", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Learnable graph convolutional network and feature fusion for multi-view learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "109", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 113237898, "Title": "Analysis of Employment Information of University Graduates through Data Mining", "Abstract": "Abstract—: The employment information of university graduates contains a lot of useful information that can provide guidance for employment. This paper studied the decision tree method in data mining and improved C4.5 with <PERSON>’s median theorem in order to further improve its computational efficiency. The information of the 2021 graduates of China West Normal University was used as an example for analysis. It was found that practical ability, English level, and computer level had a great influence on graduate destination. In addition, compared with the traditional C4.5 algorithm, the improved C4.5 algorithm was much more efficient. The calculation time of the improved C4.5 algorithm was 6.27% shorter than the traditional C4.5 algorithm when analyzing 50 000 data. The improved C4.5 algorithm had an average accuracy of 89.81% when analyzing 200 data. The experimental results demonstrate the reliability of the improved C4.5 algorithm for employment information analysis and its applicability in practical employment management. © Allerton Press, Inc. 2024. ISSN 0146-4116, Automatic Control and Computer Sciences, 2024, Vol. 58, No. 1, pp. 58–65. Allerton Press, Inc., 2024.", "Keywords": ": data mining; decision tree; employment information; graduate", "DOI": "10.3103/S0146411624010073", "PubYear": 2024, "Volume": "58", "Issue": "1", "JournalId": 14245, "JournalTitle": "Automatic Control and Computer Sciences", "ISSN": "0146-4116", "EISSN": "1558-108X", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Center for Innovation and Entrepreneurship, China West Normal University, Sichuan, Nanchong, 637000, China"}], "References": [{"Title": "WITHDRAWN: College student employment data platform based on FPGA and machine learning", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "103471", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 113241972, "Title": "Sentence Completion as a User Experience Research Method: Recommendations From an Experimental Study", "Abstract": "<p>The aim of this study is to investigate the use of the sentence completion technique (SCT) as a user experience (UX) research method. We conducted an online experiment (N = 400) to test the effect of sentence stem variations on sentence completion outcomes. Using a between-subjects design, half of the participants were exposed to impersonal sentence stems that did not include pronouns while the other half were exposed to stems formulated using first-person pronouns (PR). Additional hypotheses around stem formats (use of redundant stems, imaginative stems, two blanks stems, generic stems, stems prompting others’ perception) were tested using a within-subject design. The results do not support hypothesized differences between the pronoun and no pronoun condition. Findings however show that varying the format of the stem influences response behavior, as measured by variety, quantity and novelty of ideas, as well as the length of response. This study contributes to consolidating the use of SCT as a user research method and proposes actionable recommendations on how to create optimal sentence completion surveys in Human-Computer Interaction (HCI).</p>", "Keywords": "", "DOI": "10.1093/iwc/iwae002", "PubYear": 2024, "Volume": "36", "Issue": "1", "JournalId": 5476, "JournalTitle": "Interacting with Computers", "ISSN": "0953-5438", "EISSN": "1873-7951", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Rhine-Waal University , Faculty of Communication and Environment, Friedrich-Heinrich-Allee 25, 47475 Kamp-Lintfort, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Rhine-Waal University , Faculty of Communication and Environment, Friedrich-Heinrich-Allee 25, 47475 Kamp-Lintfort, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Luxembourg , HCI research group, Maison des Sciences Humaines, 11 Porte des Sciences, L-4366 Esch-sur-Alzette, Luxembourg"}], "References": [{"Title": "A Systematic Literature Review of Empirical Methods and Risk Representation in Usable Privacy and Security Research", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}]}, {"ArticleId": 113254378, "Title": "Adaptive Importance Sampling Based on Fault Tree Analysis for Piecewise Deterministic Markov Process", "Abstract": "", "Keywords": "rare event simulation; reliability; importance sampling; piecewise deterministic Markov process; fault tree analysis; cross-entropy; PyCATSHOO; 65C05; 62L12; 65C60; 60J25", "DOI": "10.1137/22M1522838", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 11089, "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification", "ISSN": "", "EISSN": "2166-2525", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "EDF Lab Paris-Saclay, Boulevard Gaspard Monge, 91120 Palaiseau, France.;CMAP, École Polytechnique, Institut Polytechnique de Paris, 91120 Palaiseau, France."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "EDF Lab Paris-Saclay, Boulevard Gaspard Monge, 91120 Palaiseau, France."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "EDF Lab Paris-Saclay, Boulevard Gaspard Monge, 91120 Palaiseau, France."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CMAP, École Polytechnique, Institut Polytechnique de Paris, 91120 Palaiseau, France."}], "References": [{"Title": "The Future of Sensitivity Analysis: An essential discipline for systems modeling and policy support", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "137", "Issue": "", "Page": "104954", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Cross-Entropy-Based Importance Sampling with Failure-Informed Dimension Reduction for Rare Event Simulation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "2", "Page": "818", "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification"}]}, {"ArticleId": 113254453, "Title": "An optimal Bayesian intervention policy in response to unknown dynamic cell stimuli", "Abstract": "Interventions in gene regulatory networks (GRNs) aim to restore normal functions of cells experiencing abnormal behavior, such as uncontrolled cell proliferation. The dynamic, uncertain, and complex nature of cellular processes poses significant challenges in determining the best interventions. Most existing intervention methods assume that cells are unresponsive to therapies, resulting in stationary and deterministic intervention solutions. However, cells in unhealthy conditions can dynamically respond to therapies through internal stimuli, leading to the recurrence of undesirable conditions. This paper proposes a Bayesian intervention policy that adaptively responds to cell dynamic responses according to the latest available information. The GRNs are modeled using a Boolean network with perturbation (BNp), and the fight between the cell and intervention is modeled as a two-player zero-sum game. Assuming an incomplete knowledge of cell stimuli, a recursive approach is developed to keep track of the posterior distribution of cell responses. The proposed Bayesian intervention policy takes action according to the posterior distribution and a set of Nash equilibrium policies associated with all possible cell responses. Analytical results demonstrate the superiority of the proposed intervention policy against several existing intervention techniques. Meanwhile, the performance of the proposed policy is investigated through comprehensive numerical experiments using the p53-MDM2 negative feedback loop regulatory network and melanoma network. The results demonstrate the empirical convergence of the proposed policy to the optimal Nash equilibrium policy.", "Keywords": "Bayesian intervention;Boolean networks;Gene regulatory networks;Nash equilibrium;Two-player zero-sum game", "DOI": "10.1016/j.ins.2024.120440", "PubYear": 2024, "Volume": "666", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, 360 Huntington Ave, Boston, MA, 02115, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, 360 Huntington Ave, Boston, MA, 02115, United States of America"}], "References": [{"Title": "CABEAN: a software for the control of asynchronous Boolean networks", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "879", "JournalTitle": "Bioinformatics"}, {"Title": "An intelligent intervention strategy for patients to prevent chronic complications based on reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "612", "Issue": "", "Page": "1045", "JournalTitle": "Information Sciences"}, {"Title": "Sampled-data Control of Probabilistic Boolean Control Networks: A Deep Reinforcement Learning Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "374", "JournalTitle": "Information Sciences"}, {"Title": "Symmetrizable Boolean networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "626", "Issue": "", "Page": "787", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 113254526, "Title": "Terrain-Shape-Adaptive Coverage Path Planning With Traversability Analysis", "Abstract": "Coverage path planning (CPP) is in great demand with applications in agriculture, mining, manufacturing, etc. Most research in this area focused on 2D CPP problems solving the coverage problem with irregular 2D maps. Comparatively, CPP on uneven terrains is not fully solved. When there are many slopy areas in the working field, it is necessary to adjust the path shape and make it adapt to the 3D terrain surface to save energy consumption. This article proposes a terrain-shape-adaptive CPP method with three significant features. First, the paths grow by themselves according to the local terrain surface shapes. Second, the growth rule utilizes the 3D terrain traversability analysis, which makes them automatically avoid entering hazardous zones. Third, the irregularly distributed paths are connected under an optimal sequence with an improved genetic algorithm. As a result, the method can provide an autonomously growing terrain-adaptive coverage path with high energy efficiency and coverage rate compared to previous research works. It is demonstrated on various maps and is proven to be robust to terrain conditions.", "Keywords": "", "DOI": "10.1007/s10846-024-02073-8", "PubYear": 2024, "Volume": "110", "Issue": "1", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhou", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Hui", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 5, "Name": "Yubo Xing", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, China; Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, Shanghai, China; Corresponding author."}], "References": [{"Title": "Frontier-led swarming: Robust multi-robot coverage of unknown environments", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101171", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Region coverage-aware path planning for unmanned aerial vehicles: A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "59", "Issue": "", "Page": "102073", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 113254693, "Title": "Combining bag of visual words-based features with CNN in image classification", "Abstract": "<p>Although traditional image classification techniques are often used in authentic ways, they have several drawbacks, such as unsatisfactory results, poor classification accuracy, and a lack of flexibility. In this study, we introduce a combination of convolutional neural network (CNN) and support vector machine (SVM), along with a modified bag of visual words (BoVW)-based image classification model. BoVW uses scale-invariant feature transform (SIFT) and Oriented Fast and Rotated BRIEF (ORB) descriptors; as a consequence, the SIFT–ORB–BoVW model developed contains highly discriminating features, which enhance the performance of the classifier. To identify appropriate images and overcome challenges, we have also explored the possibility of utilizing a fuzzy Bag of Visual Words (BoVW) approach. This study also discusses using CNNs/SVM to improve the proposed feature extractor’s ability to learn more relevant visual vocabulary from the image. The proposed technique was compared with classic BoVW. The experimental results proved the significant enhancement of the proposed technique in terms of performance and accuracy over state-of-the-art models of BoVW.</p>", "Keywords": "", "DOI": "10.1515/jisys-2023-0054", "PubYear": 2024, "Volume": "33", "Issue": "1", "JournalId": 7501, "JournalTitle": "Journal of Intelligent Systems", "ISSN": "0334-1860", "EISSN": "2191-026X", "Authors": [{"AuthorId": 1, "Name": "Mar<PERSON> <PERSON>", "Affiliation": "Faculty of Computers and Artificial Intelligence, Information Technology Department, Matrouh University , Matrouh , 51511 , Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Information Systems and Computer Science, October 6 University , Giza , 12211 , Egypt"}], "References": [{"Title": "A hybrid codebook model for object categorization using two-way clustering based codebook generation method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "2", "Page": "178", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "A deep neural network and classical features based scheme for objects recognition: an application for machine inspection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "5", "Page": "14935", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An Experimental Assessment of Random Forest Classification Performance Improvisation with <PERSON><PERSON> and Stage Wise Success Rate Calculation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1711", "JournalTitle": "Procedia Computer Science"}, {"Title": "A review of multimodal image matching: Methods and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "73", "Issue": "", "Page": "22", "JournalTitle": "Information Fusion"}, {"Title": "Efficient Security Model for RDF Files Used in IoT Applications", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "233", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Local features integration for content-based image retrieval based on color, texture, and shape", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "18", "Page": "28245", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Content based Image Retrieval System using Local Feature Extraction Techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "20", "Page": "16", "JournalTitle": "International Journal of Computer Applications"}, {"Title": "Snowball Framework for Web Service Composition in SOA Applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "343", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Enhanced hybrid CBIR based on multichannel LBP oriented color descriptor and HSV color statistical feature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "17", "Page": "23801", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 113263471, "Title": "A novel joint neural collaborative filtering incorporating rating reliability", "Abstract": "Deep learning-based recommendations have demonstrated impressive performance in improving recommendation accuracy. However, such approaches mainly utilize implicit feedback to predict user preferences and neglect the adverse impact of explicit preference noise, which affects the robustness and reliability of model training. To consider the reliability of both rating input and output, we propose a novel joint deep neural recommendation framework that incorporates rating reliability derived solely from ratings to provide reliable recommendations for active users. Firstly, we introduce a noise detection method based on intuitionistic fuzzy sets to identify incorrect ratings from the perspective of fuzzy preferences and label them to generate a binary rating reliability matrix. Subsequently, we propose a joint deep neural framework that integrates rating reliability to simultaneously capture the high-order features of users and items, yielding predictions with their corresponding reliability probabilities. Finally, to achieve a balance between accuracy and reliability for recommendations, we design a reliability threshold selection strategy based on K-means clustering to find an appropriate threshold. Experimental results on three widely used datasets show that our model achieves an average improvement of 9.4% and 8.0% in the metrics Recall and NDCG, respectively, compared with the closest competitor. This paper provides new insights for integrating rating reliability into a deep neural network to enhance the performance of recommender systems.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120406", "PubYear": 2024, "Volume": "665", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "Jiangzhou Deng", "Affiliation": "Key Laboratory of Big Data Intelligent Computing, Chongqing University of Posts and Telecommunications, Chongqing 400065, China;Key Laboratory of Electronic Commerce and Logistics of Chongqing, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Electronic Commerce and Logistics of Chongqing, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Electronic Commerce and Logistics of Chongqing, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Big Data Intelligent Computing, Chongqing University of Posts and Telecommunications, Chongqing 400065, China;School of Business, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 5, "Name": "Peng<PERSON> Wang", "Affiliation": "Key Laboratory of Electronic Commerce and Logistics of Chongqing, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 6, "Name": "Maokang Du", "Affiliation": "Key Laboratory of Electronic Commerce and Logistics of Chongqing, Chongqing University of Posts and Telecommunications, Chongqing 400065, China;Corresponding author"}], "References": [{"Title": "An efficient and accurate recommendation strategy using degree classification criteria for item-based collaborative filtering", "Authors": "<PERSON><PERSON><PERSON>; Jiangzhou Deng; <PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113756", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Providing reliability in recommender systems through Bernoulli Matrix Factorization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "553", "Issue": "", "Page": "110", "JournalTitle": "Information Sciences"}, {"Title": "Sentiment based multi-index integrated scoring method to improve the accuracy of recommender system", "Authors": "Wenhua Li; Xiaoguang Li; Jiangzhou Deng", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "115105", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An effective and efficient fuzzy approach for managing natural noise in recommender systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "570", "Issue": "", "Page": "623", "JournalTitle": "Information Sciences"}, {"Title": "CoNet: Co-occurrence neural networks for recommendation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "308", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Neural Collaborative Filtering Classification Model to Obtain Prediction Reliabilities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "4", "Page": "18", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Alleviating data sparsity problem in time-aware recommender systems using a reliable rating profile enrichment approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115849", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Graph neural networks with global noise filtering for session-based recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "472", "Issue": "", "Page": "113", "JournalTitle": "Neurocomputing"}, {"Title": "Improved recommender systems by denoising ratings in highly sparse datasets through individual rating confidence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "601", "Issue": "", "Page": "242", "JournalTitle": "Information Sciences"}, {"Title": "Graph-aware collaborative reasoning for click-through rate prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "3", "Page": "967", "JournalTitle": "World Wide Web"}, {"Title": "Deep neural network-based multi-stakeholder recommendation system exploiting multi-criteria ratings for preference learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119071", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hierarchical fused fuzzy deep neural network with heterogeneous network embedding for recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "620", "Issue": "", "Page": "105", "JournalTitle": "Information Sciences"}, {"Title": "An improved autoencoder for recommendation to alleviate the vanishing gradient problem", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "263", "Issue": "", "Page": "110254", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "RDERL: Reliable deep ensemble reinforcement learning-based recommender system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "263", "Issue": "", "Page": "110289", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Ordinal consistency based matrix factorization model for exploiting side information in collaborative filtering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "643", "Issue": "", "Page": "119258", "JournalTitle": "Information Sciences"}, {"Title": "Providing prediction reliability through deep neural networks for recommender systems", "Authors": "Jiangzhou Deng; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "185", "Issue": "", "Page": "109627", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 113273392, "Title": "A Novel Methodology for Hunting Exoplanets in Space Using Machine Learning", "Abstract": "<p>INTRODUCTION: Exoplanet exploration outside of our solar system has recently attracted attention among astronomers worldwide. The accuracy of the currently used detection techniques, such as the transit and radial velocity approaches is constrained. Researchers have suggested utilizing machine learning techniques to create a prediction model to increase the identification of exoplanets beyond our milky way galaxy.\r OBJECTIVES: The novel method proposed in this research paper builds a prediction model using a dataset of known exoplanets and their characteristics, such as size, distance from the parent star, and orbital period. The model is then trained using this data based on machine learning methods that Support Vector Machines and Random Forests.\r METHODS: A different dataset of recognized exoplanets is used to assess the model’s accuracy, and the findings are compared with in comparison to accuracy rates of the transit and radial velocity approaches. \r RESULTS: The prediction model created in this work successfully predicts the presence of exoplanets in the test data-set with an accuracy rate of over 90 percent.\r CONCLUSION: This discovery shows the promise and confidence of machine learning techniques for exoplanet detection.</p>", "Keywords": "Neural Network;Exoplanets;Machine Learning;Support Vector Machine;Random Forest", "DOI": "10.4108/eetiot.5331", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 46302, "JournalTitle": "EAI Endorsed Transactions on Internet of Things", "ISSN": "", "EISSN": "2414-1399", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Galgotias University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Galgotias University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Galgotias University"}], "References": [{"Title": "FFT based ensembled model to predict ranks of higher educational institutions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "23", "Page": "34129", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Facilitating Healthcare Sector through IoT: Issues, Challenges, and Its Solutions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "4", "Page": "e5", "JournalTitle": "EAI Endorsed Transactions on Internet of Things"}, {"Title": "Deciphering Microorganisms through Intelligent Image Recognition: Machine Learning and Deep Learning Approaches, Challenges, and Advancements", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "", "JournalTitle": "EAI Endorsed Transactions on Internet of Things"}, {"Title": "Using Deep Learning and Machine Learning: Real-Time Discernment and Diagnostics of Rice-Leaf Diseases in Bangladesh", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "", "JournalTitle": "EAI Endorsed Transactions on Internet of Things"}, {"Title": "Identification and Categorization of Yellow Rust Infection in Wheat through Deep Learning Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "", "JournalTitle": "EAI Endorsed Transactions on Internet of Things"}]}, {"ArticleId": 113273415, "Title": "Accurate Localization in LOS/NLOS Channel Coexistence Scenarios Based on Heterogeneous Knowledge Graph Inference", "Abstract": "<p> Accurate localization is one of the basic requirements for smart cities and smart factories. In wireless cellular network localization, the straight-line propagation of electromagnetic waves between base stations and users is called line-of-sight (LOS) wireless propagation. In some cases, electromagnetic wave signals cannot propagate in a straight line due to obstruction by buildings or trees, and these scenarios are usually called non-LOS (NLOS) wireless propagation. Traditional localization algorithms such as TDOA, AOA, etc. , are based on LOS channels, which are no longer applicable in environments where NLOS propagation is dominant, and in most scenarios, the number of base stations with LOS channels containing users is often small, resulting in traditional localization algorithms being unable to satisfy the accuracy demand of high-precision localization. In addition, some nonideal factors may be included in the actual system, all of which can lead to localization accuracy degradation. Therefore, the approach developed in this paper uses knowledge graph and graph neural network (GNN) technology to model communication data as knowledge graphs, and it adopts the knowledge graph inference technique based on a heterogeneous graph attention mechanism to infer unknown data representations in complex scenarios based on the known data and the relationships between the data to achieve high-precision localization in scenarios with LOS/NLOS channel coexistence. We experimentally demonstrate a spatial 2D localization accuracy level of approximately 10 meters on multiple datasets and find that our proposed algorithm has higher accuracy and stronger robustness than the state-of-the-art algorithms. </p>", "Keywords": "", "DOI": "10.1145/3651618", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin University, Jinnan district, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin University, Jinnan district, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin University, Jinnan district, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin University, Jinnan district, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Computer Network Emergency Response Technical Team Coordination Center of China, Nankai district, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin University, Jinnan district, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin University, Jinnan district, China"}], "References": [{"Title": "A Survey on Deep Learning for Multimodal Data Fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "829", "JournalTitle": "Neural Computation"}, {"Title": "TLDS: A Transfer-Learning-Based Delivery Station Location Selection Pipeline", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Mobile Localization Techniques for Wireless Sensor Networks: Survey and Recommendations", "Authors": "<PERSON>; <PERSON>; Everton A. <PERSON>ra", "PubYear": 2023, "Volume": "19", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}]}, {"ArticleId": 113273460, "Title": "Analysis of the Application Potential of English Translation Strategy Based on Wireless Widget Technology in 5G Technology Reports: ", "Abstract": "<p>As technology continues to evolve, the process of English translation has become easier. A technology called widget, which is used in modern research, provides an efficient graphical user interface for the interaction between the user and the application. This paper compares the newly proposed wireless widget system with existing models of English translation strategies for internet of things (IoT), cloud computing (CC), artificial intelligence and edge computing (AI+EC), and pure internet concepts. These models were compared on metrics such as accuracy, search completion rate, and throughput. The results show that the proposed system outperforms the existing models with an accuracy rate of 98%, a search completion rate of 96%, and a maximum throughput of 96%. The model shows high performance advantages and can effectively improve the efficiency of translation work.</p>", "Keywords": "", "DOI": "10.4018/IJICTE.339969", "PubYear": 2024, "Volume": "20", "Issue": "1", "JournalId": 22087, "JournalTitle": "International Journal of Information and Communication Technology Education", "ISSN": "1550-1876", "EISSN": "1550-1337", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Henan Medical College, China"}], "References": [{"Title": "Vision, challenges, roles and research issues of Artificial Intelligence in Education", "Authors": "G<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "100001", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "A multi-perspective study on Artificial Intelligence in Education: grants, conferences, journals, software tools, institutions, and researchers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "100005", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Edge computing and 5G based l ow‐delay business E nglish translation framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "5", "Page": "e321", "JournalTitle": "Internet Technology Letters"}]}, {"ArticleId": 113278184, "Title": "Heterogeneous Dual-Dynamic Attention Network for Modeling Mutual Interplay of Stocks", "Abstract": "", "Keywords": "", "DOI": "10.1109/TAI.2024.3374269", "PubYear": 2024, "Volume": "5", "Issue": "7", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Intelligent Networks and Network Security, Faculty of Electronic and Information Engineering, Xi&#x2019;an Jiaotong University, Xi&#x2019;an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Intelligent Networks and Network Security, Faculty of Electronic and Information Engineering, Xi&#x2019;an Jiaotong University, Xi&#x2019;an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Intelligent Networks and Network Security, Faculty of Electronic and Information Engineering, Xi&#x2019;an Jiaotong University, Xi&#x2019;an, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Intelligent Networks and Network Security, Faculty of Electronic and Information Engineering, Xi&#x2019;an Jiaotong University, Xi&#x2019;an, China"}], "References": [{"Title": "Multi-source aggregated classification for stock price movement prediction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "515", "JournalTitle": "Information Fusion"}]}]