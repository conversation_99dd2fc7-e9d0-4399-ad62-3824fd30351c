[{"ArticleId": 84728691, "Title": "Why me?", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3416959", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Chile, Santiago"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chile, Santiago"}], "References": []}, {"ArticleId": 84728692, "Title": "Digital healthcare in Latin America", "Abstract": "", "Keywords": "", "DOI": "10.1145/3423923", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ensenada Center for Scientific Research and Higher Education, Ensenada, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory for Scientific Computing, Petrópolis, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fluminense Federal University, Niterói, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Ensenada Center for Scientific Research and Higher Education, Ensenada, Mexico"}], "References": []}, {"ArticleId": 84728693, "Title": "Technical perspective: When the adversary is your friend", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3422602", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "UC Berkeley"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Adobe in San Francisco, CA"}], "References": []}, {"ArticleId": 84728727, "Title": "The Latin American supercomputing ecosystem for science", "Abstract": "", "Keywords": "", "DOI": "10.1145/3419977", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Research and Advanced Studies (CINVESTAV) in Mexico City, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Laboratory for Scientific Computing (LNCC), Petrópolis, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad de la República in Montevideo, Uruguay"}], "References": [{"Title": "BioinfoPortal: A scientific gateway for integrating bioinformatics applications on the Brazilian national high-performance computing network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "192", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 84728728, "Title": "Reason-checking fake news", "Abstract": "Using argument technology to strengthen critical literacy skills for assessing media reports.", "Keywords": "", "DOI": "10.1145/3397189", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Dundee, in the U.K"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Dundee, in the U.K"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Dundee, in the U.K"}], "References": [{"Title": "Argument Mining: A Survey", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "4", "Page": "765", "JournalTitle": "Computational Linguistics"}]}, {"ArticleId": 84728734, "Title": "Generative adversarial networks", "Abstract": "Generative adversarial networks are a kind of artificial intelligence algorithm designed to solve the\n generative modeling \n problem. The goal of a generative model is to study a collection of training examples and learn the probability distribution that generated them. Generative Adversarial Networks (GANs) are then able to generate more examples from the estimated probability distribution. Generative models based on deep learning are common, but GANs are among the most successful generative models (especially in terms of their ability to generate realistic high-resolution images). GANs have been successfully applied to a wide variety of tasks (mostly in research settings) but continue to present unique challenges and research opportunities because they are based on game theory while most other approaches to generative modeling are based on optimization.", "Keywords": "", "DOI": "10.1145/3422622", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Google Brain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université de Montréal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Montréal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Université de Montréal"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université de Montréal"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université de Montréal"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Université de Montréal"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université de Montréal"}], "References": []}, {"ArticleId": 84728748, "Title": "Five nonobvious remote work techniques", "Abstract": "Emulating the efficiency of in-person conversations.", "Keywords": "", "DOI": "10.1145/3410627", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Stack Overflow Inc. in New York City"}], "References": []}, {"ArticleId": 84728749, "Title": "Understanding salsa", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3416967", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Icesi in Cali, Colombia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> M.", "Affiliation": "Pontificia Universidad Javeriana Cali, Colombia"}], "References": []}, {"ArticleId": 84728750, "Title": "Data on the outside versus data on the inside", "Abstract": "Data kept outside SQL has different characteristics from data kept inside.", "Keywords": "", "DOI": "10.1145/3410623", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Salesforce"}], "References": []}, {"ArticleId": 84728751, "Title": "A panorama of computing in central America and the Caribbean", "Abstract": "", "Keywords": "", "DOI": "10.1145/3419979", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Honduras"}], "References": []}, {"ArticleId": 84728752, "Title": "Weighing grad school payback", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3425745", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": " CACM Staff", "Affiliation": ""}], "References": []}, {"ArticleId": 84728754, "Title": "Where should your IT constraint be?", "Abstract": "Locating the strategic location of the IT junction constraint.", "Keywords": "", "DOI": "10.1145/3382081", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tel Aviv University, Israel"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tel Aviv-Yaffo, Israel"}], "References": []}, {"ArticleId": 84728758, "Title": "BLeak", "Abstract": "Memory leaks in web applications are pervasive and difficult to debug. Leaks degrade responsiveness by increasing garbage collection costs and can even lead to browser tab crashes. Previous leak detection approaches designed for conventional applications are ineffective in the browser environment. Tracking down leaks currently requires intensive manual effort by web developers, which is often unsuccessful.\n This paper introduces BLEAK (Browser Leak debugger), the first system for automatically debugging memory leaks in web applications. BLEAK'S algorithms leverage the observation that in modern web applications, users often repeatedly return to the same (approximate) visual state (e.g., the inbox view in Gmail). Sustained growth between round trips is a strong indicator of a memory leak. To use BLEAK, a developer writes a short script (17-73 LOC on our benchmarks) to drive a web application in round trips to the same visual state. BLEAK then automatically generates a list of leaks found along with their root causes, ranked by return on investment. Guided by BLEAK, we identify and fix over 50 memory leaks in popular libraries and apps including Airbnb, AngularJS, Google Analytics, Google Maps SDK, and jQuery. BLEAK'S median precision is 100%; fixing the leaks it identifies reduces heap growth by an average of 94%, saving from 0.5MB to 8MB per round trip.", "Keywords": "", "DOI": "10.1145/3422598", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Massachusetts Amherst"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Massachusetts Amherst"}], "References": []}, {"ArticleId": 84728761, "Title": "The graph isomorphism problem", "Abstract": "Exploring the theoretical and practical aspects of the graph isomorphism problem.", "Keywords": "", "DOI": "10.1145/3372123", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "TU Kaiserslautern, Germany"}], "References": []}, {"ArticleId": 84728762, "Title": "A tour of dependable computing research in Latin America", "Abstract": "", "Keywords": "", "DOI": "10.1145/3416979", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal University of Paraná, Curitiba, Brazil"}, {"AuthorId": 2, "Name": "Raimundo J. <PERSON>", "Affiliation": "Federal University of Bahia, Salvador, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State University of Campinas, Campinas, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Nacional Autónoma de México, in Mexico City, México"}], "References": []}, {"ArticleId": 84728764, "Title": "Contextualized interpretable machine learning for medical diagnosis", "Abstract": "", "Keywords": "", "DOI": "10.1145/3416965", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 2, "Name": "Antonio L. P. <PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}], "References": []}, {"ArticleId": 84728766, "Title": "Three success stories about compact data structures", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3416971", "PubYear": 2020, "Volume": "63", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Técnica Federico Santa María and Millennium Institute for Foundational Research on Data, Santiago, Chile"}, {"AuthorId": 2, "Name": "<PERSON>-Sepü<PERSON>a", "Affiliation": "Universidad de Concepción, Chile"}, {"AuthorId": 3, "Name": "Diego Seco", "Affiliation": "Universidad de Concepción and Millennium Institute for Foundational Research on Data, Santiago, Chile"}], "References": [{"Title": "Fully Functional Suffix Trees and Optimal Text Searching in BWT-Runs Bounded Space", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "67", "Issue": "1", "Page": "1", "JournalTitle": "Journal of the ACM"}]}, {"ArticleId": 84729001, "Title": "Eye movements to absent objects during mental imagery and visual memory in immersive virtual reality", "Abstract": "Abstract \nThe role of eye movements in mental imagery and visual memory is typically investigated by presenting stimuli or scenes on a two-dimensional (2D) computer screen. When questioned about objects that had previously been presented on-screen, people gaze back to the location of the stimuli, even though those regions are blank during retrieval. It remains unclear whether this behavior is limited to a highly controlled experimental setting using 2D screens or whether it also occurs in a more naturalistic setting. The present study aims to overcome this shortcoming. Three-dimensional (3D) objects were presented along a circular path in an immersive virtual room. During retrieval, participants were given two tasks: to visualize the objects, which they had encoded before, and to evaluate a statement about visual details of the object. We observed longer fixation duration in the area, on which the object was previously displayed, when compared to other possible target locations. However, in 89% of the time, participants fixated none of the predefined areas. On the one hand, this shows that looking at nothing may be overestimated in 2D screen-based paradigm, on the other hand, the looking at nothing effect was still present in the 3D immersive virtual reality setting, and thus it extends external validity of previous findings. Eye movements during retrieval reinstate spatial information of previously inspected stimuli.", "Keywords": "Virtual reality; Mental imagery; Visual memory; Eye movements; Eye tracking", "DOI": "10.1007/s10055-020-00478-y", "PubYear": 2021, "Volume": "25", "Issue": "3", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Bern, Bern, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Swiss Distance University Institute, Brig, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Bern, Bern, Switzerland"}], "References": []}, {"ArticleId": 84729058, "Title": "Fault detection of continuous glucose measurements based on modified k-medoids clustering algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-020-05432-2", "PubYear": 2020, "Volume": "", "Issue": "", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Hong<PERSON>", "Affiliation": ""}], "References": [{"Title": "ADARC: An anomaly detection algorithm based on relative outlier distance and biseries correlation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "11", "Page": "2065", "JournalTitle": "Software: Practice and Experience"}]}, {"ArticleId": 84729061, "Title": "Optimal work-conserving scheduler synthesis for real-time sporadic tasks using supervisory control of timed discrete-event systems", "Abstract": "<p>Real-time scheduling strategies for safety-critical systems are primarily focused on ensuring correctness, both functional and temporal. In order to provide the desired predictability in such systems, it is often advisable that all timing requirements be guaranteed offline , before putting the system into operation. Formal approaches allow for all necessary and sufficiency conditions corresponding to a feasible schedule to be checked in a systematic manner. This enables formal approaches to act as effective mechanisms for providing timing guarantees required by safety-critical systems. In this work, we develop a scheduler synthesis framework for the optimal work-conserving scheduling of dynamically arriving, sporadic tasks using a formal approach known as “supervisory control of timed discrete-event systems” (SCTDES). The synthesis process starts with the construction of a resource-constraint-aware task execution model and a deadline-constraint-aware timing specification model, for each task in the given real-time system. The system model (i.e., composite task execution model) is then derived and transformed to guarantee work-conserving co-execution of tasks. Such a work-conserving approach enables the synthesis of schedules which avoid processor idling in the presence of ready-to-execute tasks. Next, we use the (transformed) system and specification models to obtain a supervisor which can be used to construct an optimal scheduler for the given real-time system. Finally, the applicability of the proposed scheme for real-world scenarios is shown by presenting a case study on an instrument control system (ICS).</p>", "Keywords": "Real-time systems; Sporadic task; Optimal scheduling; Supervisory control; Discrete-event systems", "DOI": "10.1007/s10951-020-00669-0", "PubYear": 2021, "Volume": "24", "Issue": "1", "JournalId": 9977, "JournalTitle": "Journal of Scheduling", "ISSN": "1094-6136", "EISSN": "1099-1425", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Guwahati, Guwahati, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Guwahati, Guwahati, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Guwahati, Guwahati, India"}], "References": []}, {"ArticleId": 84729089, "Title": "An optimized XGBoost based diagnostic system for effective prediction of heart disease", "Abstract": "Researchers have created several expert systems over the years to predict heart disease early and assist cardiologists to enhance the diagnosis process. We present a diagnostic system in this paper that utilizes an optimized XGBoost (Extreme Gradient Boosting) classifier to predict heart disease. Proper hyper-parameter tuning is essential for any classifier’s successful application. To optimize the hyper-parameters of XGBoost, we used Bayesian optimization, which is a very efficient method for hyper-parameter optimization. We also used One-Hot (OH) encoding technique to encode categorical features in the dataset to improve prediction accuracy. The efficacy of the proposed model is evaluated on Cleveland heart disease dataset and compared it with Random Forest (RF) and Extra Tree (ET) classifiers. Five different evaluation metrics: accuracy, sensitivity, specificity, F1-score, and AUC (area under the curve) of ROC charts were used for performance evaluation. The experimental results showed its validity and efficacy in the prediction of heart disease. In addition, proposed model displays better performance compared to the previously suggested models. Moreover, our proposed method reaches the high prediction accuracy of 91.8%. Our results indicate that the proposed method could be used reliably to predict heart disease in the clinic.", "Keywords": "XGBoost ; Bayesian Optimization ; Categorical feature encoding ; Heart Disease ; Prediction", "DOI": "10.1016/j.jksuci.2020.10.013", "PubYear": 2022, "Volume": "34", "Issue": "7", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science & Engineering, Samrat <PERSON>ok Technological Institute, Vidisha, Madhya Pradesh, India;Corresponding author at: <PERSON><PERSON>, Sai Enclave, Vidisha, Madhya Pradesh 464001, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science & Engineering, Samrat Ashok Technological Institute, Vidisha, Madhya Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science & Engineering, Samrat Ashok Technological Institute, Vidisha, Madhya Pradesh, India"}], "References": []}, {"ArticleId": 84729093, "Title": "Quantifying wind-driven rain on a heritage facade through computational fluid dynamics", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAET.2021.10033075", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84729095, "Title": "Analysis, identification and design of robust control techniques for ultra-lift Luo DC-DC converter powered by fuel cell", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAET.2021.10033079", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84729121, "Title": "Geometry assessment of ultra-short pulsed laser drilled micro-holes", "Abstract": "Abstract \nUltra-short pulsed laser ablation enables a defined generation of micro-holes. A parameter study on the ablation characteristics of copper clearly reveals a benefit for green wavelength with lower threshold fluence, simultaneously increasing the Rayleigh length. The use of a circular drilling method allows a defined manufacturing of micro boreholes and micro through-holes with 35 μm diameter of up to 165 μm and 300 μm length. Introducing high-resolution micro-computed X-ray tomography studying the micro-hole evolution and adjacent geometrical transformations reveals micrometer resolution and high usability. The conical geometry evolving up to an aspect ratio of 5:1 fits well to established models known for percussion drilling. However, increasing the number of pulses leads to non-conical geometry evolution, and this resulting geometry is studied for the first time. Henceforth, the exact geometrical evolution from conical to cylindrical shape upon laser drilling can be resolved revealing the impact of multiple reflections at the generated steep flanks.", "Keywords": "Ultra-short pulsed laser; Micro-holes; Micro-computed tomography; Circular path drilling", "DOI": "10.1007/s00170-020-06199-5", "PubYear": 2021, "Volume": "117", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "inspire AG, Zurich, Switzerland;Department of Mechanical Engineering, IWF, ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, IWF, ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, IWF, ETH Zurich, Zurich, Switzerland"}], "References": []}, {"ArticleId": 84729130, "Title": "The rapid development of knowledge bases using UML class diagrams", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAET.2021.10033073", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84729131, "Title": "A hybrid approach of firefly and genetic algorithm for solving optimisation problems", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAET.2021.10033076", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84729164, "Title": "STIR-RST: A Software tool for reactive smart tracer studies", "Abstract": "The introduction of “smart” tracer techniques in recent years has provided new ways to investigate sediment-water interactions and microbial activity in stream corridors. In this study, the formulation of the STIR model (<PERSON> et al., 2008) is extended to represent the transport and transformation of Resazurin-Resorufin smart tracers, and an object-oriented toolbox, STIR-RST, is presented for model evaluation and calibration. STIR-RST allows different storage processes to be represented by specific residence time distributions (RTDs), with two possible arrangements of the storage zones: nested (in-series) or competing (in-parallel). The application of STIR-RST to field tracer data is demonstrated assuming two storage zones with exponential RTD. Results show that the assumption of two storage zones provides a better approximation of the observed BTCs compared to that of a single storage zone, at the cost of higher parameter uncertainty. Similar fits are obtained for nested and competing zone arrangements.", "Keywords": "Solute transport ; Modelling ; Smart tracers ; Resazurin ; Resorufin ; Multiple storage zones", "DOI": "10.1016/j.envsoft.2020.104894", "PubYear": 2021, "Volume": "135", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical, Aerospace and Civil Engineering, University of Manchester, Manchester, M13 9PL, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Padua, Via F. Marzolo 9, 35131, Padova, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Padua, Via F. Marzolo 9, 35131, Padova, Italy"}], "References": []}, {"ArticleId": 84729256, "Title": "Moving target extraction and background reconstruction algorithm", "Abstract": "<p>It is difficult for the computer to distinguish the target from the background due to the long-time static of the target after moving. A new moving target detection and background reconstruction algorithm is proposed and is applied into the RGB video for the first time. Firstly, the proposed algorithm builds a model from the time dimension to extract the changed region. Then, it combines with the space dimension information to completely extract the moving target. The spatiotemporal correlation model is established to realize the construction of pure background. The experimental results show that the proposed algorithm can effectively reconstruct the background and the recognition rate of moving target is high.</p>", "Keywords": "Moving target extraction; Background reconstruction; RGB; Time dimension; Space dimension", "DOI": "10.1007/s12652-020-02619-2", "PubYear": 2023, "Volume": "14", "Issue": "5", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Spectral Imaging Technology CAS, Xi’an Institute of Optics and Precision Mechanics, Chinese Academy of Sciences, Xi’an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shang hai, People’s Republic of China"}], "References": [{"Title": "Moving target detection based on improved Gaussian mixture model considering camera motion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7005", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A moving vehicle tracking algorithm based on deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 84729269, "Title": "Multi-issue negotiation with deep reinforcement learning", "Abstract": "Negotiation is a process where agents work through disputes and maximize surplus. This paper investigates the use of deep reinforcement learning in the domain of negotiation, evaluating its ability to exploit, adapt, and cooperate. Two actor–critic networks were trained for the bidding and acceptance strategy, against time-based agents, behavior-based agents, and through self-play. Results reveal four key findings. First, neural agents learn to exploit time-based agents, achieving clear transitions in decision values. The primary barriers are the change in marginal utility (second derivative) and cliff-walking resulting from negotiation deadlines. Second, the Cauchy distribution emerges as suitable for sampling offers, due to its peaky center and heavy tails. Third, neural agents demonstrate adaptive behavior against behavior-based agents. Fourth, neural agents learn to cooperate during self-play. Agents learn non-credible threats, which resemble reputation-based strategies in the evolutionary game theory literature.", "Keywords": "Deep reinforcement learning ; Negotiation ; Game theory", "DOI": "10.1016/j.knosys.2020.106544", "PubYear": 2021, "Volume": "211", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, University of Edinburgh, 10 Crichton St, Edinburgh, UK Annenberg School of Communication and Journalism, University of Southern California, United States of America;Correspondence to: School of Informatics, University of Edinburgh, 10 Crichton St, Edinburgh, UK"}], "References": []}, {"ArticleId": 84729288, "Title": "NEW TECHNOLOGIES AND APPLICATIONS OF MOTION TRACKING IN VIRTUAL REALITY SYSTEMS", "Abstract": "This article is centered around the sort of virtual reality (VR) – MoCap – motion capture. VR is utilized as a part of the planning and design procedure of the working environment. Firstly, the work environment is designed and before actual building the various conditions are simulated. Product of this simulation by VR is the working environment/ building. VR is utilized too in the medical treatments. This paper investigates a way to deal with physical rehabilitation using best in class advances in virtual reality and motion tracking. As of present, there has been a boom in the utilization of the Kalman channel in Virtual/ Augmented Reality. This paper presents a brief introduction to the Kalman filter and the development of the utilization of the filter in VR motion tracking.", "Keywords": "Motion, tracking, virtual reality, VR, Motion capture, MoCap", "DOI": "10.26483/ijarcs.v11i5.6658", "PubYear": 2020, "Volume": "11", "Issue": "5", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota, Twin Cities Minnesota"}], "References": []}, {"ArticleId": 84729376, "Title": "NEIGHBOR SCORE: IMPACT OF NEIGHBORING REGIONS IN COVID-19 OUTBREAK", "Abstract": "COVID-19 has affected people from all over the globe. This highly contagious disease is spreading from one region to another by people through traveling. The authorities are now trying to identify high-risk and low-risk regions considering each region as isolated. In reality, the regions of a country are connected through buses, trains and, airplanes, etc and peoples are taking steps to go elsewhere to earn their livelihood. So, preventive and protective measures like lock-down fail to be effective. In this paper, we propose a metric named   Neighbor Score to determine the actual invaded scenario of a region considering its adjacent i.e. neighbor regions. As a part of this research, an experiment is conducted with different COVID-19 affected regions of Bangladesh. The experiment has found that the coefficient correlation value of neighboring regions' total COVID-19 cases is positive (0.54 for 50% regions). We have also ranked and zoned the different regions of Bangladesh applying the Neighbor Score .", "Keywords": "covid-19;outbreak;neighbor;region;network;metric;Bangladesh", "DOI": "10.26483/ijarcs.v11i5.6649", "PubYear": 2020, "Volume": "11", "Issue": "5", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lecturer"}], "References": []}, {"ArticleId": 84729432, "Title": "Climate’s role in Earth’s shifting spheres", "Abstract": "Purpose \nThis paper aims to present the physics of climate and climate change in an accessible manner to the layman in the context of shifting spheres.\n \n \n Design/methodology/approach \nThis paper presents the physics of climate and climate change in an accessible manner to the layman in the context of shifting spheres. This is a viewpoint and more of a literature review than new findings.\n \n \n Findings \nEarth's climate is changing due to man's influence.\n \n \n Social implications \nClimate change will be a major factor in the future of our society.\n \n \n Originality/value \nThe text is original. The information is not. There is recent information in this article. The author even updated things during the review process. The science is always improving.", "Keywords": "Climate change;Climate physics;Greenhouse gasses;Heat budget", "DOI": "10.1108/K-05-2020-0326", "PubYear": 2021, "Volume": "50", "Issue": "4", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "China-ASEAN College of Marine Science, Xiamen University Malaysia , Sepang, Malaysia"}], "References": []}, {"ArticleId": 84729543, "Title": "Anti-unification and the theory of semirings", "Abstract": "It was recently shown that anti-unification over an equational theory consisting of only unit equations (more than one) is nullary. Such pure theories are artificial and are of little effect on practical aspects of anti-unification. In this work, we extend these nullarity results to the theory of semirings, a heavily studied theory with many practical applications. Furthermore, our argument holds over semirings with commutative multiplication and/or idempotent addition. We also cover a few open questions discussed in previous work.", "Keywords": "Anti-unification ; Semirings ; Nullary", "DOI": "10.1016/j.tcs.2020.10.020", "PubYear": 2020, "Volume": "848", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON> Univerisity (JKU), Research Institute for Symbolic Computation (RISC), Altenbergerstrasse 69, 4040 Linz, Austria;The Czech Academy of Science, Institute of Computer Science (CAS ICS), Pod Vodárenskou věží 271/2 182 07, Prague, Czechia;Correspondence to: Johannes <PERSON> Univerisity (JKU), Research Institute for Symbolic Computation (RISC), Altenbergerstrasse 69, 4040 Linz, Austria"}], "References": [{"Title": "Idempotent Anti-unification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Computational Logic"}]}, {"ArticleId": 84729705, "Title": "Runtime verification for dynamic architectures", "Abstract": "The architecture of a system captures important design decisions for the system. Over time, changes in a system's implementation may lead to violations of specific design decisions. This problem is common in industry and known as architectural erosion. Since it may have severe consequences on the quality of a system, research has focused on the development of tools and techniques to address the presented problem. As of today, most of the approaches to detect architectural erosion employ static analysis techniques. While these techniques are well-suited for the analysis of static architectures, they reach their limit when it comes to dynamic architectures. Thus, in this paper, we propose an alternative approach based on runtime verification: We describe techniques to formally specify constraints for dynamic architectures and algorithms to translate such specifications to instrumentation code and corresponding monitors. The approach is implemented in Eclipse/EMF, demonstrated through a running example, and evaluated using two case studies.", "Keywords": "", "DOI": "10.1016/j.jlamp.2020.100618", "PubYear": 2021, "Volume": "118", "Issue": "", "JournalId": 781, "JournalTitle": "Journal of Logical and Algebraic Methods in Programming", "ISSN": "2352-2208", "EISSN": "2352-2216", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Exeter, Department of Computer Science, EX4 4QF, Exeter, UK;Corresponding author;http://www.marmsoler.com"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technische Universität München, Institut für Informatik, 85748 Garching bei München, Germany"}], "References": []}, {"ArticleId": 84729725, "Title": "Rapid report 2: Symptoms of anxiety and depression during the first 12 weeks of the Coronavirus (COVID-19) pandemic in Australia", "Abstract": "<p><b>Background</b>:The MindSpot Clinic, funded by the Australian Government, is a national digital mental health service (DMHS) providing services to people experiencing anxiety and depression. We recently reported increased service use in the early weeks of the COVID-19 pandemic (19 March to 15 April 2020), and a small increase in anxiety symptoms. This follow-up paper examines trends in service use and symptoms, over 12 weeks from 19 March to 10 June 2020.</p><p><b>Methods</b>:Demographics, symptoms, and psychosocial stressors were compared for participants starting an online assessment over four time-periods: A baseline \"Comparison period\" prior to the COVID-19 pandemic (1 to 28 September 2019), \"Weeks 1 - 4\" of the COVID-19 pandemic in Australia (19 March - 15 April 2020), \"Weeks 5 - 8\" (16 April - 13 May 2020) and \"Weeks 9 - 12\" (14 May - 10 June). Responses to questions about the impact of COVID-19 and strategies used by participants to improve their mental wellbeing are also reported.</p><p><b>Results</b>:A total of 5,455 people started a mental health assessment with MindSpot from 19 March to 10 June 2020. The number of assessments per week rose steadily from 303 in week 1 to a peak of 578 in week 5. Symptoms of anxiety were highest in Weeks 1 - 4, declining steadily over subsequent weeks. Psychological distress and depression, as measured by the K-10 and PHQ-9 respectively, remained stable. Concern about COVID-19 was highest in the first week then steadily declined during the following weeks. The proportions of participants reporting changes to routine were consistent across the 12 weeks, and most participants reported adopting helpful strategies to improve their mental wellbeing.</p><p><b>Conclusions</b>:We observed an initial increase in service use, which reduced over the 12 weeks. The initial rise in anxiety symptoms returned to baseline. Reported concern about the effect of COVID-19 declined steadily over 12 weeks. Symptoms of psychological distress and depression measured by the K-10 and PHQ-9, and the proportion reporting suicidal thoughts and plans did not change, and to date we have not identified indications of a mental health crisis. However, the long-term effects of COVID-19 on the economy and large sections of society are yet to be fully realised, indicating the importance of ongoing monitoring and reporting of trends as indicators of the mental health of the nation.</p><p>Crown Copyright © 2020 Published by Elsevier B.V.</p>", "Keywords": "COVID-19;anxiety;depression;internet;mental health;service implementation", "DOI": "10.1016/j.invent.2020.100351", "PubYear": 2020, "Volume": "22", "Issue": "", "JournalId": 11817, "JournalTitle": "Internet Interventions", "ISSN": "2214-7829", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia. ;Faculty of Medicine and Health Sciences, Macquarie University, Australia."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia. ;eCentreClinic, Department of Psychology, Macquarie University, Sydney, Australia."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia. ;eCentreClinic, Department of Psychology, Macquarie University, Sydney, Australia."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MindSpot Clinic, Macquarie University, Australia. ;eCentreClinic, Department of Psychology, Macquarie University, Sydney, Australia."}], "References": [{"Title": "The COVID-19 pandemic: The ‘black swan’ for mental health care and a turning point for e-health", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "", "Page": "100317", "JournalTitle": "Internet Interventions"}, {"Title": "Rapid report: Early demand, profiles and concerns of mental health users during the coronavirus (COVID-19) pandemic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "", "Page": "100327", "JournalTitle": "Internet Interventions"}]}, {"ArticleId": 84729760, "Title": "Retrofitting Soft Rules for Knowledge Representation Learning", "Abstract": "Recently, a significant number of studies have focused on knowledge graph completion using rule-enhanced learning techniques, supported by the mined soft rules in addition to the hard logic rules. However, due to the difficulty in determining the confidence of the soft rules without the global semantics of knowledge graph such as the semantic relatedness between relations, the knowledge representation may not be optimal, leading to degraded effectiveness in its application to knowledge graph completion tasks. To address this challenge, this paper proposes a retrofit framework that iteratively enhances the knowledge representation and confidence of soft rules. Specifically, the soft rules guide the learning of knowledge representation, and the representation, in turn, provides global semantics of the knowledge graph to optimize the confidence of soft rules. Extensive evaluation shows that our method achieves state-of-the-art results on link prediction and triple classification tasks, brought by the fine-tuned soft rules.", "Keywords": "Knowledge representation ; Soft rules ; Link prediction", "DOI": "10.1016/j.bdr.2020.100156", "PubYear": 2021, "Volume": "24", "Issue": "", "JournalId": 3265, "JournalTitle": "Big Data Research", "ISSN": "2214-5796", "EISSN": "2214-580X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "Xianpei Han", "Affiliation": "State Key Laboratory of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "Le Sun", "Affiliation": "State Key Laboratory of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China"}], "References": []}, {"ArticleId": 84729786, "Title": "Hybrid Approach to Document Anomaly Detection: An Application to Facilitate RPA in Title Insurance", "Abstract": "<p>Anomaly detection (AD) is an important aspect of various domains and title insurance (TI) is no exception. Robotic process automation (RPA) is taking over manual tasks in TI business processes, but it has its limitations without the support of artificial intelligence (AI) and machine learning (ML). With increasing data dimensionality and in composite population scenarios, the complexity of detecting anomalies increases and AD in automated document management systems (ADMS) is the least explored domain. Deep learning, being the fastest maturing technology can be combined along with traditional anomaly detectors to facilitate and improve the RPAs in TI. We present a hybrid model for AD, using autoencoders (AE) and a one-class support vector machine (OSVM). In the present study, OSVM receives input features representing real-time documents from the TI business, orchestrated and with dimensions reduced by AE. The results obtained from multiple experiments are comparable with traditional methods and within a business acceptable range, regarding accuracy and performance.</p>", "Keywords": "Anomaly detection; title insurance; autoencoder; one-class support vector machine (OSVM); term frequency — inverse document frequency (TF-IDF); robotic process automation; dimensionality reduction", "DOI": "10.1007/s11633-020-1247-y", "PubYear": 2021, "Volume": "18", "Issue": "1", "JournalId": 6782, "JournalTitle": "International Journal of Automation and Computing", "ISSN": "1476-8186", "EISSN": "1751-8520", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Data Science Department, CHRIST (Deemed to be University), Bangalore, India;First American India Private Ltd., Bangalore, India"}, {"AuthorId": 2, "Name": "De<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, CHRIST (Deemed to be University), Bangalore, India"}], "References": []}, {"ArticleId": 84729861, "Title": "Reproducible execution of POSIX programs with DiOS", "Abstract": "<p>In this paper, we describe DiOS , a lightweight model operating system, which can be used to execute programs that make use of POSIX APIs. Such executions are fully reproducible: running the same program with the same inputs twice will result in two exactly identical instruction traces, even if the program uses threads for parallelism. DiOS is implemented almost entirely in portable C and C++: although its primary platform is DiVM , a verification-oriented virtual machine, it can be configured to also run in KLEE, a symbolic executor. Finally, it can be compiled into machine code to serve as a user-mode kernel. Additionally, DiOS is modular and extensible. Its various components can be combined to match both the capabilities of the underlying platform and to provide services required by a particular program. Components can be added to cover additional system calls or APIs or removed to reduce overhead. The experimental evaluation has three parts. DiOS is first evaluated as a component of a program verification platform based on DiVM . In the second part, we consider its portability and modularity by combining it with the symbolic executor KLEE. Finally, we consider its use as a standalone user-mode kernel.</p>", "Keywords": "Software verification; Operating systems; POSIX; Reproducibility; C/C++", "DOI": "10.1007/s10270-020-00837-y", "PubYear": 2021, "Volume": "20", "Issue": "2", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Informatics, Masaryk University, Brno, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Informatics, Masaryk University, Brno, Czech Republic"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Informatics, Masaryk University, Brno, Czech Republic"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Informatics, Masaryk University, Brno, Czech Republic"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Informatics, Masaryk University, Brno, Czech Republic"}], "References": []}, {"ArticleId": 84729925, "Title": "Global connections and the structure of skills in local co-worker networks", "Abstract": "Abstract \n Social connections that reach distant places are advantageous for individuals, firms and cities, providing access to new skills and knowledge. However, systematic evidence on how firms build global knowledge access is still lacking. In this paper, we analyse how global work connections relate to differences in the skill composition of employees within companies and local industry clusters. We gather survey data from 10% of workers in a local industry in Sweden, and complement this with digital trace data to map co-worker networks and skill composition. This unique combination of data and features allows us to quantify global connections of employees and measure the degree of skill similarity and skill relatedness to co-workers. We find that workers with extensive local networks typically have skills related to those of others in the region and to those of their co-workers. Workers with more global ties typically bring in less related skills to the region. These results provide new insights into the composition of skills within knowledge-intensive firms by connecting the geography of network contacts to the diversity of skills accessible through them.", "Keywords": "Co-worker networks;Skills;Relatedness;Global connections;Survey;Online social network", "DOI": "10.1007/s41109-020-00325-8", "PubYear": 2020, "Volume": "5", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>ás<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Economic and Regional Studies, Institute of Economics, Budapest, Hungary;Laboratory for Networks, Technology and Innovation, Corvinus University of Budapest, Budapest, Hungary"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, Umeå University, Umeå, Sweden;Institute for Analytical Sociology, Linköping University, Norrköping, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Science, University of Zurich, Zurich, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, Umeå University, Umeå, Sweden"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Economic and Regional Studies, Institute of Economics, Budapest, Hungary;Laboratory for Networks, Technology and Innovation, Corvinus University of Budapest, Budapest, Hungary;Agglomeration and Social Networks Lendület Research Group, Hungarian Academy of Sciences, Budapest, Hungary"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, Umeå University, Umeå, Sweden;Center for Regional Science, Umeå University, Umeå, Sweden"}], "References": []}, {"ArticleId": 84730014, "Title": "Supply and demand matching model of P2P sharing accommodation platforms considering fairness", "Abstract": "<p>Due to the continuous expansion of sharing economy, the diversification of users and the heterogeneity of resources and needs on P2P platform are speeding up, which makes it difficult to match the supply and demand of P2P platform effectively. Therefore, how to achieve effective matching between service providers and customers in an increasingly complex market is a question worthy of study. In order to achieve more effective matching of heterogeneous resources and requirements, this paper focuses on P2P sharing accommodation platform, advances a theoretical framework of fair matching and builds a matching model which considering fairness. First, we analyze the transaction mode of P2P sharing accommodation platform, proposed the framework of fair matching based on preferences consistency and fairness of matching. Second, we build a matching model based on fair matching, to maximize the consistency of preference and minimize the difference between supply and demand, the fair matching framework deals with heterogeneous resources and needs by matching diversified preferences. Finally, the effectiveness and feasibility of the strategy are verified by example and sensitivity analysis. This strategy provides optimization ideas for the matching issue of P2P sharing accommodation.</p>", "Keywords": "P2P sharing accommodation platform; Supply and demand matching; Fair matching; Preferences", "DOI": "10.1007/s10660-020-09437-w", "PubYear": 2022, "Volume": "22", "Issue": "3", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management, Shanghai University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Shanghai University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Shanghai University, Shanghai, China"}], "References": [{"Title": "ULAMA: A Utilization-Aware Matching Approach for robust on-demand spatial service brokers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "1030", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "How do consumers in the sharing economy value sharing? Evidence from online reviews", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "", "Page": "113162", "JournalTitle": "Decision Support Systems"}, {"Title": "Stable two-sided satisfied matching for ridesharing system based on preference orders", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "2", "Page": "1063", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "On matching of intuitionistic fuzzy sets", "Authors": "<PERSON><PERSON><PERSON>; Grażyna Szkatuła", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "254", "JournalTitle": "Information Sciences"}, {"Title": "Two-sided matching decision-making model with hesitant fuzzy preference information for configuring cloud manufacturing tasks and resources", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "8", "Page": "2033", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 84730069, "Title": "Adaptive event-triggered dynamic output feedback H∞ control for networked T-S fuzzy systems", "Abstract": "This paper investigates the design problem of fuzzy dynamic output feedback H ∞ controller for nonlinear networked systems via mismatched membership functions and adaptive event-triggered (AET) mechanism. Firstly, an AET mechanism is introduced to save communication resources, which causes the controller and the original system's premise variables to be asynchronous. Then, considering the influence of AET and mismatched membership functions, a model of fuzzy control system is established. In addition, utilizing the Lyapunov-K<PERSON>skii(L-K) functional, sufficient conditions for the global exponential stability of the closed-loop system with H ∞ performance are derived. Besides, the controller parameters and event-triggered (ET) weight matrix are solved by a set of linear matrix inequalities (LMIs). Finally, an example is given to demonstrate the effectiveness of the proposed control method.", "Keywords": "Networked T-S fuzzy systems ; adaptive event-triggered mechanism ; dynamic output feedback H ∞ control", "DOI": "10.1080/21642583.2020.1836527", "PubYear": 2021, "Volume": "9", "Issue": "sup2", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Complexity Science, Qingdao University, Qingdao, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Complexity Science, Qingdao University, Qingdao, People's Republic of China"}], "References": []}, {"ArticleId": 84730075, "Title": "Channel and spatial attention based deep object co-segmentation", "Abstract": "Object co-segmentation is a challenging task, which aims to segment common objects in multiple images at the same time. Generally, common information of the same object needs to be found to solve this problem. For various scenarios, common objects in different images only have the same semantic information. In this paper, we propose a deep object co-segmentation method based on channel and spatial attention, which combines the attention mechanism with a deep neural network to enhance the common semantic information. Siamese encoder and decoder structure are used for this task. Firstly, the encoder network is employed to extract low-level and high-level features of image pairs. Secondly, we introduce an improved attention mechanism in the channel and spatial domain to enhance the multi-level semantic features of common objects. Then, the decoder module accepts the enhanced feature maps and generates the masks of both images. Finally, we evaluate our approach on the commonly used datasets for the co-segmentation task. And the experimental results show that our approach achieves competitive performance.", "Keywords": "Object co-segmentation ; Channel attention ; Spatial attention ; Deep learning", "DOI": "10.1016/j.knosys.2020.106550", "PubYear": 2021, "Volume": "211", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Educational Information Technology, Central China Normal University, Wuhan, China;Center for Vision, Speech and Signal Processing, University of Surrey, Guildford, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Educational Information Technology, Central China Normal University, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Visual Learning Lab, Heidelberg University, Heidelberg, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Educational Information Technology, Central China Normal University, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Educational Information Technology, Central China Normal University, Wuhan, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Center for Vision, Speech and Signal Processing, University of Surrey, Guildford, UK"}], "References": []}, {"ArticleId": 84730130, "Title": "Anisotropic angle distribution learning for head pose estimation and attention understanding in human-computer interaction", "Abstract": "Head pose estimation is an important way to understand human attention in the human-computer interaction. In this paper, we propose a novel anisotropic angle distribution learning (AADL) network for head pose estimation task. Firstly, two key findings are revealed as following: 1) Head pose image variations are different at the yaw and pitch directions with the same pose angle increasing on a fixed central pose; 2) With the fixed angle interval increasing, the image variations increase firstly and then decrease in yaw angle direction. Then, the maximum a posterior technology is employed to construct the head pose estimation network, which includes three parts, such as convolutional layer, covariance pooling layer and output layer. In the output layer, the labels are constructed as the anisotropic angle distributions on the basis of two key findings. And the anisotropic angle distributions are fitted by the 2D Gaussian-like distributions (groundtruth labels). Furthermore, the <PERSON><PERSON>back-Le<PERSON>r divergence is selected to measure the predication label and the groundtruth one. The features of head pose images are perceived at the AADL-based convolutional neural network in an end-to-end manner. Experimental results demonstrate that the developed AADL-based labels have several advantages, such as robustness for head pose image missing, insensitivity for the motion blur. Moreover, the proposed method has achieved good performance compared to several state-of-the-art methods on the Pointing’04 and CAS_PEAL_R1 databases.", "Keywords": "Head pose estimation ; Anisotropic angle distribution ; Convolutional neural network ; Regularization ; Learning behavior analysis ; Human-computer interaction", "DOI": "10.1016/j.neucom.2020.09.068", "PubYear": 2021, "Volume": "433", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan 430079, China;UCL Interaction Centre, University College London, London, United Kingdom;Department of Mechanical Engineering, City University of Hong Kong, Kowloon, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan 430079, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan 430079, China;Corresponding Author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, City University of Hong Kong, Kowloon, Hong Kong"}], "References": [{"Title": "Semantic-based padding in convolutional neural networks for improving the performance in natural language processing. A case of study in sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "315", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 84730144, "Title": "Underwater Bubble Detection and Counting By a Dynamic Changing Solid-Liquid Interfacial Process", "Abstract": "In this paper, a dynamic changing solid-liquid interfacial process was used for electrically detecting and counting underwater bubbles. The measurement system consists of a detection electrode, a reference electrode, an electric resistor connected in series with the two electrodes, a differential amplifier and a data acquisition system. Once a bubble touches the detection electrode submerged in a solution, the electrode-liquid interface is changed to the electrode-bubble interface. During this process, an electric pulse is generated and consequently be detected and counted by the measurement system. The generated electric signal is due to the dynamic change of the electric double layer (EDL) of the detection electrode. Experimental results show that the magnitude of the generated signals depends on bubble size, bubble rising height, exposed area of the detection electrode in solution and bubble-contacting position at the electrode. The magnitudes of the generated signals are larger in 3.5% NaCl solution than in pure water. Detection of air bubbles of 0.3 mm in diameter was achieved with a 3 mm × 3 mm detection electrode. The measurement system presented in this paper is simple and has a great potential on sensing and counting underwater bubbles, for applications such as underwater pipe leakage detection and bubble sensing in chemical engineering science.", "Keywords": "Solid-liquid interface ; Electric double layer ; Underwater bubble detection", "DOI": "10.1016/j.snb.2020.129083", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marine Engineering, Dalian Maritime University, Dalian, 116026, China"}, {"AuthorId": 2, "Name": "Wen<PERSON> Zhao", "Affiliation": "Department of Marine Engineering, Dalian Maritime University, Dalian, 116026, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marine Engineering, Dalian Maritime University, Dalian, 116026, China"}, {"AuthorId": 4, "Name": "Dongqing Li", "Affiliation": "Department of Mechanical and Mechatronics Engineering, University of Waterloo, Waterloo, ON, N2L 3G1, Canada;Corresponding author"}], "References": []}, {"ArticleId": 84730192, "Title": "An innovative bio-inspired flight controller for quad-rotor drones: Quad-rotor drone learning to fly using reinforcement learning", "Abstract": "Animals learn to master their capabilities by trial and error, and with out having any knowledge about their dynamics model and mathematical or physical rules. They use their maximum capabilities in an optimized way. This is the result of millions of years of evolution where the best of different possibilities are kept, and makes us rethink How does the nature perform things? , particularly when natural systems outperform our rigid systems. In this study, inspired by the nature, we developed an innovative algorithm by enhancing an existing reinforcement learning algorithm (proximal policy optimization (PPO)). Our algorithm is capable of learning to control a quad-rotor drone in order to fly. This new algorithm called Bio-inspired Flight Controller (BFC) does not use any conventional controller such as PID or MPC to control the quad-rotor drone. The goal of BFC is to completely replace the conventional controller with a controller that acts in a similar way to the animals where they learn to control their movements. It is capable of stabilizing a quad-copter in a desired point, and following way points. We implemented our algorithm in an AscTec Hummingbird quad-copter simulated in Gazebo, and tested it using different scenarios to fully measure its capabilities.", "Keywords": "Reinforcement learning ; Autonomous system ; Bio-inspired artificial intelligence ; Policy optimization ; Artificial neural network ; Bio-inspired controller ; Machine learning", "DOI": "10.1016/j.robot.2020.103671", "PubYear": 2021, "Volume": "135", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Smart Autonomous System Lab, Department of Mechanical Engineering, Kunsan National University, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Smart Autonomous System Lab, Department of Mechanical Engineering, Kunsan National University, Republic of Korea;Corresponding authors"}], "References": []}, {"ArticleId": 84730196, "Title": "Humidity Sensitivity Enhancement Effects of Metal Nanoparticles Loaded Fullerene", "Abstract": "In this paper, the response characteristics of capacitive humidity sensors based on fullerene (C<sub>60</sub>) loaded with metal nanoparticles (MNPs) were investigated. Experimental results demonstrated that the humidity sensitive properties of all the sensors are significantly improved after loading MNPs. This may be related to the hydrophilicity change of fullerene composite membranes induced by MNPs, which is confirmed by the test results of water contact angle and complex impedance spectrum. Moreover, the enhancement effect of humidity sensitivity is more obvious with the increase of MNPs content, but when the MNPs content exceeds a certain value, the enhancement effect of humidity sensitivity will decrease, which may be attributed to the agglomeration of MNPs. Meanwhile, for samples loaded with different MNPs (AuNPs, AgNPs, CuNPs), the improvement effects are quite different. Among the three samples studied, the C<sub>60</sub>/Ag sample exhibit the best response characteristics with a sensitivity of 5,812.8 pF/%RH, which is more than 200 times higher than that of the unloaded sample. The C<sub>60</sub>/Cu sample has no obvious response in low relative humidity (RH), but its sensitivity at high humidity is even higher than that of the C<sub>60</sub>/Ag sample, which shows the characteristics similar to a humidity sensitive switch. The C<sub>60</sub>/Au sample is one order of magnitude more sensitive than pure C<sub>60</sub> sample and exhibits the best stability among the three samples. The above results suggest that different MNPs can be selected to develop humidity sensors with different characteristics according to the application situations.", "Keywords": "fullerene ; metal nanoparticles ; humidity sensor ; high sensitivity", "DOI": "10.1016/j.snb.2020.129086", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, Sichuan, 610031, PR China"}, {"AuthorId": 2, "Name": "Xiangdong Chen", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, Sichuan, 610031, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, Sichuan, 610031, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, Sichuan, 610031, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, Sichuan, 610031, PR China"}, {"AuthorId": 6, "Name": "Xinglin Yu", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, Sichuan, 610031, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, Sichuan, 610031, PR China"}], "References": []}, {"ArticleId": 84730199, "Title": "Additive-improved colorimetric nitrite assay with ultrahigh sensitivity based on etching gold nanorods", "Abstract": "Gold nanomaterial (GNM)-based colorimetric sensors have gained particular attention in the analytical field because of their instrument-free, low-cost, and easy-to-use features. Herein, a universal strategy for improving the sensitivity of colorimetric assays, which is based on GNM etching, was proposed by using additives to shift the chemical equilibrium of the reactions. As a proof-of-concept demonstration, nitrite was detected with high sensitivity using gold nanorod (AuNR) by monitoring the shift of its longitudinal localized surface plasmonic resonance (Δλ) with the use of NH<sub>4</sub>Br and HCl. These two additives could change chemical equilibriums and promote the etching of more gold atoms at the AuNR ends. The assay is sensitive and 0.001 μM of nitrite could result in Δλ change by 3 nm. A linear relationship between Δλ and nitrite concentration was established (Δλ =22.8∗C<sub>nitrite</sub> ****) in the range of 0.016–8.0 μM with a coefficient of 0.999. The reaction time was also accurately controlled by adjusting solution pH with NH<sub>3</sub>⋅H<sub>2</sub>O to terminate the test. The sensitivity of this colorimetric assay toward nitrite was significantly higher than that of the conventional <PERSON><PERSON><PERSON> assay, while it was the highest among the reported GNM-based approaches. This method was successfully applied in determining the nitrite content in food samples, including waters, spinach, and rose petals, with less rigorous sample processing. The proposed strategy of using additives to alter chemical equilibriums to improve sensitivity could be extended to other GNM etching sensors.", "Keywords": "Nitrite detection ; High sensitivity ; Gold nanorod ; Etching ; Chemical equilibrium", "DOI": "10.1016/j.snb.2020.129073", "PubYear": 2021, "Volume": "328", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Deep Processing of Rice and Byproducts, College of Food Science and Engineering, Central South University of Forestry & Technology, Changsha, Hunan 410004, China;Hunan Key Laboratory of Processed Food for Special Medical Purpose, Central South University of Forestry and Technology, Changsha, 410004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Deep Processing of Rice and Byproducts, College of Food Science and Engineering, Central South University of Forestry & Technology, Changsha, Hunan 410004, China;Hunan Key Laboratory of Processed Food for Special Medical Purpose, Central South University of Forestry and Technology, Changsha, 410004, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, Georgia State University, Atlanta, GA, 30303, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Deep Processing of Rice and Byproducts, College of Food Science and Engineering, Central South University of Forestry & Technology, Changsha, Hunan 410004, China;Hunan Key Laboratory of Processed Food for Special Medical Purpose, Central South University of Forestry and Technology, Changsha, 410004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> An", "Affiliation": "National Engineering Laboratory for Deep Processing of Rice and Byproducts, College of Food Science and Engineering, Central South University of Forestry & Technology, Changsha, Hunan 410004, China;Hunan Key Laboratory of Processed Food for Special Medical Purpose, Central South University of Forestry and Technology, Changsha, 410004, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Deep Processing of Rice and Byproducts, College of Food Science and Engineering, Central South University of Forestry & Technology, Changsha, Hunan 410004, China;Hunan Key Laboratory of Processed Food for Special Medical Purpose, Central South University of Forestry and Technology, Changsha, 410004, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Xu", "Affiliation": "National Engineering Laboratory for Deep Processing of Rice and Byproducts, College of Food Science and Engineering, Central South University of Forestry & Technology, Changsha, Hunan 410004, China;Hunan Key Laboratory of Processed Food for Special Medical Purpose, Central South University of Forestry and Technology, Changsha, 410004, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "National Engineering Laboratory for Deep Processing of Rice and Byproducts, College of Food Science and Engineering, Central South University of Forestry & Technology, Changsha, Hunan 410004, China;Hunan Key Laboratory of Processed Food for Special Medical Purpose, Central South University of Forestry and Technology, Changsha, 410004, China;Corresponding author"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemical Engineering, Beijing Institute of Petrochemical Technology, Beijing, 102617, China;Corresponding author"}], "References": []}, {"ArticleId": 84730202, "Title": "Gas sensor towards n-butanol at low temperature detection: Hierarchical flower-like Ni-doped Co3O4 based on solvent-dependent synthesis", "Abstract": "In this work, hierarchical flower-like Ni-doped Co<sub>3</sub>O<sub>4</sub> was synthesized via a facile one-step coprecipitation method. In the synthesis process, a series of solvent-dependent experiments were carried out to investigate the effect of ethanol/water ratio (R-E/W) on samples. With the increasing ethanol/water ratio, the doping concentration of Ni<sup>2+</sup> increased and the microstructure evolved from micro-leaves to micro-flowers. Additionally, gas sensors based on prepared materials were fabricated to evaluate their gas sensing properties. The comparative analysis illustrated that the sensor based on 5.3 mol% Ni-doped Co<sub>3</sub>O<sub>4</sub> microflowers (R-E/W = 3/30) presented the highest response (8.34) to 100 ppm n-butanol at low optimum temperature (165 °C), with a response/recovery time of 59/63 s, and it also exhibited excellent anti-humidity properties and long-term stability. The unique hierarchical flower-like microstructure and the optimized parameters (catalytic sites, carrier concentration, ratio of Co<sup>2+</sup>, oxygen component) caused by the doping of Ni were responsible for the improved gas sensing performance. Therefore, this work presented a simple solvent-dependent route to controllably synthesize Ni-doped Co<sub>3</sub>O<sub>4</sub> sensing material, and the excellent gas sensing properties of the sensor based on 5.3 mol% Ni-doped Co<sub>3</sub>O<sub>4</sub> microflowers revealed a great application prospect in detecting n-butanol.", "Keywords": "Solvent-dependent ; Ni-doped Co<sub>3</sub>O<sub>4</sub> ; Gas sensor ; n-Butanol ; Low optimum temperature ; Anti-humidity", "DOI": "10.1016/j.snb.2020.129028", "PubYear": 2021, "Volume": "328", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 7, "Name": "Li Lv", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aerospace Science and Technology, Xidian University, 266 Xifeng Road, Xi'an, 710126, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of Synthetic Resin and Special Fiber, Ministry of Education, Changchun University of Technology, Changchun, 130012, China"}], "References": [{"Title": "Two-dimensional Cd-doped porous Co3O4 nanosheets for enhanced room-temperature NO2 sensing performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "127393", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 84730203, "Title": "PSS-PANI/PVDF composite based flexible NH3 sensors with sub-ppm detection at room temperature", "Abstract": "Improved ammonia (NH<sub>3</sub>) sensor is proposed based on polyaniline (PANI) with poly(styrene sulfonic acid) (PSS) as an additive, which was produced by in-suit polymerization of aniline on flexible porous polyvinylidene-fluoride (PVDF) membranes and then treated with PSS aqueous solution. The whole film sensor fabrication process was low-cost and convenient in operation, suitable for large-scale commercial production. The results demonstrated that the appropriate addition of PSS could significantly improve the sensor response to NH<sub>3</sub>. The PSS-PANI/PVDF film sensor response was improved to approximately 70%, which was 2.8 times higher than that of the pure PANI/PVDF film with 25% response towards 1 ppm NH<sub>3</sub> at room temperature. Furthermore, it still showed an excellent response of 9.4% for 0.1 ppm NH<sub>3</sub>. The fabricated film sensor showed remarkable long-term stability with response decreases less than 5% after 30 days, and excellent flexibility under 10,000 repeated bending times with a response value degradation of only 15.1% towards 1 ppm NH<sub>3</sub>. The flexible PSS-PANI/PVDF film offers potential applications in smart wearable devices for detecting sub-ppm NH<sub>3</sub> under ambient conditions.", "Keywords": "Flexible sensor ; Ammonia sensor ; Polyaniline ; Poly(styrene sulfonic acid) ; Polyvinylidene fluoride", "DOI": "10.1016/j.snb.2020.129085", "PubYear": 2021, "Volume": "328", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Lv", "Affiliation": "Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Shen", "Affiliation": "Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Healthcare Equipment, Zhejiang Pharmaceutical College, Ningbo, Zhejiang 315000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Ningbo University, Ningbo, Zhejiang 315211, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Gastroenterology, Ningbo First Hospital, Ningbo, Zhejiang 315010, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China;Corresponding authors"}], "References": [{"Title": "An ammonia sensor composed of polypyrrole synthesized on reduced graphene oxide by electropolymerization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "127423", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 84730213, "Title": "Bedside Detection of Carbapenemase-Producing Pathogens with Plasmonic Nanosensors", "Abstract": "Providing the right antibiotic treatment as soon as possible is crucial to improve sepsis outcomes. In this context, infections by bacteria resistant to β-lactams can be deadly. Current approaches for identifying these pathogens are longsome and require complex infrastructure. Here we introduce a diagnostic kit for detecting carbapenemase-producing pathogens at the bedside. It generates colored tests that are easily distinguished by eye or by analyzing a smartphone photograph. The color is generated by gold nanoparticles, which have been designed to detect β-lactamase activity robustly and with ultra-high sensitivity. Neither microbial culture nor specialized equipment are required. The kit can detect infections by carbapenemase-producing bacteria directly from urine samples within 2.5 h, even when these contain an excess of interfering bacteria. It can also detect them in sputum samples within 3 h. The rapid assay time coupled with the ability to detect hazardous infections enables the personalization of antimicrobial treatments much faster than a full antibiotic susceptibility test, which often takes several days to be accomplished. This kit can also be implemented in resource-constrained scenarios where other options for antimicrobial susceptibility testing may not be available.", "Keywords": "Antimicrobial resistance ; beta-lactamase ; antibiotic ; sepsis ; colorimetric ; nanoparticle", "DOI": "10.1016/j.snb.2020.129059", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Giulia Santopolo", "Affiliation": "Multidisciplinary Sepsis Group, Hospital Universitario Son Espases, Health Research Institute of the Balearic Islands (IdISBa), Palma de Mallorca, Spain;Department of Chemistry, University of the Balearic Islands, Palma de Mallorca, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>-Mo<PERSON>", "Affiliation": "Servicio de Microbiología, Hospital Son Espases, Health Research Institute of the Balearic Islands (IdISBa), Palma de Mallorca, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Multidisciplinary Sepsis Group, Hospital Universitario Son Espases, Health Research Institute of the Balearic Islands (IdISBa), Palma de Mallorca, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Multidisciplinary Sepsis Group, Hospital Universitario Son Espases, Health Research Institute of the Balearic Islands (IdISBa), Palma de Mallorca, Spain;Multidisciplinary Sepsis Unit, Hospital Universitario Son Llàtzer, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Servic<PERSON> de Microbiología, Hospital Son Espases, Health Research Institute of the Balearic Islands (IdISBa), Palma de Mallorca, Spain;Corresponding authors"}, {"AuthorId": 6, "Name": "Roberto de la Rica", "Affiliation": "Multidisciplinary Sepsis Group, Hospital Universitario Son Espases, Health Research Institute of the Balearic Islands (IdISBa), Palma de Mallorca, Spain;Department of Chemistry, University of the Balearic Islands, Palma de Mallorca, Spain;Corresponding authors"}], "References": [{"Title": "New frontiers against antibiotic resistance: A Raman-based approach for rapid detection of bacterial susceptibility and biocide-induced antibiotic cross-tolerance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "309", "Issue": "", "Page": "127774", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Beta-lactam antibiotics induced bacteriolysis on LSPR sensors for assessment of antimicrobial resistance and quantification of antibiotics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "311", "Issue": "", "Page": "127945", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 84730215, "Title": "Dynamic Compensation of Piezoresistive Pressure Sensor Based on Sparse Domain", "Abstract": "<p>In the process of transient test, due to the insufficient bandwidth of the pressure sensor, the test data is inaccurate. Firstly, based on the projection of the shock tube test signal in the sparse domain, the feature expression of the signal sample is obtained. Secondly, the problem of insufficient bandwidth is solved by inverse modeling of sensor dynamic compensation system based on swarm intelligence algorithm. In this paper, the method is used to compensate the shock tube test signals of the 85XX series pressure sensors made by the Endevco company of the United States, the working bandwidth of the sensor is widened obviously, the rise time of the pressure signal can be compensated to 12.5 μs, and the overshoot can be reduced to 8.96%. The repeatability of dynamic compensation is verified for the actual gun muzzle shock wave test data, the results show that the dynamic compensation can effectively recover the important indexes such as overpressure peak value and positive pressure action time, and the original shock wave signal is recovered from the high resonance data.</p>", "Keywords": "", "DOI": "10.1155/2020/8890028", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Changchun University of Science and Technology, Changchun 130022, China"}, {"AuthorId": 2, "Name": "Tailin Han", "Affiliation": "Changchun University of Science and Technology, Changchun 130022, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Changchun University of Science and Technology, Changchun 130022, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Changchun University of Science and Technology, Changchun 130022, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Changchun University of Science and Technology, Changchun 130022, China"}], "References": []}, {"ArticleId": 84730224, "Title": "Facile preparation of N-S co-doped graphene quantum dots (GQDs) from graphite waste for efficient humidity sensing", "Abstract": "In this work, graphene quantum dots (GQDs) were prepared from Graphitic waste. The resulting GQDs were evaluated for the potential application for resistive humidity sensors. The resistive humidity sensors were fabricated on the pre-patterned interdigital ITO electrodes using the three different concentrations (2.5, 5.0, and 10 mg) of GQDs in DMF. The GQDs films were deposited using the spin coating technique. The GQDs (10 mg/ml) based impedance sensors showed good sensitivity and lowered hysteresis as compared to the other ratios (2.5 and 5 mg) of the GQDs. The maximum calculated hysteresis of the GQDs (10 mg) based humidity sensor is around 2.2 % at 30%RH, and the minimum calculated hysteresis of the GQDs (10 mg/ml) based humidity sensor is approximately 0.79 % at 60 %RH. The response and recovery time found to be 15 s and 55 s, respectively. The interesting humidity-dependent resistive properties of these prepared GQDs make them promising for potential application in humidity sensing.", "Keywords": "GQDs ; Humidity sensor ; Response and recovery times ; Hysteresis", "DOI": "10.1016/j.snb.2020.129058", "PubYear": 2021, "Volume": "328", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Materials (CAM), Qatar University, 2713 Doha, Qatar;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Qatar University, 2713 Doha, Qatar"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry and Earth Sciences, Qatar University, 2713 Doha, Qatar"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Univ Paris Est, ICMPE (UMR7182), CNRS, UPEC, F-94320 Thiais, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Materials (CAM), Qatar University, 2713 Doha, Qatar;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Qatar University, 2713 Doha, Qatar"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Center for Advanced Materials (CAM), Qatar University, 2713 Doha, Qatar"}], "References": [{"Title": "Design and analysis of ultrafast and high-sensitivity microwave transduction humidity sensor based on belt-shaped MoO3 nanomaterial", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127138", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Design strategy for ultrafast-response humidity sensors based on gel polymer electrolytes and application for detecting respiration", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127270", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Novel application of attapulgite on high performance and low-cost humidity sensors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "127534", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "High-sensitivity resistive humidity sensor based on graphitic carbon nitride nanosheets and its application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "315", "Issue": "", "Page": "128058", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 84730296, "Title": "Key Technologies of Threat Intelligence for Satellite Communication Network", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2020.95046", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "", "Affiliation": ""}], "References": []}, {"ArticleId": 84730310, "Title": "Road visualization for smart city: Solution review with road quality qualification", "Abstract": "Pavement management consists of a series of maintenance and rehabilitation activities over the life of a road. Maintenance involves different methods and technologies that extend life by slowing the rate of deterioration. Currently, the structural conditions of the road network are assessed in the traditional way, that is, it is done manually by technicians using specialized tools. Thus, to measure deflection, for example, we use a deflectometer or a Benkelman beam. If you want to assess the deterioration of the road surface or count the vehicles passing on a road, then technicians must make a visual analysis of the road. In the same vein, most of the available research evaluates the results of data extraction and does so mainly visually. So, when a frame of reference is available, an adjustment is made to modify the database. This is where IoT (Internet of Things) comes in place by creating a simple automation process to standardize the data and transform them in information to create a database and measure the quantity and quality of the deformation with a Neural Network. The data set has been categorized manually into different types of deterioration, allowing it to be measured quantitatively and qualitatively. The sample comes from images collected by a GoPro camera, on a 60-degree angle, at 15 FPS. 4 different types of deformation were present in the data set; our model predicted with a precision ranging between 50% and 90% the different types of deformation. The validation of the results of the model shows 71% of true positive. This process automation provides standardized information for a road qualification and quantification system. Introduction The main objective of the research is the design, deployment, learning and validation of an autonomous, inexpensive tool that can qualify and quantify road degradation. This tool would be used to provide a diagnosis of the causes of road damages. The way to acquire information on a road network is usually by doing a visual appreciation of said network, and then evaluate findings using an existing frame of reference [1], [2]. Thus, the measurements do not make it possible to adequately quantify the performance of the roadway and, moreover, they remain insufficient and not rigorous from the point of view of localized information. Indeed, all local information providing information on the precise quality of the asset or the accuracy of their geographic positions is lacking. In the literature, the application of these measures is done in a global manner and consequently provides only general information on the quality of the roads. One can think, among other things, of the different types and qualities of road existing in a road network. It is relevant to obtain local information to effectively assess the overall quality of the road network or simply by segment. It is also relevant to mention that economically speaking, neglecting repairs and preservation operation of a pavement represents billions of dollars in subsequent spending. The benefits that come out of preventive maintenance are useful for both users (in terms of vehicle operating costs, in particular) and governments since the costs of interventions on a roadway, as it deteriorates, are exponentials. We can identify three types of intervention on a roadway: • Maintenance; • Rehabilitation; • Reconstruction. Thus, according to the American Association of State Highway and Transportation Officials [3], every dollar spent today to maintain a road in good condition would avoid $ 6 to $ 14 of subsequent investment to rehabilitate and reconstruct the same road, once deteriorated. The deterioration of the road network is inevitable (traffic, environment, wear). It is therefore critical to monitor it, using reliable indicators (IRI, PCI, degradation reports, etc.) which would aim to identify the areas and places where maintenance is required. The purpose of the assessment is to protect costly investments in the construction and maintenance of the road network and, therefore, to maintain access to the developed area. Currently, information is collected regularly, but in a traditional way; this information makes it possible to follow, in the best of cases, the evolution of pavement performance. This type of indicator is a road management mechanism and makes it possible to make the most of the road assets to carry out the interventions [4]. Figures Examples of image classification for model normalization. Pothole Detection with GoPro. Machine Learning results vs. Visual analysis – GoPro. Level of precision by type of distress - GoPro. Section snippets Literature review and motivation Some road authorities still use detection methods requiring human assessment such as “walk, find and watch” while others use mechanical devices to measure certain areas. So-called “walking” surveys can include 100% of the area to be surveyed, measured, recorded and, sometimes, mapped. The National Cooperative Highway Research Program (NCHRP) in the United States has synthesized nearly 334 surface damage capture methods using both a person and automated methods [5]. The transition from manual to Experimental framework The hypothesis is to have a tool to achieve automatic detection methods, making it faster and easier to apply the same process to multiple images. Automatic interpretation includes transformation recognition techniques and image configurations to identify faults and damage [16]. Crack detection using image processing techniques is difficult because not only does it represent only cracks, but also because the texture of the road surface can conceal irregularities [17]. Automatic detection of Discussion and validation In order to validate the machine learning results, a human visual analysis was performed on the available images. Results of this visual analysis were then compared with those obtained by machine learning. Thus, for example, for alligator tile cracks, a total of 47 images showed this deformation; the model detected 53, therefore 9 false positives, and was unable to recognize the deformation on three occasions (Fig. 3). We tested an environment where 2 of the 6 classes were not available to Limits The accuracy of the models of the GoPro system have certain limits. The limited number of distortion images means that supervised machine learning produces less precise results for image classification. In order to have a more efficient confusion matrix, it is therefore suggested to have at least 2000 images available per type of deformation [19]. The constant use of the video/Gopro system will gradually increase the distortion image classification database to obtain an increasingly intelligent Conclusion The new technology achieves results much faster at a low cost compared to so-called traditional technologies and techniques (Aerial Imagery, Drone, Vehicle, Visual method). In addition, the implementation of a system integrated into the road is much simpler than the so-called existing solutions. The value of the information acquired far exceeds the costs of data acquisition. With the results of the neural network, one can easily observe where are the functional deformations on a road with Declaration of Competing Interest None. Eric Leduc received his I.T. engineering M.S. in cloud computing from ETS in Montreal. He is currently pursuing a Ph.D. degree in road engineering with the ETS. His research areas are machine learning, road automation and IoT. References (21) J. Gu et al. Recent advances in convolutional neural networks Pattern Recognit. (2018) T. Saarenketo et al. Road evaluation with ground penetrating radar J. Appl. Geophys. (2000) C. Zhang An UAV-based photogrammetric mapping system for road condition assessment AASHTO and The Road Information Project, “Rough roads ahead: fix them now or pay for it later,” 2009 [Online].... D. O'Brien Computer assisted feature extraction (InterEx) W. Uddin Remote sensing laser and imagery data for inventory and condition assessment of road and airport infrastructure and GIS visualization Int. J. Roads Airports (2011) J. Miller et al. Distress Identification Manual for the Long-Term Pavement Performance Program (2003) Z. Zhang Turning mobile laser scanning points into 2D/3D on-road object models: current status R. Smith et al. The Impact of Semiautomated Pavement Distribution Collection Methods on Pavement Management Network-Level Analysis Using the MTC Street Saver (2007) A. Mei Bitumen removal determination on asphalt pavement using digital imaging processing and spectral analysis Open J. Appl. Sci. (2014) There are more references available in the full text version of this article. Cited by (0) Recommended articles (6) Research article Performance evaluation metrics for cloud, fog and edge computing: A review, taxonomy, benchmarks and standards for future research Internet of Things, Volume 12, 2020, Article 100273 Show abstract Optimization is an inseparable part of Cloud computing, particularly with the emergence of Fog and Edge paradigms. Not only these emerging paradigms demand reevaluating cloud-native optimizations and exploring Fog and Edge-based solutions, but also the objectives require significant shift from considering only latency to energy, security, reliability and cost. Hence, it is apparent that optimization objectives have become diverse and lately Internet of Things (IoT)-specific born objectives must come into play. This is critical as incorrect selection of metrics can mislead the developer about the real performance. For instance, a latency-aware auto-scaler must be evaluated through latency-related metrics as response time or tail latency; otherwise the resource manager is not carefully evaluated even if it can reduce the cost. Given such challenges, researchers and developers are struggling to explore and utilize the right metrics to evaluate the performance of optimization techniques such as task scheduling, resource provisioning, resource allocation, resource scheduling and resource execution. This is challenging due to (1) novel and multi-layered computing paradigm, e.g., Cloud, Fog and Edge, (2) IoT applications with different requirements, e.g., latency or privacy, and (3) not having a benchmark and standard for the evaluation metrics. In this paper, by exploring the literature, (1) we present a taxonomy of the various real-world metrics to evaluate the performance of cloud, fog, and edge computing; (2) we survey the literature to recognize common metrics and their applications; and (3) outline open issues for future research. This comprehensive benchmark study can significantly assist developers and researchers to evaluate performance under realistic metrics and standards to ensure their objectives will be achieved in the production environments. Research article DTCMS: Dynamic traffic congestion management in Social Internet of Vehicles (SIoV) Internet of Things, 2020, Article 100311 Show abstract With the augmentation of traffic exponentially, we observe that traffic congestion does not guarantee road safety or enhance the driving experience. In the recent past, Social Internet of Vehicles (SIoV), a social network paradigm permits social relationships among every vehicle in the network or with any road infrastructure to render a radically useful environment. SIoV is beneficial for the drivers, in improving road safety, avoiding mishaps, and providing a friendly-driving experience. In this paper, we propose a traffic scheduling algorithm to gain the maximum throughput for the flow of vehicles at a road intersection with the formation of social relationships among the vehicles and with the Road Side Units (RSUs). The algorithm estimates the flow rate of vehicles for lanes at the intersections exploiting the volume of traffic moving through the given road. A condition matrix is designed for the consistent movement of traffic considering different routes on the road segments. Social relationships are devised on various aspects of travel needs for a safe, agile, and better driving experience. Simulation results illustrate the efficacy of the proposed scheme with high traffic throughput, service rate and reduce the total travelling time, delay time, and average waiting time in comparison with Dynamic Throughput Maximization Framework and Adaptive Traffic Control Algorithm. Research article IoT based real time energy monitoring system using Raspberry Pi Internet of Things, Volume 12, 2020, Article 100292 Show abstract An IoT based real time energy monitoring system for controlling and monitoring of a switchgear industry is discussed in the present work. Raspberry Pi has been selected for this purpose due to low cost and a highly reliable technology for monitoring the energy consumption of the industry. The Raspberry Pi is used with Node.js programming language to collect the data (various electrical parameters) from the existing energy meters of the industry and to store it locally, for accessing it through Laptop or mobile phone using Grafana. The monitoring system is found be useful to the industry to understand the day-to-day energy pattern, which is essential to facilitate energy conservation measures for minimizing energy consumption. Research article Internet of Things research in supply chain management and logistics: A bibliometric analysis Internet of Things, Volume 12, 2020, Article 100318 Show abstract This study reviews Internet of Things (IoT) research in supply chain management (SCM) and logistics. A thorough review and bibliometric analysis were conducted to analytically and objectively unearth the knowledge development in IoT research within the context of SCM and logistics. The analysis started with the selection of 807 journal articles published over a two-decade period. Then, the articles were analyzed according to bibliometric parameters such as year of publication, sources, authors, and institutions. A keyword co-occurrence network was used to cluster the pertinent literature. Results of the review and bibliometric analysis reveal that IoT research has attracted significant attention from the SCM and logistics community. Three leading journals published widely on IoT and the fifteen most productive authors are identified. Based on the keyword co-occurrence clustering, the IoT literature in SCM and logistics is focalized on RFID technology, Industry 4.0 technologies, reverse logistics, and additionally covers various industries, such as the food, retailing, construction, and the pharmaceutical sector. The study provides researchers with a better understanding of IoT research in SCM and logistics and existing knowledge gaps for further research. Practitioners may benefit from the review to keep abreast of the current discussions and applications of IoT in diverse industrial sectors. To the best of the authors’ knowledge, the current review is one of the few attempts to investigate IoT research in SCM and logistics using a comprehensive set of articles published during the past two decades. Research article OKIoT: Trade off analysis of smart speaker architecture on open knowledge IoT project Internet of Things, Volume 12, 2020, Article 100310 Show abstract Successful deployment of smart environments based on Internet of Things (IoT) technologies face the challenges of constrained devices. Processing, bandwidth, and memory limitations must be considered when designing IoT systems to meet non-functional requirements. Software Engineering methods such as trade-off analysis could enhance the quality of IoT architectures at the design phase and bring benefits to end-users. These methods were applied to IoT projects specification during a short-term course (2019). Smart home capstone projects (2017, 2019) built with the proposed rationale are available on a public repository. Basic smart speaker services API supported qualitative and quantitative analysis of Brazilian Portuguese speech audios, smart speaker architecture Petri Net and queue models simulations on PIPE, and JMT tools enabled architecture trade-off analysis. Security vs. performance and accuracy vs. response time results provided insights for an optimized smart speaker open architecture. Research article How does enterprise IoT traffic evolve? Real-world evidence from a Finnish operator Internet of Things, Volume 12, 2020, Article 100294 Show abstract The adoption of Internet of Things (IoT) technologies in businesses is increasing and thus enterprise IoT (EIoT) is seemingly shifting from hype to reality. However, the actual use of EIoT over significant timescales has not been empirically analyzed. In other words, the reality remains unexplored. Furthermore, despite the variety of EIoT verticals, the use of IoT across vertical industries has not been compared. This paper uses a two-year EIoT dataset from a major Finnish mobile network operator to investigate device use across industries, cellular traffic patterns, and mobility patterns. We present a variety of novel findings: EIoT traffic volume per device has increased three-fold over the last two years, the share of LTE-enabled devices has remained low at around 2% and that 30% of EIoT devices are still 2G only, and there are order of magnitude differences between different industries’ EIoT traffic and mobility. We also show that daily traffic can be clustered into only three patterns, differing mainly in the presence and timing of a peak hour. Beyond these descriptive results, modeling and forecasting is conducted for both traffic and mobility. We forecast the total daily EIoT traffic through a temporal regression model and achieve an error of about 15% over medium-term (30 to 180 day) horizons. We also model device mobility through a Markov mixture model and quantify the upper bound of predictability for device mobility. Eric Leduc received his I.T. engineering M.S. in cloud computing from ETS in Montreal. He is currently pursuing a Ph.D. degree in road engineering with the ETS. His research areas are machine learning, road automation and IoT. Gabriel J. Assaf has been a professor of road engineering at ETS in Montreal since 1992. After obtaining his master's in applied science from Polytechnique in Montreal and his Doctorate degree from the University of Waterloo, he has advised over 100 road agencies, engineering firms, contractors and PPP consortiums on road condition, causes of deterioration, economics and maintenance and rehabilitation treatments of road infrastructure in over 20 countries. His expertise lies in pavements, roadways, road diagnosis, road conception and design, road rehabilitation. His researches pertain to infrastructures and built environments, land transportation and information and communications technologies. View full text Crown Copyright © 2020 Published by Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2020 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.iot.2020.100305", "PubYear": 2020, "Volume": "12", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ecole de technologie superieure, Univ of Quebec, Montreal, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ecole de technologie superieure, Univ of Quebec, Montreal, Canada"}], "References": []}, {"ArticleId": 84730362, "Title": "GPUs-RRTMG_LW: high-efficient and scalable computing for a longwave radiative transfer model on multiple GPUs", "Abstract": "<p>Atmospheric radiation physical process plays an important role in climate simulations. As a radiative transfer scheme, the rapid radiative transfer model for general circulation models (RRTMG) is widely used in weather forecasting and climate simulation systems. However, its expensive computational overhead poses a severe challenge to system performance. Therefore, improving the radiative transfer model’s computational performance has significant scientific research and practical value. Numerous radiative transfer models have benefited from a widely used and powerful GPU. Nevertheless, few of them have exploited CPU/GPU cluster resources within heterogeneous high-performance computing platforms. In this paper, we endeavor to demonstrate an approach that runs a large-scale, computationally intensive, longwave radiative transfer model on a GPU cluster. First, a CUDA-based acceleration algorithm of the RRTMG longwave radiation scheme (RRTMG_LW) on multiple GPUs is proposed. Then, a heterogeneous, hybrid programming paradigm (MPI+CUDA) is presented and utilized with the RRTMG_LW on a GPU cluster. After implementing the algorithm in CUDA Fortran, a multi-GPU version of the RRTMG_LW, namely GPUs-RRTMG_LW, was developed. The experimental results demonstrate that the multi-GPU acceleration algorithm is valid, scalable, and highly efficient when compared to a single GPU or CPU. Running the GPUs-RRTMG_LW on a K20 cluster achieved a (77.78 imes) speedup when compared to a single Intel Xeon E5-2680 CPU core.</p>", "Keywords": "High-performance computing; Graphics processing unit; Compute Unified Device Architecture; Radiative transfer", "DOI": "10.1007/s11227-020-03451-3", "PubYear": 2021, "Volume": "77", "Issue": "5", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, China University of Geosciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, China University of Geosciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, China University of Geosciences, Beijing, China"}, {"AuthorId": 4, "Name": "Jinrong <PERSON>", "Affiliation": "Computer Network Information Center, Chinese Academy of Sciences, Beijing, China"}], "References": []}, {"ArticleId": 84730439, "Title": "FLAGS: A methodology for adaptive anomaly detection and root cause analysis on sensor data streams by fusing expert knowledge with machine learning", "Abstract": "Anomalies and faults can be detected, and their causes verified, using both data-driven and knowledge-driven techniques. Data-driven techniques can adapt their internal functioning based on the raw input data but fail to explain the manifestation of any detection. Knowledge-driven techniques inherently deliver the cause of the faults that were detected but require too much human effort to set up. In this paper, we introduce FLAGS, the Fused-AI interpretabLe Anomaly Generation System, and combine both techniques in one methodology to overcome their limitations and optimize them based on limited user feedback. Semantic knowledge is incorporated in a machine learning technique to enhance expressivity. At the same time, feedback about the faults and anomalies that occurred is provided as input to increase adaptiveness using semantic rule mining methods. This new methodology is evaluated on a predictive maintenance case for trains. We show that our method reduces their downtime and provides more insight into frequently occurring problems.", "Keywords": "Anomaly detection ; Root cause analysis ; Machine learning ; Semantic web ; Internet of Things ; Fused AI ; User feedback", "DOI": "10.1016/j.future.2020.10.015", "PubYear": 2021, "Volume": "116", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 <PERSON>wi<PERSON>, Belgium;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Televic Rail NV, <PERSON> 1, 8870 Izegem, Belgium"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Televic Rail NV, <PERSON> 1, 8870 Izegem, Belgium"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IDLab, Ghent University-imec, Technologiepark 126, 9052 Zwijnaarde, Belgium"}], "References": [{"Title": "A generalized matrix profile framework with support for contextual series analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103487", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Input selection methods for data-driven Soft sensors design: Application to an industrial process", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "537", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 84730457, "Title": "Single image dehazing algorithm based on pyramid mutil-scale transposed convolutional network", "Abstract": "In this paper, a novel single image dehazing method based on pyramid multi-scale transposed convolutional network (MST-Net) is proposed. Conventional haze removal algorithms based on the atmospheric scattering model may lead to some problems such as incomplete dehazing and colour distortion due to the inaccurate parameter approximations or indirect image optimization and reconstruction. Therefore, we design a real end-to-end image dehazing network to directly learn the mapping relationship between hazy images and the corresponding clear images. In this network, the cascaded feature extraction blocks extract the diversified feature information of the input images by multi-channel concatenation structure, which can effectively fuse the local features of the first convolution layer into the semantic features of subsequent layers in the block. In order to reconstruct high-quality dehazed images relieving the colour distortion, we design a multi-scale transposed convolution block to gradually expand the resolution of the obtained feature maps, and introduce skip connections from the feature extraction module to supplement the detailed information of the feature map pyramid. Extensive experimental results demonstrate that the proposed method in this paper can remove the haze completely and achieve superior performance in subjective and objective evaluation over the other state-of-the-art methods.", "Keywords": "Image dehazing ; convolutional neural network ; multi-scale transposed convolution ; feature extraction ; image reconstruction", "DOI": "10.1080/21642583.2020.1833780", "PubYear": 2021, "Volume": "9", "Issue": "sup1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Henan Polytechnic University, Henan Jiaozuo, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Henan Polytechnic University, Henan Jiaozuo, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Henan Polytechnic University, Henan Jiaozuo, People’s Republic of China"}], "References": []}, {"ArticleId": 84730522, "Title": "Platform entry and homing as competitive strategies under cross-sided network effects", "Abstract": "In a competitive environment, determining when to enter the market and what homing policies should be adopted are crucial strategic decisions for platform-based businesses. In this study, we investigate the interactions between two platforms competing in two-sided markets with cross-sided network effects and determine their prices under different entry and homing strategies. We derive the equilibrium demands of two-sided markets for platforms and the equilibrium prices for two competing platforms. We further conduct a comparative analysis among scenarios with different combinations of homing and entry strategies. We find that early-entry platforms will preserve high margins in large early markets, where fierce price competition occurs once their rivals enter these markets. However, early entry is not always beneficial for platforms because the market-cannibalization effect emerges in the subsequent period. Furthermore, this effect will be amplified by the platforms' homing and entry strategies. In addition, cross-sided network effects lead buyers, sellers, and platforms to prefer a multi-homing policy, which is beneficial for society as a whole. Managerial insights are also provided for platform-based businesses.", "Keywords": "Platform competition ; Market entry ; Platform homing ; Two-sided markets ; Cross-sided network effects", "DOI": "10.1016/j.dss.2020.113428", "PubYear": 2021, "Volume": "140", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Information Management , National Cheng Kung University , Tainan 701 , Taiwan, ROC;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Information Management , National Cheng Kung University , Tainan 701 , Taiwan, ROC"}], "References": []}, {"ArticleId": 84730537, "Title": "Software engineering practices for scientific software development: A systematic mapping study", "Abstract": "Background: The development of scientific software applications is far from trivial, due to the constant increase in the necessary complexity of these applications, their increasing size, and their need for intensive maintenance and reuse. Aim: To this end, developers of scientific software (who usually lack a formal computer science background) need to use appropriate software engineering (SE) practices. This paper describes the results of a systematic mapping study on the use of SE for scientific application development and their impact on software quality. Method: To achieve this goal we have performed a systematic mapping study on 359 papers. We first describe a catalog of SE practices used in scientific software development. Then, we discuss the quality attributes of interest that drive the application of these practices, as well as tentative side-effects of applying the practices on qualities. Results: The main findings indicate that scientific software developers are focusing on practices that improve implementation productivity, such as code reuse, use of third-party libraries, and the application of “ good ” programming techniques. In addition, apart from the finding that performance is a key-driver for many of these applications, scientific software developers also find maintainability and productivity to be important. Conclusions: The results of the study are compared to existing literature, are interpreted under a software engineering prism, and various implications for researchers and practitioners are provided. One of the key findings of the study, which is considered as important for driving future research endeavors is the lack of evidence on the trade-offs that need to be made when applying a software practice, i.e., negative (indirect) effects on other quality attributes.", "Keywords": "Software engineering practices ; High performance computing ; Scientific computing", "DOI": "10.1016/j.jss.2020.110848", "PubYear": 2021, "Volume": "172", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, University of Macedonia, Thessaloniki, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, University of Macedonia, Thessaloniki, Greece;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Applied Informatics, University of Macedonia, Thessaloniki, Greece"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Alabama, United States of America"}], "References": [{"Title": "Programming languages for data-Intensive HPC applications: A systematic mapping study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "102584", "JournalTitle": "Parallel Computing"}]}, {"ArticleId": 84730545, "Title": "Local polynomial method for frequency response function identification", "Abstract": "Here we propose the local polynomial method to solve the problem of estimating the frequency response function in the linear system. Compared with other nonparametric identification methods based on the windowing strategies, this new identification method can be remarkably efficient in reducing the effect caused by the leakage error when the discrete Fourier transform is used under a non-periodic input excited signal. Considering the constraints between the coefficients of the polynomials at neighbour frequencies, we modify the proposed local polynomial method to get one constrained local polynomial method. The modified local polynomial method reduces the mean square error of the frequency response function and the estimation of the frequency response function is identified by one multi-objective least squares criterion. Finally the simulation example results confirm the identification theoretical results.", "Keywords": "Frequency response function ; nonparametric identification ; leakage error ; constraint", "DOI": "10.1080/21642583.2020.1833784", "PubYear": 2020, "Volume": "8", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "Dong <PERSON>", "Affiliation": "College of Sciences, Henan University of Engineering, Zhenzhou, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Sciences, Henan University of Engineering, Zhenzhou, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electronic Engineering and Automation, Jiangxi University of Science and Technology, Ganzhou, People’s Republic of China"}], "References": []}, {"ArticleId": 84730566, "Title": "Leveraging pointwise prediction with learning to rank for top-N recommendation", "Abstract": "<p>Pointwise prediction and Learning to Rank (L2R) are two hot strategies to model user preference in recommender systems. Currently, these two types of approaches are often considered independently, and most existing efforts utilize them separately. Unfortunately, pointwise prediction tends to cause the problem of overfitting, while L2R is more prone to higher variance. On the other hand, the advantages of multi-task learning and ensemble learning inspire us to utilize multiple approaches jointly so that methods can promote together synergistically. Therefore, we propose a new framework called CPL, where pointwise prediction and L2R are inherently combined to improve the performance of top-N recommendations. To verify the effectiveness of CPL, an instantiation of CPL, which is named CPLmg, is introduced. CPLmg is based on two components, i.e., Factorized SLIM (Sparse LInear Method) and GAPfm (Graded Average Precision factor model), to perform pointwise prediction and L2R, respectively. Different from the original version of SLIM, FSLIM reconstructs a denser representation both for users and items. Moreover, the low-rank users’ and item’s latent factor matrices act as a bridge between FSLIM and GAPfm. Extensive experiments on four real-world datasets show that CPLmg significantly outperforms the compared methods. To explore other possible combinations for CPL further, we selected another pointwise method, i.e., FunkSVD, and L2R approach, i.e., BPR, to implement CPLdb. The experimental results demonstrate the superiority of CPL again as it can help improve the performance of its pointwise prediction and L2R components.</p>", "Keywords": "Implicit feedback; Personalized recommendation; Collaborative filtering; Learning to Rank; Metrics optimization; Similarity method", "DOI": "10.1007/s11280-020-00846-3", "PubYear": 2021, "Volume": "24", "Issue": "1", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Institute for Advanced Communication and Data Science, Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Institute for Advanced Communication and Data Science, Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "Xinjiang Lu", "Affiliation": "Baidu Business Intelligence Lab, Baidu Research, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai Institute for Advanced Communication and Data Science, Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}], "References": []}, {"ArticleId": 84730674, "Title": "Fuzzy rating scales: Does internal consistency of a measurement scale benefit from coping with imprecision and individual differences in psychological rating?", "Abstract": "Abstrac Measuring psychological variables (attitudes, opinions, perceptions, feelings, etc.) there is a need for rating scales coping with both the natural imprecision and individual differences. In this respect, the so-called fuzzy rating scales have been introduced as a doubly continuous instrument allowing to capture both imprecision and individual differences. Aiming to show the advantages of using fuzzy rating scales in the setting of questionnaires, the extended Cronbach α is considered to quantify the internal consistency associated with constructs involving fuzzy rating scale-based items. This extended tool allows us to draw interesting conclusions, the main one supporting the use of fuzzy rating scales instead of standard ones (namely, Likert type, visual analogue, and even fuzzy linguistic scales). Although general theoretical conclusions could not be drawn, unequivocal majority trends can be stated from simulation-based and real-life examples. Introduction In introducing Fuzzy Sets and Logic [92], [93], [94] <PERSON><PERSON><PERSON> anticipated that they would play an important role in human thinking. In particular, applications of fuzzy approaches and fuzzy-based techniques to Psychology were foreseen and developed since the very beginning (see, for instance, [40], or the edited book [97]). And they are more active and challenging nowadays than in the past (see, for instance, [71], [72], [75], [83] for recent reviews about). Psychological measurement has traditionally dealt with giving or choosing either numbers, linguistic/qualitative labels or symbols to a wide array of variables. Psychological dimensions can be measured by different approaches, but they have relied mainly on questionnaires. As measurement is intended to be difficult into capturing all of the intrinsic nuances of psychological dimensions, most questionnaires have been preferred to including several items for every dimension, attempting to achieve a higher chance of respondents to be consistent. As it has been recently highlighted [87], the choice of response format should play a crucial role in designing a new questionnaire. In this respect, choosing the response format should be an explicit step to be added to other well-known steps such as the construct definition, the target population, the item selection, and so on. The most common response format in questionnaires is the rating scale response format. A recent guest editorial of the special issue on ‘Design aspects of rating scales in questionnaires’ [56] points out that “…Since their introduction by Thurstone [79] and Likert [44], rating scales have been determinant in questionnaires. A rating scale usually defines the graduations out of a continuum such as agreement, intensity, frequency, or satisfaction. Respondents evaluate questions and items by marking the appropriate category, which usually concerns personal characteristics, opinions, and behavior.” When aspects to be studied through questionnaires can be numerically measured in a direct way, the corresponding items make use of numerical scales and the statistical analysis of the associated responses can be analyzed by means of different well-known statistical techniques. Nevertheless, many aspects of interest are not directly measurable, so other rating scales should be considered. Psychological measurement can be frequently seen as how much individuals agree or disagree with each of a series of statements related with psychological latent variables. In measuring these variables several rating scales have been considered in the literature. Among these scales, Likert type and visual analogue (this last one also coined as graphic or continuous rating) are probably the most popular ones (e.g., [24], [37], [43], [66], [77], [80]). Likert Scales format [44] consists of some scores indicating the strength of the agreement with several assertions, the Likert type items. Sometimes, these numbers are combined with or replaced by linguistic expressions which are, for instance, adverbs of frequency and quantity. Despite Likert scales have been adopted for many social science research communities, they present some controversy and debate concerning several issues, among them, the nature of the response categories and the uses of the scores. Likert scale-based items in a questionnaire are usually easy to respond, and the answers (before numerically ‘encoding’ them) can partially capture a certain involved imprecision. Since the choice is made within a list of a few possible ones, anchored for the Likert options, individual differences are almost systematically overlooked. Furthermore, the number of applicable (non-parametric) techniques to statistically analyze Likert data is quite limited and relevant statistical information is usually lost in the analysis (see [86] for a recent short discussion about). Visual Analogue Scales (VAS’s for short) were mostly considered to overcome the limitations with ordinal discrete Likert-type scales. VAS’s have a long tradition in psychological measurement, and were introduced [28] in attaining judgements about employees by supervisors (see [90] for a recent historical study). Respondents to a VAS item/scale, mark their level of agreement to a statement by indicating a position along a continuous line between two end-points, permitting an infinite number of gradations. In contrast to Likert scales, a VAS properly captures individual differences because the choice is made within a continuum of possible options (actually, a bounded interval). However, the choice of the single point that best represents rater’s score in visual analogue scales is frequently neither easy nor natural in the usual imprecise settings (see [85]). To require a full precision seems rather unrealistic in connection with most of psychological variables. Aiming to appropriately capture the natural imprecision involved in measuring most of psychological variables, fuzzy scales have been introduced. The best known fuzzy scales of measurement are those associated with fuzzy linguistic variables, which can mathematically cope with the imprecision of linguistic labels (see [94]). But to also cope with the individual differences, which are statistically relevant, the fuzzy rating scales (see [33], [34]) are a very appropriate instrument. In Section 2 the preliminaries for fuzzy scales as well as the two main types of fuzzy scales are recalled. Furthermore, a few comments from some comparative studies (e.g., [15], [26], [47], [50], [51]) are given to state that fuzzy scales could lead to statistical conclusions different from those with Likert’s and VAS’s. Since fuzzy rating scales are the only ones capturing simultaneously imprecision and individual differences in dealing with questionnaires, they look to be more informative from a statistical perspective than the Likert type, the visual analogue and the fuzzy linguistic scales. This paper presents for the first time a study allowing us to prove such an intuition by considering a unique ‘ranking’ tool. This tool is the internal consistency of the involved constructs, which is to be based on the extension of the well-known Cronbach α coefficient and is presented in Section 3. Three key research questions will be posed about the extended tool in order to state that • the shape of fuzzy responses scarcely affects the value of the extended Cronbach coefficient (STUDY 1, Section 4); • the usual behavior of Cronbach’s coefficient in connection with the number of items of the constructs for numerical or numerically encoded responses is preserved when fuzzy scales are considered (STUDY 2, Section 5); • fuzzy rating scales are the ones very mostly showing the highest internal consistency (STUDY 3, Section 6). It should be highlighted that there are no general conclusions concerning the posed questions, so no theoretical results can be established for this purpose. However, by designing adequate novel simulation studies mimicking real-life situations, majority trends can be formally obtained. Therefore, STUDIES 1–3 will be carried out on the basis of such simulations. Finally, a real-life example is examined to complete these simulation-based studies and corroborate the majority conclusions. Figures Graphical representation of the trapezoidal fuzzy number Tra(a,b,c,d). Steps in establishing a fuzzy score on the basis of a certain FRS. A 4-tuple (x1,x2,x3,x4) generated from the simulation process, and the associated trapezoidal fuzzy datum. Eight types of fuzzy numbers sharing support, (10,40), the first six ones sharing also the core, [20,25], and all of them differing in shape. Evolution of Cronbach’s α with respect to the sample size for different numbers of items in the construct. Frequently used fuzzy linguistic encodings of 5-point Likert scales. Show all figures Section snippets Fuzzy scales for psychological measurement Fuzzy scales make use of a particular type of fuzzy set that model imprecise amounts: the fuzzy numbers. Definition 2.1 A (bounded) fuzzy number is an imprecise amount or quantity that is formally characterized by means of a mapping U ~ : R → [ 0 , 1 ] ( R denoting the space of real numbers), where U ~ ( x ) is interpreted as the degree to which real number x is compatible with U ~ , and such that for each υ ∈ ( 0 , 1 ] the υ - level set , U ~ υ = set of real numbers which are compatible with to a extent at least equal to υ , U ∼ υ = { x ∈ R : U ∼ ( A ‘ranking’ tool between rating scales: The extended α As it has been already remarked, fuzzy rating scales seem intuitively to be statistically more informative than Likert, VAS and FLS ones. However, no comparison between these scales has been made yet through a unique/unified ‘ranking’ tool, so that these scales could be ranked with respect to a certain criterion. Inspired by researches like the one by Sung and Wu [77], a first convenient tool to start with such a comparison is the one associated with the internal consistency/reliability in the Synthetic STUDY 1: Influence of the shape of fuzzy data on the internal consistency of FRS-based constructs Regarding the analysis of the effect of the shape of fuzzy data on the value of Cronbach’s α , we will consider the above referred LU fuzzy numbers, which could be characterized by means of an ordered 4-tuple ( a , b , c , d ) , trapezoidal data being an example of them. This analysis aims to discuss whether or not choosing trapezoidal numbers to model fuzzy scores leads to different α values from those considering some other usual choices. Certainly, the lack of realistic models for the distributions of Synthetic STUDY 2: Effect of the number of items on the internal consistency of FRS-based constructs In this study, and given the scarce influence of the shape of fuzzy-valued responses, fuzzy data are generated in accordance with the method in the Synthetic STUDY 1 by considering LU = Tra . This analysis aims to examine the effect of the number of items in a construct, and secondarily that of the number of respondents, on the value of the extended α . Such an influence has been widely reported in the literature in connection with Likert-type scales (e.g., [35], [36]). Different choices of K and n Synthetic STUDY 3: Influence of the rating scale on the internal consistency of constructs In developing comparative studies in connection with Research Question 3 there is a need, on one hand, for processes to encode Likert responses both numerically and linguistically fuzzy. On the other hand, because we have to consider simulation studies, there is a need to mimic the link between FRS and Likert responses, as well as the one between FRS and VAS responses. As for the Synthetic STUDY 1, in the simulations in this study the reference interval will be assumed to be [ l 0 , u 0 ] = [ 0 , 100 ] . Empirical STUDY 4: illustrating with a real-life example In this study, the majority trends in the synthetic ones are to be illustrated by means of a real-life example. A sample of 70 people (from Spain and Italy) with different age, background, work type and position has been considered to respond to a questionnaire about Food Quality And Satisfaction Evaluation that measures two constructs, namely, the ‘quality of the food and beverage’ (FB) and the ‘satisfaction with restaurant services’ (RS). The items have consisted of making the customers to General discussion and concluding remarks The preceding studies allow us to state several summary assertions concerning the majority behavior of the extended α with respect to: the shape of the fuzzy scores, the number of items in a construct where items are FRS-based, and the considered rating scale. By looking at the rows in Table 1, the results in connection with Research Question 1 lead to conclude that the shape modelling fuzzy responses scarcely influences the reliability based on the internal consistency. An implication from this CRediT authorship contribution statement María Asunción Lubiano: Investigation, Conceptualization, Methodology, Software. Antonio L. García-Izquierdo: Conceptualization, Investigation. María Ángeles Gil: Writing - review & editing, Investigation, Methodology, Supervision. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments The authors are very grateful to the reviewers in charge of the manuscript for their valuable comments and suggestions. References (98) G. Acampora et al. A hierarchical neuro-fuzzy architecture for human behavior analysis Inf. Sci. (2015) P. Diamond et al. Metric spaces of fuzzy sets Fuzzy Sets Syst. (1990) L. Stefanini et al. Generalized fuzzy differentiability with LU-parametric representation Fuzzy Sets Syst. (2014) B. Sinova et al. The median of a random fuzzy number. The 1-norm distance approach Fuzzy Sets Syst. (2012) L. Stefanini et al. Parametric representation of fuzzy numbers and applications to fuzzy calculus Fuzzy Sets Syst. (2006) E.H. Ruspini Numerical methods for fuzzy clustering Inf. Sci. (1970) M.L. Puri et al. Fuzzy random variables J. Math. Anal. Appl. (1986) I.A. Motawa et al. A fuzzy system for evaluating the risk of change in construction projects Adv. Eng. Softw. (2006) J.M. Mendel et al. A new method for calibrating the fuzzy sets used in fsQCA Inf. Sci. (2018) M.A. Lubiano et al. A hypothesis testing-based discussion on the sensitivity of means of fuzzy data with respect to data shape Fuzzy Sets Syst. (2017) M. Dagˇdeviren et al. Developing a fuzzy analytic hierarchy process (AHP) model for behavior-based safety management Inf. Sci. (2008) M.A. Lubiano et al. Hypothesis testing-based comparative analysis between rating scales for intrinsically imprecise data Int. J. Approx. Reasoning (2017) A.L. Sullivan et al. Are school psychologists’ special education eligibility decisions reliable and unbiased?: A multi-study experimental investigation J. Sch. Psychol. (2019) Y. Alkis et al. Development and validation of social anxiety scale for social media users Comput. Hum. Behav. (2017) A. Colubi et al. Nonparametric criteria for supervised classification of fuzzy data Int. J. Approx. Reasoning (2011) G. González-Rodríguez et al. Fuzzy data treated as functional data. A one-way ANOVA test approach Comput. Stat. Data Anal. (2012) K. Verswijvel et al. Designing and validating the friendship quality on social network sites questionnaire Comput. Hum. Behav. (2018) M.A. Lubiano et al. The λ → -mean squared dispersion associated with a fuzzy random variable Fuzzy Sets Syst. (2000) U. Wadgave et al. Parametric tests for Likert scale: For and against Asian J. Psychiatr. (2016) M.A. Lubiano et al. Descriptive analysis of responses to items in questionnaires. Why not using a fuzzy rating scale? Inf. Sci. (2016) R.R. Yager A procedure for ordering fuzzy subsets of the unit interval Inf. Sci. (1981) B. Hesketh et al. A future-oriented retirement transition adjustment framework J. Vocat. Behav. (2011) C.H. Yeh et al. Task oriented weighting in multi-criteria analysis Eur. J. Oper. Res. (1999) R. Körner On the variance of fuzzy random variables Fuzzy Sets Syst. (1997) L.A. Zadeh Fuzzy sets Inf. Cont. (1965) B. Hesketh et al. Work adjustment theory: An empirical test using a fuzzy rating scale J. Vocat. Behav. (1992) L.A. Zadeh Quantitative fuzzy semantics Inf. Sci. (1971) B. Hesketh et al. Practical applications and psychometric evaluation of a computerised fuzzy graphic rating scale L.A. Zadeh A computational approach to fuzzy quantifiers in natural languages Comput. Math. Appl. (1983) T. Hesketh et al. An application of a computerized fuzzy graphic rating scale to the psychological measurement of individual differences Int. J. Man-Machine Stud. (1988) E. Benoit Expression of uncertainty in fuzzy scales based measurements Measurement (2013) M. Kochen Applications of fuzzy sets in Psychology L.A. Zadeh Is there a need for fuzzy logic? Inf. Sci. (2008) C. Karyotis et al. A fuzzy computational model of emotion for cloud based sentiment analysis Inf. Sci. (2018) S.Z. Eyupoglu et al. Application of fuzzy logic in job satisfaction performance problem Procedia Comput. Sci. (2016) Q. Zhou et al. User sentiment analysis based on social network information and its application in consumer reconstruction intention Comput. Hum. Behav. (2019) M.A. Lubiano et al. Hypothesis testing for means in connection with fuzzy rating scale-based data: algorithms and applications Eur. J. Oper. Res. (2016) S.K. Savaş et al. A fuzzy ID3 induction for linguistic data sets Int. J. Intell. Syst. (2018) F. Şahin et al. Big five personality traits, entrepreneurial self-efficacy and entrepreneurial intention. A configurational approach Int. J. Entrep. Behav. Res. (2019) K.Y. Jin et al. MIMIC approach to assessing differential item functioning with control of extreme response style Behav. Res. Methods (2020) B. Efe et al. A systematic approach for an application of personnel selection in assembly line balancing problem Int. Trans. Oper. Res. (2018) S.B. Javali et al. Effect of varying sample size in estimation of reliability coefficients of internal consistency WebmedCentral BIOSTATISTICS (2011) M. Smithson Fuzzy sets and fuzzy logic in the human sciences J. Konacoglu et al. A new fuzzy decision making approach for personnel selection problem Intell. Dec. Tech. (2019) S. Srivastava et al. A review on role of Fuzzy Logic in Psychology T. Kuhlmann et al. Investigating measurement equivalence of visual analogue scales and Likert-type scales in Internet-based personality questionnaires Behav. Res. Methods (2017) P. D’Urso et al. Fuzzy data analysis and classification Adv. Data Anal. Classif. (2017) M.A. Lubiano et al. Case study-based sensititivy analysis of scale estimates w.r.t. the shape of fuzzy data S. de la Rosa de Sáa et al. Fuzzy rating scale-based questionnaires and their statistical analysis IEEE Trans. Fuzzy Syst. (2015) View more references Cited by (0) Recommended articles (6) Research article On the monotonicity of the eigenvector method European Journal of Operational Research, 2020 Show abstract Pairwise comparisons are used in a wide variety of decision situations where the importance of alternatives should be measured on a numerical scale. One popular method to derive the priorities is based on the right eigenvector of a multiplicative pairwise comparison matrix. We consider two monotonicity axioms in this setting. First, increasing an arbitrary entry of a pairwise comparison matrix is not allowed to result in a counter-intuitive rank reversal, that is, the favoured alternative in the corresponding row cannot be ranked lower than any other alternative if this was not the case before the change (rank monotonicity). Second, the same modification should not decrease the normalised weight of the favoured alternative (weight monotonicity). Both properties are satisfied by the geometric mean method but violated by the eigenvector method. The axioms do not uniquely determine the geometric mean. The relationship between the two monotonicity properties and the Saaty inconsistency index are investigated for the eigenvector method via simulations. Even though their violation turns out not to be a usual problem even for heavily inconsistent matrices, all decision-makers should be informed about the possible occurrence of such unexpected consequences of increasing a matrix entry. Research article Robust stochastic sorting with interacting criteria hierarchically structured European Journal of Operational Research, 2020 Show abstract In this paper we propose a multiple criteria decision aiding method to deal with sorting problems in which alternatives are evaluated on criteria structured in a hierarchical way and presenting interactions. The underlying preference model is the Choquet integral, while the hierarchical structure of the criteria is taken into account by applying the Multiple Criteria Hierarchy Process. Considering the Choquet integral based on a 2-additive capacity (non additive weights for considered criteria), we present a procedure to find all minimal sets of pairs of interacting criteria representing the preference information provided by the Decision Maker (DM). Robustness concerns are also addressed applying the Robust Ordinal Regression and the Stochastic Multicriteria Acceptability Analysis. Even if in different ways, both of them provide recommendations on the hierarchical sorting problem at hand by exploring the whole set of capacities compatible with the preferences provided by the DM. The applicability of the considered method to real world problems is demonstrated by an example regarding rating of European countries evaluated on economic, governmental and financial data provided by Standard & Poor’s Global Inc. Research article Group decision making with incomplete q -rung orthopair fuzzy preference relations Information Sciences, 2020 Show abstract In this paper, we propose a novel group decision making (GDM) method in incomplete q -rung orthopair fuzzy preference relations ( q -ROFPRs) environments. We propose an additive consistency definition, which is characterized by a q -rung orthopair fuzzy priority vector. The property of the proposed additive consistency definition is offered and a model to obtain missing judgments in incomplete q -ROFPRs is proposed. We present an approach to adjust the inconsistency for q -ROFPRs, propose a model to obtain the priority vector, and propose a method to increase consensus degrees of q -ROFPRs. Finally, we present a GDM method in incomplete q -ROFPRs environments and use two illustrative examples and some comparisons to illustrate that our method outperforms the existing methods for GDM in incomplete q -ROFPRs environments. The proposed GDM method offers us a useful way for GDM in incomplete q -ROFPRs environments. Research article Multi-stage consistency optimization algorithm for decision making with incomplete probabilistic linguistic preference relation Information Sciences, 2020 Show abstract Incomplete probabilistic linguistic term sets (InPLTSs) can effectively describe the qualitative pairwise judgment information in uncertain decision-making problems, making them suitable for solving real decision-making problems under time pressure and lack of knowledge. Thus, in this study, an optimization algorithm is developed for preference decision-making with the incomplete probabilistic linguistic preference relation (InPLPR). First, to fully investigate the ability of InPLTSs to express uncertain information, they are divided into two categories. Then, a two-stage mathematical optimization model based on an expected multiplicative consistency for estimating missing information is constructed, which can obtain the complete information more scientifically and effectively than some exiting methods. Subsequently, for the InPLPR with unacceptable consistency, a multi-stage consistency-improving optimization model is proposed for improving the consistency of the InPLPR by minimizing the information distortion and the number of adjusted linguistic terms, which can also minimize the uncertainty of the relationship as small as possible. Afterward, to rank all the alternatives, a mathematical model for deriving the priority weights of the alternatives is constructed and solved, which can obtain the priority weight conveniently and quickly. A decision-making algorithm based on the consistency of the InPLPR is developed, which involves estimating missing information, checking and improving the consistency, and ranking the alternatives. Finally, a numerical case involving the selection of excellent students is presented to demonstrate the application of the proposed algorithm, and a detailed validation test and comparative analysis are presented to highlight the advantages of the proposed algorithm. Research article Incremental fuzzy probability decision-theoretic approaches to dynamic three-way approximations Information Sciences, Volume 550, 2021, pp. 71-90 Show abstract As a special model of three-way decision, three-way approximations in the fuzzy probability space can be interpreted, represented, and implemented as dividing the universe into three pair-wise disjoint regions, i.e., the positive, negative and boundary regions, which are transformed from the fuzzy membership grades with respect to the fuzzy concept. To consider the temporality and uncertainty of data simultaneously, this paper focuses on the integration of dynamics and fuzziness in the context of three-way approximations. We analyze and investigate three types of fuzzy conditional probability functions based on the fuzzy T -norm operators. Besides, we introduce the matrix-based fuzzy probability decision-theoretic models to dynamic three-way approximations based on the principle of least cost. Subsequently, to solve the time-consuming computational problem, we design the incremental algorithms by the updating strategies of matrices when the attributes evolve over time. Finally, a series of comparative experiments is reported to demonstrate and verify the performance of proposed models. Research article An entropy empowered hybridized aggregation technique for group recommender systems Expert Systems with Applications, Volume 166, 2021, Article 114111 Show abstract Group recommender systems aim to suggest appropriate products/services to a group of users rather than individuals. These recommendations rely solely on determining group preferences, which is accomplished by an aggregation technique that combines individuals’ preferences. A plethora of aggregation techniques of various types have been developed so far. However, they consider only one particular aspect of the provided ratings in aggregating (e.g., counts, rankings, high averages), which imposes some limitations in capturing group members’ propensities. Besides, maximizing the number of satisfied members with the recommended items is as significant as producing items tailored to the individual users. Therefore, the ratings’ distribution is an essential element for aggregation techniques to discover items on which the majority of the members provided a consensus. This study proposes two novel aggregation techniques by hybridizing additive utilitarian and approval voting methods to feature popular items on which group members provided a consensus. Experiments conducted on three real-world benchmark datasets demonstrate that the proposed hybridized techniques significantly outperform all traditional methods. For the first time in the literature, we offer to use entropy to analyze rating distributions and detect items on which group members have reached no or little consensus. Equipping the proposed hybridized type aggregation techniques with the entropy calculation, we end up with an ultimate enhanced aggregation technique, Agreement without Uncertainty , which was proven to be even better than the hybridized techniques and outperform two recent state-of-the-art techniques. <sup> ☆ </sup> The paper is dedicated to the memory of our admired friend Dr. Enrique H. Ruspini. <sup> ☆☆ </sup> This research has been partially supported by the Principality of Asturias/FEDER Grant GRUPIN-IDI2018-000132 and the Spanish Ministry of Science, Innovation and Universities Grants PSI2013-44854-R and MTM2015-63971-P and the Spanish Ministry of Science and Innovation PID2019-104486GB-I00. Their support is gratefully acknowledged. View full text © 2020 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2020.10.042", "PubYear": 2021, "Volume": "550", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Estadística, I.O. y D.M., Facultad de Ciencias, Universidad de Oviedo, E-33071 Oviedo, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Psicología, Facultad de Psicología, Universidad de Oviedo, E-33071 Oviedo, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departamento de Estadística, I.O. y D.M., Facultad de Ciencias, Universidad de Oviedo, E-33071 Oviedo, Spain;Corresponding author"}], "References": []}, {"ArticleId": 84730690, "Title": "Multiple images encryption based on 3D scrambling and hyper-chaotic system", "Abstract": "The growth and popularization of network and multimedia technologies have led to an increase in digital data over the internet. Most of the data is confidential and requires an efficient algorithm to securely transmit the data over the insecure channel. The paper proposes a cryptographic algorithm which can be used for securing multiple digital images over the network. The algorithm uses the concept of chaos theory and elliptic curve Elgamal cryptosystem for the generation of the cipher image and sharing of the key. A 3D image is generated using the multiple plain images and undergoes permutation and substitution phase for generation of the cipher data. Experimental results and analysis shows that the proposed algorithm has got large keyspace, desired statistical properties of cipher data and resistant to attacks. Introduction Digitization has taken over the internet and other areas including the defense system, weather forecasting, banking, medical systems and much more over the past few decades. Among all the digital data available, digital images have been proved as a major area of research [1]. Confusion and diffusion are two principal concepts used in image cryptography. The former property is described as building a strong relationship between cipher and the key, whereas the later is defined as a minute change of plain data must result in a drastic change in cipher data. Today, the researchers are working to develop an efficient encryption algorithm using concepts like DNA sequencing, chaos theory, wavelet transformation, compress sensing and much more. The standard text encryption methods such as Advanced Encryption Standard (AES) and Data Encryption Standard (DES) are generally not preferred for image encryption because image pixels are highly correlated. Various authors have combined AES with other mathematical concepts such as wavelet transformation [2] or chaotic system [3] for image encryption operations. The concept of biological human DNA sequence is also explored by various authors to develop DNA based cryptosystem. Yu et al. [4] has used the concept of spatio-temporal chaotic theory along with DNA sequencing. The DNA sequence rearrangements are performed using DNA insertions and deletion operation to the rows and columns and the spatio-temporal chaotic system having the characteristic property of long periodicity and complex dynamic structure. The key streams are generated from the two plain images. Wang et al. [5] uses zig-zag transformation along with the DNA encoding system. The zigzag is a scanning method that scrambles the image. The second scrambling is done using identification value. The confusion in this method is done using the DNA sequence and cipher image is generated using the XOR operation with chaotic sequence. Li et al. [6] described a chaotic system with the wavelet transform method generating a scrambled plain image using the low-frequency components and further scrambling using Arnold’s cat map. The diffusion phase is applied to each decomposed block with a different key, hence generating a new encrypted block image. Zarebni et al. [7] has described the use of a different chaotic system to generate a single algorithm for random string generation. Gong et al. [8] describes the use of chaotic systems along with the compressed sensing method. The compressed sensing incorporates sparse signals sensing and compression by finding a solution to an undetermined linear system. Visual Secret Sharing (VSS) is a sub-class of Visual Cryptography [9] which uses the concept of cipher shares and the original image is reconstructed by stacking the shares. Chen et al. [10] in his paper has described one of the Visual Secret Sharing (VSS) methods named Random Grids which encrypts multiple images with two circular grids obtained by key and circular shifts. Broumandnia [11] in his paper has described a color image encryption scheme based on 3D chaotic map. Zhu [12] defines the use of the compressed sensing method and uses Chebyshev mapping for increasing the key-space. The Sigmoid function transforms the image pixels in a range of 0–255 stored as an 8-bit binary format. Hua et al. [13] describes a cosine-based transform where a combination of chaotic systems is being used to generate a new chaotic sequence equation which is difficult for the brute force attack. Similarly, he described 2D Sine Logistic in [14], [15] which has a wider chaotic range. The paper performs bit-level permutation and combination and Chaotic Magic Transform allowing simultaneous shuffling of image pixels in both rows and columns. Wang et al. [16] describes yet another method for fast encryption using the coupled map logistic latices which are used for key generation of permutation and diffusion on parallel computers. Arnold’s cat map, hyper-chaotic system are some other variations of chaotic generator algorithm which are generally used in the multiple image encryption algorithm. Various authors [17], [18], [19], [20], [25], [26] have discussed various chaotic system algorithms for the shuffling, confusion, diffusion and permutation of the pixel values in image security. Darwish [21] describes a 3D cat map to interact with the adaptive thresholding technique to find the association between the pixels. The key generation here depends on the plain image, hence most security is provided. Zhang et al. [22], [23] proposed a multiple image encryption scheme which involves chaotic random generation with and without permutation respectively. Various other methods that can be used for image encryption include permutation and substitution which results in good confusion and diffusion property. Zhang et al. [24] has described a chaotic system for the key generation and a substitution box (S- Box) which is used for generating the random sequences. Diaconu et al. [27] uses inter and intra bit-level permutation using the chaos theory. The binary value representation is used to calculate the number and direction of row shifts. The scrambled image is obtained by exchanging the bits among the neighbors. Thus, this creates more uniform bit-distribution and reduces the correlation. Among all the methods of image encryption, the chaotic system based scheme holds certain characteristic that is suitable for encryption, such as sensitivity to initial conditions, highly unpredictable and ergodicity. The hyper-chaotic system provides complex output signals, robust parameters, and the bandwidth is distributed. X. Wu et al. [28] describes 2D wavelet transformation along with a 6D hyper-chaotic system to be used in both spatial and frequency domain and modifies the pixel orientation of intermediary images with a different keystream. This encryption scheme is used for single image where the images are divided into bands for encryption. Encrypting multiple images requires complex chaotic structure and complex permutations. Thus, designing of good cryptosystem is expected to have enhanced security of the encryption algorithm. The algorithm in this paper describes a multiple image encryption concept that uses the 4D hyper-chaotic system for the random value generation. Multiple images are combined to form 3D image and scrambled along with rotation operation. Elliptic curve algorithm [29] with Elgamal encryption technique helps in sharing of the keys between the sender and receiver. The algorithm goes through various tests such as [30], [31] to check for the randomness of the cipher data generated and validity of the algorithm. The contribution of this paper are: 1. Multiple images are encrypted by forming 3D cube structure using multiple images. 2. To prevent the algorithm for known plain-text attack, SHA-512 is applied to the input image to generate a hash value which in turn is used to generate a shared key computed using elliptic curve cryptography. 3. To provide better retrieval of the images under a certain percentage of occlusion or noise attack, the 3D cube undergoes permutation and rotation along all the axes to generate a scrambled 3D cube. 4. To decrease the number of iterations for computing chaotic sequence, base conversion operation is used. The following is the organization of this paper. Section 2 explains how the algorithm is coded. The simulation results are described in the Section 3 and finally, the outline and conclusion are given in Section 4. Figures Plot in (a) xyz plane. (b) wxz plane. (c) wz plane. (d) xz plane. Pictorial representation for scrambling along z-axis. Block diagram for Encryption and Decryption algorithm. (a)–(h) Sample 512×512 dimension images. (i) 3D cube image generated with dimension 128×128×128 using the sample images. (j) 3D image cube after scrambling along z-plane using Pz. (k) 3D image cube af... Correlation graph for 3D plain image (a) P(x,y,z) against P(x+1,y,z). (b) P(x,y,z) against P(x,y+1,z). (c) P(x,y,z) against P(x+1,y+1,z). (d) P(x,y,z) against P(x,y,z+1). (e) P(x,y,z) against P(x,y+1,... (a) Histogram plot of a plain 3D image shown in Fig. 4i. (b) Histogram plot of a cipher 3D image shown in Fig. 4m. Show all figures Section snippets Creating 3D cube Three-dimensional image can be generated by piling each image on top of each other. To form 128 × 128 × 128 cube, the multiple 2D plain images are partitioned into 128 × 128 and combined by piling on top of each other to form the cube. Table 1 shows the number of images required for a given image size to combine and form the cube of size 128 × 128 × 128 . Key exchange The proposed scheme uses the ciphering concept of ElGamal encryption technique based on Elliptic Curve Cryptography (ECC) [32], [33] to secretly share a Simulation and analysis of the proposed scheme The proposed scheme is simulated on an Intel (R) Xeon (R) W-2133 CPU @ 3.60 GHz Fujitsu Celsius workstation using Wolfram Mathematica with 32 GB RAM. The images are taken from freely available database [37]. The elliptic curve parameters used for key exchange using ECC based on ElGamal technique are taken from Brainpool [38] and tabulated in Table 3. Fig. 4a–h shows the eight plain images used as input. The 3D image cube generated using the eight plain images is shown in Fig. 4i. Fig. 4j shows Conclusion A multiple-image encryption scheme is proposed where the multiple images are treated as a 3D cube. A 3D cube is generated using the plain images as input and a hyper-chaotic system is used for scrambling as well as generating a chaotic sequence for an encryption operation. A hash value is generated from the 3D plain cube and a key point is generated using point multiplication operation of the ECC and shared as cipher data using the ElGamal enciphering technique based on ECC as a shared key CRediT authorship contribution statement Aasawari Sahasrabuddhe: Conceptualization, Formal analysis, Data curation, Writing - original draft, Writing - review & editing. Dolendro Singh Laiphrakpam: Conceptualization, Formal analysis, Data curation, Writing - original draft, Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. References (42) H.M. Ghadirli et al. An overview of encryption algorithms in color images Signal Processing (2019) C.L. Li et al. Multiple-image encryption by using robust chaotic map in wavelet transform domain Optik (2018) A.V. Diaconu Circular inter–intra pixels bit-level permutation and chaos-based image encryption Information Sciences (2016) L. Gong et al. An image compression and encryption algorithm based on chaotic system and compressive sensing Optics & Laser Technology (2019) K.A.K. Patro et al. Multiple grayscale image encryption using cross-coupled chaotic maps Journal of Information Security and Applications (2020) X. Wu et al. A novel lossless color image encryption scheme using 2D DWT and 6D hyperchaotic system Information Sciences (2016) A. Broumandnia The 3D modular chaotic map to digital color image encryption Future Generation Computer Systems (2019) Z. Tang et al. Multiple-image encryption with bit-plane decomposition and chaotic maps Optics and Lasers in Engineering (2016) Z. Hua et al. Cosine-transform-based chaotic system for image encryption Information Sciences (2019) Z. Hua et al. Image encryption using 2D Logistic-adjusted-Sine map Information Sciences (2016) Z. Hua et al. 2D Sine Logistic modulation map for image encryption Information Sciences (2015) T. Gao et al. Analysis of the hyper-chaos generated from Chen’s system Chaos, Solitons and Fractals (2009) X. Wang et al. Fast image encryption algorithm based on parallel computing system Information Sciences (2019) S. Toughi et al. An image encryption scheme based on elliptic curve pseudo random and Advanced Encryption System Signal Processing (2017) M. Xu et al. A novel image cipher based on 3D bit matrix and latin cubes Information Sciences (2019) T.H. Chen et al. Multi-image encryption by circular random grids Information Sciences (2012) X. Zhang et al. Multiple-image encryption algorithm based on mixed image element and permutation Optics and Lasers in Engineering (2017) Y. Wu et al. NPCR and UACI randomness tests for image encryption Cyber Journals: Multidisciplinary Journals in Science and Technology, Journal of Selected Areas in Telecommunications (2011) J.M. Pollard Monte Carlo methods for index computation (mod p) Mathematics of Computation (1978) A. Rukhin, J. Soto, J. Nechvatal, M. Smid, E Barker, S Leigh, M Levenson, M Vangel, D Banks, A Heckert, J Dray, S. Vo,... Y. Luo et al. A novel chaotic image encryption algorithm based on improved baker map and logistic map Multimedia Tools and Applications (2019) View more references Cited by (0) Recommended articles (6) Research article Residual implications on lattice L of intuitionistic truth values based on powers of continuous t-norms Information Sciences, Volume 550, 2021, pp. 109-128 Show abstract Residual implications are a special class of implications on the lattice L of intuitionistic truth values which possess interesting theoretical and practical properties. Many studies have investigated the properties of the types of implications on L and established the relationships among them. In this paper, the powers of the continuous t-norms T on L are introduced, and their properties studied. A new type of implication on L , termed the T -power based implication, is derived from the powers of the continuous t-norms T , as denoted by I I T and satisfies certain properties of the residual implications defined on the interval [0, 1] under certain conditions. Some important properties are analyzed. These results collectively reveal that they do not intersect the most well-known classes of fuzzy implications. Finally, we investigate the solutions of some Boolean-like laws for I I T . Research article A novel lossless color image encryption scheme using 2D DWT and 6D hyperchaotic system Information Sciences, Volumes 349–350, 2016, pp. 137-153 Show abstract This paper proposes a new lossless encryption algorithm for color images based on a six-dimensional (6D) hyperchaotic system and the two-dimensional (2D) discrete wavelet transform (DWT). Different from the current image encryption methods, our image encryption scheme is constructed using the 2D DWT and 6D hyperchaotic system in both the frequency domain and the spatial domain, where the key streams depend on not only the hyperchaotic system but the plain-image. In the presented algorithm, the plain-image is firstly divided into four image sub-bands by means of the 2D DWT. Secondly, the sub-bands are permutated by a key stream, and then the size of them is decreased by a constant factor. Thirdly, the 2D inverse DWT is employed to reconstruct an intermediate image by the four encrypted image sub-bands. Finally, to further enhance the security, the pixel values of the intermediate image are modified by using another key stream. Experimental results and security analysis demonstrate that the proposed algorithm has a high security, fast speed and can resist various attacks. Research article Image encryption algorithm based on the matrix semi-tensor product with a compound secret key produced by a Boolean network Information Sciences, Volume 539, 2020, pp. 195-214 Show abstract In this paper, a chaotic image encryption algorithm based on the matrix semi-tensor product (STP) with a compound secret key is designed. First, a new scrambling method is designed. The pixels of the initial plaintext image are randomly divided into four blocks. The pixels in each block are then subjected to different numbers of rounds of Arnold transformation, and the four blocks are combined to generate a scrambled image. Then, a compound secret key is designed. A set of pseudosecret keys is given and filtered through a synchronously updating Boolean network to generate the real secret key. This secret key is used as the initial value of the mixed linear-nonlinear coupled map lattice (MLNCML) system to generate a chaotic sequence. Finally, the STP operation is applied to the chaotic sequences and the scrambled image to generate an encrypted image. Compared with other encryption algorithms, the algorithm proposed in this paper is more secure and effective, and it is also suitable for color image encryption. Research article Designing permutation–substitution image encryption networks with Henon map Neurocomputing, Volume 283, 2018, pp. 53-63 Show abstract In traditional permutation–substitution architecture type image cipher, the permutation and substitution generally are two independent parts, and the diffusion performed by substitution is more like the cipher block chaining mode of operation. However, such operation approaches clearly downgrade the encryption efficiency because the pixel values need to be modified one by one for 2–4 overall rounds and images are scanned twice for permutation and substitution in each round. To improve the encryption efficiency, a new two-point diffusion strategy realized by discrete Henon map is proposed in this paper, which can significantly accelerate the diffusion process if there is more than one processing unit. Besides, the permutation and substitution are no longer two independent parts and they intermingle with each other so that the image required to be scanned just one time. To achieve the better ability of resisting chosen-plaintext or known-plaintext attack, the substitution keystream generated in our method is dependent on the plain image. Consequently, different plain images produce the distinct keystream for substitution. The results of various security analyses prove that our proposed image cryptosystem owns superior security, meanwhile, time complexity analysis shows that it can achieve faster encryption speed than most typical image encryption schemes. Research article A new fractional one dimensional chaotic map and its application in high-speed image encryption Information Sciences, Volume 550, 2021, pp. 13-26 Show abstract Chaos theory has been widely used in the design of image encryption schemes. Some low-dimensional chaotic maps have been proved to be easily predictable because of their small chaotic space. On the other hand, high-dimensional chaotic maps have a larger chaotic space. However, their structures are too complicated, and consequently, they are not suitable for real-time image encryption. Motivated by this, we propose a new fractional one-dimensional chaotic map with a large chaotic space. The proposed map has a simple structure and a high chaotic behavior in an extensive range of its control parameters values. Several chaos theoretical tools and tests have been carried out to analyze and prove the proposed map’s high chaotic behavior. Moreover, we use the proposed map in the design of a novel real-time image encryption scheme. In this new scheme, we combine the substitution and permutation stages to simultaneously modify both of the pixels’ positions and values. The merge of these two stages and the use of the new simple one-dimensional chaotic map significantly increase the proposed scheme’s security and speed. Besides, the simulation and experimental analysis prove that the proposed scheme has high performances. Research article Novel image encryption algorithm based on cycle shift and chaotic system Optics and Lasers in Engineering, Volume 68, 2015, pp. 126-134 Show abstract In this paper, a novel image encryption algorithm is proposed. The cycle shift in bits of pixels and the chaotic system are employed for the encryption of the proposed scheme. For cycle shift operations, random integers with the same size of the original image are produced to scramble the plaintext image. Moreover, the scrambled image effects the initial values of the chaotic system for the further encryption process, which increases the sensitivity of plaintext images of the scheme. The scrambled image is encrypted into the ciphered image by the keys which are produced by the chaotic system. The simulation experiments and theoretical analyses indicate that the proposed scheme is superior and able to resist exhaustive attack and statistical attack. View full text © 2020 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2020.10.031", "PubYear": 2021, "Volume": "550", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Silchar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Silchar, India;Corresponding author"}], "References": [{"Title": "Multiple grayscale image encryption using cross-coupled chaotic maps", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "", "Page": "102470", "JournalTitle": "Journal of Information Security and Applications"}]}, {"ArticleId": 84730695, "Title": "Development of novel carbon black-based heterogeneous oligonucleotide-antibody assay for sulfur mustard detection", "Abstract": "Herein, a novel configuration for the detection of a chemical warfare agent, namely sulfur mustard (bis(2-chloroethyl)sulphide) was designed to develop a biosensor that can play a relevant role in the monitoring of such a chemical threat in risk and war areas. In detail, a tailor-made heterogeneous oligonucleotide-antibody biosensor was realised, based on the formation of a sulfur mustard-oligonucleotide adduct on the surface of a screen-printed electrode. Thus, the immunoassay was carried out through a sequential immobilisation process, involving a primary antibody, ad hoc synthesised for the selective recognition of the sulfur mustard-oligonucleotide adduct, followed by an alkaline phosphatase-conjugated anti-mouse anti-IgG as secondary antibody. The detection was performed by adding the substrate 1-naphthyl phosphate and measuring the enzymatic product in differential pulse voltammetry. In order to improve the electroanalytical performances of the novel heterogeneous oligonucleotide-antibody sensor, we have explored the advantageous use of carbon black for the detection of alkaline phosphatase by-products. The electrochemical response of graphite-based screen-printed electrodes modified with three types of carbon black (i.e. N220, N115, and HP160) was thoroughly investigated by using cyclic voltammetry and amperometry for the detection of different enzymatic by-products namely 1-naphthol, hydroquinone, 4-nitrophenol, and ascorbic acid. This study allowed to shed light on the capacity of carbon black of remarkably enhancing the measurement of alkaline phosphatase’s products. Particularly, carbon black N220 and 1-naphthol were selected for developing a highly performant heterogeneous oligonucleotide-antibody assay for the detection of sulfur mustard. Under optimized conditions, the carbon black-based biosensor showed a linear range from 20 μM to 80 mM and a LOD of 12 μM in liquid samples, demonstrating high sensitivity as well as selectivity in presence of some possible interfering sources in the war field.", "Keywords": "Screen-printed electrodes ; Heterogeneous oligonucleotide-antibody biosensors ; Oligonucleotide-sulfur mustard adduct ; Chemical warfare agents ; Alkaline phosphatase", "DOI": "10.1016/j.snb.2020.129054", "PubYear": 2021, "Volume": "328", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Science and Technologies, University of Rome Tor Vergata, Via della Ricerca Scientifica, 00133, Rome, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical Science and Technologies, University of Rome Tor Vergata, Via della Ricerca Scientifica, 00133, Rome, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bundeswehr Medical Academy, Medical CBRN Defence, Munich, Germany;<PERSON><PERSON><PERSON>St<PERSON><PERSON>-Institute of Pharmacology and Toxicology, Ludwig-Maximilian-University Munich, Munich, Germany;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bundeswehr Institute of Pharmacology and Toxicology, Munich, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Chemical Science and Technologies, University of Rome Tor Vergata, Via della Ricerca Scientifica, 00133, Rome, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Chemical Science and Technologies, University of Rome Tor Vergata, Via della Ricerca Scientifica, 00133, Rome, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Bundeswehr Medical Academy, Medical CBRN Defence, Munich, Germany;<PERSON><PERSON><PERSON>St<PERSON>ub-Institute of Pharmacology and Toxicology, Ludwig-Maximilian-University Munich, Munich, Germany"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Science and Technologies, University of Rome Tor Vergata, Via della Ricerca Scientifica, 00133, Rome, Italy"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Science and Technologies, University of Rome Tor Vergata, Via della Ricerca Scientifica, 00133, Rome, Italy;SENSE4MED, via Renato Rascel 30, 00128, Rome, Italy;Corresponding authors"}], "References": []}, {"ArticleId": 84730722, "Title": "Function Design of Intelligent Service Cloud Platform Based on “Internet+”", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2020.95047", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "著刚 涂", "Affiliation": ""}], "References": []}, {"ArticleId": 84730761, "Title": "E2-MACH: Energy efficient multi-attribute based clustering scheme for energy harvesting wireless sensor networks", "Abstract": "<p> Internet of things have emerged enough due to its applications in a wide range of fields such as governance, industry, healthcare, and smart environments (home, smart, cities, and so on). Internet of things–based networks connect smart devices ubiquitously. In such scenario, the role of wireless sensor networks becomes vital in order to enhance the ubiquity of the Internet of things devices with lower cost and easy deployment. The sensor nodes are limited in terms of energy storage, processing, and data storage capabilities, while their radio frequencies are very sensitive to noise and interference. These factors consequently threaten the energy consumption, lifetime, and throughput of network. One way to cope with energy consumption issue is energy harvesting techniques used in wireless sensor network–based Internet of things. However, some recent studies addressed the problems of clustering and routing in energy harvesting wireless sensor networks which either concentrate on energy efficiency or quality of service. There is a need of an adequate approach that can perform efficiently in terms of energy utilization as well as to ensure the quality of service. In this article, a novel protocol named energy-efficient multi-attribute-based clustering scheme (E <sup>2</sup> -MACH) is proposed which addresses the energy efficiency and communication reliability. It uses selection criteria of reliable cluster head based on a weighted function defined by multiple attributes such as link statistics, neighborhood density, current residual energy, and the rate of energy harvesting of nodes. The consideration of such parameters in cluster head selection helps to preserve the node’s energy and reduce its consumption by sending data over links possessing better signal-to-noise ratio and hence ensure minimum packet loss. The minimized packet loss ratio contributes toward enhanced network throughput, energy consumption, and lifetime with better service availability for Internet of things applications. A set of experiments using network simulator 2 revealed that our proposed approach outperforms the state-of-the-art low-energy adaptive clustering hierarchy and other recent protocols in terms of first-node death, overall energy consumption, and network throughput. </p>", "Keywords": "", "DOI": "10.1177/1550147720968047", "PubYear": 2020, "Volume": "16", "Issue": "10", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Excellence in Information Technology, Institute of Management Sciences, Peshawar, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, International Islamic University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Excellence in Information Technology, Institute of Management Sciences, Peshawar, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Excellence in Information Technology, Institute of Management Sciences, Peshawar, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Sciences, Northumbria University, Newcastle Upon Tyne, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Sciences, Northumbria University, Newcastle Upon Tyne, UK"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, National University of Computer and Emerging Sciences (NUCES), Karachi, Pakistan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "COMSATS University Islamabad, Attock, Pakistan"}], "References": [{"Title": "Energy Optimization of PR-LEACH Routing Scheme Using Distance Awareness in Internet of Things Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "48", "Issue": "2", "Page": "244", "JournalTitle": "International Journal of Parallel Programming"}]}, {"ArticleId": ********, "Title": "Erratum", "Abstract": "", "Keywords": "", "DOI": "10.1002/int.22321", "PubYear": 2021, "Volume": "36", "Issue": "1", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "A tags’ arrival rate estimation method using weighted grey model(1,1) and sliding window in mobile radio frequency identification systems", "Abstract": "<p>In order to guarantee the tag identification accuracy and efficiency in mobile radio frequency identification system, it is necessary to estimate the tags’ arrival rate before performing identification. This research aims to develop a novel estimation method based on improved grey model(1,1) and sliding window mechanism. By establishing tags’ dynamic arrival model, this article emphasizes the importance of tags’ arrival rate estimation in mobile radio frequency identification system. Using sliding window mechanism and weighted coefficients method, weighted grey model(1,1) with sliding window (WGMSW(1,1)) is proposed based on traditional grey model(1,1). For experimental verification, three kinds of data are used as original data in WGMSW(1,1). The experimental results show that the proposed method has lower estimation error rate, lower computation complexity, and high system stability.</p>", "Keywords": "", "DOI": "10.1177/1550147720967894", "PubYear": 2020, "Volume": "16", "Issue": "10", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Engineering, Inner Mongolia Agriculture University, Hohhot, China;Inner Mongolia Autonomous Region Key Laboratory of Big Data Research and Application of Agriculture and Animal Husbandry, Hohhot, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Engineering, Inner Mongolia Agriculture University, Hohhot, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Engineering, Inner Mongolia Agriculture University, Hohhot, China"}], "References": [{"Title": "An Improved Maximum a Posterior-Based Estimation Method Coping with Capture Effect for RFID Tags Identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "2050011", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}]}, {"ArticleId": 84731114, "Title": "Scheduling of emergency tasks for multiservice UAVs in post-disaster scenarios", "Abstract": "Single-task UAVs are increasingly being employed to carry out surveillance, parcel delivery, communication support, and other specific tasks. When the geographical area of operation of single-task missions is common, e.g., in post-disaster recovery scenarios, it is more efficient to have multiple tasks carried out as part of a single UAV mission. In these scenarios, the UAVs’ equipment and mission plan must be carefully selected to minimize the carried load and overall resource consumption. In this paper, we investigate the joint planning of multitask missions leveraging a fleet of UAVs equipped with a standard set of accessories enabling heterogeneous tasks. To this end, an optimization problem is formulated yielding the optimal joint planning and deriving the resulting quality of the delivered tasks. In addition, two heuristic solutions are developed for large-scale environments to cope with the increased complexity of the optimization framework. The joint planning is applied to a specific scenario of a flood in the San Francisco area. Results show the effectiveness of the proposed heuristic solutions, which provide good performance and allow for drastic savings in the computational time required to plan the UAVs’ trajectories with respect to the optimal approach, thus enabling prompt reaction to the emergency events.", "Keywords": "Unmanned Aerial Vehicles ; Post-emergency monitoring ; Fleet area coverage ; Parcel delivery", "DOI": "10.1016/j.comnet.2020.107644", "PubYear": 2021, "Volume": "184", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politecnico di Torino, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CNR-IEIIT, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Politecnico di Torino, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Politecnico di Torino, Italy;CNR-IEIIT, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National and Kapodistrian University of Athens, Greece"}], "References": []}, {"ArticleId": 84731180, "Title": "Weakest Preconditions in Fibrations", "Abstract": "Weakest precondition transformers are useful tools in program verification. One of their key properties is compositionality, that is, the weakest precondition predicate transformer (wppt for short) associated to program f ; g should be equal to the composition of the wppts associated to f and g . In this paper, we study the categorical structure behind wppts from a fibrational point of view. We characterize the wppts that satisfy compositionality as the ones constructed from the Cartesian lifting of a monad. We moreover show that Cartesian liftings of monads along lax slice categories bijectively correspond to Eilenberg-Moore monotone algebras. We then instantiate our techniques by deriving wppts for commonplace effects such as the maybe monad, the non-empty powerset monad, the counter monad or the distribution monad. We also show how to combine them to derive the wppts appearing in the literature of verification of probabilistic programs.", "Keywords": "Weakest precondition predicate transformer ; Monad ; Computational effects ; Fibered category theory ; Hoare logic", "DOI": "10.1016/j.entcs.2020.09.002", "PubYear": 2020, "Volume": "352", "Issue": "", "JournalId": 9641, "JournalTitle": "Electronic Notes in Theoretical Computer Science", "ISSN": "1571-0661", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Institute of Informatics, 2-1-2 Hit<PERSON><PERSON>shi, Chiyodaku, Tokyo, Japan IMDEA Software Institute & Universidad Politécnica de Madrid, Campus de Montegancedo, 28223 Pozuelo de Alarcón, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Informatics, 2-1-2 Hit<PERSON><PERSON>shi, Chiyodaku, Tokyo, Japan IMDEA Software Institute & Universidad Politécnica de Madrid, Campus de Montegancedo, 28223 Pozuelo de Alarcón, Madrid, Spain"}], "References": [{"Title": "The next 700 relational program logics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Exequiel R<PERSON>s", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 84731218, "Title": "Parametrized Fixed Points and Their Applications to Session Types", "Abstract": "Parametrized fixed points are of particular interest to denotational semantics and are often given by “dagger operations” [<PERSON> and <PERSON><PERSON><PERSON>, Fixed-Point Operations on ccc's. Part I, Theoretical Computer Science (ISSN 0304-3975) 155 (1996), 1–38, https://doi.org/10.1016/0304-3975(95)00010-0 ; <PERSON> and <PERSON><PERSON><PERSON>, Iteration Theories. The Equational Logic of Iterative Processes, in: EATCS Monographs on Theoretical Computer Science, Springer-Verlag Berlin Heidelberg, ISBN 978-3-642-78034-9, 1993, xv+630 pp., https://doi.org/10.1007/978-3-642-78034-9 ; <PERSON> and <PERSON><PERSON><PERSON>, Some Equational Laws of Initiality in 2CCC's, International Journal of Foundations of Computer Science 6 (1995) 95–118, https://doi.org/10.1142/S0129054195000081 .]. Dagger operations that satisfy the Conway identities [<PERSON> and <PERSON>, Fixed-Point Operations on ccc's. Part I, Theoretical Computer Science (ISSN 0304-3975) 155 (1996), 1–38, doi: https://doi.org/10.1016/0304-3975(95)00010-0 .] are particularly useful, because these identities imply a large class of identities used in semantic reasoning. We generalize existing techniques to define dagger operations on ω -categories and on O -categories. These operations enjoy a 2-categorical structure that implies the Conway identities. We illustrate these operators by considering applications to the semantics of session-typed languages.", "Keywords": "O -categories ; ω -categories ; fixed points ; dagger operations ; Conway identities", "DOI": "10.1016/j.entcs.2020.09.008", "PubYear": 2020, "Volume": "352", "Issue": "", "JournalId": 9641, "JournalTitle": "Electronic Notes in Theoretical Computer Science", "ISSN": "1571-0661", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Carnegie Mellon University, Pittsburgh, PA, United States of America"}], "References": []}, {"ArticleId": 84731223, "Title": "Domain Theoretic Second-Order E<PERSON>'s Method for Solving Initial Value Problems", "Abstract": "A domain-theoretic method for solving initial value problems (IVPs) is presented, together with proofs of soundness, completeness, and some results on the algebraic complexity of the method. While the common fixed-precision interval arithmetic methods are restricted by the precision of the underlying machine architecture, domain-theoretic methods may be complete, i.e., the result may be obtained to any degree of accuracy. Furthermore, unlike methods based on interval arithmetic which require access to the syntactic representation of the vector field, domain-theoretic methods only deal with the semantics of the field, in the sense that the field is assumed to be given via finitely-representable approximations, to within any required accuracy. In contrast to the domain-theoretic first-order Euler method, the second-order method uses the local Lipschitz properties of the field. This is achieved by using a domain for Lipschitz functions, whose elements are consistent pairs that provide approximations of the field and its local Lipschitz properties. In the special case where the field is differentiable, the local Lipschitz properties are exactly the local differential properties of the field. In solving IVPs, Lipschitz continuity of the field is a common assumption, as a sufficient condition for uniqueness of the solution. While the validated methods for solving IVPs commonly impose further restrictions on the vector field, the second-order Euler method requires no further condition. In this sense, the method may be seen as the most general of its kind. To avoid complicated notations and lengthy arguments, the results of the paper are stated for the second-order Euler method. Nonetheless, the framework, and the results, may be extended to any higher-order Euler method, in a straightforward way.", "Keywords": "domain theory ; domain of Lipschitz functions ; initial value problem ; algebraic complexity ; interval arithmetic", "DOI": "10.1016/j.entcs.2020.09.006", "PubYear": 2020, "Volume": "352", "Issue": "", "JournalId": 9641, "JournalTitle": "Electronic Notes in Theoretical Computer Science", "ISSN": "1571-0661", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computing, Imperial College, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Ningbo, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Mathematical Sciences, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science, The Australian National University, Canberra, Australia"}], "References": []}, {"ArticleId": 84731255, "Title": "Research on Predictive Model of Spanish Vocabulary Pronunciation Based on Recurrent Neural Network", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2020.1010182", "PubYear": 2020, "Volume": "10", "Issue": "10", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "皎谷 赵", "Affiliation": ""}], "References": []}, {"ArticleId": 84731273, "Title": "Research on the Client Design of WeChat Scan Code Sign-In Platform in College Classroom", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2020.1010183", "PubYear": 2020, "Volume": "10", "Issue": "10", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "建芳 刘", "Affiliation": ""}], "References": []}, {"ArticleId": 84731278, "Title": "Combining Algebraic Effect Descriptions Using the Tensor of Complete Lattices", "Abstract": "Algebras can be used to interpret the behaviour of effectful programs. In particular, we use Eilenberg-Moore algebras given over a complete lattices of truth values, which specify answers to queries about programs. The algebras can be used to formulate a quantitative logic of behavioural properties, specifying a congruent notion of program equivalence coinciding with a notion of applicative bisimilarity. Many combinations of effects can be interpreted using these algebras. In this paper, we specify a method of generically combining effects and the algebras used to interpret them. At the core of this method is the tensor of complete lattices, which combines the carrier sets of the algebras. We show that this tensor preserves complete distributivity of complete lattices. Moreover, the universal properties of this tensor can then be used to properly combine the Eilenberg-Moore algebras. We will apply this method to combine the effects of probability, global store, cost, nondeterminism, and error effects. We will then compare this method of combining effects with the more traditional method of combining equational theories using interaction laws.", "Keywords": "Algebraic effects ; Eilenberg-Moore algebra ; Tree monad ; Complete lattice ; Tensor product ; Program equivalence ; Quantitative logic ; Applicative bisimilarity ; Probability ; Nondeterminism ; Global store", "DOI": "10.1016/j.entcs.2020.09.013", "PubYear": 2020, "Volume": "352", "Issue": "", "JournalId": 9641, "JournalTitle": "Electronic Notes in Theoretical Computer Science", "ISSN": "1571-0661", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Science, Tallinn University of Technology, Tallinn, Estonia"}], "References": [{"Title": "Behavioural Equivalence via Modalities for Algebraic Effects", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "From Equations to Distinctions: Two Interpretations of Effectful Computations", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "317", "Issue": "", "Page": "1", "JournalTitle": "Electronic Proceedings in Theoretical Computer Science"}]}, {"ArticleId": 84731338, "Title": "Bifibrations of Polycategories and Classical Linear Logic", "Abstract": "The main goal of this article is to expose and relate different ways of interpreting the multiplicative fragment of classical linear logic in polycategories. Polycategories are known to give rise to models of classical linear logic in so-called representable polycategories with duals, which ask for the existence of various polymaps satisfying the different universal properties needed to define tensor, par, and negation. We begin by explaining how these different universal properties can all be seen as instances of a single notion of universality of a polymap parameterised by an input or output object, which also generalises the classical notion of universal multimap in a multicategory. We then proceed to introduce a definition of in-cartesian and out-cartesian polymaps relative to a refinement system (= strict functor) of polycategories, in such a way that universal polymaps can be understood as a special case. In particular, we obtain that a polycategory is a representable polycategory with duals if and only if it is bifibred over the terminal polycategory 1 . Finally, we present a Grothendieck correspondence between bifibrations of polycategories and pseudofunctors into MAdj , the (weak) 2-polycategory of multivariable adjunctions. When restricted to bifibrations over 1 we get back the correspondence between *-autonomous categories and Frobenius pseudomonoids in MAdj that was recently observed by <PERSON><PERSON>.", "Keywords": "Polycategories ; linear logic ; bifibrations ; G<PERSON><PERSON><PERSON><PERSON> construction ; Frobenius monoids", "DOI": "10.1016/j.entcs.2020.09.003", "PubYear": 2020, "Volume": "352", "Issue": "", "JournalId": 9641, "JournalTitle": "Electronic Notes in Theoretical Computer Science", "ISSN": "1571-0661", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of Birmingham, Birmingham, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire d'Informatique de l'École Polytechnique, Palaiseau, France"}], "References": []}, {"ArticleId": 84731344, "Title": "Towards a Classification of Behavioural Equivalences in Continuous-time Markov Processes", "Abstract": "Bisimulation is a concept that captures behavioural equivalence of states in a transition system. In [<PERSON><PERSON>, <PERSON>, and <PERSON>, Bisimulation for feller-dynkin processes, in: Proceedings of the Thirty-Fifth Conference on the Mathematical Foundations of Programming Semantics, Electronic Notes in Theoretical Computer Science 347 (2019) 45–63.], we proposed two equivalent definitions of bisimulation on continuous-time stochastic processes where the evolution is a flow through time. In the present paper, we develop the theory further: we introduce different concepts that correspond to different behavioural equivalences and compare them to bisimulation. In particular, we study the relation between bisimulation and symmetry groups of the dynamics. We also provide a game interpretation for two of the behavioural equivalences. We then compare those notions to their discrete-time analogues.", "Keywords": "Stochastic Processes ; Markov Processes ; continuous time ; bisimulation ; diffusion", "DOI": "10.1016/j.entcs.2020.09.004", "PubYear": 2020, "Volume": "352", "Issue": "", "JournalId": 9641, "JournalTitle": "Electronic Notes in Theoretical Computer Science", "ISSN": "1571-0661", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, McGill University, Montreal, Canada"}, {"AuthorId": 2, "Name": "Florence Clerc", "Affiliation": "School of Computer Science, McGill University, Montreal, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, McGill University, Montreal, Canada"}], "References": []}, {"ArticleId": 84731682, "Title": "Sentiment analysis of tweets using a unified convolutional neural network‐long short‐term memory network model", "Abstract": "<p>Sentiment analysis focuses on identifying and classifying the sentiments expressed in text messages and reviews. Social networks like Twitter, Facebook, and Instagram generate heaps of data filled with sentiments, and the analysis of such data is very fruitful when trying to improve the quality of both products and services alike. Classic machine learning techniques have a limited capability to efficiently analyze such large amounts of data and produce precise results; they are thus supported by deep learning models to achieve higher accuracy. This study proposes a combination of convolutional neural network and long short‐term memory (CNN‐LSTM) deep network for performing sentiment analysis on Twitter datasets. The performance of the proposed model is analyzed with machine learning classifiers, including the support vector classifier, random forest (RF), stochastic gradient descent (SGD), logistic regression, a voting classifier (VC) of RF and SGD, and state‐of‐the‐art classifier models. Furthermore, two feature extraction methods (term frequency‐inverse document frequency and word2vec) are also investigated to determine their impact on prediction accuracy. Three datasets (US airline sentiments, women's e‐commerce clothing reviews, and hate speech) are utilized to evaluate the performance of the proposed model. Experiment results demonstrate that the CNN‐LSTM achieves higher accuracy than those of other classifiers.</p>", "Keywords": "convolutional neural networks;deep learning;long short‐term memory;sentiment analysis;text mining;Twitter data analysis", "DOI": "10.1111/coin.12415", "PubYear": 2021, "Volume": "37", "Issue": "1", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Khawaja Freed University, Punjab, Paksitan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Technology, The Islamia University of Bahawalpur, Bahawalpur, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, <PERSON><PERSON><PERSON><PERSON> University Meerut, Meerut, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Khawaja Freed University, Punjab, Paksitan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, South Korea"}], "References": [{"Title": "Aspect-based sentiment analysis using smart government review data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "20", "Issue": "1/2", "Page": "142", "JournalTitle": "Applied Computing and Informatics"}]}, {"ArticleId": 84731939, "Title": "CALCULATION OF STAGES OF THE TECHNOLOGICAL PROCESS OF MANUFACTURE OF PPD DETECTORS USING COMPUTER MATHEMATICAL MODELING AND PRODUCTION OF ALPHA RADIOMETER ON THEIR BASIS", "Abstract": "The article describes the developed radiometer for Express measurement of alpha radiation of radioactive elements based on a large-diameter silicon detector. The main element of the PPD detector is made using computer mathematical modeling of all stages of the technological process of manufacturing detectors, taking into account at each stage the degree of influence of the properties of the initial silicon on the electrophysical and radiometric characteristics of the detector. Detectors are manufactured for certain types of devices. The developed radiometer is designed to measure alpha radiation of natural isotopes (238U, 234U, 232Th, 226Ra, 222Rn, 218Po, 214Bi, etc.) in various environments. It also shows the principle of operation of the device, provides a block diagram of the measuring complex, describes the electronic components of the radiometer, as well as the block diagram. Signal transformations (spectrum transfer, filtering, accumulation) are implemented programmatically on the basis of a digital processing module. The device can detect the presence of specific elements in various environments, as well as protect people from the harmful effects of adverse radiation and can be used both in the field and stationary.", "Keywords": "silicon; semiconductor detectors; computer mathematical modeling; radiometer; alpha radiation; amplifier; microcircuit; transistor; charge-sensitive amplifier; microcontroller.", "DOI": "10.33693/2313-223X-2020-7-2-21-28", "PubYear": 2020, "Volume": "7", "Issue": "2", "JournalId": 70233, "JournalTitle": "Computational nanotechnology", "ISSN": "2313-223X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "SALI RADZHAPOV", "Affiliation": "Physical-technical Institute, SPA «Physics-Sun», Academy of Sciences of the Republic of Uzbekistan, Tashkent, Republic of Uzbekistan"}, {"AuthorId": 2, "Name": "RUSTAM RAKHIMOV", "Affiliation": "Physical-technical Institute, SPA «Physics-Sun», Academy of Sciences of the Republic of Uzbekistan"}, {"AuthorId": 3, "Name": "BEGJAN RADZHAPOV", "Affiliation": "Physical-technical Institute, SPA «Physics-Sun», Academy of Sciences of the Republic of Uzbekistan"}, {"AuthorId": 4, "Name": "MARS ZUFAROV", "Affiliation": "Physical-technical Institute, SPA «Physics-Sun», Academy of Sciences of the Republic of Uzbekistan"}], "References": []}, {"ArticleId": 84731940, "Title": "MATHEMATICAL MODELING OF OPTIMAL PARAMETERS OF ATMOSPHERIC INFLUENCE ON THE PROPERTIES OF THE SOLAR MODULE", "Abstract": "Technological factors of environmental impact on the protective structures of solar modules are considered. It is shown that the problem of extending the service life of solar converters is successfully solved by improving the technology of their opera-tion - indirect activities. It is shown that after each regular cleaning of the surface of the protective structures of the solar module can almost completely restore the working efficiency of its power output, even at high (up to 35%) level of reduction due to dust on the territory of our country within one to two days. The atmospheric impact on the protective structures of solar modules was calculated. The influence of the partial pressure of air particles and the large difference between night and day temperatures on the degree of dust contamination of the protective coatings of the solar module is analyzed. They are the main natural factors that reduce the output power and efficiency of solar energy to electricity converters. Keywords: dust and air pollution, climate parame", "Keywords": "dust and air pollution; climate parame", "DOI": "10.33693/2313-223X-2020-7-2-58-63", "PubYear": 2020, "Volume": "7", "Issue": "2", "JournalId": 70233, "JournalTitle": "Computational nanotechnology", "ISSN": "2313-223X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "ERKIN IMAMOV", "Affiliation": "Tashkent University of information technologies named after <PERSON>, Tashkent, Republic of Uzbekistan"}, {"AuthorId": 2, "Name": "RAMIZULLA MUMINOV", "Affiliation": "Physical-technical Institute, SPA “Physics-Sun”, Academy of Sciences of Uzbekistan, Tashkent, Republic of Uzbekistan"}, {"AuthorId": 3, "Name": "RUSTAM RAKHIMOV", "Affiliation": "Institute of materials science, SPA “Physics-sun”, Academy of Sciences of Uzbekistan, Tashkent, Republic of Uzbekistan"}], "References": []}, {"ArticleId": 84731941, "Title": "Neural Attentive Travel package Recommendation via exploiting long-term and short-term behaviors", "Abstract": "Travel package recommendation is a critical task in the tourism e-commerce recommender systems. Recently, an increasing number of studies proposed various travel package recommendation algorithms to improve Online Travel Agencies (OTAs) service, such as collaborative filtering-based, matrix factorization-based and neural network-based methods. Despite their value, however, the main challenges that incorporating complex descriptive information of the travel packages and capturing complicated users’ long-term preferences for fine-grained travel package recommendation are still not fully resolved. In terms of these issues, this paper propose a novel model named Neural Attentive Travel package Recommendation (NATR) for tourism e-commerce by combining users’ long-term preferences with short-term preferences. Specifically, NATR mainly contains two core modules, namely, travel package encoder and user encoder . The travel package encoder module is developed to learn a unified travel package representation by an attentive multi-view learning approach including word-level and view-level attention mechanisms. The user encoder module is designed to study long-term and short-term preference of the user by Bidirectional Long Short-Term Memory (Bi-LSTM) neural networks with package-level attention mechanism. In addition, we further adopt a gated fusion approach to coalesce these two kinds of preferences for learning high-quality the user’s representation. Extensive experiments are conducted on a real-life tourism e-commerce dataset, the results demonstrate the proposed model yields significant performance advantages over several competitive methods. Further analyses from different attention weights provide insights of attentive multi-view learning and gated fusion network, respectively.", "Keywords": "Recommender systems ; Travel recommendations ; Sequential behaviors ; Neural networks ; Personalized attention", "DOI": "10.1016/j.knosys.2020.106511", "PubYear": 2021, "Volume": "211", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Provincial Key Laboratory of E-Business, Nanjing University of Finance and Economics, Nanjing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, China;Jiangsu Provincial Key Laboratory of E-Business, Nanjing University of Finance and Economics, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Provincial Key Laboratory of E-Business, Nanjing University of Finance and Economics, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Jiangxi University of Science and Technology, Ganzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Provincial Key Laboratory of E-Business, Nanjing University of Finance and Economics, Nanjing, China"}], "References": [{"Title": "Chaotic multi-swarm whale optimizer boosted support vector machine for medical diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> Chen", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "105946", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 84731960, "Title": "Wasserstein distance to independence models", "Abstract": "An independence model for discrete random variables is a Segre-Veronese variety in a probability simplex. Any metric on the set of joint states of the random variables induces a <PERSON><PERSON><PERSON> metric on the probability simplex. The unit ball of this polyhedral norm is dual to the <PERSON><PERSON><PERSON>tz polytope. Given any data distribution, we seek to minimize its <PERSON><PERSON><PERSON> distance to a fixed independence model. The solution to this optimization problem is a piecewise algebraic function of the data. We compute this function explicitly in small instances, we study its combinatorial structure and algebraic degrees in general, and we present some experimental case studies.", "Keywords": "Algebraic statistics ; Lipschitz polytope ; Optimal transport ; Polar degrees ; Segre-Veronese variety ; Wasserstein distance", "DOI": "10.1016/j.jsc.2020.10.005", "PubYear": 2021, "Volume": "104", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "Türkü Özlüm Çelik", "Affiliation": "<PERSON>, 8888 University Drive, Burnaby, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UCLA, 520 Portola Plaza, Los Angeles, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "MPI-MiS Leipzig, Inselstr. 22, Leipzig, Germany;UCLA, 520 Portola Plaza, Los Angeles, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>ur<PERSON>", "Affiliation": "MPI-MiS Leipzig, Inselstr. 22, Leipzig, Germany;UC Berkeley, 970 Evans Hall, Berkeley, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, KTH Royal Institute of Technology, Stockholm, Lindstedtsvägen 25, Stockholm, Sweden"}], "References": []}, {"ArticleId": 84731963, "Title": "The emancipatory potential of digital entrepreneurship: A study of financial technology-driven inclusive growth", "Abstract": "Digital entrepreneurship possesses immense societal implications beyond its commercial significance. Yet our knowledge of the emancipatory potential of digital entrepreneurship remains limited because few studies have gone beyond the conventional emphasis on profits and wealth creation. Drawing on the emancipatory perspective that views entrepreneurship as change creation through the removal of constraints, this article examines how emancipation can occur through the actions of digital entrepreneurs. Using an empirical investigation of entrepreneurial endeavours set against disadvantaged communities in Indonesia, we uncover constraints facing a developing economy and the role of digital technologies in ameliorating them. Through extensive fieldwork and in-depth case study analyses, we identify constraining societal norms and restrictive practices, as well as the three forms of digital enablement - to emulate services, aggregate capital and equalise opportunities – necessary for the enactment of digitally enabled emancipation. We present a framework to illustrate the enactment of emancipatory digital entrepreneurship for the inclusive development of businesses and communities.", "Keywords": "Digital entrepreneurship ; Emancipation ; Fintech ; Case study", "DOI": "10.1016/j.im.2020.103384", "PubYear": 2022, "Volume": "59", "Issue": "3", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "UNSW Business School, UNSW Sydney, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "UNSW Business School, UNSW Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Sydney Business School, The University of Sydney, Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Economics University of Indonesia, Indonesia"}], "References": []}, {"ArticleId": 84731978, "Title": "COMPUTATIONAL AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> METHOD FOR DETERMINING THE STRUCTURAL APPEARANCE OF TRANSMISSION SHAFTS MADE USING COMPOSITE MATERIALS", "Abstract": "This computational and experimental work is dedicated to the development of promising designs of vehicle drive shafts made of polymer composite materials. The paper analyzes the existing models of drive shafts vehicles and substantiates the use of a carbon-fiber drive shaft with titanium tips. A manufacturing technology for such a product is also presented. Evaluation of structure performance under the action of ultimate loads was carried out by the finite element method considering anisotropic properties of reinforced materials. Prototypes of composite drive shafts were produced for further laboratory tests. According to the tests results, the drive shafts withstands a greater torsional moment than the calculated one. As a result, it was decided to install them on a racing vehicle for carrying out field tests. During the field tests of the composite drive shaft, the mounting structure between titanium tip and composite tube was destroyed. During the full-scale tests there were no loads exceeding 60% of the calculated ones, therefore, the main cause of structural failure is fatigue failure. The next stage of the research was the development of a design that is resistant to long-term cyclic loads and has sufficient reliability. This design was designed, manufactured and tested in the framework of field tests.", "Keywords": "composite materials; drive shafts; finite element analysis; racing car.", "DOI": "10.33693/2313-223X-2020-7-2-71-78", "PubYear": 2020, "Volume": "7", "Issue": "2", "JournalId": 70233, "JournalTitle": "Computational nanotechnology", "ISSN": "2313-223X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "VALENTIN EREMIN", "Affiliation": "Moscow Aviation Institute (National Research University), Moscow, Russian Federation"}, {"AuthorId": 2, "Name": "ALEXANDER BOLSHIKH", "Affiliation": "Moscow Aviation Institute (National Research University)"}, {"AuthorId": 3, "Name": "MAKSIM SHKURIN", "Affiliation": "Moscow Aviation Institute (National Research University)"}], "References": []}, {"ArticleId": 84731988, "Title": "Business process variant analysis: Survey and classification", "Abstract": "It is common for business processes to exhibit a high degree of internal heterogeneity, in the sense that the executions of the process differ widely from each other due to contextual factors, human factors, or deliberate business decisions. For example, a quote-to-cash process in a multinational company is typically executed differently across different countries or even across different regions in the same country. Similarly, an insurance claims handling process might be executed differently across different claims handling centers or across multiple teams within the same claims handling center. A subset of executions of a business process that can be distinguished from others based on a given predicate (e.g. the executions of a process in a given country) is called a process variant. Understanding differences between process variants helps analysts and managers to make informed decisions as to how to standardize or otherwise improve a business process, for example by helping them find out what makes it that a given variant exhibits a higher performance than another one. Process variant analysis is a family of techniques to analyze event logs produced during the execution of a process, in order to identify and explain the differences between two or more process variants. A wide range of methods for process variant analysis have been proposed in the past decade. However, due to the interdisciplinary nature of this field, the proposed methods and the types of differences they can identify vary widely, and there is a lack of a unifying view of the field. To close this gap, this article presents a systematic literature review of methods for process variant analysis. The identified studies are classified according to their inputs, outputs, analysis purpose, underpinning algorithms, and extra-functional characteristics. The paper closes with a broad classification of approaches into three categories based on the paradigm they employ to compare multiple process variants.", "Keywords": "Process mining ; Machine learning ; Business process management", "DOI": "10.1016/j.knosys.2020.106557", "PubYear": 2021, "Volume": "211", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Melbourne, Melbourne, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Melbourne, Melbourne, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tartu, Tartu, Estonia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Free University of Bozen-Bolzano, Bolzano, Italy"}], "References": []}, {"ArticleId": 84731991, "Title": "COMBINAT<PERSON><PERSON><PERSON> POLYN<PERSON><PERSON>LLY COMPUTABLE CHARACTERISTICS OF SUBSTITUTIONS AND THEIR PROPERTIES", "Abstract": "The construction and selection of a suitable bijective function, that is, substitution, is now becoming an important applied task, particularly for building block encryption systems. Many articles have suggested using different approaches to determining the quality of substitution, but most of them are highly computationally complex. The solution of this problem will significantly expand the range of methods for constructing and analyzing scheme in information protection systems. The purpose of research is to find easily measurable characteristics of substitutions, allowing to evaluate their quality, and also measures of the proximity of a particular substitutions to a random one, or its distance from it. For this purpose, several characteristics were proposed in this work: difference and polynomial, and their mathematical expectation was found, as well as variance for the difference characteristic. This allows us to make a conclusion about its quality by comparing the result of calculating the characteristic for a particular substitution with the calculated mathematical expectation. From a computational point of view, the thesises of the article are of exceptional interest due to the simplicity of the algorithm for quantifying the quality of bijective function substitutions. By its nature, the operation of calculating the difference characteristic carries out a simple summation of integer terms in a fixed and small range. Such an operation, both in the modern and in the prospective element base, is embedded in the logic of a wide range of functional elements, especially when implementing computational actions in the optical range, or on other carriers related to the field of nanotechnology.", "Keywords": "substitution; S-<PERSON>; Kuznechik; Bel<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>.", "DOI": "10.33693/2313-223X-2020-7-2-34-41", "PubYear": 2020, "Volume": "7", "Issue": "2", "JournalId": 70233, "JournalTitle": "Computational nanotechnology", "ISSN": "2313-223X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "VLADIMIR NIKONOV", "Affiliation": "Secure Information Technology Assistance Foundation, Moscow, Russian Federation"}, {"AuthorId": 2, "Name": "ANTON ZOBOV", "Affiliation": "Russian Academy of Natural Sciences, Moscow, Russian Federation"}], "References": []}, {"ArticleId": 84732052, "Title": "ECMarker: interpretable machine learning model identifies gene expression biomarkers predicting clinical outcomes and reveals molecular mechanisms of human disease in early stages", "Abstract": "Motivation <p>Gene expression and regulation, a key molecular mechanism driving human disease development, remains elusive, especially at early stages. Integrating the increasing amount of population-level genomic data and understanding gene regulatory mechanisms in disease development are still challenging. Machine learning has emerged to solve this, but many machine learning methods were typically limited to building an accurate prediction model as a ‘black box’, barely providing biological and clinical interpretability from the box.</p> Results <p>To address these challenges, we developed an interpretable and scalable machine learning model, ECMarker, to predict gene expression biomarkers for disease phenotypes and simultaneously reveal underlying regulatory mechanisms. Particularly, ECMarker is built on the integration of semi- and discriminative-restricted Boltzmann machines, a neural network model for classification allowing lateral connections at the input gene layer. This interpretable model is scalable without needing any prior feature selection and enables directly modeling and prioritizing genes and revealing potential gene networks (from lateral connections) for the phenotypes. With application to the gene expression data of non-small-cell lung cancer patients, we found that ECMarker not only achieved a relatively high accuracy for predicting cancer stages but also identified the biomarker genes and gene networks implying the regulatory mechanisms in the lung cancer development. In addition, ECMarker demonstrates clinical interpretability as its prioritized biomarker genes can predict survival rates of early lung cancer patients (P-value &lt; 0.005). Finally, we identified a number of drugs currently in clinical use for late stages or other cancers with effects on these early lung cancer biomarkers, suggesting potential novel candidates on early cancer medicine.</p> Availabilityand implementation <p>ECMarker is open source as a general-purpose tool at https://github.com/daifengwanglab/ECMarker.</p> Contact <p><EMAIL></p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btaa935", "PubYear": 2021, "Volume": "37", "Issue": "8", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biostatistics and Medical Informatics, University of Wisconsin – Madison, Madison, WI 53706, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Stony Brook University, Stony Brook, NY 11794, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departments of Pathology and Urology;Stony Brook Cancer Center, Stony Brook Medicine, Stony Brook, NY 11794, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Department of Biostatistics and Medical Informatics, University of Wisconsin – Madison, Madison, WI 53706, USA;Waisman Center, University of Wisconsin – Madison, Madison, WI 53705, USA"}], "References": []}, {"ArticleId": 84732165, "Title": "Fixed point theorem by altering distance technique in complete fuzzy metric spaces", "Abstract": "The aim of this paper is to define the generalised altering distance function and to extend the Banach contraction principal in complete fuzzy metric spaces using altering distance. The f-mapping also plays an important role to find fixed point. Our result extends the result of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2013) in fuzzy metric space. © 2020 Inderscience Enterprises Ltd.. All rights reserved.", "Keywords": "Altering distance.; Control function; Fuzzy metric space", "DOI": "10.1504/IJCAET.2020.110479", "PubYear": 2020, "Volume": "13", "Issue": "4", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, <PERSON><PERSON><PERSON>, Mullana, Ambala, Haryana, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Bundelkhand University, Jhansi, UP, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, <PERSON><PERSON><PERSON>, Mullana, Ambala, Haryana, India"}], "References": []}, {"ArticleId": 84732166, "Title": "Two generalised fixed point theorems in G-metric space without iterations", "Abstract": "Two generalised fixed point theorems are proved using the well-known infimum property of real numbers without an appeal to the iterative procedure. © 2020 Inderscience Enterprises Ltd.. All rights reserved.", "Keywords": "Fixed point; G-Cauchy sequence; G-contractive fixed point.; G-metric space; The infimum property", "DOI": "10.1504/IJCAET.2020.110485", "PubYear": 2020, "Volume": "13", "Issue": "4", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Advanced Sciences, Vellore Institute of Technology, Vellore-632014, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Advanced Sciences, Vellore Institute of Technology, Vellore-632014, Tamil Nadu, India"}], "References": []}, {"ArticleId": 84732167, "Title": "Multi-response optimisation in CNC turning of Al-6082 T6 using grey Taguchi method coupled with principal component analysis", "Abstract": "The present work focuses to analyse the importance of turning parameters on the responses: machining time, surface roughness and material removal rate in CNC turning while machining of aluminium alloy Al-6082 T6 using tungsten carbide tool. Cutting speed, feed rate and depth of cut with three levels each have been considered in the current work as the machining parameters. The present study uses the Taguchi's DOE methodology, grey relational approach and principal component analysis (PCA) to optimise the response parameters simultaneously. Experiments have been conducted as per Taguchi's L9 orthogonal array. The experimental results were then analysed using the grey relational analysis (GRA) along with the principal component analysis (PCA). The speed and feed are observed to be statistically significant on the responses whereas the depth of cut is insignificant. Optimal levels for the parameters are determined using the grey Taguchi method and PCA. The confirmation test is carried out and the results are validated. © 2020 Inderscience Enterprises Ltd.. All rights reserved.", "Keywords": "Grey Taguchi; Machining time; Material removal rate; MRR; Multi-response optimisation.; Surface roughness; Turning", "DOI": "10.1504/IJCAET.2020.110487", "PubYear": 2020, "Volume": "13", "Issue": "4", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, G P<PERSON><PERSON> Reddy Engineering College (Autonomous), Kurnool, AP  518007, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, G P<PERSON><PERSON> Reddy Engineering College (Autonomous), Kurnool, AP  518007, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, G P<PERSON><PERSON> Reddy Engineering College (Autonomous), Kurnool, AP  518007, India"}], "References": []}, {"ArticleId": 84732168, "Title": "Inverse kinematic analysis of 5-axis hybrid parallel kinematic machine using CAD and regression analysis approach", "Abstract": "Since three decades for their potentially desirable fast dynamic performance, rigidity, and acceptable accuracy parallel kinematic machines (PKM) attracted interest from industry and academia in the machine tool/robot sectors. PKMs are highly used for their higher accuracy as it relies on system stiffness and rigidity. In PKM, the inverse kinematic analysis for finding the velocity and acceleration of a limb having more than two degree of freedom (DOF) manually is tedious. Also, generation of transformation matrix is too complex. In present work, six degrees of freedom 5-Axis hybrid parallel kinematic machine (HPKM) with hemisphere workspace has been modelled and assembled in CATIA. Secondly, inverse kinematic analysis of PKM was carried out in digital mockup unit (DMU), CATIA. The velocities and accelerations of all the three limbs at three different feed rates and variations in joint angles were found. On the other hand, the regression equations were generated for velocity and acceleration of three limbs, joint angles with respect to position and time, while the tool travels along the semi circular contour trajectory. © 2020 Inderscience Enterprises Ltd.. All rights reserved.", "Keywords": "5-Axis HPKM; Contour trajectory; Digital mockup unit; DMU; Inverse kinematics; Regression analysis.", "DOI": "10.1504/IJCAET.2020.110486", "PubYear": 2020, "Volume": "13", "Issue": "4", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Vignan's Institute of Engineering for Women, Visakhapatnam, AP, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Jntuk, Kakinada, AP, India"}], "References": []}, {"ArticleId": 84732366, "Title": "Mapping thins to identify active forest management in southern pine plantations using Landsat time series stacks", "Abstract": "The southeastern United States is unique in terms of both the intensity and scale of forest management, which includes substantial thinning and other forms of harvesting. Because thinning is not a land use transition, and the disturbance signal is relatively subtle compared to a clear cut, there is a dearth of studies that attempt to detect thinning over large areas. Our goal was to detect pine thins as an indicator of active forest management using Landsat data. Areas which undergo thinning are indicative of active forest management in the region. Our approach uses a machine learning method which combines first-order harmonics and metrics from 3-year Fourier regression of Landsat time series stacks, layers from the Global Forest Change product, and other vetted national products into a random forests model to classify forest thins in the southeastern US. Forest Harvest Inspection Records for Virginia were used for training and validation. Models were successful separating thins from clear cuts and non-harvested pines (overall accuracy 86%, clear cut accuracy 90%, thin accuracy 83% for a simplified 10-predictor variable model). Examination of variable importance illustrates the physical meaning behind the models. The curve fit statistics (R<sup>2</sup> or RMSE) of the NDVI, Pan, and SWIR1 harmonic curve fits, which are an indication of a departure from typical vegetation phenology caused by thinning or other disturbances, were consistently among the top predictors. The harmonic regression constant, sine and cosine from the Landsat 8 panchromatic band were also important. These describe the visible reflectance (500–680 nm) phenology over the time period at a high spatial resolution (15 m). The Loss Year from the Global Forest Change product, which is an indication of stand replacing disturbance, was also consistently among the most important variables in the classifiers. High performance computing, such as Google Earth Engine, and analysis-ready data are important for this approach. This work has importance for quantification of actively managed forests in a region of the world where production forestry is the dominant land disturbance signal and a significant economic engine.", "Keywords": "Multitemporal ; Harmonic regression ; Silviculture ; Forest management monitoring ; Mitigation ; Land cover ; Land management", "DOI": "10.1016/j.rse.2020.112127", "PubYear": 2021, "Volume": "252", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Forest Resources and Environmental Conservation, Virginia Tech, Blacksburg, VA 24061, United States of America;Corresponding author at: 310 West Campus Drive, Room 307A Cheatham Hall, Department of Forest Resources and Environmental Conservation, Virginia Tech, Blacksburg, Virginia 24061, United States of America"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Forest Resources and Environmental Conservation, Virginia Tech, Blacksburg, VA 24061, United States of America"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Conservation Management Institute, Department of Fish and Wildlife Conservation, Virginia Tech, Blacksburg, VA 24061, United States of America"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Forest Resources and Environmental Conservation, Virginia Tech, Blacksburg, VA 24061, United States of America"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Forest Resources and Environmental Conservation, Virginia Tech, Blacksburg, VA 24061, United States of America"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Forest Resources and Environmental Conservation, Virginia Tech, Blacksburg, VA 24061, United States of America"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "International Paper, Inc., 6400 Poplar Ave, Memphis, TN 38197, United States of America"}], "References": []}, {"ArticleId": 84732374, "Title": "A location-aware power control mechanism for interference mitigation in M2M communications over cellular networks", "Abstract": "Machine-to-Machine (M2M) communication is considered as a promising technology in fifth-generation (5G) cellular networks to reduce the power consumption and improve energy efficiency. To achieve these benefits, identification of unknown machine location is crucial for mitigating interference. Therefore, this paper proposes a location-aware communication mode selection based power control mechanism for reducing interference in M2M communications over cellular networks. The proposed mechanism depends on the threshold distance while communicating among M2M pair. Consequently, user equipment (UE) selects the proper communication mode in the proposed mechanism. Resource allocation between the M2M UEs (MUEs) and cellular UEs (CUEs) that share the resources in both orthogonal and non-orthogonal ways are analyzed for different modes. A water filling algorithm and Lagrange decomposition scheme based power optimization process are adopted for allocating the proper optimal transmit power to each UE in the proposed mechanism. Simulation results are compared with the non-orthogonal and orthogonal resource sharing scheme based proposed mechanism which shows that the orthogonal resource sharing scheme provides better system performance in the proposed network.", "Keywords": "Machine-to-Machine (M2M) Communications ; Mode selection ; Power control ; Interference mitigation ; Energy efficiency", "DOI": "10.1016/j.compeleceng.2020.106867", "PubYear": 2020, "Volume": "88", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Communication Engineering, Military Institute of Science and Technology (MIST), Mirpur Cantonment, Dhaka 1216, Bangladesh;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Bangladesh University of Engineering and Technology (BUET), Dhaka 1205, Bangladesh"}], "References": []}, {"ArticleId": 84732391, "Title": "Mobile Game Based Chemical Bond Learning Application", "Abstract": "<p><p>Mobile game technology is growing rapidly and this is directly proportional to the increasing number of mobile games in circulation. The growing market of the mobile game industry is not accompanied by the number of games that have educative content in them. The challenging, fun, and addictive nature of the game has a negative impact if no educational content is provided. Therefore, educational content needs to be present in a game so that the game can have a positive impact on the players. This research makes a chemical educational game to study chemical bonding material that aims as an interactive learning media for students and the general public. This game is made based on mobile with 2D characters consisting of 5 levels. Level 1 contains material for salt compounds, level 2 contains material for acid compounds, level 3, 4 and 5 contains material for basic compounds. The results of this game trial show that all the features of this game application run well, can be run on several smartphones, and most users from 20 audiences stated that this game is comfortable and easy to use.</p></p>", "Keywords": "Chemical Bond, Learning, Game, Mobile, 2D Characters", "DOI": "10.25273/doubleclick.v3i2.5840", "PubYear": 2020, "Volume": "3", "Issue": "2", "JournalId": 55840, "JournalTitle": "DOUBLECLICK: Journal of Computer and Information Technology", "ISSN": "", "EISSN": "2579-5317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 2, "Name": "Rizky <PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Elektronika Negeri Surabaya"}], "References": []}, {"ArticleId": 84732393, "Title": "Sistem Informasi Geografis Layanan Publik Lingkup Kota Makassar Berbasis Web", "Abstract": "<p>Perkembangan Sistem Informasi Geografis saat ini terus berkembang seiring kemajuan teknologi dan informasi khususnya dalam bidang informasi letak dan kondisi suatu wilayah. Sistem Informasi Geografis dibutuhkan untuk mengetahui berbagai informasi umum mengenai suatu lokasi. Pada penelitian ini, Sistem Informasi Geografis dibuat dengan tujuan membantu masyarakat untuk mengetahui layanan publik apa saja yang ada di lingkup kota Makassar.  Perancangan dilakukan dengan menggunakan metode <em>Linear Sequential Model</em> (atau disebut juga “<em>Classic Life Cycle</em>” atau “<em>Waterfall Model</em>”) yang merupakan metode pengembangan perangkat lunak dengan pendekatan sekuensial dengan cakupan aktivitas Rekayasa Sistem dan Analisis, <PERSON><PERSON><PERSON><PERSON> Luna<PERSON>, Perancangan (DeGISn), Pembuatan Coding, Pen<PERSON><PERSON><PERSON> (<em>Testing</em>) dan <PERSON> (<em>Maintenance</em>). Aplikasi yang dibuat merupakan aplikasi berbasis web dengan menggunakan Bahasa program PHP. Hasil dari penelitian ini yaitu adanya aplikasi system informasi geografis layanan masyarakat lingkup kota Makassar berbasis website yang dapat mempermudah masyarakat dalam menemukan kantor-kantor pelayanan publik di kota Makassar.</p>", "Keywords": "Sistem Informasi Geografis;<PERSON><PERSON><PERSON>;Makassar;Website", "DOI": "10.25273/doubleclick.v4i1.6073", "PubYear": 2020, "Volume": "4", "Issue": "1", "JournalId": 55840, "JournalTitle": "DOUBLECLICK: Journal of Computer and Information Technology", "ISSN": "", "EISSN": "2579-5317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "STMIK AKBA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "STMIK AKBA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "STMIK AKBA"}], "References": []}, {"ArticleId": 84732420, "Title": "KLASIFIKASI CITRA DIGITAL RETINA PENDERITA DIABETES RETINOPATI MENGGUNAKAN METODE EUCLIDEAN", "Abstract": "Retinopati diabetes merupakan gangguan yang terjadi pada mata, yang disebabkan oleh penyakit diabetes. Diagnosa Retinopati Diabetes dapat dilakukan menggunakan citra digital retina. Pada penelitian ini dilakukan klasifikasi dan analisa tingkat keparahan Retinopati Diabetes berdasarkan ciri atau fitur gambar retina. Terdapat 2 dataset yang digunakan. Dataset pertama merupakan hasil dari preprocessing gambar yaitu konversi gambar ke <em>grayscale</em>. Dataset kedua didapat dengan menghapus optikal disk pada gambar dataset pertama. Metode ekstraksi ciri yang digunakan pada penelitian ini adalah ekstraksi ciri tekstur metode statistik orde pertama menggunakan histogram. Ciri gambar yang didapatkan adalah <em>mean, variance, skewness, curtosis, </em>dan <em>entropy</em>. Klasifikasi dilakukan sebanyak 2 kali pada dataset pertama dan kedua menggunakan metode klasifikasi jarak euclidean. Pengujian dilakukan menggunakan confusion matrix dengan menghitung nilai akurasi, presisi, dan recall. Berdasarkan hasil perngujian didapatkan bahwa dataset kedua memiliki nilai akurasi yang lebih tinggi yaitu 64,81%, nilai precision 69,3%, dan recall sebesar 64,8%", "Keywords": "", "DOI": "10.25273/doubleclick.v3i2.5796", "PubYear": 2020, "Volume": "3", "Issue": "2", "JournalId": 55840, "JournalTitle": "DOUBLECLICK: Journal of Computer and Information Technology", "ISSN": "", "EISSN": "2579-5317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84732422, "Title": "Implementasi Convolutional Neural Network Untuk Klasifikasi Varietas Pada Citra Daun Sawi Menggunakan Keras", "Abstract": "<p align=\"center\"><strong>Abstra</strong><strong>k</strong></p><p align=\"center\"><strong> </strong></p><p>Perkembangan pertanian di Indonesia membuat Indonesia memiliki banyak varietas sayuran salah satunya sayuran yang banyak diminasti dikalangan masyarakat yaitu sayur sawi. Tapi sayangnya banyak masyarakat kita yang masih sulit untuk membedakan atau mengklasifikasi berbagai macam sawi. Hal ini akan menjadi masalah karena ketidaktahuan jika terus berlanjut maka akan tidak ada yang melestarikan. Permasalahan ini bisa diatasi dengan menggunakan metode deep learning salah satunya yaitu metode convolutional neural network untuk mengklasifikasi citra gambar sawi yang mampu melakukan proses pembelajaran secara sendiri dalam pengenalan sebuah gambar. Penelitian ini menghasilkan data uji coba dari klasifikasi citra pada sayuran varietas sawi yaitu sawi pakcoy, sawi putih, sawi dan sawi caisim menghasilkan nilai akurasi sebesar 83%, recall 80 % dan presisi 89%. Ketika data training dilakukan perbedaan dengan jumlah perbandingannya maka hasil yang didapatkan adalah sama yaitu 83%</p><p> </p><p><strong>Kata Kunci </strong>— Klasifikasi, Sawi, CNN</p><p><em> </em></p>", "Keywords": "jurnal teknik informatika;Journal of Computer and Information Technology;jurnal sistem informasi;journal of information system", "DOI": "10.25273/doubleclick.v4i1.5812", "PubYear": 2020, "Volume": "4", "Issue": "1", "JournalId": 55840, "JournalTitle": "DOUBLECLICK: Journal of Computer and Information Technology", "ISSN": "", "EISSN": "2579-5317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas AMIKOM Yogyakarta"}], "References": []}, {"ArticleId": 84732444, "Title": "<PERSON><PERSON><PERSON> Aktivitas Ilegal Didalam Jaringan Dengan Wireshark", "Abstract": "<p><PERSON><PERSON><PERSON> kemanan jaringan computer adalah satu hal yang mutlak dalam membangun suatu jaringan. Pada dasarnya sistem keamanan yang dimiliki oleh sistem operasi tidaklah cukup untuk mengamankan jaringan komputer. <PERSON><PERSON> karena itu untuk mendapatkan sebuah keamanan jaringan computer  maka diperlukan suatu tools yang dapat mendeteksi adanya suatu mekanisme serangan dari jaringan. <PERSON><PERSON> serangan yang terjadi bisa <em>flooding</em> ataupun <em>syn flood</em>. Dimana tujuan serangan ini adalah untuk membuat komputer yang mengakses tidak bisa berjalan dengan normal jaringan komputer. Wireshark merupakan software yang dapat menganalisa aktivitas jaringan komputer sehingga dapat membantu mendeteksi serangan yang akan terjadi sehingga pengguna tidak perlu khawatir dengan serangan tersebut.</p>", "Keywords": "<PERSON><PERSON>: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "DOI": "10.25273/doubleclick.v4i1.5668", "PubYear": 2020, "Volume": "4", "Issue": "1", "JournalId": 55840, "JournalTitle": "DOUBLECLICK: Journal of Computer and Information Technology", "ISSN": "", "EISSN": "2579-5317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Indraprasta PGRI"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas Indraprasta PGRI"}], "References": []}, {"ArticleId": 84732495, "Title": "AN ANALYTICAL STUDY ON DIRECTIONS AND PRO<PERSON>LE MATCHING FOR CARPOOL SYSTEM", "Abstract": "These days, numbers of citizens are exploring different approaches to make  better effective use of existing resources. In country like India, there are nearly about one to two people for every car and the number of person in an average car is nearly five. As a result of this, it is clearly understandable that the car driving has great potential for competence in this regard, on the basis of sharing of personal vehicles. Plenty of time and energy is saved by Carpooling, from a public and environmental point of view, but most importantly it reduces air pollution. Carpooling can share the location of each car between people with similar path or route. Carpooling evolved as an economical and anxiety-free system for distribution. In this analytical study paper, presentation of information is made about an overview of computer-based platforms that improve sustainability. In particular, this discussion will help car users choose the basis of transportation solutions for their natural footprint based on their needs, preferences and location. The success rate of this automotive system is based on the relative routes that directly connect traditional points and destinations to the most involved passengers and increase the integer of association from single to double, requiring change in two successive passengers. This analytical study paper reviews from the artistic nature of the modern carpooling method. It identifies the most important challenges in adopting modern carpooling system and the anticipated results to the same identified problems.", "Keywords": "Carpooling, Genetic algorithm, CLACSOON, AICS, HTTP, Mobile Client", "DOI": "10.26483/ijarcs.v11i5.6653", "PubYear": 2020, "Volume": "11", "Issue": "5", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Scholar, SOCA, SSSUTMS, Sehore, M.P., India"}], "References": []}, {"ArticleId": 84732501, "Title": "MODEL-BASED COURSE DESIGN FOR EFFECTIVE BLENDED LEARNING APPROACHES", "Abstract": "Blended learning has come off, more or less, as the most logical and natural evolution of our learning systems. It suggests an elegant solution to the challenges of tailoring e-learning environment to face-to-face learning environment. It represents an opportunity to integrate the innovative and technological advances offered by online learning with the interaction and participation offered in the best of traditional learning. It can be supported and enhanced by using the wisdom and one-to-one interaction of personal coaches. Believe it or not, the relevance of the pedagogy used in relaying knowledge cannot be over-emphasised. Also, it has long been recognized that specialized delivery technologies can provide efficient and timely access to learning materials. A teacher handling face-to-face students might be able to discern when they are getting the message by their countenances or when they don’t seem to by their reactions. How would an e-learning teacher meet the needs of their students psychologically, morally, socially and even academically since they cannot see each other? That brought the needs for the blended knowledge of ensuring that there is a predefined method of transferring idea to online students in such a way that the needs of these students are met. This is to fill such gap. This research considered the following four stages as paramount to the development and practice of blended e-learning which was adapted from IBM 4-tier: learning from information, learning from interaction, learning from collaboration, and learning from collocation.", "Keywords": "pedagogy;e-learning;online learning;blended learning", "DOI": "10.26483/ijarcs.v11i5.6645", "PubYear": 2020, "Volume": "11", "Issue": "5", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department Babcock University, Ilisan Remo, Ogun State,  Nigeria"}], "References": []}]