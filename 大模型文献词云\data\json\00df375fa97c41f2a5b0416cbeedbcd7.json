[{"ArticleId": 105362831, "Title": "A Roadmap for Quality of the Digital Human Model in the Textile and Apparel Industry enabled by Digital Transformation", "Abstract": "Digital transformation and the paradigm shift towards the virtual reality applications have altered the products in different sectors, such as textile and apparel industry. Development of the digital human models enables the industry to innovate the traditional process of planning, production and sales. The use of digital technology based on the body measurement data requires the anthropometric properties as well as the ergonomic assessment of realistic dynamic motions to create complex movements and fitting garments. This paper lays a roadmap to create a digital human model library by using the 3D body scanner and optimization algorithm to ensure the quality of the processes.", "Keywords": "Digital transformation ; Virtual Reality ; Digital human model ; 3D scanner ; Quality ; Digital Quality Measurement", "DOI": "10.1016/j.ifacol.2022.12.043", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Gökcen Bas", "Affiliation": "Vienna University of Technology, Institute of Production Engineering and Photonic Technologies, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Vienna University of Technology, Institute of Production Engineering and Photonic Technologies, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Vienna University of Technology, Institute of Production Engineering and Photonic Technologies, Austria"}], "References": []}, {"ArticleId": 105362834, "Title": "Women in STEM in Paraguay", "Abstract": "The purpose of this paper is to examine the current situation of women in science, technology, engineering and mathematics (STEM) fields in Paraguay using enrollment data from the largest university of the country. The paper also presents data from the National Researcher Incentive Program which was implemented in 2011. Results show that the situation of Paraguayan women regarding their choice of career and field of study is not dissimilar from other countries. With the exception of medical and health-related fields, there remains a significant gender gap in STEM careers. Socio-cultural factors such as society&#x27;s expectations for women, child bearing, family responsibilities and other traditional gender-stereotyping issues play a decisive role in the choice of women&#x27;s careers.", "Keywords": "women researchers ; diversity in the sciences ; enrollment data ; Paraguay", "DOI": "10.1016/j.ifacol.2022.12.055", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technical University of Sofia, Bulgaria"}, {"AuthorId": 2, "Name": "Valentina <PERSON>", "Affiliation": "University National de Asuncion, Paraguay"}, {"AuthorId": 3, "Name": "Larizza Delorme", "Affiliation": "University National de Asuncion, Paraguay"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Science Foundation, United States of America"}], "References": [{"Title": "The State of Play in Diversity and Inclusion in STEM–A Review of Empirical Evidence, Focusing on Gender", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "570", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105362844, "Title": "Virtual environment can ease the challenges of LGBTQ+ older adults – literature review", "Abstract": "LGBTQ+ older adults make up a unique and growing subgroup of the ageing population and are a very heterogeneous group with specific needs. The purpose of the literature review is to determine what needs and challenges LGBTQ+ older adults face and what their quality of life is. The integrative review method of scientific literature in English was used. The selection of articles was made according to the following inclusion criteria: accessibility, scientificity, content relevance and, topicality. After the selection, a qualitative content analysis was applied to the data, from which we gained an insight into the needs and challenges of LGBTQ+ older adults. Three content categories were identified: health status, discrimination and stigma towards LGBTQ+ older adults, and staff working with LGBTQ+ older adults. Research on LGBTQ+ older adults has been in the spotlight for the past few years, mostly in the United States of America. A dedicated virtual space and/or online community can overcome some of the barriers that LGBTQ+ older adults face, reducing social isolation and feelings of loneliness while providing a sense of community.", "Keywords": "sexual orientation ; discrimination ; needs ; health condition ; virtual environment", "DOI": "10.1016/j.ifacol.2022.12.026", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Alma Mater Europaea - ECM, Slovenska ulica 17, 2000 Maribor, Slovenia"}], "References": [{"Title": "Ambient Assisted Living in Lifetime Neighbourhoods", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "16896", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Smart Silver Villages as part of Social Infrastructure for Older Adults in Rural Areas", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "16914", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Social Infrastructure supporting Ambient Assisted Living in a Smart Silver City: Literature Review and Research Agenda", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "942", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Smart Age-Friendly Environments", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "768", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "The use of ICT in older adults strengthens their social network and reduces social isolation: Literature Review and Research Agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "645", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Planning Digital Transformation of Care in Rural Areas", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "750", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105362845, "Title": "Using incomplete polynomial functions of the odd degree n and their inverses for data encryption and decryption", "Abstract": "The need and the demand to hide the content of written messages from prying eyes has arisen as long as writing itself has existed. Particularly, recent times the protection of information is considered to be quite crucial due to the increased use and development of streaming applications. Therefore, the urging of having more sophisticated, stronger and hard to break data encryption and decryption systems is increasing. In order to attain these requirements, cryptography plays an important role, where many researchers have come up with different proposals and developed algorithms that have helped out a little in ensuring the confidentiality, integrity and authentication of the given information. However, even the internet security through modern cryptography is quite complex and depends on the difficulty of certain computational problems in mathematics. Modern systems of encryption are based on complex mathematical algorithms and carry out a combination of symmetric and asymmetric key encryption schemes to secure communication. For those a significant background in algebra, number theory and geometry is required. In this paper a new cryptosystem is presented, which is based on compound commutative functions such as exponential and logarithmic functions. After the necessary theoretical deliberation in this paper, we will provide the encryption and decryption approach accompanied with relevant examples.", "Keywords": "Encryption ; decryption ; exponential functions ; logarithmic functions ; compound commutative functions", "DOI": "10.1016/j.ifacol.2022.12.075", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UBT-Higher Education Institution"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UBT-Higher Education Institution"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "UBT-Higher Education Institution"}], "References": []}, {"ArticleId": 105362848, "Title": "Exploitation of exponential and logarithmic functions for data encryption and decryption", "Abstract": "The need and the demand to hide the content of written messages from prying eyes has arisen as long as writing itself has existed. Particularly, in recent times the protection of information is considered to be quite crucial due to the increased use and development of streaming applications. Therefore, the urge to have more sophisticated, stronger and hard to break data encryption and decryption systems is increasing. In order to attain these requirements, cryptography plays an important role, where many researchers have come up with different proposals and developed algorithms that have helped out a little in ensuring the confidentiality, integrity and authentication of the given information. However, even the internet security through modern cryptography is quite complex and depends on the difficulty of certain computational problems in mathematics. Modern systems of encryption are based on complex mathematical algorithms and carry out a combination of symmetric and asymmetric key encryption schemes to secure communication. For those a significant background in algebra, number theory and geometry is required. In this paper a new cryptosystem is presented, which is based on compound commutative functions such as exponential and logarithmic functions. After the necessary theoretical deliberation in this paper, we will provide the encryption and decryption approach accompanied with relevant examples", "Keywords": "Encryption ; decryption ; exponential functions ; logarithmic functions ; compound commutative functions", "DOI": "10.1016/j.ifacol.2022.12.036", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Business and Technology, Pristina, Kosova"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Business and Technology, Pristina, Kosova"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Business and Technology, Pristina, Kosova"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Business and Technology, Pristina, Kosova"}], "References": []}, {"ArticleId": 105362850, "Title": "Language and culture internalization for human-like autotelic AI", "Abstract": "Building autonomous agents able to grow open-ended repertoires of skills across their lives is a fundamental goal of artificial intelligence (AI). A promising developmental approach recommends the design of intrinsically motivated agents that learn new skills by generating and pursuing their own goals—autotelic agents. But despite recent progress, existing algorithms still show serious limitations in terms of goal diversity, exploration, generalization or skill composition. This Perspective calls for the immersion of autotelic agents into rich socio-cultural worlds, an immensely important attribute of our environment that shapes human cognition but is mostly omitted in modern AI. Inspired by the seminal work of <PERSON><PERSON><PERSON><PERSON>, we propose Vygotskian autotelic agents—agents able to internalize their interactions with others and turn them into cognitive tools. We focus on language and show how its structure and informational content may support the development of new cognitive functions in artificial agents as it does in humans. We justify the approach by uncovering several examples of new artificial cognitive functions emerging from interactions between language and embodiment in recent works at the intersection of deep reinforcement learning and natural language processing. Looking forward, we highlight future opportunities and challenges for Vygotskian autotelic AI research, including the use of language models as cultural models supporting artificial cognitive development. A goal of AI is to develop autonomous artificial agents with a wide set of skills. The authors propose the immersion of intrinsically motivated agents within rich socio-cultural worlds, focusing on language as a way for artificial agents to develop new cognitive functions.", "Keywords": "Computer science;Language and linguistics;Engineering;general", "DOI": "10.1038/s42256-022-00591-4", "PubYear": 2022, "Volume": "4", "Issue": "12", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INRIA Bordeaux – Sud-Ouest, Talence, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "INRIA Bordeaux – Sud-Ouest, Talence, France; Université de Bordeaux, Talence, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INRIA Bordeaux – Sud-Ouest, Talence, France; ENSTA Paris, Palaiseau, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INRIA Bordeaux – Sud-Ouest, Talence, France; ENSTA Paris, Palaiseau, France; Microsoft Research, Montreal, Canada"}], "References": [{"Title": "Measuring and characterizing generalization in deep reinforcement learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "4", "Page": "e45", "JournalTitle": "Applied AI Letters"}, {"Title": "Autotelic Agents with Intrinsically Motivated Goal-Conditioned Reinforcement Learning: A Short Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "1159", "JournalTitle": "Journal of Artificial Intelligence Research"}]}, {"ArticleId": 105362892, "Title": "Unsupervised domain adaptation using fuzzy rules and stochastic hierarchical convolutional neural networks", "Abstract": "Unsupervised domain adaptation (UDA) describes a set of techniques for using previously acquired knowledge from labeled original data to support task completion in comparable but unlabeled target data. Existing UDA methods often use two classifiers to detect misaligned local areas between the original and prey vocations, resulting in poor implementation. To address this issue, we propose a fuzzy rules and stochastic classifier-based domain adaptation framework called SH-CNN+SMTEOA. Initially, the cross-domain mixed sampling approach is used to test the original and prey data. After that, the Principal Component Analysis is used to extract the characteristics, and fuzzy criteria are used to choose the suitable characteristics. Finally, we introduce the Stochastic Hierarchical Convolutional Neural Network for classification and the Selective Multi-Threshold Entropy Optimization Algorithm for judging a target instance’s dependability based on its predictive multi-threshold values. Investigations on UDA benchmark datasets reveal that the proposed method outperforms other methods in classification.", "Keywords": "Unsupervised domain adaptation ; Fuzzy rules ; Principal component analysis ; Stochastic hierarchical convolutional neural network ; Selective multi-threshold entropy optimization algorithm", "DOI": "10.1016/j.compeleceng.2022.108547", "PubYear": 2023, "Volume": "105", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, PR China;Pazhou Lab, Guangzhou, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, PR China;EIAS Data Science Lab, College of Computer and Information Sciences, Prince Sultan University, Riyadh 11586, Saudi Arabia;Corresponding author at: School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Software Engineering, Shenzhen University, Shenzhen, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, South China University of Technology, Guangzhou, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, PR China;Peng Cheng Laboratory, PR China;Corresponding author at: School of Software Engineering, South China University of Technology, Guangzhou, PR China"}], "References": [{"Title": "Geometric Knowledge Embedding for unsupervised domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; Yuguang Yan; Yuzhong Ye", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105155", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-source domain adaptation with joint learning for cross-domain sentiment classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105254", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Unsupervised domain adaptation with structural attribute learning networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "96", "JournalTitle": "Neurocomputing"}, {"Title": "Continual coarse-to-fine domain adaptation in semantic segmentation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "104426", "JournalTitle": "Image and Vision Computing"}, {"Title": "Unsupervised Domain Adaptation via bidirectional generation and middle domains alignment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108229", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 105363137, "Title": "Using Neural Networks to Detect Anomalies in X-Ray Images Obtained with Full-Body Scanners", "Abstract": "<p> In this paper, we solve the problem of detecting anomalies in X-ray images obtained by full-body scanners (FBSs). The paper describes the sequence and description of image preprocessing methods used to convert the original images obtained with an FBS to images with visually distinguishable anomalies. Examples of processed images are given. The first (preliminary) results of using a neural network for anomaly detection are shown. </p>", "Keywords": "full-body scanner; X-ray image; anomaly detection; image histogram equalization; neural network; U-2-Net", "DOI": "10.1134/S00051179220100034", "PubYear": 2022, "Volume": "83", "Issue": "10", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "RUDN University, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "RUDN University, Moscow, Russia"}, {"AuthorId": 3, "Name": "N. <PERSON><PERSON>", "Affiliation": "RUDN University, Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "RUDN University, Moscow, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "RUDN University, Moscow, Russia; Institute for Systems Analysis, Russian Academy of Sciences, Moscow, Russia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "RUDN University, Moscow, Russia"}], "References": [{"Title": "U2-Net: Going deeper with nested U-structure for salient object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "107404", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 105363173, "Title": "Calibration of Inexact Computer Models with Heteroscedastic Errors", "Abstract": "Computer models are commonly used to represent a wide range of real systems, but they often involve some unknown parameters. Estimating the parameters by collecting experimental data becomes essential in many scientific fields, ranging from engineering to biology. However, most of the existing methods are developed under the assumption that the experimental data contains homoscedastic measurement errors. Motivated by an experiment of plant relative growth rates where replicates are available, we propose a new calibration method for inexact computer models with heteroscedastic measurement errors. Asymptotic properties of the parameter estimators are derived which can be used to quantify the uncertainty of the estimates, and a goodness-of-fit test is developed to detect the presence of heteroscedasticity. Numerical examples and empirical studies demonstrate that the proposed method not only yields accurate parameter estimation, but also provides accurate predictions for physical data in the presence of both heteroscedasticity and model misspecification. An R package for the proposed methodology is provided in an open repository. © 2022 Society for Industrial and Applied Mathematics and American Statistical Association.", "Keywords": "Gaussian process; input-dependent noise; plant biology; replication; uncertainty quantification; 00A20", "DOI": "10.1137/21M1417946", "PubYear": 2022, "Volume": "10", "Issue": "4", "JournalId": 11089, "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification", "ISSN": "", "EISSN": "2166-2525", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics and Probability, Michigan State University, East Lansing, MI 48824 USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Agricultural and Biological Engineering, University of Illinois at Urbana-Champaign, Urbana, IL 61820 USA."}, {"AuthorId": 3, "Name": "Berkley J. Walker", "Affiliation": "Plant Research Laboratory/Plant Biology, Michigan State University, East Lansing, MI 48824 USA."}], "References": []}, {"ArticleId": 105363360, "Title": "Wireless Sensor Network Security Analysis for Data and Aggregation", "Abstract": "<p>Data security is critical in wireless sensor networks (WSNs) because communication signals are highly available due to data transmission in free space. Attacks ranging from passive eavesdropping to active snooping are more common on these networks. This paper proposes secure data transfer using data encryption based on the improved Rives<PERSON>–<PERSON><PERSON><PERSON>–<PERSON><PERSON> (RSA) with <PERSON><PERSON><PERSON><PERSON> (DH) key exchange algorithm (IRSA-DH). For this purpose, the adaptive distance-based agglomerative hierarchical (ADAH)-based clustering method is used. Then the cluster head (CH) is selected using the improved weight-based rain optimization (IWRO) to improve the network’s lifespan. This study aims to design a secure group communication method for WSNs. In order to generate and distribute the key to the group, the RSA and DH and key exchange algorithm had been hybridized with the Key Management Center (KMC). For safe communication between users, the key exchange technique is investigated. The performance measures such as throughput, packet loss ratio (PLR), packet delivery ratio (PDR), latency, energy consumption, end-to-end delay (EED) and network lifetime are analyzed and compared with the existing approaches.</p>", "Keywords": "Wireless sensor network; <PERSON><PERSON><PERSON><PERSON> key exchange algorithm; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>–<PERSON><PERSON>an; rain optimization; key management and cluster", "DOI": "10.1142/S0219265922500025", "PubYear": 2023, "Volume": "23", "Issue": "2", "JournalId": 20646, "JournalTitle": "Journal of Interconnection Networks", "ISSN": "0219-2659", "EISSN": "1793-6713", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, School of Computing, SRM Institute of Science and Technology, Kattankulathur, Chennai, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, School of Computing, SRM Institute of Science and Technology, Kattankulathur, Chennai, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, B<PERSON> V. Raju Institute of Technology, Narsapur, Telangana 502313, India"}], "References": [{"Title": "A practical approach to energy consumption in wireless sensor networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "2", "Page": "190", "JournalTitle": "International Journal of Advanced Intelligence Paradigms"}, {"Title": "An efficient cryptographic technique using modified <PERSON><PERSON><PERSON><PERSON> in wireless sensor networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "6", "Page": "155014772092577", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Clustering objectives in wireless sensor networks: A survey and research direction analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "180", "Issue": "", "Page": "107376", "JournalTitle": "Computer Networks"}, {"Title": "A firefly algorithm for power management in wireless sensor networks (WSNs)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "9", "Page": "9411", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An Efficient and Secure Text Encryption Scheme for Wireless Sensor Network (WSN) Using Dynamic Key Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "6", "Page": "788", "JournalTitle": "International Journal of Computer Networks And Applications"}]}, {"ArticleId": 105363506, "Title": "Valuing Corporate Securities When the Firm’s Assets are Illiquid", "Abstract": "<p>We use stochastic dynamic programming to design and solve an extended structural setting for which the illiquidity of the firm’s assets under liquidation is interpreted as an intangible corporate security. This asset tends to reduce bond values, augment yield spreads, and, thus, partially explain the credit-spread puzzle. To assess our construction, we provide a sensitivity analysis of the values of corporate securities with respect to the illiquidity parameter.</p>", "Keywords": "Structural model; Corporate securities; Illiquidity costs; Stochastic dynamic programming", "DOI": "10.1007/s10614-022-10352-5", "PubYear": 2024, "Volume": "63", "Issue": "2", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Decision Sciences, HEC Montréal and GERAD, Montréal, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Economics and Management, University of Sfax, Sfax, Tunisia; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Finance Department, UQAM, Montréal, Canada"}], "References": []}, {"ArticleId": 105363573, "Title": "Human-Like Movements of Industrial Robots Positively Impact Observer Perception", "Abstract": "<p>The number of industrial robots and collaborative robots on manufacturing shopfloors has been rapidly increasing over the past decades. However, research on industrial robot perception and attributions toward them is scarce as related work has predominantly explored the effect of robot appearance, movement patterns, or human-likeness of <i>humanoid</i> robots. The current research specifically examines attributions and perceptions of industrial robots-specifically, articulated collaborative robots-and how the type of movements of such robots impact human perception and preference. We developed and empirically tested a novel model of robot movement behavior and demonstrate how altering the movement behavior of a robotic arm leads to differing attributions of the robot's human-likeness. These findings have important implications for emerging research on the impact of robot movement on worker perception, preferences, and behavior in industrial settings.</p><p>© The Author(s), under exclusive licence to Springer Nature B.V. 2022, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Acceptance;Anthropomorphism;Articulated robot;Collaborative robots;HRI;Human-likeness;Industrial robots;Movements", "DOI": "10.1007/s12369-022-00954-2", "PubYear": 2023, "Volume": "15", "Issue": "8", "JournalId": 5388, "JournalTitle": "International Journal of Social Robotics", "ISSN": "1875-4791", "EISSN": "1875-4805", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Computer Science, University of St. Gallen, Rosenbergstrasse 30, 9000 St. Gallen, Switzerland."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Computer Science, University of St. Gallen, Rosenbergstrasse 30, 9000 St. Gallen, Switzerland."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Behavioral Science and Technology, University of St. Gallen, Torstrasse 25, 9000 St. Gallen, Switzerland."}], "References": [{"Title": "How to include User eXperience in the design of Human-Robot Interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "102072", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Do I Have a Personality? Endowing Care Robots with Context-Dependent Personality Traits", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "8", "Page": "2081", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Impact of Anthropomorphic Robot Design on Trust and Attention in Industrial Human-Robot Interaction", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}]}, {"ArticleId": 105363805, "Title": "Retraction Note: The utilization of rough set theory and data reduction based on artificial intelligence in recommendation system", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-022-07759-5", "PubYear": 2023, "Volume": "27", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Cao", "Affiliation": "School of Arts, Hunan City University, Yiyang, China; Department of Culture and Arts Contents, Dongbang Culture University, Seoul, South Korea"}], "References": []}, {"ArticleId": 105363825, "Title": "Hierarchical neural network: Integrate divide-and-conquer and unified approach for argument unit recognition and classification", "Abstract": "Argument unit recognition and classification (AURC) is a promising and critical research topic in argument mining, which aims to extract the argument units that express support or opposing stance in a given argumentative text under controversial topics. Existing studies treated the AURC as a sequence labeling problem and designed a unified approach to predict argument unit boundary and argument unit stance simultaneously. In this paper, we propose a general framework h ierarchical n eural n etwork (HNN) for AURC, by fusing two different approach: divide-and-conquer approach and unified approach. The divide-and-conquer approach considers the correlation of the two tasks inherent in AURC (task 1: argument unit recognition, AUR and task 2: argument unit classification, AUC), and jointly optimize them for prediction by a novel probability transition matrix. Finally, we used a token-level attention mechanism to efficiently fuse probability distributions obtained by our proposed divide-and-conquer approach and existing unified approach. Experimental results on two benchmark datasets demonstrate the effectiveness of our proposed framework.", "Keywords": "", "DOI": "10.1016/j.ins.2022.12.050", "PubYear": 2023, "Volume": "624", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, China;Key Laboratory of Computational Intelligence and Chinese Information Processing of Ministry of Education, Shanxi University, China;Corresponding author at: School of Computer and Information Technology, Shanxi University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Infocomm Research, A*STAR, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, China;Key Laboratory of Computational Intelligence and Chinese Information Processing of Ministry of Education, Shanxi University, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Finance, Shanxi University of Finance and Economics, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Shanxi University, China"}], "References": [{"Title": "Metaphor identification: A contextual inconsistency based neural sequence labeling approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "268", "JournalTitle": "Neurocomputing"}, {"Title": "Multiple perspective attention based on double BiLSTM for aspect and sentiment pair extract", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "438", "Issue": "", "Page": "302", "JournalTitle": "Neurocomputing"}, {"Title": "Incorporate opinion-towards for stance detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "246", "Issue": "", "Page": "108657", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 105363827, "Title": "Assessing the communication gap between AI models and healthcare professionals: Explainability, utility and trust in AI-driven clinical decision-making", "Abstract": "This paper contributes with a pragmatic evaluation framework for explainable Machine Learning (ML) models for clinical decision support. The study revealed a more nuanced role for ML explanation models, when these are pragmatically embedded in the clinical context. Despite the general positive attitude of healthcare professionals (HCPs) towards explanations as a safety and trust mechanism, for a significant set of participants there were negative effects associated with confirmation bias, accentuating model over-reliance and increased effort to interact with the model. Also, contradicting one of its main intended functions, standard explanatory models showed limited ability to support a critical understanding of the limitations of the model. However, we found new significant positive effects which repositions the role of explanations within a clinical context: these include reduction of automation bias, addressing ambiguous clinical cases (cases where HCPs were not certain about their decision) and support of less experienced HCPs in the acquisition of new domain knowledge.", "Keywords": "Explainable model ; Explainable AI ; ML in healthcare ; User study ; Clinical decision support ; Automation bias ; Confirmation bias ; Explanation's impact", "DOI": "10.1016/j.artint.2022.103839", "PubYear": 2023, "Volume": "316", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, The University of Manchester, United Kingdom of Great Britain and Northern Ireland;Digital Experimental Cancer Medicine Team, Cancer Biomarker Centre, CRUK Manchester Institute, University of Manchester, United Kingdom of Great Britain and Northern Ireland;Corresponding author at: Department of Computer Science, The University of Manchester, United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Biology Medicine and Health, The University of Manchester, United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, The University of Manchester, United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Biology Medicine and Health, The University of Manchester, United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Landers", "Affiliation": "Digital Experimental Cancer Medicine Team, Cancer Biomarker Centre, CRUK Manchester Institute, University of Manchester, United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Biology Medicine and Health, The University of Manchester, United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, The University of Manchester, United Kingdom of Great Britain and Northern Ireland;Digital Experimental Cancer Medicine Team, Cancer Biomarker Centre, CRUK Manchester Institute, University of Manchester, United Kingdom of Great Britain and Northern Ireland;Idiap Research Institute, Switzerland"}], "References": [{"Title": "Evaluating XAI: A comparison of rule-based and example-based explanations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "291", "Issue": "", "Page": "103404", "JournalTitle": "Artificial Intelligence"}, {"Title": "Examining the effects of power status of an explainable artificial intelligence system on users’ perceptions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "5", "Page": "946", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Explaining black-box classifiers using post-hoc explanations-by-example: The effect of explanations and error-rates in XAI user studies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "294", "Issue": "", "Page": "103459", "JournalTitle": "Artificial Intelligence"}, {"Title": "Information fusion as an integrative cross-cutting enabler to achieve robust, explainable, and trustworthy medical artificial intelligence", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "79", "Issue": "", "Page": "263", "JournalTitle": "Information Fusion"}, {"Title": "The explainability paradox: Challenges for xAI in digital pathology", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "133", "Issue": "", "Page": "281", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 105363847, "Title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Reveals the Rank of a Count Matrix", "Abstract": "<p>Estimating the rank of a corrupted data matrix is an important task in data analysis, most notably for choosing the number of components in PCA. Significant progress on this task was achieved using random matrix theory by characterizing the spectral properties of large noise matrices. However, utilizing such tools is not straightforward when the data matrix consists of count random variables, e.g., Poisson, in which case the noise can be heteroskedastic with an unknown variance in each entry. In this work, we focus on a Poisson random matrix with independent entries and propose a simple procedure, termed <i>biwhitening</i>, for estimating the rank of the underlying signal matrix (i.e., the Poisson parameter matrix) without any prior knowledge. Our approach is based on the key observation that one can scale the rows and columns of the data matrix simultaneously so that the spectrum of the corresponding noise agrees with the standard Marchenko-Pastur (MP) law, justifying the use of the MP upper edge as a threshold for rank selection. Importantly, the required scaling factors can be estimated directly from the observations by solving a matrix scaling problem via the Sin<PERSON><PERSON>-<PERSON> algorithm. Aside from the Poisson, our approach is extended to families of distributions that satisfy a quadratic relation between the mean and the variance, such as the generalized Poisson, binomial, negative binomial, gamma, and many others. This quadratic relation can also account for missing entries in the data. We conduct numerical experiments that corroborate our theoretical findings, and showcase the advantage of our approach for rank estimation in challenging regimes. Furthermore, we demonstrate the favorable performance of our approach on several real datasets of single-cell RNA sequencing (scRNA-seq), High-Throughput Chromosome Conformation Capture (Hi-C), and document topic modeling.</p>", "Keywords": "62H12;62H25;Hi-C;<PERSON><PERSON>-<PERSON> law;PCA;Poisson noise;Sinkhorn;bi-proportional scaling;count data;heteroskedastic noise;matrix scaling;rank estimation;rank selection;scRNA-seq", "DOI": "10.1137/21M1456807", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 62900, "JournalTitle": "SIAM Journal on Mathematics of Data Science", "ISSN": "", "EISSN": "2577-0187", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Program in Applied Mathematics, Yale University, New Haven, CT, USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Systems Engineering, University of Pennsylvania, Philadelphia, PA, USA."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathology, Interdepartmental Program in Computational Biology and Bioinformatics, Program in Applied Mathematics, Yale University, New Haven, CT, USA."}], "References": [{"Title": "Matrix Denoising for Weighted Loss Functions and Heterogeneous Signals", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "3", "Page": "987", "JournalTitle": "SIAM Journal on Mathematics of Data Science"}, {"Title": "Low-rank matrix denoising for count data using unbiased Kullback-Leibler risk estimation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "107423", "JournalTitle": "Computational Statistics & Data Analysis"}]}, {"ArticleId": 105363861, "Title": "A COMPARATIVE ANALYSIS FOR STOCK PRICE PREDICTION USING IMPROVED EXPANSIVE DEEP LSTM MODEL", "Abstract": "Due to a lack of clarity and flexibility, prediction leveraging ML models is not well fitted in many sections of commercial decision processes. Proposed model aim to employ deep learning strategy in the stock market pricing area to generate positive risk-adjusted price by analyzing previous transaction data and maintaining greater accuracy with a lower error rate. In this study, the deep learning approach is used, which is capable of handling time-series data. The results are obtained with evaluation of error rate metric MSE & RMSE which express how distant the data points are from the regression line. RMSE measures the dispersion of these residuals. It shows how concentrated the data is on the best fit line. This study compares a unique deep learning methodology with deep LSTM, GA and Harris Hawk optimization. As a part of this analysis results are observed and plotted for the various company stocks dataset, which clearly shows the effectiveness of proposed approach with reduced error rate. © 2022, Engg Journals Publications. All rights reserved.", "Keywords": "Deep Learning; deep LSTM; DEEP RNN; recurrent neural network; Stock market prediction", "DOI": "10.21817/indjcse/2022/v13i6/*********", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 54293, "JournalTitle": "Indian Journal of Computer Science and Engineering", "ISSN": "0976-5166", "EISSN": "2231-3850", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity School of Engineering & Technology, Amity University, Maharashtra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Amity School of Engineering & Technology, Amity University, Maharashtra, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity School of Engineering & Technology, Amity University, Maharashtra, India"}], "References": []}, {"ArticleId": *********, "Title": "Green hydrothermal synthesis of Ga doping derived 3D ZnO nanosatellites for high sensitive gas sensors", "Abstract": "A green hydrothermal synthesis methodology is employed for the synthesis of Ga<sup>3+</sup> doped ZnO 3D nanosatellite structures. XRD technique exposed the hexagonal wurtzite type crystal structure of the 3D Ga<sup>3+</sup> doped ZnO nanostructures. Ga<sup>3+</sup> induced 3D morphology evolution in accordance to Ga doping concentration is realized via . the FESEM images. The FESEM images indicated one sided capped rod like structures (50–250 nm) in pristine ZnO, whereas 3D satellite kind of morphology (&gt;500 nm) was observed in 5% Ga<sup>3+</sup> doped ZnO structures. TEM images revealed physical specifications of 3D nanosatellite structures exposing the central rod of 500 nm length &amp; 250 nm in width and the branch rods grown vertical to the central rod of length 300 nm and width of 50 nm. Further, optical absorbance and photoluminescence spectra confirmed the electron rich state of ZnO with Ga<sup>3+</sup> doping. The gas sensing performance has shown selectivity toward CO gas at a working temperature of 250 ⁰C. High CO sensing was recorded for 5% Ga<sup>3+</sup> doped ZnO 3D nanosatellites with attractive sensing characteristics such as good response, fast response time &amp; fast recovery time.", "Keywords": "3D nanostructure ; Green synthesis ; Ga<sup>3+</sup> doped ZnO ; Gas sensing ; CO", "DOI": "10.1016/j.snb.2022.133215", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> V.S.", "Affiliation": "Department of Electronics and Communication Engineering, <PERSON><PERSON> of Technology, Tiruchirappalli 621112, India"}, {"AuthorId": 2, "Name": "Balraj B", "Affiliation": "Department of Electronics and Communication Engineering, K<PERSON> of Technology, Tiruchirappalli 621112, India;Corresponding author"}, {"AuthorId": 3, "Name": "Siva C", "Affiliation": "Department of Physics and Nanotechnology, SRM Institute of Science and Technology, Kattankulathur 639113, India"}, {"AuthorId": 4, "Name": "Amuthameena S", "Affiliation": "Department of Biomedical Engineering, Kongunadu College of Engineering and Technology, Tiruchirappalli 621215, India"}], "References": []}, {"ArticleId": 105364378, "Title": "Exponential-Wrapped Distributions on Symmetric Spaces", "Abstract": "", "Keywords": "statistics on manifolds; exponential map; differential of the exponential map; affine locally symmetric spaces; Lie groups; wrapped distributions; 53Z50; 62E99; 53C35", "DOI": "10.1137/21M1461551", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 62900, "JournalTitle": "SIAM Journal on Mathematics of Data Science", "ISSN": "", "EISSN": "2577-0187", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Aix Marseille Univ., CNRS, Centrale Marseille, Institut Fresnel, Marseille, France."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Aix Marseille Univ., CNRS, Centrale Marseille, Institut Fresnel, Marseille, France."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Aix Marseille Univ., CNRS, Centrale Marseille, Institut Fresnel, Marseille, France."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Aix Marseille Univ., CNRS, Centrale Marseille, Institut Fresnel, Marseille, France."}], "References": [{"Title": "Geodesic Analysis in Kendall’s Shape Space with Epidemiological Applications", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "4", "Page": "549", "JournalTitle": "Journal of Mathematical Imaging and Vision"}]}, {"ArticleId": 105364691, "Title": "Fine-Grained Runtime Monitoring of Real-Time Embedded Systems", "Abstract": "<p>Dynamically ensuring the correctness of the functional behavior of a real-time embedded system is tedious, especially in the autonomous domain. Even though the current real-time task model provides sufficient information to perform basic schedulability tests, it is inadequate to be used in runtime monitoring to assert and guarantee the correctness of a system under hardware/software malfunctions or malicious cyber attacks. In this article, we present a runtime monitoring approach based on a fine-grained model of real-time tasks.</p>", "Keywords": "", "DOI": "10.1145/3577949.3577970", "PubYear": 2022, "Volume": "42", "Issue": "1", "JournalId": 12096, "JournalTitle": "ACM SIGAda Ada Letters", "ISSN": "1094-3641", "EISSN": "1557-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ. Brest, Lab-STICC, CNRS, UMR 6285, Brest, France"}, {"AuthorId": 2, "Name": "Hai Nam Tran", "Affiliation": "Univ. Brest, Lab-STICC, CNRS, UMR 6285, Brest, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Univ. Brest, Lab-STICC, CNRS, UMR 6285, Brest, France"}], "References": []}, {"ArticleId": 105364692, "Title": "REMEMBERING JURIS", "Abstract": "<p><PERSON><PERSON> was a great scientist, teacher, advisor, builder of institutions, and an overall wonderful human being-a mensch.</p><p>I had the privilege of being his advisee, and having a benefit of his friendship, his mentoring, and his support throughout my academic life. He had a profound influence on my taste in research, on my career, and on my life.</p>", "Keywords": "", "DOI": "10.1145/3577971.3577978", "PubYear": 2022, "Volume": "53", "Issue": "4", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105364703, "Title": "Energy-Efficient Model for Intruder Detection Using Wireless Sensor Network", "Abstract": "<p>A wireless sensor network (WSN) can be used for various purposes, including area monitoring, health care, smart cities, and defence. Numerous complex issues arise in these applications, including energy efficiency, coverage, and intruder detection. Intruder detection is a significant obstacle in various wireless sensor network applications. It causes data fusion that jeopardizes the network’s confidentiality, lifespan, and coverage. Various algorithm has been proposed for intruder detection where each node act as an agent, or some monitoring nodes are deployed for intruder detection. The proposed protocol detects intruders by transmitting a known bit from the Cluster Head (CH) to all nodes. The legal nodes must acknowledge their identification to the CH in order to be valid; otherwise, if the CH receives an incorrect acknowledgement from a node or receives no acknowledgement at all, it is an intruder. The proposed protocol assists in protecting sensor data from unauthorized access and detecting the intruder with its location through the identity of other legal nodes. The simulation results show that the proposed protocol delivers better results for identifying intruders for various parameters.</p>", "Keywords": "Base station; cluster head; intruder; residual energy; wireless sensor network", "DOI": "10.1142/S0219265921490025", "PubYear": 2024, "Volume": "24", "Issue": "Supp01", "JournalId": 20646, "JournalTitle": "Journal of Interconnection Networks", "ISSN": "0219-2659", "EISSN": "1793-6713", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, <PERSON><PERSON> University of Technology, Gorakhpur, Uttar Pradesh 273016, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, <PERSON><PERSON> University of Technology, Gorakhpur, Uttar Pradesh 273016, India"}], "References": [{"Title": "An Energy and Fault Aware Mechanism of Wireless Sensor Networks Using Multiple Mobile Agents", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "22", "JournalTitle": "International Journal of Distributed Systems and Technologies"}]}, {"ArticleId": 105364752, "Title": "Estimation of Excitation Current of a Synchronous Machine Using Machine Learning Methods", "Abstract": "<p>A synchronous machine is an electro-mechanical converter consisting of a stator and a rotor. The stator is the stationary part of a synchronous machine that is made of phase-shifted armature windings in which voltage is generated and the rotor is the rotating part made using permanent magnets or electromagnets. The excitation current is a significant parameter of the synchronous machine, and it is of immense importance to continuously monitor possible value changes to ensure the smooth and high-quality operation of the synchronous machine itself. The purpose of this paper is to estimate the excitation current on a publicly available dataset, using the following input parameters: Iy: load current; PF: power factor; e: power factor error; and df: changing of excitation current of synchronous machine, using artificial intelligence algorithms. The algorithms used in this research were: k-nearest neighbors, linear, random forest, ridge, stochastic gradient descent, support vector regressor, multi-layer perceptron, and extreme gradient boost regressor, where the worst result was elasticnet, with R2 = −0.0001, MSE = 0.0297, and MAPE = 0.1442; the best results were provided by extreme boosting regressor, with R2¯ = 0.9963, MSE¯ = 0.0001, and MAPE¯ = 0.0057, respectively.</p>", "Keywords": "artificial intelligence algorithms; excitation current; regression algorithms; synchronous machine artificial intelligence algorithms ; excitation current ; regression algorithms ; synchronous machine", "DOI": "10.3390/computers12010001", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Rijeka, Trg Braće Mažuranića 10, 51000 Rijeka, Croatia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, University of Rijeka, Vukovarska 58, 51000 Rijeka, Croatia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, University of Rijeka, Vukovarska 58, 51000 Rijeka, Croatia; Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, University of Rijeka, Vukovarska 58, 51000 Rijeka, Croatia"}], "References": [{"Title": "Temperature prediction for electric vehicles of permanent magnet synchronous motor using robust machine learning tools", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "1", "Page": "243", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 105364813, "Title": "Smartphone touch gesture for right-handed older adults: touch performance and offset models", "Abstract": "<p>Older adults have difficulties with smartphone touch gestures, such as tapping, dragging, zooming, and rotating. To alleviate these difficulties, this study examined error patterns in interaction traces of older adults and then proposed models and suggestions for error compensation. Two experiments were conducted among 88 right-handed older adults, who were asked to complete four tasks through tapping, dragging, zooming, and rotating. Their real-time interaction traces were captured through a self-developed program and their touch performance was analyzed. Four major findings for right-handed older adults were derived from the experiment. First, the right-handed older participants always deviated to the right when tapping. To reduce the tapping deviation, offset models were proposed for automatic correction. Second, older participants’ dragging from the left top corner to the right bottom corner of the screen included two phases: dragging to the top of the target and then calibrating to the target. Third, the calibration time during dragging is important, adding which increases the <PERSON><PERSON>’s law model’s fitting effect from 22 to 72%. Forth, continuous tapping on touchscreen is three-dimensional movement, whose movement time is different from that of two-dimensional pointing tasks in <PERSON><PERSON>’s law. When participants were required to tap as two-dimensional movement, the fitting effect of <PERSON><PERSON>’s law increased from 47 to 80%.</p>", "Keywords": "Smartphones; Older adults; Touching gestures; <PERSON><PERSON>’s law; Interaction traces", "DOI": "10.1007/s12652-022-04502-8", "PubYear": 2023, "Volume": "14", "Issue": "3", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The 28th Research Institute of China Electronics Technology Group Corporation, Nanjing, People’s Republic of China"}], "References": [{"Title": "Touch-dynamics based Behavioural Biometrics on Mobile Devices – A Review from a Usability and Performance Perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Adapting smartwatch interfaces to hand gestures during movements: offset models and the C-shaped pattern of tapping", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "8099", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "User recognition based on periocular biometrics and touch dynamics", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "114", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Effects of the Font Size and Line Spacing of Simplified Chinese Characters on Smartphone Readability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Dong", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "177", "JournalTitle": "Interacting with Computers"}]}, {"ArticleId": 105364878, "Title": "Sparse RGB-D images create a real thing: A flexible voxel based 3D reconstruction pipeline for single object", "Abstract": "Reconstructing 3D models for single objects with complex backgrounds has wide applications like 3D printing, AR/VR, and so on. It is necessary to consider the tradeoff between capturing data at low cost and getting high-quality reconstruction results. In this work, we propose a voxel-based modeling pipeline with sparse RGB-D images to effectively and efficiently reconstruct a single real object without the geometrical post-processing operation on background removal. First, referring to the idea of VisualHull, useless and inconsistent voxels of a targeted object are clipped. It helps focus on the target object and rectify the voxel projection information. Second, a modified TSDF calculation and voxel filling operations are proposed to alleviate the problem of depth missing in the depth images. They can improve TSDF value completeness for voxels on the surface of the object. After the mesh is generated by the MarchingCube, texture mapping is optimized with view selection, color optimization, and camera parameters fine-tuning. Experiments on Kinect capturing dataset, TUM public dataset, and virtual environment dataset validate the effectiveness and flexibility of our proposed pipeline.", "Keywords": "Sparse RGB-D ; 3D reconstruction ; TSDF ; Depth map", "DOI": "10.1016/j.visinf.2022.12.002", "PubYear": 2023, "Volume": "7", "Issue": "1", "JournalId": 47888, "JournalTitle": "Visual Informatics", "ISSN": "2543-2656", "EISSN": "2468-502X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, 430072, Hubei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Art, Wuhan Business University, Wuhan, 430000, Hubei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei, 230601, Anhui, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, 430072, Hubei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, 430072, Hubei, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, 430072, Hubei, China;Corresponding author"}], "References": [{"Title": "Real-time dense 3D reconstruction and camera tracking via embedded planes representation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "10-12", "Page": "2215", "JournalTitle": "The Visual Computer"}, {"Title": "Adaptive depth estimation for pyramid multi-view stereo", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "268", "JournalTitle": "Computers & Graphics"}]}, {"ArticleId": 105364909, "Title": "SocialHaterBERT: A dichotomous approach for automatically detecting hate speech on Twitter through textual analysis and user profiles", "Abstract": "Social media platforms have evolved into an online representation of our social interactions. We may use the resources they provide to analyze phenomena that occur within them, such as the development and viralization of offensive and hostile content. In today’s polarized world, the escalating nature of this behavior is cause for concern in modern society. This research includes an in-depth examination of previous efforts and strategies for detecting and preventing hateful content on the social network Twitter, as well as a novel classification approach based on users’ profiles, related social environment and generated tweets. This paper’s contribution is threefold: (i) an improvement in the performance of the HaterNet algorithm, an expert system developed in collaboration with the Spanish National Office Against Hate Crimes of the Spanish State Secretariat for Security (Ministry of the Interior) that is capable of identifying and monitoring the evolution of hate speech on Twitter using an LTSM + MLP neural network architecture. To that end, a model based on BERT, HaterBERT , has been created and tested using HaterNet ’s public dataset, providing results that show a significant improvement; (ii) A methodology to create a user database in the form of a relational network to infer textual and centrality features. This contribution, SocialGraph , has been independently tested with various traditional Machine Learning and Deep Learning algorithms, demonstrating its usefulness in spotting haters; (iii) a final model, SocialHaterBERT , that integrates the previous two approaches by analyzing features other than those inherent in the text. Experiment results reveal that this last contribution greatly improves outcomes, establishing a new field of study that transcends textual boundaries, paving the way for future research in coupled models from a diachronic and dynamic perspective.", "Keywords": "Hate speech ; Twitter ; Deep learning ; Social network analysis ; BERT ; Topic modeling", "DOI": "10.1016/j.eswa.2022.119446", "PubYear": 2023, "Volume": "216", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Gloria del Valle-Cano", "Affiliation": "Escuela Politécnica Superior, Universidad Autonóma de Madrid, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Escuela Politécnica Superior, Universidad Autonóma de Madrid, Madrid, Spain;UC3M-Santander Big Data Institute, Universidad Carlos III de Madrid, Madrid, Spain;Correspondence to: Escuela Politécnica Superior, Universidad Autónoma de Madrid, C/ Francisco Tomás y Valiente, 11, Campus de Cantoblanco, 28049 Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science & Informatics, Cardiff University, UK;UC3M-Santander Big Data Institute, Universidad Carlos III de Madrid, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dirección General de Coordinación y Estudios, Secretaría de Estado de Seguridad, Ministerio del Interior, Madrid, Spain"}], "References": [{"Title": "Detection of Hate Speech Text in Hindi-English Code-mixed Data", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "737", "JournalTitle": "Procedia Computer Science"}, {"Title": "Building a formal model for hate detection in French corpora", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "2358", "JournalTitle": "Procedia Computer Science"}, {"Title": "Resources and benchmark corpora for hate speech detection: a systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "2", "Page": "477", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Comparing pre-trained language models for Spanish hate speech detection", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON>-<PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114120", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Challenges of Hate Speech Detection in Social Media", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 105364931, "Title": "An optimal far field radiation pattern synthesis of mutually coupled antenna arrays", "Abstract": "", "Keywords": "", "DOI": "10.1002/mmce.23198", "PubYear": 2022, "Volume": "32", "Issue": "7", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering HIT  Haldia India"}, {"AuthorId": 2, "Name": "Durbadal Mandal", "Affiliation": "Department of Electronics and Communication Engineering NIT  Durgapur India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering NIT  Durgapur India"}], "References": [{"Title": "Optimization of circular antenna arrays using the cuckoo search algorithm", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "8", "Page": "", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}]}, {"ArticleId": 105364940, "Title": "A new hybrid entropy-based decision support method and its application to online shopping selection", "Abstract": "The current study proposes a new method called the hybrid entropy-based decision support method (named HEBM) to address multi-criteria decision making (MCDM) problems with uncertainty. In the proposed HEBM, the decision makers (DMs)’ imprecise evaluations are characterized with intuitionistic multiplicative values; weights of the criteria are generated by an improved method with hybrid entropy and cross-entropy measures; and alternatives rankings are determined by defined closeness coefficients. Simulation experiments verify the validity of the improved criteria weights generation method with hybrid entropy and cross-entropy measures. Compared with existing MCDM methods, the proposed HEBM has the following advantages: (1) it avoids the information loss and has higher accuracy; (2) the weights of the criteria derived can directly characterize the DMs’ preferences on the criteria; and (3) quantitative and qualitative information are both analyzed. Simulation experiments and comparative analyses are conducted to demonstrate the effectiveness and superiority of the proposed method. A case study of online shopping selection problems is presented to illustrate the applicability of the proposed method.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108917", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, 96 Jinzhai Road, Hefei, Anhui 230026, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, 96 Jinzhai Road, Hefei, Anhui 230026, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, 96 Jinzhai Road, Hefei, Anhui 230026, PR China"}], "References": [{"Title": "Distance‐based intuitionistic multiplicative multiple criteria decision‐making methods for healthcare management in West China Hospital", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "", "JournalTitle": "Expert Systems"}, {"Title": "A priority-based intuitionistic multiplicative UTASTAR method and its application in low-carbon tourism destination selection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106026", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-stage multi-attribute decision making method based on online reviews for hotel selection considering the aspirations with different development speeds", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106421", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Exponential operational laws and new aggregation operators for intuitionistic multiplicative set in multiple-attribute group decision making process", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "538", "Issue": "", "Page": "245", "JournalTitle": "Information Sciences"}, {"Title": "Three-stage reject inference learning framework for credit scoring using unsupervised transfer learning and three-way decision theory", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "113366", "JournalTitle": "Decision Support Systems"}, {"Title": "Three-way group consensus decision based on hierarchical social network consisting of decision makers and participants", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "585", "Issue": "", "Page": "289", "JournalTitle": "Information Sciences"}, {"Title": "Two different approaches for consistency of intuitionistic multiplicative preference relation using directed graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "10", "Page": "4653", "JournalTitle": "Soft Computing"}, {"Title": "A data-driven decision-making framework for personnel selection based on LGBWM and IFNs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "109227", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105364981, "Title": "Pre-service teachers' perceptions and intentions regarding the use of chatbots through statistical and lag sequential analysis", "Abstract": "Chatbots provide unique interactions with compatible learning system features, improving the limitations of current learning systems. Educational chatbots are seen as the future of technology integration in the field of education. The success and usefulness of chatbots in the educational setting are highly dependent on teachers&#x27; beliefs regarding their efficacy, yet most research focuses on the effects on students&#x27; learning. Only a few studies have investigated teachers’ beliefs regarding the use of chatbots, which is considered an important issue. Owning to teachers&#x27; beliefs having been transformed from their pre-service teacher training, this study used quantitative (i.e., questionnaires), qualitative (i.e., interview), and evidence-based (i.e., behavioral analysis) methods to investigate pre-service teachers&#x27; learning perceptions and intentions about using chatbots for learning during their training phases. The results of this study revealed that learning perceptions did not reflect pre-service teachers&#x27; propensity to use chatbots, but the behavioral analysis uncovered some specific intentions for using chatbots. We further discuss these findings to provide recommendations for the future development of chatbots use in education.", "Keywords": "Educational chatbot ; Pre-service teacher ; Learning behavior analysis ; Technology enhanced learning", "DOI": "10.1016/j.caeai.2022.100119", "PubYear": 2023, "Volume": "4", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Education, National Yang Ming Chiao Tung University, 1001 University Road, Hsinchu, 300, Taiwan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Education, National Yang Ming Chiao Tung University, 1001 University Road, Hsinchu, 300, Taiwan"}], "References": [{"Title": "Technology-enhanced learning in higher education: A bibliometric analysis with latent semantic approach", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "106177", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Chatbots: A Tool to Supplement the Future Faculty Mentoring of Doctoral Engineering Students", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "", "Page": "373", "JournalTitle": "International Journal of Doctoral Studies"}, {"Title": "Chatbot Experiences of Informal Language Learners", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "4", "Page": "51", "JournalTitle": "International Journal of Computer-Assisted Language Learning and Teaching"}, {"Title": "How Should My Chatbot Interact? A Survey on Social Characteristics in Human–Chatbot Interaction Design", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "729", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "AI Chatbot for Tourist Recommendations: A Case Study in the City of Jeddah, Saudi Arabia", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "19", "Page": "18", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "Predicting preservice teachers' intention to use technology-enabled learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "104207", "JournalTitle": "Computers & Education"}, {"Title": "Beliefs about digital technologies and teachers’ acceptance of an educational app for preschoolers", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "172", "Issue": "", "Page": "104264", "JournalTitle": "Computers & Education"}, {"Title": "Chatbots for language learning—Are they really useful? A systematic review of chatbot‐supported language learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "237", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "An analysis of children’ interaction with an AI chatbot and its impact on their interest in reading", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "104576", "JournalTitle": "Computers & Education"}, {"Title": "User Preferences of Privacy-Enhancing Attributes of a Smart Speaker", "Authors": "<PERSON><PERSON><PERSON> Choi; Jonghwa Park; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "18", "Page": "3649", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 105365073, "Title": "A comparative performance analysis of intelligence-based algorithms for optimizing competitive facility location problems", "Abstract": "Most companies operate to maximize profits and increase their market shares in competitive environments. Since the proper location of the facilities conditions their market shares and profits, the competitive facility location problem (CFLP) has been extensively applied in the literature. This problem generally falls within the class of NP-hard problems, which are difficult to solve. Therefore, choosing a proper solution method to optimize the problem is a key factor. Even though CFLPs have been consistently solved and investigated, an important question that keeps being neglected is how to choose an appropriate solution technique. Since there are no specific criteria for choosing a solution method, the reasons behind the selection approach are mostly unclear. These models are generally solved using several optimization techniques. As harder-to-solve problems are usually solved using meta-heuristics, we apply different meta-heuristic techniques to optimize a new version of the CFLP that incorporates reliability and congestion. We divide the algorithms into four categories based on the nature of the meta-heuristics: evolution-based, swarm intelligence-based, physics-based, and human-based. GAMS software is also applied to solve smaller-size CFLPs. The genetic algorithm and differential evolution of the first category, particle swarm optimization and artificial bee colony optimization of the second, Tabu search and harmony search of the third, and simulated annealing and vibration damping optimization of the fourth are applied to solve our CFLP model. Statistical analyses are implemented to evaluate and compare their relative performances. The results show the algorithms of the first and third categories perform better than the others.", "Keywords": "Competitive facility location ; Optimization ; Computational intelligence ; Meta-heuristics", "DOI": "10.1016/j.mlwa.2022.100443", "PubYear": 2023, "Volume": "11", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering Sharif University of Technology, Tehran, Iran;Department of Industrial Engineering College of Engineering, West Tehran Branch Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering Sharif University of Technology, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Business Systems and Analytics Department, Distinguished Chair of Business Analytics, La Salle University, Philadelphia, USA;Business Information Systems Department, Faculty of Business Administration and Economics, University of Paderborn, Paderborn, Germany;Correspondence to: Business Systems and Analytics Department, Distinguished Chair of Business Analytics, La Salle University, Philadelphia, PA 19141, United States"}, {"AuthorId": 4, "Name": "Francisco J. Santos-Arteaga", "Affiliation": "Departamento de Análisis Económico y Economía Cuantitativa, Universidad Complutense de Madrid, Madrid, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering College of Engineering, West Tehran Branch Islamic Azad University, Tehran, Iran"}], "References": [{"Title": "ε -Constraint method for bi-objective competitive facility location problem with uncertain demand scenario", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "33", "JournalTitle": "EURO Journal on Computational Optimization"}, {"Title": "A Kernel Search Matheuristic to Solve The Discrete Leader-Follower Location Problem", "Authors": "<PERSON>-<PERSON><PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "1", "Page": "73", "JournalTitle": "Networks and Spatial Economics"}, {"Title": "The Follower Competitive Location Problem with Comparison-Shopping", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "367", "JournalTitle": "Networks and Spatial Economics"}, {"Title": "Gradual covering location problem with multi-type facilities considering customer preferences", "Authors": "Hande Küçükaydın; Necati Aras", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106577", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Solution of asymmetric discrete competitive facility location problems using ranking of candidate locations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "23", "Page": "17705", "JournalTitle": "Soft Computing"}, {"Title": "A single-facility competitive location problem in the plane based on customer choice rules", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "323", "JournalTitle": "Journal of Data, Information and Management"}, {"Title": "Generalized Benders decomposition for competitive facility location with concave demand and zone-specialized variable attractiveness", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "130", "Issue": "", "Page": "105236", "JournalTitle": "Computers & Operations Research"}, {"Title": "An hybrid particle swarm optimization with crow search algorithm for feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "", "Page": "100108", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Restaurant recommender system based on sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "", "Page": "100114", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Enhancing the pattern recognition capacity of machine learning techniques: The importance of feature positioning", "Authors": "<PERSON><PERSON><PERSON>; Francisco <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "", "Page": "100196", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Competitive facility location problem with foresight considering discrete-nature attractiveness for facilities: Model and solution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "146", "Issue": "", "Page": "105900", "JournalTitle": "Computers & Operations Research"}, {"Title": "Machine-learning models for spatially-explicit forecasting of future racial segregation in US cities", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "", "Page": "100359", "JournalTitle": "Machine Learning with Applications"}]}, {"ArticleId": 105365077, "Title": "Website-Based Government Learning Development: An Innovation for Learning in the Implementation of Life-Based Curriculum", "Abstract": "<p>In the digital era, the internet is one of the most influential learning resources in various sectors including the education sector. With the digitalization process in the education sector, the learning process can be carried out anywhere and anytime, the concept of \"space and time\" which has been an indicator in determining the speed and understanding of students begins to fuse with digital learning in the form of virtual learning, this is in line with the concept of a life-based curriculum which emphasizes that the learning process can be carried out anywhere, in any situation and circumstance. This study aims to develop a website-based government learning product as a learning innovation in the implementation of a life-based curriculum. The specific objectives of this study are (1) how the characteristics of the development of website-based government learning are, and (2) how effective the development of website-based government learning is. The development is carried out using the development model from Sugiyono (2013) which has been adjusted by the researcher. The product of the learning model was then tested for validity and product testing with students majoring in Law and Citizenship at the State University of Malang as test respondents .\r  \r Please add with research result</p>", "Keywords": "Digital Learning;Web-Based Learning Model;Government Science", "DOI": "10.3991/ijim.v16i24.36167", "PubYear": 2022, "Volume": "16", "Issue": "24", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "Sri Untari", "Affiliation": "Universitas Negeri Malang, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Malang"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Malang"}], "References": []}, {"ArticleId": 105365120, "Title": "Methods and Tools for Evaluating the Characteristics of MIS-Capacitor Gas Sensors", "Abstract": "<p> Based on the electrophysical models of electrical characteristics of MIS-capacitor sensors, we analyze the features of methods and tools for evaluating metrological characteristics of the sensors in general terms and using the example of hydrogen-sensitive sensor elements with Pd–SiO (_2) –Si structure. Recommendations are given on the choice of optimal circuit modes for determining the performance characteristics of the sensors of concentrations of hydrogen-containing gases and of gas analyzers for various purposes. </p>", "Keywords": "MIS structure; capacitance–voltage characteristic; gas-sensitive MIS capacitor; output signal", "DOI": "10.1134/S00051179220100162", "PubYear": 2022, "Volume": "83", "Issue": "10", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research Nuclear University MEPhI, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research Nuclear University MEPhI, Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research Nuclear University MEPhI, Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research Nuclear University MEPhI, Moscow, Russia"}], "References": []}, {"ArticleId": 105365134, "Title": "Visual Odometry from the Images of the Reference Surface with Small Interframe Rotations", "Abstract": "<p> The paper considers the problem of visual odometry based on a sequence of video frames formed using a camera perpendicularly downward facing the reference surface. The problem is solved under the assumption that the shooting frequency is high, so that the interframe rotation and shift parameters are small. The technology is implemented in the form of a sequence of the following steps: determining the shift and rotation with an accuracy of an integer number of pixels using the correlation method, clarifying the shift and rotation parameters using the optical flow method, and correcting estimation errors associated with uneven motion and fluctuations in the distance of the camera to the reference surface by estimating deviations of local calibration characteristics from their mean values. The results of experimental studies of the technology on test trajectories obtained by simulating the motion of a vehicle along the reference surface are presented. </p>", "Keywords": "visual odometry; reference surface; frame sequence; modeling", "DOI": "10.1134/S00051179220100022", "PubYear": 2022, "Volume": "83", "Issue": "10", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Samara University, Samara, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Samara University, Samara, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Image Processing Systems Institute, a Branch of Federal Research Center “Crystallography and Photonics”, Samara, Russia"}], "References": [{"Title": "A review of monocular visual odometry", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "5", "Page": "1053", "JournalTitle": "The Visual Computer"}, {"Title": "Vehicle Motion Estimation Using Visual Observations of the Elevation Surface", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "10", "Page": "1730", "JournalTitle": "Automation and Remote Control"}, {"Title": "Unsupervised deep learning based ego motion estimation with a downward facing camera", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "785", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 105365145, "Title": "Double Double Descent: On Generalization Errors in Transfer Learning between Linear Regression Tasks", "Abstract": "", "Keywords": "overparameterized learning; linear regression; transfer learning; double descent; 62J05; 68Q32", "DOI": "10.1137/22M1469559", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 62900, "JournalTitle": "SIAM Journal on Mathematics of Data Science", "ISSN": "", "EISSN": "2577-0187", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, Ben-Gurion University, Be’er-Sheva 8410501, Israel ()."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Rice University, Houston, TX 77005 USA ()."}], "References": [{"Title": "Two Models of Double Descent for Weak Features", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "1167", "JournalTitle": "SIAM Journal on Mathematics of Data Science"}, {"Title": "Probing transfer learning with a model of synthetic correlated datasets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "1", "Page": "015030", "JournalTitle": "Machine Learning: Science and Technology"}]}, {"ArticleId": *********, "Title": "Retailer Leadership under Monopolistic Competition", "Abstract": "<p> We investigate a modification of the <PERSON><PERSON><PERSON><PERSON> model supplemented by retailing; namely, we consider various situations of Stackelberg equilibrium under retailer leadership and the condition of free entry of manufacturers to the market. For each of the situations, we provide detailed solutions considered taking into account the preferences of participants in the market interaction. This allows one to understand which of the considered situations are most beneficial to occur for retailer, manufacturers, and for society as a whole. Moreover, optimal taxation is considered. Situations are revealed when it is beneficial for the state to tax and when, on the contrary, to subsidize the producer. </p>", "Keywords": "Dixit–<PERSON>iglitz model; retailer; Staсkelberg equilibrium; public welfare; consumer surplus", "DOI": "10.1134/S00051179220100186", "PubYear": 2022, "Volume": "83", "Issue": "10", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Novosibirsk State University, Novosibirsk, Russia"}], "References": []}, {"ArticleId": *********, "Title": "Design and implementation of P4 virtual switches and P4 virtual networks", "Abstract": "This paper introduces the design and implementation of P4 virtual switches (VSWs) and P4 virtual networks (VNs). The proposed switch virtualization supports multiple VSWs embedded in a single P4 physical switch (PSW). Similar to a PSW, users use standard P4 language to program their VSWs. Given a set of VSWs and their target PSW, our hypervisor integrates all the VSW programs to generate configuration files that enable the target PSW to become a platform to realize switch virtualization. Users can define customized header types and metadata for their VSWs. In addition, our system supports live reconfiguration. Reconfiguring a VSW in a PSW will not interrupt the other VSWs in the PSW. Based on the proposed switch virtualization scheme, we present the way to share multiple VNs in a P4 physical network to provide multitenant services. A VN is composed of multiple VSWs and multiple virtual links (VL), and each VN is provisioned with guaranteed bandwidth for their VLs. Each tenant can control and manage its own VN—similar to a dedicated P4 network. In addition, each tenant can assign a priority to its traffic. The proposed network virtualization guarantees traffic isolation, preventing inter-VN interference. Results on an experimental network used for performance evaluation show that our system can successfully generate VSWs and VNs. The proposed hitless reconfiguration method can prevent service interruptions between VSWs during VSW reconfiguration. The experimental results also show that our network virtualization can accurately guarantee the provisioned bandwidths of VNs in a physical P4 network.", "Keywords": "", "DOI": "10.1016/j.comcom.2022.12.016", "PubYear": 2023, "Volume": "199", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Delta Electronics, Hsinchu, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Communications Engineering, National Chung Cheng University, Chiayi, Taiwan;Corresponding author"}], "References": []}, {"ArticleId": 105365458, "Title": "Intelligent Detection of Arrhythmia Episodes in Dialysis Patients", "Abstract": "This work discusses the design of an automatic detector of arrhythmia episodes in patients submitted to dialysis. The system aims to operate on portable devices in real-time, allowing a faster response of healthcare workers to possible intercurrence episodes. The detection is based on processing short windows of samples extracted from the electrocardiogram signal around the R-wave peak in raw format. A comprehensive study evaluating several classification techniques and class-imbalance strategies is conducted based on the MIT-BIH Arrhythmia Database. Besides, a new procedure for tuning the sample window length based on an experimental feature importance cumulative distribution is proposed. Results show that a Random Forest classifier, trained with minority class oversampling, is cost-effective regarding complexity and computational cost, achieving an accuracy of 98.7% for windows sizes as small as 105 samples.", "Keywords": "", "DOI": "10.21528/lnlm-vol20-no2-art3", "PubYear": 2022, "Volume": "20", "Issue": "2", "JournalId": 27507, "JournalTitle": "Learning and Nonlinear Models", "ISSN": "1676-2789", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105365529, "Title": "Reminiscences of <PERSON><PERSON>", "Abstract": "<p>I was fortunate to have been a student of <PERSON><PERSON> during the early years of the Computer Science Department at Cornell. <PERSON><PERSON> was a founding member of the department and its first chair.</p>", "Keywords": "", "DOI": "10.1145/3577971.3577976", "PubYear": 2022, "Volume": "53", "Issue": "4", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105365530, "Title": "SIGACT News Complexity Theory Column 115", "Abstract": "<p><PERSON><PERSON> was so preternaturally wise and brilliant that in time people may find it hard to believe that someone so extraordinary existed in this world. Yet as I write this, three months to the day after <PERSON><PERSON> passed away, it still seems impossible that such a force of nature can no longer be part of this world.</p>", "Keywords": "", "DOI": "10.1145/3577971.3577977", "PubYear": 2022, "Volume": "53", "Issue": "4", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science, University of Rochester, Rochester, NY, USA"}], "References": []}, {"ArticleId": 105365829, "Title": "Active anomaly detection based on deep one-class classification", "Abstract": "Active learning has been utilized as an efficient tool in building anomaly detection models by leveraging expert feedback. In an active learning framework, a model queries samples to be labeled by experts and re-trains the model with the labeled data samples. It unburdens in obtaining annotated datasets while improving anomaly detection performance. However, most of the existing studies focus on helping experts identify as many abnormal data samples as possible, which is a sub-optimal approach for one-class classification-based deep anomaly detection. In this paper, we tackle two essential problems of active learning for Deep SVDD: query strategy and semi-supervised learning method. First, rather than solely identifying anomalies, our query strategy selects uncertain samples according to an adaptive boundary. Second, we apply noise contrastive estimation in training a one-class classification model to incorporate both labeled normal and abnormal data effectively. We analyze that the proposed query strategy and semi-supervised loss individually improve an active learning process of anomaly detection and further improve when combined together on seven anomaly detection datasets.", "Keywords": "", "DOI": "10.1016/j.patrec.2022.12.009", "PubYear": 2023, "Volume": "167", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "Min<PERSON><PERSON> Kim", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Applied Sciences, Harvard University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering, King’s College London, United Kingdom;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Republic of Korea"}], "References": [{"Title": "An overview and a benchmark of active learning for outlier detection with one-class classifiers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114372", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep Learning for Anomaly Detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>bing Cao", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Anomaly detection using improved deep SVDD model with data structure preservation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "1", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 105365832, "Title": "All-in-one microfluidic device with an integrated porous filtration membrane for on-site detection of multiple salivary biomarkers", "Abstract": "A hand-held microfluidic device is developed with an integrated filtration membrane for electrochemical monitoring of multiple salivary biomarkers. The platform consists of array of screen-printed electrodes, integrated with a high throughput microfluidic system that was developed via clean-room free 3D-printing technique. A ‘channel-inside-channel’ configuration is introduced to facilitate pump-less passive flow of saliva at the broad sensing chambers. In order to eliminate the salivary protein based biofouling problems, a flexible porous filtration membrane is integrated at the microfluidic inlet that avoids the passage of bulkier protein constituents. As the presence of separate insulator layer on the sensing electrodes adversely affects its integration with the fluidic channel, the electrode substrate itself is utilized as the insulator by exposing the bottom region of the printed electrodes as active region. Enzyme based electrochemical sensors are developed with suitable nanomaterials for monitoring glucose, lactate, cholesterol and uric acid from collected saliva samples. The device showed distinct differences in salivary glucose levels for healthy subjects before and after consuming glucose-rich drinks. The glucose sensor responses are validated with high performance liquid chromatography and commercial colorimetric assay methods, and observed good correlation with parallel finger prick blood glucose measurements with an average Pearson coefficient of 0.9161, demonstrating its efficacy in saliva based point-of-care analysis.", "Keywords": "Point-of-care sensor ; Microfluidic sensor ; Salivary biomarkers ; 3D-printing ; Screen-printing ; Wireless data collection", "DOI": "10.1016/j.snb.2022.133214", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrodics and Electrocatalysis Division, CSIR-Central Electrochemical Research Institute (CECRI), Karaikudi, Tamil Nadu 630003, India;Academy of Scientific and Innovative Research (AcSIR)-CSIR, Ghaziabad 201002, Uttar Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrodics and Electrocatalysis Division, CSIR-Central Electrochemical Research Institute (CECRI), Karaikudi, Tamil Nadu 630003, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research & Development Group, Hitachi, Ltd., 1-280 Higashi-koigakubo, Kokubunji-shi, Tokyo 1858601, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrodics and Electrocatalysis Division, CSIR-Central Electrochemical Research Institute (CECRI), Karaikudi, Tamil Nadu 630003, India;Academy of Scientific and Innovative Research (AcSIR)-CSIR, Ghaziabad 201002, Uttar Pradesh, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrodics and Electrocatalysis Division, CSIR-Central Electrochemical Research Institute (CECRI), Karaikudi, Tamil Nadu 630003, India;Academy of Scientific and Innovative Research (AcSIR)-CSIR, Ghaziabad 201002, Uttar Pradesh, India;Corresponding author at: Electrodics and Electrocatalysis Division, CSIR-Central Electrochemical Research Institute (CECRI), Karaikudi, Tamil Nadu 630003, India"}], "References": [{"Title": "Recent advances in microfluidic paper-based assay devices for diagnosis of human diseases using saliva, tears and sweat samples", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "342", "Issue": "", "Page": "130078", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105366087, "Title": "Establishing the National Cybersecurity (Resilience) Ecosystem", "Abstract": "The goal and motivation of this paper is to present an approach on establishing National Cybersecurity (Resilience) Ecosystem in Western Balkan Countries which stems from the desire to provide a secure environment for public services to the citizens of the Western Balkans as much as possible. The paper deals with the system dependencies and cybersecurity trends, the need for standard compliances and infrastructure. Another part of the paper based on the results of the gap analysis of the current frameworks where we created a mapping table that helps the reader to understand the link between the several national stakeholders which will ensure the cybersecurity resilience. Finally, in the paper we explained the integrations of the current national cybersecurity ecosystem to the Global Cybersecurity Ecosystems and the European cybersecurity technical ecosystem which is followed with the summarizing the overall research work with findings and recommendations. These services at the same time must be easily accessible and based on international standards. As small countries as we are, the possibilities for implementing such models are enormous even as pilot projects in the beginning, continuing with their expansion. Therefore, we are convinced and motivated to push this model of a cybersecurity (resilience) ecosystem forward and to become a model of success for other countries.", "Keywords": "cybersecurity ; resilience ; ecosystem ; standards ; models", "DOI": "10.1016/j.ifacol.2022.12.008", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University for Business and Technology - UBT Prishtina , Kosovo"}], "References": [{"Title": "Toward a Sustainable Cybersecurity Ecosystem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "3", "Page": "74", "JournalTitle": "Computers"}]}, {"ArticleId": 105366088, "Title": "A Research Cluster's Vision for a Pilot Factory in the South East Technological University of Ireland", "Abstract": "A research activity proposed for the South East Technological University (SETU), Waterford in Ireland from its CONNEXIONS Catalyst pathway grant is the development of an interdisciplinary research cluster within INSYTE, which spans the schools of Business, Education, Science, Engineering and Industry. It aims to co-evolve cutting-edge models of educational delivery. This research cluster has emerged from the work of the INSYTE Centre located in the INSYTE-Cooley Research Lab (ICRL) in the Digitization Hub of the Luke Wadding Library at SETU, Waterford. Educational techniques have evolved over the past decades and these advances have not necessarily filtered down to various disciplines, e.g. engineering. The focus of this research cluster is to have the ‘Learner’ at the center of the ‘Learning Experience’ and to wrap the technology around them. This in turn, will increase the adoption and usage of such technologies. It will do this by cross-fertilization of knowledge over diverse domains to produce customised human-centred solutions for communities. Equality, diversity and inclusion are critical strands in this research. This paper shows that the European Union (EU) is supportive of changes in education required to close the skills gap in engineering and to enable reskilling to support Industry 5.0. In addition to the creation of cutting-edge models of educational delivery the vision of this multidisciplinary human centered research cluster is the establishment of a state-of-the-art pilot factory as an Irish national initiative in SETU, Waterford.", "Keywords": "Human Centered ; Third Level Education ; Engineering ; Inclusivity ; Diversity ; ICT ; Continuous Adult Learning ; Micro Credentials ; Industry 4.0 ; Industry 5.0", "DOI": "10.1016/j.ifacol.2022.12.028", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-Kent", "Affiliation": "Department of Engineering Technology, South East Technological University (SETU) Waterford, Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "INSYTE, SETU, Ireland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Education, SETU, Waterford, Ireland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "INSYTE, SETU, Waterford, Ireland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "INSYTE, SETU, Waterford, Ireland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "INSYTE, SETU, Waterford, Ireland"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INSYTE, SETU, Waterford, Ireland"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "INSYTE, SETU, Waterford, Ireland"}], "References": [{"Title": "Social Responsibility, Human Centred Systems and Engineering Ethics:", "Authors": "<PERSON><PERSON>; <PERSON>.", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "17451", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Diversity and Inclusion Working Group: Raising the Profile of Diversity and Inclusion in IFAC", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "576", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "D&I in Engineering Education, past, present, and future: A Country Case Study of Ireland.", "Authors": "<PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "564", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Digital Enterprise Technologies: Do Enterprise Control and Automation Technologies Reinforce Gender Biases and Marginalisation?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "551", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105366095, "Title": "Psychological distress among healthcare workers during the Covid-19 in Kosovo", "Abstract": "Epidemic outbreaks, such as Covid-19, can cause psychological distress for health workers. This research explored the psychosocial experiences of healthcare workers caring for Covid-19 patients in Kosovo. From June 21 to February 9, 2022, we enrolled 21 physicians and 52 general and head nurses (in total 73 participants) who provided care for Covid-19 patients in secondary and tertiary health care institutions around Kosovo. The interviews were conducted face-to-face. Thematic analysis was used as a method for the analysis of interview transcripts. The psychological experiences of physicians and nurses caring for Covid-19 patients can be summarised into four main themes. First, fear of being contaminated with Covid-19 in the workplace and fear of infecting family members. The second, fear due to the unknown nature of the disease. Third, emotional distress from delivering bad news to the patients and families. And fourth, the death of Covid-19 patients. The research found out that the healthcare workers in Kosovo experienced psychological distress, i.e., feeling overwhelmed, helpless, guilty, fearful, and anxious while providing care services for patients with Covid-19.", "Keywords": "Covid-19 ; healthcare workers ; Kosovo ; patients ; psychological distress", "DOI": "10.1016/j.ifacol.2022.12.056", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor. Faculty of Psychology, University for Business and Technology (UBT), Prishtinë, Kosovo"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Assistant Professor. Faculty of Psychology, University for Business and Technology (UBT), Prishtinë, Kosovo"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lecturer. Faculty of Psychology, University for Business and Technology (UBT), Prishtinë, Kosovo"}], "References": []}, {"ArticleId": 105366106, "Title": "Cost Oriented Cyber-Physical System algorithm for pig farm microclimate and air quality control", "Abstract": "The article describes the possibility of using and the principle of operation of a Cost Oriented Cyber-Physical System (COCPS) for intelligent microclimate and air quality control in pig farms. The proposed cyber-physical system is based on open-source software in order to reduce the initial cost for the farmers. The parameters to be monitored and controlled are described. The described algorithm for the cyber-physical system, takes timely action before unfavorable conditions for the farmed animals are reached, thus provides optimal and healthy environment for the animals, increases productivity and helps achieving lower energy consumption of the buildings and optimal amounts of food intake of the animals.", "Keywords": "Cyber-physical systems ; automation algorithms ; farm automation ; smart farming ; OpenHAB", "DOI": "10.1016/j.ifacol.2022.12.047", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information and Communication Technologies, Bulgarian Academy of Sciences, Sofia, Bulgaria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Information and Communication Technologies, Bulgarian Academy of Sciences, Sofia, Bulgaria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information and Communication Technologies, Bulgarian Academy of Sciences, Sofia, Bulgaria"}], "References": []}, {"ArticleId": 105366110, "Title": "Identification and Measures to Eliminate Delays in the Construction Sector in Kosovo", "Abstract": "Since 1999, the Construction sector in Kosovo has followed a great development and is considered an important sector of economic development, where we now have more than 3400 Construction companies operating in Kosovo. In the construction sector in Kosovo, whether in the public or private sector, there are delays and non-compliance with the dynamic plans approved in the implementation of contracted projects. Delays in implementing various projects directly affect the Kosovo economy in general. During this research are analyzed 128 questionnaires realized by various construction companies that operate in different municipalities of Kosovo and carry out various construction works.", "Keywords": "Delays ; construction ; factors ; critical ; employer", "DOI": "10.1016/j.ifacol.2022.12.040", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UBT – Higher Education Institution – Prishtinë Kosovë"}], "References": []}, {"ArticleId": 105366111, "Title": "Digital literacy during the coronavirus pandemic in older adults: Literature Review and Research Agenda", "Abstract": "Demographic change in the developed world is leading to a higher proportion of older adults and longer life expectancy. Measures to control the coronavirus disease have affected older adults the most. Social isolation and access to remote health services has been a problem for many people. We have used the method of scientific literature review. The selection of articles was made in accordance with the following inclusion criteria: accessibility, scientificity, content relevance and topicality. After selection, the results were analysed by qualitative content analysis. With the content analysis of twenty scientific articles, we gained an insight into digital literacy of older adults during the COVID-19 pandemic. Three content categories were identified: (1) poor digital literacy of older adults, (2) inequality in ICT access, (3) use of ICT reduces the negative impact of social isolation. We note that there is a large digital divide in digital literacy and competences among older adults which expanded during the coronavirus disease pandemic. Several factors, including socio-economic status, internet access and the poor adaptation of ICT for older adults affect digital literacy. Rapid development of remote health and social care, poor digital literacy of older adults and the poor adaptation of ICT for older adults dictate that the problem must be tackled systemically.", "Keywords": "older adults ; ICT ; digital literacy ; COVID-19 ; digital divide", "DOI": "10.1016/j.ifacol.2022.12.027", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Assist. <PERSON><PERSON><PERSON>", "Affiliation": "Alma Mater Europaea - ECM, Research Institute of Social Gerontology, Slovenska ulica 17, 2000 Maribor, Slovenia"}, {"AuthorId": 2, "Name": "Prof. Ddr. <PERSON>", "Affiliation": "Alma Mater Europaea - ECM, Research Institute of Social Gerontology, Slovenska ulica 17, 2000 Maribor, Slovenia"}, {"AuthorId": 3, "Name": "Assist. Prof. Dr. <PERSON>", "Affiliation": "Alma Mater Europaea - ECM, Research Institute of Social Gerontology, Slovenska ulica 17, 2000 Maribor, Slovenia"}, {"AuthorId": 4, "Name": "Assist. Prof. Dr. <PERSON>", "Affiliation": "Alma Mater Europaea - ECM, Research Institute of Social Gerontology, Slovenska ulica 17, 2000 Maribor, Slovenia"}], "References": [{"Title": "Smart Lifetime Neighbourhoods: Literature Review and Research Agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "16902", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Older Adults Knowledge about using smart technology during the Covid-19 crisis-a qualitative pilot study", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "675", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "The use of ICT in older adults strengthens their social network and reduces social isolation: Literature Review and Research Agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "645", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Mobile apps supporting people with dementia and their carers: Literature Review and Research Agenda", "Authors": "<PERSON><PERSON>; Sanja <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "663", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": *********, "Title": "Energy-efficient PEGASIS Clustering based multipath routing with Lion Swarm and Chicken swarm optimization (PEGA-LSCS) for Wireless Sensor Network", "Abstract": "Due to the popularity gain in the wide research area in different field of application wireless sensor network (WSN) have been subject to deployed in the last decades.WSN due to use in harsh environment difficult to exchange batteries physically. For that reason Energy efficiency acts as a major factor to restoration of battery power in sensor network. The proposed algorithm prolong the lifetime of the sensor network by enhancing load distributions in the WSN.This work used the chain-based routing PEGASIS (Power Energy Gathering in sensor information system.) as basis. In this approach optimal path model Lion swarm and chicken swarm in PEGASIS based chain clustering (PEGA-LSCS) has been used. The optimal path is chosen by using Lion Swarm Optimization (LSO) and to reduce the energy consumption of the network the Enhanced Clustering approach is initiated using Chicken Swarm Optimization (CSO).As a result the noticeably improvement of lifetime in this approach has been showing. Other results like residual energy, packet delivery ratio and end to end delay are also considered. © 2022, Engg Journals Publications. All rights reserved.", "Keywords": "Clustering; CSO; LSO; PEGASIS; WSN", "DOI": "10.21817/indjcse/2022/v13i6/*********", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 54293, "JournalTitle": "Indian Journal of Computer Science and Engineering", "ISSN": "0976-5166", "EISSN": "2231-3850", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering and applications, Indira Gandhi Institute of Technology, Sarang, Odisha, Dhenkanal, 759146, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering and applications, Indira Gandhi Institute of Technology, Sarang, Odisha, Dhenkanal, 759146, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering and applications, Indira Gandhi Institute of Technology, Sarang, Odisha, Dhenkanal, 759146, India"}], "References": []}, {"ArticleId": 105366325, "Title": "11th Ada Developer Room at FOSDEM 2022", "Abstract": "<p>FOSDEM is a huge open source event organized each year in Brussels, Belgium. Among others, it features dozens of tracks on specific topics: the Developer Rooms. This year, for the 11th time, there was a track about the Ada programming language and related technologies. A brief overview of the full-day \"Ada DevRoom\" program is given, followed by reports of most presentations by their respective authors.</p>", "Keywords": "ada; fosdem; free and open source", "DOI": "10.1145/3577949.3577951", "PubYear": 2022, "Volume": "42", "Issue": "1", "JournalId": 12096, "JournalTitle": "ACM SIGAda Ada Letters", "ISSN": "1094-3641", "EISSN": "1557-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "KU Leuven, Leuven, Belgium"}], "References": []}, {"ArticleId": 105366326, "Title": "Citational practices as a site of resistance and radical pedagogy", "Abstract": "<p>Discursive infrastructures are forms of writing that remain mostly invisible but shape higher-level practices built upon their base. This article argues that citational practices are a form of discursive infrastructure that are bases that shape our work. Most importantly, we argue that the infrastructural base built through citation practices is in a moment of breakdown as increasing amounts of people call for more just citational practices that surface multiply marginalized and underrepresented (MMU) scholar voices. Consequently, this article both theorizes citations as infrastructure while also focusing on a case study of the MMU scholar database to help build a more equitable and socially just disciplinary infrastructure.</p>", "Keywords": "DEI; citations; discourse; infrastructure; pedagogy; social justice", "DOI": "10.1145/3507870.3507872", "PubYear": 2022, "Volume": "10", "Issue": "3", "JournalId": 75910, "JournalTitle": "Communication Design Quarterly", "ISSN": "2166-1642", "EISSN": "2166-1642", "Authors": [{"AuthorId": 1, "Name": "Cana Uluak <PERSON>aqiyaq", "Affiliation": "Virginia Tech"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Clemson University"}], "References": []}, {"ArticleId": 105366376, "Title": "Modification of the Confidence Bar Algorithm Based on Approximations of the Main Diagonal of the Hessian Matrix for Solving Optimal Control Problems", "Abstract": "<p> The paper proposes an approach to the study of the standard optimal control problem based on the use of reduction to a finite-dimensional optimization problem with subsequent use of approximation of the main diagonal of the Hessian. The results of computational experiments on solving auxiliary optimization problems for separable, quasiseparable, and <PERSON><PERSON>ck–<PERSON> functions are presented. </p>", "Keywords": "confidence bar algorithm; quasiseparable function; Hessian matrix; optimal control problem", "DOI": "10.1134/S00051179220100113", "PubYear": 2022, "Volume": "83", "Issue": "10", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Matrosov Institute for System Dynamics and Control Theory, Siberian Branch, Russian Academy of Sciences, Irkutsk, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> S. Aniki<PERSON>", "Affiliation": "Matrosov Institute for System Dynamics and Control Theory, Siberian Branch, Russian Academy of Sciences, Irkutsk, Russia"}, {"AuthorId": 3, "Name": "T. S. Zarodnyuk", "Affiliation": "Matrosov Institute for System Dynamics and Control Theory, Siberian Branch, Russian Academy of Sciences, Irkutsk, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Matrosov Institute for System Dynamics and Control Theory, Siberian Branch, Russian Academy of Sciences, Irkutsk, Russia"}], "References": []}, {"ArticleId": 105366395, "Title": "Real-Time Person Identification by Video Image Based on YOLOv2 and VGG 16 Networks", "Abstract": "<p> This paper deals with the problem of video-based face recognition. Nowadays, facial recognition methods have made a big step forward, but video-based recognition with its poor quality, difficult lighting conditions, and real-time requirements is still a difficult and unfinished task. </p><p>The paper uses the apparatus of convolutional networks for various stages of processing: for capturing and detecting a face, for constructing a feature vector, and finally for recognition. All algorithms are implemented and studied in the Matlab environment to simplify their further export to embedded applications. </p>", "Keywords": "VGG16 convolutional neural network; face recognition; YOLOv2 object detection algorithm; deep learning; face database", "DOI": "10.1134/S00051179220100095", "PubYear": 2022, "Volume": "83", "Issue": "10", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Bauman Moscow State Technical University, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bauman Moscow State Technical University, Moscow, Russia"}], "References": []}, {"ArticleId": 105366796, "Title": "Microforce Sensing and Flexible Assembly Method for Key Parts of ICF Microtargets", "Abstract": "<p>Microassembly is one of the key techniques in various advanced industrial applications. Meanwhile, high success rates for axial hole assembly of thin-walled deep-cavity-type items remain a challenging issue. Hence, the flexible assembly approach of thin-walled deep-cavity parts is investigated in this study using the assembly of the key components, the microtarget component TMP (thermomechanical package) and the hohlraum in ICF (inertial confinement fusion) research, as examples. A clamping force-assembly force mapping model based on multisource microforce sensors was developed to overcome the incapacity of microscopic vision to properly identify the condition of components after contact. The ICF microtarget flexible assembly system, which integrates multisource microforce sensing and a six degrees of freedom micromotion sliding table, is presented to address the constraint that the standard microassembly approach is difficult to operate once the parts contact. This method can detect contact force down to the mN level, modify deviation of the component posture efficiently, and achieve nondestructive ICF microtarget assembly at the end.</p>", "Keywords": "microassembly; microforce perception; flexible assembly; thin-walled deep-cavity parts microassembly ; microforce perception ; flexible assembly ; thin-walled deep-cavity parts", "DOI": "10.3390/act12010001", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Institute of Advanced Manufacturing Technology, Soochow University, Suzhou 215000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Institute of Advanced Manufacturing Technology, Soochow University, Suzhou 215000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Research Institute of Advanced Manufacturing Technology, Soochow University, Suzhou 215000, China; Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>ing <PERSON>", "Affiliation": "Research Institute of Advanced Manufacturing Technology, Soochow University, Suzhou 215000, China"}], "References": []}, {"ArticleId": 105366894, "Title": "The Future of TECIS", "Abstract": "It is a tradition to present on nearly each IFAC World Congress an update of this dynamic TC. Therefore, in this contribution the role of process and manufacturing automation especially control engineering will be presented and discussed based on previous papers. In addition, Ethics and Diversity are shortly discussed. In this paper an outlook on the topics of this IFAC TC will be given from the viewpoint of control engineering and related subjects.", "Keywords": "Ethics ; Systems Theory ; Future tasks for TECIS ; Social ; ethical aspects", "DOI": "10.1016/j.ifacol.2022.12.002", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "TU Wien Institute for Mechanics and Mechatronics, Favoritenstrasse 9, A1040, Wien"}], "References": [{"Title": "The IFAC TC SWIIS Past, Present, Future", "Authors": "<PERSON><PERSON>; and <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "17421", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "The IFAC TC SWIIS Past, Present, Future", "Authors": "<PERSON><PERSON>; and <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "17421", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105366908, "Title": "The Impact of Knowledge Management and Knowledge Transfer in Growth and Innovation A Study of Business Start-Ups, Business Incubators and Business Accelerators", "Abstract": "Knowledge management is the process where employees of an organisation share their expertise, experience, knowledge, and information in their day-to-day jobs, and the transfer of this knowledge occurs in two forms: explicit knowledge and tacit knowledge. This research is based on interviews with 13 respondents, mentors of business start-ups, owners of business start-ups, and owners of business incubators or business accelerators. The study aims to investigate if knowledge transfer from business incubators to start-ups help the start-ups succeed. Moreover, the study aims to find areas of knowledge transfer from business incubators or business accelerators to business start-ups and see the effect of knowledge spillover on business start-ups&#x27; success and the most common type of knowledge transfer.", "Keywords": "Knowledge Management ; Knowledge transfer ; Business Start-Up ; Business Accelerator", "DOI": "10.1016/j.ifacol.2022.12.010", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UBT College for Business and Technology, Pristina, 10000 Kosovo"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UBT College for Business and Technology, Pristina, 10000 Kosovo"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "UBT College for Business and Technology , Pristina , 10000 Kosovo"}], "References": []}, {"ArticleId": 105366911, "Title": "The role of virtual reality in rehabilitation of patients with Parkinson's disease: Literature Review and Research Agenda", "Abstract": "Parkinson&#x27;s disease (PD) is the second most common neurodegenerative disorder. The disease is progressive in nature, and there is no cure to this day. Virtual reality (VR), exergaming or serious games are implemented in the therapeutic processes and are considered a beneficial rehabilitation approach. In this study, we reviewed the scientific literature on the effects of the implementation of virtual reality in rehabilitating patients with Parkinson&#x27;s disease. The search was conducted through two databases, PubMed, and Scopus. The following keywords were used in search of titles, abstracts, and keywords of papers, in different combinations: Parkinson&#x27;s disease, virtual reality, virtual reality exposure therapy, exergame, and rehabilitation. We selected nine articles for the final review. Most studies showed positive results from VR-based rehabilitation methods and better results than control groups that received traditional rehabilitation methods. However, the small sample size and lack of a follow-up period limit the usefulness of the results.", "Keywords": "Parkinson's disease ; Parkinson ; virtual reality ; virtual reality therapy ; exergame ; rehabilitation", "DOI": "10.1016/j.ifacol.2022.12.024", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Applied Health Sciences, 10000 Zagreb, Croatia;Alma Mater Europaea - ECM, 2000 Maribor, Slovenia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Applied Health Sciences, 10000 Zagreb, Croatia;Alma Mater Europaea - ECM, 2000 Maribor, Slovenia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Alma Mater Europaea - ECM, 2000 Maribor, Slovenia"}], "References": []}, {"ArticleId": 105367107, "Title": "Tea-cake CBIR: a tea-cake content-based image retrieval model with rich and intensive feature extraction", "Abstract": "<p>Tea-cake content-based image retrieval (CBIR) is an essential issue in tea-cake traceability. Popular CBIR methods work on medical and social networking, whereas tea-cake images have intensive and parallel representations among classes. While facing tea-cake CBIR, low inter-class and high intra-class distances increase retrieval difficulties using traditional CBIR methods. Thus, this paper proposes a tea-cake CBIR approach based on deep neural networks to retrieve tea-cakes. In the model, we establish a feature extraction model with designed dense blocks, where a cross-entropy loss function is explored to train the model and catch detailed features. Furthermore, to decrease intra-class and expand inter-class interval, a masked autoregressive discriminative normalization flow (MADNF) is presented to map the gained features in high dimensions to corresponding representations in Gaussian spaces. Particularly, a maximum likelihood function is developed to train MADNF for avoiding non-convergence. Extensive experiments on the tea-cake dataset show our method has significant performance compared with current competitors. Furthermore, experiments on the bird species dataset further demonstrate the effectiveness of our proposed approach.</p>", "Keywords": "Tea-cake image retrieval; Convolutional neural networks; Feature distance measurement; Normalization flow", "DOI": "10.1007/s00371-022-02685-x", "PubYear": 2023, "Volume": "39", "Issue": "11", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Army Logistics Academy, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing University of Science and Technology, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Army Logistics Academy, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Chongqing Vocational Institute of Engineering, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing University of Science and Technology, Chongqing, China"}], "References": [{"Title": "HDR image retrieval by using color-based descriptor and tone mapping operator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "6", "Page": "1111", "JournalTitle": "The Visual Computer"}, {"Title": "ESA: a hybrid bio-inspired metaheuristic optimization approach for engineering problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "323", "JournalTitle": "Engineering with Computers"}, {"Title": "Image Retrieval by Integrating Global Correlation of Color and Intensity Histograms with Local Texture Features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "47-48", "Page": "34875", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Retrieval of colour and texture images using local directional peak valley binary pattern", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "1569", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A deep neural network model for content-based medical image retrieval with multi-view classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "7", "Page": "1837", "JournalTitle": "The Visual Computer"}, {"Title": "A novel algorithm for global optimization: Rat Swarm Optimizer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Atulya Nagar", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8457", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "DSHPoolF: deep supervised hashing based on selective pool feature map for image retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "2391", "JournalTitle": "The Visual Computer"}, {"Title": "BEPO: A novel binary emperor penguin optimizer for automatic feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106560", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 105367152, "Title": "Global reliable data generation for imbalanced binary classification with latent codes reconstruction and feature repulsion", "Abstract": "<p>Common binary classification algorithms which learn directly from imbalanced data can lead to a bias towards the majority class. Although the over-sampling technology can solve the imbalance problem, the realness of the synthesized samples cannot be guaranteed. Generative Adversarial Networks can improve the authenticity of the generated samples. However, it may cause mode collapse, resulting in the data distribution space of the minority class changed after balance. A sample-level data generation method is proposed in this paper for imbalanced classification. Firstly, we present a reconstruction technique of latent codes with mutual information constraints for global data generation. The latent codes of the input sample are divided into latent vectors of key features and subordinate features respectively. We can obtain the mutated latent codes by retaining the key features’ latent vector and randomly replacing the subordinate features’ latent vector. Then the reliable similar mutation samples are generated through decoder restoration, mutual information constraint, and discriminant confrontation. In addition, the feature repulsion technique and the combination coding technique are proposed to solve the problem of feature extraction and classification for samples in overlapping areas. The former carries out supervised feature representation learning of the key features’ latent vector. The latter superimposes the reconstruction error of each dimension of the sample as a supplement for the latent vector of key features. Combined with a variety of typical base classifiers, a large number of experimental results on public datasets show that the proposed method outperforms other typical data balancing methods in F1-Measure and G-Mean.</p>", "Keywords": "Imbalanced learning; Generative adversarial networks (GANs); Latent codes reconstruction; Feature repulsion; Combination coding", "DOI": "10.1007/s10489-022-04330-5", "PubYear": 2023, "Volume": "53", "Issue": "13", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Chongqing Electric Power Company Marketing Service Center, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Chongqing Electric Power Company Marketing Service Center, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 8, "Name": "Shiyuan Fu", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing, China"}], "References": [{"Title": "Deep learning fault diagnosis method based on global optimization GAN for unbalanced data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104837", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Conditional Wasserstein generative adversarial network-gradient penalty-based approach to alleviating imbalanced data classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1009", "JournalTitle": "Information Sciences"}, {"Title": "A dynamic financial distress forecast model with multiple forecast results under unbalanced data environment", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105365", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A hybrid scheme-based one-vs-all decision trees for multi-class classification tasks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "198", "Issue": "", "Page": "105922", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An ensemble imbalanced classification method based on model dynamic selection driven by data partition hybrid sampling", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "113660", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "DBIG-US: A two-stage under-sampling algorithm to face the class imbalance problem", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114301", "JournalTitle": "Expert Systems with Applications"}, {"Title": "EUSC: A clustering-based surrogate model to accelerate evolutionary undersampling in imbalanced classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107033", "JournalTitle": "Applied Soft Computing"}, {"Title": "Classification of imbalanced hyperspectral images using SMOTE-based deep learning methods", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "114986", "JournalTitle": "Expert Systems with Applications"}, {"Title": "SMOTE-NaN-DE: Addressing the noisy and borderline examples problem in imbalanced classification by natural neighbors and differential evolution", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "223", "Issue": "", "Page": "107056", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "ADA-INCVAE: Improved data generation using variational autoencoder for imbalanced classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "3", "Page": "2838", "JournalTitle": "Applied Intelligence"}, {"Title": "Binary imbalanced data classification based on diversity oversampling by generative models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "585", "Issue": "", "Page": "313", "JournalTitle": "Information Sciences"}, {"Title": "The interaction of normalisation and clustering in sub-domain definition for multi-source transfer learning based time series anomaly detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109894", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 105367226, "Title": "JPEG Column: 91st JPEG Meeting", "Abstract": "<p>The 91st JPEG meeting was held online from 19 to 23 April 2021. This meeting saw several activities relating to holographic coding, notably the release of the JPEG Pleno Holography Call for Proposals, consolidated with the definition of the use cases and requirements for holographic coding and common test conditions that will assure the evaluation of the future proposals.</p>", "Keywords": "", "DOI": "10.1145/3577941.3577945", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 26893, "JournalTitle": "ACM SIGMultimedia Records", "ISSN": "", "EISSN": "1947-4598", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Telecomunicacoes (IT) and Universidade da Beira Interior (UBI), Portugal"}], "References": []}, {"ArticleId": 105367409, "Title": "Mobile Application with Augmented Reality Focused on the Study of Human Anatomy", "Abstract": "<p>Education in current times has taken leaps and bounds, so the use of traditional classrooms in conjunction with textbooks and static 2D images is no longer enough, even more so in the teaching of human anatomy, which requires images, didactic methods, and easy understanding. In that sense, Augmented Reality (AR) is a technological tool that takes on great importance in various areas and education is no exception. For this reason, this research describes the results of the development of an application with AR focused on helping in the study of human anatomy, being developed with ARCore technology and the Unity IDE, following the Mobile-D methodology. The results were analyzed in 3 indicators (amount of light required, time required for surface recognition, and ease of use of the application), of which in the first indicator a good performance was obtained requiring only 30 lux for recognition of illuminated flat surfaces; for the second indicator, it was obtained that the delay time is 3 seconds, which makes it acceptable; for the third indicator, it was obtained that 93% of participants interviewed in the research agreed that the application is easy to use and very easy to use. From the developed application was concluded that this, in general terms, has good performance in the sections of the amount of light required, the time required, and ease of use.</p>", "Keywords": "augmented reality;mobile-d methodology;mobile application;human anatomy;learning", "DOI": "10.3991/ijim.v16i24.34709", "PubYear": 2022, "Volume": "16", "Issue": "24", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Perú"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Perú"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad Privada del Norte"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Continental"}, {"AuthorId": 5, "Name": "<PERSON>-Carbonell", "Affiliation": "Universidad Privada Norbert Wiener"}], "References": []}, {"ArticleId": 105367616, "Title": "Coordinated scheduling of production and logistics for large-scale closed-loop manufacturing using Benders decomposition optimization", "Abstract": "This paper studies coordinated scheduling of production and logistics for a large-scale closed-loop manufacturing system by integrating its manufacturing and recycling process. In addition to the forward manufacturing process, different recycling units in reverse recycling process are also studied. A decentralized network is designed to formulate the coordinated scheduling problem as a mixed integer programming model with both binary and integer variables. As the problem for closed-loop manufacturing is large-scale and computational-consuming in nature, the model is divided into integer variable sub-models and complex binary variable sub-models for preprocessing and reprocessing respectively. An iterative solution approach by Benders decomposition is developed to accelerate the solving efficiency in large-scale case by updating custom constraints. A case study is conducted to investigate the managerial implications of the decentralized network for the closed-loop manufacturing system. Computational experiments demonstrate the validity and efficiency of the proposed iterative solution approach for the large-scale scenarios.", "Keywords": "Closed-loop manufacturing ; Mixed-integer linear programming ; Recycling process ; Manufacturing sustainability ; Iterative decomposition", "DOI": "10.1016/j.aei.2022.101848", "PubYear": 2023, "Volume": "55", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Intelligence Science and Engineering, Shenzhen Polytechnic, Shenzhen 518055, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information and Control Engineering, Liaoning Petrochemical University, Fushun 113001, China;Institute of Intelligence Science and Engineering, Shenzhen Polytechnic, Shenzhen 518055, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, Liaoning Petrochemical University, Fushun 113001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical, Energy and Power Engineering, Yangzhou University, Yangzhou 225009, China"}, {"AuthorId": 5, "Name": "Xinchao Li", "Affiliation": "Institute of Intelligence Science and Engineering, Shenzhen Polytechnic, Shenzhen 518055, China"}], "References": [{"Title": "Energy-Constrained Multi-UAV Coverage Path Planning for an Aerial Imagery Mission Using Column Generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "1", "Page": "125", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Economic modelling of robotic disassembly in end-of-life product recovery for remanufacturing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106339", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Scheduling of flexible manufacturing plants with redesign options: A MILP-based decomposition algorithm and case studies", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "106777", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "A larger pie or a larger slice? Contract negotiation in a closed-loop supply chain with remanufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106377", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Remanufacturing in a competitive market: A closed-loop supply chain in a Stackelberg game framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113655", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Balancing of two-sided disassembly lines: Problem definition, MILP model and genetic algorithm approach", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "124", "Issue": "", "Page": "105064", "JournalTitle": "Computers & Operations Research"}, {"Title": "Heuristic solution methods for the selective disassembly sequencing problem under sequence-dependent costs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "127", "Issue": "", "Page": "105151", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 105367675, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S2666-6294(22)00016-X", "PubYear": 2022, "Volume": "7", "Issue": "", "JournalId": 75569, "JournalTitle": "Graphics and Visual Computing", "ISSN": "2666-6294", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105367686, "Title": "“Cut out the middleman” – Automating Business Processes with Blockchains and Smart Contracts", "Abstract": "Competence in data management is fundamental for the survival of companies. In this paper we go beyond topics like data integration, data quality, data governance or data wrangling. The notion is to outline the conceptual approaches of blockchain technology and smart contracts as an innovative approach to sharing data and automating contractual processes between interested parties. Cryptocurrencies - with bitcoin the first and most prominent one – have become highly relevant on the financial markets. The underlying technology for crypto money follows a distributed ledger concept called blockchain. However, blockchain technology today amounts to much more than storing virtual money, having gained new ground with the capability to implement so-called smart contracts, i.e., software code as part of the chain. In this discussion we outline the concept of distributed ledgers and blockchain and outline fundamentals of smart contracts. Finally, proof of concepts and first applications exemplify the options that companies now have to enhance their business.", "Keywords": "Innovation Management ; Intelligent Systems ; Applications", "DOI": "10.1016/j.ifacol.2022.12.079", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Dr. <PERSON><PERSON>", "Affiliation": "QuinScape GmbH Wittekindstr. 30, 44139 Dortmund, Germany"}], "References": [{"Title": "What Do We Mean by Smart Contracts? Open Challenges in Smart Contracts", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "", "Page": "45", "JournalTitle": "Frontiers in Blockchain"}, {"Title": "Data Strategy and Data Trust – Drivers for Business Development", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "8", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105367840, "Title": "Adaptive FEM for Helmholtz Equation with Large Wavenumber", "Abstract": "<p>A posteriori upper and lower bounds are derived for the linear finite element method (FEM) for the <PERSON><PERSON>holtz equation with large wavenumber. It is proved rigorously that the standard residual type error estimator seriously underestimates the true error of the FE solution for the mesh size h in the preasymptotic regime, which is first observed by <PERSON><PERSON><PERSON> et al. (Int J Numer Methods Eng 40:3443–3462, 1997) for a one dimensional problem. By establishing an equivalence relationship between the error estimators for the FE solution and the corresponding elliptic projection of the exact solution, an adaptive algorithm is proposed and its convergence and quasi-optimality are proved under the condition that \\(k^3h_0^{1+\\alpha }\\) is sufficiently small, where k is the wavenumber, \\(h_0\\) is the initial mesh size, and \\(\\frac{1}{2}<\\alpha \\le 1\\) is a regularity constant depending on the maximum reentrant angle of the domain. Numerical tests are given to verify the theoretical findings and to show that the adaptive continuous interior penalty finite element method (CIP-FEM) with appropriately selected penalty parameters can greatly reduce the pollution error and hence the residual type error estimator for this CIP-FEM is reliable and efficient even in the preasymptotic regime. </p>", "Keywords": "Adaptive FEM; Convergence; Quasi-optimality; He<PERSON><PERSON>tz equation with large wavenumber; 65N12; 65N15; 65N30; 78A40", "DOI": "10.1007/s10915-022-02074-5", "PubYear": 2023, "Volume": "94", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Nanjing University, Jiangsu, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Nanjing University, Jiangsu, People’s Republic of China"}], "References": []}, {"ArticleId": 105367896, "Title": "A Systematic Review of Mobile Learning Trends in Supporting the Mastery of Spelling", "Abstract": "<p>Teaching and learning to spell forms the basis for mastering English Language. The emerging mobile learning trends in the current 21st century education is useful in teaching and learning spelling. However, there were limited reviews about them. As such, a systematic review was conducted to provide an up-to-date synthesis on the mobile learning trends in supporting the mastery of spelling from year 2012 to year 2022. This review adhered to Preferred Reporting Items for Systematic Reviews and Meta-Analyses which is also known as the PRISMA guideline. Three databases were involved: Web of Science, Scopus and Educational Resources Information Centre. After a rigorous screening process, a total of 19 articles fitted its objectives. Firstly, significant findings from the studies portrayed the use of four platforms in mobile learning trends in supporting the mastery of spelling namely mobile apps, spell-checker apps, word prediction apps and text messaging apps. Secondly, the primary level of education was more dominant in the related studies than the secondary and tertiary levels. Thirdly, it emphasised more on the students with a lower prevalence on teachers and both teachers and students as the respondents in the associated studies. This paper contributes to systematically reviewing 19 eligible related studies through a step-by-step protocol of identification, screening, eligibility and included as in the PRISMA guideline to establish quality review. It benefits both the English Language teachers and students in utilising various mobile learning platforms to support the mastery of spelling at all levels of education. It aspires to foreground the paucity in this topic in which more substantial studies are required to address the research gaps. The ultimate goal is to impose effective and practical strategies to advance mobile learning trends in supporting the mastery of spelling.</p>", "Keywords": "English Language;21st century education;Mobile learning trends;Mastery of spelling;Systematic review", "DOI": "10.3991/ijim.v16i24.33633", "PubYear": 2022, "Volume": "16", "Issue": "24", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universiti Kebangsaan Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Kebangsaan Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Kebangsaan Malaysia"}], "References": []}, {"ArticleId": 105367909, "Title": "Fault Tolerant Control Based on Thau Observer of a Reconfigurable Quadrotor with Total Loss of Actuator", "Abstract": "<p>In this paper, we propose a Fault Tolerant Control (FTC) strategy for a reconfigurable quadrotor with foldable arms, to deal with a total loss of one of its rotors. The proposed strategy allows adapting the configuration of the quadrotor when one of its rotors is lost. It consists of: (i) a Fault Detection and Isolation (FDI) module, (ii) a robust controller, and (iii) a reconfiguration module. The FDI module is designed based on the nonlinear Thau observer in order to detect and identify the rotors faults. Once a rotor fault is detected based on the residual analysis, the quadrotor changes its shape by rotating the two adjacent arms in the direction of damaged one in order to form a trirotor configuration. This transformation induces a variation of vehicle’s center of gravity (CoG), inertia, and consequently the control matrix. To deal with this issue, a sliding mode controller (SMC) is designed based on the control allocation matrix, where the control efforts are redistributed among healthy actuators. Numerical simulations are carried out to verify the effectiveness of the proposed approach. The obtained results show that the proposed strategy is successful in controlling the damaged quadrotor by trajectory tracking.</p>", "Keywords": "Reconfigurable quadrotor; trajectory tracking; active FTC; nonlinear Thau observer; sliding mode control; foldable arms; rotor total loss of effectiveness; control allocation", "DOI": "10.1142/S2301385024500146", "PubYear": 2024, "Volume": "12", "Issue": "4", "JournalId": 26691, "JournalTitle": "Unmanned Systems", "ISSN": "2301-3850", "EISSN": "2301-3869", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Complex Systems Control and Simulators Laboratory, Ecole Militaire Polytechnique, Algiers, Algeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Complex Systems Control and Simulators Laboratory, Ecole Militaire Polytechnique, Algiers, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Complex Systems Control and Simulators Laboratory, Ecole Militaire Polytechnique, Algiers, Algeria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Complex Systems Control and Simulators Laboratory, Ecole Militaire Polytechnique, Algiers, Algeria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Process Control Laboratory, Ecole Nationale Polytechnique, Algiers, Algeria"}], "References": [{"Title": "Sliding-Mode Disturbance Observer-Based Control for Fractional-Order System with Unknown Disturbances", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "193", "JournalTitle": "Unmanned Systems"}, {"Title": "Data-based Distributed Fault Diagnosis for Adaptive Structures using Convolutional Neural Networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "221", "JournalTitle": "Unmanned Systems"}, {"Title": "Trajectory Following with a MAV Under Rotor Fault Conditions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "4", "Page": "263", "JournalTitle": "Unmanned Systems"}, {"Title": "Fault Diagnosis and Reconfiguration for Mobile Robot Localization Based on Multi-Sensors Data Fusion", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "1", "Page": "69", "JournalTitle": "Unmanned Systems"}]}, {"ArticleId": 105367981, "Title": "ALP: Alleviating CPU-Memory Data Movement Overheads in Memory-Centric Systems", "Abstract": "Partitioning applications between near-data processing (NDP) and host CPU cores causes inter-segment data movement overhead, which is caused by moving data generated by one segment (e.g., instructions, functions) and used in other consecutive segments. Prior works take two approaches to this problem. The first approach maps segments to NDP or host cores based on the properties of each segment, neglecting the inter-segment data movement overhead. The second approach partitions applications based on the overall memory bandwidth savings, and does not offload each segment to the best-fitting core if they incur high inter-segment data movement. We show that 1) mapping each segment to its best-fitting core ideally can provide substantial benefits, and 2) the inter-segment data movement reduces this benefit significantly. We introduce ALP, a new programmer-transparent technique to alleviate the inter-segment data movement overhead between host and memory in NDP systems. ALP proactively and accurately transfers the required data between the segments based on the key observation that the instructions that generate the inter-segment data stay the same across different executions of a program. ALP uses a compiler pass to identify these instructions and uses specialized hardware to transfer their produced data at runtime. We evaluate ALP across a wide range of workloads and demonstrate 54.3% and 45.4% average speedup over CPU-only and NDP-only executions, respectively.", "Keywords": "Near-data processing;inter-segment data movement;application partitioning", "DOI": "10.1109/TETC.2022.3226132", "PubYear": 2023, "Volume": "11", "Issue": "2", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Toronto, Toronto, ON, Canada"}, {"AuthorId": 3, "Name": "Geraldo F. Oliveira", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Architecture, University of Malaga, Málaga, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology and Electrical Engineering (D-ITET), ETH Zurich, Zürich, Switzerland"}], "References": []}, {"ArticleId": 105367983, "Title": "The Ada Numerics Model", "Abstract": "<p>This paper describes the challenges of making portable calculations across different architectures, and how the Ada model addresses the issues.</p>", "Keywords": "ada; fixed points; floating points; numerics", "DOI": "10.1145/3577949.3577953", "PubYear": 2022, "Volume": "42", "Issue": "1", "JournalId": 12096, "JournalTitle": "ACM SIGAda Ada Letters", "ISSN": "1094-3641", "EISSN": "1557-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Adalog, Issy-Les-Moulineaux, France"}], "References": []}, {"ArticleId": 105367991, "Title": "Introduction", "Abstract": "<p>This article is the introduction to the second of two Communication and Design Quarterly special issues focused on conceptualizations of infrastructure. While there are more continuities than differences between the themes and methodologies of articles in the first and second issues, this second issue leans towards articles that have taken up infrastructure as it pertains to writing and rhetoric. This introduction frames the value of infrastructure as a metaphor for making visible how writing and rhetoric structure and enact much of our world, especially for writing pedagogy. In addition, this article concludes by introducing the six contributions in this issue.</p>", "Keywords": "infrastructure; technical communication; writing studies", "DOI": "10.1145/3507870.3507871", "PubYear": 2022, "Volume": "10", "Issue": "3", "JournalId": 75910, "JournalTitle": "Communication Design Quarterly", "ISSN": "2166-1642", "EISSN": "2166-1642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Portland State University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Clemson University"}], "References": []}, {"ArticleId": 105368133, "Title": "Adaptive end‐effector pose control for tomato harvesting robots", "Abstract": "<p>This paper investigates the development of a tomato-harvesting robot operating on a plant factory and primarily studies the reachable pose of tomatoes in the nondexterous workspace of manipulator. The end-effector can only reach the tomatoes with reachable poses when the tomatoes are within the nondexterous workspace. If the grasping pose is not reachable, it will lead to grasping failure. An adaptive end-effector pose control method based on a genetic algorithm (GA) is proposed to find a reachable pose. The inverse kinematic solution based on analysis method of the manipulator is analyzed and the objective function of whether the manipulator has a solution or not is obtained. The grasping pose is set as an individual owing to the position of the tomatoes is fixed and the grasping pose is variable. The GA is used to solve until a pose that can make the inverse kinematics have a solution is generated. This pose is the reachable grasping pose of the tomato at this position. The quintic interpolation polynomial is used to plan the trajectory to avoid damage to tomatoes owing to fast approaching speed and a distance based background filtering method is proposed. Experiments were performed to verify the effectiveness of the proposed method. The radius of the workspace of the UR3e manipulator with the end-effector increased from 550 to 800 mm and the grasping range expanded by 208%. The harvesting success rate using the adaptive end-effector pose control method and trajectory planning method was 88%. The cycle of harvesting a tomato was 20 s. The experimental results indicated that the proposed tomato-recognition and end-effector pose control method are feasible and effective.</p>", "Keywords": "end-effector pose control;harvesting robot;manipulator;nondexterous workspace", "DOI": "10.1002/rob.22146", "PubYear": 2023, "Volume": "40", "Issue": "3", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The School of Control Science and Engineering Dalian University of Technology Dalian China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The School of Control Science and Engineering Dalian University of Technology Dalian China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The School of Control Science and Engineering Dalian University of Technology Dalian China"}, {"AuthorId": 4, "Name": "Dongbing Gu", "Affiliation": "The School of Computer Science and Electronic Engineering University of Essex Colchester UK"}], "References": [{"Title": "Development of a sweet pepper harvesting robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "6", "Page": "1027", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Performance improvements of a sweet pepper harvesting robot in protected cropping environments", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "7", "Page": "1197", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 105368207, "Title": "SDAC - SECURE DATA STORAGE AND POSITION BASED ACCESS CONTROL IN CLOUD USING KEY SHARING SYSTEM", "Abstract": "A health record is viewed as an essential component for managing and saving patients facts. Because the data will be accessed through interoperable devices and various owners, privacy and security are the most important processes to shield the health records in the public cloud platform. In the proposed, SDAC – secure data storage and position-based access control in cloud using key sharing system-patients manage and securely send their medical records in a unified manner in order to readily access the data in the cloud platform. Furthermore, before transferring the data to storage, each record was encrypted through chipertext and identity-based encryption mechanisms, as well as privacy access method also enabled. The data is kept by plentiful tables that are connected together depending on attributes and the tables that are reserved up to date are stated to as source tables. The source table is used to create the shared database instance for the cloud tenants. These shared tables and shared database instances STSI structures are created by combining data from numerous tables. During the download, the data is collected from the STSI table. Additionally, data between renters is mapped using the shared nearest neighbour method. During data storage and retrieval operations, encryption and decryption processes are carried out; the ciphertext is generated from the plaintext using the polynomial, encryption key, hash function, and policy. At the end of the decryption process, the secret key is revealed to obtain the plaintext. © 2022, Engg Journals Publications. All rights reserved.", "Keywords": "Attribute, Connected Tables; Decryption; Encryption; Privacy; Storage", "DOI": "10.21817/indjcse/2022/v13i6/221306027", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 54293, "JournalTitle": "Indian Journal of Computer Science and Engineering", "ISSN": "0976-5166", "EISSN": "2231-3850", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Dattatray Dharma", "Affiliation": ""}, {"AuthorId": 2, "Name": "Dr <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105368277, "Title": "MSSRGO: A multimeta-model-based global optimization method using a selection-rank-based infill sampling strategy", "Abstract": "In recent years, multimeta-model-based global optimization (MMGO) methods have received increasing attention due to their good performance in solving expensive black box problems. In this article, a multimeta-model-based global optimization method using the selection-rank-based infill sampling strategy (MSSRGO) algorithm is proposed to obtain more precise solutions with satisfactory computing costs for expensive black box problems. The MSSRGO utilizes three basic meta models (kriging (KRG), radial basis function (RBF) and polynomial response surface (PRS)) to capture the complex relationship between design variables and objective functions. In each iteration, first, two reduced subspaces are identified and used alternately with the whole design space. Then, a novel selection-rank-based sampling strategy and a greedy searching strategy in promising regions are proposed to obtain new potential points. Moreover, fifteen typical benchmark functions (six low-dimensional problems and nine high-dimensional problems) are employed to test the performance of MSSRGO. Compared with one well-known (EGO) and four recent (MSEGO, MGOSIC, MDSD and EMMGO) algorithms, the numerical experiments and engineering application show that the MSSRGO has superior searching precision, the same or lower computing costs and strong robustness.", "Keywords": "Multimeta-models ; Selection-rank-based infill sampling strategy ; Greedy search ; Global optimization ; Expensive black box problem", "DOI": "10.1016/j.jksuci.2022.12.007", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Mingyang Li", "Affiliation": "Smart Transport Key Laboratory of Hunan Province, School of Transport and Transportation Engineering, Central South University, Changsha 410075, China"}, {"AuthorId": 2, "Name": "Lipen<PERSON>", "Affiliation": "Smart Transport Key Laboratory of Hunan Province, School of Transport and Transportation Engineering, Central South University, Changsha 410075, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Smart Transport Key Laboratory of Hunan Province, School of Transport and Transportation Engineering, Central South University, Changsha 410075, China;Corresponding author"}], "References": [{"Title": "An efficient multiple meta-model-based global optimization method for computationally intensive problems", "Authors": "Jichao Gu", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "102958", "JournalTitle": "Advances in Engineering Software"}, {"Title": "SGOP: Surrogate-assisted global optimization using a Pareto-based sampling strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107380", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-surrogates and multi-points infill strategy-based global optimization method", "Authors": "Pengcheng Ye; Guang Pan", "PubYear": 2023, "Volume": "39", "Issue": "2", "Page": "1617", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 105368361, "Title": "Improved Multi-Label Image Classification Performance using Supervised CNN-LSTM Deep Neural Network", "Abstract": "Deep Convolutional Neural Network (CNN) classification of single-object image labels has shown high efficiency. However, the great bulk of actual application data comprises of multiple label object images that belong to a variety of scenes, objects, and actions in a single image. Most of the recent research studies on multiple object label classification rely on individual classifiers for each label category and use probability ranking for final classification. These methods already in place work better, but they cannot find the dependencies between multiple labels in an image. In this paper, we use deep CNN architecture and long short-term memory (LSTM) to solve the problem of label dependence. Our proposed CNN-LSTM methodology learns the embeddings of object label to depict semantic object label dependence and image label association using a robust multi-label classifier cost function (RMLC), which is a ramp loss function. The feature extraction is carried out by a convolution neural network (CNN) pipeline; whereas multi-object label correlation is identified by LSTM using object labels and features extracted from input images. We use the loss function to make sure that correlated labels and corresponding features map close to each other, limiting the high value updation of weights for the images with improper labels, and the object label prediction progresses every time, which helps to improve the multi-label learning task. Experiments conducted using the proposed framework on benchmark visual recognition datasets such as CIFAR-10, STL-10, PASCAL VOC 2007, MS-COCO, and NUS-WIDE provided performance comparatively better than many existing methods in terms of accuracy and mean average precision. The CNN-LSTM + RMLC achieve the best test accuracy of 95.56 % on the STL dataset, which is 4% higher than the existing method, and the best mean average precision (mAP) of 82.6 on the MS-COCO dataset, which shows the feasibility and usefulness of our suggested framework on multiple label image classification. © 2022, Engg Journals Publications. All rights reserved.", "Keywords": "CNN; image classification; label correlation; LSTM; Multi-label image; ramp loss", "DOI": "10.21817/indjcse/2022/v13i6/221306085", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 54293, "JournalTitle": "Indian Journal of Computer Science and Engineering", "ISSN": "0976-5166", "EISSN": "2231-3850", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Lakshmi C", "Affiliation": ""}], "References": []}, {"ArticleId": 105368473, "Title": "PI PUF: A Processor-Intrinsic PUF for IoT", "Abstract": "Most IoT devices cannot offer sufficient resources for traditional cryptography technologies, posing a severe security challenge. Physical unclonable function (PUF) is a new lightweight hardware security primitive that extracts the entropy from process variations of circuits. However, the existing PUFs commonly are ASICs that require extra overhead on the area and extended instruction. To address such issues, this paper proposes a novel Processor-Intrinsic PUF (PI PUF). It exploits the pipeline design of processors and the timing dependence of data paths to generate abnormal information which carries the unclonable hardware characteristics. Moreover, a multi-round instruction obfuscation (MRIO) mechanism and the Gray code encoding method are conducted in the abnormal information to improve the uniqueness and randomness of our PUF. Finally, we design a lightweight authentication protocol based on the proposed PUF, whose security is further enhanced by shared key encryption and XOR random number encryption. Meanwhile, we also provide detailed security analysis under various attack scenarios. The Monte Carlo simulation results show that the proposed PUF achieves 50.93% of uniqueness, 50.62% of randomness, and 5.02% of the worst bit error rate, which outperforms the same type of state-of-the-art works. It also passes the NIST test.", "Keywords": "Internet of Things (IoT) ; Processor-Intrinsic PUF (PI PUF) ; RISC-V processors ; Device authentication ; Hardware security", "DOI": "10.1016/j.compeleceng.2022.108540", "PubYear": 2023, "Volume": "105", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Ningbo University, Ningbo, 315211, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical and Electronic Engineering, Wenzhou University, Wenzhou, 325035, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Ningbo University, Ningbo, 315211, China;Corresponding authors"}, {"AuthorId": 4, "Name": "Gang Li", "Affiliation": "College of Electrical and Electronic Engineering, Wenzhou University, Wenzhou, 325035, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Semiconductors (College of Integrated Circuits), Hunan University, Changsha, 410082, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Semiconductors (College of Integrated Circuits), Hunan University, Changsha, 410082, China;Innovation Institute of Industrial Design and Machine Intelligence Quanzhou-Hunan University, Hunan University, Changsha, 410082, China"}], "References": [{"Title": "DTA-PUF: Dynamic Timing-aware Physical Unclonable Function for Resource-constrained Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "3", "Page": "1", "JournalTitle": "ACM Journal on Emerging Technologies in Computing Systems"}, {"Title": "A lightweight authentication scheme with privacy preservation for vehicular networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "100", "Issue": "", "Page": "108016", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Multi-valued logic arbiter PUF designs based on CNTFETs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108295", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 105368477, "Title": "Designing Cloud-Based Gameplay Automation: Exploratory Software Testing, Game State-Analysis, and Test-Driven Development (TDD) Applied to Robotic Process Automation (RPA)", "Abstract": "", "Keywords": "", "DOI": "10.20533/ijicr.2042.4655.2022.0137", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 34574, "JournalTitle": "International Journal of Intelligent Computing Research", "ISSN": "", "EISSN": "2042-4655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105368573, "Title": "The role of system‐use practices for sustaining motivation in crowdsourcing: A technology‐in‐practice perspective", "Abstract": "<p>The success of crowdsourcing (CS) systems depends on sustained participation, which is an ongoing challenge for the majority of CS providers. Unfortunately, participants are frequently demotivated by technical difficulties and the incorrect use of CS systems, which can result in CS failure. Although the literature generally assumes that sustained participation in CS is determined by a shift between intrinsic and extrinsic motivation, the role of system-use practices in facilitating such a shift remains unknown. We explore how CS system-use practices influence participants' sustained motivation, evolving from initiation to progression to sustention. Using the notion of technology-in-practice as a lens, we develop and examine a process model using an in-depth case study of a large-scale ongoing CS project, the Australian Newspaper Digitisation Program. The findings suggest that CS participants' motivation is shaped by an evolving combination of three basic components (i.e., contextual condition, outcome and action intensity) and mediated by two types of system-use practice (i.e., passive and active). Passive-use practices facilitate sustaining motivation from initiation to progression, whereas active-use practices have a key role in sustention. Our study contributes to the emerging literature on the substantial role of system-use practices in sustaining motivation, resulting in sustained participation. The findings also offer actionable insights into improving the viability of CS systems in retaining and motivating continuous and increased contributions from participants.</p>", "Keywords": "crowdsourcing;sustained participation;sustaining motivation;system-use practices;technology-in-practice", "DOI": "10.1111/isj.12423", "PubYear": 2023, "Volume": "33", "Issue": "4", "JournalId": 7047, "JournalTitle": "Information Systems Journal", "ISSN": "1350-1917", "EISSN": "1365-2575", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems and Business Analytics Deakin University  Melbourne Australia"}, {"AuthorId": 2, "Name": "Ruonan Sun", "Affiliation": "School of Management Lancaster University  Lancaster Lancashire UK"}], "References": [{"Title": "Crowdsourcing as a strategic IS sourcing phenomenon: Critical review and insights for future research", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "4", "Page": "101593", "JournalTitle": "The Journal of Strategic Information Systems"}, {"Title": "\"Research Perspectives: Through Whose Eyes? The Critical Concept of Researcher Perspective \"", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "483", "JournalTitle": "Journal of the Association for Information Systems"}, {"Title": "Helping Yourself or Others? Motivation Dynamics for High-Performing Volunteers in GLAM Crowdsourcing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "", "Page": "1", "JournalTitle": "Australasian Journal of Information Systems"}, {"Title": "Taking Time into Account: Understanding Microworkers’ Continued Participation in Microtasks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "4", "Page": "893", "JournalTitle": "Journal of the Association for Information Systems"}]}, {"ArticleId": *********, "Title": "ASSESSMENT OF THE DETECTION CAPACITY OF NORMALIZED GAMMACHIRP CEPSTRAL COEFFICIENTS IN ANDROID MALWARE", "Abstract": "Android has grown to become the leading mobile operating system on the market due, in part, to its popularity and open-source nature. On the other hand, the Android ecosystem has become a fertile ground for malware, posing substantial security risks to ordinary mobile users. This is hardly surprising given the sheer number of devices it controls. Despite several research efforts over the years, Android malware detection remains a challenge. This is largely due to the fact that it is still unknown which features or combination of features can effectively distinguish malicious Android applications from benign ones. To this end, this research explores an Android malware detection system that uses the low-level audio Normalized Gammachirp Cepstral Coefficients as features to classify malware with machine learning techniques. First, we convert the Android Application Package datasets into audio datasets, then extract the audio features. To evaluate our approach, twenty-four machine learning algorithms were implemented, and results were collected. The experimental results show that the proposed malware features achieved the highest accuracy, precision, recall, f1-score and AUC and with respective values of 97.6%, 98.3%, 98.6%, 98.4% and 96.4%%. It also achieved 98% area under the precision-recall curve indicating that Normalized Gammachirp Cepstral Coefficients are effective for Android malware classification. Moreover, the processing and detection times were also reasonably short. Amongst the best-performing models were Extratrees, Random forest, CatBoost, XGBoost and KNeighbors. © 2022, Engg Journals Publications. All rights reserved.", "Keywords": "Android malware; Classification; malware audio; Normalized Gammachirp Cepstral Coefficients", "DOI": "10.21817/indjcse/2022/v13i6/221306102", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 54293, "JournalTitle": "Indian Journal of Computer Science and Engineering", "ISSN": "0976-5166", "EISSN": "2231-3850", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Zululand, KwaDlangezwa, 3880, South Africa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Zululand, KwaDlangezwa, 3880, South Africa"}], "References": []}, {"ArticleId": 105368741, "Title": "Retraction Note: Prediction research of financial time series based on deep learning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-022-07760-y", "PubYear": 2023, "Volume": "27", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Finance and Statistics, Hunan University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Trade, Hunan University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International and Business, Tianjin University of Finance and Economics, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business Department ESCP Europe Business School, Paris, France"}], "References": []}, {"ArticleId": 105368858, "Title": "A Brief Analysis of the SwinIR Image Super-Resolution", "Abstract": "SwinIR is a recent image restoration method based on the Swin Transformer architecture. In contrast to other traditional convolutional neural networks, SwinIR is capable of capturing sophisticated attention between image patches, leading to remarkable results. In this paper, we focus on the aspect of single-image super-resolution by SwinIR. We discuss the characteristics of the architecture of this algorithm and compare it to other deep learning methods. © 2022 IPOL & the authors.", "Keywords": "single image super-resolution; vision transformer; <PERSON><PERSON> transformer", "DOI": "10.5201/ipol.2022.430", "PubYear": 2022, "Volume": "12", "Issue": "", "JournalId": 13128, "JournalTitle": "Image Processing On Line", "ISSN": "", "EISSN": "2105-1232", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universit´e Paris-Saclay, ENS Paris-Saclay, Centre Borelli, Gif-sur-Yvette, France"}], "References": []}, {"ArticleId": 105368859, "Title": "Accelerated Bregman Primal-Dual Methods Applied to Optimal Transport and Wasserstein Barycenter Problems", "Abstract": "", "Keywords": "primal-dual method; optimal transport; Wasserstein barycenter; saddle-point; Primary: 65Y20; 49Q22; Secondary: 90C05; 90C06; 90C08; 90C47", "DOI": "10.1137/22M1481865", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 62900, "JournalTitle": "SIAM Journal on Mathematics of Data Science", "ISSN": "", "EISSN": "2577-0187", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CEREMADE, CNRS & Université Paris-Dauphine, PSL University, Paris, 91128 France."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Adolfo Ibáñez, Facultad de Ingeniería y Ciencias, 7941169 Santiago, Chile, and Departamento de Ingeniería Industrial, Universidad Católica del Norte, 1780000 Antofagasta, Chile."}], "References": []}, {"ArticleId": 105368906, "Title": "Wide‐stopband substrate‐integrated waveguide diplexers and dual‐band bandpass filter with large frequency ratios", "Abstract": "", "Keywords": "", "DOI": "10.1002/mmce.23195", "PubYear": 2022, "Volume": "32", "Issue": "7", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "Hao<PERSON>", "Affiliation": "College of Electronic and Optical Engineering Nanjing University of Posts and Telecommunications  Nanjing China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Poly‐Grames Research Center École Polytechnique de Montréal  Montréal Quebec Canada"}, {"AuthorId": 3, "Name": "Chun‐Xia Zhou", "Affiliation": "Ministerial Key Laboratory of JGMT Nanjing University of Science and Technology  Nanjing China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Ministerial Key Laboratory of JGMT Nanjing University of Science and Technology  Nanjing China"}, {"AuthorId": 5, "Name": "Li‐<PERSON>", "Affiliation": "College of Electronic and Optical Engineering Nanjing University of Posts and Telecommunications  Nanjing China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Electronic and Optical Engineering Nanjing University of Posts and Telecommunications  Nanjing China"}], "References": [{"Title": "Compact planar substrate‐integrated waveguide diplexers with wide‐stopband characteristics", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "6", "Page": "", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}]}, {"ArticleId": 105368913, "Title": "Deep contextual disambiguation of homonyms and polysemants", "Abstract": "<p>A new metric method for word sense disambiguation is put forward, being formulated within a phenomenological analogy to the wave function of an observable quantity in quantum mechanics where the actual meaning of a multivalued word (a homonym or a polysemant) is determined by its context. The choice of the actualized sense is determined by the minimal semantic distance between the semantics of possible word senses and that of the context, where the meanings of the word in question and the context follow from their semantic fields based on lexicographic hyperchains. In contrast to the common models, our method accounts for semantic polarity. The formulated method showed good results in disentangling polysemy, which was not possible to achieve within the contextualized embedding approacdh based on bidirectional encoder representations from transformers (BERT).</p>", "Keywords": "", "DOI": "10.1093/llc/fqac081", "PubYear": 2023, "Volume": "38", "Issue": "2", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Problems of Artificial Intelligence, Prospekt Akademika Ghlushkova , Kyjiv (Kiev), Ukraine;State Scientific and Technical Library of Ukraine , Kyjiv (Kiev), Ukraine"}], "References": [{"Title": "Systematic Homonym Detection and Replacement Based on Contextual Word Embedding", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "17", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 105368965, "Title": "A Systematic Review of the Literature in Dynamic Video Summarization", "Abstract": "", "Keywords": "", "DOI": "10.20533/ijmip.2042.4647.2022.0064", "PubYear": 2022, "Volume": "12", "Issue": "1", "JournalId": 36659, "JournalTitle": "International Journal of Multimedia and Image Processing", "ISSN": "", "EISSN": "2042-4647", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105369000, "Title": "Teleporting through virtual environments: benefits of navigational feedback and practice", "Abstract": "<p>Virtual environments (VEs) can be infinitely large, but movement of the virtual reality (VR) user is constrained by the surrounding real environment. Teleporting has become a popular locomotion interface to allow complete exploration of the VE. To teleport, the user selects the intended position (and sometimes orientation) before being instantly transported to that location. However, locomotion interfaces such as teleporting can cause disorientation. This experiment explored whether practice and feedback when using the teleporting interface can reduce disorientation. VR headset owners participated remotely. On each trial of a triangle completion task, the participant traveled along two path legs through a VE before attempting to point to the path origin. Travel was completed with one of two teleporting interfaces that differed in the availability of rotational self-motion cues. Participants in the feedback condition received feedback about their pointing accuracy. For both teleporting interfaces tested, feedback caused significant improvement in pointing performance, and practice alone caused only marginal improvement. These results suggest that disorientation in VR can be reduced through feedback-based training.</p>", "Keywords": "Virtual reality; Locomotion interface; Teleporting; Navigation; Feedback; Remote data collection", "DOI": "10.1007/s10055-022-00737-0", "PubYear": 2023, "Volume": "27", "Issue": "2", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Iowa State University, Ames, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Virtual Reality Applications Center, Iowa State University, Ames, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Virtual Reality Applications Center, Iowa State University, Ames, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Virtual Reality Applications Center, Iowa State University, Ames, USA"}], "References": [{"Title": "Boundaries Reduce Disorientation in Virtual Reality", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "78", "JournalTitle": "Frontiers in Virtual Reality"}]}, {"ArticleId": 105369036, "Title": "Correction to: Manifold learning for novelty detection and its application in gesture recognition", "Abstract": "", "Keywords": "", "DOI": "10.1007/s40747-022-00951-y", "PubYear": 2023, "Volume": "9", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The School of Software Engineering, Jinling Institute of Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Laboratory Center for Basic Medical Sciences, Nanjing Medical University, Nanjing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The School of Computer Engineering, Jinling Institute of Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The School of Computer Engineering, Jinling Institute of Technology, Nanjing, People’s Republic of China"}], "References": []}, {"ArticleId": 105369117, "Title": "A Clustering Offloading Decision Method for Edge Computing Tasks Based on Deep Reinforcement Learning", "Abstract": "<p>In many IoT scenarios, the resources of terminal devices are limited, and it is difficult to provide services with low latency and low energy consumption. Mobile edge computing is an effective solution by offloading computing tasks to edge server processing. There are some problems in the existing offloading decision algorithms: the offloading decision method based on heuristic algorithms cannot dynamically adjust the policy in the changing environment; the offloading algorithm based on deep reinforcement learning will lead to slow convergence and poor exploration effect due to the problem of dimension explosion. To solve the above problems, this paper designs an offloading decision algorithm to make dynamic decisions in a mobile edge computing network with multi-device access. The algorithm comprehensively considers the energy consumption of terminal equipment, offloading overhead, average delay and success rate of task completion, aiming to achieve the highest total revenue of the whole system in a period of time. In this work, the online offloading problem is abstracted as a Markov decision process. Based on the Double Dueling Deep Q-Network (D3QN) algorithm, the offloading decision is designed to adapt to the highly dynamic environment of the edge computing network and solve the problem of high state space complexity. In addition, this paper innovatively introduces a clustering algorithm into deep reinforcement learning (DRL) to preprocess the action space and solve the explosion problem of the action space dimension caused by the increase of terminal devices. The experimental results show that the proposed algorithm is superior to the baseline strategies such as Deep Q-Network (DQN) algorithm in convergence speed and total reward.</p>", "Keywords": "Mobile edge computing; Offloading decisions; Deep reinforcement learning; Dimension explosion; Clustering algorithm", "DOI": "10.1007/s00354-022-00199-7", "PubYear": 2023, "Volume": "41", "Issue": "1", "JournalId": 6245, "JournalTitle": "New Generation Computing", "ISSN": "0288-3635", "EISSN": "1882-7055", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Electronic Engineering, Sichuan Normal University, Chengdu, China; Institute of Cyber and Communication Technology, Sichuan Normal University, Chengdu, China"}, {"AuthorId": 2, "Name": "Huanzhou Li", "Affiliation": "School of Physics and Electronic Engineering, Sichuan Normal University, Chengdu, China; Institute of Cyber and Communication Technology, Sichuan Normal University, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electronic Engineering, Sichuan Normal University, Chengdu, China; Institute of Cyber and Communication Technology, Sichuan Normal University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON>glin <PERSON>", "Affiliation": "School of Physics and Electronic Engineering, Sichuan Normal University, Chengdu, China; Institute of Cyber and Communication Technology, Sichuan Normal University, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Electronic Engineering, Sichuan Normal University, Chengdu, China; Institute of Cyber and Communication Technology, Sichuan Normal University, Chengdu, China"}], "References": [{"Title": "QoS-Aware Robotic Streaming Workflow Allocation in Cloud Robotics Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "544", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "A survey on computation offloading modeling for edge computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "169", "Issue": "", "Page": "102781", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Deep reinforcement learning based mobile edge computing for intelligent Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101184", "JournalTitle": "Physical Communication"}, {"Title": "Deep reinforcement learning: a survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "12", "Page": "1726", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "A review of edge computing: Features and resource virtualization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "155", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "A Survey on Task Offloading in Multi-access Edge Computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "102225", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": 105369125, "Title": "Contents", "Abstract": "", "Keywords": "", "DOI": "10.1016/S2405-8963(22)03125-1", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105369153, "Title": "A Big Data-Driven Intelligent Knowledge Discovery Method for Epidemic Spreading Paths", "Abstract": "<p>The prevention and control of communicable diseases such as COVID-19 has been a worldwide problem, especially in terms of mining towards latent spreading paths. Although some communication models have been proposed from the perspective of spreading mechanism, it remains hard to describe spreading mechanism anytime. Because real-world communication scenarios of disease spreading are always dynamic, which cannot be described by time-invariant model parameters, to remedy such gap, this paper explores the utilization of big data analysis into this area, so as to replace mechanism-driven methods with big data-driven methods. In modern society with high digital level, the increasingly growing amount of data in various fields also provide much convenience for this purpose. Therefore, this paper proposes an intelligent knowledge discovery method for critical spreading paths based on epidemic big data. For the major roadmap, a directional acyclic graph of epidemic spread was constructed with each province and city in mainland China as nodes, all features of the same node are dimension-reduced, and a composite score is evaluated for each city per day by processing the features after principal component analysis. Then, the typical machine learning model named XGBoost carries out processing of feature importance ranking to discriminate latent candidate spreading paths. Finally, the shortest path algorithm is used as the basis to find the critical path of epidemic spreading between two nodes. Besides, some simulative experiments are implemented with use of realistic social network data.</p>", "Keywords": "Big data; knowledge discovery; mining methods; disease spreading paths", "DOI": "10.1142/S0218126623501931", "PubYear": 2023, "Volume": "32", "Issue": "11", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Intelligent Finance and Economics, Henan Institute of Economics and Trade, Zhengzhou 450053, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Literature and Science, China University of Petroleum-Beijing at Karamay, Karamay, P. R. China"}], "References": [{"Title": "Mitigate SIR epidemic spreading via contact blocking in temporal networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}]}, {"ArticleId": *********, "Title": "Understanding Tax Evasion and Professionalism of Tax Administration in Kosovo", "Abstract": "Tax administration is the key governmental institution that is responsible for creating, maintaining, and implementing tax policies, which should be suitable for tax administration and for taxpayers. Despite willingness of tax administration to have effective tax compliance, tax inspectors are finding irregularities during tax auditing visits and those irregularities appear as a result of lack of knowledge, technical mistakes and as a result of purposeful tax evasion and tax avoidance. Tax avoidance and tax evasion as phenomena are appearing mostly in developing countries. The research focused on understanding perception of taxpayers on decision of Kosovo government to abolish taxpayers that did not pay taxes in the period 1999-2008 and to understand taxpayer perception on professionalism of tax inspectors and on tax procedures that are implemented in Kosovo. Research concluded that tax amnesty did not have any impact on businesses and in Kosovo market, and while tax inspectors behave professionally during TAK auditing visits, tax avoidance occurs regardless professionalism of TAK staff and regardless of level of satisfaction of taxpayers. Research provided four recommendations for TAK that are related to: capacity building for TAK staff, increase in electronic services for taxpayers, awareness that tax evasion and tax avoidance occurs mainly in developing countries, and positive impact of tax amnesties in fighting informality.", "Keywords": "Tax administration ; tax avoidance ; tax evasion ; tax policies ; large taxpayer unit ; taxpayer ; tax-auditing visit ; value added tax ; fine ; professionalism", "DOI": "10.1016/j.ifacol.2022.12.013", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UBT Higher Education, Kosovo"}, {"AuthorId": 2, "Name": "Besnik Skenderi", "Affiliation": "UBT Higher Education, Kosovo"}], "References": []}, {"ArticleId": *********, "Title": "The Framework for Research of Smart Silver Villages", "Abstract": "Smart villages are feasible solutions to develop European rural areas and reverse depopulation processes. As rural areas face migration of younger people towards urban areas, the ageing of population is there even faster and presents more challenges. Older adults would prefer to stay in their home and social environment as long as possible, which also goes for older adults in rural areas. The European office of the World Health Organization (later on WHO) prepared the handbook with eight domains supporting the development of an age-friendly environment. This article reviewed how much literature covers smart villages and age-friendly environments in rural areas. We used the Web of Science search tool and several search strings to consider if there is sufficient research covering this topic. While there is much research covering the age-friendly environment in cities, few only are dedicated to rural areas. We concluded that there is a need for further research to support the development of smart silver villages as rural age-friendly environments and that eight WHO domains give us a suitable frame for community development in this direction. The literature review was performed to determine the conditions for the research framework on smart silver village. Subsequent research should focus on the smart village field, including care for older adults and developing social infrastructure for older adults in rural areas. Besides that, the literature review for age-friendly environment research considers the applicability of their results in rural areas and smart villages.", "Keywords": "ageing ; older adults ; smart village ; ICT ; age-friendly ; rural area", "DOI": "10.1016/j.ifacol.2022.12.059", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Alma Mater Europaea – ECM in Zavod INRISK , Slovenia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Alma Mater Europaea – ECM in Zavod INRISK , Slovenia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zavod INRISK, Slovenia"}], "References": [{"Title": "Ambient Assisted Living in Lifetime Neighbourhoods", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "16896", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Real estate taxation and other fiscal policies as regulators of growth in ageing regions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "16908", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Smart Silver Villages as part of Social Infrastructure for Older Adults in Rural Areas", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "16914", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Social Infrastructure supporting Ambient Assisted Living in a Smart Silver City: Literature Review and Research Agenda", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "942", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Sustainable and Adequate Home-Care Logistics Including Precedence Constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "948", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Smart Age-Friendly Environments", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "768", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "The use of ICT in older adults strengthens their social network and reduces social isolation: Literature Review and Research Agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "645", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Digital transformation of community health and social services for ageing cohorts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "756", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Planning Digital Transformation of Care in Rural Areas", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "750", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Review of Telecare in Smart Age-Friendly Cities", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "744", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Advanced health technologies require skills and influence the culture of education: Literature Review and Research Agenda", "Authors": "<PERSON><PERSON><PERSON> N.; <PERSON><PERSON><PERSON>.; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "657", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105369217, "Title": "An insight into human rights in the engineering workplace and education", "Abstract": "The paper focuses on three aspects. The first focus of this research is to investigate the challenges of women working as engineers in Kosovo and Ireland. The second focus is to investigate the challenges of both men and women working as engineers in Kosovo and Ireland. The third focus is to investigate the awareness of applicable laws regarding human rights and/or gender equality and the possibility of the inclusion of human rights and/or gender equality laws within education to help deal with discrimination problems that may arise in the future. A comparison is made between similar groups in Kosovo and Ireland. The survey asks questions about the perceived socio-economic difficulties, flexibility issues and lack of opportunity for career advancement for women and men working as engineers. In addition the survey reviews the possibility of the inclusion of human rights and/or gender equality laws during education to help deal with discrimination problems that may arise in the future.", "Keywords": "Women ; Men ; Engineering ; Challenges ; Diversity ; Inclusion ; Human Rights ; Law ; Kosovo ; Ireland ; Gender equality", "DOI": "10.1016/j.ifacol.2022.12.014", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University for Business and Technology, Kosovo"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Engineering Technology, South East Technological University, Ireland"}, {"AuthorId": 3, "Name": "Blertë Retkoceri", "Affiliation": "University for Business and Technology, Kosovo"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "INSYTE Centre, South East Technological University, Ireland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "INSYTE Centre, South East Technological University, Ireland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University for Business and Technology, Kosovo"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University for Business and Technology, Kosovo"}], "References": [{"Title": "TECIS Inclusion and Diversity working group vision", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "17415", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105369219, "Title": "Noise measurements of wind turbines for electricity generation in Shala Park", "Abstract": "The global energy crisis has increased the emergency demand for electricity generation in the Republic of Kosovo. In order to respond to the challenges of climate and energy, our country has also given priority to strategic investments. These investments are regulated by the law of Kosovo on Alternative Wind Energy in Shala of Bajgora Park. Our paper has presented the effect of noise exposure of the rotation of the wings from the head of wind turbines and their negative effects on the mobility of people who frequent their spaces, creating health concerns with symptoms that are exposed to such cases. During the research of the paper the effect of noise and acoustic pollution was treated, including low frequency noise which is gradually reduced with increasing distance from different geographical positions for the installation of 27 GE (Generation of Energy) 137-3.8 MW (Mega Wat) wind turbines of the General brand. Prejudices of the selection of the development of disturbing noise indications related to the measurements performed during the study work were found annoying elements that affect the sustainable environmental development. Experimental measurements have shown that the aerodynamic noises in the form of wind waves blowing with the turbine blades were above the permitted standards.", "Keywords": "wind ; turbine blades ; noise ; information bias ; environmental pollution", "DOI": "10.1016/j.ifacol.2022.12.003", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "UBT, Higher Education Institution, Faculty of Engineering of Energy Pristina, Kosova"}, {"AuthorId": 2, "Name": "Avni. Ali<PERSON>maj", "Affiliation": "UBT, Higher Education Institution, Faculty of Engineering of Energy Pristina, Kosova"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "UBT, Higher Education Institution, Faculty of Engineering of Energy Pristina, Kosova"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UBT, Higher Education Institution, Faculty of Engineering of Energy Pristina, Kosova"}], "References": []}, {"ArticleId": 105369220, "Title": "The development of a novel educational model to successfully upskill technical workers for Industry 5.0: Ireland a case study", "Abstract": "The aim of this research is to describe how, in Ireland 2022, a new andragogical educational model for adult learners from industry was designed to meet the need to upskill established technical workers. The programme is designed to meet the dynamic requirements of Industry 4.0 and Industry 5.0 and has been established by primary and secondary research. The newly designed programme addresses the need for improvement and growth of current teaching and learning models in technical education.", "Keywords": "Education ; Andragogy ; Industry 4.0 ; Industry 5.0. Learner centred education ; programme design ; educational theorists ; engineering technicians", "DOI": "10.1016/j.ifacol.2022.12.072", "PubYear": 2022, "Volume": "55", "Issue": "39", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-Kent", "Affiliation": "Department of Engineering Technology, South East Technological University, Waterford, Ireland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Education, South East Technological University, Waterford, Ireland"}], "References": []}, {"ArticleId": 105369315, "Title": "The Book Review Column", "Abstract": "<p>Sadly, I never had a chance to really get to know <PERSON><PERSON>. However, at the time that I was still wondering what avenues to pursue in computer science, his work was one of my principal motivations. His talks, several of which I remember attending back in the 1980s, were crystal clear, entertaining, and inspiring. As much as he will be missed, he lives on through his legacy, as manifested in his work as well as his many eminent students.</p>", "Keywords": "", "DOI": "10.1145/3577971.3577973", "PubYear": 2022, "Volume": "53", "Issue": "4", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Clark University, Worcester, MA, USA"}], "References": []}, {"ArticleId": 105369317, "Title": "SweetAda", "Abstract": "<p>This article tries to describe what is SweetAda, how it was developed, its uses, and its possible future routes.</p>", "Keywords": "ada; sweetada", "DOI": "10.1145/3577949.3577955", "PubYear": 2022, "Volume": "42", "Issue": "1", "JournalId": 12096, "JournalTitle": "ACM SIGAda Ada Letters", "ISSN": "1094-3641", "EISSN": "1557-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SweetAda home, Sesto <PERSON> (FI), Italy"}], "References": []}, {"ArticleId": 105369345, "Title": "Visualizing Topics and Opinions Helps Students Interpret Large Collections of Peer Feedback for Creative Projects", "Abstract": "We deployed a feedback visualization tool to learn how students used the tool for interpreting feedback from peers and teaching assistants. The tool visualizes the topic and opinion structure in a collection of feedback and provides interaction for reviewing providers’ backgrounds. A total of 18 teams engaged with the tool to interpret feedback for course projects. We surveyed students (N = 69) to learn about their sensemaking goals, use of the tool to accomplish those goals, and perceptions of specific features. We interviewed students (N = 12) and TAs (N = 2) to assess the tool’s impact on students’ review processes and course instruction. Students discovered valuable feedback, assessed project quality, and justified design decisions to teammates by exploring specific icon patterns in the visualization. The interviews revealed that students mimicked strategies implemented in the tool when reviewing new feedback without the tool. Students found the benefits of the visualization outweighed the cost of labeling feedback.", "Keywords": "Feedback sensemaking; feedback support; formative feedback; visualization design; learning", "DOI": "10.1145/3571817", "PubYear": 2023, "Volume": "30", "Issue": "3", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Illinois, Urbana-Champaign, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> Lee", "Affiliation": "University of Illinois, Urbana-Champaign, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of California, San Diego, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Adobe Creative Intelligence Lab, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois, Urbana-Champaign, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Illinois, Urbana-Champaign, USA"}], "References": [{"Title": "Using learning analytics to understand student perceptions of peer feedback", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "106658", "JournalTitle": "Computers in Human Behavior"}, {"Title": "CommunityClick", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 105369631, "Title": "Retraction Note: An improvised video coding algorithm for deep learning-based video transmission using HEVC", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-022-07761-x", "PubYear": 2023, "Volume": "27", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Deparment of Computer Science and Engineering, MNM Jain Engineering College, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Adhiyamaan College of Engineering, Hosur, India"}], "References": []}, {"ArticleId": 105369694, "Title": "TopSort: A High-Performance Two-Phase Sorting Accelerator Optimized on HBM-Based FPGAs", "Abstract": "The emergence of high-bandwidth memory (HBM) brings new opportunities to boost the performance of sorting acceleration on FPGAs, which was conventionally bounded by the available off-chip memory bandwidth. However, it is nontrivial for designers to fully utilize this immense bandwidth. First, the existing sorter designs cannot be directly scaled at the increasing rate of available off-chip bandwidth, as the required on-chip resource usage grows at a much faster rate and would bound the sorting performance in turn. Second, designers need an in-depth understanding of HBM's characteristics to effectively utilize the HBM bandwidth. To tackle these challenges, we present TopSort, a novel two-phase sorting solution optimized for HBM-based FPGAs. In the first phase, 16 merge trees work in parallel to fully utilize 32 HBM channels’ bandwidth. In the second phase, TopSort reuses the logic from phase one to form a wider merge tree to merge the partially sorted results from phase one. TopSort also adopts HBM-specific optimizations to reduce resource overhead and improve bandwidth utilization. TopSort can sort up to 4 GB data using all 32 HBM channels, with an overall sorting performance of 15.6 GB/s. TopSort is 6.7× and 2.7× faster than state-of-the-art CPU and FPGA sorters.", "Keywords": "Sorting;merge sort;hardware acceleration;high-bandwidth memory;memory-centric design;FPGA;floorplan", "DOI": "10.1109/TETC.2022.3228575", "PubYear": 2023, "Volume": "11", "Issue": "2", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of California, Los Angeles, CA, USA"}, {"AuthorId": 2, "Name": "Licheng Guo", "Affiliation": "Department of Computer Science, University of California, Los Angeles, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering Science, Simon Fraser University, Burnaby, BC, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of California, Los Angeles, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of California, Los Angeles, CA, USA"}], "References": []}, {"ArticleId": 105369704, "Title": "Construction of binary classification model by ML with IP on the CIFAR10 dataset", "Abstract": "Learning through machines is currently considered to be one of the most popular topics on a global scale. It is even possible to say this about the modern electricity that is used in the world today. To be more specific about what machine learning is, however, we can say that it is just one method of teaching the machine by providing it with a significant amount of data. Machine Learning algorithms allow you to do image processing at scale, and with great detail. This work finds that both the ASC and the J48 have a high level, in addition to the same level of accuracy, which is 90%; both the ASC and the J48 have a high level, in addition to the same level of precision, which is 0.92. The ASC and J48 both have a high level of performance as well as the same recall level, which is 0.90; the ASC and J48 both have a high level of performance as well as the same ROC level, which is 0.98; and the ASC and J48 both have a high level of performance as well as the same PRC level, which is 0.97. The ASC and J48 both have the same high F-Measure value, which is 0.90; the ASC and J48 both have the same high Kappa value, which is 0.80; the ASC and J48 both have the same high MCC value, which is 0.82; and the ASC and J48 both have the same best performance, which is 0.10 of MAE. Both the ASC and the J48 have achieved the same level of performance, which is RMSE of 0.29. Both the ASC and the J48 have achieved the same best performance, which is equal to 20% of RAE. Both the ASC and the J48 have achieved the same level of performance, which is 57.27% of RRSE. Low-level accuracy, which is 60%, is possessed by the IBK. The precision of the FLDA is only at a low level, which is 0.65. Low level recall, which is 0.60, is exhibited by the IBK. The PRC for the IBK is quite low, coming in at 0.60. IBK has a PRC that is relatively low, coming in at 0.57. The F-Measure for the FLDA is the lowest possible, coming in at 0.55. The Kappa level of the IBK is quite low, coming in at 0.20. The MCC level in the IBK is the lowest possible, coming in at 0.25. The worst performance level for the IBK is 0.41 of MAE; the worst performance level for the IBK is 0.60 of RMSE; the worst performance level for the IBK is 82% of RAE; and the worst performance level for the IBK is 120.42% of RRSE. © 2022, Engg Journals Publications. All rights reserved.", "Keywords": "IBK; Image processing; kappa; Machine learning; RAE", "DOI": "10.21817/indjcse/2022/v13i6/221306143", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 54293, "JournalTitle": "Indian Journal of Computer Science and Engineering", "ISSN": "0976-5166", "EISSN": "2231-3850", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>.", "Affiliation": ""}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON><PERSON>.", "Affiliation": ""}], "References": []}, {"ArticleId": 105369718, "Title": "Phenomenological model of hardening and flow for Ti-6Al-4 V titanium alloy sheets under hot forming conditions", "Abstract": "<p>Hardening is the core factor to determine deformation uniformity in sheet metal forming. Hot deformation of titanium alloy sheets encounters the coupled effects of strain hardening, strain rate hardening and softening, which makes the determination of forming parameters aiming for an enhanced hardening very difficult in practical processes. This paper presents a new model to quantify the hardening of Ti-6Al-4 V titanium alloy sheets under hot forming conditions based on the underlying correlation between uniform strain and hardening. Firstly, to precisely determine hot deformation characteristics of titanium alloy sheets, hot tensile uniaxial tests using Gleeble systems at various strain rates of 0.01–1 s<sup>−1</sup> and temperatures of 973–1123 K were performed systematically. A newly developed volume-based correction method for stress–strain curves of Gleeble thermo-mechanical testing was proposed to eliminate the damaging effect of temperature gradients on strain calculations, which enables the strain hardening, strain rate hardening and softening to be determined precisely. Then, a simple unified formula of hardening components ( n , m and s ) was proposed to predict the achievable uniform strain at certain conditions efficiently. Using which, occupation of each hardening can be quantified and compared to facilitate the determination of process parameters. Finally, a phenomenological model based on the hardening and softening components was developed to predict the hot flow behaviour. The proposed quantitative model can provide an efficient and useful approach for process designers to design process parameters driven by the objective of enhancing hardening to maximize uniform deformation during hot forming of titanium alloy sheets.</p>", "Keywords": "Titanium alloy sheets; Hot forming; Hardening quantification; Correction method; Constitutive model", "DOI": "10.1007/s00170-022-10629-x", "PubYear": 2023, "Volume": "125", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Material Science and Engineering, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Spaceflight Precision Machinery Institute, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Material Science and Engineering, Harbin Institute of Technology, Harbin, China"}], "References": []}]