[{"ArticleId": 115201632, "Title": "Sensing to Hear through Memory: Ultrasound Speech Enhancement without Real Ultrasound Signals", "Abstract": "<p>Speech enhancement on mobile devices is a very challenging task due to the complex environmental noises. Recent works using lip-induced ultrasound signals for speech enhancement open up new possibilities to solve such a problem. However, these multi-modal methods cannot be used in many scenarios where ultrasound-based lip sensing is unreliable or completely absent. In this paper, we propose a novel paradigm that can exploit the prior learned ultrasound knowledge for multi-modal speech enhancement only with the audio input and an additional pre-enrollment speaker embedding. We design a memory network to store the ultrasound memory and learn the interrelationship between the audio and ultrasound modality. During inference, the memory network is able to recall the ultrasound representations from audio input to achieve multi-modal speech enhancement without needing real ultrasound signals. Moreover, we introduce a speaker embedding module to further boost the enhancement performance as well as avoid the degradation of the recalling when the noise level is high. We adopt an end-to-end multi-task manner to train the proposed framework and perform extensive evaluations on the collected dataset. The results show that our method yields comparable performance with audio-ultrasound methods and significantly outperforms the audio-only methods.</p>", "Keywords": "", "DOI": "10.1145/3659598", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shanghai Jiao Tong University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai Jiao Tong University, China"}], "References": [{"Title": "Endophasia", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Monaural speech enhancement through deep wave-U-net", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "158", "Issue": "", "Page": "113582", "JournalTitle": "Expert Systems with Applications"}, {"Title": "SoundLip", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Smartphone-based Handwritten Signature Verification using Acoustic Signals", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "ISS", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Quantifying the Causal Effect of Individual Mobility on Health Status in Urban Space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "LASense", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "UltraSpeech", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Lipwatch: Enabling Silent Speech Recognition on Smartwatches using Acoustic Sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 115201804, "Title": "Enhanced Open-Circuit Voltage Subject to Harmonic Excitations in Piezoelectric Energy Harvesters Using Hybrid Optimization Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2024.2343994", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "Jaibhavani K. S.", "Affiliation": "Department of Electronics and Instrumentation Engineering, SRM Valliammai Engineering College, Chengalpattu, India"}, {"AuthorId": 2, "Name": "Visalakshi S.", "Affiliation": "Department of Electronics and Instrumentation Engineering, SRM Valliammai Engineering College, Chengalpattu, India"}], "References": [{"Title": "Shape optimization of piezoelectric energy harvesters based on isogeometric analysis and particle swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "7", "Page": "1637", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 115201890, "Title": "GobhiSet: Dataset of raw, manually, and automatically annotated RGB images across phenology of Brassica oleracea var. Botrytis", "Abstract": "This research introduces an extensive dataset of unprocessed aerial RGB images and orthomosaics of Brassica oleracea crops, captured via a DJI Phantom 4. The dataset, publicly accessible, comprises 244 raw RGB images, acquired over six distinct dates in October and November of 2020 as well as 6 orthomosaics from an experimental farm located in Portici, Italy. The images, uniformly distributed across crop spaces, have undergone both manual and automatic annotations, to facilitate the detection, segmentation, and growth modelling of crops. Manual annotations were performed using bounding boxes via the Visual Geometry Group Image Annotator (VIA) and exported in the Common Objects in Context (COCO) segmentation format. The automated annotations were generated using a framework of Grounding DINO + Segment Anything Model (SAM) facilitated by YOLOv8x-seg pretrained weights obtained after training manually annotated images dated 8 October, 21 October, and 29 October 2020. The automated annotations were archived in Pascal Visual Object Classes (PASCAL VOC) format. Seven classes, designated as Row 1 through Row 7, have been identified for crop labelling. Additional attributes such as individual crop ID and the repetitiveness of individual crop specimens are delineated in the Comma Separated Values (CSV) version of the manual annotation. This dataset not only furnishes annotation information but also assists in the refinement of various machine learning models, thereby contributing significantly to the field of smart agriculture. The transparency and reproducibility of the processes are ensured by making the utilized codes accessible. This research marks a significant stride in leveraging technology for vision-based crop growth monitoring.", "Keywords": "Brassica oleracea; Manual annotation; Automatic annotation; Segment anything model; Grounding DINO", "DOI": "10.1016/j.dib.2024.110506", "PubYear": 2024, "Volume": "54", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering, University of Campania “<PERSON><PERSON>, Via Roma 29, Aversa, (CE) 81031, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Agricultural Sciences, University of Naples “Federico II”, Via Università 100, Portici (NA) 80055, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Engineering, University of Campania “<PERSON><PERSON>, Via Roma 29, Aversa, (CE) 81031, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biological and Pharmaceutical Environmental Sciences and Technologies, University of Campania “<PERSON><PERSON>, Via <PERSON>, 43, 81100 Caserta, (CE), Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Agricultural Sciences, University of Naples “Federico II”, Via Università 100, Portici (NA) 80055, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Agricultural Sciences, University of Naples “Federico II”, Via Università 100, Portici (NA) 80055, Italy"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Sciences, University of Naples “Federico II”, Via Università 100, Portici (NA) 80055, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Engineering, University of Campania “<PERSON><PERSON>, Via Roma 29, Aversa, (CE) 81031, Italy"}], "References": [{"Title": "Towards a better understanding of annotation tools for medical imaging: a survey", "Authors": "<PERSON><PERSON>; Manal <PERSON>; Manal <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "18", "Page": "25877", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "GobhiSet: Dataset of raw, manually, and automatically annotated RGB images across phenology of Brassica oleracea var. Botrytis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "", "Page": "110506", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 115201968, "Title": "Accurate Blood Pressure Measurement Using Smartphone's Built-in Accelerometer", "Abstract": "<p>Efficient blood pressure (BP) monitoring in everyday contexts stands as a substantial public health challenge that has garnered considerable attention from both industry and academia. Commercial mobile phones have emerged as a promising tool for BP measurement, benefitting from their widespread popularity, portability, and ease of use. Most mobile phone-based systems leverage a combination of the built-in camera and LED to capture photoplethysmography (PPG) signals, which can be used to infer BP by analyzing the blood flow characteristics. However, due to low Signal-to-Noise (SNR), various factors such as finger motion, improper finger placement, skin tattoos, or fluctuations in environmental lighting can distort the PPG signal. These distortions consequentially affect the performance of BP estimation. In this paper, we introduce a novel sensing system that utilizes the built-in accelerometer of a mobile phone to capture seismocardiography (SCG) signals, enabling accurate BP measurement. Our system surpasses previous mobile phone-based BP measurement systems, offering advantages such as high SNR, ease of use, and power efficiency. We propose a triple-stage noise reduction scheme, integrating improved complete ensemble empirical mode decomposition with adaptive noise (ICEEMDAN), recursive least squares (RLS) adaptive filter, and soft-thresholding, to effectively reconstruct high-quality heartbeat waveforms from initially contaminated raw SCG signals. Moreover, we introduce a data augmentation technique encompassing normalization coupled with temporal-sliding, effectively augmenting the diversity of the training sample set. To enable battery efficiency on smartphone, we propose a resource-efficient deep learning model that incorporates resource-efficient convolution, shortcut connections, and Huber loss. We conduct extensive experiments with 70 volunteers, comprising 35 healthy individuals and 35 individuals diagnosed with hypertension, under a user-independent setting. The excellent performance of our system demonstrates its capacity for robust and accurate daily BP measurement.</p>", "Keywords": "", "DOI": "10.1145/3659599", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Soochow University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Soochow University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Macquarie University, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Soochow University, China"}, {"AuthorId": 5, "Name": "<PERSON>peng Dai", "Affiliation": "Nanjing University, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Shenzhen Institutes of Advanced Technology, CAS, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Soochow University, China"}, {"AuthorId": 8, "Name": "<PERSON> Gu", "Affiliation": "Macquarie University, Australia"}], "References": [{"Title": "Deep Learning on Mobile and Embedded Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": *********, "Title": "Sistem Rekomendasi Pembelian Smartphone berbasis Algoritma K-Means dan Singular Value Decomposition", "Abstract": "<p>Perkembangan teknologi informasi yang pesat, memberi dampak pada ketesedian informasi yang berlimpah. Hal ini menjadikan suatu masalah yang disebut kelebihan informasi, menyebabkan pengguna internet sulit memahami dan membuat keptusan. E-commerce merupakan salah satu yang terdampak dari kelebihan informasi, dengan banyaknya produk dan pengguna baik dari penjual maupun pembeli yang ada. Sistem rekomendasi adalah bagian penting dari e-commerce yang menjadi salah satu cara menangani kelebihan informasi, dengan memberikan rekomendasi produk kepada pembeli agar membantu menentukan pilihan. Dalam sistem rekomendasi memiliki permasalahan scalability, dimana banyaknya produk yang tersedia membuatnya menjadi tidak efektif dan efisien dalam memberikan rekomendasi kepada pembeli. <PERSON><PERSON>, penelitian ini mengusulkan metode sistem rekomendasi yang dikombinasikan teknik clustering. Menggunakan algoritma K-Means untuk mengelompokkan produk, kemudian algoritma Singular Value Decomposition (SVD) untuk membuat rekomendasi di dalam cluster yang terbentuk. Hasil keluaran model yaitu, rekomendasi produk dan prediksi rating yang diberikan pembeli dari produk yang direkomendasikan. Evaluasi model mendapatkan nilai dbi sebesar 0,703 untuk clustering, nilai rata-rata terbaik MAE 0.8150 dan RMSE 1.1781 untuk rekomendasi yang dihasilkan. Kesimpulan yang didapat bahwa metode ini dapat menangani masalah scalability dan memberikan rekomendasi yang akurat dengan nilai evaluasi yang lebih baik dibandingkan penelitian sebelumnya.</p>", "Keywords": "Sistem Rekomendasi;Collaborative Filtering;K-Means Clustering;Singular Value Decomposition (SVD)", "DOI": "10.25077/TEKNOSI.v10i1.2024.45-53", "PubYear": 2024, "Volume": "10", "Issue": "1", "JournalId": 32016, "JournalTitle": "Jurnal Teknologi dan Sistem Informasi", "ISSN": "2460-3465", "EISSN": "2476-8812", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Dian <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Dian <PERSON>"}], "References": []}, {"ArticleId": 115202029, "Title": "The Personality Dimensions GPT-3 Expresses During Human-Chatbot Interactions", "Abstract": "<p>Large language models such as GPT-3 and ChatGPT can mimic human-to-human conversation with unprecedented fidelity, which enables many applications such as conversational agents for education and non-player characters in video games. In this work, we investigate the underlying personality structure that a GPT-3-based chatbot expresses during conversations with a human. We conducted a user study to collect 147 chatbot personality descriptors from 86 participants while they interacted with the GPT-3-based chatbot for three weeks. Then, 425 new participants rated the 147 personality descriptors in an online survey. We conducted an exploratory factor analysis on the collected descriptors and show that, though overlapping, human personality models do not fully transfer to the chatbot's personality as perceived by humans. We also show that the perceived personality is significantly different from that of virtual personal assistants, where users focus rather on serviceability and functionality. We discuss the implications of ever-evolving large language models and the change they affect in users' perception of agent personalities.</p>", "Keywords": "", "DOI": "10.1145/3659626", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}], "References": [{"Title": "He Is Just Like Me", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "GPT-3: Its Nature, Scope, Limits, and Consequences", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "681", "JournalTitle": "Minds and Machines"}, {"Title": "How Should My Chatbot Interact? A Survey on Social Characteristics in Human–Chatbot Interaction Design", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "729", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Making conversations with chatbots more personalized", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "106627", "JournalTitle": "Computers in Human Behavior"}, {"Title": "GPT-3: What’s it good for?", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "113", "JournalTitle": "Natural Language Engineering"}, {"Title": "Exploring User Expectations of Proactive AI Systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Social Emotional Learning with Conversational Agents", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 115202182, "Title": "MalSort: Lightweight and efficient image-based malware classification using masked self-supervised framework with Swin Transformer", "Abstract": "The proliferation of malware has exhibited a substantial surge in both quantity and diversity, posing significant threats to the Internet and indispensable network applications. The accurate and effective classification makes a pivotal role in defending against malware. Numerous approaches employ supervised learning techniques, specifically Convolutional Neural Networks (CNNs), to train feature extractors. However, acquiring a substantial quantity of labeled samples incurs significant expenses, and relying solely on CNNs as feature extractors may result in restricted local receptive fields, consequently compromising the preservation of crucial features. In order to address these constraints, we propose an effective malware classification approach, denoted as MalSort, which leverages the masked self-supervised framework with Swin Transformer. Initially, each instance of malware is transformed into a color image. Furthermore, the Swin Transformer self-supervised framework is utilized to extract multi-scale key feature vectors from a randomly masked partial color image, while the prediction module is employed to predict the masked image. Ultimately, the pre-trained encoder is fine-tuned using the malware dataset to effectively carry out a malware classification task. Our MalSort exhibits a reduced reliance on labeled data samples during the training phase, thereby obviating the necessity for extensive amounts of labeled data. Consequently, the MalSort conserves hardware resources and improve its training efficiency. The experimental results indicate that the MalSort outperforms existing models by achieving a classification accuracy of 97.85%, a recall of 97.63%, a precision of 97.85%, and an F1-score of 97.85% on the BIG2015 dataset. Similarly, on the Malimg dataset, the model achieves percentages of 98.28%, 98.18%, 98.19%, and 98.28% for classification accuracy, recall, precision, and F1-score, respectively.", "Keywords": "", "DOI": "10.1016/j.jisa.2024.103784", "PubYear": 2024, "Volume": "83", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, 050024, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, 050024, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Police scientific research department, Hebei Vocational College of Public Security Police, Shijiazhuang, 050091, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, 050024, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, 050024, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Tan", "Affiliation": "School of Computing, Engineering and Built Environment, Edinburgh Napier University, Edinburgh, EH10 5DT, UK;Corresponding author"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Network and Information Security of Hebei Province, College of Computer and Cyber Security, Hebei Normal University, Shijiazhuang, 050024, China"}], "References": [{"Title": "Impact of fully connected layers on performance of convolutional neural networks for image classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "112", "JournalTitle": "Neurocomputing"}, {"Title": "MalFamAware: automatic family identification and malware classification through online clustering", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "3", "Page": "371", "JournalTitle": "International Journal of Information Security"}, {"Title": "Research on unsupervised feature learning for Android malware detection based on Restricted Boltzmann Machines", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "91", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "PROUD-MAL: static analysis-based progressive framework for deep unsupervised malware classification of windows portable executable", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "1", "Page": "673", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Android malware obfuscation variants detection method based on multi-granularity opcode features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "141", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep Learning-Based Multi-Classification for Malware Detection in IoT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "17", "Page": "", "JournalTitle": "Journal of Circuits, Systems and Computers"}]}, {"ArticleId": 115202205, "Title": "ShuffleFL: Addressing Heterogeneity in Multi-Device Federated Learning", "Abstract": "<p>Federated Learning (FL) has emerged as a privacy-preserving paradigm for collaborative deep learning model training across distributed data silos. Despite its importance, FL faces challenges such as high latency and less effective global models. In this paper, we propose ShuffleFL, an innovative framework stemming from the hierarchical FL, which introduces a user layer between the FL devices and the FL server. ShuffleFL naturally groups devices based on their affiliations, e.g., belonging to the same user, to ease the strict privacy restriction-\"data at the FL devices cannot be shared with others\", thereby enabling the exchange of local samples among them. The user layer assumes a multi-faceted role, not just aggregating local updates but also coordinating data shuffling within affiliated devices. We formulate this data shuffling as an optimization problem, detailing our objectives to align local data closely with device computing capabilities and to ensure a more balanced data distribution at the intra-user devices. Through extensive experiments using realistic device profiles and five non-IID datasets, we demonstrate that ShuffleFL can improve inference accuracy by 2.81% to 7.85% and speed up the convergence by 4.11x to 36.56x when reaching the target accuracy.</p>", "Keywords": "", "DOI": "10.1145/3659621", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Delft University of Technology, Delft, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Delft University of Technology, Delft, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Delft University of Technology, Delft, The Netherlands"}], "References": [{"Title": "FLAME", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Asynchronous federated learning on heterogeneous devices: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "", "Page": "100595", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 115202231, "Title": "A typical sample-driven learning framework for automatic disease diagnosis", "Abstract": "Disease diagnosis mainly depends on the doctor’s medical knowledge and clinical experience, which can be treated as a medical text classification task. We observe that existing data-driven methods always suffer from the distribution bias since a small amount of common diseases appear high-frequently, while most diseases are infrequent in real-world, which leads to an unbalanced data distribution in the disease diagnosis task. To address this problem, we propose a new learning framework, T ypical sample- D riven G raph N eural N etwork (TD-GNN) for disease knowledge representation and classification. In our framework, different from previous methods, each disease (label) is concretized and learned from several corresponding well-representative samples rather than full imbalance data. In addition, the contrastive learning strategy is utilized to enhance the distinguishable features learning between different diseases. In this study, we construct a real-world dataset covering 350 common diseases to evaluate the proposed learning method. The experimental results demonstrate that the proposed TD-GNN significantly outperforms the state-of-the-art baselines, especially for the majority of diseases in which only small samples can be collected from the real world. Additionally, our method can provide a sample-based interpretation for disease prediction learning.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111745", "PubYear": 2024, "Volume": "161", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Beijing University of Posts and Telecommunications, Beijing, 100876, China;Key Laboratory of Trustworthy Distributed Computing and Service(BUPT), Ministry of Education, Beijing, 100876, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Tsinghua University, Beijing, 100084, China"}, {"AuthorId": 3, "Name": "Xiangling Fu", "Affiliation": "School of Computer Science, Beijing University of Posts and Telecommunications, Beijing, 100876, China;Key Laboratory of Trustworthy Distributed Computing and Service(BUPT), Ministry of Education, Beijing, 100876, China;Corresponding author at: School of Computer Science, Beijing University of Posts and Telecommunications, Beijing, 100876, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Tsinghua University, Beijing, 100084, China;Corresponding author at: Department of Electronic Engineering, Tsinghua University, Beijing, 100084, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, Tsinghua University, Beijing, 100084, China;College of AI, Tsinghua University, Beijing, 100084, China"}], "References": []}, {"ArticleId": 115202239, "Title": "ECSkin: Tessellating Electrochromic Films for Reconfigurable On-skin Displays", "Abstract": "<p>Emerging electrochromic (EC) materials have advanced the frontier of thin-film, low-power, and non-emissive display technologies. While suitable for wearable or textile-based applications, current EC display systems are manufactured in fixed, pre-designed patterns that hinder the potential of reconfigurable display technologies desired by on-skin interactions. To realize the customizable and scalable EC display for skin wear, this paper introduces ECSkin, a construction toolkit composed of modular EC films. Our approach enables reconfigurable designs that display customized patterns by arranging combinations of premade EC modules. An ECSkin device can pixelate patterns and expand the display area through tessellating congruent modules. We present the fabrication of flexible EC display modules with accessible materials and tools. We performed technical evaluations to characterize the electrochromic performance and conducted user evaluations to verify the toolkit's usability and feasibility. Two example applications demonstrate the adaptiveness of the modular display on different body locations and user scenarios.</p>", "Keywords": "", "DOI": "10.1145/3659613", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cornell University, Hybrid Body Lab, Ithaca, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, Hybrid Body Lab, Ithaca, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cornell University, Hybrid Body Lab, Ithaca, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cornell University, Hybrid Body Lab, Ithaca, USA"}], "References": [{"Title": "Probing User Perceptions of On-Skin Notification Displays", "Authors": "<PERSON> (<PERSON>) <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "SkinKit", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "ANISMA: A Prototyping Toolkit to Explore Haptic Skin Deformation Applications Using Shape-Memory Alloys", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "SkinLink: On-body Construction and Prototyping of Reconfigurable Epidermal Interfaces", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 115202255, "Title": "A Multi-channel Long-term External Attention Network for Aeroengine Remaining Useful Life Prediction", "Abstract": "", "Keywords": "", "DOI": "10.1109/TAI.2024.3400929", "PubYear": 2024, "Volume": "5", "Issue": "10", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Automation, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> Yan", "Affiliation": "School of Mechanical Engineering, Xi&#x2019;an Jiaotong University, Xi&#x2019;an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Zhejiang University of Technology, Hangzhou, China"}], "References": [{"Title": "Multiscale deep bidirectional gated recurrent neural networks based prognostic method for complex non-linear degradation systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "554", "Issue": "", "Page": "120", "JournalTitle": "Information Sciences"}, {"Title": "PCT: Point cloud transformer", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "187", "JournalTitle": "Computational Visual Media"}, {"Title": "An integrated deep multiscale feature fusion network for aeroengine remaining useful life prediction with multisensor data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107652", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An integrated deep multiscale feature fusion network for aeroengine remaining useful life prediction with multisensor data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107652", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Artificial intelligence in prognostics and health management of engineering systems", "Authors": "<PERSON> Ochella; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "108", "Issue": "", "Page": "104552", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep Reconstruction of 3-D Human Poses From Video", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "3", "Page": "497", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Explainable Natural Language Inference via Identifying Important Rationales", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "3", "Page": "438", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Transformer-based hierarchical latent space VAE for interpretable remaining useful life prediction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101781", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "One-Stage Deep Edge Detection Based on Dense-Scale Feature Fusion and Pixel-Level Imbalance Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "1", "Page": "70", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Multi-scale integrated deep self-attention network for predicting remaining useful life of aero-engine", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105860", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multi-scale integrated deep self-attention network for predicting remaining useful life of aero-engine", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105860", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A bidirectional recursive gated dual attention unit based RUL prediction approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105885", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multi-scale split dual calibration network with periodic information for interpretable fault diagnosis of rotating machinery", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106181", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Robust Graph Autoencoder-Based Detection of False Data Injection Attacks Against Data Poisoning in Smart Grids", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "3", "Page": "1287", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}]}, {"ArticleId": 115202350, "Title": "Federal learning-based a dual-branch deep learning model for colon polyp segmentation", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19197-6", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Fan", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Federated Learning Approach to Anomaly Detection in Smart Buildings", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Internet of Things"}, {"Title": "PVT v2: Improved baselines with Pyramid Vision Transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "415", "JournalTitle": "Computational Visual Media"}]}, {"ArticleId": 115202356, "Title": "ASTER Data Processing and Fusion for Alteration Minerals and Silicification Detection: Implications for Cupriferous Mineralization exploration in the Western Anti-Atlas, Morocco", "Abstract": "Alteration minerals and silicification are typically associated with a variety of ore mineralizations and could be detected using multispectral remote sensing sensors as indicators for mineral exploration. In this investigation, the Visible Near-Infra-Red (VNIR), Short-Wave Infra-Red (SWIR), and Thermal Infra-Red (TIR) bands of the ASTER satellite sensor derived layers were fused to detect alteration minerals and silicification in east the Kerdous inlier for cupriferous mineralization exploration. Several image processing techniques were executed in the present investigation, namely, Band Ratio (BR), Selective Principal Component Analysis (SPCA) and Constrained Energy Minimization (CEM) techniques. Initially, the BR and SPCA processing results revealed several alteration zones, including argillic, phyllic, dolomitization and silicification as well as iron oxides and hydroxides. Then, these zones were mapped at sub-pixel level using the CEM technique. Pyrophyllite, kaolinite, dolomite, illite, muscovite, montmorillonite, topaz and hematite were revealed displaying a significant distribution in relation with the eastern Amlen region lithological units and previously detected mineral potential zones using HyMap imaging spectroscopy. Mainly, a close spatial association between iron oxides and hydroxide minerals, argillic, and phyllic alteration was detected, as well as a strong silicification was detected around doleritic dykes unit in Jbel Lkest area. A weighted overlay approach was used in the integration of hydrothermal alteration minerals and silicification, which allowed the elaboration of a new mineral alteration map of study area with five alteration intensities. ASTER and the various employed processing techniques allowed a practical and cost effective mapping of alteration features, which corroborates well with field survey and X-ray diffraction analysis. Therefore, ASTER data and the employed processing techniques offers a practical approach for mineral prospection in comparable settings.", "Keywords": "Remote sensing; ASTER; Hydrothermal alteration mapping; SPCA; CEM; Kerdous inlier", "DOI": "10.1016/j.aiig.2024.100077", "PubYear": 2024, "Volume": "5", "Issue": "", "JournalId": 78709, "JournalTitle": "Artificial Intelligence in Geosciences", "ISSN": "2666-5441", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Geomatic, Georessources and Environment Laboratory, Faculty of Sciences and Techniques, Sultan <PERSON>, Beni <PERSON>, Morocco;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Geomatic, Georessources and Environment Laboratory, Faculty of Sciences and Techniques, Sultan <PERSON> University, Beni <PERSON>, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Geomatic, Georessources and Environment Laboratory, Faculty of Sciences and Techniques, Sultan <PERSON> University, Beni <PERSON>, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Oceanography and Environment (INOS), Higher Institution Center of Excellence (HICoE) in Marine Science, University Malaysia Terengganu (UMT), Kuala Nerus 21030, Malaysia;Faculty of Built Environment & Surveying, Universiti Teknologi Malaysia UTM, Johor Bahru 81310, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Office of Hydrocarbons and Mines - Office National des Hydrocarbures et des Mines (ONHYM), Moulay Hassan Boulevard, Rabat, Morocco"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Office of Hydrocarbons and Mines - Office National des Hydrocarbures et des Mines (ONHYM), Moulay Hassan Boulevard, Rabat, Morocco"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Built Environment & Surveying, Universiti Teknologi Malaysia UTM, Johor Bahru 81310, Malaysia"}], "References": [{"Title": "Alteration and structural features mapping in Kacho-Mesqal zone, Central Iran using ASTER remote sensing data for porphyry copper exploration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "155", "JournalTitle": "International Journal of Image and Data Fusion"}, {"Title": "A review of machine learning in processing remote sensing data for mineral exploration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "268", "Issue": "", "Page": "112750", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 115202367, "Title": "A convolutional neural network with image and numerical data to improve farming of edible crickets as a source of food—A decision support system", "Abstract": "<p> Crickets ( Gryllus bimaculatus ) produce sounds as a natural means to communicate and convey various behaviors and activities, including mating, feeding, aggression, distress, and more. These vocalizations are intricately linked to prevailing environmental conditions such as temperature and humidity. By accurately monitoring, identifying, and appropriately addressing these behaviors and activities, the farming and production of crickets can be enhanced. This research implemented a decision support system that leverages machine learning (ML) algorithms to decode and classify cricket songs, along with their associated key weather variables (temperature and humidity). Videos capturing cricket behavior and weather variables were recorded. From these videos, sound signals were extracted and classified such as calling, aggression, and courtship. Numerical and image features were extracted from the sound signals and combined with the weather variables. The extracted numerical features, i.e., Mel-Frequency Cepstral Coefficients (MFCC), Linear Frequency Cepstral Coefficients, and chroma, were used to train shallow (support vector machine, k-nearest neighbors, and random forest (RF)) ML algorithms. While image features, i.e., spectrograms, were used to train different state-of-the-art deep ML models, i,e., convolutional neural network architectures (ResNet152V2, VGG16, and EfficientNetB4). In the deep ML category, ResNet152V2 had the best accuracy of 99.42%. The RF algorithm had the best accuracy of 95.63% in the shallow ML category when trained with a combination of MFCC+chroma and after feature selection. In descending order of importance, the top 6 ranked features in the RF algorithm were, namely humidity, temperature, C#, mfcc11, mfcc10, and D. From the selected features, it is notable that temperature and humidity are necessary for growth and metabolic activities in insects. Moreover, the songs produced by certain cricket species naturally align to musical tones such as C# and D as ranked by the algorithm. Using this knowledge, a decision support system was built to guide farmers about the optimal temperature and humidity ranges and interpret the songs (calling, aggression, and courtship) in relation to weather variables. With this information, farmers can put in place suitable measures such as temperature regulation, humidity control, addressing aggressors, and other relevant interventions to minimize or eliminate losses and enhance cricket production. </p>", "Keywords": "Insects; Sound classification; Transfer Learning; machine learning; deep learning; decision support system", "DOI": "10.3389/frai.2024.1403593", "PubYear": 2024, "Volume": "7", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Data Management, Modelling and Geo-Information Unit, International Centre of Insect Physiology and Ecology, Kenya; @iLabAfrica, Strathmore University, Kenya"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Data Management, Modelling and Geo-Information Unit, International Centre of Insect Physiology and Ecology, Kenya; School of Agricultural, Earth, and Environmental Sciences, University of KwaZulu-Natal, South Africa"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Data Management, Modelling and Geo-Information Unit, International Centre of Insect Physiology and Ecology, Kenya"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "@iLabAfrica, Strathmore University, Kenya"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Data Management, Modelling and Geo-Information Unit, International Centre of Insect Physiology and Ecology, Kenya"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Data Management, Modelling and Geo-Information Unit, International Centre of Insect Physiology and Ecology, Kenya; Corresponding author."}], "References": [{"Title": "Classification Model Evaluation Metrics", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "599", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Acoustic Classification of Mosquitoes using Convolutional Neural Networks Combined with Activity Circadian Rhythm Information", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "In Press", "Issue": "In Press", "Page": "59", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Performance analysis of machine learning models for intrusion detection system using Gini Impurity-based Weighted Random Forest (GIWRF) feature selection technique", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}, {"Title": "Optimal ratio for data splitting", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "531", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}]}, {"ArticleId": 115202437, "Title": "The experimental study of logging residue stock on logging sites following clear-cutting using a sorting machine system", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSPM.2023.138578", "PubYear": 2023, "Volume": "20", "Issue": "4", "JournalId": 20041, "JournalTitle": "International Journal of Simulation and Process Modelling", "ISSN": "1740-2123", "EISSN": "1740-2131", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115202506, "Title": "A comprehensive review on zero-shot-learning techniques", "Abstract": "<p>Advancements in computational capabilities have enabled the implementation of advanced deep learning models across various domains of knowledge, yet the increasing complexity and scarcity of data in specialized areas pose significant challenges. Zero-shot learning (ZSL), a subset of transfer learning, has emerged as an innovative solution to these challenges, focusing on classifying unseen categories present in the test set but absent during training. Unlike traditional methods, ZSL utilizes semantic descriptions, like attribute lists or natural language phrases, to map intermediate features from the training data to unseen categories effectively, enhancing the model’s applicability across diverse and complex domains. This review provides a concise synthesis of the advancements, methodologies, and applications in the field of zero-shot learning, highlighting the milestones achieved and possible future directions. We aim to offer insights into the contemporary developments in ZSL, serving as a comprehensive reference for researchers exploring the potentials and challenges of implementing ZSL-based methodologies in real-world scenarios.</p>", "Keywords": "", "DOI": "10.3233/IDT-24027", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 36670, "JournalTitle": "Intelligent Decision Technologies", "ISSN": "1872-4981", "EISSN": "1875-8843", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bioinformatics and Human Electrophysiology Laboratory, Department of Informatics, Ionian University, Corfu, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Bioinformatics and Human Electrophysiology Laboratory, Department of Informatics, Ionian University, Corfu, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bioinformatics and Human Electrophysiology Laboratory, Department of Informatics, Ionian University, Corfu, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Patras, Patras, Greece"}], "References": [{"Title": "A zero-shot learning method for fault diagnosis under unknown working loads", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "4", "Page": "899", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Classifier and Exemplar Synthesis for Zero-Shot Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Boqing Gong", "PubYear": 2020, "Volume": "128", "Issue": "1", "Page": "166", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Dual triplet network for image zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "373", "Issue": "", "Page": "90", "JournalTitle": "Neurocomputing"}, {"Title": "DeepKinZero: zero-shot learning for predicting kinase–phosphosite associations involving understudied kinases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "12", "Page": "3652", "JournalTitle": "Bioinformatics"}, {"Title": "Zero-shot learning by mutual information estimation and maximization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>ng Lv", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105490", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Zero-shot learning for action recognition using synthesized features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "390", "Issue": "", "Page": "117", "JournalTitle": "Neurocomputing"}, {"Title": "A novel dataset-specific feature extractor for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "391", "Issue": "", "Page": "74", "JournalTitle": "Neurocomputing"}, {"Title": "Dual-stream generative adversarial networks for distributionally robust zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "519", "Issue": "", "Page": "407", "JournalTitle": "Information Sciences"}, {"Title": "Learning discriminative domain-invariant prototypes for generalized zero shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105796", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Using Task Descriptions in Lifelong Machine Learning for Improved Performance and Zero-Shot Transfer", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "67", "Issue": "", "Page": "673", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Multi-modal generative adversarial network for zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "197", "Issue": "", "Page": "105847", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Similarity preserving feature generating networks for zero-shot learning", "Authors": "Yuanbo Ma; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "406", "Issue": "", "Page": "333", "JournalTitle": "Neurocomputing"}, {"Title": "Convolutional prototype learning for zero-shot recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "103924", "JournalTitle": "Image and Vision Computing"}, {"Title": "Deep transductive network for generalized zero shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107370", "JournalTitle": "Pattern Recognition"}, {"Title": "Pseudo distribution on unseen classes for generalized zero shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Ya<PERSON> Yao", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "451", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Zero shot learning based on class visual prototypes and semantic consistency", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "368", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Transferrable Feature and Projection Learning with Class Hierarchy for Zero-Shot Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "12", "Page": "2810", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Unseen image generating domain-free networks for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "411", "Issue": "", "Page": "67", "JournalTitle": "Neurocomputing"}, {"Title": "Discriminative comparison classifier for generalized zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> Zhang", "PubYear": 2020, "Volume": "414", "Issue": "", "Page": "10", "JournalTitle": "Neurocomputing"}, {"Title": "A further study on biologically inspired feature enhancement in zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "257", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Learning domain invariant unseen features for generalized zero-shot classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "206", "Issue": "", "Page": "106378", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Bidirectional generative transductive zero-shot learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "5313", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Disentangled features with direct sum decomposition for zero shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "426", "Issue": "", "Page": "216", "JournalTitle": "Neurocomputing"}, {"Title": "Inference guided feature generation for generalized zero-shot learning", "Authors": "Zongyan Han; Zhenyong Fu; Guangyu Li", "PubYear": 2021, "Volume": "430", "Issue": "", "Page": "150", "JournalTitle": "Neurocomputing"}, {"Title": "Research progress of zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "3600", "JournalTitle": "Applied Intelligence"}, {"Title": "A plug-in attribute correction module for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107767", "JournalTitle": "Pattern Recognition"}, {"Title": "Class label autoencoder with structure refinement for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "54", "JournalTitle": "Neurocomputing"}, {"Title": "Domain-aware Stacked AutoEncoders for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "429", "Issue": "", "Page": "118", "JournalTitle": "Neurocomputing"}, {"Title": "Cross-class generative network for zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "555", "Issue": "", "Page": "147", "JournalTitle": "Information Sciences"}, {"Title": "Graph active learning for GCN-based zero-shot classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "435", "Issue": "", "Page": "15", "JournalTitle": "Neurocomputing"}, {"Title": "Joint Visual and Semantic Optimization for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "215", "Issue": "", "Page": "106773", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Zero-shot learning with self-supervision by shuffling semantic embeddings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "437", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Zero-shot policy generation in lifelong reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "446", "Issue": "", "Page": "65", "JournalTitle": "Neurocomputing"}, {"Title": "Towards zero shot learning of geometry of motion streams and its application to anomaly recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114916", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Zero shot augmentation learning in internet of biometric things for health signal processing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "142", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Class knowledge overlay to visual feature learning for zero-shot image classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "207", "Issue": "", "Page": "103206", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Dual VAEGAN: A generative model for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107352", "JournalTitle": "Applied Soft Computing"}, {"Title": "Dual VAEGAN: A generative model for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107352", "JournalTitle": "Applied Soft Computing"}, {"Title": "Visual Structure Constraint for Transductive Zero-Shot Learning in the Wild", "Authors": "<PERSON><PERSON><PERSON>; Dong<PERSON> Chen; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "6", "Page": "1893", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Bangla Sign alphabet recognition with zero-shot and transfer learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "84", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Semantic-diversity transfer network for generalized zero-shot learning via inner disagreement based OOD detector", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "229", "Issue": "", "Page": "107337", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Adversarial strategy for transductive zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "750", "JournalTitle": "Information Sciences"}, {"Title": "Explainable zero-shot learning via attentive graph convolutional network and knowledge graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "741", "JournalTitle": "Semantic Web"}, {"Title": "Coarse-grained generalized zero-shot learning with efficient self-focus mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "463", "Issue": "", "Page": "400", "JournalTitle": "Neurocomputing"}, {"Title": "Zero-shot learning via a specific rank-controlled semantic autoencoder", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108237", "JournalTitle": "Pattern Recognition"}, {"Title": "Zero-shot Learning via the fusion of generation and embedding for image recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "831", "JournalTitle": "Information Sciences"}, {"Title": "Integrated generalized zero-shot learning for fine-grained classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108246", "JournalTitle": "Pattern Recognition"}, {"Title": "Context-sensitive zero-shot semantic segmentation model based on meta-learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "465", "Issue": "", "Page": "465", "JournalTitle": "Neurocomputing"}, {"Title": "Kernelized distance learning for zero-shot recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "801", "JournalTitle": "Information Sciences"}, {"Title": "Zero-shot learning for compound fault diagnosis of bearings", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116197", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Discriminative deep attributes for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108435", "JournalTitle": "Pattern Recognition"}, {"Title": "A zero-shot learning framework via cluster-prototype matching", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108469", "JournalTitle": "Pattern Recognition"}, {"Title": "Learning discriminative and representative feature with cascade GAN for generalized zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107780", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Generalized Zero-Shot Learning using Identifiable Variational Autoencoders", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116268", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Learn more from less: Generalized zero-shot learning with severely limited labeled data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "477", "Issue": "", "Page": "25", "JournalTitle": "Neurocomputing"}, {"Title": "How robust are discriminatively trained zero-shot learning models?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "", "Page": "104392", "JournalTitle": "Image and Vision Computing"}, {"Title": "GAN-MVAE: A discriminative latent feature generation framework for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "77", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Domain-aware multi-modality fusion network for generalized zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "488", "Issue": "", "Page": "23", "JournalTitle": "Neurocomputing"}, {"Title": "A zero-shot deep metric learning approach to Brain–Computer Interfaces for image retrieval", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "246", "Issue": "", "Page": "108556", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Meta-DZSL: a meta-dictionary learning based approach to zero-shot recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "14", "Page": "15938", "JournalTitle": "Applied Intelligence"}, {"Title": "Meta-DZSL: a meta-dictionary learning based approach to zero-shot recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "14", "Page": "15938", "JournalTitle": "Applied Intelligence"}, {"Title": "Meta hyperbolic networks for zero-shot learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "491", "Issue": "", "Page": "57", "JournalTitle": "Neurocomputing"}, {"Title": "DeepGOZero: improving protein function prediction from sequence and zero-shot learning based on ontology axioms", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "Supplement_1", "Page": "i238", "JournalTitle": "Bioinformatics"}, {"Title": "Cross-modal propagation network for generalized zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "159", "Issue": "", "Page": "125", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Learning the Compositional Domains for Generalized Zero-shot Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "221", "Issue": "", "Page": "103454", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Hippocampus-heuristic character recognition network for zero-shot learning in Chinese character recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "108818", "JournalTitle": "Pattern Recognition"}, {"Title": "Generalized zero-shot domain adaptation with target unseen class prototype learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "20", "Page": "17793", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Malware‐SMELL: A zero‐shot learning strategy for detecting zero‐day vulnerabilities", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102785", "JournalTitle": "Computers & Security"}, {"Title": "Malware‐SMELL: A zero‐shot learning strategy for detecting zero‐day vulnerabilities", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102785", "JournalTitle": "Computers & Security"}, {"Title": "Cross-modal prototype learning for zero-shot handwritten character recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108859", "JournalTitle": "Pattern Recognition"}, {"Title": "SMDM: Tackling zero-shot relation extraction with semantic max-divergence metric learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "6", "Page": "6569", "JournalTitle": "Applied Intelligence"}, {"Title": "SMDM: Tackling zero-shot relation extraction with semantic max-divergence metric learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "6", "Page": "6569", "JournalTitle": "Applied Intelligence"}, {"Title": "Region interaction and attribute embedding for zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "984", "JournalTitle": "Information Sciences"}, {"Title": "Zero-Shot Learning on 3D Point Cloud Objects and Beyond", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "10", "Page": "2364", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Semantic Contrastive Embedding for Generalized Zero-Shot Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "11", "Page": "2606", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "MFF: Multi-modal feature fusion for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "510", "Issue": "", "Page": "172", "JournalTitle": "Neurocomputing"}, {"Title": "Semantic-aligned reinforced attention model for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "104586", "JournalTitle": "Image and Vision Computing"}, {"Title": "Lightweight network learning with Zero-Shot Neural Architecture Search for UAV images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "260", "Issue": "", "Page": "110142", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hybrid routing transformer for zero-shot learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "109270", "JournalTitle": "Pattern Recognition"}, {"Title": "A Survey of Zero-shot Generalisation in Deep Reinforcement Learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "", "Page": "201", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "A zero-shot fault semantics learning model for compound fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "221", "Issue": "", "Page": "119642", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Learning cross-domain semantic-visual relationships for transductive zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "109591", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 115202564, "Title": "Imbalance-Aware Discriminative Clustering for Unsupervised Semantic Segmentation", "Abstract": "<p>Unsupervised semantic segmentation (USS) aims at partitioning an image into semantically meaningful segments by learning from a collection of unlabeled images. The effectiveness of current approaches is plagued by difficulties in coordinating representation learning and pixel clustering, modeling the varying feature distributions of different classes, handling outliers and noise, and addressing the pixel class imbalance problem. This paper introduces a novel approach, termed Imbalance-Aware Dense Discriminative Clustering (IDDC), for USS, which addresses all these difficulties in a unified framework. Different from existing approaches, which learn USS in two stages (i.e., generating and updating pseudo masks, or refining and clustering embeddings), IDDC learns pixel-wise feature representation and dense discriminative clustering in an end-to-end and self-supervised manner, through a novel objective function that transfers the manifold structure of pixels in the embedding space of a vision Transformer (ViT) to the label space while tolerating the noise in pixel affinities. During inference, the trained model directly outputs the classification probability of each pixel conditioned on the image. In addition, this paper proposes a new regularizer, based on the <PERSON><PERSON> function, to handle pixel class imbalance and cluster degeneration in a single shot. Experimental results demonstrate that IDDC significantly outperforms all previous USS methods on three real-world datasets, COCO-Stuff-27, COCO-Stuff-171, and Cityscapes. Extensive ablation studies validate the effectiveness of each design. Our code is available at https://github.com/MY-LIU100101/IDDC .</p>", "Keywords": "Unsupervised semantic segmentation; Imbalance-Aware Dense Discriminative Clustering; End-to-end training; Deep clustering", "DOI": "10.1007/s11263-024-02083-x", "PubYear": 2024, "Volume": "132", "Issue": "10", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Liu", "Affiliation": "Department of Biological Science and Medical Engineering, Beihang University, Beijing, China; Department of Computer Science, University of Illinois, Chicago, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biological Science and Medical Engineering, Beihang University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Illinois, Chicago, USA; Corresponding author."}], "References": []}, {"ArticleId": 115202569, "Title": "Semantic mask-based two-step approach: a general framework for X-ray diffraction peak search in high-throughput molecular sieve synthetic system", "Abstract": "X-ray diffraction (XRD) is used for characterizing the crystal structure of molecular sieves after synthetic experiments. However, for a high-throughput molecular sieve synthetic system, the huge amount of data derived from large throughput capacity makes it difficult to analyze timely. While the kernel step of XRD analysis is to search peaks, an automatic way for peak search is needed. Thus, we proposed a novel semantic mask-based two-step framework for peak search in XRD patterns: (1) mask generation, we proposed a multi-resolution net (MRN) to classify the data points of XRD patterns into binary masks (peak/background). (2) Peak search, based on the generated masks, the background points are used to fit an n-order polynomial background curve and estimate the random noises in XRD patterns. Then we proposed three rules named mask, shape, and intensity to screening peaks from initial peak candidates generated by maximum search. Besides, a voting strategy is proposed in peak screening to obtain a precise peak search result. Experiments show that the proposed MRN achieves the state-of-the-art performance compared with other semantic segmentation methods and the proposed peak search method performs better than Jade when using f1 score as the evaluation index.", "Keywords": "XRD; Peak search; Semantic segmentation; Multi-resolution net; High-throughput", "DOI": "10.1007/s40747-024-01396-1", "PubYear": 2024, "Volume": "10", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Smart Manufacturing in Energy Chemical Process, Ministry of Education, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Smart Manufacturing in Energy Chemical Process, Ministry of Education, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, East China University of Science and Technology, Shanghai, China; Qingyuan Innovation Laboratory, Quanzhou, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, East China University of Science and Technology, Shanghai, China; Qingyuan Innovation Laboratory, Quanzhou, China; Corresponding author."}, {"AuthorId": 5, "Name": "Zhiqing Yuan", "Affiliation": "State Key Laboratory of Green Chemical Engineering and Industrial Catalysts, Sinopec Shanghai Research Institute of Petrochemical Technology, Shanghai, China"}], "References": []}, {"ArticleId": 115202665, "Title": "SoftmaxU: Open softmax to be aware of unknowns", "Abstract": "Softmax, as one of the most fundamental classification methods, has been widely exploited in the modern machine learning society. However, the conventional softmax model is trained to predict the labels in the known environment. The real world contains many unknowns (unknown classes and unknown class number). To handle this problem, first, we propose a general open softmax model (SoftmaxU). Then, to validate our proposed general open softmax framework, a deep neural network-based SoftmaxU model (DSoftmaxU) is implemented, in which Bayesian low-rank and deep non-linear subspace network is proposed to generate the unknown class number and detect the novel classes. In addition, the corresponding posterior probability inference and model optimization algorithm is derived. Finally, we demonstrate the proposed open softmax model on both the synthetic and real datasets to validate our theoretic analysis, where our model achieves an average performance improvement of 2% along with unknown class number detection against the conventional open-set, novelty detection methods. Our source code will be available on the website for the further study ( https://github.com/yexlwh/SoftmaxU ).", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108594", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science and Technology, Ningbo University, Ningbo 315211, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science and Technology, Ningbo University, Ningbo 315211, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Science and Technology, Ningbo University, Ningbo 315211, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Science and Technology, Ningbo University, Ningbo 315211, China;Corresponding author"}], "References": [{"Title": "Unsupervised feature selection based on adaptive similarity learning and subspace clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103855", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Kernel two-dimensional ridge regression for subspace clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107749", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep self-representative subspace clustering network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "108041", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep compact polyhedral conic classifier for open and closed set recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "108080", "JournalTitle": "Pattern Recognition"}, {"Title": "Towards open-set touchless palmprint recognition via weight-based meta metric learning", "Authors": "Hui<PERSON> Shao; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108247", "JournalTitle": "Pattern Recognition"}, {"Title": "Fuzzy subspace clustering noisy image segmentation algorithm with adaptive local variance & non-local information and mean membership linking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "104672", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Unsupervised video anomaly detection via normalizing flows with implicit latent features", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "108703", "JournalTitle": "Pattern Recognition"}, {"Title": "Enforced block diagonal subspace clustering with closed form solution", "Authors": "Yalan Qin; Hanzhou Wu; Jian <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "108791", "JournalTitle": "Pattern Recognition"}, {"Title": "Pairnorm based Graphical Convolution Network for zero-shot multi-label classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105012", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Adaptive aggregation-distillation autoencoder for unsupervised anomaly detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108897", "JournalTitle": "Pattern Recognition"}, {"Title": "SC-GAN: Subspace Clustering based GAN for Automatic Expression Manipulation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109072", "JournalTitle": "Pattern Recognition"}, {"Title": "Towards open-set text recognition via label-to-prototype learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109109", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 115202675, "Title": "<PERSON>'s connection and fractions containing successive Padovan numbers in their decimal representation, reading left to right", "Abstract": "<p>In this paper, it is given a generalization of the relation of the repeating decimals which displays the successive terms of the <PERSON><PERSON><PERSON><PERSON> sequence and <PERSON>’s rows. Additionally, two types of fractions that contain successive terms of the Padovan sequence in their decimal representation are given, by reading from left to right and giving numerical illustrations.</p>", "Keywords": "<PERSON><PERSON><PERSON><PERSON> sequence;<PERSON><PERSON><PERSON> sequence;<PERSON>’s connection", "DOI": "10.31926/but.mif.2024.4.66.1.8", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 72080, "JournalTitle": "SERIES III  -  MATEMATICS, INFORMATICS, PHYSICS", "ISSN": "2065-2151", "EISSN": "2065-216X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mother Teresa University, Skopje, North Macedonia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mother Teresa University, Skopje, North Macedonia"}], "References": []}, {"ArticleId": 115202692, "Title": "Changing Your Tune: Lessons for Using Music to Encourage Physical Activity", "Abstract": "<p>Our research investigated whether music can communicate physical activity levels in daily life. Past studies have shown that simple musical tunes can provide wellness information, but no study has examined whether musical feedback can affect daily behavior or lead to healthier habits. We conducted a within-subject study with 62 participants over a period of 76 days, providing either musical or text-based feedback on their daily physical activity. The music was built and personalized based on participants' step counts and baseline wellness perceptions. Results showed that participants were marginally more active during the music feedback compared to their baseline period, and significantly more active compared to the text-based feedback (p = 0.000). We also find that the participant's average activity may influence the musical features they find most inspiration within a song. Finally, context influenced how musical feedback was interpreted, and specific musical features correlated with higher activity levels regardless of baseline perceptions. We discuss lessons learned for designing music-based feedback systems for health communication.</p>", "Keywords": "", "DOI": "10.1145/3659611", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Virginia, Charlottesville, Virginia, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Virginia, Charlottesville, Virginia, USA"}], "References": [{"Title": "Sounds of Health", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Narrative-Based Visual Feedback to Encourage Sustained Physical Activity: A Field Trial of the WhoIsZuki Mobile Health Platform", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 115202726, "Title": "Pseudo-spectrum of non-Archimedean matrix pencils", "Abstract": "<p>In this paper, we define the notions of the C-trace pseudo-spectrum, the M-determinant pseudo-spectrum and the pseudo-spectrum of non-Archimedean matrix pencils. Many results are proved about the C-trace pseudo-spectrum, the M-determinant pseudo-spectrum and the pseudo-spectrum of non-Archimedean matrix pencils. Examples are given to support our work.</p>", "Keywords": "Non-Archimedean matrix pencil;determinant;trace;spectrum", "DOI": "10.31926/but.mif.2024.********", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 72080, "JournalTitle": "SERIES III  -  MATEMATICS, INFORMATICS, PHYSICS", "ISSN": "2065-2151", "EISSN": "2065-216X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> University, Morocco"}], "References": []}, {"ArticleId": 115202729, "Title": "Multiple stocks recommendation: a spatio-temporal hypergraph learning approach", "Abstract": "<p>Stocks do not exist in isolation in the financial markets, but there are complex relationships among them, which leads to the fluctuations in the stock prices have the properties of synchronism, systematic linkage and conductivity. From the relevancy-based perspective, a group of stocks with distinct degrees of correlation constitute the spatial structure of hypergraph, which combines with temporal information together to provide the important basis for the analysis of financial market behaviors. However, how to effectively learn the intrinsic relevancies of stocks is still an open problem. In this article, we proposed the M ultiple S tock R ecommendation system based on a novel S patio- T emporal H ypergraph L earning framework (MSR-STHL). Firstly, inspired by the existing works, LSTM-attention module is applied to learn the temporal features of stocks, where <PERSON><PERSON> process is involved to enhance the attention mechanism in the long-term time scale. Secondly, by means of the prior knowledge and data-driven methods, the relevancy-based spatial structures among stocks are modeled from several aspects, where data-driven way can provide the potential relations among stocks and make up the disadvantage of untimely change of prior knowledge. Thirdly, graph attention networks and hypergraph convolution operations are used to achieve the fusion learning of multiple graphs/hypergraphs. Finally, the recommended stocks on the return prediction are provided. The proposed model is evaluated on three stock market datasets, i.e. NASDAQ, NYSE, and China A-share, respectively. In comparison with state-of-the-art methods, the proposed model can outperform the existing methods and the validity is confirmed.</p>", "Keywords": "Time series prediction; Stock recommendation; Hypergraph; Graph learning", "DOI": "10.1007/s10489-024-05491-1", "PubYear": 2024, "Volume": "54", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Kong Xin", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China; Shandong Provincial Key Laboratory for Novel Distributed Computer Software Technology, Jinan, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China; Corresponding author."}], "References": [{"Title": "HGC: HyperGraph based Clustering scheme for power aware wireless sensor networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "175", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Research on Data Mining and Investment Recommendation of Individual Users Based on Financial Time Series Analysis", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "2", "Page": "64", "JournalTitle": "International Journal of Data Warehousing and Mining"}, {"Title": "Predicting Stock Market Price Movement Using Sentiment Analysis: Evidence From Ghana", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "1", "Page": "33", "JournalTitle": "Applied Computer Systems"}, {"Title": "Multi-DQN: An ensemble of Deep Q-learning agents for stock market forecasting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113820", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hypergraph convolution and hypergraph attention", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107637", "JournalTitle": "Pattern Recognition"}, {"Title": "Forecasting daily stock trend using multi-filter feature selection and deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114444", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel graph convolutional feature based convolutional neural network for stock trend prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "556", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "GE-STDGN: a novel spatio-temporal weather prediction model based on graph evolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "7", "Page": "7638", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 115202894, "Title": "Multiple attribute group decision-making based on the ExpTODIM method and linguistic Pythagorean operators", "Abstract": "<p>The linguistic Pythagorean fuzzy sets (LPFSs) are productive and advantageous when decision makers (DMs) express evaluation information in decision making problems (DMPs). Due to the variability of parameters, the Aczel–Alsina t-norm and t-conorm have great applications in DMP under fuzzy sets environment. The traditional TODIM method can consider DMs’ psychological behaviors in DMPs which is a powerful tool. So, this article proposes a new multiple attribute group decision-making (MAGDM) approach based on the novel versions of TODIM method about linguistic Pythagorean fuzzy Aczel–Alsina operators. First, we introduce the operations based on <PERSON>czel–Alsina t-norm and t-conorm about linguistic Pythagorean fuzzy numbers (LPFNs). Second, we give the linguistic Pythagorean fuzzy Aczel–Alsina weighted averaging operator and linguistic Pythagorean fuzzy Aczel–Alsina weighted geometric operator, and relevant properties of the introduced operators are listed. Then, we establish a new multiple attribute group decision-making (MAGDM) approach based on ExpTODIM in LPFSs, and the built method can solve the DMPs with weight information completely unknown or completely known, the weight information is completely unknown which can be gotten by gray correlation coefficient. Finally, we solve a DMP about green building using the established approach and compare with the methodologies in existence to illustrate the advantages of purposed approach.</p>", "Keywords": "Linguistic Pythagorean fuzzy set; <PERSON><PERSON><PERSON>–Alsina T-norm and T-conorm; Aggregation operators; ExpTODIM method", "DOI": "10.1007/s41066-024-00485-3", "PubYear": 2024, "Volume": "9", "Issue": "3", "JournalId": 4880, "JournalTitle": "Granular Computing", "ISSN": "2364-4966", "EISSN": "2364-4974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab Numer Simulat Sichuan Prov Univ, College of Mathematics and Information Science, Data Recovery Key Lab Sichuan Prov, Neijiang Normal Univ, Neijiang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Lab Numer Simulat Sichuan Prov Univ, College of Mathematics and Information Science, Data Recovery Key Lab Sichuan Prov, Neijiang Normal Univ, Neijiang, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Lab Numer Simulat Sichuan Prov Univ, College of Mathematics and Information Science, Data Recovery Key Lab Sichuan Prov, Neijiang Normal Univ, Neijiang, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab Numer Simulat Sichuan Prov Univ, College of Mathematics and Information Science, Data Recovery Key Lab Sichuan Prov, Neijiang Normal Univ, Neijiang, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Management, Shanghai University, Shanghai, China"}], "References": [{"Title": "A novel TODIM‐VIKOR approach based on entropy and Jensen–<PERSON><PERSON><PERSON> divergence measure for picture fuzzy sets in a decision‐making problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "12", "Page": "2140", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Linguistic Einstein aggregation operator‐based TOPSIS for multicriteria group decision making in linguistic Pythagorean fuzzy environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "6", "Page": "2825", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Sine trigonometric operational laws and its based Pythagorean fuzzy aggregation operators for group decision-making process", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "4421", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "TODIM method based on cumulative prospect theory for multiple attribute group decision‐making under 2‐tuple linguistic Pythagorean fuzzy environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "6", "Page": "2548", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Probabilistic linguistic\n q‐ \n rung orthopair fuzzy Generalized <PERSON><PERSON> and <PERSON>fer<PERSON><PERSON> mean operators for group decision‐making with unknown weights of experts", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "12", "Page": "7770", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "<PERSON><PERSON><PERSON>–Alsina aggregation operators and their application to intuitionistic fuzzy multiple attribute decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "2", "Page": "1529", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multiple attribute group decision-making based on generalized aggregation operators under linguistic interval-valued Pythagorean fuzzy environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "591", "JournalTitle": "Granular Computing"}, {"Title": "Multiple attribute group decision making based on advanced linguistic intuitionistic fuzzy weighted averaging aggregation operator of linguistic intuitionistic fuzzy numbers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "587", "Issue": "", "Page": "813", "JournalTitle": "Information Sciences"}, {"Title": "Internal audit planning using spherical fuzzy ELECTRE", "Authors": "<PERSON><PERSON>; Hatice Camgoz-Akdag", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "108155", "JournalTitle": "Applied Soft Computing"}, {"Title": "On three perspectives for deriving three-way decision with linguistic intuitionistic fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "588", "Issue": "", "Page": "350", "JournalTitle": "Information Sciences"}, {"Title": "Multiple attribute group decision-making based on generalized interval-valued Pythagorean fuzzy Einstein geometric aggregation operators", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "2", "Page": "293", "JournalTitle": "Granular Computing"}, {"Title": "Multiattribute decision making based on Fermatean hesitant fuzzy sets and modified VIKOR method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "1532", "JournalTitle": "Information Sciences"}, {"Title": "A novel TOPSIS method with decision-theoretic rough fuzzy sets", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1221", "JournalTitle": "Information Sciences"}, {"Title": "Dombi operations for linguistic T-spherical fuzzy number: an approach for selection of the best variety of maize", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "18", "Page": "9083", "JournalTitle": "Soft Computing"}, {"Title": "Group decision making based on weighted distance measure of linguistic intuitionistic fuzzy sets and the TOPSIS method", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "611", "Issue": "", "Page": "660", "JournalTitle": "Information Sciences"}, {"Title": "Extended CODAS method for multi-attribute group decision-making based on 2-tuple linguistic Fermatean fuzzy Hamacher aggregation operators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "3", "Page": "441", "JournalTitle": "Granular Computing"}, {"Title": "Extension of GRA method for multiattribute group decision making problem under linguistic Pythagorean fuzzy setting with incomplete weight information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "11", "Page": "9726", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Type-2 intuitionistic fuzzy TODIM for intelligent decision-making under uncertainty and hesitancy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "18", "Page": "13373", "JournalTitle": "Soft Computing"}, {"Title": "A personalized individual semantics model for computing with linguistic intuitionistic fuzzy information and application in MCDM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "8", "Page": "4501", "JournalTitle": "Soft Computing"}, {"Title": "Group decision making based on entropy measure of Pythagorean fuzzy sets and Pythagorean fuzzy weighted arithmetic mean aggregation operator of Pythagorean fuzzy numbers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "361", "JournalTitle": "Information Sciences"}, {"Title": "Linguistic Pythagorean fuzzy CRITIC-EDAS method for multiple-attribute group decision analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105777", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A new approach for spherical fuzzy TOPSIS and spherical fuzzy VIKOR applied to the evaluation of hydrogen storage systems", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "8", "Page": "4403", "JournalTitle": "Soft Computing"}, {"Title": "A novel FMEA approach for submarine pipeline risk analysis based on IVIFRN and ExpTODIM-PROMETHEE-II", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "110065", "JournalTitle": "Applied Soft Computing"}, {"Title": "Spherical fuzzy TODIM method for MAGDM integrating cumulative prospect theory and CRITIC method and its application to commercial insurance selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "9", "Page": "10275", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Multi‐attribute group decision‐making based on linguistic Pythagorean fuzzy copula extended power average operator", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "7", "Page": "e13272", "JournalTitle": "Expert Systems"}, {"Title": "Emergency response scheme selection with T-spherical hesitant probabilistic fuzzy TODIM-TPZSG approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106190", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Assessment of Solar Panel Using Multiattribute Decision-Making Approach Based on Intuitionistic Fuzzy Aczel Alsina Heronian Mean Operator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "The extended ELECTRE III group decision making method based on regret theory under probabilistic interval-valued hesitant fuzzy environments", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120618", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A decision-making mechanism for multi-attribute group decision-making using 2-tuple linguistic T-spherical fuzzy maximizing deviation method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "6", "Page": "1659", "JournalTitle": "Granular Computing"}, {"Title": "Consensus of three-way group decision with weight updating based on a novel linguistic intuitionistic fuzzy similarity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "648", "Issue": "", "Page": "119537", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 115202912, "Title": "MCIF-Transformer Mask RCNN: Multi-Branch Cross-Scale Interactive Feature Fusion Transformer Model for PET/CT Lung Tumor Instance Segmentation", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.047827", "PubYear": 2024, "Volume": "79", "Issue": "3", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Huiling Lu", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Probability-based Mask R-CNN for pulmonary embolism detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "422", "Issue": "", "Page": "345", "JournalTitle": "Neurocomputing"}, {"Title": "Unsupervised supervoxel-based lung tumor segmentation across patient scans in hybrid PET/MRI", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114244", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Mask-RCNN with spatial attention for pedestrian segmentation in cyber–physical systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "109", "JournalTitle": "Computer Communications"}, {"Title": "Vision transformer: To discover the “four secrets” of image patches", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "105", "Issue": "", "Page": "102248", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 115202928, "Title": "Transformer-Based Classification of User Queries for Medical Consultancy", "Abstract": "The need for skilled medical support is growing in the era of digital healthcare. This research presents an innovative strategy, utilizing the RuBERT model, for categorizing user inquiries in the field of medical consultation with a focus on expert specialization. By harnessing the capabilities of transformers, we fine-tuned the pre-trained RuBERT model on a varied dataset, which facilitates precise correspondence between queries and particular medical specialisms. Using a comprehensive dataset, we have demonstrated our approach’s superior performance with an F1-score of over 91.8%, calculated through both cross-validation and the traditional split of test and train datasets. Our approach has shown excellent generalization across medical domains such as cardiology, neurology and dermatology. This methodology provides practical benefits by directing users to appropriate specialists for prompt and targeted medical advice. It also enhances healthcare system efficiency, reduces practitioner burden, and improves patient care quality. In summary, our suggested strategy facilitates the attainment of specific medical knowledge, offering prompt and precise advice within the digital healthcare field.", "Keywords": "", "DOI": "10.31857/S0005117924030088", "PubYear": 2024, "Volume": "85", "Issue": "3", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research University Higher School of Economics, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research University Higher School of Economics, Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Babyblog LTD, Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Babyblog LTD, Moscow, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research University Higher School of Economics, Moscow, Russia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Research University Higher School of Economics, Moscow, Russia"}], "References": [{"Title": "Text classification using improved bidirectional transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "e6486", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 115202931, "Title": "A Genetic Algorithm with Lower Neighborhood Search for the Three-Dimensional Multiorder Open-Size Rectangular Packing Problem", "Abstract": "<p>This paper addresses the multiorder open-dimension three-dimensional rectangular packing problem (3D-MOSB-ODRPP), which involves packing rectangular items from multiple orders into a single, size-adjustable container. We propose a novel metaheuristic approach combining a genetic algorithm with the Gurobi solver. The algorithm incorporates a lower neighborhood search strategy and is underpinned by a mathematical model representing the multiorder open-dimension packing scenario. Extensive experiments validate the effectiveness of the proposed approach. The LNSGA algorithm outperforms <PERSON><PERSON><PERSON> and the traditional genetic algorithm in solution quality and computational efficiency. For small-scale instances, LNSGA achieves optimal values in most cases. LNSGA demonstrates significant optimization improvements over <PERSON><PERSON><PERSON> and the genetic algorithm for large-scale instances. The superior performance is attributed to the effective integration of the lower neighborhood search mechanism and the Gurobi solver. This study offers valuable insights for optimizing the packing process in e-commerce warehousing and logistics operations.</p>", "Keywords": "", "DOI": "10.1155/2024/4456261", "PubYear": 2024, "Volume": "2024", "Issue": "", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Wuzi University, School of Information, Beijing 101149, China;Beijing Intelligent Logistics System Collaborative Innovation Center, Beijing 101149, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Economics and Management, School of Airport Economics and Management, Beijing 100102, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Capital University of Economics and Business, School of Management and Engineering, Beijing 100070, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing Wuzi University, School of Information, Beijing 101149, China"}, {"AuthorId": 5, "Name": "Lingjie Kong", "Affiliation": "Beijing Wuzi University, School of Information, Beijing 101149, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Beijing Wuzi University, School of Information, Beijing 101149, China;Beijing Intelligent Logistics System Collaborative Innovation Center, Beijing 101149, China"}], "References": [{"Title": "A matheuristic framework for the Three-dimensional Single Large Object Placement Problem with practical constraints", "Authors": "E.<PERSON><PERSON>; A.A.S. Leão; F.M.B. Toledo", "PubYear": 2020, "Volume": "124", "Issue": "", "Page": "105058", "JournalTitle": "Computers & Operations Research"}, {"Title": "Multi-objective 3D bin packing problem with load balance and product family concerns", "Authors": "Seda Erbayrak; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "159", "Issue": "", "Page": "107518", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Variable-sized bin packing problem with conflicts and item fragmentation", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107844", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Integrated optimization algorithm: A metaheuristic approach for complicated optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "586", "Issue": "", "Page": "424", "JournalTitle": "Information Sciences"}, {"Title": "Self-play learning strategies for resource assignment in Open-RAN networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "108682", "JournalTitle": "Computer Networks"}, {"Title": "Learning practically feasible policies for online 3D bin packing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "1", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Multi-objective optimization of the 3D container stowage planning problem in a barge convoy system", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "144", "Issue": "", "Page": "105796", "JournalTitle": "Computers & Operations Research"}, {"Title": "On-line three-dimensional packing problems: A review of off-line and on-line solution approaches", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "108122", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An improved Harris Hawks Optimization algorithm for continuous and discrete optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "104952", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Solving 3D packing problem using Transformer network and reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "119153", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Exact and metaheuristic approaches to solve the integrated production scheduling, berth allocation and storage yard allocation problem", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "153", "Issue": "", "Page": "106174", "JournalTitle": "Computers & Operations Research"}, {"Title": "A hybrid biogeography-based optimization algorithm for three-dimensional bin size designing and packing problem", "Authors": "Mingzhou Chen; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "180", "Issue": "", "Page": "109239", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Towards reliable robot packing system based on deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "", "Page": "102028", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Optimizing e-commerce warehousing through open dimension management in a three-dimensional bin packing system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1613", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 115202940, "Title": "PADRE – A Repository for Research on Fault Detection and Isolation of Unmanned Aerial Vehicle Propellers", "Abstract": "<p>Unmanned aerial vehicles are being used increasingly in a variety of applications. They are more and more often operating in close proximity to people and equipment. This necessitates ensuring maximum stability and flight safety. A fundamental step to achieving this goal is timely and effective diagnosis of possible defects. Popular data-based methods require a large amount of data collected during flights in various conditions. This paper describes an open PADRE database of such measurements for the detection and classification of the most common faults - multirotor propeller failures. It presents the procedure of data acquisition, the structure of the repository and ways to use the various types of data contained therein. The repository enables research on drone fault detection to be undertaken without time-consuming preparation of measurement data. The database is available on GitHub at https://github.com/AeroLabPUT/UAV_measurement_data . The article also introduces new and universal quality indicators for evaluating classifiers with non-uniform parameters, are proposed. They allow comparison of methods tested for a variety of fault classes and with different processing times.</p>", "Keywords": "UAV; Fault detection; FDI; Sensor; IMU; Acoustic detection", "DOI": "10.1007/s10846-024-02101-7", "PubYear": 2024, "Volume": "110", "Issue": "2", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Control, Robotics and Electrical Engineering, Poznan University of Technology, Poznań, Poland; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Information Technology, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Giernacki", "Affiliation": "Faculty of Control, Robotics and Electrical Engineering, Poznan University of Technology, Poznań, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Information Technology, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Information Technology, University of Technology Sydney, Sydney, Australia"}], "References": [{"Title": "ALFA: A dataset for UAV fault and anomaly detection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2-3", "Page": "515", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "UAVs path planning architecture for effective medical emergency response in future networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101337", "JournalTitle": "Physical Communication"}, {"Title": "DL-PR: Generalized automatic modulation classification method based on deep learning with priori regularization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106082", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": *********, "Title": "Almost η-Ricci solitons on two classes of almost Kenmotsu manifolds", "Abstract": "<p>The object of the present paper is to characterize two classes of almost Kenmotsu manifolds admitting almost η-Ricci solitons. In this context, we have shown that in a (k, µ) and (k, µ)' -almost Kenmotsu manifold admitting an almost η-Ricci soliton the curvature conditions (i) the manifold is Einstein, (ii) the manifold is Ricci symmetric (∇S = 0), (iii) the manifold is Ricci semisymmetric (R · S = 0) and (iv) the manifold is projective Ricci semisymmetric (P · S = 0) are equivalent. Also, we have shown that the curvature condition Q · P = 0 in a (k, µ)-almost Kenmotsu manifold admitting an almost η-Ricci soliton holds if and only if the manifold is locally isometric to the hyperbolic space H2n+1(−1) and if a (k, µ)' -almost Kenmotsu manifold admitting an almost η-Ricci soliton satisfies the curvature condition Q · R = 0, then it is locally isometric to the Riemannian product H n+1(−4) × ℝn. n.</p>", "Keywords": "Almost Kenmotsu manifolds;Almost η-Ricci solitons;Ricci symmetry;Ricci semisymmetry;Projective Ricci semisymmetry", "DOI": "10.31926/but.mif.2024.4.66.1.3", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 72080, "JournalTitle": "SERIES III  -  MATEMATICS, INFORMATICS, PHYSICS", "ISSN": "2065-2151", "EISSN": "2065-216X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Calcutta, West Bengal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Calcutta, West Bengal, India"}], "References": []}, {"ArticleId": 115202964, "Title": "Unveiling AI-Generated Financial Text: A Computational Approach Using Natural Language Processing and Generative Artificial Intelligence", "Abstract": "<p>This study is an in-depth exploration of the nascent field of Natural Language Processing (NLP) and generative Artificial Intelligence (AI), and it concentrates on the vital task of distinguishing between human-generated text and content that has been produced by AI models. Particularly, this research pioneers the identification of financial text derived from AI models such as ChatGPT and paraphrasing tools like QuillBot. While our primary focus is on financial content, we have also pinpointed texts generated by paragraph rewriting tools and utilized ChatGPT for various contexts this multiclass identification was missing in previous studies. In this paper, we use a comprehensive feature extraction methodology that combines TF–IDF with Word2Vec, along with individual feature extraction methods. Importantly, combining a Random Forest model with Word2Vec results in impressive outcomes. Moreover, this study investigates the significance of the window size parameters in the Word2Vec approach, revealing that a window size of one produces outstanding scores across various metrics, including accuracy, precision, recall and the F1 measure, all reaching a notable value of 0.74. In addition to this, our developed model performs well in classification, attaining AUC values of 0.94 for the ‘GPT’ class; 0.77 for the ‘Quil’ class; and 0.89 for the ‘Real’ class. We also achieved an accuracy of 0.72, precision of 0.71, recall of 0.72, and F1 of 0.71 for our extended prepared dataset. This study contributes significantly to the evolving landscape of AI text identification, providing valuable insights and promising directions for future research.</p>", "Keywords": "", "DOI": "10.3390/computation12050101", "PubYear": 2024, "Volume": "12", "Issue": "5", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, University of Management and Technology, Lahore 54770, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Finance, Bucharest University of Economic Studies, 6 Piata Romana, 010374 Bucharest, Romania"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, Satya <PERSON> Christian University, Salatiga 50715, Indonesia; School of Information Technology, Deakin University, Campus 221 Burwood Hwy, Burwood, VIC 3125, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering, University of Management and Technology, Lahore 54770, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Data Science, The Islamia University of Bahawalpur, Bahawalpur 63100, Pakistan; School of Natural and Computing Sciences, University of Aberdeen, Aberdeen AB24 3FX, Scotland, UK"}], "References": [{"Title": "Universal Lemmatizer: A sequence-to-sequence model for lemmatizing Universal Dependencies treebanks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "5", "Page": "545", "JournalTitle": "Natural Language Engineering"}, {"Title": "A Comparative Analysis of Machine Learning Techniques for Cyberbullying Detection on Twitter", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "11", "Page": "187", "JournalTitle": "Future Internet"}, {"Title": "Natural language processing: state of the art, current trends and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "3", "Page": "3713", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Ontology-based semantic retrieval of documents using Word2vec model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "102110", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Comparing two SVM models through different metrics based on the confusion matrix", "Authors": "<PERSON>; <PERSON>; Mercedes Landete", "PubYear": 2023, "Volume": "152", "Issue": "", "Page": "106131", "JournalTitle": "Computers & Operations Research"}, {"Title": "Improving the Polarity of Text through word2vec Embedding for Primary Classical Arabic Sentiment Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "3", "Page": "2249", "JournalTitle": "Neural Processing Letters"}, {"Title": "A Comprehensive Study of ChatGPT: Advancements, Limitations, and Ethical Considerations in Natural Language Processing and Cybersecurity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "8", "Page": "462", "JournalTitle": "Information"}, {"Title": "Cyberbullying Detection on Social Media Using Stacking Ensemble Learning and Enhanced BERT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "8", "Page": "467", "JournalTitle": "Information"}]}, {"ArticleId": 115202977, "Title": "The Impact of “Retail Media” on Online Marketplaces: Insights from a Field Experiment", "Abstract": "<p>Advertising on e-commerce marketplaces, wherein sponsored product listings are interleaved with organic product listings in the search results, is a large and growing phenomenon that falls under the umbrella of “retail media.” In this paper, taking the perspective of the marketplace, we obtain insights into the impact of sponsored listings being shown at the most salient positions in the list of results. To do so, we analyze data from a large-scale field experiment at Flipkart, a leading online marketplace in India. We find nuanced results that substantially vary across categories. In the electronics category, the sponsored listings perform worse (in terms of clicks and conversions) than the organic listings that they replace, whereas the organic listings in the neighborhood of the sponsored listings perform better than in the absence of the sponsored listings. Surprisingly, these effects are reversed in the clothing and footwear categories, in which the ads perform better than the displaced organic listings, suggesting that sponsored listings might help the platform identify new high-relevance products and improve search rankings for these categories. However, at the search level, because of the countervailing impacts on sponsored listings and neighboring organic listings (even though the directions of these effects are different for different categories), we find that increasing the fraction of sponsored listings (by about 10% points) does not affect clicks and conversions in any product category. This implies that ads bring in additional revenue for the marketplace yet do not hurt overall consumer response (in the short run). We theorize that the variation across categories occurs because of differing degrees of information asymmetry on product relevance to a query between the marketplace and the independent sellers of listed products and provide supporting evidence for this mechanism.</p><p>History: <PERSON><PERSON>, Senior Editor; <PERSON>, Associate Editor.</p><p>Supplemental Material: The online appendix is available at https://doi.org/10.1287/isre.2022.0188 .</p>", "Keywords": "e-commerce platforms; advertising; retail media; sponsored search; third-party sellers; asymmetric information; product heterogeneity", "DOI": "10.1287/isre.2022.0188", "PubYear": 2025, "Volume": "36", "Issue": "1", "JournalId": 11444, "JournalTitle": "Information Systems Research", "ISSN": "1047-7047", "EISSN": "1526-5536", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> School of Business, University of California, Irvine, California 92697"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Columbia Business School, Columbia University, New York, New York 10027"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kelley School of Business, Indiana University, Bloomington, Indiana 47405"}], "References": [{"Title": "An Empirical Analysis of Seller Advertising Strategies in an Online Marketplace", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "1", "Page": "37", "JournalTitle": "Information Systems Research"}]}, {"ArticleId": 115202979, "Title": "On lacunary Δ^m-statistical convergence of triple sequence in intuitionisticfuzzy normed space", "Abstract": "<p>In this study, we define lacunary Δm-statistical convergence in the framework of intuitionistic fuzzy normed spaces (IFNS) for triple sequences. We prove several results for lacunary Δm-statistical convergence of triple sequence in IFNS. We further established lacunary Δm-statistical Cauchy sequences and provided the Cauchy convergence criterion for this novel idea of convergence.</p>", "Keywords": "Statistical convergence;Lacunary Δm-statistical convergence;Triple Sequence;Intuitionistic fuzzy normed space", "DOI": "10.31926/but.mif.2024.********", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 72080, "JournalTitle": "SERIES III  -  MATEMATICS, INFORMATICS, PHYSICS", "ISSN": "2065-2151", "EISSN": "2065-216X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology, India"}], "References": []}, {"ArticleId": 115203006, "Title": "Cost-Effective Signcryption for Securing IoT: A Novel Signcryption Algorithm Based on Hyperelliptic Curves", "Abstract": "<p>Security and efficiency remain a serious concern for Internet of Things (IoT) environments due to the resource-constrained nature and wireless communication. Traditional schemes are based on the main mathematical operations, including pairing, pairing-based scalar multiplication, bilinear pairing, exponential operations, elliptic curve scalar multiplication, and point multiplication operations. These traditional operands are cost-intensive and require high computing power and bandwidth overload, thus affecting efficiency. Due to the cost-intensive nature and high resource requirements, traditional approaches are not feasible and are unsuitable for resource-limited IoT devices. Furthermore, the lack of essential security attributes in traditional schemes, such as unforgeability, public verifiability, non-repudiation, forward secrecy, and resistance to denial-of-service attacks, puts data security at high risk. To overcome these challenges, we have introduced a novel signcryption algorithm based on hyperelliptic curve divisor multiplication, which is much faster than other traditional mathematical operations. Hence, the proposed methodology is based on a hyperelliptic curve, due to which it has enhanced security with smaller key sizes that reduce computational complexity by 38.16% and communication complexity by 62.5%, providing a well-balanced solution by utilizing few resources while meeting the security and efficiency requirements of resource-constrained devices. The proposed strategy also involves formal security validation, which provides confidence for the proposed methodology in practical implementations.</p>", "Keywords": "", "DOI": "10.3390/info15050282", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha 410083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha 410083, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha 410083, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "EIAS Data Science Lab, College of Computer and Information Sciences, Prince Sultan University, Riyadh 11586, Saudi Arabia; School of Computer Science and Technology, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "EIAS Data Science Lab, College of Computer and Information Sciences, Prince Sultan University, Riyadh 11586, Saudi Arabia"}], "References": [{"Title": "Design and analysis of authenticated key agreement scheme in cloud-assisted cyber–physical systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "1267", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-level trust-based intelligence schema for securing of internet of things (IoT) against security threats using cryptographic authentication", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "9", "Page": "7081", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An Analysis of Cybersecurity Attacks against Internet of Things and Security Solutions", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "4", "Page": "11", "JournalTitle": "Journal of Computer and Communications"}, {"Title": "A secure and lightweight certificateless hybrid signcryption scheme for Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "23", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Certificateless signature schemes in Industrial Internet of Things: A comparative survey", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "116", "JournalTitle": "Computer Communications"}, {"Title": "Detection and prevention of man-in-the-middle attack in iot network using regression modeling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "103126", "JournalTitle": "Advances in Engineering Software"}, {"Title": "An overview of IoT architectures, technologies, and existing open-source projects", "Authors": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "20", "Issue": "", "Page": "100626", "JournalTitle": "Internet of Things"}, {"Title": "Security issues in IoT applications using certificateless aggregate signcryption schemes: An overview", "Authors": "Padmalaya Nayak; G Swapna", "PubYear": 2023, "Volume": "21", "Issue": "", "Page": "100641", "JournalTitle": "Internet of Things"}, {"Title": "Elliptic Curve Cryptography; Applications, challenges, recent advances, and future trends: A comprehensive survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "", "Page": "100530", "JournalTitle": "Computer Science Review"}, {"Title": "A comprehensive study of DDoS attacks over IoT network and their countermeasures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "127", "Issue": "", "Page": "103096", "JournalTitle": "Computers & Security"}, {"Title": "Cryptography Algorithms for Enhancing IoT Security", "Authors": "Fursan Thabit; Ozgu Can; Asia Othman <PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "", "Page": "100759", "JournalTitle": "Internet of Things"}, {"Title": "Attacks on IoT: Side-Channel Power Acquisition Framework for Intrusion Detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "5", "Page": "187", "JournalTitle": "Future Internet"}, {"Title": "An efficient heterogeneous signcryption scheme for internet of things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "94", "Issue": "", "Page": "101821", "JournalTitle": "Pervasive and Mobile Computing"}]}, {"ArticleId": 115203013, "Title": "INTERNAL AUDITORS AS CHANGE AGENTS: WHAT A DIFFERENCE A YEAR MAKES!", "Abstract": "", "Keywords": "", "DOI": "10.1080/07366981.2024.2348283", "PubYear": 2024, "Volume": "69", "Issue": "5", "JournalId": 3790, "JournalTitle": "EDPACS", "ISSN": "0736-6981", "EISSN": "1936-1009", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "THE FUTURE OF INTERNAL AUDITING: GARDENER OF GOVERNANCE", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "66", "Issue": "5", "Page": "1", "JournalTitle": "EDPACS"}, {"Title": "THE GLOBAL INTERNAL AUDIT STANDARDS – OLD WINE IN NEW BOTTLES?", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "69", "Issue": "3", "Page": "1", "JournalTitle": "EDPACS"}]}, {"ArticleId": 115203262, "Title": "A fuzzy detection approach to high-dimensional anomalies", "Abstract": "<p>Rare anomalies allow to be hidden in any subspace upon a high-dimensional space so that high-dimensional dimensional of the data brings a lot of trouble for anomalous detectors. To mine high-dimensional anomalies, this paper proposes a novel hypersphere with density fuzzy. On the one hand, the major contributors are chosen by using the fuzzy and a density estimator to quantify the contributions created by unknown instances, and then the hypersphere trained by the major contributors achieves anomalous identification. On the other hand, an inner product kernel is also derived to assist that the hypersphere pays more attention to local regions containing anomalies. Experimental results on ten UCI datasets show that the proposed model wins over the opponents on the three ultra-high dimensional datasets and most high-dimensional datasets in mining anomalies. Results also indicate that these models with fuzzy perform better than those without fuzzy, meanwhile, there is no significant difference between detected results. We demonstrate that calculating data density or attending data local regions can alleviate negative effects caused by high dimensionality on anomalous detection results to a certain extent. Additionally, the effort created by fuzzy to resist the curse of dimensionality do not rely on specific scenarios and specific detectors, while the assistance afforded by kernels to resist high dimensionality is dependent of detector structures.</p>", "Keywords": "Anomaly detection; Density fuzzy; High dimensionality", "DOI": "10.1007/s00530-024-01343-7", "PubYear": 2024, "Volume": "30", "Issue": "3", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Chongqing Technology and Business University, Chongqing, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Intelligent Networking and New Energy Vehicles, Geely University of China, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Intelligent Science and Engineering, Yunnan Technology and Business University, Yunnan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing Three Gorges University, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Big Data, Chongqing Polytechnic Institute, Chongqing, China"}], "References": [{"Title": "An irrelevant attributes resistant approach to anomaly detection in high-dimensional space using a deep hypersphere structure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "108301", "JournalTitle": "Applied Soft Computing"}, {"Title": "Anomaly detection for high-dimensional space using deep hypersphere fused with probability approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "5", "Page": "4205", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "An intuitionistic fuzzy bireduct model and its application to cancer treatment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "108124", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A deep hypersphere approach to high-dimensional anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "125", "Issue": "", "Page": "109146", "JournalTitle": "Applied Soft Computing"}, {"Title": "Fuzzy rough assisted missing value imputation and feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "2773", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 115203402, "Title": "Retraction Note: A novel approach to automatic detection of presentation slides in educational videos", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-024-09984-5", "PubYear": 2024, "Volume": "36", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center of Digital Life, School of Data and Computer Science, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Communication and Design, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center of Digital Life, School of Data and Computer Science, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center of Digital Life, School of Data and Computer Science, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Security, Guilin University of Electronic Technology, Guilin, China; Corresponding author."}], "References": []}, {"ArticleId": *********, "Title": "Game-Theoretic Analysis of the Interaction of Economic Agents in the Cournot Oligopoly with Consideration of the Linear Structure, the Green Effect and Fairness Concern", "Abstract": "A comparative analysis of the efficiency of various ways of organization of economic agents is carried out, taking into account the structure and regulations of their interaction in the models of the Cournot oligopoly. The Cournot oligopoly models in the form of a supply chain are constructed and analytically investigated, taking into account the green effect and concern for fairness. For symmetric models of the Cournot oligopoly with different ways of organization of economic agents, the respective structures of social and individual preferences are analytically obtained. A numerical study of the Cournot oligopoly models in various forms with asymmetrical agents has been carried out, and the corresponding structures of social and individual preferences have been obtained.", "Keywords": "", "DOI": "10.31857/S0005117924020088", "PubYear": 2024, "Volume": "85", "Issue": "2", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Hourglass cascaded recurrent stereo matching network", "Abstract": "Stereo matching acts a crucial role in computer vision and robotics applications. An accurate cost volume and robust disparity regression method are essential for stereo matching of high accuracy. Following GCNet and PSMNet, constructing 4D cost volume and then using the soft argmin method to regress has been dominated. However, it will encounter many difficulties due to the multi-modal distribution of cost volume. One of the reasons for this multi-modal distribution is the occlusion area which not be possible to find a matching region on the reference image and rarely discussed. In this paper, we propose to use global context information could improve the performance of model in occluded regions. Recently, novel recurrent neural network regression methods are proposed, but most of them regress disparity maps from 3D cost volume. In this paper, we propose the new combinatorial paradigm that combine stacked hourglass modules and recurrent neural networks to further aggregate 4D cost volume and regress disparity respectively. The proposed method can be seamlessly integrated into most stereo matching networks, we improved the accuracy by 45% for PSMNet and 38% for GwcNet in our experiment. Experimental results on Scene Flow, KITTI2012, KITTI2015, and ETH3D datasets show our method is competitive. The code is available at: https://github.com/truman1211/HCRnet .", "Keywords": "", "DOI": "10.1016/j.imavis.2024.105074", "PubYear": 2024, "Volume": "147", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yuan", "Affiliation": "College of Applied Mathematics, Chengdu University of Information Technolog, Chengdu 610225, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "Jiancheng Hu", "Affiliation": "College of Applied Mathematics, Chengdu University of Information Technolog, Chengdu 610225, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Communication and Information Engineering, Chongqing University of Posts and Telecommunications, Chongqing 400065, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Applied Mathematics, Chengdu University of Information Technolog, Chengdu 610225, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Applied Mathematics, Chengdu University of Information Technolog, Chengdu 610225, PR China"}], "References": [{"Title": "EdgeStereo: An Effective Multi-task Learning Network for Stereo Matching and Edge Detection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "910", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "NLCA-Net: a non-local context attention network for stereo matching", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "e18", "JournalTitle": "APSIPA Transactions on Signal and Information Processing"}, {"Title": "Attention-guided aggregation stereo matching network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104088", "JournalTitle": "Image and Vision Computing"}, {"Title": "Domain-adaptive modules for stereo matching network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "461", "Issue": "", "Page": "217", "JournalTitle": "Neurocomputing"}, {"Title": "Edge supervision and multi-scale cost volume for stereo matching", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "117", "Issue": "", "Page": "104336", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 115203516, "Title": "A maturity-driven selection model for effective digital transformation governance mechanisms in large organizations", "Abstract": "", "Keywords": "", "DOI": "10.1108/K-10-2023-2136", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115203564, "Title": "Prototype learning based generic multiple object tracking via point-to-box supervision", "Abstract": "Generic multiple object tracking aims to recover the trajectories for generic moving objects of the same category. This task relies on the ability of effectively extracting representative features of the target objects. To this end, we propose a novel prototype learning based model, PLGMOT, that can explore the template features of an exemplar object and extend to more objects to acquire their prototype. Their prototype features can be continuously updated during the video, in favor of generalization to all the target objects with different appearances. More importantly, on the public benchmark GMOT-40, our method achieves more than 14% advantage over the state-of-the-art methods, with less than 0.5% of the training data that is not even completely annotated in the form of bounding boxes, thanks to our proposed point-to-box label refinement training algorithm and hierarchical motion-aware association algorithm.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110588", "PubYear": 2024, "Volume": "154", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, 350108, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, 350108, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, 350108, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> She", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, 350108, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou, 350108, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Dalhousie University, Halifax, Canada"}], "References": [{"Title": "LaSOT: A High-quality Large-scale Single Object Tracking Benchmark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "2", "Page": "439", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Online multiple object tracking using joint detection and embedding network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "108793", "JournalTitle": "Pattern Recognition"}, {"Title": "Similarity based person re-identification for multi-object tracking using deep Siamese network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "20", "Page": "18171", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Fast re-OBJ: real-time object re-identification in rigid scenes", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "6", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Real-time siamese multiple object tracker with enhanced proposals", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109141", "JournalTitle": "Pattern Recognition"}, {"Title": "SAPENet: Self-Attention based Prototype Enhancement Network for Few-shot Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109170", "JournalTitle": "Pattern Recognition"}, {"Title": "TAT: Targeted backdoor attacks against visual object tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "109629", "JournalTitle": "Pattern Recognition"}, {"Title": "Transformer-based visual object tracking via fine–coarse concatenated attention and cross concatenated MLP", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "146", "Issue": "", "Page": "109964", "JournalTitle": "Pattern Recognition"}, {"Title": "SCGTracker: Spatio-temporal correlation and graph neural networks for multiple object tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "149", "Issue": "", "Page": "110249", "JournalTitle": "Pattern Recognition"}, {"Title": "Frozen is better than learning: A new design of prototype-based classifier for semantic segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "110431", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 115203574, "Title": "Artificial Intelligence for Optimal Sizing and Location of DG in Power Systems", "Abstract": "When placing dispersed production resources into distribution systems, it is necessary to review and evaluate a wide range of operating conditions and system security. A large number of indicators are used in evaluating the optimal location of distributed production resources; In such a way that the grouping of these indicators in obtaining a multi-objective indicator as well as obtaining their optimal weight is usually taken into account in the industry. In this article, using the genetic algorithm, the optimal weights for the said performance indicator were obtained, and based on this, the optimal positioning and sizing of the distributed generation sources in the IEEE 33-bus and IEEE 69-bus standard test networks were discussed.  Simulation results show the effectiveness of this performance indicator with optimal coefficients in optimal placement and capacity determination of dispersed production resources.", "Keywords": "DG Units;Genetic Algorithms;Smart Grids;Multi-Objective Indicator", "DOI": "10.15866/ireaco.v17i1.23372", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 27633, "JournalTitle": "International Review of Automatic Control (IREACO)", "ISSN": "1974-6059", "EISSN": "1974-6067", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratory of Automation Electrical Systems Environment (LAESE), National Engineering School of Monastir (ENIM)"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Medical Instrumentation Techniques Engineering Department, Al-Hikma University College (HIUC)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Automation Electrical Systems Environment (LAESE), National Engineering School of Monastir (ENIM)"}], "References": []}, {"ArticleId": 115203638, "Title": "Technical Perspective: From Binary Join to Free Join", "Abstract": "<p>Most queries access data from more than one relation, which makes joins between relations an extremely common operation. In many cases the execution time of a query is dominated by the processing of the involved joins. This observation has led to a wide range of techniques to speed up join processing like, e.g. efficient hash joins, bitmap filters to eliminate non-joining tuples early on, blocked lookups to hide cache latencies, and many others.</p>", "Keywords": "", "DOI": "10.1145/3665252.3665258", "PubYear": 2024, "Volume": "53", "Issue": "1", "JournalId": 23659, "JournalTitle": "ACM SIGMOD Record", "ISSN": "0163-5808", "EISSN": "1943-5835", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TUM"}], "References": []}, {"ArticleId": 115203685, "Title": "Shifted window-based Transformer with multimodal representation for the systematic staging of rectal cancer", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11761-024-00400-3", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4313, "JournalTitle": "Service Oriented Computing and Applications", "ISSN": "1863-2386", "EISSN": "1863-2394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Parkinson disease prediction using machine learning-based features from speech signal", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "1", "Page": "101", "JournalTitle": "Service Oriented Computing and Applications"}, {"Title": "Analytical study of the encoder-decoder models for ultrasound image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "1", "Page": "81", "JournalTitle": "Service Oriented Computing and Applications"}]}, {"ArticleId": 115203821, "Title": "Expanding the wrench feasible workspace of quadrotor-based mobile cable-driven parallel manipulators using multi-objective optimization and machine learning", "Abstract": "Quadrotor-based Reconfigurable Cable-Driven Parallel Manipulators (QRCDPMs) have previously been introduced as having significant potential in extending the feasible workspace of Cable-Driven Parallel Manipulators (CDPMs). However, such configurations have limitations in executing positive Z-direction trajectories resulting in non-optimal workspace and performance. This study proposes a novel Quadrotor-based Enhanced Reconfiguration Cable-Driven Parallel Manipulator (QERCDPM) that significantly enhances the Wrench Feasible Workspace (WFW). This allows for extended safe platform operation along all XYZ directions by adjusting the bottom cable exit point. Simulation results indicate that the proposed setup achieves a 96.39 % ratio of safe workspace to available workspace in various cable exit points configurations. Tension Distribution analysis for different trajectories demonstrates the proposed setup's superior cable tension management, increased flexibility and reachability. A Random Forest based Particle Swarm Optimization - Modified Frequency Bat Algorithm (RFPSOMFB) optimization algorithm is proposed, which outperforms existing methods in terms of run time and solution quality. The findings hold significance for applications requiring safe and efficient trajectory execution in dynamic and constrained environments, contributing to the evolution of multi-robot based intelligent and distributed systems.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2024.109273", "PubYear": 2024, "Volume": "117", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, SASTRA Deemed to be University, Thanjavur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, SASTRA Deemed to be University, Thanjavur, India;Corresponding author at: School of Mechanical Engineering, SASTRA Deemed to be University, Thanjavur, India"}], "References": [{"Title": "Multi-objective path planning of an autonomous mobile robot using hybrid PSO-MFB optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106076", "JournalTitle": "Applied Soft Computing"}, {"Title": "Autonomous navigation and obstacle avoidance of an omnidirectional mobile robot using swarm optimization and sensors deployment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "3", "Page": "172988142092949", "JournalTitle": "International Journal of Advanced Robotic Systems"}, {"Title": "Mobile robot path planning using fuzzy enhanced improved Multi-Objective particle swarm optimization (FIMOPSO)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116875", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-objective particle swarm optimization with multi-mode collaboration based on reinforcement learning for path planning of unmanned air vehicles", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109075", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Machine learning aided multi-objective optimization and multi-criteria decision making: Framework and two applications in chemical engineering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "165", "Issue": "", "Page": "107945", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Investigative analysis of different mutation on diversity-driven multi-parent evolutionary algorithm and its application in area coverage optimization of WSN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "14", "Page": "9565", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 115203861, "Title": "Microstructure and mechanical properties of Al-Cu alloy during wire and arc additive manufacturing by adding micron TiB\n <sub>2</sub>\n particles", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2024.2351170", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "Huisheng Ren", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People’s Republic of China;Shandong Provincial Key Laboratory of Special Welding Technology, Harbin Institute of Technology at Weihai, Weihai, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People’s Republic of China;Shandong Provincial Key Laboratory of Special Welding Technology, Harbin Institute of Technology at Weihai, Weihai, People’s Republic of China"}, {"AuthorId": 3, "Name": "Haoyu Kong", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People’s Republic of China;Shandong Provincial Key Laboratory of Special Welding Technology, Harbin Institute of Technology at Weihai, Weihai, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People’s Republic of China;Shandong Provincial Key Laboratory of Special Welding Technology, Harbin Institute of Technology at Weihai, Weihai, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People’s Republic of China;Shandong Provincial Key Laboratory of Special Welding Technology, Harbin Institute of Technology at Weihai, Weihai, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People’s Republic of China;Shandong Provincial Key Laboratory of Special Welding Technology, Harbin Institute of Technology at Weihai, Weihai, People’s Republic of China"}], "References": []}, {"ArticleId": 115203911, "Title": "Matrix normal PCA for interpretable dimension reduction and graphical noise modeling", "Abstract": "Principal component analysis (PCA) is one of the most widely used dimension reduction and multivariate statistical techniques. From a probabilistic perspective, PCA seeks a low-dimensional representation of data in the presence of independent identical Gaussian noise. Probabilistic PCA (PPCA) and its variants have been extensively studied for decades. Most of them assume the underlying noise follows a certain independent identical distribution. However, the noise in the real world is usually complicated and structured. To address this challenge, some variants of PCA for non-IID data have been proposed. However, most of the existing methods only assume that the noise is correlated in the feature space while there may exist two-way structured noise. To this end, we propose a powerful and intuitive PCA method (MN-PCA) through modeling the graphical noise by the matrix normal distribution, which enables us to explore the structure of noise in both the feature space and the sample space. MN-PCA obtains a low-rank representation of data and the structure of noise simultaneously. And it can be explained as approximating data over the generalized Mahalanobis distance. We first solve this model by a standard approach – maximizing the regularized likelihood – and then develop a novel algorithm that exploits the Wasserstein distance, which is more robust. Extensive experiments on various data demonstrate their effectiveness.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110591", "PubYear": 2024, "Volume": "154", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "NCMIS, CEMS, RCSDS, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, China;School of Mathematics Sciences, University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NCMIS, CEMS, RCSDS, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, China;School of Mathematics Sciences, University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "NCMIS, CEMS, RCSDS, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, China;School of Mathematics Sciences, University of Chinese Academy of Sciences, Beijing 100049, China;Key Laboratory of Systems Health Science of Zhejiang Province, School of Life Science, Hangzhou Institute for Advanced Study, University of Chinese Academy of Sciences, Chinese Academy of Sciences, Hangzhou 310024, China;Corresponding author at: NCMIS, CEMS, RCSDS, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, China"}], "References": [{"Title": "A generalized least-squares approach regularized with graph embedding for dimensionality reduction", "Authors": "Xi<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107023", "JournalTitle": "Pattern Recognition"}, {"Title": "Adaptive quantile low-rank matrix factorization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107310", "JournalTitle": "Pattern Recognition"}, {"Title": "Robust semi-supervised nonnegative matrix factorization for image clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107683", "JournalTitle": "Pattern Recognition"}, {"Title": "Poisson PCA for matrix count data", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "109401", "JournalTitle": "Pattern Recognition"}, {"Title": "Self-paced principal component analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "109692", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 115203943, "Title": "Reconstruction of seamless harmonized Landsat Sentinel-2 (HLS) time series via self-supervised learning", "Abstract": "The Harmonized Landsat Sentinel-2 (HLS) data, harmonizing Landsat-8/9 and Sentinel-2 imagery, offers frequent 30 m resolution multispectral observations but is often contaminated by clouds, shadows, and snow that reduce the availability of good-quality surface observations. Traditional techniques for reconstructing HLS time series, such as polynomial, logistic, or harmonic functions that model seasonal reflectance changes struggle with complex changes violating the function fitting assumptions. We propose a data-driven time series reconstruction framework based on Transformer, termed self-supervised learning for interpolation (SSLI) with a smoothing constraint to model seasonal reflectance change patterns without any annotated labels. In this study, a year of HLS 30 m data were processed into 3-day surface reflectance composites (i.e., 122 composites). SSLI was trained by using randomly selected 70% of the good-quality 3-day composites in a time series to reconstruct the reflectance for the remaining 30%. The random masking was undertaken independently for each HLS pixel time series and for each training iteration so that the selected composite periods cover all the good-quality periods evenly. The methodology was tested on five diverse regions in the Conterminous United States (CONUS) each using three HLS tiles. Two versions of SSLI were evaluated. SSLI i was trained using time series samples from one tile per region to impose data independence, and SSLI ii was trained using samples from all the tiles to simulate data availability in real-world applications. The results were compared with those of three state-of-the practice approaches for gap-filling reflectance and Normalized difference vegetation index (NDVI) time series, i.e., the fill-and-fit (FF), the dynamic temporal smoothing (DTS), and the double logistic (DL) algorithms. The superior performance of SSLI, reflected in lower RMSE (SSLI i: 0.0192, SSLI ii: 0.0164) and higher R<sup>2</sup> (SSLI i: 0.9150, SSLI ii: 0.9349) compared to the other algorithms, demonstrates much higher accuracy in reconstructing complex phenological changes with multiple greenness peaks (e.g., in cropland) and robustness to temporal cadence variations and time series noise. The potential of adapting SSLI for land cover mapping and global-scale time series reconstruction is discussed. The developed codes and training data were made publicly available.", "Keywords": "", "DOI": "10.1016/j.rse.2024.114191", "PubYear": 2024, "Volume": "308", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geography and Resource Management, The Chinese University of Hong Kong, Shatin, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Geospatial Sciences Center of Excellence, Department of Geography and Geospatial Sciences, South Dakota State University, Brookings, SD 57007, USA;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Geography, The University of Hong Kong, Pokfulam, Hong Kong;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Geography, Environment, & Spatial Sciences, and the Center for Global Change and Earth Observations, Michigan State University, East Lansing, MI 48824, USA"}, {"AuthorId": 5, "Name": "Khuong K. Tran", "Affiliation": "Geospatial Sciences Center of Excellence, Department of Geography and Geospatial Sciences, South Dakota State University, Brookings, SD 57007, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geography, Environment, & Spatial Sciences, and the Center for Global Change and Earth Observations, Michigan State University, East Lansing, MI 48824, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Geospatial Sciences Center of Excellence, Department of Geography and Geospatial Sciences, South Dakota State University, Brookings, SD 57007, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Geography, Environment, & Spatial Sciences, and the Center for Global Change and Earth Observations, Michigan State University, East Lansing, MI 48824, USA"}], "References": [{"Title": "Towards national-scale characterization of grassland use intensity from integrated Sentinel-2 and Landsat time series", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "238", "Issue": "", "Page": "111124", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Robust Landsat-based crop time series modelling", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "238", "Issue": "", "Page": "110810", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Lessons learned implementing an operational continuous United States national land change monitoring capability: The Land Change Monitoring, Assessment, and Projection (LCMAP) approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "238", "Issue": "", "Page": "111356", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Continental-scale land surface phenology from harmonized Landsat 8 and Sentinel-2 imagery", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111685", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A within-season approach for detecting early growth stages in corn and soybean using high temporal and spatial resolution imagery", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "242", "Issue": "", "Page": "111752", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Spatially and temporally complete Landsat reflectance time series modelling: The fill-and-fit approach", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111718", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Landsat 9: Empowering open science and applications through continuity", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111968", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Thick cloud removal in Landsat images based on autoregression of Landsat time-series data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "249", "Issue": "", "Page": "112001", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Sharpening ECOSTRESS and VIIRS land surface temperature using harmonized Landsat-Sentinel surface reflectances", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "251", "Issue": "", "Page": "112055", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Reconstructing daily 30 m NDVI over complex agricultural landscapes using a crop reference curve approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112156", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A Bayesian model to estimate land surface phenology parameters with harmonized Landsat 8 and Sentinel-2 images", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "261", "Issue": "", "Page": "112471", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Long time-series NDVI reconstruction in cloud-prone regions via spatio-temporal tensor completion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112632", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Validation of the U.S. Geological Survey's Land Change Monitoring, Assessment and Projection (LCMAP) Collection 1.0 annual land cover products 1985–2017", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "265", "Issue": "", "Page": "112646", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Multiscale assessment of land surface phenology from harmonized Landsat 8 and Sentinel-2, PlanetScope, and PhenoCam imagery", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "266", "Issue": "", "Page": "112716", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Monitoring standing herbaceous biomass and thresholds in semiarid rangelands from harmonized Landsat 8 and Sentinel-2 imagery to support within-season adaptive management", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "112907", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Conterminous United States Landsat-8 top of atmosphere and surface reflectance tasseled cap transformation coefficients", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "274", "Issue": "", "Page": "112992", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A new object-class based gap-filling method for PlanetScope satellite image time series", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113136", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A novel algorithm for the generation of gap-free time series by fusing harmonized Landsat 8 and Sentinel-2 observations with PhenoCam time series for detecting land surface phenology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "282", "Issue": "", "Page": "113275", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "CRYSTAL: A novel and effective method to remove clouds in daily nighttime light images by synergizing spatiotemporal information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "295", "Issue": "", "Page": "113658", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Demonstration of large area land cover classification with a one dimensional convolutional neural network applied to single pixel temporal metric percentiles", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "295", "Issue": "", "Page": "113653", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 115203962, "Title": "Sextortion: Prevalence and correlates in 10 countries", "Abstract": "The growing threat of sexual extortion (”sextortion”) has garnered significant attention in the news and by law enforcement agencies around the world. Foundational knowledge of prevalence and risk factors, however, is still nascent. The present study surveyed 16,693 respondents, distributed equally across 10 different countries, to assess prevalence of victimization and perpetration of threatening to disseminate intimate images. Weighted by gender, age, region, and population, 14.5% of respondents indicated at least one experience of victimization, while 4.8% of respondents indicated perpetration of the same. Demographic risk factors for perpetration and victimization were also assessed. Consistent with findings from other studies, men (15.7%) were 1.17 times more likely to report being victimized compared to women (13.2%), and 1.43 times more likely to report perpetration. LGBTQ+ respondents were 2.07 times more likely to report victimization compared to non-LGBTQ+ respondents, and 2.51 times more likely to report offending behaviors. Age was significantly associated, with younger participants more likely to report both victimization and perpetration experiences. The most common type of perpetrator, as reported by victims, was a former or current partner. Despite the strong likelihood of under-reporting given the topic area, the study found that experiencing threats to distribute intimate content is a relatively commonplace occurrence, impacting 1 in 7 adults. Implications for potential mitigation are discussed.", "Keywords": "Nonconsensual explicit imagery; Sextortion; Sexual extortion; Technology facilitated gender-based violence; Image-based sexual abuse; Cybercrime", "DOI": "10.1016/j.chb.2024.108298", "PubYear": 2024, "Volume": "158", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Social Equity Research Centre, RMIT University, Melbourne, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Google, 1600 Amphitheatre Parkway, Mountain View 94043, CA, United States"}], "References": [{"Title": "Homophobia is online: Sexual victimization and risks on the internet and mental health among bisexual, homosexual, pansexual, asexual, and queer adolescents", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "106728", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Image-based sexual abuse: Victim-perpetrator overlap and risk-related correlates of coerced sexting, non-consensual dissemination of intimate images, and cyberflashing", "Authors": "<PERSON>; <PERSON>; <PERSON>dell", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "107879", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": *********, "Title": "Synthesis of Bidirectional Programs from Examples with Functional Dependencies", "Abstract": "", "Keywords": "", "DOI": "10.2197/ipsjjip.32.451", "PubYear": 2024, "Volume": "32", "Issue": "", "JournalId": 18954, "JournalTitle": "Journal of Information Processing", "ISSN": "", "EISSN": "1882-6652", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The Graduate University for Advanced Studies, SOKENDAI; National Institute of Informatics"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Graduate University for Advanced Studies, SOKENDAI; National Institute of Informatics"}, {"AuthorId": 3, "Name": "Zhenjiang Hu", "Affiliation": "National Institute of Informatics; School of Computer Science, Peking University"}], "References": [{"Title": "Provenance-guided synthesis of Datalog programs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Synbit: synthesizing bidirectional programs using unidirectional sketches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": *********, "Title": "When line meets agile in public service organizations: Exploring the role of felt accountability amongst line managers", "Abstract": "<p>Despite citizen calls for agile government, public service organizations often default to hierarchy and adopt dual structure organization designs combining agile and non-agile units. However, ensuring effective collaboration and avoiding accountability challenges at the interface of line and agile units remains a vexing issue. Although accountability is implicitly assumed in agile organizing, it is not readily manifested or experienced. Through this interpretive case study of a public service organization in the Nordics, we examine through the lens of felt accountability, the reaction and roles of line managers to emergent accountability challenges precipitated by parallel maintenance of agile and non-agile unit combinations.</p>", "Keywords": "", "DOI": "10.3233/IP-230057", "PubYear": 2024, "Volume": "29", "Issue": "2", "JournalId": 28496, "JournalTitle": "Information Polity", "ISSN": "1570-1255", "EISSN": "1875-8754", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "How Enterprises Adopt Agile Forms of Organizational Design", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "1", "Page": "84", "JournalTitle": "ACM SIGMIS Database"}, {"Title": "Incorporating agile practices in public sector IT management: A nudge toward adaptive governance", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "3", "Page": "251", "JournalTitle": "Information Polity"}]}, {"ArticleId": *********, "Title": "Automatic exploration and transfer design of associative rules in She Ethnic Clothing Coloration", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19357-8", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Pinghua Xu", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Wenqing Jiang", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "U2-Net: Going deeper with nested U-structure for salient object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "107404", "JournalTitle": "Pattern Recognition"}, {"Title": "Multimedia webpage visual design and color emotion test", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "2", "Page": "2621", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Digital image color analysis method to extract fashion color semantics from artworks", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "11", "Page": "17115", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Coloration Parsing and Transfer Design of Han Folk Costumes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "4", "Page": "1", "JournalTitle": "Journal on Computing and Cultural Heritage"}, {"Title": "Test the configuration and color of 3D model space design with web multimedia interface", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "11", "Page": "33107", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Color aesthetics in cultural and creativive packaging designs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "12", "Page": "35607", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Autoencoders and their applications in machine learning: a survey", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "2", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 115204055, "Title": "Smart traffic control: machine learning for dynamic road traffic management in urban environments", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19331-4", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Artificial intelligence based commuter behaviour profiling framework using Internet of things for real-time decision-making", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "16057", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An Improved Method of Nonmotorized Traffic Tracking and Classification to Acquire Traffic Parameters at Intersections", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "2", "Page": "312", "JournalTitle": "International Journal of Intelligent Transportation Systems Research"}, {"Title": "Traffic Lights Analysis and Simulation Using Fuzzy Inference System of Mamdani on Three-Signaled Intersections.", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "268", "JournalTitle": "Procedia Computer Science"}, {"Title": "A dynamic discarding technique to increase speed and preserve accuracy for YOLOv3", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "16", "Page": "9961", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Machine learning driven intelligent and self adaptive system for traffic management in smart cities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "5", "Page": "1203", "JournalTitle": "Computing"}, {"Title": "Smart cities: Fusion-based intelligent traffic congestion control system for vehicular networks using machine learning techniques", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "3", "Page": "417", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "MotionTrack: rethinking the motion cue for multiple object tracking in USV videos", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "4", "Page": "2761", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 115204071, "Title": "Load flow balancing in super smart grids: A review of technical challenges, possible solutions and future trends from European prospective", "Abstract": "Policies aim to increase the penetration of renewable energy resources (RERs) to minimize the dependence on fossil fuels, have compelled different countries to devise their diverse blueprints by transforming smart grids infrastructure into futuristic grids or super smart grids (SSGs). Europe is taking the lead by developing SSGs by 2050, based on two exclusive alternatives, i.e., totally based on RERs and dispersed large decentralized networks. Critically assessing tensions in balancing load flow associated with RERs is a complicated task in SSGs. To solve this concern, the advanced probabilistic model is adopted in this research to observe the variations in demand and response profiles and mitigate it through transmission network planning using a super smart node transmission network topology (SSN). The proposed SSN transmission network topology outperforms the power synergy hub (PSHub) topology used in the existing literature through achieving an optimal load flow balancing in SSGs power infrastructure. This is also verified through simulation results, which provides an effective utilization of continuous spinning reserves (CSRs) and vehicle to grid (V2G) topologies integrated in an SSN transmission network topology for managing an optimized load flow balancing in SSGs. Additionally, this research work also provides a comprehensive overview of technical challenges in SSGs in terms of power flow control, their possible solutions, and future prospects. Finally, this paper concluded with the 2050 framework of futuristic SSGs, which provides a road map path for a sustainable and green energy environment for European infrastructure.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2024.109265", "PubYear": 2024, "Volume": "117", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electrical Engineering Department, National University of Computer & Emerging Sciences, Chiniot-Faisalabad, Pakistan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, National University of Computer & Emerging Sciences, Chiniot-Faisalabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, National University of Computer & Emerging Sciences, Chiniot-Faisalabad, Pakistan"}], "References": [{"Title": "Experts and intelligent systems for smart homes’ Transformation to Sustainable Smart Cities: A comprehensive review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122380", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 115204119, "Title": "Retraction Note: Ear recognition system using adaptive approach Runge–Kutta (AARK) threshold segmentation with ANFIS classification", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-024-09958-7", "PubYear": 2024, "Volume": "36", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Kalasalingam Academy of Research and Education, Virudhunagar, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Kalasalingam Academy of Research and Education, Virudhunagar, India"}], "References": []}, {"ArticleId": 115204168, "Title": "Multivariate and hybrid data-driven models to predict thermoelectric power plants fuel consumption", "Abstract": "Ensuring the reliable operation of diesel/Heavy fuel oil (HFO) engines in Thermoelectric Power Plants (TPPs) requires constant monitoring and control of operational parameters. Time series forecasting methods can predict physical system characteristics based on historical data, which can help regulate engine operational parameters. Despite the widespread use of these techniques in coal and natural gas powered TPPs, further examination of their application in large diesel/HFO engines in Brazilian TPPs operating under dispatch conditions is needed. This work investigated the fuel consumption generalization capacity of linear, nonlinear, and hybrid prediction models, both considering univariate and multivariate approaches, related to a TPP engine-driven generator in Pernambuco, Brazil. For multivariate model development, exogenous variables were selected based on change of causality overtime analysis, which sought not only to infer the relationship between signals but also to identify the influence regime of each causality during operations. The feature selection step initially identified a performance improvement when eight additional features were used. However, each feature causality throughout the operations indicated that only four signals helped significantly predict consumption in different ways. The exogenous variables introduction to ARIMA and NAR models resulted in significant improvements only to the nonlinear approach, NARX, which could recognize operational disturbances more quickly when compared to other analyzed models. In addition, the application of additive and multivariate hybrid models provided the ability to detect more complex variations and, at the same time, maintain model stability during full-load operation, benefiting from linear and nonlinear characteristics capturing ability related to the combined model.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124219", "PubYear": 2024, "Volume": "252", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Polytechnic School of Pernambuco, Pernambuco University, 455 Benfica St., Madalena, Recife 50720-001, Brazil;Advanced Institute of Technology and Innovation (IATI), 31 Potyra St., Prado, Recife 50751-310, Brazil;Corresponding author at: Polytechnic School of Pernambuco, Pernambuco University, 455 Benfica St., Madalena, Recife 50720-001, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Institute of Technology and Innovation (IATI), 31 Potyra St., Prado, Recife 50751-310, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronics, Federal University of Technology–Paraná (UTFPR), 330 Dr. <PERSON> <PERSON>., <PERSON><PERSON><PERSON>, Ponta Grossa 84017-220, Brazil"}, {"AuthorId": 4, "Name": "Carmelo J.A. <PERSON>-Filho", "Affiliation": "Polytechnic School of Pernambuco, Pernambuco University, 455 Benfica St., Madalena, Recife 50720-001, Brazil"}], "References": [{"Title": "An adaptive hybrid system using deep learning for wind speed forecasting", "Authors": "Paulo S.G. de Mattos Neto; João F.L. <PERSON> Oliveira; Domingos S. de O. Santos <PERSON>ior", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "495", "JournalTitle": "Information Sciences"}, {"Title": "Deep recurrent modelling of Granger causality with latent confounding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "118036", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Causal variable selection for industrial process quality prediction via attention-based GRU network", "Authors": "Le Yao; Zhiqiang Ge", "PubYear": 2023, "Volume": "118", "Issue": "", "Page": "105658", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 115204173, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0957-4174(24)01075-3", "PubYear": 2024, "Volume": "250", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [], "References": []}, {"ArticleId": 115204278, "Title": "A Unified Model Fusing Region of Interest Detection and Super Resolution for Video Compression", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.049057", "PubYear": 2024, "Volume": "79", "Issue": "3", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115204458, "Title": "A compliant constant-force mechanism with sub-Newton force and millimeter stroke output", "Abstract": "Force control technology plays a pivotal role in manipulating vulnerable objects. This paper presents a new compliant mechanism with constant-force output characteristics, which can prevent force damage due to displacement overshoot. Due to its unique passive structure, it is exempted from intricate sensors and complicated controllers. The quasi-static constant-force mechanism is obtained by the parallel combination of positive- and negative-stiffness mechanisms. Such a straightforward method facilitates a rapid development and implementation process. The procedures of architectural arrangement selection, theoretical model establishment, parameter sensitivity evaluation, and multi-objective structural optimization are carried out to achieve specified constant-force characteristics. Moreover, the prototype fabrication and performance testing of a constant-force mechanism have been performed. The results demonstrate that the mechanism delivers a constant output force of 339 mN in the motion range of 1.84 mm. The constant-force feature is solely governed by the actuating displacement and is independent of driving speed and acceleration. The promising application of the constant-force mechanism has been demonstrated by manipulating small force-sensitive objects such as biological eggs.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115465", "PubYear": 2024, "Volume": "374", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electromechanical Engineering, Faculty of Science and Technology, University of Macau, Avenida da Universidade, Taipa, Macau, China"}, {"AuthorId": 2, "Name": "Qingsong Xu", "Affiliation": "Department of Electromechanical Engineering, Faculty of Science and Technology, University of Macau, Avenida da Universidade, Taipa, Macau, China;Corresponding author"}], "References": [{"Title": "An Overview of Procedures and Tools for Designing Nonstandard Beam-Based Compliant Mechanisms", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "134", "Issue": "", "Page": "103001", "JournalTitle": "Computer-Aided Design"}, {"Title": "Recent design and development of piezoelectric-actuated compliant microgrippers: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "331", "Issue": "", "Page": "113002", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "FEA-based optimization and experimental verification of a typical flexure-based constant force module", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "332", "Issue": "", "Page": "113083", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Design of a new passive end-effector based on constant-force mechanism for robotic polishing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102278", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Design, analysis and evaluation of a self-lockable constant-force compliant gripper", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "335", "Issue": "", "Page": "113354", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Sensor-based force decouple controller design of macro–mini manipulator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "", "Page": "102415", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Automatic Optimization for Compliant Constant Force Mechanisms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "2", "Page": "61", "JournalTitle": "Actuators"}, {"Title": "An integrated modeling method for piezo-actuated compliant mechanisms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "364", "Issue": "", "Page": "114770", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 115204502, "Title": "IoT-Enabled crop storage monitoring system", "Abstract": "<p xml:lang=\"en\">This paper introduces an innovative IoT-enabled crop storage monitoring system designed to revolutionize the preservation and quality assurance of stored agricultural produce. Central to this system are IoT sensors strategically deployed within storage facilities. These sensors are tasked with the continuous tracking and monitoring of crucial environmental parameters, specifically temperature, humidity, and gas levels. Leveraging this real-time data, the system is engineered to promptly detect and respond to any deviations from the prescribed optimal storage conditions. The core strength of this system lies in its ability to generate instantaneous alerts upon detecting irregularities. These alerts serve as preemptive measures, effectively averting potential spoilage and curtailing post-harvest losses by enabling timely interventions and corrective actions. By harnessing IoT technology, this paper aims to create a proactive, automated, and responsive framework that ensures the integrity and safety of stored agricultural produce, ultimately contributing to enhanced food security and sustainability.</p>", "Keywords": "", "DOI": "10.26634/jcom.11.4.20654", "PubYear": 2024, "Volume": "11", "Issue": "4", "JournalId": 43475, "JournalTitle": "i-manager's Journal on Computer Science", "ISSN": "2347-2227", "EISSN": "2347-6141", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DMI St. John the Baptist University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DMI St. John the Baptist University"}], "References": [{"Title": "Internet of Things (IoT) and Agricultural Unmanned Aerial Vehicles (UAVs) in smart farming: A comprehensive review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "", "Page": "100187", "JournalTitle": "Internet of Things"}, {"Title": "Internet of Things-Based Smart Farming Monitoring System for Bolting Reduction in Onion Farms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}]}, {"ArticleId": 115204571, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-7051(24)00568-9", "PubYear": 2024, "Volume": "295", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [], "References": []}, {"ArticleId": 115204600, "Title": "Data-Driven Identification and Analysis of Waiting Times in Business Processes: A Systematic Literature Review", "Abstract": "Reducing waiting times in end-to-end business processes is a recurrent concern in the field of business process management. The uptake of data-driven approaches in this field in the past two decades, most notably process mining, has created new opportunities for fine-grained analysis of waiting times based on execution data. As a result, a wide range of approaches for waiting time identification and analysis on the basis of business process execution data have been reported in the literature. In many instances, different approaches have considered different notions of waiting time and different causes for waiting time. At present, there is a lack of a consolidated overview of these manifold approaches, and how they relate to or complement each other. The article presents a literature review that starts with the question of what approaches for identification and analysis of waiting time are available in the literature, and then refines this question by adding questions which shed light onto different causes and notions of waiting time. The survey leads to a multidimensional taxonomy of data-driven waiting time analysis techniques, in terms of purpose, causes, and measures. The survey identifies gaps in the field, chiefly a scarcity of integrated multi-causal approaches to analyze waiting times in business processes, and a lack of empirically validated approaches in the field.", "Keywords": "Business process management; Process mining; Waiting time", "DOI": "10.1007/s12599-024-00868-5", "PubYear": 2025, "Volume": "67", "Issue": "2", "JournalId": 5738, "JournalTitle": "Business & Information Systems Engineering", "ISSN": "2363-7005", "EISSN": "1867-0202", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Tartu, Tartu, Estonia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tartu, Tartu, Estonia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tartu, Tartu, Estonia"}], "References": [{"Title": "Predicting performances in business processes using deep neural networks", "Authors": "Gyunam Park; Minseok Song", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "113191", "JournalTitle": "Decision Support Systems"}, {"Title": "Process Mining for Six Sigma", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "3", "Page": "277", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Assessing product quality from the production process logs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "5-6", "Page": "1615", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Prescriptive process monitoring: <i>Quo vadis</i> ?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e1097", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 115204637, "Title": "Study on the long-term medical effects of radiation in overexposed people", "Abstract": "<b >Objective</b> To investigate the long-term effects of radiation exposure on health and provide empirical data for radiological research. <b >Methods</b> The clinical symptoms, lens, cytogenetic, immune function and tumor incidence of 45 people exposed to ionizing radiation in the dose range of 0.02–1.36 Gy were observed. <b >Results</b> Compared with those of cytogenetic examination, lens opacity and tumor detection, the detection rate of lymphocyte micronuclei in the medical follow-up over-exposure group, interventional radiation group and nuclear medicine group was greater than that in the conventional radiation group, and the detection rate was the highest in the medical follow-up over-exposure group. There was a significant difference between the medical follow-up overexposure group and the interventional radiation group, and the differences among the medical follow-up overexposure group, the interventional radiation group and the conventional radiation group were statistically significant. <b >Conclusions</b> Excessive exposure has certain effects on lens function, immune function and cytogenetics, and the detection rate increases with increasing dose, reflecting a certain dose‒effect relationship. Genetic damage can persist in an infected person, increasing the risk of cancer.", "Keywords": "Radiation accident; Acute radiation sickness; Far-back effect; Medical follow-up", "DOI": "10.1016/j.jrras.2024.100955", "PubYear": 2024, "Volume": "17", "Issue": "3", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Gansu Provincial Center for Disease Control and Prevention, Joint Laboratory of Institute of Radiology, Chinese Academy of Medical Sciences, NO.310 Donggang West Road, Lanzhou, Gansu, 730000, China;Corresponding author. Tel.: + 86- 0931-8264944; fax: +86-0931-8266115"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Gansu Provincial Center for Disease Control and Prevention, Joint Laboratory of Institute of Radiology, Chinese Academy of Medical Sciences, NO.310 Donggang West Road, Lanzhou, Gansu, 730000, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Gansu Provincial Center for Disease Control and Prevention, Joint Laboratory of Institute of Radiology, Chinese Academy of Medical Sciences, NO.310 Donggang West Road, Lanzhou, Gansu, 730000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Gansu Provincial Center for Disease Control and Prevention, Joint Laboratory of Institute of Radiology, Chinese Academy of Medical Sciences, NO.310 Donggang West Road, Lanzhou, Gansu, 730000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Gansu Provincial Center for Disease Control and Prevention, Joint Laboratory of Institute of Radiology, Chinese Academy of Medical Sciences, NO.310 Donggang West Road, Lanzhou, Gansu, 730000, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Gansu Provincial Center for Disease Control and Prevention, Joint Laboratory of Institute of Radiology, Chinese Academy of Medical Sciences, NO.310 Donggang West Road, Lanzhou, Gansu, 730000, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Gansu Provincial Center for Disease Control and Prevention, Joint Laboratory of Institute of Radiology, Chinese Academy of Medical Sciences, NO.310 Donggang West Road, Lanzhou, Gansu, 730000, China"}], "References": []}, {"ArticleId": 115204646, "Title": "« Défense et technologies digitales. Compte rendu de La cyberdéfense. Politique de l’espace numérique »", "Abstract": "", "Keywords": "", "DOI": "10.4000/ijb6", "PubYear": 2023, "Volume": "37-3/4", "Issue": "", "JournalId": 24661, "JournalTitle": "Netcom", "ISSN": "0987-6014", "EISSN": "2431-210X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115204700, "Title": "Évaluer le contenu des notifications d’alerte diffusées en France via FR-Alert® : enjeux scientifiques et retombées opérationnelles", "Abstract": "<p lang=\"fr\"><p>En France, depuis juin 2022, les autorités préfectorales ont la possibilité d’envoyer une notification d’alerte sur les téléphones portables des individus situés dans une zone de danger, depuis une plateforme appelée FR-Alert®. Si de nombreux travaux montrent que ce message doit être clair, précis et formulé avec des mots simples, pour être compris par le plus grand nombre, aucune étude n’avait toutefois été menée pour analyser le contenu de messages réellement diffusés et le comparer à l’état des connaissances scientifiques et opérationnelles. Pour combler ce manque, une grille d’analyse a été appliquée sur 100 notifications d’exercices et 29 alertes réelles, envoyées entre mai 2022 et février 2024. Cette grille liste la fréquence des éléments présents (pour calculer un score) et permet la comparaison entre toutes les notifications. Parmi les résultats, on note que : 1) la plupart des notifications ont suivi les préconisations (69% incluent au moins 50% des attendus) ; 2) des messages s’écartent des préconisations et soulèvent des questions sur leur appréhension par les publics visés ; 3) la temporalité de l’événement (heure de début) et l’insertion d’un lien internet pour certifier le statut de l’émetteur ont très souvent été oubliés. La démultiplication des exercices, l’utilisation répété par certaines préfectures ou l’envoi d’alertes réelles n’induisent toutefois pas d’évolution dans les scores, ce qui amène à questionner le transfert des connaissances vers les acteurs opérationnels et à imaginer différentes pistes d’évolution pour la plateforme FR-Alert®.</p></p>", "Keywords": "", "DOI": "10.4000/w8a0", "PubYear": 2023, "Volume": "37-3/4", "Issue": "", "JournalId": 24661, "JournalTitle": "Netcom", "ISSN": "0987-6014", "EISSN": "2431-210X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115204707, "Title": "Sybil attack vulnerability trilemma", "Abstract": "", "Keywords": "", "DOI": "10.1080/17445760.2024.2352740", "PubYear": 2024, "Volume": "39", "Issue": "4", "JournalId": 14122, "JournalTitle": "International Journal of Parallel, Emergent and Distributed Systems", "ISSN": "1744-5760", "EISSN": "1744-5779", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, King's College London, London, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Imperial College London, London, UK;I-X Centre for AI In Science, Imperial College London, London, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Informatics, King's College London, London, UK"}], "References": [{"Title": "Detecting Sybil attacks in vehicular ad hoc networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "69", "JournalTitle": "International Journal of Parallel, Emergent and Distributed Systems"}, {"Title": "The Energy Consumption of Blockchain Technology: Beyond Myth", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "6", "Page": "599", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Byzantine-tolerant uniform node sampling service in large-scale networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "412", "JournalTitle": "International Journal of Parallel, Emergent and Distributed Systems"}, {"Title": "Cypherpunk ideology: objectives, profiles, and influences (1992–1998)", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "315", "JournalTitle": "Internet Histories"}, {"Title": "Blockchain-enabled Peer-to-Peer energy trading", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "107299", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Sybil attacks on identity-augmented Proof-of-Stake", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "199", "Issue": "", "Page": "108424", "JournalTitle": "Computer Networks"}, {"Title": "Machine economies", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "1", "Page": "1", "JournalTitle": "Electronic Markets"}, {"Title": "Security and dependability analysis of blockchain systems in partially synchronous networks with Byzantine faults", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Parallel, Emergent and Distributed Systems"}]}, {"ArticleId": 115204766, "Title": "A study of supervised machine learning algorithms for traffic prediction in SD-WAN", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJWGS.2024.138600", "PubYear": 2024, "Volume": "20", "Issue": "2", "JournalId": 17784, "JournalTitle": "International Journal of Web and Grid Services", "ISSN": "1741-1106", "EISSN": "1741-1114", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115204808, "Title": "Cholera detection system using CNN machine learning algorithm", "Abstract": "<p xml:lang=\"en\">A comprehensive cholera detection system leveraging cutting-edge technologies such as neural networks, machine learning, chatbots, live maps, and real-time statistical graphs is proposed. The system integrates a user-friendly chatbot interface to interact with individuals, prompting them to input relevant health information and symptoms. Behind the scenes, neural networks and machine learning algorithms analyze the data to detect potential cholera cases, offering users instant insights into their health status. The system incorporates live maps to track reported cases geographically, enabling a swift response from health authorities. Moreover, real-time statistical graphs provide dynamic visualizations of cholera trends, aiding in the identification of potential outbreak hotspots. By amalgamating these technologies, the cholera detection system not only facilitates early diagnosis and intervention but also enhances public health monitoring and management, contributing to the overall control and prevention of cholera outbreaks.</p>", "Keywords": "", "DOI": "10.26634/jse.18.3.20656", "PubYear": 2024, "Volume": "18", "Issue": "3", "JournalId": 39941, "JournalTitle": "i-manager’s Journal on Software Engineering", "ISSN": "0973-5151", "EISSN": "2230-7168", "Authors": [{"AuthorId": 1, "Name": "Zakeyu <PERSON>", "Affiliation": "DMI-St John the Baptist University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DMI-St John the Baptist University"}], "References": [{"Title": "Design, simulation and performance analysis bio-sensors for the detection of cholera and diarrhea using MEMS technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "2", "Page": "419", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 115204829, "Title": "On the Use of Digital Twin Data in Models Related to Considering the Environment Impact on Enterprises", "Abstract": "Digital twins reflect the state of the environment and the activities of enterprises affected by the hydrometeorological conditions. It is proposed to use models to calculate indicators for assessing impacts of natural hazards or of climate change; of forecasts of these impacts; damage estimates; of calculating the cost of actions to protect enterprises; of assessing the feasibility of carrying out preventive actions in order to optimize them. Requirements for impact assessment models working with a digital twin are given. The difficulties in using such models are presented. Proposals for the development of impact models are being considered. A diagram of the use of digital twins in modeling impacts of environmental on enterprises is shown.", "Keywords": "", "DOI": "10.31857/S0005117924030064", "PubYear": 2024, "Volume": "85", "Issue": "3", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "All-Russian Research Institute of Hydrometeorological Information — World Data Center, Obninsk, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "All-Russian Research Institute of Hydrometeorological Information — World Data Center, Obninsk, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Obninsk Institute of Atomic Energy — department of the National Research Nuclear University “MEPhI”, Obninsk, Russia"}], "References": []}, {"ArticleId": 115204840, "Title": "MeshLink: A surface structured mesh generation framework to facilitate automated data linkage", "Abstract": "With the rapid progress in engineering simulation, there is an increasing industrial need for accurate mesh generation methods. Furthermore, the development of data-driven methods require an innovative mesh generation framework that can integrate deep learning models to facilitate automatic data linkage. This paper develops a surface structured mesh generation framework named MeshLink. This framework extends the support for mesh data and associated algorithms through the Mesh-based Feature Information Framework (MFIF). First, in order to map the model to the parametric domain, we discretize the input model using triangular meshes. To handle complex geometric shapes, we develop a structured mesh generation technique based on conformal mapping. Then, we generate the surface structured mesh of the model based on inverse mapping algorithm. The MeshLink framework overcomes the limitations of traditional mesh generation workflows by integrating deep learning models. We adopted a structured mesh evaluation model based on graph neural network, which enhance the efficiency of the framework. Finally, based on the mesh quality evaluation results, we use the corresponding mesh optimization algorithm to generate high-quality surface structure meshes. The MeshLink framework not only provides a tool that supports high-quality surface structured mesh generation, but also facilitates the storage, linking and retrieval of mesh data sources.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2024.103661", "PubYear": 2024, "Volume": "194", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing 100048, China;Beijing Key Laboratory of Big Data Technology for Food Safety, Beijing 100048, China;National Engineering Laboratory For Agri-product Quality Traceability, Beijing 100048, China"}, {"AuthorId": 2, "Name": "Haisheng Li", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing 100048, China;Beijing Key Laboratory of Big Data Technology for Food Safety, Beijing 100048, China;National Engineering Laboratory For Agri-product Quality Traceability, Beijing 100048, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing 100048, China;Beijing Key Laboratory of Big Data Technology for Food Safety, Beijing 100048, China;National Engineering Laboratory For Agri-product Quality Traceability, Beijing 100048, China"}], "References": [{"Title": "NNW-GridStar: Interactive structured mesh generation software for aircrafts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "102803", "JournalTitle": "Advances in Engineering Software"}, {"Title": "MVE-Net: An Automatic 3-D Structured Mesh Validity Evaluation Framework Using Deep Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "141", "Issue": "", "Page": "103104", "JournalTitle": "Computer-Aided Design"}, {"Title": "MGNet: a novel differential mesh generation method based on unsupervised neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "5", "Page": "4409", "JournalTitle": "Engineering with Computers"}, {"Title": "Video restoration based on deep learning: a comprehensive survey", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "6", "Page": "5317", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A digital engineering framework to facilitate automated data exchange between geometric inspection and structural analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "183", "Issue": "", "Page": "103498", "JournalTitle": "Advances in Engineering Software"}]}, {"ArticleId": 115204844, "Title": "Signal Recognition without State Space Expansion Based on Observations Containing a Singular Interference: The Case of Nonlinear Parameters of Basis Functions", "Abstract": "<p lang=\"ru\"><p>Предлагается новый метод распознавания совокупности сигналов (из заданного ансамбля, с линейно и нелинейно входящими в них параметрами) в условиях существенной априорной неопределенности, не позволяющей воспользоваться известными статистическими методами. Сигналы могут присутствовать в аддитивной смеси, содержащей шум наблюдений и сингулярную помеху, при этом закон распределения шума полагается неизвестным, а считается заданной лишь его корреляционная матрица. Метод инвариантен к данной помехе, не требует традиционного расширения пространства состояний и обеспечивает декомпозицию и распараллеливание вычислительной процедуры. Для представления сигналов и помехи используются традиционные линейные спектральные разложения с неизвестными коэффициентами и заданными базисными функциями. Анализируются случайные и методические погрешности, а также достигаемый вычислительный эффект. Приводится иллюстративный пример.</p></p>", "Keywords": "", "DOI": "10.31857/S0005231024020052", "PubYear": 2024, "Volume": "", "Issue": "2", "JournalId": 58264, "JournalTitle": "Автоматика и телемеханика", "ISSN": "0005-2310", "EISSN": "2413-9777", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "АО «Концерн Радиоэлектронные технологии»"}], "References": []}, {"ArticleId": 115204988, "Title": "Sample-analysis based adversarial attack with saliency map", "Abstract": "With the widespread application of deep learning, the vulnerability of neural networks has attracted considerable attention, raising reliability and security concerns. Therefore, research on the robustness of neural networks has become increasingly critical. In this paper, we propose a novel sample-analysis based robustness evaluation method that overcomes the drawbacks of existing techniques, such as solving difficulty, single strategy, and loose radius. Our algorithm comprises two parts: robustness evaluation and adversarial attacks. Specifically, we introduce formal definitions of multiple sample types and a general solution to the problem of adversarial samples. We formulate a disturbance model-based description of adversarial samples in the adversarial attack algorithm and utilize saliency map to solve them. Our experimental results demonstrate that our adversarial attack algorithm not only achieves a high attack success rate in a relatively small disturbance range but also generates multiple adversarial examples for each clean example. Our algorithm can evaluate the robustness of complex datasets and models, overcome the lack of a single strategy in solving adversarial examples, and provide a more accurate radius of robustness.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111733", "PubYear": 2024, "Volume": "161", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computing Technologies, Swinburne University of Technology, Australia"}], "References": [{"Title": "A game-based approximate verification of deep neural networks with provable guarantees", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "807", "Issue": "", "Page": "298", "JournalTitle": "Theoretical Computer Science"}, {"Title": "A3CMal: Generating adversarial samples to force targeted misclassification by reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107505", "JournalTitle": "Applied Soft Computing"}, {"Title": "Generating facial expression adversarial examples based on saliency map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "104318", "JournalTitle": "Image and Vision Computing"}, {"Title": "Saliency Attack: Towards Imperceptible Black-box Adversarial Attack", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Bayesian evolutionary optimization for crafting high-quality adversarial examples with limited query budget", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110370", "JournalTitle": "Applied Soft Computing"}, {"Title": "Universal backdoor attack on deep neural networks for malware detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "110389", "JournalTitle": "Applied Soft Computing"}, {"Title": "MISPSO-Attack: An efficient adversarial watermarking attack based on multiple initial solution particle swarm optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110777", "JournalTitle": "Applied Soft Computing"}, {"Title": "Stealthy dynamic backdoor attack against neural networks for image classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "149", "Issue": "", "Page": "110993", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning-based fusion networks with high-order attention mechanism for 3D object detection in autonomous driving scenarios", "Authors": "<PERSON><PERSON> Jiang; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "111253", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 115205084, "Title": "A bizarre synthesized cascaded optimized predictor (BizSCOP) model for enhancing security in cloud systems", "Abstract": "<p>Due to growing network data dissemination in cloud, the elasticity, pay as you go options, globally accessible facilities, and security of networks have become increasingly important in today's world. Cloud service providers, including AWS, Azure, GCP, and others, facilitate worldwide expansion within minutes by offering decentralized communication network functions, hence providing security to cloud is still remains a challenging task. This paper aims to introduce and evaluate the Biz-SCOP model, a novel intrusion detection system developed for cloud security. The research addresses the pressing need for effective intrusion detection in cloud environments by combining hybrid optimization techniques and advanced deep learning methodologies. The study employs prominent intrusion datasets, including CSE-CIC-IDS 2018, CIC-IDS 2017, and a cloud intrusion dataset, to assess the proposed model's performance. The study's design involves implementing the Biz-SCOP model using Matlab 2019 software on a Windows 10 OS platform, utilizing 8 GB RAM and an Intel core i3 processor. The hybrid optimization approach, termed HyPSM, is employed for feature selection, enhancing the model's efficiency. Additionally, an intelligent deep learning model, C2AE, is introduced to discern friendly and hostile communication, contributing to accurate intrusion detection. Key findings indicate that the Biz-SCOP model outperforms existing intrusion detection systems, achieving notable accuracy (99.8%), precision (99.7%), F1-score (99.8%), and GEO (99.9%). The model excels in identifying various attack types, as demonstrated by robust ROC analysis. Interpretations and conclusions emphasize the significance of hybrid optimization and advanced deep learning techniques in enhancing intrusion detection system performance. The proposed model exhibits lower computational load, reduced false positives, ease of implementation, and improved accuracy, positioning it as a promising solution for cloud security.</p>", "Keywords": "Cloud Security;Intrusion detection system (IDS);Amazon web services (AWS);Deep Learning;Hybrid optimization;Bizarre synthesized cascaded optimized predictor (BizSCOP);And learning rate estimation", "DOI": "10.1186/s13677-024-00657-1", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Anna University, Chennai, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences, Saveetha University, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, St<PERSON>’s College of Engineering, Chennai, India"}], "References": [{"Title": "Enhanced Honeypot cryptographic scheme and privacy preservation for an effective prediction in cloud security", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "103719", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A Design of an Integrated Cloud-based Intrusion Detection System with Third Party Cloud Service", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "365", "JournalTitle": "Open Computer Science"}, {"Title": "Intrusion detection systems in the cloud computing: A comprehensive and deep literature review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "e6646", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Network optimization using defender system in cloud computing security based intrusion detection system withgame theory deep neural network (IDSGT-DNN)", "Authors": "E Balamurugan; Abolfazl Mehbodniya; <PERSON><PERSON>", "PubYear": 2022, "Volume": "156", "Issue": "", "Page": "142", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A Survey on Intrusion Detection Systems for Fog and Cloud Computing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "3", "Page": "89", "JournalTitle": "Future Internet"}, {"Title": "Deep Learning Based Distributed Intrusion Detection in Secure Cyber Physical Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "2067", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "A Two-layer Fog-Cloud Intrusion Detection Model for IoT Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "100557", "JournalTitle": "Internet of Things"}, {"Title": "An efficient optimal security system for intrusion detection in cloud computing environment using hybrid deep learning technique", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "173", "Issue": "", "Page": "103236", "JournalTitle": "Advances in Engineering Software"}, {"Title": "A systematic review on effective energy utilization management strategies in cloud data centers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Extremely boosted neural network for more accurate multi-stage Cyber attack prediction in cloud computing environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "An LSTM ‐based novel near‐real‐time multiclass network intrusion detection system for complex cloud environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "11", "Page": "e8024", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Marine Goal Optimizer Tuned Deep BiLSTM-Based Self-Configuring Intrusion Detection in Cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "22", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Grid Computing"}, {"Title": "DL-HIDS: deep learning-based host intrusion detection system using system calls-to-image for containerized cloud environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "9", "Page": "12218", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Deep learning-based network anomaly detection and classification in an imbalanced cloud environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "232", "Issue": "", "Page": "1636", "JournalTitle": "Procedia Computer Science"}, {"Title": "Toward Generating a New Cloud-Based Distributed Denial of Service (DDoS) Dataset and Cloud Intrusion Traffic Characterization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "4", "Page": "195", "JournalTitle": "Information"}, {"Title": "Towards Detection of Network Anomalies using Machine Learning Algorithms on the NSL-KDD Benchmark Datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "233", "Issue": "", "Page": "960", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 115205163, "Title": "A comprehensive survey of convergence analysis of beetle antennae search algorithm and its applications", "Abstract": "In recent years, swarm intelligence optimization algorithms have been proven to have significant effects in solving combinatorial optimization problems. Introducing the concept of evolutionary computing, which is currently a hot research topic, into swarm intelligence optimization algorithms to form novel swarm intelligence optimization algorithms has proposed a new research direction for better solving combinatorial optimization problems. The longhorn beetle whisker search algorithm is an emerging heuristic algorithm, which originates from the simulation of longhorn beetle foraging behavior. This algorithm simulates the touch strategy required by longhorn beetles during foraging, and achieves efficient search in complex problem spaces through bioheuristic methods. This article reviews the research progress on the search algorithm for longhorn beetles from 2017 to present. Firstly, the basic principle and model structure of the beetle whisker search algorithm were introduced, and its differences and connections with other heuristic algorithms were analyzed. Secondly, this paper summarizes the research achievements of scholars in recent years on the improvement of longhorn whisker search algorithms. Then, the application of the beetle whisker search algorithm in various fields was explored, including function optimization, engineering design, and path planning. Finally, this paper summarizes the research achievements of scholars in recent years on the improvement of the longhorn whisker search algorithm, and proposes future research directions, including algorithm deep learning fusion, processing of multimodal problems, etc. Through this review, readers will have a comprehensive understanding of the research status and prospects of the longhorn whisker search algorithm, providing useful guidance for its application in practical problems.", "Keywords": "Beetle antennae search algorithm; Swarm intelligence; Convergence analysis; Heuristic algorithm; Combinatorial optimization; Application", "DOI": "10.1007/s10462-024-10789-0", "PubYear": 2024, "Volume": "57", "Issue": "6", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Intelligent Manufacturing and Electroniclectronic Engineeringngineering, Wenzhou University of Technology, Wenzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Intelligent Manufacturing and Electroniclectronic Engineeringngineering, Wenzhou University of Technology, Wenzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Intelligent Manufacturing and Electroniclectronic Engineeringngineering, Wenzhou University of Technology, Wenzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Intelligent Manufacturing and Electroniclectronic Engineeringngineering, Wenzhou University of Technology, Wenzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligent Manufacturing and Electroniclectronic Engineeringngineering, Wenzhou University of Technology, Wenzhou, China; Wenzhou Key Laboratory of New Energy Materials and Devices, Wenzhou University of Technology, Wenzhou, China; Corresponding author."}], "References": [{"Title": "A beetle antennae search algorithm based on Lévy flights and adaptive strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "35", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Tracking control of redundant mobile manipulator: An RNN based metaheuristic approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "400", "Issue": "", "Page": "272", "JournalTitle": "Neurocomputing"}, {"Title": "Beetle antenna strategy based grey wolf optimization", "Authors": "Qingsong Fan; <PERSON><PERSON><PERSON> Huang; <PERSON><PERSON> Li", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113882", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Preaching-inspired swarm intelligence algorithm and its applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106552", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A clustering based Swarm Intelligence optimization technique for the Internet of Medical Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114648", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Enhanced Beetle Antennae Search with Zeroing Neural Network for online solution of constrained optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "447", "Issue": "", "Page": "294", "JournalTitle": "Neurocomputing"}, {"Title": "A Collaborative Beetle Antennae Search Algorithm Using Memory Based Adaptive Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "6", "Page": "440", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Joint application of multi-object beetle antennae search algorithm and BAS-BP fuel cost forecast network on optimal active power dispatch problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107149", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Improved whale optimization algorithm and its application in heterogeneous wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "5", "Page": "155014772110181", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Smart surgical control under RCM constraint using bio-inspired network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "121", "JournalTitle": "Neurocomputing"}, {"Title": "Application of modified beetle antennae search algorithm and BP power flow prediction model on multi-objective optimal active power dispatch", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "108027", "JournalTitle": "Applied Soft Computing"}, {"Title": "Dual Beetle Antennae Search system for optimal planning and robust control of 5-link biped robots", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "60", "Issue": "", "Page": "101556", "JournalTitle": "Journal of Computational Science"}, {"Title": "Non-linear Activated Beetle Antennae Search: A novel technique for non-convex tax-aware portfolio optimization problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "116631", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimised trajectory tracking control for quadrotors based on an improved beetle antennae search algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "3", "Page": "382", "JournalTitle": "Journal of Control and Decision"}, {"Title": "Using Quadratic Interpolated Beetle Antennae Search for Higher Dimensional Portfolio Selection Under Cardinality Constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "62", "Issue": "4", "Page": "1413", "JournalTitle": "Computational Economics"}, {"Title": "An improved beetle antennae search algorithm with Lévy flight and its application in micro-laser assisted turning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101732", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Review and empirical analysis of sparrow search algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "10", "Page": "10867", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Intelligent Beetle Antenna Search with Deep Transfer Learning Enabled Medical Image Classification Model", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "46", "Issue": "3", "Page": "3159", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Path-following and collision-avoidance guidance of unmanned sailboats based on beetle antennae search optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "7", "Page": "2105", "JournalTitle": "Robotica"}, {"Title": "A path planning algorithm for mobile robot based on water flow potential field method and beetle antennae search algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Li", "PubYear": 2023, "Volume": "109", "Issue": "", "Page": "108730", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 115205178, "Title": "Deep multimodal fusion for 3D mineral prospectivity modeling: Integration of geological models and simulation data via canonical-correlated joint fusion networks", "Abstract": "Data-driven three-dimensional (3D) mineral prospectivity modeling (MPM) employs diverse 3D exploration indicators to express geological architecture and associated characteristics in ore systems. The integration of 3D geological models with 3D computational simulation data enhances the effectiveness of 3D MPM in representing the geological architecture and its coupled geodynamic processes that govern mineralization. Despite variations in modality (i.e., data source, representation, and information abstraction levels) between geological models and simulation data, the cross-modal gap between these two types of data remains underexplored in 3D MPM. This paper presents a novel 3D MPM approach that robustly fuses multimodal information from geological models and simulation data. Acknowledging the coupled and correlated nature of geological architectures and geodynamic processes, a joint fusion strategy is employed, aligning information from both modalities by enforcing their correlation. A joint fusion neural network is devised to extract maximally correlated features from geological models and simulation data, fusing them in a cross-modality feature space. Specifically, correlation analysis (CCA) regularization is utilized to maximize the correlation between features of the two modalities, guiding the network to learn coordinated and joint fused features associated with mineralization. This results in a more effective 3D mineral prospectivity model that harnesses the strengths from both modalities for mineral exploration targeting. The proposed method is evaluated in a case study of the world-class Jiaojia gold deposit, NE China. Extensive experiments were carried out to compare the proposed method with state-of-the-art methods, methods using unimodal data, and variants without CCA regularization. Results demonstrate the superior performance of the proposed method in terms of prediction accuracy and targeting efficacy, highlighting the importance of CCA regularization in enhancing predictive power in 3D MPM.", "Keywords": "", "DOI": "10.1016/j.cageo.2024.105618", "PubYear": 2024, "Volume": "188", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Metallogenic Prediction of Nonferrous Metals and Geological Environment Monitoring (MOE), School of Geosciences and Info-Physics, Central South University, Changsha, 410083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Metallogenic Prediction of Nonferrous Metals and Geological Environment Monitoring (MOE), School of Geosciences and Info-Physics, Central South University, Changsha, 410083, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Metallogenic Prediction of Nonferrous Metals and Geological Environment Monitoring (MOE), School of Geosciences and Info-Physics, Central South University, Changsha, 410083, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Forestry, Central South University of Forestry and Technology, Changsha 410004, China;Key Laboratory of Metallogenic Prediction of Nonferrous Metals and Geological Environment Monitoring (MOE), School of Geosciences and Info-Physics, Central South University, Changsha, 410083, China"}, {"AuthorId": 5, "Name": "Xinyue Li", "Affiliation": "Key Laboratory of Metallogenic Prediction of Nonferrous Metals and Geological Environment Monitoring (MOE), School of Geosciences and Info-Physics, Central South University, Changsha, 410083, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Key Laboratory of Metallogenic Prediction of Nonferrous Metals and Geological Environment Monitoring (MOE), School of Geosciences and Info-Physics, Central South University, Changsha, 410083, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "MNR Laboratory of Metallogeny and Mineral Resource Assessment, Institute of Mineral Resources, Chinese Academy of Geological Sciences, Beijing, 100037, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "MNR Laboratory of Metallogeny and Mineral Resource Assessment, Institute of Mineral Resources, Chinese Academy of Geological Sciences, Beijing, 100037, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geodesy and Geoinformation, Vienna University of Technology, Vienna, 1040, Austria"}, {"AuthorId": 10, "Name": "Xiancheng Mao", "Affiliation": "Key Laboratory of Metallogenic Prediction of Nonferrous Metals and Geological Environment Monitoring (MOE), School of Geosciences and Info-Physics, Central South University, Changsha, 410083, China"}], "References": [{"Title": "Recognizing multivariate geochemical anomalies for mineral exploration by combining deep learning and one-class support vector machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "104484", "JournalTitle": "Computers & Geosciences"}, {"Title": "Mineral prospectivity mapping using a joint singularity-based weighting method and long short-term memory network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "158", "Issue": "", "Page": "104974", "JournalTitle": "Computers & Geosciences"}, {"Title": "Learning 3D mineral prospectivity from 3D geological models using convolutional neural networks: Application to a structure-controlled hydrothermal gold deposit", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "161", "Issue": "", "Page": "105074", "JournalTitle": "Computers & Geosciences"}, {"Title": "Preliminary geological mapping with convolution neural network using statistical data augmentation on a 3D model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "105187", "JournalTitle": "Computers & Geosciences"}, {"Title": "Multi-condition controlled sedimentary facies modeling based on generative adversarial network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "171", "Issue": "", "Page": "105290", "JournalTitle": "Computers & Geosciences"}, {"Title": "Infomax-based deep autoencoder network for recognition of multi-element geochemical anomalies linked to mineralization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "175", "Issue": "", "Page": "105341", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": *********, "Title": "Search for a Suboptimal Solution to the Dynamic Traveling Salesman Problem by the Monte Carlo Method", "Abstract": "<p lang=\"ru\"><p>Рассматривается задача составления плана обхода прямолинейно движущихся в одну точку целей для простых движений перехватчика (коммивояжера). Предлагаются новый критерий задачи на основе начального разбиения области возможного перехвата, а также алгоритм поиска субоптимального плана обхода на основе построения дерева поиска решения методом Монте-Карло. Разработана численная реализация алгоритма, проведено моделирование и статистически проанализированы полученные планы обхода целей.</p></p>", "Keywords": "", "DOI": "10.31857/S0005231024020065", "PubYear": 2024, "Volume": "", "Issue": "2", "JournalId": 58264, "JournalTitle": "Автоматика и телемеханика", "ISSN": "0005-2310", "EISSN": "2413-9777", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Институт проблем управления им. В.А. Трапезникова РАН"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Институт проблем управления им. В.А. Трапезникова РАН"}], "References": [{"Title": "An analysis of Single-Player <PERSON> Tree Search performance in Sokoban", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116224", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Monte Carlo Tree Search: a review of recent modifications and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "3", "Page": "2497", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 115205271, "Title": "Retraction Note: Exploring a narrative-based framework for historical exhibits combining JanusVR with photometric stereo", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-024-09971-w", "PubYear": 2024, "Volume": "36", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Animation, Eastern Liaoning University, Dandong, China; Corresponding author."}], "References": []}, {"ArticleId": 115205290, "Title": "Unveiling University Groupings: A Clustering Analysis for Academic Rankings", "Abstract": "<p>The evaluation and ranking of educational institutions are of paramount importance to a wide range of stakeholders, including students, faculty members, funding organizations, and the institutions themselves. Traditional ranking systems, such as those provided by QS, ARWU, and THE, have offered valuable insights into university performance by employing a variety of indicators to reflect institutional excellence across research, teaching, international outlook, and more. However, these linear rankings may not fully capture the multifaceted nature of university performance. This study introduces a novel clustering analysis that complements existing rankings by grouping universities with similar characteristics, providing a multidimensional perspective on global higher education landscapes. Utilizing a range of clustering algorithms—K-Means, GMM, Agglomerative, and Fuzzy C-Means—and incorporating both traditional and unique indicators, our approach seeks to highlight the commonalities and shared strengths within clusters of universities. This analysis does not aim to supplant existing ranking systems but to augment them by offering stakeholders an alternative lens through which to view and assess university performance. By focusing on group similarities rather than ordinal positions, our method encourages a more nuanced understanding of institutional excellence and facilitates peer learning among universities with similar profiles. While acknowledging the limitations inherent in any methodological approach, including the selection of indicators and clustering algorithms, this study underscores the value of complementary analyses in enriching our understanding of higher educational institutions’ performance.</p>", "Keywords": "", "DOI": "10.3390/data9050067", "PubYear": 2024, "Volume": "9", "Issue": "5", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Informatics, University of Western Macedonia, 52100 Kastoria, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, University of Western Macedonia, 52100 Kastoria, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics and Telecommunications, University of Ioannina, 47100 Arta, Greece"}], "References": []}, {"ArticleId": 115205372, "Title": "Technical Perspective on 'Better Differentially Private Approximate Histograms and Heavy Hitters using the Misra-Gries Sketch'", "Abstract": "<p>The topics of private data analysis and streaming data management have both been separately the focus of much study within the data management community for many years. However, more recently there have been studies which bring these two previously isolated topics together.</p>", "Keywords": "", "DOI": "10.1145/3665252.3665254", "PubYear": 2024, "Volume": "53", "Issue": "1", "JournalId": 23659, "JournalTitle": "ACM SIGMOD Record", "ISSN": "0163-5808", "EISSN": "1943-5835", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Warwick, United Kingdom"}], "References": []}, {"ArticleId": 115205403, "Title": "自動運転の段階解除通知における運転支援ロボットの言語的配慮戦略が信頼性・煩わしさに及ぼす影響の一考察", "Abstract": "本稿では，運転支援ロボットが自動運転の段階解除を通知する際に用いる言語的配慮戦略が信頼性と煩わしさに及ぼす影響を検討する．研究の方法として，ポライトネス理論における言語的配慮戦略を参考に設計した段階解除通知の発話および，RoBoHoNとドライビングシミュレータを用いた刺激提示動画を作成した．クラウドソーシングによって240人の実験参加者を対象に，運転支援ロボットの発話を信頼性および煩わしさについて主観評価してもらうビデオベースの実験を実施した．実験の結果，自動運転解除6段階のいずれの段階においても，本実験で評価した言語的配慮戦略のうち「話し手と聞き手の両者を行動に含める」，「理由を言う」，「敬意を示す」戦略および言語的配慮を伴わない直接的な発話の信頼性が高く，かつ煩わしさが低い傾向を確認した．", "Keywords": "自動運転;運転支援ロボット;言語的配慮戦略;信頼性;煩わしさ;automatic driving;driver-assistance robots;linguistic consideration strategies;reliability;annoyance", "DOI": "10.3156/jsoft.36.2_640", "PubYear": 2024, "Volume": "36", "Issue": "2", "JournalId": 16268, "JournalTitle": "Journal of Japan Society for Fuzzy Theory and Intelligent Informatics", "ISSN": "1347-7986", "EISSN": "1881-7203", "Authors": [{"AuthorId": 1, "Name": "Tomoki MIYAMOTO", "Affiliation": "Graduate School of Informatics and Engineering, The University of Electro-Communications"}, {"AuthorId": 2, "Name": "Tomoya YAMASHITA", "Affiliation": "Graduate School of Engineering, Tokyo Polytechnic University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Tokyo Polytechnic University"}], "References": [{"Title": "Polite speech strategies and their impact on drivers’ trust in autonomous vehicles", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "107015", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 115205477, "Title": "Storage management with multi-version partitioned BTrees", "Abstract": "Modern persistent Key/Value-Stores operate on updatable datasets — massively exceeding the size of available main memory. Tree-based key/value storage management structures became particularly popular in storage engines. B + -Trees allow constant search performance, however write-heavy workloads yield inefficient write patterns to secondary storage devices and poor performance characteristics. LSM-Trees overcome this issue by horizontal partitioning fractions of data — small enough to fully reside in main memory, but require frequent maintenance to sustain search performance. To this end, firstly, we propose Multi-Version Partitioned BTrees (MV-PBT) as sole storage and index management structure in key-sorted storage engines like Key/Value-Stores. Secondly, we compare MV-PBT against LSM-Trees. The logical horizontal partitioning in MV-PBT allows leveraging recent advances in modern B + -Tree techniques in a small transparent and memory resident portion of the structure. Structural properties sustain steady read performance, even on historical data, and yield efficient write patterns as well as reduced write-amplification. We integrate MV-PBT in the WiredTiger key/value storage engine. MV-PBT offers an up to 2x increased steady throughput in comparison to LSM-Trees and several orders of magnitude in comparison to B + -Trees in a YCSB workload. Moreover, MV-PBT exhibits robust time-travel query performance and outperforms LSM-Trees by 20% and B + -Trees by an order of magnitude.", "Keywords": "Storage engine; Storage management; Index management; Append storage; Time-travel query processing", "DOI": "10.1016/j.is.2024.102403", "PubYear": 2024, "Volume": "125", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Data Management Lab, Reutlingen University, Alteburgsr. 150, Reutlingen, 72762, BW, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Data Management Lab, Reutlingen University, Alteburgsr. 150, Reutlingen, 72762, BW, Germany"}], "References": [{"Title": "LSM-based storage techniques: a survey", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "1", "Page": "393", "JournalTitle": "The VLDB Journal"}]}, {"ArticleId": *********, "Title": "A federated learning approach to network intrusion detection using residual networks in industrial IoT networks", "Abstract": "This paper introduces a sophisticated approach to network security, with a primary emphasis on utilizing deep learning for intrusion detection. In real-world scenarios, the high dimensionality of training data poses challenges for simple deep learning models and can lead to vanishing gradient issues with complex neural networks. Additionally, uploading network traffic data to a central server for training raises privacy concerns. To tackle these issues, the paper introduces a Residual Network (ResNet)-based deep learning model trained using a federated learning approach. The ResNet effectively tackles the vanishing gradient problem, while federated learning enables multiple Internet Service Providers (ISPs) or clients to engage in joint training without sharing their data with third parties. This approach enhances accuracy through collaborative learning while maintaining privacy. Experimental results on the X-IIoTID dataset indicate that the proposed model outperforms conventional deep learning and machine learning methods in terms of accuracy and other metrics used for evaluation. Specifically, the proposed methodology achieved 99.43% accuracy in a centralized environment and 99.16% accuracy in a federated environment.", "Keywords": "Intrusion detection (IDS); Industrial IoT; Deep learning; Residual networks; ML; Industry 4.0", "DOI": "10.1007/s11227-024-06153-2", "PubYear": 2024, "Volume": "80", "Issue": "13", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>sha <PERSON>", "Affiliation": "<PERSON> <PERSON> <PERSON> NIT Jalandhar, Jalandhar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON> <PERSON> NIT Jalandhar, Jalandhar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Computer Engineering, University of Limerick, Castletroy, Limerick, Ireland; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON> <PERSON> NIT Jalandhar, Jalandhar, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data Science Institute, University of Galway, Galway, Ireland"}], "References": [{"Title": "Intelligent intrusion detection based on federated learning aided long short-term memory", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "", "Page": "101157", "JournalTitle": "Physical Communication"}, {"Title": "A Novel Deep Learning-Based Intrusion Detection System for IoT Networks", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "2", "Page": "34", "JournalTitle": "Computers"}, {"Title": "Fed-ANIDS: Federated learning for anomaly-based network intrusion detection systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>el<PERSON><PERSON>", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "121000", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 115205673, "Title": "MTG_CD: Multi-scale learnable transformation graph for fault classification and diagnosis in microservices", "Abstract": "<p>The rapid advancement of microservice architecture in the cloud has led to the necessity of effectively detecting, classifying, and diagnosing run failures in microservice applications. Due to the high dynamics of cloud environments and the complex dependencies between microservices, it is challenging to achieve robust real-time system fault identification. This paper proposes an interpretable fault diagnosis framework tailored for microservice architecture, namely Multi-scale Learnable Transformation Graph for Fault Classification and Diagnosis(MTG_CD). Firstly, we employ multi-scale neural transformation and graph structure adjacency matrix learning to enhance data diversity while extracting temporal-structural features from system monitoring metrics Secondly, a graph convolutional network (GCN) is utilized to fuse the extracted temporal-structural features in a multi-feature modeling approach, which helps to improve the accuracy of anomaly detection. To identify the root cause of system faults, we finally conduct a coarse-grained level diagnosis and exploration after obtaining the results of classifying the fault data. We evaluate the performance of MTG_CD on the microservice benchmark SockShop, demonstrating its superiority over several baseline methods in detecting CPU usage overhead, memory leak, and network delay faults. The average macro F1 score improves by 14.05%.</p>", "Keywords": "Microservice architecture;Neural transformation;Graph convolution network;Fault diagnosis;Fault detection", "DOI": "10.1186/s13677-024-00666-0", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China; Corresponding author."}, {"AuthorId": 4, "Name": "Jianhua Ren", "Affiliation": "West China Second University hospital, Sichuan University, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}], "References": [{"Title": "Delta Debugging Microservice Systems with Parallel Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "16", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Graph-based root cause analysis for service-oriented and microservice architectures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "110432", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Extractive Arabic Text Summarization Using Modified PageRank Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "2", "Page": "73", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "Performance Anomaly Detection in Web Services: an RNN-based Approach Using Dynamic Quality of Service Features", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "2", "Page": "729", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A survey on anomaly detection for technical systems using LSTM networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "131", "Issue": "", "Page": "103498", "JournalTitle": "Computers in Industry"}, {"Title": "AI-Enabled Secure Microservices in Edge Computing: Opportunities and Challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "2", "Page": "1485", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Root Cause Analysis of Anomalies Based on Graph Convolutional Neural Network", "Authors": "<PERSON><PERSON><PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "8", "Page": "1155", "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering"}, {"Title": "Micro2vec: Anomaly detection in microservices systems by mining numeric representations of computer logs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "208", "Issue": "", "Page": "103515", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Effectively Detecting Operational Anomalies In Large-Scale IoT Data Infrastructures By Using A GAN-Based Predictive Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "11", "Page": "2909", "JournalTitle": "The Computer Journal"}, {"Title": "Task offloading in hybrid-decision-based multi-cloud computing network: a cooperative multi-agent deep reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "MSRA-G: Combination of multi-scale residual attention network and generative adversarial networks for hyperspectral image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "121", "Issue": "", "Page": "106017", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Identifying performance anomalies in fluctuating cloud environments: A robust correlative-GNN-based explainable approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "77", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "CausalRCA: Causal inference based precise fine-grained root cause localization for microservice applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "203", "Issue": "", "Page": "111724", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 115205837, "Title": "Highly responsive broadband Si-based MoS2 phototransistor on high-k dielectric", "Abstract": "<p>The demand for photodetectors and image sensors has grown exponentially in the past decade in biomedical, security surveillance, robotics, automotive, quality control, image recognition, and military applications, due to their superior quality, broadband detection, lower noise, and economic viability. Here, we report a MoS<sub>2</sub> channel-based phototransistor over an HfO<sub>2</sub>/n-Si substrate isolated by an hBN layer. The high photoresponse is achieved through the integration of the photoconduction, photogating, and mobility enhancement process by utilizing excellent features of MoS<sub>2</sub>, HfO<sub>2</sub>/Si, and hBN. The capacitive coupling of the photogenerated carriers by high- k dielectric HfO<sub>2</sub> leads to modulation of MoS<sub>2</sub> Fermi level due to electrostatic doping. Furthermore, the MoS<sub>2</sub> also contributes to the photogeneration of carriers due to its semiconducting nature, leading to additional photocurrent. Ultimately, the combination of photogating, photoconduction, and swift carrier extraction with remarkable mobility of 11.65 cm<sup>2</sup> · V<sup>−1</sup> · s<sup>−1</sup> results in high responsivity, external quantum efficiency, and detectivity of 4.5 × 10<sup>8</sup> A · W<sup>−1</sup>, 0.72 × 10<sup>6</sup>, and 6.20 × 10<sup>16</sup> Jones at 266 nm illumination, respectively. The device also demonstrates broadband photoresponse from 266–1000 nm wavelengths. The high responsivity distinguishes the potential of our device for the future of optoelectronics and broadband image sensing applications.</p>", "Keywords": "photogating; photoconduction; two-dimensional materials; high k dielectric; carrier scattering; phototransistor", "DOI": "10.1007/s11432-024-3994-4", "PubYear": 2024, "Volume": "67", "Issue": "6", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Integrated Circuits, State Key Laboratory of Silicon and Advanced Semiconductor Materials, Zhejiang University, Hangzhou, China; ZJU-Hangzhou Global Scientific and Technological Innovation Centre, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ZJU-Hangzhou Global Scientific and Technological Innovation Centre, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Integrated Circuits, State Key Laboratory of Silicon and Advanced Semiconductor Materials, Zhejiang University, Hangzhou, China; School of Materials Science and Engineering, State Key Laboratory of Silicon and Advanced Semiconductor Materials, Zhejiang University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "College of Integrated Circuits, State Key Laboratory of Silicon and Advanced Semiconductor Materials, Zhejiang University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Beijing Key Lab of Nanophotonics and Ultrafine Optoelectronic Systems, School of Physics, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "ZJU-Hangzhou Global Scientific and Technological Innovation Centre, Hangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>ng Xu", "Affiliation": "College of Integrated Circuits, State Key Laboratory of Silicon and Advanced Semiconductor Materials, Zhejiang University, Hangzhou, China; Corresponding author."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, State Key Laboratory of Silicon and Advanced Semiconductor Materials, Zhejiang University, Hangzhou, China; Corresponding author."}], "References": []}, {"ArticleId": 115205840, "Title": "Lightweight authentication protocol for connected medical IoT through privacy-preserving access", "Abstract": "With the rapid progress of communication technology, the Internet of Things (IoT) has emerged as an essential element in our daily lives. Given that the IoT encompasses diverse devices that often have limited resources in terms of communication, computation, and storage. Consequently, the National Institute of Standards and Technology (NIST) has standardized several lightweight cryptographic algorithms for encryption and decryption, specifically designed to meet the needs of resource-constrained IoT devices. These cryptographic algorithms, known as authenticated encryption with associated data (AEAD), offer more than just confidentiality—they also guarantee information integrity and authentication. Unlike conventional encryption algorithms like AES, which solely provide confidentiality, AEAD algorithms encompass additional functionality to achieve authenticity. This eliminates the need for separate algorithms like message authentication codes to ensure authenticity. Therefore, by leveraging the characteristics of an AEAD protocol, it is possible to develop a lightweight authentication framework to mitigate the security risks inherent in public communication channels. Therefore, in this work, we designed the lightweight authentication protocol for the smart healthcare system (BLAP-SHS) using an AEAD mechanism. In order to do this, a session key must first be created for encrypted communication. This is done via a method called mutual authentication, which verifies the legitimacy of both the user and the server. The random-or-real methodology ensures the security of the derived session key, and the Scyther tool is used to assess BLAP-SHS’ resistance to man-in-the-middle and replay attacks. Through using the technique of informal security analysis, the resilience of BLAP-SHS against denial of service, and password-guessing threats are evaluated. By juxtaposing BLAP-SHS with other prominent authentication techniques, the usefulness of BLAP-SHS is also assessed in terms of computing and communication costs. We illustrate that the BLAP-SHS requires a reduction in computation cost ranging from [70.11% to 95.21%] and a reduction in communication resources ranging from [3.85% to 9.09%], as evidenced by our comparative study.", "Keywords": "Smart healthcare system; Security; Privacy; Authentication; Encryption", "DOI": "10.1016/j.eij.2024.100474", "PubYear": 2024, "Volume": "26", "Issue": "", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Management and Technology, Lahore, 54770, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "EIAS Data Science Lab, College of Computer and Information Sciences, and Center of Excellence in Quantum and Intelligent Computing, Prince Sultan University, Riyadh 11586, Saudi Arabia;Department of Mathematics and Computer Science, Faculty of Science, Menoufia University, 32511, Egypt;Corresponding author at: EIAS Data Science Lab, College of Computer and Information Sciences, and Center of Excellence in Quantum and Intelligent Computing, Prince Sultan University, Riyadh 11586, Saudi Arabia"}], "References": [{"Title": "An improved user authentication scheme for electronic medical record systems", "Authors": "Mad<PERSON><PERSON><PERSON> R; Chaitanya S. Na<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "22007", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A secure and improved two factor authentication scheme using elliptic curve and bilinear pairing for cyber physical systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "16", "Page": "22425", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A pairing-free data authentication and aggregation mechanism for Intelligent Healthcare System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "198", "Issue": "", "Page": "282", "JournalTitle": "Computer Communications"}, {"Title": "PAAF-SHS: PUF and authenticated encryption based authentication framework for the IoT-enabled smart healthcare system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "26", "Issue": "", "Page": "101159", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 115205909, "Title": "AIR-Net: Acupoint image registration network for automatic acupoint recognition and localization", "Abstract": "Acupoint localization is integral to various Traditional Chinese Medicine (TCM) practices, including acupuncture, moxibustion, and massage. We introduce a cutting-edge atlas-based image registration framework that leverages deep neural networks for the automatic identification and localization of acupoints. This innovative approach incorporates both local and global features within a Transformer network, meticulously designed to assimilate comprehensive human body morphology and detailed acupoint data. Further bolstering our methodology is an expansive dataset consisting of 89,951 pairs of images, each meticulously annotated with acupoint labels to facilitate precise localization. By integrating body contours with specific acupoint indicators, our Transformer-based network sets a new precedent in acupoint recognition precision. Preliminary experiments demonstrate the efficacy of our proposed framework, achieving an impressive accuracy of over 90%— a significant improvement over current state-of-the-art solutions. This notable enhancement in acupoint localization underscores our method’s potential to substantially elevate the precision and reliability of TCM clinical practices.", "Keywords": "", "DOI": "10.1016/j.displa.2024.102743", "PubYear": 2024, "Volume": "83", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences, Shenzhen, Guangdong 518055, China;University of Chinese Academy of Sciences, Beijing 101408, China;Xin-Huangpu Joint Innovation Institute of Chinese Medicine, Guangzhou, China"}, {"AuthorId": 2, "Name": "Yongsheng Teng", "Affiliation": "Shenzhen University General Hospital, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences, Shenzhen, Guangdong 518055, China;Xin-Huangpu Joint Innovation Institute of Chinese Medicine, Guangzhou, China;Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Huang", "Affiliation": "Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences, Shenzhen, Guangdong 518055, China;University of Chinese Academy of Sciences, Beijing 101408, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences, Shenzhen, Guangdong 518055, China;University of Chinese Academy of Sciences, Beijing 101408, China;Xin-Huangpu Joint Innovation Institute of Chinese Medicine, Guangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Shenzhen University General Hospital, Shenzhen, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences, Shenzhen, Guangdong 518055, China;Xin-Huangpu Joint Innovation Institute of Chinese Medicine, Guangzhou, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences, Shenzhen, Guangdong 518055, China;Xin-Huangpu Joint Innovation Institute of Chinese Medicine, Guangzhou, China;Corresponding author at: Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences, Shenzhen, Guangdong 518055, China"}], "References": [{"Title": "Efficient 3D human pose estimation from RGBD sensors", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; Inmaculada Mora-<PERSON><PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102225", "JournalTitle": "Displays"}, {"Title": "LAIU-Net: A learning-to-augment incorporated robust U-Net for depressed humans’ tongue segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "", "Page": "102371", "JournalTitle": "Displays"}, {"Title": "TSRNet: Tongue image segmentation with global and local refinement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "81", "Issue": "", "Page": "102601", "JournalTitle": "Displays"}]}, {"ArticleId": 115205918, "Title": "Guardians of the Cloud: A Survey on Cloud Monitoring Architectures", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2024.13527", "PubYear": 2024, "Volume": "13", "Issue": "5", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115205931, "Title": "Can Large Language Models Be Good Companions?: An LLM-Based Eyewear System with Conversational Common Ground", "Abstract": "<p>Developing chatbots as personal companions has long been a goal of artificial intelligence researchers. Recent advances in Large Language Models (LLMs) have delivered a practical solution for endowing chatbots with anthropomorphic language capabilities. However, it takes more than LLMs to enable chatbots that can act as companions. Humans use their understanding of individual personalities to drive conversations. Chatbots also require this capability to enable human-like companionship. They should act based on personalized, real-time, and time-evolving knowledge of their users. We define such essential knowledge as the common ground between chatbots and their users, and we propose to build a common-ground-aware dialogue system from an LLM-based module, named OS-1, to enable chatbot companionship. Hosted by eyewear, OS-1 can sense the visual and audio signals the user receives and extract real-time contextual semantics. Those semantics are categorized and recorded to formulate historical contexts from which the user's profile is distilled and evolves over time, i.e., OS-1 gradually learns about its user. OS-1 combines knowledge from real-time semantics, historical contexts, and user-specific profiles to produce a common-ground-aware prompt input into the LLM module. The LLM's output is converted to audio, spoken to the wearer when appropriate. We conduct laboratory and in-field studies to assess OS-1's ability to build common ground between the chatbot and its user. The technical feasibility and capabilities of the system are also evaluated. Our results show that by utilizing personal context, OS-1 progressively develops a better understanding of its users. This enhances user satisfaction and potentially leads to various personal service scenarios, such as emotional support and assistance.</p>", "Keywords": "", "DOI": "10.1145/3659600", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 3, "Name": "Zhouyang Lu", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Information Sciences, University of Strathclyde, Glasgow, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Bayes Business School, City, University of London, London, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Oxford Suzhou Centre for Advanced Research, Suzhou, China"}, {"AuthorId": 7, "Name": "Mingzhi Dong", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 9, "Name": "Qin Lv", "Affiliation": "Department of Computer Science, University of Colorado Boulder, Boulder, Colorado, United States"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, University of Michigan, Ann Arbor, Michigan, United States"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "School of Microelectronics, Fudan University, Shanghai, China"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}], "References": [{"Title": "Challenges in Building Intelligent Open-domain Dialog Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Survey on evaluation methods for dialogue systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "755", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Tangible Privacy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Chatbots: History, technology, and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "", "Page": "100006", "JournalTitle": "Machine Learning with Applications"}, {"Title": "How Do You Feel Online", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "MemX", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Do Smart Glasses Dream of Sentimental Visions?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Privacy and Data Protection in ChatGPT and Other AI Chatbots: Strategies for Securing User Information", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Security and Privacy in Pervasive Computing"}, {"Title": "CASES: A Cognition-Aware Smart Eyewear System for Understanding How People Read", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "A Survey on Evaluation of Large Language Models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 115206140, "Title": "Indoor Scene Classification through Dual-Stream Deep Learning: A Framework for Improved Scene Understanding in Robotics", "Abstract": "<p>Indoor scene classification plays a pivotal role in enabling social robots to seamlessly adapt to their environments, facilitating effective navigation and interaction within diverse indoor scenes. By accurately characterizing indoor scenes, robots can autonomously tailor their behaviors, making informed decisions to accomplish specific tasks. Traditional methods relying on manually crafted features encounter difficulties when characterizing complex indoor scenes. On the other hand, deep learning models address the shortcomings of traditional methods by autonomously learning hierarchical features from raw images. Despite the success of deep learning models, existing models still struggle to effectively characterize complex indoor scenes. This is because there is high degree of intra-class variability and inter-class similarity within indoor environments. To address this problem, we propose a dual-stream framework that harnesses both global contextual information and local features for enhanced recognition. The global stream captures high-level features and relationships across the scene. The local stream employs a fully convolutional network to extract fine-grained local information. The proposed dual-stream architecture effectively distinguishes scenes that share similar global contexts but contain different localized objects. We evaluate the performance of the proposed framework on a publicly available benchmark indoor scene dataset. From the experimental results, we demonstrate the effectiveness of the proposed framework.</p>", "Keywords": "", "DOI": "10.3390/computers13050121", "PubYear": 2024, "Volume": "13", "Issue": "5", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "Sultan <PERSON><PERSON>", "Affiliation": "Department of Computer Science, National University of Technology, Islamabad 44000, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, College of Engineering, Umm Al-Qura University, Makkah 24382, Saudi Arabia"}], "References": [{"Title": "Restock and straightening system for retail automation using compliant and mobile manipulation", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "235", "JournalTitle": "Advanced Robotics"}, {"Title": "Indoor Home Scene Recognition Using Capsule Neural Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "440", "JournalTitle": "Procedia Computer Science"}, {"Title": "Scale-space multi-view bag of words for scene categorization", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "1", "Page": "1223", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multi-Scale Person Localization With Multi-Stage Deep Sequential Framework", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1217", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "DeepScene: Scene classification via convolutional neural network with spatial pyramid pooling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116382", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Indoor localization system using deep learning based scene recognition", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "20", "Page": "28405", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "NIR/RGB image fusion for scene classification using deep neural networks", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "7", "Page": "2725", "JournalTitle": "The Visual Computer"}, {"Title": "pNNCLR: Stochastic pseudo neighborhoods for contrastive learning based unsupervised representation learning problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "593", "Issue": "", "Page": "127810", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 115206207, "Title": "A novel robust black-box fingerprinting scheme for deep classification neural networks", "Abstract": "With the growing value and high training cost of deep neural network (DNN) models, there has been increasing research interest in deep neural network model watermarking. However, the existing literature lacks robust model watermarking methods with retrospective functionality, which hinders multi-user distribution and fine-grained copyright verification of deep neural network models. To achieve the model watermarking with retrospective functionality that is generally denoted the model fingerprinting, this study proposes a novel robust black-box fingerprinting scheme for deep classification neural networks, in which a fingerprint that is unique for each user is inserted in the given DNN. Specifically, a DWT-DCT-SVD-based embedding method is introduced to construct a high-quality poisoned image by spreading the fingerprint as a poisoned trigger to the entire image, which both leads to the traceability of the to-be-protected DNN and significantly enhances the stealthiness and anti-forgery of the embedded poisoned trigger. As the amplitude of the embedded fingerprint is rather small, it poses a challenge that makes the model hard to capture features of the embedded fingerprint during the training process. To tackle this challenge, this study then proposes a poisoned feature enhancement module, which groups classification categories of clean images without the fingerprint as one type while taking the poisoned images with the correct fingerprint and the poisoned adversarial images containing the other incorrect fingerprints as another two different types. This module, which can be removed during fingerprint verification, improves the reliability and fidelity of the model fingerprinting. Accordingly, an adversarial training strategy is designed, aiming at enhancing the effective training of classification neural network models that contain the fingerprint with small embedding strength. Combining these strategies then gives rise to the proposed scheme, which is denoted the UfNet for notational convenience. Extensive experimental simulation results demonstrate the excellent invisibility of the fingerprint in the poisoned image and the desirable verification of the fingerprint in case of even a one-bit difference. Additionally, it also exhibits robustness against the related attacks such as STRIP, Neural Cleanse, Fine-Pruning, and GradCAM, which outperforms the state-of-the-arts (SOTAs). Furthermore, the proposed UfNet is robust to collusion attacks and frame the innocent. These results show the feasibility and effectiveness of the introduced model fingerprinting framework and the proposed implementation methodology in this paper.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124201", "PubYear": 2024, "Volume": "252", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, Guangzhou 510642, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, Guangzhou 510642, People’s Republic of China;Key Laboratory of Smart Agricultural Technology in Tropical South China, Ministry of Agriculture and Rural Affairs, Guangzhou 510642, People’s Republic of China;Guangdong Provincial Key Laboratory of Agricultural Artificial Intelligence, Guangzhou 510642, People’s Republic of China;Guangzhou Key Laboratory of Intelligent Agriculture, Guangzhou 510642, People’s Republic of China;Corresponding author at: College of Mathematics and Informatics, South China Agricultural University, Guangzhou 510642, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, Guangzhou 510642, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, Guangzhou 510642, People’s Republic of China;Key Laboratory of Smart Agricultural Technology in Tropical South China, Ministry of Agriculture and Rural Affairs, Guangzhou 510642, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Informatics, South China Agricultural University, Guangzhou 510642, People’s Republic of China;Key Laboratory of Smart Agricultural Technology in Tropical South China, Ministry of Agriculture and Rural Affairs, Guangzhou 510642, People’s Republic of China;Guangzhou Key Laboratory of Intelligent Agriculture, Guangzhou 510642, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai 12478, People’s Republic of China"}], "References": [{"Title": "BlindNet backdoor: Attack on deep neural network using blind watermark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "5", "Page": "6217", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 115206303, "Title": "Design of PMN-PT-based dual-resonance acoustic emission sensor for partial discharge detection", "Abstract": "This paper presents the design, fabrication, and testing of a dual-resonance acoustic emission (AE) sensor based on PMN-PT single crystal for partial discharge (PD) detection. The use of high-performance PMN-PT single crystal enhances the sensor sensitivity. Resonance peak coupling is utilized to increase the bandwidth of the sensor effectively. The effects of piezoelectric vibrator size, vibrator spacing, and external circuitry on the sensor output are analyzed using equivalent circuits and finite element models (FEM) for sensor design. After calibration, the self-made sensor has a peak sensitivity of 76.4 dB and a bandwidth of 20 kHz to 150 kHz, which meets the demand of PD detection. The PD detection experiments verify that the self-made sensors can accurately detect PD with a high signal-to-noise ratio of 15.8 dB. In the end, the reliability test proves that the sensors have good stability and temperature characteristics. Besides above sensor’s performance improvement, our design has the great potential to reduce the sensor’s cross section, resulting better suitability on curved surfaces. The feasibility of employing PMN-PT as a replacement for PZT in the fabrication of AE sensors is proven by these good test results.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115432", "PubYear": 2024, "Volume": "373", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Tang", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electric Power Research Institute, Shanghai Municipal Electric Power Company, Shanghai 200437, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Inorganic Functional Materials and Devices, Shanghai Institute of Ceramics, University of Chinese Academy of Sciences, Shanghai 201800, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China;Corresponding author"}], "References": []}, {"ArticleId": *********, "Title": "Retraction Note: Sine–cosine algorithm for feature selection with elitism strategy and new updating mechanism", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-024-09947-w", "PubYear": 2024, "Volume": "36", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering, Universiti Malaysia Perlis (UniMAP), Arau, Perlis, Malaysia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering, Universiti Malaysia Perlis (UniMAP), Arau, Perlis, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering, Universiti Malaysia Perlis (UniMAP), Arau, Perlis, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Engineering, Universiti Malaysia Perlis (UniMAP), Arau, Perlis, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, Universiti Malaysia Perlis (UniMAP), Arau, Perlis, Malaysia"}], "References": []}, {"ArticleId": 115206418, "Title": "Detecting Users' Emotional States during Passive Social Media Use", "Abstract": "<p>The widespread use of social media significantly impacts users' emotions. Negative emotions, in particular, are frequently produced, which can drastically affect mental health. Recognizing these emotional states is essential for implementing effective warning systems for social networks. However, detecting emotions during passive social media use---the predominant mode of engagement---is challenging. We introduce the first predictive model that estimates user emotions during passive social media consumption alone. We conducted a study with 29 participants who interacted with a controlled social media feed. Our apparatus captured participants' behavior and their physiological signals while they browsed the feed and filled out self-reports from two validated emotion models. Using this data for supervised training, our emotion classifier robustly detected up to 8 emotional states and achieved 83% peak accuracy to classify affect. Our analysis shows that behavioral features were sufficient to robustly recognize participants' emotions. It further highlights that within 8 seconds following a change in media content, objective features reveal a participant's new emotional state. We show that grounding labels in a componential emotion model outperforms dimensional models in higher-resolutional state detection. Our findings also demonstrate that using emotional properties of images, predicted by a deep learning model, further improves emotion recognition.</p>", "Keywords": "", "DOI": "10.1145/3659606", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, ETH Zürich, Zurich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, ETH Zürich, Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, ETH Zürich, Zurich, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, ETH Zürich, Zurich, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, ETH Zürich, Zurich, Switzerland"}], "References": [{"Title": "Impact of content characteristics and emotion on behavioral engagement in social media: literature review and research agenda", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "2", "Page": "329", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Detection of Artifacts in Ambulatory Electrodermal Activity Data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Fine-grained emotion recognition: fusion of physiological signals and facial expressions on spontaneous emotion corpus", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "3", "Page": "162", "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing"}, {"Title": "How Do You Feel Online", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Analysis of the effect of comedic film on changes of heart rate using photoplethysmogram and electrocardiogram", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Bamba<PERSON> Set<PERSON>wan", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "208", "JournalTitle": "Procedia Computer Science"}, {"Title": "Designing for Emotion Regulation Interventions: An Agenda for HCI Theory and Research", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}]}, {"ArticleId": 115206436, "Title": "Emotional expression in mathematics e-learning using emojis: A gender-based analysis", "Abstract": "<p>Emotional feeling is a phase of neurobiological activity that plays an important role in cognitive thinking and learning, although largely overlooked in complex tutoring fields like Mathematics. This paper introduces an innovative e-learning Mathematics course integrating emojis as a feedback mechanism to express students’ emotional responses towards mathematical challenges. By providing a platform for intuitive emotional expression, this approach aims to strengthen engagement and comprehension. Through empirical investigation, emotional reactions of online mathematics students are explored, with attention to gender-related differences in emoji usage. A survey administered to 100 students prompts them to select emojis conveying their sentiments towards mathematical problems. Statistical analyses reveal that emojis effectively capture students’ emotions, with an emphasis on gender-based variations in selection. These insights illuminate the dynamics of emotional expression and hold implications for fostering comprehensive learning environments that mitigate negative emotions such as mathematical anxiety. By empowering educators to monitor students’ emotional reactions and adapt teaching strategies accordingly, this approach has the potential to cultivate confident and proficient learners essential for STEM (Science, Technology, Engineering, Mathematics) advancement.</p>", "Keywords": "", "DOI": "10.3233/IDT-240170", "PubYear": 2024, "Volume": "18", "Issue": "2", "JournalId": 36670, "JournalTitle": "Intelligent Decision Technologies", "ISSN": "1872-4981", "EISSN": "1875-8843", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Sex differences in emoji use, familiarity, and valence", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "106305", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Internet of emotional people: Towards continual affective computing cross cultures via audiovisual signals", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "294", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Emojis influence emotional communication, social attributions, and information processing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "106722", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Special Collection of Extended Selected Papers on “Novel Research Results Presented in The 12th International Conference on Information, Intelligence, Systems and Applications (IISA2021), 12–14 July 2021, Chania, Crete, Greecehttps://easyconferences.eu/iisa2021/”", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "641", "JournalTitle": "Intelligent Decision Technologies"}, {"Title": "V-GRAFFER, a system for Visual GRoup AFFect Recognition, Part I: Foundations", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "3", "Page": "631", "JournalTitle": "Intelligent Decision Technologies"}, {"Title": "Review on sentiment analysis for text classification techniques from 2010 to 2021", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "6", "Page": "8137", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Artificial Intelligence and User Experience in reciprocity: Contributions and state of the art", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "1", "Page": "73", "JournalTitle": "Intelligent Decision Technologies"}, {"Title": "A survey on sentiment analysis and its applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "29", "Page": "21567", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Real-time emotion generation in human-robot dialogue using large language models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "1271610", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 115206467, "Title": "Abusive Language Detection in Khasi Social Media Comments", "Abstract": "<p>This paper describes the work performed for automated abusive language detection in the Khasi language, a low-resource language spoken primarily in the state of Meghalaya, India. A dataset named Khasi Abusive Language Dataset (KALD) was created which consists of 4,573 human-annotated Khasi YouTube and Facebook comments. A corpus of Khasi text was built and it was used to create Khasi word2vec and fastText word embeddings. Deep learning, traditional machine learning, and ensemble models were used in the study. Experiments were performed using word2vec, fastText, and topic vectors obtained using LDA. Experiments were also performed to check if zero-shot cross-lingual nature of language models such as LaBSE and LASER can be utilized for abusive language detection in the Khasi language. The best F1 score of 0.90725 was obtained by an XGBoost classifier. After feature selection and rebalancing of the dataset, F1 score of 0.91828 and 0.91945 were obtained by an SVM based classifiers.</p>", "Keywords": "", "DOI": "10.1145/3664285", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Indian Institute of Information Technology Guwahati, Guwahati, India;Computer Science and Engineering, Assam Don Bosco University, Guwahati, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Assam Don Bosco University, Guwahati, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Jyrwa", "Affiliation": "Computer Science and Engineering, Assam Don Bosco University, Guwahati, India"}, {"AuthorId": 4, "Name": "Floriginia Shadap", "Affiliation": "Computer Science and Engineering, Assam Don Bosco University, Guwahati, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Indian Institute of Information Technology Guwahati, Guwahati, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Accenture Labs,  Bangalore, India;Computer Science and Engineering, Indian Institute of Information Technology Guwahati, Guwahati, India"}], "References": [{"Title": "Sentiment analysis on product reviews based on weighted word embeddings and deep neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "23", "Page": "e5909", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Exploring end-to-end framework towards Khasi speech recognition system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "2", "Page": "419", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Multilingual Offensive Language Identification for Low-resource Languages", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Part-of-Speech (POS) Tagging Using Deep Learning-Based Approaches on the Designed Khasi POS Corpus", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Bidirectional convolutional recurrent neural network architecture with group-wise enhancement mechanism for text sentiment classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "2098", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Detection of Hate Speech using BERT and Hate Speech Word Embedding with Deep Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "1", "Page": "2166719", "JournalTitle": "Applied Artificial Intelligence"}]}, {"ArticleId": 115206482, "Title": "Harmonizing GEDI and LVIS Data for Accurate and Large-Scale Mapping of Foliage Height Diversity", "Abstract": "", "Keywords": "", "DOI": "10.1080/07038992.2024.2341762", "PubYear": 2024, "Volume": "50", "Issue": "1", "JournalId": 4152, "JournalTitle": "Canadian Journal of Remote Sensing", "ISSN": "0703-8992", "EISSN": "1712-7971", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Environmental and Life Sciences Graduate Program, Trent University, Peterborough, ON, K9J 7B8, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Biology, Trent University, Peterborough, ON, K9J 7B8, Canada"}], "References": [{"Title": "The Global Ecosystem Dynamics Investigation: High-resolution laser ranging of the Earth’s forests and topography", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "100002", "JournalTitle": "Science of Remote Sensing"}, {"Title": "Biomass estimation from simulated GEDI, ICESat-2 and NISAR across environmental gradients in Sonoma County, California", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "242", "Issue": "", "Page": "111779", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Mapping global forest canopy height through integration of GEDI and Landsat data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112165", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Fusing simulated GEDI, ICESat-2 and NISAR data for regional aboveground biomass mapping", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112234", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Modelling lidar-derived estimates of forest attributes over space and time: A review of approaches and future trends", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "260", "Issue": "", "Page": "112477", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "The impact of geolocation uncertainty on GEDI tropical forest canopy height estimation and change monitoring", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "100024", "JournalTitle": "Science of Remote Sensing"}, {"Title": "Mapping the presence and distribution of tree species in Canada's forested ecosystems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "282", "Issue": "", "Page": "113276", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Structural and species diversity explain aboveground carbon storage in forests across the United States: Evidence from GEDI and forest inventory data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "295", "Issue": "", "Page": "113703", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 115206542, "Title": "Aspect based sentiment analysis with instruction tuning and external knowledge enhanced dependency graph", "Abstract": "<p>Aspect-Based Sentiment Analysis (ABSA) is generally defined as a fine-grained task in Natural Language Processing (NLP). Recently, the integration of the Large Language Model (LLM) and Graph Convolutional Network (GCN) has been widely studied to excavate the underlying contextual information and support the sentiment polarity prediction. However, in existing research, the LLM is usually employed directly to generate the contextual feature representation without any specific instructions, which is not suitable for learning the domain language corpus. In addition, the existing works usually fuse the contextual feature and graph feature by GCN simply, and it ignores further specific processing to highlight the sentiment representations before the model’s final outputting. To tackle these two imperfections, this work proposes a novel ABSA model Instruction Tuning-based Graph Convolutional Network (ITGCN) to implement the subtask of predicting sentiment polarities (^\textrm{R2}) , which leverages the instructed LLM to generate the task-oriented contextual representation and the GCN to exploit the external affective knowledge-assisted syntactic features. In the proposed ITGCN, firstly, the inputting sentence is reconstructed with the designed task-specific instructions, which tell the LLM what is the target in the input. Secondly, this work’s dependency graph, before being processed by GCN, is weighted by the affective knowledge extracted from SenticNet. This kind of dependency graph is endowed with affective information, which is closer to the intention of the related study. Finally, to learn more structured knowledge, a bi-layer sentiment representation module is proposed and utilized to enhance the feature representation. To validate the effectiveness of the proposed ITGCN, extensive experiments have been conducted on five public and available datasets. The proposed ITGCN achieves competitive performance and outperforms the selected state-of-the-art baselines, obviously.</p>", "Keywords": "ABSA; Instruction tuning; GCN; Affective knowledge; Bi-layer sentiment representation", "DOI": "10.1007/s10489-024-05492-0", "PubYear": 2024, "Volume": "54", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei, China; Anhui Province Key Laboratory of Affective, Hefei University of Technology, Hefei, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei, China; Anhui Province Key Laboratory of Affective, Hefei University of Technology, Hefei, China"}, {"AuthorId": 3, "Name": "Fuji Ren", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 4, "Name": "Piao Shi", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei, China; Anhui Province Key Laboratory of Affective, Hefei University of Technology, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information Science & Technology, The University of Tokyo, Tokyo, Japan"}], "References": [{"Title": "Position-aware hierarchical transfer model for aspect-level sentiment classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "Is position important? deep multi-task learning for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "10", "Page": "3367", "JournalTitle": "Applied Intelligence"}, {"Title": "Aspect-gated graph convolutional networks for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "7", "Page": "4408", "JournalTitle": "Applied Intelligence"}, {"Title": "A unified position-aware convolutional neural network for aspect based sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "450", "Issue": "", "Page": "91", "JournalTitle": "Neurocomputing"}, {"Title": "Phrase dependency relational graph attention network for Aspect-based Sentiment Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; S<PERSON><PERSON> Shi", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107736", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Aspect-based Sentiment Analysis using Dependency Parsing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Aspect-based sentiment analysis with component focusing multi-head co-attention networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "489", "Issue": "", "Page": "9", "JournalTitle": "Neurocomputing"}, {"Title": "Graph convolutional network with multiple weight mechanisms for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "500", "Issue": "", "Page": "124", "JournalTitle": "Neurocomputing"}, {"Title": "Effective inter-aspect words modeling for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "4", "Page": "4366", "JournalTitle": "Applied Intelligence"}, {"Title": "Effective inter-aspect words modeling for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "4", "Page": "4366", "JournalTitle": "Applied Intelligence"}, {"Title": "Dual-channel and multi-granularity gated graph attention network for aspect-based sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "11", "Page": "13145", "JournalTitle": "Applied Intelligence"}, {"Title": "Multiple graph convolutional networks for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "10", "Page": "12985", "JournalTitle": "Applied Intelligence"}, {"Title": "Learn from structural scope: Improving aspect-level sentiment analysis with hybrid graph convolutional networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "518", "Issue": "", "Page": "373", "JournalTitle": "Neurocomputing"}, {"Title": "PTR: Prompt Tuning with Rules for Text Classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "182", "JournalTitle": "AI Open"}, {"Title": "Incorporating semantics, syntax and knowledge for aspect based sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "12", "Page": "16138", "JournalTitle": "Applied Intelligence"}, {"Title": "Improving aspect-based sentiment analysis with Knowledge-aware Dependency Graph Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "289", "JournalTitle": "Information Fusion"}, {"Title": "Aspect-based sentiment analysis via multitask learning for online reviews", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "264", "Issue": "", "Page": "110326", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Dependency-enhanced graph convolutional networks for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "19", "Page": "14195", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 115206577, "Title": "Optimal path planning with hybrid firefly algorithm and cuckoo search optimisation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2024.138565", "PubYear": 2024, "Volume": "27", "Issue": "3/4", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115206579, "Title": "A Reliable Optimal Hybrid Spectrum Sensing Algorithm with Hardware Impairments for Cognitive Radio Network", "Abstract": "Spectrum sensing algorithms exploit partial knowledge about the signal structure. A typical strategy for doing this is feature matching, i.e. having prior knowledge about some features of the signal; the detector makes a decision based on whether the feature is present in the input. Maximizing the detection probability for a provided false alarm rate is a hectic challenge for most of the spectral sensing methods. This paper presents a reliable, optimal hybrid spectrum sensing scheme (ROHSS) with hardware impairments for cognitive radio networks. The proposed two-stage ROHSS algorithm utilizes two detectors for low and high Signal Noise Ratio (SNR) bands. In the first stage, a double-threshold improved energy detector is used for the high SNR band and an anti eigen value-based detector is used for the low SNR band based on their merits and complexities. In the second stage, the Student-Teacher Neural Network (STNN) based detector utilizes the estimated energy and eigenvalue of the signal and gives a decision. The main objective of the proposed ROHSS algorithm is to sense the vacant frequency slots and allocate them to the Primary User’s (PUs) quickly in order to reduce the delay caused by the efficient operation of the fusion center. The proposed ROHSS algorithm is implemented in both MATLAB and Xilinx simulation tools and the performance is compared with the existing state-of-art algorithms.", "Keywords": "", "DOI": "10.14445/23939141/IJMCA-V11I1P101", "PubYear": 2024, "Volume": "11", "Issue": "1", "JournalId": 43310, "JournalTitle": "International Journal of Mobile Computing and Application", "ISSN": "", "EISSN": "2393-9141", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115206689, "Title": "Deep Neural Network Discipline and Consequence to be Achieved through Internet Implementation Dropdown", "Abstract": "", "Keywords": "", "DOI": "10.54216/FPA.160115", "PubYear": 2024, "Volume": "16", "Issue": "1", "JournalId": 91518, "JournalTitle": "Fusion: Practice and Applications", "ISSN": "2770-0070", "EISSN": "2692-4048", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business Information Technology Department, Business Informatics College, University of Information Technology and Communications, Baghdad, Iraq"}], "References": []}, {"ArticleId": 115206695, "Title": "3D colored object reconstruction from a single view image through diffusion", "Abstract": "In this paper we propose a novel 3D colored object reconstruction method from a single view image. Given a reference image, a conditional diffusion probabilistic model is built to reconstruct both a 3D point cloud shape and the corresponding color features at each point, and then images from arbitrary views can be synthesized using a volume rendering technique. The approach involves several sequential steps. First, the reference RGB image is encoded into separate shape and color latent variables. Then, a shape prediction module predicts reverse geometric noise based on the shape latent variable within the diffusion model. Next, a color prediction module predicts color features for each 3D point using information from the color latent variable. Finally, a volume rendering module transforms the generated colored point cloud into 2D image space, facilitating training based solely on a reference image. Experimental results demonstrate that the proposed method achieves competitive performance on colored 3D shape reconstruction and novel view image synthesis.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124225", "PubYear": 2024, "Volume": "252", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Information Science, Nanchang Hangkong University, Nanchang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Science, Nanchang Hangkong University, Nanchang, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Information Science, Nanchang Hangkong University, Nanchang, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Science, Nanchang Hangkong University, Nanchang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Informatics, Cardiff University, Wales, UK"}], "References": [{"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "NeRF", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "1", "Page": "99", "JournalTitle": "Communications of the ACM"}]}]