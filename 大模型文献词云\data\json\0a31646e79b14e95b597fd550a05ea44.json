[{"ArticleId": 93196047, "Title": "Approximate Generalized Matching: f-Matchings and f-Edge Covers", "Abstract": "<p>We present almost linear time approximation schemes for several generalized matching problems on nonbipartite graphs. Our results include \\(O_\\epsilon (m\\alpha (m, n))\\) -time algorithms for \\((1-\\epsilon )\\) -maximum weight f -matching and \\((1+\\epsilon )\\) -approximate minimum weight f -edge cover. As a byproduct, we also obtain direct algorithms for the exact cardinality versions of these problems running in \\(O(m\\alpha (m, n)\\sqrt{f(V)})\\) time, where f ( V ) is the sum of degree constraint on the entire vertex set. The technical contributions of this work include an efficient method for maintaining relaxed complementary slackness in generalized matching problems and approximation-preserving reductions between the f -matching and f -edge cover problems.</p>", "Keywords": "Matching; f-Factors; Edge covers", "DOI": "10.1007/s00453-022-00949-5", "PubYear": 2022, "Volume": "84", "Issue": "7", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "EECS Department, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "EECS Department, University of Michigan, Ann Arbor, USA"}], "References": []}, {"ArticleId": 93196052, "Title": "Data on point cloud scanning and ground radar of composite lining in jointly constructed tunnel", "Abstract": "The present dataset pertains to field records of construction quality of composite lining in a jointly constructed tunnel. The dataset includes the original mining surface profile data collected by the terrestrial laser scanning (TLS) and radar information on backfill quality outside the segmental lining which was obtained by the ground-penetration radar (GPR) detection. The point cloud data of the mining surface was further processed and compared with the design tunnel model to evaluate the level of over and under- excavation. The radargram provides details on the variation of the signal waveform by which the heterogeneity of backfill can be recognized. The dataset can be used to verify that the voids are prone to occur in the outside backfill of the composite lining. Furthermore, this dataset provides a method for detecting and preventing the defects of the composite lining and also facilitates the post-construction treatment. Additional foreseeable use of this dataset includes providing modeling material for researchers interested in knowing how voids in backfill influence the behavior of composite lining. As a supplement, this dataset supports the numerical analysis outlined in the article titled “Numerical evaluation of segmental tunnel lining with voids in outside backfill” [1] .", "Keywords": "Ground-penetrating radar;Heterogeneity;Jointly constructed tunnel;Over-under-excavation;Terrestrial laser scanning", "DOI": "10.1016/j.dib.2022.107993", "PubYear": 2022, "Volume": "41", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, School of Naval Architecture, Ocean, and Civil Engineering, Shanghai Jiao Tong University, Shanghai 200240, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, College of Engineering, Shantou University, Shantou, Guangdong 515063, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory for Digital Maintenance of Buildings and Infrastructure, Shanghai Jiao Tong University, Shanghai 200240, China."}], "References": []}, {"ArticleId": 93196057, "Title": "A survey of deep learning approaches to image restoration", "Abstract": "In this paper, we present an extensive review on deep learning methods for image restoration tasks. Deep learning techniques, led by convolutional neural networks, have received a great deal of attention in almost all areas of image processing, especially in image classification. However, image restoration is a fundamental and challenging topic and plays significant roles in image processing, understanding and representation. It typically addresses image deblurring, denoising, dehazing and super-resolution. There are substantial differences in the approaches and mechanisms in deep learning methods for image restoration. Discriminative learning based methods are able to deal with issues of learning a restoration mapping function effectively, while optimisation models based methods can further enhance the performance with certain learning constraints. In this paper, we offer a comparative study of deep learning techniques in image denoising, deblurring, dehazing, and super-resolution, and summarise the principles involved in these tasks from various supervised deep network architectures, residual or skip connection and receptive field to unsupervised autoencoder mechanisms. Image quality criteria are also reviewed and their roles in image restoration are assessed. Based on our analysis, we further present an efficient network for deblurring and a couple of multi-objective training functions for super-resolution restoration tasks. The proposed methods are compared extensively with the state-of-the-art methods with both quantitative and qualitative analyses. Finally, we point out potential challenges and directions for future research.", "Keywords": "Deep learning ; Convolutional neural networks ; Image restoration ; Image deblurring ; Image super-resolution", "DOI": "10.1016/j.neucom.2022.02.046", "PubYear": 2022, "Volume": "487", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, The University of Manchester, Manchester M13 9PL, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, The University of Manchester, Manchester M13 9PL, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, The University of Manchester, Manchester M13 9PL, UK;Corresponding author"}], "References": [{"Title": "A survey on semi-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "2", "Page": "373", "JournalTitle": "Machine Learning"}, {"Title": "Learning temporal coherence via self-supervision for GAN-based video generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Single-image deblurring with neural networks: A comparative survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "203", "Issue": "", "Page": "103134", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 93196058, "Title": "Gastrointestinal disorders in children with autism: Could artificial intelligence help?", "Abstract": "", "Keywords": "", "DOI": "10.35712/aig.v3.i1.1", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 78350, "JournalTitle": "Artificial Intelligence in Gastroenterology", "ISSN": "2644-3236", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Samara <PERSON>uli", "Affiliation": ""}], "References": [{"Title": "Review of deep learning: concepts, CNN architectures, challenges, applications, future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "53", "JournalTitle": "Journal of Big Data"}, {"Title": "Detection of Autism Spectrum Disorder in Children Using Machine Learning Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "5", "Page": "386", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 93196065, "Title": "Dataset on thermal comfort, perceived stress, and anxiety in university students under confinement due to COVID-19 in a hot and humid region of Mexico", "Abstract": "This dataset was compiled to estimate the levels of thermal comfort and mental health in a sample group of university students confined due to the COVID-19 pandemic. By the time research was carried out, these students of a hot and humid region of Mexico, had already spent 200 days on distance learning using online platforms. A total of 324 records were documented with a final sample of 316 valid participants. The total records were collected directly from the students through a web platform (Microsoft forms). This data set can be used to generate correlations between mental health, thermal comfort, and individual characteristics in the study population that will allow to identify the influence of the built environment and local climate on the levels of stress and anxiety that university students experienced under confinement. It can also be used to issue recommendations to improve the quality of built spaces and for the construction of adaptive models of thermal comfort considering mental health as a study variable .", "Keywords": "Anxiety;COVID-19;Humidity sensation;St<PERSON> perceived;Thermal sensation", "DOI": "10.1016/j.dib.2022.107996", "PubYear": 2022, "Volume": "41", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Institute for Renewable Energy, National Autonomous University of Mexico, Morelos, Mexico."}, {"AuthorId": 2, "Name": "L.A. <PERSON><PERSON>", "Affiliation": "Center for the Human and Integral Development of University Students, University of Veracruz, Coatzacoalcos, Mexico. ;Faculty of Nursing, University of Veracruz, Veracruz, Mexico."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Hernández-<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Nursing, University of Veracruz, Veracruz, Mexico."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Research in Energy and Sustainable Resources, University of Veracruz, Veracruz, Mexico."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>Valdés", "Affiliation": "Faculty of Medicine, University of Veracruz, Veracruz, Mexico."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Chemical Sciences, University of Veracruz, Veracruz, Mexico."}], "References": []}, {"ArticleId": 93196069, "Title": "The optimal integration strategy and authorization mechanism of AI chip with the architecture design capacity", "Abstract": "As manufacturers and subsystem suppliers purchase different authorizations of chip technology to meet customer requirements for chip functions, different authorization mechanisms have emerged. Moreover, manufacturers are considering whether to integrate subsystem suppliers to eliminate indirect costs. Therefore, this paper analytically explores the associated operational challenges of whether manufacturers (M) make backward integration and how to design subsystem architecture when technology providers (TP) are leaders. Moreover, we analytically derive the optimal backward integration strategy and the authorization mechanism. Our findings suggest that previous studies on chip supply chains have failed to explore integration and different authorization mechanisms. First, no matter what the authorization mechanism is, the integration cost and the revenue sharing ratio between the technology provider and the manufacturer affect the backward integration strategy. Then, considering the manufacturer’s architectural design, the manufacturer determines whether to conduct architectural design based on the architectural design capability and the architectural design ability cost conversion factor. Secondly, we obtain the Pareto optimality of TP and M in the integration process, the Pareto optimality of TP, M and S in the architecture design process with non-integration, the Pareto optimality of TP and M in the architecture design process with integration and the Pareto optimality of M and S in the architecture design process. Finally, we find the manufacturer&#x27;s architectural design can promote demand for M and TP, and for M, it causes demand to trend higher in the short term, but not for TP.", "Keywords": "Technology-intensive ; Authorization mechanism ; Integration ; Design capability", "DOI": "10.1016/j.cie.2022.108027", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Zhitang Li", "Affiliation": "School of Business Administration, Northeastern University, Shenyang 110169, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Northeastern University, Shenyang 110169, China;Corresponding author at: School of Business Administration, Northeastern University, 500 Wisdom Street, Shenyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Northeastern University, Shenyang 110169, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Northeastern University, Shenyang 110169, China"}], "References": [{"Title": "Cooperative models for technology supply chain in online social community", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "106460", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 93196225, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0924-4247(22)00058-9", "PubYear": 2022, "Volume": "335", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [], "References": []}, {"ArticleId": 93196229, "Title": "Es geht um <PERSON>ht", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2022.02.03", "PubYear": 2022, "Volume": "", "Issue": "2", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93196329, "Title": "Nonlinear forced vibration and stability analysis of nonlinear systems combining the IHB method and the AFT method", "Abstract": "The incremental harmonic balance (IHB) method is a very powerful tool for analyzing nonlinear forced vibrations in structures subjected to periodic forces. In this paper, a methodology is proposed to interpret the stability of the calculated solutions and the nonlinear vibrations of nonlinear systems by combining the IHB method and the Alternating Frequency/Time (AFT) method. The main purpose of combining the IHB method and the AFT method is to analyze the nonlinear vibration of systems with complex nonlinearities. For the mass matrix, damping matrix, and linear stiffness matrix, the <PERSON>rkin procedure is performed according to the classical IHB method. The nonlinear force matrix and its derivative matrix are calculated in the time domain and transformed into the frequency domain by Fourier transform. By multiplying these matrices with a transformation matrix, they are transformed into matrices that can be used in the IHB method. Three example problems are proposed to prove the effectiveness of the suggested method and the results compared with the results calculated using numerical integration (NI) and the results presented in previous literature. The results are very close. The proposed method can analyze nonlinear vibrations of systems with complex nonlinearities as well as geometric nonlinearities.", "Keywords": "Incremental harmonic balance method ; Alternating frequency/time ; Nonlinear forced vibration", "DOI": "10.1016/j.compstruc.2022.106771", "PubYear": 2022, "Volume": "264", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Mechanical Engineering, Academy of Sciences, Pyongyang 999093, Democratic People's Republic of Korea"}, {"AuthorId": 2, "Name": "Kwangchol Ri", "Affiliation": "Department of Light Industry Machinery Engineering, Pyongyang University of Mechanical Engineering, Pyongyang 999093, Democratic People's Republic of Korea;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Forest Science, Kim Il Sung University, Pyongyang 999093, Democratic People's Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Thermal Engineering, <PERSON> University of Technology, Pyongyang 999093, Democratic People's Republic of Korea"}, {"AuthorId": 5, "Name": "Poknam Han", "Affiliation": "College of Ship and Ocean Technology, <PERSON> University of technology, Pyongyang 999093, Democratic People's Republic of Korea"}], "References": []}, {"ArticleId": 93196336, "Title": "Characterization of the Autoencoder Radiation Anomaly Detection (ARAD) model", "Abstract": "In this work we demonstrate an in-depth analysis and characterization of the Autoencoder Radiation Anomaly Detection (ARAD) algorithm. ARAD is a deep convolutional autoencoder designed to detect anomalous radioactive signatures in gamma-ray spectra collected by NaI(Tl) detectors. This model works by learning a dimensionally constrained representation of background gamma-ray spectra called the latent space. The latent space cannot fully describe anomalous components in new spectra, resulting in a decrease in spectral reconstruction accuracy that triggers an alarm. This paper demonstrates the model’s performance on a set of data collected outside of the High Flux Isotope Reactor and Radiochemical Engineering Development Center facilities at Oak Ridge National Laboratory. We also perform an evaluation of the model’s detection performance using a set of publicly available synthetic data representing a radiation detector moving throughout an urban city street. We demonstrate the algorithm’s ability to detect sources in locations with highly dynamic background count rates resulting from variations in naturally occurring radioactive materials and precipitation-induced radon washout, a challenge for many traditional radiation detection algorithms. We compare these results against those from another unsupervised radiation anomaly detection algorithm based on principal component analysis. ARAD achieved excellent performance on both datasets and proves the viability and efficacy of autoencoders for radiation anomaly detection.", "Keywords": "Radiation detection ; Anomaly detection ; Deep learning ; Machine learning ; Gamma-ray spectroscopy", "DOI": "10.1016/j.engappai.2022.104761", "PubYear": 2022, "Volume": "111", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN 37830, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN 37830, United States of America"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN 37830, United States of America"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN 37830, United States of America"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN 37830, United States of America"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN 37830, United States of America"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Cadre5, LLC, Knoxville, TN 37932, United States of America"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, Knoxville, TN 37996, United States of America"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, Knoxville, TN 37996, United States of America"}], "References": []}, {"ArticleId": 93196393, "Title": "Low illumination Image Enhancement based on Improved Retinex Algorithm", "Abstract": "<p lang=\"zh\"><p><p>Aiming at the problems of insufficient illumination and low contrast of low illumination image, an improved Retinex low illumination image enhancement algorithm is proposed. Firstly, the brightness component V of the original image is extracted in HSV color space, and its enhancement by Single-Scale Retinex (SSR) is used to obtain the reflection component. For the edge problem caused by the estimation of illumination component, the Gaussian weighted bilateral filter is used as the filter function to maintain the edge information. Then, the saturation component S is adaptively stretched to improve the color saturation. However, different low illumination images have different contrast, and some images have insufficient contrast enhancement, so a global adaptive algorithm is introduced to modify the contrast and obtain the final image. According to the logarithmic characteristics of human vision, it can adaptively enhance the contrast of different images without over enhancement. Experimental results show that the proposed algorithm can effectively improve the visual quality of the image, the contrast is improved significantly and image edge details are protected, and objective evaluations such as average gradient, information entropy and peak signal-to-noise ratio have been improved.</p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022023301012", "PubYear": 2022, "Volume": "33", "Issue": "1", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-<PERSON> Wang", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93196413, "Title": "Research of Art Point of Interest Recommendation Algorithm Based on Modified VGG-16 Network", "Abstract": "<p lang=\"zh\"><p><p>Traditional point of interest (POI) recommendation algorithms ignore the semantic context of comment information. Integrating convolutional neural networks into recommendation systems has become one of the hotspots in art POI recommendation research area. To solve the above problems, this paper proposes a new art POI recommendation model based on improved VGG-16. Based on the original VGG-16, the improved VGG-16 method optimizes the fully connection layer and uses transfer learning to share the weight parameters of each layer in VGG-16 pre-training model for subsequent training. The new model fuses the review information and user check-in information to improve the performance of POI recommendation. Experiments on real check-in data sets show that the proposed model has better recommendation performance than other advanced points of interest recommendation methods.</p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022023301008", "PubYear": 2022, "Volume": "33", "Issue": "1", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "劉怡 劉怡", "Affiliation": ""}], "References": []}, {"ArticleId": 93196423, "Title": "A model‐based approach to user preference discovery in multi‐criteria recommender system using genetic programming", "Abstract": "<p>Multi-criteria recommender systems (MCRSs) provide suggestions to users based on their preferences to various criteria. Incorporation of criteria ratings into recommendation framework can provide quality recommendations to users because these ratings can elicit users' preferences efficiently. However, elicitation of user's overall preference based on criteria ratings is a key issue in MCRS. Even though several aggregation methods for the elicitation of users' overall preference have been investigated in the literature, no method has been shown the superiority under all circumstances. Therefore, we propose a model based approach to user preference discovery in multi-criteria RS using genetic programming (GP). In this work, we suggest three-stage process to generate recommendations to users. First, we learn user preference transformation function to aggregate criteria ratings by using GP, and then we utilize the preference function, so derived, for computing similarities in MCRS. Finally, items are recommended to users. Experimental results on Yahoo! Movies dataset show the superiority of our proposed approach in comparison to other aggregation approaches.</p>", "Keywords": "collaborative filtering;genetic programming;multi-criteria ratings;preference ratings;recommender system", "DOI": "10.1002/cpe.6899", "PubYear": 2022, "Volume": "34", "Issue": "11", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, The LNM Institute of Information Technology, Jaipur, Rajasthan, 302031 India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Banaras Hindu University, Varanasi, Uttar Pradesh, 221005 India"}], "References": [{"Title": "A novel deep multi-criteria collaborative filtering model for recommendation system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104811", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Credibility score based multi-criteria recommender system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105756", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-criteria collaborative filtering recommender by fusing deep neural network and matrix factorization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "A fuzzy ubiquitous traveler clustering and hotel recommendation system by differentiating travelers’ decision-making behaviors", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106585", "JournalTitle": "Applied Soft Computing"}, {"Title": "A metric for Filter Bubble measurement in recommender algorithms considering the news domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106771", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 93196452, "Title": "Personalized recommendation: From clothing to academic", "Abstract": "<p>Information retrieval is useful in all aspects of life, ranging from clothing shopping to education and academic pursuits. Many systems optimize models with pairwise ranking techniques such as Bayesian Personalized Ranking (BPR) for personalized information retrieval. A Bayesian personalized ranking system can assist specific shoppers, students, and researchers based on their interaction, which has illustrated an enormous capability for improvement by generating intelligent recommendations, such as clothing, books, and other related information. However, for such users, finding the desired clothing and books online is complex and is influenced by various factors (e.g., visual appearance and time). As such, traditional personalized recommendation methods that model only user-product interaction data would deliver unsatisfactory recommendation results. In this paper, we propose combining visual, temporal, and sequential information for personalized recommendations. Technically speaking, our main contributions include: (1) We incorporate the image features of clothing and books into personalized ranking to model users’ preferences. (2) We design a new time model for personalized recommender systems. The visual features are then injected into the time model to capture the temporal dynamics of visual preferences. (3) To this end, we present a Time Hierarchical Embedding (T-Sherlock) approach, which can incorporate sequential and temporal information simultaneously to model users’ preferences for different categories of products. To reduce the impact of adversarial noise, we train a T-Sherlock objective function using minimax adversarial training (AT-Sherlock). Experiments on real-world datasets demonstrated the efficacy of our methods in comparison to baselines.</p>", "Keywords": "Personalized recommendation; Category hierarchy; Temporal dynamics; Adversarial training; Academic services", "DOI": "10.1007/s11042-022-12259-7", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, Hangzhou, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, Hangzhou, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, Hangzhou, People’s Republic of China"}, {"AuthorId": 4, "Name": "Shufeng Gong", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, Hangzhou, People’s Republic of China"}], "References": []}, {"ArticleId": 93196468, "Title": "Emotion recognition from spontaneous speech using emotional vowel-like regions", "Abstract": "<p>Spontaneous speech varies in terms of characteristics such as emotion, volume, and pitch. Emotion, itself, is not uniformly distributed across an utterance. The extraction of relevant portions from an utterance that contain meaningful information in terms of emotion is always challenging. The vowel like regions (VLRs) are known to contain emotion-specific information. However, for spontaneous speech, all the VLRs in an utterance do not contain emotion. This paper proposes a method for extracting the emotional VLRs from a set of vowels in an utterance based on the fundamental frequency of a VLR. Further, the recently proposed epoch synchronous single frequency cepstral coefficients (SFCCs) features are combined with the epoch-based features producing 1.33% better result than state-of-the-art technique. In general, the accuracy value reduces for long utterances because all the VLRs in a long utterance are not consistent with the ground truth label. However, the proposed approach produced an improvement in accuracy by 8.22% for the long utterances when emotional VLRs were used in place of all VLRs.</p>", "Keywords": "Emotional vowel; Single frequency cepstral coefficients (SFCC); Fundamental-frequency; Speech emotion recognition (SER); Long-utterance; Vowel-like-regions(VLRs)", "DOI": "10.1007/s11042-022-12453-7", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Patna, India; Vellore Institute of Technology, Bhopal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Patna, India"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Patna, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Patna, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Patna, India"}], "References": [{"Title": "Pitch-synchronous single frequency filtering spectrogram for speech emotion recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "31-32", "Page": "23347", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93196512, "Title": "Experimental based comparative study on open-source network intrusion detection system", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJITST.2022.10045469", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 27845, "JournalTitle": "International Journal of Internet Technology and Secured Transactions", "ISSN": "1748-569X", "EISSN": "1748-5703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93196513, "Title": "Topic adaptive sentiment classification based community detection for social influential gauging in online social networks", "Abstract": "<p>Online Social Networks (OSNs) such as Twitter, Facebook, Instagram, and WhatsApp are turned as a place for many of people in recent years to spend much of their time, due to their huge network structure and massive amounts of user-generated data in it. Those data’s are widely used in various real-world applications such as online marketing, epidemiology, digital marketing, online product or service promotion, and online recommendation systems. Presently, the twitter has grown to become a mainstream medium for the dissemination of messages, which creates necessitated intensive research challenges in the field of social influential gauging, Influence Maximization Problems, alongside an information diffusion. First, to address the social influential gauging a novel Topic Adaptive Sentiment Classification based Community Detection (TASCbCD) algorithm is proposed to detect communities in twitter network based on the results of topic based sentiment classification using robust topic features. In the topic modelling, the initial topics of each extracted data and the robust topic features were used to classify using a multi-class support vector machine. The WordNet and SentiWordNet are benchmark data sets that are used for supporting those classification to achieve the desired results. The resultant communities give a better visualization of identifying the overlapping communities that helps to gauge the topic based social influential user in OSNs. However, from the experimental result, it is observed that the proposed algorithm achieves better results in RandIndex and Scaled Density metrics than state-of-the-art methods for communities detection.</p>", "Keywords": "Topic modeling; Sentiment analysis; Community detection; Influential spreader identification; Online social networks", "DOI": "10.1007/s11042-021-11855-3", "PubYear": 2023, "Volume": "82", "Issue": "6", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Puducherry, Karaikal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, College of Engineering Guindy, Anna University, Chennai, India"}], "References": [{"Title": "A multi-objective ant colony optimization algorithm for community detection in complex networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "5", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A decomposition-based ant colony optimization algorithm for the multi-objective community detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "173", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Eventfully Safapp: hybrid approach to event detection for social media mining", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "87", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Community evaluation in Facebook groups", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "45-46", "Page": "33603", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multimedia recommendation using Word2Vec-based social relationship mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "26-27", "Page": "34499", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Exchange-based diffusion in Hb-Graphs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "15", "Page": "22429", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93196517, "Title": "Epidemiological Mucormycosis treatment and diagnosis challenges using the adaptive properties of computer vision techniques based approach: a review", "Abstract": "<p>As everyone knows that in today's time Artificial Intelligence, Machine Learning and Deep Learning are being used extensively and generally researchers are thinking of using them everywhere. At the same time, we are also seeing that the second wave of corona has wreaked havoc in India. More than 4 lakh cases are coming in 24 h. In the meantime, news came that a new deadly fungus has come, which doctors have named Mucormycosis (Black fungus). This fungus also spread rapidly in many states, due to which states have declared this disease as an epidemic. It has become very important to find a cure for this life-threatening fungus by taking the help of our today's devices and technology such as artificial intelligence, data learning. It was found that the CT-Scan has much more adequate information and delivers greater evaluation validity than the chest X-Ray. After that the steps of Image processing such as pre-processing, segmentation, all these were surveyed in which it was found that accuracy score for the deep features retrieved from the ResNet50 model and SVM classifier using the Linear kernel function was 94.7%, which was the highest of all the findings. Also studied about Deep Belief Network (DBN) that how easy it can be to diagnose a life-threatening infection like fungus. Then a survey explained how computer vision helped in the corona era, in the same way it would help in epidemics like Mucormycosis.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022.</p>", "Keywords": "Artificial intelligence;Black fungus;Computer vision;Deep learning;Mucormycosis", "DOI": "10.1007/s11042-022-12450-w", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, GLA University, Mathura, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, GLA University, Mathura, 281406 India."}], "References": [{"Title": "A deep learning based medical image segmentation technique in Internet-of-Medical-Things domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "135", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Design of Low Power with Expanded Noise Margin Subthreshold 12T SRAM Cell for Ultra-Low Power Devices", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "6", "Page": "2150106", "JournalTitle": "Journal of Circuits, Systems and Computers"}, {"Title": "Deep learning approaches for COVID-19 detection based on chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114054", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Automatic detection of coronavirus disease (COVID-19) using X-ray images and deep convolutional neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "3", "Page": "1207", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "Novel coronavirus (COVID-19) diagnosis using computer vision and artificial intelligence techniques: a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> Ban<PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "13", "Page": "19931", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93196544, "Title": "Stocks of year 2020: prediction of high variations in stock prices using LSTM", "Abstract": "<p>Stock Market movement is highly volatile, complex, and non-linear. Several researchers have proposed innovative approaches to predict stock price movement using traditional data analytics, machine learning, or deep learning. Data scientists have proved that if effective mathematical models are deployed, stock prices can be predicted with very high accuracy. Deep learning is the most popular technique used for stock price prediction due to its effective results in time-series based and non-linear patterns. In the Year 2020, stock prices variations are too high to be analyzed by traditional approaches. Very few research works have been carried out to predict high variations in stock prices during this time. The main motive of this research is to investigate whether deep learning can predict so high variations in stock prices in the Year 2020 and build proposed neural network model. In this paper, Long Short-Term Memory (LSTM) is used with adam optimizer and sigmoid activation function to train and test the model. Various stock indexes data are extracted using Yahoo Finance API. Window size of 60 days is used as stock prices are dependent on the previous day’s prices. Experiment analysis has proved that LSTM using our layers set up was able to predict stock prices with adequate accuracy. Mean Absolute Percentage Error (MAPE) values are better than traditional data analytics techniques. The values of MAPE score calculated using our proposed approach are 3.89, 1.21, 3.01, 1.19, 2.03, and 0.86 for NSE, BSE, NASDAQ, NYSE, Dow Jones, and Nikkei 225 respectively for duration Jan 2010 to March 2020.</p>", "Keywords": "Stock Price prediction; Deep learning; LSTM; RNN; MAPE", "DOI": "10.1007/s11042-022-12390-5", "PubYear": 2023, "Volume": "82", "Issue": "7", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Petroleum and Energy Studies, Dehradun, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Thapar Institute of Engg. & Tech, Patiala, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Punjabi University, Patiala, India"}], "References": [{"Title": "Stock price prediction using deep learning and frequency decomposition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114332", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A CNN-BiLSTM-AM method for stock price prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "4741", "JournalTitle": "Neural Computing and Applications"}, {"Title": "StockPred: a framework for stock Price prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "12", "Page": "17923", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A graph-based CNN-LSTM stock price prediction algorithm with leading indicators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "1751", "JournalTitle": "Multimedia Systems"}, {"Title": "Applications of deep learning in stock market prediction: Recent progress", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115537", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 93196584, "Title": "AuSR1: Authentication and self-recovery using a new image inpainting technique with LSB shifting in fragile image watermarking", "Abstract": "With the rapid development of multimedia technology, editing and manipulating digital images have become more accessible than ever. This paper proposed color image authentication based on blind fragile image watermarking for tamper detection and self-recovery named AuSR1. The AuSR1 divides each channel of the cover image into non-overlapping blocks with the size of 2 × 2 pixels. The authentication data is embedded into the original block location, while the recovery data is embedded into the distant location from the original location based on the block mapping algorithm. The watermark data is then embedded into the 2 LSB to achieve high quality of the recovered image under tampering attacks. In addition, the permutation algorithm is applied to ensure the security of the watermark data. The AuSR1 utilizes a three-layer authentication algorithm to achieve a high detection rate.   The experimental results show that the scheme produced a PSNR value of 45.57 dB and an SSIM value of 0.9972 of the watermarked images. Furthermore, the AuSR1 detected the tampered area of the images with a high precision value of 0.9943. In addition, the recovered image achieved a PSNR value of 27.64 dB and an SSIM value of 0.9339 on a 50% tampering rate.", "Keywords": "Blind fragile watermarking ; Self-embedding ; Image authentication ; Self-recovery ; Image inpainting", "DOI": "10.1016/j.jksuci.2022.02.009", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatic, Faculty of Computer Science, Universitas Amikom, Yogyakarta, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Graphic and Multimedia, Faculty of Computing, College of Computing and Applied Sciences, Universiti Malaysia Pahang, Kuantan, Malaysia;Corresponding author at: Faculty of Computing, College of Computing and Applied Sciences, Universiti Malaysia Pahang, Pekan, 26600 Kuantan, Malaysia"}], "References": [{"Title": "A novel triple recovery information embedding approach for self-embedded digital image watermarking", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "Page": "31239", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An integer wavelet transform and pixel value differencing based feature specific hybrid technique for 2D ECG steganography with high payload capacity", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "6", "Page": "8505", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A hybrid-Sudoku based fragile watermarking scheme for image tampering detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "12881", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Deep learning for real-time semantic segmentation: Application in ultrasound imaging", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "27", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "DWT based color image watermarking using maximum entropy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "10", "Page": "15487", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel pixel-wise authentication-based self-embedding fragile watermarking method", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "3", "Page": "531", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 93196644, "Title": "Integrating reviews and ratings into graph neural networks for rating prediction", "Abstract": "<p>In the area of recommendation systems, one of the fundamental tasks is rating prediction. Most existing neural network methods independently extract user’s and item’s review features utilizing a parallel convolutional neural network(CNN) and use them as the representation of users and items to predict rating scores. There are two main drawbacks of these methods: (1) They typically only leverage user or item reviews but ignore the latent information provided by user-item interactions. (2) The historical rating scores are not integrated into the representation of users and items, they are simply used as labels to train models. Thus the rating information is not adequately utilized, leading to the prediction performance of these methods is not superior. To remedy these drawbacks mentioned above, in this paper, we build a unified graph convolutional network(GCN) to capture the interaction information between user and item, also obtain additional information provided by reviews and rating scores. As both reviews and ratings carry interactive messages among users and items, they would magnify the learning performance of user-item features. Specifically, we first construct a multi-attributed bipartite graph(MA-bipartite graph) to represent users, items, and their interactions through reviews and ratings. Then we divide the MA-bipartite graph into two sub-graphs according to the attributes of the edge types which represent the user-item interaction in review domain and item domain respectively. Next, an attributed GCN model is explicitly designed to learn latent features of users and items by incorporating review embeddings and rating score weights. Finally, the attention mechanism is carried to fuse user and item features dynamically to conduct the rating prediction. We conduct our experiments on two real-world datasets. The results demonstrate that the proposed model achieved the state-of-the-art performance, which increases the prediction accuracy by more than 3%, compared with baseline methods.</p>", "Keywords": "Reviews; Recommendation; Graph convolution network; Rating prediction", "DOI": "10.1007/s12652-021-03626-7", "PubYear": 2023, "Volume": "14", "Issue": "7", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Symbol Computation and Knowledge Engineering of Ministry of Education, Changchun, China; College of Computer Science and Technology, Jilin University, Changchun, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Symbol Computation and Knowledge Engineering of Ministry of Education, Changchun, China; College of Computer Science and Technology, Jilin University, Changchun, China"}, {"AuthorId": 3, "Name": "Zhenkun Shi", "Affiliation": "Tianjin Institute of Industrial Biotechnology, Chinese Academy of Sciences, Changchun, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Amrit Campus, Tribhuvan University, Kathmandu, Nepal"}], "References": []}, {"ArticleId": 93196681, "Title": "Computational completeness of spiking neural P systems with inhibitory rules for generating string languages", "Abstract": "Spiking neural P systems with inhibitory rules (in short, SNP-IR systems) are a distributed parallel computing model, abstracted by the spiking and inhibitory mechanisms of biological neurons. Computational completeness of SNP-IR systems as number generating/accepting and function computing devices has been studied recently. However, computational completeness of SNP-IR systems as language generating devices still has not been investigated. We discuss the relationship of languages generated by SNP-IR systems with regular languages. Moreover, we prove that SNP-IR systems can generate recursively enumerable languages by means of a projection of inverse-morphic image.", "Keywords": "Membrane computing ; Spiking neural P systems with inhibitory rules ; String language ; Computational completeness", "DOI": "10.1016/j.tcs.2022.02.025", "PubYear": 2022, "Volume": "920", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, 610039, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, 610039, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Electronic Information, Xihua University, Chengdu, 610039, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, 610039, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, 610039, China"}], "References": [{"Title": "Spiking neural P systems with inhibitory rules", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "105064", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-focus image fusion based on dynamic threshold neural P systems and surfacelet transform", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105794", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Computational power of dynamic threshold neural P systems for generating string languages", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "851", "Issue": "", "Page": "77", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Spiking neural P systems with autapses", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "570", "Issue": "", "Page": "383", "JournalTitle": "Information Sciences"}, {"Title": "Multi-focus image fusion approach based on CNP systems in NSCT domain", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "210", "Issue": "", "Page": "103228", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "LSTM-SNP: A long short-term memory model inspired from spiking neural P systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107656", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 93196682, "Title": "“Nah, it’s just annoying!” A Deep Dive into User Perceptions of Two-Factor Authentication", "Abstract": "<p>Two-factor authentication (2FA) is a recommended or imposed authentication mechanism for valuable online assets. However, 2FA mechanisms usually exhibit user experience issues that create user friction and even lead to poor acceptance, hampering the wider spread of 2FA. In this paper, we investigate user perceptions of 2FA through in-depth interviews with 42 participants, revealing key requirements that are not well met today despite recently emerged 2FA solutions. First, we investigate past experiences with authentication mechanisms emphasizing problems and aspects that hamper good user experience. Second, we investigate the different authentication factors more closely. Our results reveal particularly interesting preferences regarding the authentication factor ”ownership” in terms of properties, physical realizations, and interaction. These findings suggest a path towards 2FA mechanisms with considerably better user experience, promising to improve the acceptance and hence, the proliferation of 2FA for the benefit of security in the digital world.</p>", "Keywords": "Two-Factor Authentication; Human Factors; Usability; User Experience", "DOI": "10.1145/3503514", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Glasgow, Scotland, Technical University of Darmstadt, Germany, Keio University, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Keio University, Kohoku-ku Yokohama, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Keio University, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technical University of Darmstadt, Darmstadt, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Technical University of Darmstadt, Darmstadt, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Technical University of Darmstadt, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Technical University of Munich, Germany"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Keio University, Japan"}], "References": [{"Title": "The password is dead, long live the password – A laboratory study on user perceptions of authentication schemes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "26", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Until you have something to lose! Loss aversion and two-factor authentication adoption", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Applied Computing and Informatics"}]}, {"ArticleId": 93196684, "Title": "Renaissance: A self-stabilizing distributed SDN control plane using in-band communications", "Abstract": "By introducing programmability, automated verification, and innovative debugging tools, Software-Defined Networks (SDNs) are poised to meet the increasingly stringent dependability requirements of today&#x27;s communication networks. However, the design of fault-tolerant SDNs remains an open challenge. This paper considers the design of dependable SDNs through the lenses of self-stabilization—a very strong notion of fault-tolerance. In particular, we develop algorithms for an in-band and distributed control plane for SDNs, called Renaissance, which tolerate a wide range of failures. Our self-stabilizing algorithms ensure that after the occurrence of arbitrary failures, (i) every non-faulty SDN controller can reach any switch (or another controller) within a bounded communication delay (in the presence of a bounded number of failures) and (ii) every switch is managed by a controller. We evaluate Renaissance through a rigorous worst-case analysis as well as a prototype implementation (based on OVS and Floodlight, and Mininet).", "Keywords": "SDN ; Distributed control plane ; Self-stabilization", "DOI": "10.1016/j.jcss.2022.02.001", "PubYear": 2022, "Volume": "127", "Issue": "", "JournalId": 4857, "JournalTitle": "Journal of Computer and System Sciences", "ISSN": "0022-0000", "EISSN": "1090-2724", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science, Université catholique de Louvain, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Chalmers University of Technology, Sweden;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Akamai, Israel"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Chalmers University of Technology, Sweden"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science, University of Vienna, Austria;TU Berlin, Germany"}], "References": []}, {"ArticleId": 93196738, "Title": "Customizable Tabular Access to Web Data Records for Convenient Low-vision Screen Magnifier Interaction", "Abstract": "<p> To interact with webpages, people with low vision typically rely on screen magnifier assistive technology that enlarges screen content and also enables them to pan the content to view the different portions of a webpage. This back-and-forth panning between different webpage portions makes it especially inconvenient and arduous for screen magnifier users to interact with web data records (e.g., list of flights, products, job advertisements), as this interaction typically involves making frequent comparisons between the data records based on their attributes, e.g., comparing available flights in a travel website based on their prices, durations, etc. To address this issue, we present TableView+, an enhanced version of our previous TableView prototype – a browser extension that leverages a state-of-the-art data extraction method to automatically identify and extract information in web data records, and subsequently presents the information to a screen magnifier user in a compactly arranged data table to facilitate easier comparisons between records. TableView+ introduces new features aimed mostly at addressing the critical shortcomings of TableView, most notably the absence of interface customization options. In this regard, TableView+ enables low-vision users to customize the appearance of the data table based on their individual needs and eye conditions. TableView+ also saves these customizations so as to automatically apply them to the best extent possible the next time the users interact with the data records on either the same or other similar websites. A user study with 25 low-vision participants showed that with TableView+, the panning time further decreased by 8.5\\% on unfamiliar websites and by 8.02\\% on a familiar website than with TableView when compared to a screen magnifier. </p>", "Keywords": "web accessibility; usability; screen magnifier; low vision; visually impaired", "DOI": "10.1145/3517044", "PubYear": 2022, "Volume": "15", "Issue": "2", "JournalId": 17450, "JournalTitle": "ACM Transactions on Accessible Computing", "ISSN": "1936-7228", "EISSN": "1936-7236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Stony Brook University, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Old Dominion University, USA"}], "References": [{"Title": "Bringing Things Closer: Enhancing Low-Vision Interaction Experience with Office Productivity Applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON> <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "EICS", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 93196747, "Title": "Parameter continuity in time-varying Gauss–<PERSON><PERSON> models for learning from small training data sets", "Abstract": "The Linear time-invariant dynamic models are widely adopted in the industry. In the machine learning domain, such models are known as time-invariant continuous-state hidden Gauss–<PERSON><PERSON> models. Their super-class, the linear time-varying dynamic models, have relatively sparse applications as predictive models and classifiers of time series. This is typically due to the model complexity and the need for a significantly larger training set than time-invariant models. Without a large training set, a better modeling performance is counteracted by a less robust model. In this paper, we propose the continuity preference of the time-varying parameters of the model, which significantly reduces the required amount of training data while maintaining the modeling performance. We also derive a simple modification of the Expectation–Maximization algorithm incorporating continuity in parameters. The modified algorithm shows robust learning performance. The model performance is demonstrated by experiments on real 6-axis robotic manipulators in a laboratory, the Skoda Auto car producer body shop, and also on a public benchmark data set.", "Keywords": "", "DOI": "10.1016/j.ins.2022.02.037", "PubYear": 2022, "Volume": "595", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Control Engineering, Faculty of Electrical Engineering at CTU in Prague, Czech Republic;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Czech Institute of Informatics, Robotics and Cybernetics at CTU in Prague, Czech Republic"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Czech Institute of Informatics, Robotics and Cybernetics at CTU in Prague, Czech Republic"}], "References": []}, {"ArticleId": 93196760, "Title": "An IoT-Based Intelligent Geological Disaster Application Using Open-Source Software Framework", "Abstract": "<p>With the development of Internet of Things (IoT) and machine learning technologies, mobile geographic information systems (GISs) have developed rapidly. Moreover, mobile GIS applications serve all walks of life including remote sensing, geological disaster management, and decision support systems. This article discusses the main development methods of the Android system for mobile GIS, analyzes the characteristics of different development methods, and mainly introduces the technology of developing mobile GIS based on free and open-source software (FOSS) framework. Finally, we present a data collection framework for an Android application development, based on QGIS, QFiled, GeoServer, PostgreSQL, and GeoPackage. The mobile GIS can collect important data. Furthermore, the data collection framework uses a data aggregation technique to filter and remove redundant data. Machine learning approaches are integrated in the GIS to make it intelligent. The application, in the Xishan mining area of Taiyuan, proves that the proposed framework can complete the collection and storage of geological disaster data, which has certain practical significance. Our experimental results show that the data aggregation method is approximately 42.3–44.09 percent (training times) more efficient than the no aggregation approach. Moreover, the attention network may produce an additional overhead in the prediction process, depending on the model. This overhead is observed between 0.58% and 2.83% for the LSTM model.</p>", "Keywords": "", "DOI": "10.1155/2022/9285258", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mining Engineering, Taiyuan University of Technology, Taiyuan 030024, Shanxi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mining Engineering, Taiyuan University of Technology, Taiyuan 030024, Shanxi, China"}], "References": [{"Title": "Exploiting dynamic spatio-temporal correlations for citywide traffic flow prediction using attention based neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "852", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 93196763, "Title": "Revisiting signal analysis in the big data era", "Abstract": "A fast and accurate time–frequency analysis is challenging for many applications, especially in the current big data era. A recent work introduces a fast continuous wavelet transform that effectively boosts the analysis speed without sacrificing the resolution of the result.", "Keywords": "Computational science;Electroencephalography – EEG;Electrophysiology;Software", "DOI": "10.1038/s43588-022-00210-7", "PubYear": 2022, "Volume": "2", "Issue": "2", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Biomedical Center for Advanced ESR Technology, Cornell University, Ithaca, USA;Department of Chemistry and Chemical Biology, Ithaca, USA"}], "References": [{"Title": "The fast continuous wavelet transformation (fCWT) for real-time, high-quality, noise-resistant time–frequency analysis", "Authors": "Lukas P. A. Arts; Egon. L<PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "47", "JournalTitle": "Nature Computational Science"}]}, {"ArticleId": 93196769, "Title": "Evaluating Engagement in Technology-Supported Social Interaction by People Living with Dementia in Residential Care", "Abstract": "Technologies can support the well-being of people living with dementia in residential care by fostering meaningful interactions through multimodal and playful features. This article presents the evaluation of A Better Visit, an app that aims at engaging residents and visitors in shared activities. We define engagement in terms of dyadic interactions with the interface of the device, and interactions within the social context of the care home. We generated two Motivational Models that describe different perspectives of how people engaged with the system: the first was built with the design team, and the second was built using the first Motivational Model as a sensitising concept for the analysis of data from four care homes that used the app for periods of three months. Our findings show that the facilitation of activities plays a relevant role for engaging in social interaction. We discuss how technologies can contribute to engaging people living with dementia in social interaction: firstly, facilitation can be understood as a concept that is supported by people and technology. Secondly, we discuss the influence of the social context of the setting and the understandings of the individual in how people assess the engagement of the residents in social interaction.", "Keywords": "evaluation; engagement; social interaction; residential care; dementia; facilitation; motivational models", "DOI": "10.1145/3514497", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Future Self Living Lab, Centre for Design Innovation, Swinburne University of Technology, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Future Self Living Lab, Centre for Design Innovation, Swinburne University of Technology, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Future Self Living Lab, Centre for Design Innovation, Swinburne University of Technology, Australia"}], "References": []}, {"ArticleId": 93196823, "Title": "Thiazole inhibitors of α-glucosidase: Positional isomerism modulates selectivity, enzyme binding and potency of inhibition", "Abstract": "Isomerism plays a key role in determining potency, selectivity and type of inhibition exhibited by enzyme inhibitors. We present 20 new benzylidene-hydrazinyl-thiazole inhibitors of α-glucosidase featuring positional isomerism of the methyl group at 3 and 4 positions of their piperidine ring. This structural property helped understand their potency and selectivity to the enzyme yielding new clues to α-glucosidase inhibition. The isomerism was pivotal to improving or deteriorating enzyme binding and potency of inhibition shown by the target compounds. Data from enzyme kinetics experiments were in agreement with docking and molecular dynamics simulations revealing a direct influence of isomerism on enzyme-inhibitor molecular interactions. Generally, the 4-methyl derivatives showed more selectivity toward the enzyme since they established more and stronger molecular contacts with the enzyme than their 3-methyl counterparts. However, the isomerism did not significantly affect the type of inhibition since majority of the compounds exhibited noncompetitive enzyme inhibition except for one. Our work provides essential and interesting clues to understanding α-glucosidase inhibition by thiazole isomers that would help explore new avenues to designing and developing better α-glucosidase inhibitors as antidiabetic drugs.", "Keywords": "Allosteric;Enzyme kinetics;Isomer;Molecular docking;Molecular dynamics;Noncompetitive;Thiazole;α-glucosidase inhibitor", "DOI": "10.1016/j.compbiolchem.2022.107647", "PubYear": 2022, "Volume": "98", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Clinical Biochemistry Unit, Department of Pathology, College of Medicine, King Saud University, Riyadh 11461, Saudi Arabia. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dr. Panjwani Center for Molecular Medicine &amp; Drug Research, International Center for Chemical and Biological Sciences, University of Karachi, Karachi 75210, Pakistan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dr. Panjwani Center for Molecular Medicine &amp; Drug Research, International Center for Chemical and Biological Sciences, University of Karachi, Karachi 75210, Pakistan."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmaceutical Chemistry, Faculty of Pharmacy, Anadolu University, 26470 Eskişehir, Turkey."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacognosy, Faculty of Pharmacy, Anadolu University, 26470 Eskişehir, Turkey; Faculty of Pharmacy, Eastern Mediterranean University, via Mersin 10, Famagusta 99628, N. Cyprus, Turkey."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Pharmaceutical Chemistry, Faculty of Pharmacy, Anadolu University, 26470 Eskişehir, Turkey."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biomedical Research Laboratory, College of Medicine, Alfaisal University, Riyadh, Saudi Arabia."}], "References": []}, {"ArticleId": 93196824, "Title": "Impacts of tax refund on enterprise’s decisions on recycled materials production: A cross-regional perspective", "Abstract": "Recently along with the development of waste recycling markets, a distinct management scenario which extends the traditional municipality-level management scenario to a cross-regional management scenario emerges. Under the cross-regional management scenario, on the one hand, disparities in value-added tax refund policy aggravate imbalanced development of construction and demolition waste recycling markets between regions. On the other hand, how preferential taxation policies would influence decisions of waste recycling enterprises are unclear. To fill the research gap, this paper investigates impacts of value-added tax refund policies on enterprise’s cross-regional recycling decisions by establishing game models that involve two recycling enterprises and two transportation enterprises. Through developing and analyzing game theory based models, the optimal decisions of involved enterprises are obtained. The findings reveal that (i) when the disparities in transportation costs and market capacities between two regions are moderate, recycling enterprises should choose cross-regional production rather than local production; (ii) enlarging market capacities after cross-regional recycling could simultaneously increase recycling quantities of waste in original region. But moderate market capacities and high value-added tax refund rate promote both recycling enterprises being better off in cross-regional production scenario; and (iii) raising value-added tax refund rate is not always necessary under certain conditions, where higher value-added tax refund rate reversely decrease recycling enterprise profits in current region. To enhance the effectiveness of value-added tax refund policy, reducing cross-regional transportation costs and balancing regional market capacities should be applied by the local government. The findings are valuable in expanding the research scope of traditional construction and demolition waste recycling markets and increasing the efficiency of applying tax policies in promoting waste recycling. There are two research limitations; one is that we did not consider the illegal waste dumping (i.e., dumping waste into unauthorized areas) frequently occurring in the process of waste transportation, and the other is that we considered unified preferential taxation for both of the recycling enterprises.", "Keywords": "Construction and demolition waste ; Waste management ; Value-added tax refund ; Cross-regional recycling ; Policies", "DOI": "10.1016/j.cie.2022.108035", "PubYear": 2022, "Volume": "167", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Southwest Jiaotong University, Chengdu 610031, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management, Guangzhou University, Guangzhou 510006, PR China;Corresponding author"}], "References": []}, {"ArticleId": 93196941, "Title": "A stock market trading framework based on deep learning architectures", "Abstract": "<p>Market prediction has been a key interest for professionals around the world. Numerous modern technologies have been applied in addition to statistical models over the years. Among the modern technologies, machine learning and in general artificial intelligence have been at the core of numerous market prediction models. Deep learning techniques in particular have been successful in modeling the market movements. It is seen that automatic feature extraction models and time series forecasting techniques have been investigated separately however a stacked framework with a variety of inputs is not explored in detail. In the present article, we suggest a framework based on a convolutional neural network (CNN) paired with long-short term memory (LSTM) to predict the closing price of the Nifty 50 stock market index. A CNN-LSTM framework extracts features from a rich feature set and applies time series modeling with a look-up period of 20 trading days to predict the movement of the next day. Feature sets include raw price data of target index as well as foreign indices, technical indicators, currency exchange rates, commodities price data which are all chosen by similarities and well-known trade setups across the industry. The model is able to capture the information based on these features to predict the target variable i.e. closing price with a mean absolute percentage error of 2.54% across 10 years of data. The suggested framework shows a huge improvement on return than the traditional buy and hold method.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022.</p>", "Keywords": "Convolutional neural network (CNN);Deep learning architecture;Long short term memory", "DOI": "10.1007/s11042-022-12328-x", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Nirma University, Ahmedabad, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Software Engineer at Quinbay Technology, Bangalore, India."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Data Science associate at ZS, Pune, India."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, School of Technology, Pandit Deendayal Petroleum University, Gandhinagar, India."}], "References": [{"Title": "An innovative neural network approach for stock market prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "2098", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Predictive intelligence using ANFIS‐induced OWAWA for complex stock market prediction", "Authors": "W<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "8", "Page": "4586", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 93196989, "Title": "Based on neutrosophic fuzzy environment: a new development of FWZIC and FDOSM for benchmarking smart e-tourism applications", "Abstract": "The task of benchmarking smart e-tourism applications based on multiple smart key concept attributes is considered a multi-attribute decision-making (MADM) problem. Although the literature review has evaluated and benchmarked these applications, data ambiguity and vagueness continue to be unresolved issues. The robustness of the fuzzy decision by opinion score method (FDOSM) and fuzzy weighted with zero inconsistency (FWZIC) is proven compared with that of other MADM methods. Thus, this study extends FDOSM and FWZIC under a new fuzzy environment to address the mentioned issues whilst benchmarking the applications. The neutrosophic fuzzy set is used for this purpose because of its high ability to handle ambiguous and vague information comprehensively. Fundamentally, the proposed methodology comprises two phases. The first phase adopts and describes the decision matrices of the smart e-tourism applications. The second phase presents the proposed framework in two sections. In the first section, the weight of each attribute of smart e-tourism applications is calculated through the neutrosophic FWZIC (NS-FWZIC) method. The second section employs the weights determined by the NS-FWZIC method to benchmark all the applications per each category (tourism marketing and smart-based tourism recommendation system categories) through the neutrosophic FDOSM (NS-FDOSM). Findings reveal that: (1) the NS-FWZIC method effectively weights the applications’ attributes. Real time receives the highest importance weight (0.402), whereas augmented reality has the lowest weight (0.005). The remaining attributes are distributed in between. (2) In the context of group decision-making, NS-FDOSM is used to uniform the variation found in the individual benchmarking results of the applications across all categories. Systematic ranking, sensitivity analysis and comparison analysis assessments are used to evaluate the robustness of the proposed work. Finally, the limitations of this study are discussed along with several future directions.", "Keywords": "e-tourism; Smart application; NS-FWZIC; NS-FDOSM; Benchmarking; Multi-attribute decision-making; Neutrosophic", "DOI": "10.1007/s40747-022-00689-7", "PubYear": 2022, "Volume": "8", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Science, Komar University of Science and Technology, Sulaymaniyah, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Computer Center, College of Health and Medical Technology, Middle Technical University, Baghdad, Iraq"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "British University in Dubai, Dubai, United Arab Emirates"}, {"AuthorId": 6, "Name": "H. A. Alsattar", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim, Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Informatics Institute for Postgraduate Studies (IIPS), Iraqi Commission for Computers and Informatics (ICCI), Baghdad, Iraq"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, University of Melbourne, Victoria, Australia"}, {"AuthorId": 9, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Future Technology Research Center, National Yunlin University of Science and Technology, Douliou, Taiwan"}, {"AuthorId": 10, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Foundation of Alshuhda, Baghdad, Iraq"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Foundation of Alshuhda, Baghdad, Iraq"}], "References": [{"Title": "e-Tourism beyond COVID-19: a call for transformative research", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "187", "JournalTitle": "Information Technology & Tourism"}, {"Title": "An FMEA-based TOPSIS approach under single valued neutrosophic sets for maritime risk evaluation: the case of ship navigation safety", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "24", "Page": "18749", "JournalTitle": "Soft Computing"}, {"Title": "Fuzzy decision by opinion score method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106595", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel fuzzy hybrid neutrosophic decision‐making approach for the resilient supplier selection problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "12", "Page": "1934", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multidimensional benchmarking of the active queue management methods of network congestion control based on extension of fuzzy decision by opinion score method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "796", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "An augmented Tabu search algorithm for the green inventory-routing problem with time windows", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100802", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "How smart is e-tourism? A systematic review of smart tourism recommendation system applying data management", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>wan <PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "", "Page": "100337", "JournalTitle": "Computer Science Review"}, {"Title": "New mHealth hospital selection framework supporting decentralised telemedicine architecture for outpatient cardiovascular disease-based integrated techniques: Haversine-GPS and AHP-VIKOR", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "219", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Extension of TOPSIS method for group decision-making under triangular linguistic neutrosophic cubic sets", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "5", "Page": "3359", "JournalTitle": "Soft Computing"}, {"Title": "Interval type 2 trapezoidal‐fuzzy weighted with zero inconsistency combined with VIKOR for evaluating smart e‐tourism applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "9", "Page": "4723", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "<PERSON><PERSON><PERSON>–<PERSON><PERSON><PERSON> theory for classification and hybridised models of multi-criteria decision analysis for prioritisation: a telemedicine framework for patients with heart diseases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; O. <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "9", "Page": "4333", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Novel dynamic fuzzy Decision-Making framework for COVID-19 vaccine dose recipients", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "", "Page": "147", "JournalTitle": "Journal of Advanced Research"}, {"Title": "Rise of multiattribute decision‐making in combating COVID‐19: A systematic review of the state‐of‐the‐art literature", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "6", "Page": "3514", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Rescuing emergency cases of COVID-19 patients: An intelligent real-time MSC transfusion framework based on multicriteria decision-making methods", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "9676", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 93197011, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-7051(22)00201-5", "PubYear": 2022, "Volume": "241", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [], "References": []}, {"ArticleId": 93197054, "Title": "Optimized hybrid machine learning approach for smartphone based diabetic retinopathy detection", "Abstract": "<p>Diabetic Retinopathy (DR) is defined as the Diabetes Mellitus difficulty that harms the blood vessels in the retina. It is also known as a silent disease and cause mild vision issues or no symptoms. In order to enhance the chances of effective treatment, yearly eye tests are vital for premature discovery. Hence, it uses fundus cameras for capturing retinal images, but due to its size and cost, it is a troublesome for extensive screening. Therefore, the smartphones are utilized for scheming low-power, small-sized, and reasonable retinal imaging schemes to activate automated DR detection and DR screening. In this article, the new DIY (do it yourself) smartphone enabled camera is used for smartphone based DR detection. Initially, the preprocessing like green channel transformation and CLAHE (Contrast Limited Adaptive Histogram Equalization) are performed. Further, the segmentation process starts with optic disc segmentation by WT (watershed transform) and abnormality segmentation (Exudates, microaneurysms, haemorrhages, and IRMA) by Triplet half band filter bank (THFB). Then the different features are extracted by Haralick and ADTCWT (Anisotropic Dual Tree Complex Wavelet Transform) methods. Using life choice-based optimizer (LCBO) algorithm, the optimal features are chosen from the mined features. Then the selected features are applied to the optimized hybrid ML (machine learning) classifier with the combination of NN and DCNN (Deep Convolutional Neural Network) in which the SSD (Social Ski-Driver) is utilized for the best weight values of hybrid classifier to categorize the severity level as mild DR, severe DR, normal, moderate DR, and Proliferative DR. The proposed work is simulated in python environment and to test the efficiency of the proposed scheme the datasets like APTOS-2019-Blindness-Detection, and EyePacs are used. The model has been evaluated using different performance metrics. The simulation results verified that the suggested scheme is provides well accuracy for each dataset than other current approaches.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022.</p>", "Keywords": "And DIY smartphone enabled camera;Diabetic retinopathy;Machine learning;Optimization;Segmentation;Smartphone", "DOI": "10.1007/s11042-022-12103-y", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Amity University, Uttar Pradesh, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity University, Uttar Pradesh, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "U.<PERSON><PERSON><PERSON><PERSON> University, Uttar Pradesh, India."}], "References": [{"Title": "A novel life choice-based optimizer", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; K. P<PERSON> S<PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "12", "Page": "9121", "JournalTitle": "Soft Computing"}, {"Title": "Parameters optimization of support vector machines for imbalanced data using social ski driver algorithm", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "11", "Page": "6925", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Optimal feature selection-based diabetic retinopathy detection using improved rider optimization algorithm enabled with deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "1431", "JournalTitle": "Evolutionary Intelligence"}]}, {"ArticleId": 93197078, "Title": "Model Based Intrusion Detection using Data Mining Techniques with Feature Reduction", "Abstract": "<p>As the technology is advancing so are the data storing practices. Nowadays data is stored online which is the main reason as to why the data is constantly under threat. Therefore there is an urgent need of computer se- curity for securing this confidential data, which is mostly customer personal data which if got leaked will not only pose threat to the customer but also to the organization liable for storing and preserving that data. These unwanted activities are termed as intrusions and the detection of these unwanted activities by constantly monitoring and analysing the system is known as intrusion detection. IDS created using data mining techniques is an effective way of detecting intrusions whose implementation is discussed ahead in this paper. The approach involves building of classification model and hybrid model which are created using classification techniques and, combining both classification and clustering techniques respectively. Classification model can detect known attacks effectively whereas hybrid models can detect unknown or new attacks also. NSL-KDD dataset is used as training dataset which is normalalized and then its feature reduction is done using different techniques. The best feature selection technique among all is chosen by using decision table algorithm. The comparison of the results of different models is done over different performance evaluation parameters. The results show that hybrid models perform better than classification models with improved results as the the data is first preprocessed which makes a classifier more effective.</p>", "Keywords": "Data mining;IDS;NSL-KDD;CFS Feature Selection;Feature selection;intrusion detection;U2R attacks;R2L attacks", "DOI": "10.18535/ijecs/v11i02.4657", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 17925, "JournalTitle": "International Journal Of Engineering And Computer Science", "ISSN": "", "EISSN": "2319-7242", "Authors": [{"AuthorId": 1, "Name": "Jyotsna Goyal", "Affiliation": "Department of Computer Science Engineering and Technology Thapar University Patiala, India"}], "References": []}, {"ArticleId": 93197100, "Title": "Client Selection in Federated Learning under Imperfections in Environment", "Abstract": "<p>Federated learning promises an elegant solution for learning global models across distributed and privacy-protected datasets. However, challenges related to skewed data distribution, limited computational and communication resources, data poisoning, and free riding clients affect the performance of federated learning. Selection of the best clients for each round of learning is critical in alleviating these problems. We propose a novel sampling method named the irrelevance sampling technique. Our method is founded on defining a novel irrelevance score that incorporates the client characteristics in a single floating value, which can elegantly classify the client into three numerical sign defined pools for easy sampling. It is a computationally inexpensive, intuitive and privacy preserving sampling technique that selects a subset of clients based on quality and quantity of data on edge devices. It achieves 50–80% faster convergence even in highly skewed data distribution in the presence of free riders based on lack of data and severe class imbalance under both Independent and Identically Distributed (IID) and Non-IID conditions. It shows good performance on practical application datasets.</p>", "Keywords": "federated learning; client selection; class imbalance; free-riders; active learning; faster convergence; FedAvg; FSVRG; COOP federated learning ; client selection ; class imbalance ; free-riders ; active learning ; faster convergence ; FedAvg ; FSVRG ; COOP", "DOI": "10.3390/ai3010008", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Indian Institute of Technology (Indian School of Mines) Dhanbad, Dhanbad 826004, India↑These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Indian Institute of Technology (Indian School of Mines) Dhanbad, Dhanbad 826004, India↑These authors contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, UiT The Arctic University of Norway, 9019 Tromsø, Norway↑Author to whom correspondence should be addressed"}], "References": []}, {"ArticleId": 93197136, "Title": "Abrechnungsdaten im Gesundheitswesen: Da muss mehr Nutzen gehen", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2022.02.11", "PubYear": 2022, "Volume": "", "Issue": "2", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Model Predictive Control for Mobile Cart Based on Canonical Evolution Strategies", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.122049", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "肇江 陈", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Performance Analysis on Destination Driven Multicast Routing Algorithm", "Abstract": "The internet’s architecture today has a problem in routing information depending on what the receiver is interested in without knowing the sender and receiver addresses. As a result, the Publish-Subscribe paradigm was developed. In this network, we build and use in the design, implementation, and evaluation of Publish-Subscribe network via destination driven multicast routing algorithm for selecting the shortest path in the network. Basically, the networks have Router to perform routing mechanism, the publisher is the producer of information, and the subscriber is the consumer of information with their own deferent type of module for facilitating their function. Every connection in the network is bidirectional way of communication (an undirected graph) with random seed available in the network. Each Router has topology management module for creating a picture of the networks and computing the available path. It informs to the forwarder in order to send the information of network for intended receiver. Record table module used for recording of the network information comes from the subscriber or the publisher via link state advertisement then it informs to the topology manager. In this network, the receiver and sender don’t expect to be active at the same time, don’t know each other’s addresses, and don’t use any blocking mechanisms or client send requests and server replay responses. The Publish-Subscribe network is first developed and designed enough, after which it is implemented and evaluated using the destination driven multicast routing algorithm (DDMC) to pick the shortest path in the network and active match published information. The proposed work evaluated via total bit (produced 1,000,000 bit per second), and throughput was 83.33%.", "Keywords": "Publish-Subscribe;DDMC;Link State", "DOI": "10.4236/jcc.2022.102004", "PubYear": 2022, "Volume": "10", "Issue": "2", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Assosa University, Assosa, Ethiopia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Assosa University, Assosa, Ethiopia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ethiotelecom IT Operation and Maintenance Supervisor, Assosa, Ethiopia"}], "References": []}, {"ArticleId": ********, "Title": "Deep Learning-Based Emotion Detection", "Abstract": "In order to make artificial intelligence smarter by detecting user emotions, this project analyzes and determines the current type of human emotions through computer vision, semantic recognition and audio feature classification. In facial expression recognition, for the problems of large number of parameters and poor real-time performance of expression recognition methods based on deep learning, <PERSON> and <PERSON> et al. proposed a face expression recognition method based on multilayer feature fusion with light-weight convolutional networks, which uses an improved inverted residual network as the basic unit to build a lightweight convolutional network model. Based on this method, this experiment optimizes the traditional CNN MobileNet model and finally constructs a new model framework ms_model_M, which has about 5% of the number of parameters of the traditional CNN MobileNet model. ms_model_M is tested on two commonly used real expression datasets, FER-2013 and AffectNet, the accuracy of ms_model_M is 74.35% and 56.67%, respectively, and the accuracy of the traditional MovbliNet model is 74.11% and 56.48% in the tests of these two datasets. This network structure well balances the recognition accuracy and recognition speed of the model. For semantic emotion detection and audio emotion detection, the existing models and APIs are used in this experiment.", "Keywords": "Expression Recognition;CNN;Face Recognition;Semantic Recognition;Feature Fusion;Inverted Residual", "DOI": "10.4236/jcc.2022.102005", "PubYear": 2022, "Volume": "10", "Issue": "2", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dublin City University, Dublin, Ireland ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dublin City University, Dublin, Ireland ."}], "References": []}, {"ArticleId": 93197190, "Title": "Some Inapproximability Results of MAP Inference and Exponentiated Determinantal Point Processes", "Abstract": "We study the computational complexity of two hard problems on determinantal point processes (DPPs). One is maximum a posteriori (MAP) inference, i.e., to find a principal submatrix having the maximum determinant. The other is probabilistic inference on exponentiated DPPs (E-DPPs), which can sharpen or weaken the diversity preference of DPPs with an exponent parameter p. We present several complexity-theoretic hardness results that explain the difficulty in approximating MAP inference and the normalizing constant for E-DPPs. We first prove that unconstrained MAP inference for an n × n matrix is NP-hard to approximate within a factor of 2βn, where β = 10-1013. This result improves upon the best-known inapproximability factor of (9/8 - ϵ), and rules out the existence of any polynomial-factor approximation algorithm assuming P ≠ NP. We then show that log-determinant maximization is NP-hard to approximate within a factor of 5/4 for the unconstrained case and within a factor of 1 + 10-1013 for the size-constrained monotone case. In particular, log-determinant maximization does not admit a polynomial-time approximation scheme unless P = NP. As a corollary of the first result, we demonstrate that the normalizing constant for E-DPPs of any (fixed) constant exponent p ≥ β-1 = 101013 is NP-hard to approximate within a factor of 2βpn, which is in contrast to the case of p ≤ 1 admitting a fully polynomial-time randomized approximation scheme. © 2022 AI Access Foundation. All rights reserved.", "Keywords": "machine learning;mathematical foundations", "DOI": "10.1613/jair.1.13288", "PubYear": 2022, "Volume": "73", "Issue": "", "JournalId": 56494, "JournalTitle": "Journal of Artificial Intelligence Research", "ISSN": "", "EISSN": "1076-9757", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CyberAgent, Inc., 40-1 Udagawacho Shibuya-ku, Tokyo, Japan"}], "References": []}, {"ArticleId": 93197274, "Title": "An Efficient and Secure Authentication for Ambient Assisted Living System", "Abstract": "<p>Although the birthrate is declining, the average life expectancy continues to increase. Therefore, it is more important for elderly people to maintain their independence while staying at home. Ambient Assisted Living (AAL) includes the use of devices and methods of ensuring that elderly people can stay safe and age at home rather than at a facility. Assisted living services help people live as independently and safely as possible when they can no longer perform everyday activities on their own. Because the information transmitted in AAL systems is personal, the security and privacy of such data are becoming important issues that must be addressed. Herein, we propose an efficient and secure authentication scheme for an AAL system. Our proposed authentication scheme not only satisfies several important security requirements of such a system but also withstands various types of attacks. Moreover, the proposed authentication scheme achieves lightweight performance by manipulating basic cryptographic operations including bitwise-eXclusive-OR (XOR) and hash functions. We simulated our proposed authentication scheme using Automated Validation of Internet Security Protocols and Applications (AVISPA), which is a prominent security verification tool. Security and performance analysis show that our proposed scheme is not only robust against several attacks and has a lower computational cost in terms of execution time than those of existing authentication schemes.</p>", "Keywords": "ambient assisted living; healthcare.; wearable computing; Web security and privacy", "DOI": "10.13052/jwe1540-9589.2136", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 58533, "JournalTitle": "Journal of Web Engineering", "ISSN": "1540-9589", "EISSN": "1544-5976", "Authors": [{"AuthorId": 1, "Name": "Myung-Kyu Yi", "Affiliation": "College of It Convergence, Gachon University13120, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of It Convergence, Gachon University13120, South Korea"}], "References": []}, {"ArticleId": 93197364, "Title": "An Analysis Model of Potential Topics in English Essays Based on Semantic Space", "Abstract": "<p lang=\"zh\"><p><p>With the reform of English examination in China in recent years, the automatic evaluation of English essays as subjective questions has always been the focus and difficulty of research. The existing automatic essay evaluation system (AEE) has obtained good feedback on the vocabulary, syntactic and other features of English essays, but there is still a problem of low accuracy in the analysis of potential topic in English essay. In order to solve this problem, this paper takes relational triples as the carrier to analyze the potential topics of English essays. By constructing the hierarchical topic trees hybrid semantic spaces to carry out topic clustering, distributed representation of topic relational triples and topic set extension in English essays. Then, based on the improved on-topic analysis algorithm in this paper, the paper analyzes the topic of English essay in multiple dimensions to obtain more abundant potential on-topic semantic information. The experiment results show that the proposed model can reduce the noise caused by non-topic words effectively, and improve the fine-grained topic semantic space in English essays, and the proposed model has better performance than the current methods of on-topic analysis in English esssays.</p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022023301014", "PubYear": 2022, "Volume": "33", "Issue": "1", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93197370, "Title": "Multiple Scene Sentiment Analysis Based on Chinese Speech and Text", "Abstract": "<p lang=\"zh\"><p><p>This paper proposes a multi-scene sentiment analysis model for Chinese speech and text based on CNN-BiGRU-CTC + ERNIE-BiLSTM. The model is applied to the intelligent customer service scenario. While conducting voice interaction, intelligent customer service can obtain the user’s current emotion, to give a more humane answer and improve the user experience. All the training data sets in this paper adopted public data sets such as Aishell-1 and NLPCC 2014, etc.We have been able to achieve a testing accuracy of about 94.5%. The accuracy is improved by 5.24% compared to the latest speech sentiment analysis model that uses audio as a feature. The advantage of this paper is that it adopts the ERNIE language pre-training model to conduct sentiment analysis on speech signals, which still has a good classification accuracy in the case of individual wrong words in speech recognition. </p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022023301015", "PubYear": 2022, "Volume": "33", "Issue": "1", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93197397, "Title": "SDN for IoT Environment: A Survey and Research Challenges", "Abstract": "Recently, it has emerged that both Internet of Things (IoT), and Software Defined Network (SDN) are becoming popular technologies. The main goal of IoT is to link electronic devices via the internet, meanwhile SDN facilitates network arrangement for management of a network by distinguishing the control layer and the data layer from each other. The number of electronic devices over the internet is increasing constantly, therefore it is a complicated process to manage and control especially over the huge distributed network. IoT network can be reasonably flexible and programmable through The SDN without introducing any trouble to the previously implemented network infrastructure. This paper reviews various IoT domains and applications such as cellular network, wireless Sensor, IoT management, security and smart city framework and common IoT SDN solutions. Moreover, The IoT and SDN notion has been explored critically, with assessing the current contributions in the research field. Lastly, analyzing current available solutions for SDN-based IoT implementations comparatively helps easily understanding the emerging trends view.", "Keywords": "SDN;SDN Applications;IoT;Integration of SDN-IoT", "DOI": "10.1051/itmconf/20224201005", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Raparin, Rania, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Raparin, Rania, Iraq"}], "References": [{"Title": "Traffic modeling and performance evaluation of SDN‐based NB‐IoT access network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "e5145", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A hierarchical approach for accelerating IoT data management process based on SDN principles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "103027", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 93197399, "Title": "Natural Language Processing and Parallel Computing for Information Retrieval from Electronic Health Records", "Abstract": "In this paper, we review the literature to find suitable information retrieval techniques for EHealth. Also discussed NLP techniques that have been proved their capability to extract valuable information in unstructured data from EHR. One of the best NLP techniques used for searching free text is LSI, due to its capability of finding semantic terms and in rich the search results by finding the hidden relations between terms. LSI uses a mathematical model called SVD, which is not scalable for large amounts of data due to its complexity and exhausts the memory, and a review for recent applications of LSI was discussed.", "Keywords": "", "DOI": "10.1051/itmconf/20224201013", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hashemite University, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Al al-Bayt University, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Al al-Bayt University, Jordan"}], "References": []}, {"ArticleId": 93197407, "Title": "Implementation of Soft Computing Techniques in Predicting and Optimizing the Operating Parameters of Compression Ignition Diesel Engines: State-of-the-Art Review, Challenges, and Future Outlook", "Abstract": "<p>Fossil fuels being the primary source of energy to global industrialization and rapid development are being consumed at an alarming rate, thus creating a dire need to search for alternative fuels and optimize the internal combustion (IC) engine performance parameters. Traditional methods of testing and optimizing the performances of IC engines are complex, time consuming, and expensive. This has led the researchers to shift their focus to faster and inexpensive techniques like soft computing (SC), which predict the optimum performance with a substantial accuracy. The SC techniques commonly used are artificial neural network (ANN), fuzzy logic, adaptive neuro-fuzzy inference system (ANFIS), genetic algorithm (GA), particle swarm optimization (PSO), and hybrid techniques like ANN-GA, ANN-PSO, and others. The data of engine parameters predicted with these models have been found to be in very close indices with the experimented values making them a reliable predicting tool. The ANN, fuzzy logic, and ANFIS models have been found to have a correlation coefficient (R) above 0.9 suggesting a good level of agreement between experimented and predicted values of several engine-out parameters. In the present review article, the application of various SC techniques in the prediction and the optimization of output parameters of compression ignition (CI) diesel engines are thoroughly reviewed along with their future prospects and challenges. This review work highlights the implication of these SC techniques in CI diesel engines run on both conventional fuel as well as biodiesels.</p>", "Keywords": "Engines;Diesel engines;Emissions;Algorithms", "DOI": "10.1115/1.4053920", "PubYear": 2022, "Volume": "22", "Issue": "5", "JournalId": 9341, "JournalTitle": "Journal of Computing and Information Science in Engineering", "ISSN": "1530-9827", "EISSN": "1944-7078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Energy Science and Engineering, Indian Institute of Technology Guwahati, Guwahati 781039, Assam, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Indian Institute of Technology Guwahati, Guwahati 781039, Assam, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Indian Institute of Technology Guwahati, Guwahati 781039, Assam, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> K. Saha", "Affiliation": "Department of Mechanical Engineering, Indian Institute of Technology Guwahati, Guwahati 781039, Assam, India"}], "References": [{"Title": "Thermal Error Modeling of Feed Axis in Machine Tools Using Particle Swarm Optimization-Based Generalized Regression Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "A Predictive Dispatching Rule Assisted by Multi-Layer Perceptron for Scheduling Wafer Fabrication Lines", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "3", "Page": "", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Specific Soft Computing Strategies for Evaluating the Performance and Emissions of an SI Engine Using Alcohol-Gasoline Blended Fuels—A Comprehensive Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "4", "Page": "3293", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Application of Artificial Neural Network for Internal Combustion Engines: A State of the Art Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "2", "Page": "897", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 93197480, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0169-023X(22)00017-9", "PubYear": 2022, "Volume": "138", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [], "References": []}, {"ArticleId": 93197578, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0097-8493(22)00015-2", "PubYear": 2022, "Volume": "102", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [], "References": []}, {"ArticleId": 93197604, "Title": "Color image encryption algorithm based on hyperchaotic system and improved quantum revolving gate", "Abstract": "<p>Because quantum computing can break encryption systems based on mathematical models, this paper proposes a new color quantum image encryption algorithm based on hyperchaotic system and quantum revolving gate. First, the classic image is diffused, the matrix generated by the four-wing chaotic system is serialized, and XORed with the key and plaintext to obtain the semi-ciphertext image. Then, the quantum rotation method is used to perform scrambling operations on images quantized using the improved novelty enhanced quantum representation (NEQR) model. The angle of rotation is determined by the sequence generated by the piecewise chaotic map. Finally, the experimental results and performance analysis prove that the algorithm has high security performance.</p>", "Keywords": "Four-wing chaotic system; Quantum rotation; NEQR; Quantum color image encryption", "DOI": "10.1007/s11042-022-12220-8", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime University, Dalian, China;Guangxi Key Lab of Multi-source Information Mining & Security, Guangxi Normal University, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime University, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication, Lanzhou University of Technology, Lanzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime University, Dalian, China"}], "References": [{"Title": "Content-based image retrieval system using ORB and SIFT features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "2725", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Image encryption algorithm for synchronously updating Boolean networks based on matrix semi-tensor product theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "16", "JournalTitle": "Information Sciences"}, {"Title": "Double quantum color images encryption scheme based on DQRCI", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "9-10", "Page": "6661", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Image encryption algorithm based on the matrix semi-tensor product with a compound secret key produced by a Boolean network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "195", "JournalTitle": "Information Sciences"}, {"Title": "An efficient technique for object recognition using Shi-<PERSON><PERSON> corner detection algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4423", "JournalTitle": "Soft Computing"}, {"Title": "A geometrically robust multi-bit video watermarking algorithm based on 2-D DFT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "9", "Page": "13491", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "2D object recognition: a comparative analysis of SIFT, SURF and ORB feature descriptors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "12", "Page": "18839", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A privacy image encryption algorithm based on piecewise coupled map lattice with multi dynamic coupling coefficient", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "569", "Issue": "", "Page": "217", "JournalTitle": "Information Sciences"}, {"Title": "Image steganography based on style transfer and quaternion exponent moments", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107618", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 93197607, "Title": "Tiled streaming for layered 3D virtual reality videos with viewport prediction", "Abstract": "<p>In recent years, the demand of 3D video services has gradually increased. More and more bandwidth hungry applications are proposed, such as immersive media services which need a virtual reality (VR) headset and 3D VR videos to provide users immersive experience of watching 3D VR videos. Tiled streaming is often used for providing 3D VR videos die to the high bitrates of 3D VR videos. In a VR headset the sight is limited. Users can only watch a fraction of entire 3D VR videos in the viewport. Transmitting the content of a VR video outside the viewport is unnecessary and infeasible due to the high bitarate of 3D VR videos. Generally, In VR applications, content within the viewport should keep the highest quality while providing only basic quality outside the viewprot. Such an adaptive streaming relies on the precision of viewport prediction. Errors of viewport prediction result in the expensive overhead of quality repairing and re-transmission delay on the traditional versioned coded VR videos,, where a whole new version of video content needs to be resent and the sent content of low-quality version is discarded and cannot be reused. In this paper, we propose a novel adaptative streaming approach for providing 3D VR videos using Scalable Video Coding (SVC) with viewport prediction. For better quality adaptation, we take CubeMap projection as the projection format of 3D VR videos. Besides, we use the Scalability extension of High Efficiency Video Coding (SHVC) to encode 3D VR videos to multiple layers for supplying different qualities and also use the tiling to divide videos into rectangular regions for finer quality adaptation. The experimental results show that our proposed method outperformed other previous approaches in terms of the weighted video quality and the relative time spent on the highest quality, especially with low available network bandwidth. Even under certain miss rates of tiles, compared to previous approaches, our proposed method requires fewer bandwidth overhead and shorter re-transmission delay for repairing the quality of missed tiles in most cases.</p>", "Keywords": "Virtual reality; SHVC; Viewport prediction; Tiling; Metaverse", "DOI": "10.1007/s11042-022-12277-5", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National University of Tainan, Tainan, Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National University of Tainan, Tainan, Republic of China"}], "References": [{"Title": "Content-based image retrieval system using ORB and SIFT features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "2725", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A Survey of Deep Learning and Its Applications: A New Paradigm to Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "1071", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 93197617, "Title": "A comparative study of various techniques of image segmentation for the identification of hand gesture used to guide the slide show navigation", "Abstract": "<p>Interaction between human and computer is becoming powerful day by day with the development of ubiquitous computing. Hand gesture recognition plays an efficient role to establish interaction between human and computer. Gesture is way of communication to understand body language. We can interact with computer using various devices like keyboard, mouse etc. This paper focus on comparing the different segmentation technique used to enhance the controling of slide show navigation without using these devices like mouse, keyboard, touch screen or laser device etc. Hand gesture recognition used to perform interaction by capturing the image, the image segmentation techniques detect the region of interst(ROI) which show the hand region. The gesture can be detected by analysing segmented hand region. All segemented regions are compared on the basis of their features. This paper show comparison of thresholding, laplacian kernel, k-means and canny edge detection segmentation technique use for recognition system to makes interaction easy, convenient and does not require any other system.</p>", "Keywords": "Human computer interaction; Region of interest; Hand gesture recognition; Machine learning; Image segmentation", "DOI": "10.1007/s11042-022-12203-9", "PubYear": 2022, "Volume": "81", "Issue": "10", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Uttarakhand Technical University, Dehradun, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graphic Era Hill University, Bhimtal Campus, Nainital, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CCSIT, Teerthanker Mahaveer University, Moradabad, India"}], "References": [{"Title": "Design of hand gesture recognition system for human-computer interaction", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "9-10", "Page": "5989", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An integrated two-stage approach for image segmentation via active contours", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "21177", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Design and evaluation of a hand gesture recognition approach for real-time interactions", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17707", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multi-scale region composition of hierarchical image segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "43-44", "Page": "32833", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An improved Gabor wavelet transform and rough K-means clustering algorithm for MRI brain tumor image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "6939", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93197712, "Title": "Preface", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.datak.2022.101994", "PubYear": 2022, "Volume": "138", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire <PERSON>, Conservatoire National des Arts et Métiers, 292 rue St Martin, FR-75141 Paris cedex 03, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science and Engineering, University of Derby, Markeaton Street, Derby DE22 3AW, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Language Technology German Research Center for Artificial Intelligence, Stuhlsatzenhaus weg 3 Saarland Informatics Campus D 3 2 D-66123, Saarbrücken, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Semantic Computing Group, Bielefeld University, Zehlendorfer Damm 201 D-33619 Bielefeld, Germany"}], "References": []}, {"ArticleId": 93197713, "Title": "Computational Model of the Transition from Novice to Expert Interaction Techniques", "Abstract": "<p>Despite the benefits of expert interaction techniques, many users do not learn them and continue to use novice ones. This article aims at better understanding if, when and how users decide to learn and ultimately adopt expert interaction techniques. This dynamic learning process is a complex skill-acquisition and decision-making problem. We first present and compare three generic benchmark models, inspired by the neuroscience literature, to explain and predict the learning process for shortcut adoption. Results show that they do not account for the complexity of users’ behavior. We then introduce a dedicated model, Transition, combining five cognitive mechanisms: implicit and explicit learning, decay, planning and perseveration. Results show that our model outperforms the three benchmark models both in terms of model fitting and model simulation. Finally, a post-analysis shows that each of the five mechanisms contribute to goodness-of-fit, but the role of perseveration is unclear regarding model simulation.</p>", "Keywords": "Computational models; Interaction Techniques; Shortcut; Menus; Computational Rationality", "DOI": "10.1145/3505557", "PubYear": 2023, "Volume": "30", "Issue": "5", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sorbonne Université, CNRS"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sorbonne Université, CNRS"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sorbonne Université, CNRS"}], "References": []}, {"ArticleId": 93197715, "Title": "Cartographs enable interpretation of complex network visualizations", "Abstract": "Networks offer a powerful visual representation of complex systems. Cartographs introduce a diverse set of network layouts for highlighting and visually inspecting chosen characteristics of a network. The resulting visualizations are interpretable and can be used to explore complex datasets, such as large-scale biological networks.", "Keywords": "Computational science;Network topology;Scientific data;Software", "DOI": "10.1038/s43588-022-00203-6", "PubYear": 2022, "Volume": "2", "Issue": "2", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [], "References": []}, {"ArticleId": 93197721, "Title": "Designing Creative AI Partners with COFI: A Framework for Modeling Interaction in Human-AI Co-Creative Systems", "Abstract": "<p>Human-AI co-creativity involves both humans and AI collaborating on a shared creative product as partners. In a creative collaboration, interaction dynamics, such as turn-taking, contribution type, and communication, are the driving forces of the co-creative process. Therefore the interaction model is a critical and essential component for effective co-creative systems. There is relatively little research about interaction design in the co-creativity field, which is reflected in a lack of focus on interaction design in many existing co-creative systems. The primary focus of co-creativity research has been on the abilities of the AI. This article focuses on the importance of interaction design in co-creative systems with the development of the Co-Creative Framework for Interaction design (COFI) that describes the broad scope of possibilities for interaction design in co-creative systems. Researchers can use COFI for modeling interaction in co-creative systems by exploring alternatives in this design space of interaction. COFI can also be beneficial while investigating and interpreting the interaction design of existing co-creative systems. We coded a dataset of existing 92 co-creative systems using COFI and analyzed the data to show how COFI provides a basis to categorize the interaction models of existing co-creative systems. We identify opportunities to shift the focus of interaction models in co-creativity to enable more communication between the user and AI leading to human-AI partnerships.</p>", "Keywords": "Human-AI Co-Creativity; Co-Creativity; Interaction Design; Framework", "DOI": "10.1145/3519026", "PubYear": 2023, "Volume": "30", "Issue": "5", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of North Carolina at Charlotte"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of North Carolina at Charlotte"}], "References": [{"Title": "Human-AI Collaboration in a Cooperative Game Setting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 93197879, "Title": "Artificial intelligence as a future in cancer surgery", "Abstract": "", "Keywords": "", "DOI": "10.35713/aic.v3.i1.11", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 77765, "JournalTitle": "WArtificial Intelligence in Cancer", "ISSN": "2644-3228", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93197915, "Title": "Selection of features from power theories to compose NILM datasets", "Abstract": "The load disaggregation concept is gaining attention due to the increasing need for optimized energy utilization and detailed characterization of electricity consumption profiles, especially through Nonintrusive Load Monitoring (NILM) approaches. This occurs since knowledge about individualized consumption per appliance allows to create strategies striving for energy savings, improvement of energy efficiency, and creating energy awareness to consumers. Moreover, by using feature extraction to devise energy disaggregation, one can achieve accurate identification of electric appliances. However, even though several literature works propose distinct features to be utilized, no consensus exists in the literature about the most appropriate set of features that ensure high accuracy on load disaggregation. Thus, beyond presenting a critical analysis of some significant features often selected in the literature, this paper proposes identifying the most relevant ones considering collinearity and machine learning algorithms. The results show that high-performance metrics can be achieved with fewer features than usually adopted in the literature. Moreover, it is demonstrated that the Conservative Power Theory can offer the most representative features for appliance identification, leading to efficient power consumption disaggregation.", "Keywords": "Load disaggregation ; Nonintrusive load monitoring ; Features quality ; Smart meters ; Electric consumption management", "DOI": "10.1016/j.aei.2022.101556", "PubYear": 2022, "Volume": "52", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Federal University of Technology - Parana (UTFPR), <PERSON><PERSON><PERSON><PERSON>, PR, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, University of Campinas (UNICAMP), Campinas, SP, Brazil"}, {"AuthorId": 3, "Name": "Thais <PERSON><PERSON>", "Affiliation": "Institute of Science and Technology, São Paulo State University (UNESP), Sorocaba, SP, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Science and Technology, São Paulo State University (UNESP), Sorocaba, SP, Brazil"}, {"AuthorId": 5, "Name": "Flavio A.S. Gonçalves", "Affiliation": "Institute of Science and Technology, São Paulo State University (UNESP), Sorocaba, SP, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Science and Technology, São Paulo State University (UNESP), Sorocaba, SP, Brazil"}], "References": [{"Title": "Daily Activity Feature Selection in Smart Homes Based on Pearson Correlation Coefficient", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "2", "Page": "1771", "JournalTitle": "Neural Processing Letters"}, {"Title": "A convolutional autoencoder-based approach with batch normalization for energy disaggregation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "3", "Page": "2961", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 93197924, "Title": "Integration of semantic patterns and fuzzy concepts to reduce the boundary region in three-way decision-making", "Abstract": "A three-way decision framework based on fuzzy concepts can better understand uncertain decision boundaries by automatically dividing opinions into three decision regions : positive, negative, and boundary regions. However, when the fuzzy values of the opinions are very close, the boundary regions tend to be large. The difficult problem is how to significantly reduce the boundary regions while maintaining high classification accuracy . The most useful opinion features include fuzzy concepts and semantic features (or patterns). Unlike fuzzy concepts, semantic features are represented by using semantic patterns that frequently appear in opinions. We also observe a low statistical correlation between semantic patterns and fuzzy concepts. Therefore, this paper proposes a novel opinion classification method that integrates semantic patterns with fuzzy concepts in a three-way decision framework. The new method can increase the discriminative power of classifying opinions using features. Experimental results verify that the integration of fuzzy concepts and semantic patterns has better classification performance than using fuzzy concepts alone. This also shows that fusing multiple features is an effective solution to represent opinions more meaningfully, thereby effectively improving classification accuracy and reducing uncertain boundaries in three-way opinion classification.", "Keywords": "", "DOI": "10.1016/j.ins.2022.02.036", "PubYear": 2022, "Volume": "595", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "L.D.C.S. <PERSON>", "Affiliation": "School of Computer Science, Faculty of Science, Queensland University of Technology, Australia;Department of Information Technology, Faculty of Management Studies and Commerce, University of Sri Jayewardenepura, Sri Lanka;School of Computer Science, University of Colombo, Sri Lanka"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Faculty of Science, Queensland University of Technology, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Faculty of Science, Queensland University of Technology, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Colombo, Sri Lanka"}], "References": [{"Title": "A comparative study of machine translation for multilingual sentence-level sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1078", "JournalTitle": "Information Sciences"}, {"Title": "Two-stage three-way enhanced technique for ensemble learning in inclusive policy text classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "271", "JournalTitle": "Information Sciences"}, {"Title": "Three-way decision based on third-generation prospect theory with Z-numbers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "569", "Issue": "", "Page": "13", "JournalTitle": "Information Sciences"}, {"Title": "Positionless aspect based sentiment analysis using attention mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107136", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel fusion-based deep learning model for sentiment analysis of COVID-19 tweets", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107242", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A sentiment analysis-based expert weight determination method for large-scale group decision-making driven by social media data", "Authors": "<PERSON><PERSON> Wan; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115629", "JournalTitle": "Expert Systems with Applications"}, {"Title": "TWD-SFNN: Three-way decisions with a single hidden layer feedforward neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "15", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 93197947, "Title": "Room temperature optical detection of ultra-low ozone concentration using photoluminescent ZnO nanohybrids", "Abstract": "The highly sensitive room temperature (RT) photoluminescence (PL) emission of zinc oxide (ZnO) in the presence of ozone (O<sub>3</sub>) is demonstrated for the first time. The optical materials examined consist of commercially available luminescent ZnO nanoparticles (ZnO NPs), as well as ZnO/polymer nanohybrids, with different polymeric matrices (poly(poly(ethylene glycol) methyl ether methacrylate) (PPEGMA) and polydimethylsiloxane (PDMS)), which are excited with a UV pulsed laser source (λ<sub>ex</sub>= 248 nm, τ<sub>ex</sub>= 15 ns). The PL emission of the ZnO NPs and their nanohybrids (ZnO/PPEGMA, ZnO/PDMS) is investigated upon exposure to ozone gas and their sensing characteristics, such as response ( % Δ I ), reversibility, response/recovery time are determined, as a function of ozone concentration in synthetic air. Sensing tests revealed that all materials allowed the detection of ozone gas in a wide range of concentrations in synthetic air (1600 down to 50 ppb), with the ZnO/PDMS nanohybrid exhibiting the highest response and recovery time of about 50 s (at 750 ppb ozone concentration) and 100 s, respectively. Finally, its remarkable optical response of about 20% at 50 ppb ozone concentration at RT, underlines the great potential of the ZnO/PDMS nanohybrid to serve as a sensitive probe for ozone detection, thereby introducing new developments in optosensing applications.", "Keywords": "ZnO ; Nanohybrids ; Photoluminescence ; Optical sensing ; Ozone sensor", "DOI": "10.1016/j.snb.2022.131614", "PubYear": 2022, "Volume": "359", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, University of Crete, 70013 Heraklion, Crete, Greece;Institute of Electronic Structure and Laser, Foundation for Research and Technology-Hellas (IESL-FORTH), P.O. Box 1385, 70013 Heraklion, Crete, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Science and Technology, University of Crete, 70013 Heraklion, Crete, Greece;Institute of Electronic Structure and Laser, Foundation for Research and Technology-Hellas (IESL-FORTH), P.O. Box 1385, 70013 Heraklion, Crete, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Electronic Structure and Laser, Foundation for Research and Technology-Hellas (IESL-FORTH), P.O. Box 1385, 70013 Heraklion, Crete, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Electronic Structure and Laser, Foundation for Research and Technology-Hellas (IESL-FORTH), P.O. Box 1385, 70013 Heraklion, Crete, Greece;Department of Physics, University of Crete, 70013 Heraklion, Crete, Greece"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Science and Technology, University of Crete, 70013 Heraklion, Crete, Greece;Institute of Electronic Structure and Laser, Foundation for Research and Technology-Hellas (IESL-FORTH), P.O. Box 1385, 70013 Heraklion, Crete, Greece"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Electronic Structure and Laser, Foundation for Research and Technology-Hellas (IESL-FORTH), P.O. Box 1385, 70013 Heraklion, Crete, Greece;Corresponding author"}], "References": [{"Title": "FET-type gas sensors: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "330", "Issue": "", "Page": "129240", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Ozone detection in the ppt-level with rGO-ZnO based sensor", "Authors": "B.S. de Lima; <PERSON><PERSON><PERSON><PERSON>; W.A.S. Silva", "PubYear": 2021, "Volume": "338", "Issue": "", "Page": "129779", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 93197954, "Title": "SAMBA: A Generic Framework for Secure Federated Multi-Armed Bandits", "Abstract": "The multi-armed bandit is a reinforcement learning model where a learning agent re- peatedly chooses an action (pull a bandit arm) and the environment responds with a stochastic outcome (reward) coming from an unknown distribution associated with the chosen arm. Bandits have a wide-range of application such as Web recommendation sys- tems. We address the cumulative reward maximization problem in a secure federated learning setting, where multiple data owners keep their data stored locally and collaborate under the coordination of a central orchestration server. We rely on cryptographic schemes and propose Samba, a generic framework for Secure federAted Multi-armed BAndits. Each data owner has data associated to a bandit arm and the bandit algorithm has to sequen- tially select which data owner is solicited at each time step. We instantiate Samba for five bandit algorithms. We show that Samba returns the same cumulative reward as the non- secure versions of bandit algorithms, while satisfying formally proven security properties. We also show that the overhead due to cryptographic primitives is linear in the size of the input, which is confirmed by our proof-of-concept implementation. © 2022 AI Access Foundation. All rights reserved.", "Keywords": "machine learning;reinforcement learning;distributed AI", "DOI": "10.1613/jair.1.13163", "PubYear": 2022, "Volume": "73", "Issue": "", "JournalId": 56494, "JournalTitle": "Journal of Artificial Intelligence Research", "ISSN": "", "EISSN": "1076-9757", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS LIG, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Univ. <PERSON>lermont Auvergne, CNRS LIMOS, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Univ. <PERSON>lermont Auvergne, CNRS LIMOS, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS LIG, France"}], "References": []}, {"ArticleId": 93198044, "Title": "Proteome-wide identification of non-histone lysine methylation in tomato during fruit ripening", "Abstract": "<b  >Introduction</b> Histone and non-histone methylations are important post-translational modifications in plants. Histone methylation plays a crucial role in regulating chromatin structure and gene expression. However, the involvement of non-histone methylation in plant biological processes remains largely unknown. <b  >Methods</b> The methylated substrates and methylation sites during tomato fruit ripening were identified by LC-MS/MS. Bioinformatics of lysine methylated proteins was conducted to analyze the possible role of methylated proteins. The effects of methylation modification on protein functions were preliminarily investigated by site-directed mutation simulation. <b  >Results</b> A total of 241 lysine methylation (mono-, di- and trimethylation) sites in 176 proteins were identified with two conserved methylation motifs: xxxxxxExxx_K_xxxExxxxxx and xxxxxxExxx_K_xxxxxxxxxx. These methylated proteins were mainly related to fruit ripening and senescence, oxidation reduction process, signal transduction, stimulus and stress responses, and energy metabolism. Three representative proteins, thioredoxin (Trx), glutathione S-transferase T1 (GST T1), and NADH dehydrogenase (NOX), were selected to investigate the effect of methylation modifications on protein activity. Mimicking demethylation led to decreased Trx activity but increased GST T1 and NOX activities. In addition, RT-qPCR exhibited that the expression of many genes that encode proteins subjected to methylation was upregulated during fruit ripening. <b  >Conclusion</b> Our study suggests that tomato fruit ripening undergo non-histone lysine methylation, which may participate in the regulation of fruit ripening. It is the first report of methyl proteome profiling of non-histone lysine in horticultural crops.", "Keywords": "Lysine;Methylome;Non-histone;Post-translational modification;Ripening;Tomato", "DOI": "10.1016/j.jare.2022.02.013", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Applied Botany, South China Botanical Garden, Chinese Academy of Sciences, Guangzhou 510650, China; Institute of Quality Standard and Monitoring Technology for Agro-products of Guangdong Academy of Agricultural Sciences, Guangzhou 510640, China; Key Laboratory of Testing and Evaluation for Agro-product Safety and Quality, Ministry of Agriculture and Rural Affairs, Guangzhou 510640, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing 100049, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Applied Botany, South China Botanical Garden, Chinese Academy of Sciences, Guangzhou 510650, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Applied Botany, South China Botanical Garden, Chinese Academy of Sciences, Guangzhou 510650, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Applied Botany, South China Botanical Garden, Chinese Academy of Sciences, Guangzhou 510650, China; Center of Economic Botany, Core Botanical Gardens, Chinese Academy of Sciences, Guangzhou 510650, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Agro-food Science and Technology Research Institute, Guangxi Academy of Agricultural Sciences, Nanning 530007, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Applied Botany, South China Botanical Garden, Chinese Academy of Sciences, Guangzhou 510650, China; Center of Economic Botany, Core Botanical Gardens, Chinese Academy of Sciences, Guangzhou 510650, China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Subtropical Silviculture, Zhejiang A&amp;F University, Lin'an 311300, Zhejiang Province, China. Electronic address:  ."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Applied Botany, South China Botanical Garden, Chinese Academy of Sciences, Guangzhou 510650, China; Center of Economic Botany, Core Botanical Gardens, Chinese Academy of Sciences, Guangzhou 510650, China; Agro-food Science and Technology Research Institute, Guangxi Academy of Agricultural Sciences, Nanning 530007, China. Electronic address:  ."}], "References": []}, {"ArticleId": 93198068, "Title": "TSR-VFD: Generating temporal super-resolution for unsteady vector field data", "Abstract": "We present TSR-VFD, a novel deep learning solution that recovers temporal super-resolution (TSR) of three-dimensional vector field data (VFD) for unsteady flow. In scientific visualization, TSR-VFD is the first work that leverages deep neural nets to interpolate intermediate vector fields from temporally sparsely sampled unsteady vector fields. The core of TSR-VFD lies in using two networks: InterpolationNet and MaskNet, that process the vector components of different scales from sampled vector fields as input and jointly output synthesized intermediate vector fields. To demonstrate our approach’s effectiveness, we report qualitative and quantitative results with several data sets and compare TSR-VFD against vector field interpolation using linear interpolation (LERP), generative adversarial network (GAN), and recurrent neural network (RNN). In addition, we compare TSR-VFD with a lossy compression (LC) scheme. Finally, we conduct a comprehensive study to evaluate critical parameter settings and network designs.", "Keywords": "Temporal super-resolution ; Unsteady vector field ; Data reconstruction ; Deep learning", "DOI": "10.1016/j.cag.2022.02.001", "PubYear": 2022, "Volume": "103", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Notre Dame, Notre Dame, IN, 46556, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Notre Dame, Notre Dame, IN, 46556, USA"}], "References": [{"Title": "Latent Space Subdivision: Stable and Controllable Time Predictions for Fluid Flow", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "8", "Page": "15", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 93198097, "Title": "A multi- unmanned aerial vehicle dynamic task assignment method based on bionic algorithms", "Abstract": "Because of the long calculation time in task allocation algorithms and the long travelled distance of the Unmanned Aerial Vehicle (UAV) during task allocation, a multi-UAV task allocation method based on three bionic algorithms, which are the ant colony algorithm, bat algorithm, and gray wolf algorithm was proposed in this paper. The Multi-UAV Dynamic Task Assignment Method Based on Bionic Algorithms (TABA) method dynamically allocates UAVs based on the number of task points and includes a comparison mechanism. The results of experiments demonstrate that, among the three algorithms, the proposed method has 30% improvement to the conventional methods, and the UAV travel time is about one-third that of the original algorithm on average, thereby revealing the effectively reduced algorithm complexity. Moreover, the algorithm can reduce the travel time of UAVs.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.107820", "PubYear": 2022, "Volume": "99", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing 100048, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing 100048, China;Corresponding author"}, {"AuthorId": 3, "Name": "Xiaofeng Lian", "Affiliation": "School of Artificial Intelligence, Beijing Technology and Business University, Beijing 100048, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing 100048, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing 100048, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Beijing University of Civil Engineering and Architecture, Beijing 100044, China"}], "References": [{"Title": "Memory Pattern Identification for Feedback Tracking Control in Human–Machine Systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "2", "Page": "210", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Multi-task allocation with an optimized quantum particle swarm method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106603", "JournalTitle": "Applied Soft Computing"}, {"Title": "Three-Dimensional Multi-Mission Planning of UAV Using Improved Ant Colony Optimization Algorithm Based on the Finite-Time Constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "79", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "A novel hybrid particle swarm optimization for multi-UAV cooperate path planning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "10", "Page": "7350", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 93198194, "Title": "Alert Now or Never: Understanding and Predicting Notification Preferences of Smartphone Users", "Abstract": "<p> Notifications are an indispensable feature of mobile devices, but their delivery can interrupt and distract users. Prior work has examined interventions, such as deferring notification delivery to opportune moments, but has not systematically studied how users might prefer an intelligent system to manage their notifications. Hence, we directly probed Android smartphone users’ notification preferences via a one-week experience-sampling study ( N = 35). We found that users prefer mitigating undesired interruptions by suppressing alerts over deferring them and referred to notification content factors more frequently than contextual factors for explaining their preferences. Then we demonstrated the challenges and potentials of leveraging user actions to help predict notification preferences. Specifically, we showed that a model personalized using user actions achieved a performance gain of 39% than a generic model. This improvement is similar to the 42% performance gain using labels solicited from the user while using observable user actions causes no extra disruption. </p>", "Keywords": "Notification; Interruptibility; Intelligent system; Smartphone; Android; Experience-sampling method", "DOI": "10.1145/3478868", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Google, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Google, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Google, USA"}], "References": []}, {"ArticleId": 93198231, "Title": "Automated Assessment in Computer Science Education: A State-of-the-Art Review", "Abstract": "<p>Practical programming competencies are critical to the success in computer science education and go-to-market of fresh graduates. Acquiring the required level of skills is a long journey of discovery, trial and error, and optimization seeking through a broad range of programming activities that learners must perform themselves. It is not reasonable to consider that teachers could evaluate all attempts that the average learner should develop multiplied by the number of students enrolled in a course, much less in a timely, deeply, and fairly fashion. Unsurprisingly, exploring the formal structure of programs to automate the assessment of certain features has long been a hot topic among CS education practitioners. Assessing a program is considerably more complex than asserting its functional correctness, as the proliferation of tools and techniques in the literature over the past decades indicates. Program efficiency, behavior, readability, among many other features, assessed either statically or dynamically, are now also relevant for automatic evaluation. The outcome of an evaluation evolved from the primordial boolean values to information about errors and tips on how to advance, possibly taking into account similar solutions. This work surveys the state-of-the-art in the automated assessment of CS assignments, focusing on the supported types of exercises, security measures adopted, testing techniques used, type of feedback produced, and the information they offer the teacher to understand and optimize learning. A new era of automated assessment, capitalizing on static analysis techniques and containerization, has been identified. Furthermore, this review presents several other findings from the conducted review, discusses the current challenges of the field, and proposes some future research directions.</p>", "Keywords": "automated assessment; computer science; programming; feedback; learning analytics", "DOI": "10.1145/3513140", "PubYear": 2022, "Volume": "22", "Issue": "3", "JournalId": 10672, "JournalTitle": "ACM Transactions on Computing Education", "ISSN": "", "EISSN": "1946-6226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CRACS - INESC TEC & DCC - FCUP, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CRACS - INESC TEC & DCC - FCUP, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CRACS - INESC TEC & DCC - FCUP, Portugal"}], "References": []}, {"ArticleId": 93198241, "Title": "Automatic Mapping of the Best-Suited DNN Pruning Schemes for Real-Time Mobile Acceleration", "Abstract": "Weight pruning is an effective model compression technique to tackle the challenges of achieving real-time deep neural network (DNN) inference on mobile devices. However, prior pruning schemes have limited application scenarios due to accuracy degradation, difficulty in leveraging hardware acceleration, and/or restriction on certain types of DNN layers. In this article, we propose a general, fine-grained structured pruning scheme and corresponding compiler optimizations that are applicable to any type of DNN layer while achieving high accuracy and hardware inference performance. With the flexibility of applying different pruning schemes to different layers enabled by our compiler optimizations, we further probe into the new problem of determining the best-suited pruning scheme considering the different acceleration and accuracy performance of various pruning schemes. Two pruning scheme mapping methods—one -search based and the other is rule based—are proposed to automatically derive the best-suited pruning regularity and block size for each layer of any given DNN. Experimental results demonstrate that our pruning scheme mapping methods, together with the general fine-grained structured pruning scheme, outperform the state-of-the-art DNN optimization framework with up to 2.48\n \n \\( \\times \\) \n \n and 1.73\n \n \\( \\times \\) \n \n DNN inference acceleration on CIFAR-10 and ImageNet datasets without accuracy loss.", "Keywords": "Network pruning; mobile acceleration; neural architecture search", "DOI": "10.1145/3495532", "PubYear": 2022, "Volume": "27", "Issue": "5", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, Boston, MA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Northeastern University, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of William and Mary, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, USA"}, {"AuthorId": 7, "Name": "Yuxuan <PERSON>", "Affiliation": "Northeastern University, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Michigan State University, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "College of William and Mary, USA"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, USA"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "University of Pittsburgh, USA"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, USA"}], "References": []}, {"ArticleId": 93198256, "Title": "AKAME: A post-quantum authenticated key-agreement and message encryption scheme based on ring-LWE", "Abstract": "<p>The continuous progress in development of quantum computer brings the threat of realization of <PERSON><PERSON>’s cryptanalysis algorithms, which in turn leads to find alternatives for current cryptosystems based on <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and El<PERSON> Curve <PERSON><PERSON><PERSON>. The existing post-quantum cryptographic primitiveshave longer key sizes and high computational time. This makes switching from pre-quantum to post-quantum cryptography complex in many applications. This paper presents an authenticated quantum secure key-agreement and message encryption (AKAME) scheme with optimal key-sizes and decryption time. The paper aims to provide a robust key-agreement algorithm between two entities without using any reconciliation mechanism. The underlying security of the scheme is based on the Ring-Learning with Error (RLWE) problem, a lattice-based hard problem. The proposed scheme is capable of providing both 2<sup>118</sup> or 2<sup>254</sup> classical security and 2<sup>102</sup> or 2<sup>241</sup> quantum security. Further, the paper presents comparative analysis between existing schemes and presented scheme.</p>", "Keywords": "Post quantum cryptography; Lattice based cryptography; Ring learning with error; Reconciliation; Key agreement", "DOI": "10.1007/s41870-022-00888-y", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, M.B.M. Engineering College, Faculty of Engineering & Architecture, Jai Narain Vyas University, Jodhpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, M.B.M. Engineering College, Faculty of Engineering & Architecture, Jai Narain Vyas University, Jodhpur, India"}], "References": [{"Title": "Lattice-based Key-sharing Schemes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Contextual fully homomorphic encryption schemes-based privacy preserving framework for securing fog-assisted healthcare data exchanging applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "4", "Page": "1545", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 93198260, "Title": "Implication of Optimizing NPU Dataflows on Neural Architecture Search for Mobile Devices", "Abstract": "Recent advances in deep learning have made it possible to implement artificial intelligence in mobile devices. Many studies have put a lot of effort into developing lightweight deep learning models optimized for mobile devices. To overcome the performance limitations of manually designed deep learning models, an automated search algorithm, called\n neural architecture search \n (\n NAS \n ), has been proposed. However, studies on the effect of hardware architecture of the mobile device on the performance of NAS have been less explored. In this article, we show the importance of optimizing a hardware architecture, namely, NPU dataflow, when searching for a more accurate yet fast deep learning model. To do so, we first implement an optimization framework, named FlowOptimizer, for generating a best possible NPU dataflow for a given deep learning operator. Then, we utilize this framework during the latency-aware NAS to find the model with the highest accuracy satisfying the latency constraint. As a result, we show that the searched model with FlowOptimizer outperforms the performance by 87.1% and 92.3% on average compared to the searched model with NVDLA and Eyeriss, respectively, with better accuracy on a proxy dataset. We also show that the searched model can be transferred to a larger model to classify a more complex image dataset, i.e., ImageNet, achieving 0.2%/5.4% higher Top-1/Top-5 accuracy compared to MobileNetV2-1.0 with 3.6\n \n \\( \\times \\) \n \n lower latency.", "Keywords": "Dataflow optimization; neural networks; neural architecture search; neural processing unit", "DOI": "10.1145/3513085", "PubYear": 2022, "Volume": "27", "Issue": "5", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Daegu Gyeongbuk Institute of Science and Technology (DGIST), Daegu, Korea"}, {"AuthorId": 2, "Name": "Junsang Park", "Affiliation": "Daegu Gyeongbuk Institute of Science and Technology (DGIST), Daegu, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Daegu Gyeongbuk Institute of Science and Technology (DGIST), Daegu, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Daegu Gyeongbuk Institute of Science and Technology (DGIST), Korea"}], "References": []}, {"ArticleId": 93198276, "Title": "EF-Train: Enable Efficient On-device CNN Training on FPGA through Data Reshaping for Online Adaptation or Personalization", "Abstract": "<p>Conventionally, DNN models are trained once in the cloud and deployed in edge devices such as cars, robots, or unmanned aerial vehicles (UAVs) for real-time inference. However, there are many cases that require the models to adapt to new environments, domains, or new users. In order to realize such domain adaption or personalization, the models on devices need to be continuously trained on the device. In this work, we design EF-Train, an efficient DNN training accelerator with a unified channel-level parallelism-based convolution kernel that can achieve end-to-end training on resource-limited low-power edge-level FPGAs. It is challenging to implement on-device training on resource-limited FPGAs due to the low efficiency caused by different memory access patterns among forward, backward propagation, and weight update. Therefore, we developed a data reshaping approach with intra-tile continuous memory allocation and weight reuse. An analytical model is established to automatically schedule computation and memory resources to achieve high energy efficiency on edge FPGAs. The experimental results show that our design achieves 46.99 GFLOPS and 6.09 GFLOPS/W in terms of throughput and energy efficiency, respectively.</p>", "Keywords": "On-device training; edge FPGAs; data reshaping", "DOI": "10.1145/3505633", "PubYear": 2022, "Volume": "27", "Issue": "5", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Pittsburgh, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Pittsburgh, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Pittsburgh, USA"}, {"AuthorId": 4, "Name": "Jingtong Hu", "Affiliation": "University of Pittsburgh, USA"}], "References": []}, {"ArticleId": 93198298, "Title": "Mutation and dynamic objective-based farmland fertility algorithm for workflow scheduling in the cloud", "Abstract": "Nowadays, many scientific applications are deployed in the cloud to execute at a lower cost. However, the growing scale of workflows makes scheduling problems challenging. To minimize the workflow execution cost under deadline constraints, this article proposes a Mutation and Dynamic Objective-based Farmland Fertility (MDO-FF) algorithm for obtaining a near-optimal solution within a relatively shorter time. A Dynamic Objective Strategy (DOS) is introduced to accelerate the convergence speed, while a multi-swarm evolutionary approach and mutation strategies are incorporated to enhance the search diversity and help to escape from local optima. By seeking new potential solutions and searching in its corresponding neighborhoods, our proposed MDO-FF can make a good trade-off between exploration and exploitation. Extensive experiments are conducted on well-known scientific workflows with different types and sizes. The experimental results demonstrate that in most cases, our MDO-FF outperforms the existing algorithms in terms of constraint satisfiability and solution quality.", "Keywords": "Cloud computing ; Workflow scheduling ; Deadline constraints ; Meta-heuristics", "DOI": "10.1016/j.jpdc.2022.02.005", "PubYear": 2022, "Volume": "164", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing 100081, China;@qq.com"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Tsinghua University, Beijing 100084, China"}], "References": [{"Title": "An efficient fault tolerant workflow scheduling approach using replication heuristics and checkpointing in the cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "14", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Developing accurate and scalable simulators of production workflow management systems with WRENCH", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "162", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Improved many-objective particle swarm optimization algorithm for scientific workflow scheduling in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106649", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "PSO+LOA: hybrid constrained optimization for scheduling scientific workflows in the cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "11", "Page": "13139", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 93198303, "Title": "A green dynamic TSP with detailed road gradient dependent fuel consumption estimation", "Abstract": "The rapid transition to the digitalization of firms directs companies to speed up their decision-making processes and make technology-based decisions. The increasing environmental concerns and fossil-fuel scarcity force companies to employ new logistical aims, such as reducing fuel consumption and greenhouse gas emission in addition to the traditional cost minimization and profit maximization objectives. Traditional assumptions in fuel consumption calculation have been left not only to reach the objectives of the company and make consistent delivery plans but also to increase the solution quality of real-life problems. From this point of view, this study contributes to the related literature by developing a decision support model for a Travelling Salesman Problem that considers dynamic customer requests and realistic road gradients of the entire road network while calculating fuel consumption from transportation operations. The applicability of the developed model has been shown with a real-life case study in which laboratory samples have been collected and transferred from local family clinics to a central laboratory. Several objective functions are employed to demonstrate the benefits of considering realistic road gradients. The results of the numerical analyses on static and dynamic instances illustrate the benefits of respecting realistic road gradients in fuel consumption and addressing dynamic requests during delivery operations.", "Keywords": "Sustainable urban logistics ; TSP ; Fuel estimation ; Road gradients", "DOI": "10.1016/j.cie.2022.108024", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Operations Management and Marketing Division, Department of Business Administration, Çankırı Karatekin University, Uluyazı Campus, 18100 Çankırı, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Operations Management Division, Department of Business Administration, Hacettepe University, Ankara, Turkey"}, {"AuthorId": 3, "Name": "Mine Ömürgönülşen", "Affiliation": "Operations Management Division, Department of Business Administration, Hacettepe University, Ankara, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Operations Research and Logistics Group, Wageningen University, Wageningen, the Netherlands"}], "References": [{"Title": "Time-dependent vehicle routing problem with time windows of city logistics with a congestion avoidance approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "104813", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": ********, "Title": "স<PERSON><PERSON><PERSON><PERSON><PERSON> - A Bengali Virtual Assistant", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022921946", "PubYear": 2022, "Volume": "183", "Issue": "53", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Object Detection in Artisanal Small-Scale Gold Mining Environments using a Modified YOLOv4 Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022921906", "PubYear": 2022, "Volume": "183", "Issue": "50", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93198380, "Title": "The Impact of Game Elements on Learner Motivation: Influence of Initial Motivation and Player Profile", "Abstract": "Several studies have been conducted in recent years into the effects of gamification on learner motivation. However, little is known about how learner profiles affect the impact of specific game elements. This article analyzes the effect of a gamified mathematic learning environment on the motivation and the motivated behaviors of 258 learners in secondary schools in France. Overall, results indicate that randomly assigned game elements generally demotivate learners. A more thorough analysis revealed that gamification has a positive impact on the most amotivated learners to do mathematic although different effects were observed on learners. In particular, we noticed significant influences of their initial level of motivation and their player type on the variation in motivation during the study. We show that these influences vary according to the game element they used. These findings suggest that to increase efficiency, gamification should be tailored not only to the player profile but also to their level of initial motivation for the learning task.", "Keywords": "Gamification;interactive learning environment;learner motivation;player types", "DOI": "10.1109/TLT.2022.3153239", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Lyon, Lyon, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Iaelyon School of Management, CNRS, LIRIS, University of Lyon, University Jean Moulin Lyon 3, Lyon, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INSA Lyon, 69261 CNRS, UCBL, LIRIS, UMR5205, INSA Lyon, University of Lyon, Villeurbanne, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universit&#x00E9; Sa<PERSON>ie <PERSON>, CNRS, LIRIS, University of Lyon, Chamb&#x00E9;ry, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Lyon, Lyon, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Iaelyon School of Management, CNRS, LIRIS, University of Lyon, University Jean Moulin Lyon 3, Lyon, France"}], "References": []}, {"ArticleId": 93198464, "Title": "An Errors Correction Model for the Errors of Non-word and Real-word in English Composition", "Abstract": "<p lang=\"zh\"><p><p>In the procedure from composing English, it is inevitable to face the phenomenon of word writing errors. In recent years, English composition automatic correcting system has attracted much attention. However, the precision of the existing word errors correcting system is vague generalization. So as to move forward the accuracy of checking and correcting word errors, this paper designs a word errors correction model based on natural language processing technology. This model designs phoneme matching method based on an improved IDM algorithm, and combined with a non-word input errors correction method based on character distance. The accuracy of correcting non-word errors in this model reached 86.5%. The study also proposes a real-word errors correction method, which is implemented basing on the real-word confusion set and combining the binary statistical model and the GloVe word vector model, improving the real-word errors correction method based on feature annotation of the real-word confusion set, with an accuracy of 77.9%. </p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022023301013", "PubYear": 2022, "Volume": "33", "Issue": "1", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93198497, "Title": "Blockchain Use Cases Against Climate Destruction", "Abstract": "<p>Based on the current measures, it is unlikely that the targets of the Paris Agreement on climate change are to be achieved within the given time. Therefore, new solutions are needed to get climate change under control. Emerging technologies like blockchain allow for new ways to approach climate change. The blockchain serves only as an enabling technology for cryptocurrencies but is a stand-alone tool applicable for various purposes. This paper aims to shed light on the overlap between the areas of blockchain and climate change. Research in this area was examined for potential blockchain use cases to support climate action using a systematic literature review. The found applications can be grouped into the main categories of Emissions Trading and Green Certificates, Sustainable Energy, Sustainable Mobility, and Green Financing. Within these applications, blockchains are being used as supporting technology. Especially transparency, traceability, and immutability are particularly beneficial in blockchain-based applications against climate change. As a downside of the technology, controversial aspects of the blockchain are considered as the energy consumption of the technology.</p>", "Keywords": "blockchain application; climate change; use cases", "DOI": "10.37256/ccds.3220221277", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 78207, "JournalTitle": "Cloud Computing and Data Science", "ISSN": "2737-4106", "EISSN": "2737-4092", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Management, Communication & IT, Management Center Innsbruck, Innsbruck, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Management, Communication & IT, Management Center Innsbruck, Innsbruck, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Management, Communication & IT, Management Center Innsbruck, Innsbruck, Austria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Management, Communication & IT, Management Center Innsbruck, Innsbruck, Austria"}], "References": []}, {"ArticleId": 93198505, "Title": "Robust energy-based model updating framework for random processes in dynamics: Application to shaking-table experiments", "Abstract": "This paper presents a robust model updating strategy for correcting finite element models from datasets acquired in low-frequency dynamics. The proposed methodology is based on the minimization of a modified Constitutive Relation Error (mCRE) made of two terms: (i) a Hermitian data-to-model distance written in the frequency domain enriched with (ii) a CRE residual accounting for model bias with strong mechanical content. An automated L-curve based methodology is derived for tuning the relative weight of the two terms and improving the algorithm robustness to noise level. An extended formulation of the mCRE in terms of Power Spectral Density is also proposed: a data windowing preprocessing step ensures statistical consistency of the updated parameters when dealing with noisy random processes. The methodology is applied to two earthquake engineering examples. The performances of the methodology are assessed using synthetic measurements from a plane frame subjected to random ground acceleration. Actual measurements from the SMART2013 database are next processed to observe the eigenfrequency drop of a reinforced-concrete structure submitted to a sequence of gradually damaging shaking-table tests. In this last application, the corrected model predictions are in good correlation with former data-driven subspace-based identification results.", "Keywords": "Model updating ; Low-frequency dynamics ; Modified Constitutive Relation Error (mCRE) ; Low signal-to-noise ratio measurements ; Earthquake engineering ; SMART2013 shaking-table tests", "DOI": "10.1016/j.compstruc.2022.106746", "PubYear": 2022, "Volume": "264", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, ENS Paris-Saclay, CNRS, LMT - Laboratoire de Mécanique et Technologie, 91190 Gif-sur-Yvette, France;DES-Service d’Études Mécaniques et Thermiques (SEMT), CEA, Université Paris-Saclay, 91191 Gif-sur-Yvette, France;Corresponding author at: Université Paris-Saclay, CentraleSupélec, ENS Paris-Saclay, LMPS - Laboratoire de Mécanique Paris-Saclay, 91190, Gif-sur-Yvette, France"}, {"AuthorId": 2, "Name": "P.<PERSON><PERSON><PERSON>", "Affiliation": "DES-Service d’Études Mécaniques et Thermiques (SEMT), CEA, Université Paris-Saclay, 91191 Gif-sur-Yvette, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, ENS Paris-Saclay, CNRS, LMT - Laboratoire de Mécanique et Technologie, 91190 Gif-sur-Yvette, France;Institut Universitaire de France (IUF), 1 rue Descartes, 75231 Paris CEDEX 5, France"}], "References": []}, {"ArticleId": 93198537, "Title": "Trajectory Planing for Cooperating Unmanned Aerial Vehicles in the IoT", "Abstract": "<p>The use of Unmanned Aerial Vehicles (UAVs) in data transport has attracted a lot of attention and applications, as a modern traffic engineering technique used in data sensing, transport, and delivery to where infrastructure is available for its interpretation. Due to UAVs’ constraints such as limited power lifetime, it has been necessary to assist them with ground sensors to gather local data, which has to be transferred to UAVs upon visiting the sensors. The management of such ground sensor communication together with a team of flying UAVs constitutes an interesting data muling problem, which still deserves to be addressed and investigated. This paper revisits the issue of traffic engineering in Internet-of-Things (IoT) settings, to assess the relevance of using UAVs for the persistent collection of sensor readings from the sensor nodes located in an environment and their delivery to base stations where further processing is performed. We propose a persistent path planning and UAV allocation model, where a team of heterogeneous UAVs coming from various base stations are used to collect data from ground sensors and deliver the collected information to their closest base stations. This problem is mathematically formalised as a real-time constrained optimisation model, and proven to be NP-hard. The paper proposes a heuristic solution to the problem and evaluates its relative efficiency through performing experiments on both artificial and real sensors networks, using various scenarios of UAVs settings.</p>", "Keywords": "real-time visitation; cooperative UAVs; path planning; clustered network real-time visitation ; cooperative UAVs ; path planning ; clustered network", "DOI": "10.3390/iot3010010", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 58626, "JournalTitle": "IoT", "ISSN": "", "EISSN": "2624-831X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Knowledge and Information Stewardship, University of Cape Town, Cape Town 7701, South Africa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of the Western Cape, Cape Town 7535, South Africa"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Communication Networks and Security Research Laboratory, Cartage University, Tunis 1054, Tunisia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Communication Networks and Security Research Laboratory, Cartage University, Tunis 1054, Tunisia"}], "References": []}, {"ArticleId": 93198558, "Title": "Infrared Image Segmentation for Power Equipment Using Linear Spectral Clustering and Maximal Similarity-based Region Merging", "Abstract": "<p lang=\"zh\"><p><p>The diagnostic method of power equipment based on infrared images is widely used because it has the advantages of non-contact and does not affect the online operation of power equipment. However, in actual using, the power equipment diagnosis method based on infrared image still relies on manual judgment, that is, the detection personnel can judge the fault according to the obtained infrared image of power equipment by experience. This process consumes a lot of time, and the subjectivity is strong, misjudgment rate is higher, which cannot meet the requirements of modern smart grid development. Infrared image of power equipment contains a lot of noise, and the edge is fuzzy. In this paper, we propose a new infrared image segmentation method for power equipment by using linear spectral clustering and maximal similarity-based region merging under complex backgrounds. In this method, the linear spectral clustering algorithm (LSC) is used to segment the image into super-pixels, and the pixels with similar color and distance are clustered to the same center. The calculated OTSU threshold based on the global image is used to pre-label the background of each super-pixel block. The maximum similarity-based region merging algorithm (MSRM) is utilized to merge the super-pixel blocks. Meanwhile, it obtains the target equipment, the over-segmentation and under-segmentation rates are reduced effectively. Finally, the mathematical morphology algorithm is used to post-process the image. Experimental results show that, compared with other algorithms, this new method can obtain more accurate and complete target equipment under complex background. </p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022023301005", "PubYear": 2022, "Volume": "33", "Issue": "1", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93198612, "Title": "COVID-19 Virus Prediction Using CNN and Logistic Regression Classification Strategies", "Abstract": "COVID-19 virus is certainly considered as one of the harmful viruses amongst all the illnesses in biological science. COVID-19 symptoms are fever, cough, sore throat, and headache. The paper gave a singular function for the prediction of most of the COVID-19 virus diseases and presented with the Convolutional Neural Networks and Logistic Regression which might be the supervised learning and gaining knowledge of strategies for most of COVID-19 virus diseases detection. The proposed system makes use of an 8-fold pass determination to get a correct result. The COVID-19 virus analysis dataset is taken from Microsoft Database, Kaggle, and UCI websites gaining knowledge of the repository. The proposed studies investigate Convolutional Neural Networks (CNN) and Logistic Regression (LR) about the usage of the UCI database, Kaggle, and Google Database Datasets. This paper proposed a hybrid method for COVID-19 virus, most disease analyses through reducing the dimensionality of capabilities the usage of Logistic Regression (LR), after which making use of the brand new decreased function dataset to Convolutional Neural Networks and Logistic regression. The proposed method received the accuracy of 78.82%, sensitiveness of 97.41%, and specialness of 98.73%. The overall performance of the proposed system is appraised thinking about performance, accuracy, error rate, sensitiveness, particularity, correlation and coefficient. The proposed strategies achieved the accuracy of 78.82% and 97.41% respectively through Convolutional Neural Networks and Logistic Regression.", "Keywords": "Machine Learning;COVID-19 Virus;Deep Learning;ANN;CNN and LR", "DOI": "10.4236/jdaip.2022.101005", "PubYear": 2022, "Volume": "10", "Issue": "1", "JournalId": 27493, "JournalTitle": "Journal of Data Analysis and Information Processing", "ISSN": "2327-7211", "EISSN": "2327-7203", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Data Science Research Laboratory, BlueCrest University, Monrovia, Liberia ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Data Science Research Laboratory, BlueCrest University, Monrovia, Liberia ."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data Science Research Laboratory, BlueCrest University, Monrovia, Liberia ."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Data Science Research Laboratory, BlueCrest University, Monrovia, Liberia ."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data Science Research Laboratory, BlueCrest University, Monrovia, Liberia ."}], "References": []}, {"ArticleId": 93198849, "Title": "Optimized integration of metabolomics and lipidomics reveals brain region-specific changes of oxidative stress and neuroinflammation in type 1 diabetic mice with cognitive decline", "Abstract": "<b  >Introduction</b> Type 1 diabetes (T1D) causes cognitive decline and has been associated with brain metabolic disorders, but its potential molecular mechanisms remain unclear. <b  >Objectives</b> The purpose of this study was to explore the molecular mechanisms underlying T1D-induced cognitive impairment using metabolomics and lipidomics. <b  >Methods</b> We developed an optimized integration approach of metabolomics and lipidomics for brain tissue based on UPLC-Q-TOF-MS and analyzed a comprehensive characterization of metabolite and lipid profiles in the hippocampus and frontal cortex of T1D male mice with cognitive decline (T1DCD) and age-matched control (CONT) mice. <b  >Results</b> The results show that T1DCD mice had brain metabolic disorders in a region-specific manner relative to CONT mice, and the frontal cortex exhibited a higher lipid peroxidation than the hippocampus in T1DCD mice. Based on metabolic changes, we found that microglia was activated under diabetic condition and thereby promoted oxidative stress and neuroinflammation, leading to neuronal injury, and this event was more pronounced in the frontal cortex than the hippocampus. <b  >Conclusion</b> Our results suggest that brain region-specific shifts in oxidative stress and neuroinflammation may contribute to diabetic cognitive decline, and the frontal cortex could be the more vulnerable brain region than the hippocampus.", "Keywords": "Brain;Cognition;Diabetes;Lipidomics;Metabolomics;Neuroinflammation", "DOI": "10.1016/j.jare.2022.02.011", "PubYear": 2023, "Volume": "43", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON> He", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 8, "Name": "Wenqing Li", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China."}, {"AuthorId": 11, "Name": "Hongchang Gao", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China. Electronic address:  ."}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Institute of Metabonomics &amp; Medical NMR, School of Pharmaceutical Sciences, Wenzhou Medical University, Wenzhou 325035, China. Electronic address:  ."}], "References": []}, {"ArticleId": 93198887, "Title": "SKG-Learning: a deep learning model for sentiment knowledge graph construction in social networks", "Abstract": "<p>Traditional sentiment analysis methods pay little attention to the inseparable relations between evaluation words and evaluation aspects, and the relations between evaluation words and topics. There have been many studies of knowledge graph (KG), which can effectively store and manage massive amounts of information and is suitable to associate emotion words with evaluation aspects and topics. This study proposes SKG-Learning based on a deep learning model to construct a sentiment knowledge graph (SKG) for sentiment analysis. Entity and relation are the cornerstones of SKG; thus, the task of SKG-Learning is divided into named entity recognition and relation extraction. We propose a bidirectional long short-term memory model (Bi-LSTM) with background knowledge embedding and co-extraction of features (BBC-LSTM) to extract entities. BBC-LSTM completes the embedding of background knowledge such as topic and emotion information and uses three-dimensional tensors to co-extract the deep features of aspect entities and sentiment entities. It solves the problems that it is difficult to recognize entities from insufficient context, and traditional models usually neglect the relevance between sentiment entities and aspect entities. A relation extraction model based on an encoder–decoder model (ED-Learning) is proposed to extract and classify the relation between sentiment and aspect entity, that is, the emotional tendency of sentiment entity toward aspect entity. Experiments show that the proposed methods can more efficiently extract entities and relations from social network texts. We confirm the validity of an SKG constructed by the SKG-Learning model in an emotional analysis task.</p>", "Keywords": "Sentiment analysis; Sentiment knowledge graph; Named entity recognition; Relation extraction", "DOI": "10.1007/s00521-022-07028-4", "PubYear": 2022, "Volume": "34", "Issue": "13", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China; Institute of Artificial Intelligence on Education, Shanghai Normal University, Shanghai, China; Shanghai Engineering Research Center of Intelligent Education and Bigdata, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China; Department of Electronic and Electrical Engineering, Brunel University London, Uxbridge, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}], "References": [{"Title": "Knowledge graph fusion for smart systems: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "56", "JournalTitle": "Information Fusion"}, {"Title": "Multi-way matching based fine-grained sentiment analysis for user reviews", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "5409", "JournalTitle": "Neural Computing and Applications"}, {"Title": "SK-GCN: Modeling Syntax and Knowledge via Graph Convolutional Network for aspect-level sentiment classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106292", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "ABCDM: An Attention-based Bidirectional CNN-RNN Deep Model for sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "279", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 93198902, "Title": "Towards visual programming abstractions in Software‐Defined Networking", "Abstract": "<p>Since Software-Defined Networking (SDN) emerged, the research community and industry have developed numerous projects and fostered novel use cases. However, engineers now need to learn how to program the control and data planes, which might slow down technology acceptance. To accelerate it, visual programming abstractions facilitate the incorporation of SDN technologies and assist in creating new applications. So far, very little effort has been made in this field. This letter presents an early-stage SDN visual abstraction initiative based on the Scratch/Blockly programming framework, initially aimed at kids. The objective is to illustrate how this work could be extended to provide value-added resources for network programming.</p>", "Keywords": "blockly;human-defined networking;network programmability;scratch;Software-Defined Networking;softwarized networks;visual programming", "DOI": "10.1002/itl2.358", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad de Alcalá, Dpto. de Automatica  Alcalá de Henares Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "DTU Fotonik, Technical University of Denmark  Kongens Lyngby Denmark"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad de Alcalá, Dpto. de Automatica  Alcalá de Henares Spain"}], "References": [{"Title": "Scalability, Consistency, Reliability and Security in SDN Controllers: A Survey of Diverse SDN Controllers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "The Programmable Data Plane", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 93198912, "Title": "Exploring an AI-based writing Assistant's impact on English language learners", "Abstract": "The increasing use of English as a Lingua Franca (ELF) worldwide has brought attention to tools that can assist English as a Foreign Language (EFL) learners in their journey to fluency. Much research has shown that EFL learners often do not have sufficient latitude to output at a satisfactory level when writing in a second language. In addition, cognitive (working memory) resources are spent on low-level writing tasks (word production, translation) at the expense of time being allocated to higher-level writing tasks such as organization and revision. The researcher&#x27;s laboratory developed an AI-based web application called “AI KAKU” to assist EFL learners in reducing the cognitive barriers they face when producing written text in English. While there has been much research and discussion on Automated Writing Evaluation (AWE) technologies or older technologies such as spell check and grammar check, few studies have attempted to use AI-based tools as learning instruments outside assessments. This study recruited adult EFL participants in a counter-balanced experiment to evaluate the potential impact of AI KAKU on student writing. Preliminary results indicate that this is a potentially useful tool for English language learners who need more structured assistance than traditional word processors.", "Keywords": "CALL ; AI in education ; L2 writing ; Cognitive load ; AI agent", "DOI": "10.1016/j.caeai.2022.100055", "PubYear": 2022, "Volume": "3", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Global Engineering for Development, Environment and Society, Department of Transdisciplinary Science and Engineering, School of Environment and Society, Tokyo Institute of Technology, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Global Engineering for Development, Environment and Society, Department of Transdisciplinary Science and Engineering, School of Environment and Society, Tokyo Institute of Technology, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "L.A. English Academy, Himeji, Japan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Global Engineering for Development, Environment and Society, Department of Transdisciplinary Science and Engineering, School of Environment and Society, Tokyo Institute of Technology, Tokyo, Japan;Corresponding author"}], "References": [{"Title": "Examining the impact of Grammarly on the quality of mobile L2 writing", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "2", "Page": "74", "JournalTitle": "The JALT CALL Journal"}]}, {"ArticleId": 93199083, "Title": "Fuzzy-enhanced robust fault-tolerant control of IFOC motor with matched and mismatched disturbances", "Abstract": "<p xml:lang=\"fr\"><p style='text-indent:20px;'>This paper focuses on the dynamical analysis of the permanent magnet asynchronous motor with the aim of subsequently designing effective robust control laws for the indirect field-oriented control (IFOC) devices. We first perform some tasks which demonstrate the existence of chaos phenomenon in the IFOC using relevant indicators such as phase portraits, bifurcations diagrams and <PERSON><PERSON><PERSON><PERSON> exponents. Chaotic signature and some striking transitions are revealed such as period-doubling, torus, period-adding and chaos when an accessible parameter of the IFOC motor is changed. More interestingly, a certain range of the parameter space corresponds to the transient chaos. This behavior was not reported previously and can be considered as an enriching contribution. Secondly, due to the great interest to reduce the upper bound of uncertainties and interference, conventional sliding mode control (SMC) has been abundantly investigated for fault-tolerant control (FTC) systems. However, this approach presents several drawbacks in terms of overshoot, less robustness, transient state error, large chattering and speed of convergence that limit its use for industrial applications. For these reasons, the integral sliding mode control (ISMC) and the fuzzy sliding mode control (FISMC) are proposed to keep the IFOC motor in the regular operation zone. The optimal feedback gains and a sufficient condition are proposed for the stability of the overall IFOC system is drawn based on the linear quadratic regulator (LQR) method. To highlight the effectiveness and applicability of the proposed control scheme, numerical simulation results are presented. This analysis allows us a great knowledge of engineers for interpreting the operation of the IFOC motor. To highlight the effectiveness and the applicability of the proposed control scheme, numerical simulations results are presented and clearly demonstrated the feasibility of these techniques.</p></p>", "Keywords": "Disturbances;fuzzy sliding mode control;indirect field-oriented control;integral sliding mode control;linear quadratic regulator;robust control", "DOI": "10.3934/mfc.2022006", "PubYear": 2022, "Volume": "5", "Issue": "4", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Concurrent PI Controller Design for Indirect Vector Controlled Induction Motor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "1", "Page": "130", "JournalTitle": "Asian Journal of Control"}, {"Title": "Polynomial robust observer implementation based passive synchronization of nonlinear fractional-order systems with structural disturbances", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "9", "Page": "1369", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": 93199137, "Title": "Computational Argumentation for Medical Device Regulatory Classification", "Abstract": "<p>This work implements argumentation as the basis for modeling the relevant EU legislation concerning medical devices classification. Stakeholders can consult a web application for determining the risk-based class of a medical device based on the relevant legislation. The described approach is generally applicable to any other analogous cases of decision-making based on legislative regulations. One of the main advantages of using argumentation is the explainability and the high modularity of software permitting the extension and/or modification of the code when new relevant regulations become available.</p>", "Keywords": "Medical devices regulation; classification; argumentation; Gorgias-B", "DOI": "10.1142/S0218213022500051", "PubYear": 2022, "Volume": "31", "Issue": "1", "JournalId": 18388, "JournalTitle": "International Journal on Artificial Intelligence Tools", "ISSN": "0218-2130", "EISSN": "1793-6349", "Authors": [{"AuthorId": 1, "Name": "Sofia Almpani", "Affiliation": "School of Electrical and Computer Engineering, National Technical University of Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Public and One Health, University of Thessaly, Karditsa, Thessaly, Greece;School of Business, University of Nicosia, Nicosia, Cyprus"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Applied Mathematical and Physical Sciences, National Technical University of Athens, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, National Technical University of Athens, Greece"}], "References": []}, {"ArticleId": 93199139, "Title": "Quantum computing challenges in the software industry. A fuzzy AHP-based approach", "Abstract": "<b  >Context</b> The current technology revolution has posed unexpected challenges for the software industry. In recent years, the field of quantum computing (QC) technologies has continued to grow in influence and maturity, and it is now poised to revolutionise software engineering. However, the evaluation and prioritisation of QC challenges in the software industry remain unexplored, relatively under-identified and fragmented. <b  >Objective</b> The purpose of this study is to identify, examine and prioritise the most critical challenges in the software industry by implementing a fuzzy analytic hierarchy process (F-AHP). <b  >Method</b> First, to identify the key challenges, we conducted a systematic literature review by drawing data from the four relevant digital libraries and supplementing these efforts with a forward and backward snowballing search. Second, we followed the F-AHP approach to evaluate and rank the identified challenges, or barriers. <b  >Results</b> The results show that the key barriers to QC adoption are the lack of technical expertise, information accuracy and organisational interest in adopting the new process. Another critical barrier is the lack of standards of secure communication techniques for implementing QC. <b  >Conclusion</b> By applying F-AHP, we identified institutional barriers as the highest and organisational barriers as the second highest global weight ranked categories among the main QC challenges facing the software industry. We observed that the highest-ranked local barriers facing the software technology industry are the lack of resources for design and initiative while the lack of organisational interest in adopting the new process is the most significant organisational barrier. Our findings, which entail implications for both academicians and practitioners, reveal the emergent nature of QC research and the increasing need for interdisciplinary research to address the identified challenges.", "Keywords": "Fuzzy analytic hierarchy process (F-AHP) ; Software process automation ; Multiple-criteria decision-making (MCDM) ; Quantum software requirement ; Quantum computing", "DOI": "10.1016/j.infsof.2022.106896", "PubYear": 2022, "Volume": "147", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "LUT School of Engineering Science, Lappeenranta-Lahti University of Technology (LUT), Finland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LUT School of Engineering Science, Lappeenranta-Lahti University of Technology (LUT), Finland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Turku School of Economics, University of Turku, Finland 20500"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Optentia Research Focus Area, North-West University, Vanderbijlpark, South Africa;Department of Management, School of Business & Law, University of Agder, Norway;Norwegian School of Hotel Management, University of Stavanger, Stavanger, Norway"}], "References": [{"Title": "State-of-the-art quantum computing simulators: Features, optimizations, and improvements for D-GM", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "393", "Issue": "", "Page": "223", "JournalTitle": "Neurocomputing"}, {"Title": "Quantum computing based hybrid solution strategies for large-scale discrete-continuous optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "106630", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "A systematic literature review on semantic web enabled software testing", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "110485", "JournalTitle": "Journal of Systems and Software"}, {"Title": "An integration of a QFD model with Fuzzy-ANP approach for determining the importance weights for engineering characteristics of the proposed wheelchair design", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106136", "JournalTitle": "Applied Soft Computing"}, {"Title": "On the performance of hybrid search strategies for systematic literature reviews in software engineering", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "106294", "JournalTitle": "Information and Software Technology"}, {"Title": "Guidelines for the search strategy to update systematic literature reviews in software engineering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "127", "Issue": "", "Page": "106366", "JournalTitle": "Information and Software Technology"}, {"Title": "A fuzzy analytical hierarchy process to prioritize the success factors of requirement change management in global software development", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "e2292", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "Quantum Computing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "3", "Page": "12", "JournalTitle": "ACM SIGSOFT Software Engineering Notes"}, {"Title": "Quantum Algorithms for Feedforward Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Quantum Computing"}, {"Title": "Quantum computing assisted deep learning for fault detection and diagnosis in industrial process systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "107119", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Software modernization to embrace quantum technology", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "102933", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Agile trends in Chinese global software development industry: Fuzzy AHP based conceptual mapping", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107090", "JournalTitle": "Applied Soft Computing"}, {"Title": "Methods for accelerating geospatial data processing using quantum computers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "1", "JournalTitle": "Quantum Machine Intelligence"}, {"Title": "Quantum computing: A taxonomy, systematic review and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "66", "JournalTitle": "Software: Practice and Experience"}]}, {"ArticleId": 93199172, "Title": "Meta-search based approach for Arabic information retrieval", "Abstract": "The semantic relations between Arabic word representations were recognized and widely studied in theoretical studies in linguistics many centuries ago. Nonetheless, most of the previous research in automatic information retrieval (IR) focused on stem or root-based indexing, while lemmas and patterns are under-exploited. However, the authors believe that each of the four morphological levels encapsulates a part of the meaning of words. That is, the purpose is to aggregate these levels using more sophisticated approaches to reach the optimal combination which enhances IR.,The authors first compare the state-of-the art Arabic natural language processing (NLP) tools in IR. This allows to select the most accurate tool in each representation level i.e. developing four basic IR systems. Then, the authors compare two rank aggregation approaches which combine the results of these systems. The first approach is based on linear combination, while the second exploits classification-based meta-search.,Combining different word representation levels, consistently and significantly enhances IR results. The proposed classification-based approach outperforms linear combination and all the basic systems.,The work stands by a standard experimental comparative study which assesses several NLP tools and combining approaches on different test collections and IR models. Thus, it may be helpful for future research works to choose the most suitable tools and develop more sophisticated methods for handling the complexity of Arabic language.,The originality of the idea is to consider that the richness of Arabic is an exploitable characteristic and no more a challenging limit. Thus, the authors combine 4 different morphological levels for the first time in Arabic IR. This approach widely overtook previous research results.,The peer review history for this article is available at: https://publons.com/publon/10.1108/OIR-11-2020-0515\" class=\"intent_external_link text-link ExtLink\" rel=\"noopener noreferrer nofollow\" target=\"_blank\">https://publons.com/publon/10.1108/OIR-11-2020-0515 </p>", "Keywords": "Arabic word morphology;Arabic information retrieval;Meta-search;Linear combination;SVM classification", "DOI": "10.1108/OIR-11-2020-0515", "PubYear": 2022, "Volume": "46", "Issue": "7", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Sciences Department, National School of Computer Sciences, Manouba, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory of Computer Science for Industrial Systems, Carthage University, Manouba, Tunisia; Joint Group for Artificial Reasoning and Information Retrieval, Manouba, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Computer Science for Industrial Systems, Carthage University, Manouba, Tunisia; Joint Group for Artificial Reasoning and Information Retrieval, Manouba, Tunisia; Higher Institute of Multimedia Arts of Manouba (ISAMM), Manouba University, Manouba, Tunisia"}], "References": []}, {"ArticleId": 93199195, "Title": "Artificial Intelligence Effectiveness and Impact within COVID-19", "Abstract": "Artificial intelligence is one of the most important programs that are used in all sectors of society. Accordingly, artificial intelligence has become required to help combat Covid 19. In the field of education, artificial intelligence has become the ideal alternative to study and complementary to distance study. This research provides a summary of the types of artificial intelligence, its benefits, drawbacks, and the field of its uses. This paper will present the importance of using intelligence Artificial intelligence in the field of education specifically and its importance in expanding students’ awareness in the case of distance study.", "Keywords": "", "DOI": "10.1051/itmconf/20224201008", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "Ragad M <PERSON>", "Affiliation": "Department of Information Technology, AlBuraimi University College, Buraimi, Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, AlBuraimi University College, Buraimi, Oman"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, AlBuraimi University College, Buraimi, Oman"}], "References": [{"Title": "A review of mathematical modeling, artificial intelligence and datasets used in the study, prediction and management of COVID-19", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "11", "Page": "3913", "JournalTitle": "Applied Intelligence"}, {"Title": "Impact of Technologies During COVID-19 Pandemic for Improving Behavior Intention to Use E-learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "1", "Page": "184", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "A Combined Model for Continuous Intention to Use E-Learning System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "3", "Page": "113", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "Impact of Technologies During the COVID-19 Pandemic for Improving Behavioral Intention to Use E-Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "3", "Page": "137", "JournalTitle": "International Journal of Information and Communication Technology Education"}, {"Title": "Aligning and Assessing Teaching Approaches With SOLO Taxonomy in a Computer Programming Course", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "4", "Page": "1", "JournalTitle": "International Journal of Information and Communication Technology Education"}, {"Title": "A Review of Virtual Reality Applications in an Educational Domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "22", "Page": "99", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}]}, {"ArticleId": 93199229, "Title": "Weather Classification Model Performance: Using CNN, Keras-Tensor Flow", "Abstract": "Nowadays, automation is at its peak. The article provides a base to examine the weather through the machine without human intervention. This study offers a thorough classification model to forecast a weather type. Here, the model facilitates defining the best results for the weather prediction model to any climatic zones and categorizes the climate into four types: cloud, rain, shine, and sunrise. This model designs and reveals using convolution neural networks (CNN) algorithms with Keras framework and TensorFlow library. For practical implementations, use the images dataset available from the kaggle.com website. As a result, this research presents the performance of the designed and developed model. It shows accuracy, validation accuracy, losses, and validation losses approximately 94%, 92%, 18%, and 22%, respectively.", "Keywords": "Climate;Convolution Neural Network (CNN);Keras;TensorFlow;Weather Classification Model.", "DOI": "10.1051/itmconf/***********", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Engineering & Computer Science, Lebanese French University, Erbil, Kurdistan, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Accounting, College of Administrative and Financial Science, Knowledge University, Erbil, Kurdistan, Iraq"}], "References": [{"Title": "A comparative study of prediction and classification models on NCDC weather data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "5", "Page": "414", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": ********, "Title": "The development trend of China’s aging population: a forecast perspective", "Abstract": "To accurately predict the aging population in China, a novel grey prediction model (CFODGMW(1,1, $$\\alpha $$ \n α \n ) model) is established in this study. The CFODGMW(1,1, $$\\alpha $$ \n α \n ) model has all the advantages of the weighted least square method, combined fractional-order accumulation generation operation and grey prediction model with time power term, which makes it have excellent prediction performance. Compared with the traditional grey prediction model based on the least square method and the first-order accumulation operation, the CFODGMW(1,1, $$\\alpha $$ \n α \n ) model has stronger adaptability. The proposed model and its competing models are used to analyze the aging population in five regions of China. The results show that the prediction performance of the CFODGMW(1,1, $$\\alpha $$ \n α \n ) model is better than other models. Based on this, the CFODGMW(1,1, $$\\alpha $$ \n α \n ) model is used to predict the aging population in China in the next 4 years, and some suggestions are given based on the development trend of the aging population.", "Keywords": "Aging population; The weighted least square method; Grey prediction model; The whale optimizer; The combined fractional-order accumulation generation operation", "DOI": "10.1007/s40747-022-00685-x", "PubYear": 2022, "Volume": "8", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Network Crime Investigation of Hunan Provincial Colleges, Hunan Police Academy, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Public administration, Xiangtan University, Xiangtan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Public administration, Xiangtan University, Xiangtan, China"}], "References": [{"Title": "Application of a novel grey forecasting model with time power term to predict China's GDP", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "3", "Page": "343", "JournalTitle": "Grey Systems: Theory and Application"}, {"Title": "Predicting Chinese total retail sales of consumer goods by employing an extended discrete grey polynomial model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "104261", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 93199288, "Title": "Eigenvalue-based entropy and spectrum of bipartite digraph", "Abstract": "Graph entropy is an important measure of the evolution and complexity of networks. Bipartite graph is a special network and an important mathematical model for system resource allocation and management. In reality, a network system usually has obvious directionality. The direction of the network, or the movement trend of the network, can be described with spectrum index. However, little research has been done on the eigenvalue-based entropy of directed bipartite network. In this study, based on the adjacency matrix, the in-degree Laplacian matrix and the in-degree signless Laplacian matrix of directed bipartite graph, we defined the eigenvalue-based entropy for the directed bipartite network. Using the eigenvalue-based entropy, we described the evolution law of the directed bipartite network structure. Aiming at the direction and bipartite feature of the directed bipartite network, we improved the generation algorithm of the undirected network. We then constructed the directed bipartite nearest-neighbor coupling network, directed bipartite small-world network, directed bipartite scale-free network, and directed bipartite random network. In the proposed model, spectrum of those directed bipartite network is used to describe the directionality and bipartite property. Moreover, eigenvalue-based entropy is empirically studied on a real-world directed movie recommendation network, in which the law of eigenvalue-base entropy is observed. That is, if eigenvalue-based entropy value of the recommendation system is large, the evolution of movie recommendation network becomes orderless. While if eigenvalue-based entropy value is small, the structural evolution of the movie recommendation network tends to be regular. The simulation experiment shows that eigenvalue-based entropy value in the real directed bipartite network is between the values of a directed bipartite small world and a scale-free network. It shows that the real directed bipartite network has the structural property of the two typical directed bipartite networks. The coexistence of the small-world phenomena and the scale-free phenomena in the real network is consistent with the evolution law of typical network models. The experimental results show that the validity and rationality of the definition of eigenvalue-based entropy, which serves as a tool in the analysis of directed bipartite networks.", "Keywords": "Directed bipartite complex networks; Spectrum; Eigenvalue-based entropy; In-degree Laplacian matrix; In-degree signless Laplacian matrix", "DOI": "10.1007/s40747-022-00679-9", "PubYear": 2022, "Volume": "8", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "Yan Sun", "Affiliation": "School of Computer of Qinghai Normal University, Xining, China; School of Computer, Qinghai Minzu University, Xining, China; State Key Laboratory of Tibetan Intelligent Information Processing and Application, Qinghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer of Qinghai Normal University, Xining, China; State Key Laboratory of Tibetan Intelligent Information Processing and Application, Qinghai, China"}], "References": []}, {"ArticleId": 93199388, "Title": "Far-field bistatic scattering simulation for rice crop biophysical parameters retrieval using modified radiative transfer model at X- and C-band", "Abstract": "Dual-polarimetric (i.e., HH and VV) scattering responses at X- and C-bands from indigenously designed far-field bistatic specular (bi-spec) scatterometer acquired over the entire rice crop phenology have been analyzed using a modified parametric radiative transfer model (MRTM). The scattering responses are examined over a wide-ranging bi-spec incidence angle varying from 20° to 60° at 10° intervals. Furthermore, optimization of the bi-spec scatterometer system showed high sensitivity at 40° specular angle of incidence based on the correlation analysis between the measured value of bi-spec scattering coefficient (σ<sub>Measured</sub><sup>0</sup>) and vegetation biophysical parameters such as leaf area index (LAI) and plant water content (PWC). The MRTM implied to investigate the dominance of surface (<sub>σ</sub>Surface<sup>0</sup>) and vegetation(σ<sub>Vegetation</sub><sup>0</sup>) specular scattering components within the total value of simulated bi-spec scattering coefficient (σ<sub>Simulated</sub><sup>0</sup>) in forward scattering alignment (FSA) convention. The vegetation phase function (VPF) and a bi-directional reflectance distribution function (BRDF) are parameterized to approximate scattering responses from the vegetation volume layer and the surface beneath vegetation. In addition, empirical frequency-specific parameters (i.e., b<sub>1</sub>and b<sub>2</sub>) are used to simulate temporal dynamics of σ<sub>Simulated</sub><sup>0</sup> using a linear relationship between vegetation optical depth (VOD) with LAI and PWC. The model and empirical frequency-specific parameters are calibrated using a constrained non-linear least square optimization algorithm, and the results are validated against the value of σ<sub>Measured</sub><sup>0</sup>. According to the simulation findings, the total specular scattering decomposition offers a robust model for interpreting time-series microwave scattering scenarios through vegetation in the FSA convention. Moreover, as compared to C-band, the inverse modeling of MRTM showed high retrieval accuracies of LAI at VV polarization and PWC at HH polarization for the X-band.", "Keywords": "Far-field bi-spec scatterometer system ; Forward scattering alignment ; Leaf area index ; Plant water content ; MRTM", "DOI": "10.1016/j.rse.2022.112959", "PubYear": 2022, "Volume": "272", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Indian Institute of Technology (BHU), Varanasi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Indian Institute of Technology (BHU), Varanasi, India;Corresponding author at: Department of Physics, Indian Institute of Technology (BHU), Varanasi 221005, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Physics, Indian Institute of Technology (BHU), Varanasi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Indian Institute of Technology (BHU), Varanasi, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Indian Institute of Technology (BHU), Varanasi, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Indian Institute of Technology (BHU), Varanasi, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Environment and Sustainable Development, Banaras Hindu University, Varanasi, India"}], "References": [{"Title": "Accuracy assessment of the global TanDEM-X digital elevation model in a mountain environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111724", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 93199400, "Title": "The interpretation of zeta test results: a supplement", "Abstract": "<p>Zeta is a mathematical method which has been used to try to solve authorship attribution problems in both the early modern and modern periods. <PERSON>, one of its leading practitioners, has recently made two important claims (<PERSON>, Zeta revisited, Digital Scholarship in the Humanities, print publication forthcoming). Given his status as an expert on Zeta, these claims are likely to be treated by researchers as authoritative. Most Zeta graphs show a good separation between the base and counter segments. <PERSON> claims that this is an important finding, rather than, as <PERSON><PERSON> argued, a mechanical consequence of the method (<PERSON><PERSON><PERSON>, The interpretation of zeta test results. Digital Scholarship in the Humanities, 34(2): 401–18). Drawing a bisector line is a common technique for interpreting a Zeta graph and, in practice, it produces the same attributions as the ones that <PERSON>’s less mathematical approach produces. <PERSON><PERSON><PERSON> argued that this is an unsound technique, an argument that <PERSON> has now rejected. This paper elaborates <PERSON><PERSON><PERSON>’s arguments and presents the results of new experiments to show that both of <PERSON>’s claims are incorrect and should not be accepted.</p>", "Keywords": "", "DOI": "10.1093/llc/fqac011", "PubYear": 2022, "Volume": "37", "Issue": "4", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Independent student"}], "References": [{"Title": "Big data or not enough? Zeta test reliability and the attribution of <PERSON>", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "542", "JournalTitle": "Digital Scholarship in the Humanities"}, {"Title": "Zeta revisited", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "4", "Page": "1002", "JournalTitle": "Digital Scholarship in the Humanities"}]}, {"ArticleId": 93199430, "Title": "Exploring Chinese lexical differences based on synergetic-linguistic model", "Abstract": "At the stage of current studies, the synergetic-linguistic model is mainly used for the validation of individual languages, lacking application to the measurement of linguistic differences. In addition, the structure of the model is largely fixed and the pattern is relatively simple. We have innovatively proposed a lexical differences analysis framework and conducted a comparative study between Vietnamese Chinese (VC) and Mandarin Chinese. The framework, based on the synergetic-linguistic model, shows great effectiveness of lexical synergetic system modification and the reasonability of determining the similarity threshold. The comparative results demonstrate distinct differences that lay between VC and Mandarin Chinese through difference measurement.", "Keywords": "", "DOI": "10.1093/llc/fqac009", "PubYear": 2022, "Volume": "37", "Issue": "4", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "Nan<PERSON> Lin", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Guangdong University of Foreign Studies , Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Guangdong University of Foreign Studies , Guangzhou, China and , Guangzhou, China;Guangzhou Key Laboratory of Multilingual Intelligent Processing, Guangdong University of Foreign Studies , Guangzhou, China and , Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Guangdong University of Foreign Studies , Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information, Hunan Institute of Information Technology , Changsha, China"}], "References": []}, {"ArticleId": 93199512, "Title": "Мультискрученные аддитивные коды с дополнительными двойственными над конечными полями", "Abstract": "", "Keywords": "", "DOI": "10.31857/S055529232201003X", "PubYear": 2022, "Volume": "58", "Issue": "1", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "С. Шарма", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93199638, "Title": "A novel hybrid machine learning algorithm for detection in smart cities", "Abstract": "This article tries to develop a novel hybrid method for the state estimation as well as the broken rotor bar (BRB) detection of the induction motors (IMs) in smart grids. This is so significant to survive the IM from harsh damages and avoid malfunctioning in the system. The suggested method is constructed based on the <PERSON><PERSON> filter as a successful machine learning and sigma point which have shown superior performance over the other well-known methods in the area. In order to enhance its efficiency, sigma points would make a transformation which can make the model more stable and precise. The experimental results on a test IM reveal the high accuracy and stable performance of the proposed model. Three varied scenarios are simulated to show the quality and appropriate performance of the proposed intelligent method at different loading situations. The simulation results validate the high quality and promising performance of the proposed model.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.107787", "PubYear": 2022, "Volume": "99", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Civil Engineering and Architecture, Shandong University of Science and Technology, Qingdao, Shandong Province 266590, China;Corresponding author"}, {"AuthorId": 2, "Name": "Shuangxu Han", "Affiliation": "College of Clinical Medicine, Shandong First Medical University, Taian, Shandong Province 271021, China"}], "References": [{"Title": "Health monitoring of induction motors through embedded systems-simulation of broker rotor bar fault and abnormal gear teeth fault", "Authors": "<PERSON><PERSON><PERSON>; Dr <PERSON><PERSON><PERSON>; Dr <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "", "Page": "103077", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A fuzzy type-2 fault detection methodology to minimize false alarm rate in induction motor monitoring applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106373", "JournalTitle": "Applied Soft Computing"}, {"Title": "Neuroadaptive observer-based discrete-time command filtered fault-tolerant control for induction motors with load disturbances", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "435", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 93199681, "Title": "Building energy management and forecasting using artificial intelligence: Advance technique", "Abstract": "This paper investigates the smart energy management of a building using artificial intelligence (AI) and real-time data. The proposed method uses a stochastic structure including the point estimate method and grey wolf optimization (GWO) to provide a suitable scheduling program for a renewable based building. Moreover, a correction approach is developed to improve the search ability of the GWO. Different renewable sources of photovoltaics and wind turbine are considered for the power supply to the building. Considering the big size of the unit, two gas turbines are also incorporated to help emergency support of the building. The output power of the renewable energy sources are forecasted using the support vector machine (SVM) to have an accurate and reliable analysis. Considering the high uncertainty effects, point estimate method is a suitable method for handling the forecast errors. Two different scenarios are simulated to check the energy management problem in the normal and emergency cases in the building. The model is validated using a typical building test system.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.107790", "PubYear": 2022, "Volume": "99", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Peoples Friendship University of Russia (RUDN University), Moscow, Russian Federation;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Peoples Friendship University of Russia (RUDN University), Moscow, Russian Federation;Moscow State University of Civil Engineering, Moscow, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Peoples Friendship University of Russia (RUDN University), Moscow, Russian Federation"}], "References": [{"Title": "Transformation operators based grey wolf optimizer for travelling salesman problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "", "Page": "101454", "JournalTitle": "Journal of Computational Science"}]}]