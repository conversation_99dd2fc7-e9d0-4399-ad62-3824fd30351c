[{"ArticleId": 88312061, "Title": "Calculation of 1D and 2D densities in VMD: A flexible and easy-to-use code", "Abstract": "Characterization of structural information at the atomistic level in molecular dynamics (MD) simulations is a necessary task for researchers in the fields of materials modeling and simulation . Visualization of the density distribution is typically one of the most important properties in structural characterization. Visual Molecular Dynamics (VMD) is a widely used molecular visualization package that can not only visualize complex molecular systems but also perform analysis by integrating special plugins or by running in-house generated TCL scripts. However, a density analysis is still not an in-built feature of VMD. This work presents a flexible and easy-to-use TCL code to be used in VMD, that can perform both 1D and 2D density calculations over any specified local areas of a given system. By using the built-in commands of VMD, the code can access and process trajectory files in any formats that are supported by VMD, as produced by mainstream simulation packages, i.e., LAMMPS, GROMACS, NAMD, and CHARMM, etc. This work introduces the calculation method, code, and usages in detail to provide a quick start for users in their density analysis work. Program summary Program title: DensityCalculator CPC Library link to program files: https://doi.org/10.17632/vcbh2gt8wg.1 Developer's repository link: https://github.com/yuxiangwang321/DensityCalculator Licensing provisions: GPLv3 Programming language: TCL/TK External routines: VMD (version 1.9.3, http://www.ks.uiuc.edu/Research/vmd/ ) Nature of problem: Calculate mass or number density distribution from trajectory files in VMD, and project the density along a specified 1D axis or onto a specified 2D plane. Solution method: The 1D or 2D density distribution is calculated through the following steps: slice the concerned area with specified resolutions in X, Y, and Z directions; then the mass or atom number in each unit slice (or cuboid in 2D case) is calculated; if there are multiple frames, average the mass or atom number by the frame number; finally, density in each unit is divided by the slice volume to get the density. Additional comments including restrictions and unusual features: The code must be run with VMD as a platform, the current version only can only calculate mass or number density. The code can calculate both 1D and 2D density distributions in any specified local or global areas of the system. Introduction Structural information in molecular simulations is always of interest for researchers, and amongst these, the density distribution is one of the most important properties. Since it can sometimes be challenging for experimental approaches to provide such information at the nanoscale, particularly for interfaces, as a powerful auxiliary means, numerical simulation approaches can complement this limitation and provide characterization the density distribution [1], [2], [3], [4], [5]. In some molecular dynamics (MD) simulation packages [6], [7], the density information can be calculated in real time while the simulation is running, but at least two disadvantages are apparent: one is the computing of such properties can inevitably slow the simulations; another is that the density calculation settings have to be set a priori of running of the simulations. In the latter case, unexpected or rare phenomena can be missed with such a priori settings, leading to the need to run extra (potentially expensive) simulations. It is thus desireable to calculate these properties by postprocessing the trajectory data produced in the simulations, and some postprocessing packages or tools [8], [9], [10], [11], [12], [13], [14] therefore have been created for this purpose. These packages can provide a wide range of analysis functions, such as calculation of hydrogen bonding, root mean square displacement (RMSD), radial distribution function (RDF), and of course the density distribution. However, there are challenges in the use these packages: the learning curve of some packages can be steep to grasp, especially for beginners who only want to calculate the density (sometimes even the installation procedure would present a sufficient obstacle in this instance); some packages have limited functionality in terms of density calculation (e.g., cannot calculate the 2D density distribution or cannot calculate the density distribution at arbitrary local sites); furthermore, some packages are designed only for a limited range of input trajectory file formats. At present, to the authors' best knowledge, there is no freely-available open-source code for creating 2D density plots over a pre-specified region in the simulation system, that can handle a wide-range of trajectory formats as input. Visual Molecular Dynamics (VMD) [15] is a widely used visualization package for MD simulations, it is open-source, easy to obtain and install on a wide range of platforms, and can support many types of trajectory data. One of the most important features in VMD is the integration of the Tk console (a TCL programming platform) which equips VMD with an excellent expandable feature. By programming in the Tk console, new functions can be added in VMD through in-house plugins or TCL codes. The previous work of Giorgino [10] provided a VMD tool for the calculation of 1D density plots; it is notable that the majority of users who have cited this tool have applied the code to analyze interfacial simulations. It is expected that the current work will similarly find wide utility for researchers working in the area of interfacial modeling. These facts have prompted the creation of a flexible and easy-to-use VMD-based tool that focuses on both 1D and 2D density distribution calculations, as summarized herein. Therefore, this work presents a TCL code, DensityCalculator (DC), which can address the drawbacks detailed above. DC can calculate the mass and number density from trajectory data loaded into VMD. Both the density distribution projected along an axis (1D case) or onto a plane (2D case) can be calculated. In the latter case, the 2D density may be computed and visualized over a pre-defined region in the simulation cell. In particular, users can specify global or any local sites in the simulation cell to calculate the density distribution, and users can also specify any frame range with any step size, and resolution of the selected area (e.g. slice). Based on the powerful atom selection syntax in VMD, it is possible to also specify arbitrary atom selections for the density calculation. In addition, the code is easy to use, requiring users only to have basic knowledge of how to use VMD and know how to open the Tk console. Herein, the calculation method, code, and demonstration of the usage of the code by examples will be introduced, along with comments regarding some limitations of the current code. Figures A schematic indicating the parameters in a 1D density calculation. The light blue colored box denotes the whole simulation cell, the light yellow colored box denotes the local area where the density d... A schematic of the parameters in a 2D density calculation. The simulation cell is hidden (provided in Fig. 1), the light yellow colored box denotes the local area where the density distribution will b... The 1D mass density distribution along the Y direction of a water/cellulose system with different resolution values (a) yr=232, dy=0.5 Å and (b) yr=58, dy=2 Å. The 2D mass density distribution on the XY plane of the water/cellulose system in Fig. 3. Panels (a) and (c) show simulation snapshots of cellulose and water, respectively, (b) and (d) show the 2D den... 1D density profiles of a MDPD droplet calculated for three single frames (frame 0, 50, and 99, respectively) and by averaging 100 frames (frames 0 to 99). The inset is the snapshot of the MDPD droplet... 2D number density heatmaps on the XY plane generated for the MDPD droplet. Left, density map calculated from one single frame, right, density map obtained from the average over 100 frames. Section snippets 1D case The calculation method of DC is straightforward. Taking the 1D mass density distribution along the X axis as an example (Fig. 1 provides the parameters): first, determine the area by specifying low and high boundaries along different directions, x l , x h , y l , y h , z l , z h ; then, equally divide x h − x l into xr segments with length dx ( xr is referred to as the resolution, dx is referred to as the interval), together with boundaries in Y and Z directions, a total of xr slices with volume Δ V (Eq. (1)) are Code description To illustrate the architecture of the DC code, a pseudo-code description is provided in Table 1. For clarity, twelve parallel if (or elseif ) small blocks for the calculation of different density type (mass or number) onto different projection planes (XY, YZ, ZX, or none) or along different projection axes (X, Y, Z, or none) are placed in the selection pool. In each block, the first step is slicing the specified area according to boundaries and resolutions, and then obtaining the cuboids (or Usage and examples This code must be invoked by VMD in its working directory, so the following examples will take VMD 1.9.3 as an example. The example trajectory files include a . dcd file and a . psf file, to follow these examples these files and the source dc.tcl file should be copied into the VMD working directory. The length unit is Angstrom (Å), the mass density unit is g/cm<sup>3</sup>, the number density unit is atoms/nm<sup>3</sup>, the unit of total mass is amu. Limitations Some limitations of this code should be discussed. At present, the code is configured to produce mass density analyses, and does not compute other types of density, such as the charge density. Also, the code is currently configured to handle orthorhombic cells only. A survey of the citations of the 1D VMD density code reported by Giorgino [10] indicates that orthorhombic cells were almost always used in these cases. For the purposes of this code, a similar usage scenario is envisaged, which Conclusions The present work introduces a TCL code that runs in VMD for the calculation of density distribution, based on any trajectory format that can be loaded by VMD. The code can calculate both 1D and 2D density distributions at any arbitrarily specified areas of a simulation cell; users can also specify the resolution of the density heatmap to get extremely detailed or general distributions of the systems. In addition, users can determine the frame range and step of the trajectory files for the Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This work was funded by the Ford Alliance scheme and Deakin University . References (21) Y. Wang et al. Appl. Surf. Sci. (2019) M. Bergenstråhle et al. Carbohydr. Res. (2010) K. Kulasinski et al. J. Mech. Phys. Solids (2017) W. Humphrey et al. J. Mol. Graph. (1996) S. Plimpton J. Comput. Phys. (1995) R. Briones et al. Biophys. J. (2019) R. Gautier et al. Biophys. J. (2018) R.T. McGibbon et al. Biophys. J. (2015) T. Giorgino Comput. Phys. Commun. (2014) V. Gapsys et al. J. Comput.-Aided Mol. Des. (2013) There are more references available in the full text version of this article. Cited by (0) Recommended articles (6) Research article Inhomogeneity of polylysine adsorption layers on lipid membranes revealed by theoretical analysis of electrokinetic data and molecular dynamics simulations Bioelectrochemistry, Volume 141, 2021, Article 107828 Show abstract The adsorption of large polycations on a charged lipid membrane is qualitatively different from the small inorganic cations, which almost uniformly populate the membrane surface. We assume that the polycationic adsorption layer might be laterally inhomogeneous starting from a certain polymer length, and this effect can be more visible for membranes with low anionic lipid content. To study systems with inhomogeneous adsorption layers, we carried out electrokinetic measurements of mobility of liposomes containing anionic and neutral phospholipids in the presence of polylysine molecules. Some of these systems were simulated by all-atom molecular dynamics. Here we proposed a theoretical approach accounting for the formation of separated regions at the membrane surface, which differ in charge density and surface potential. Our model allowed us to determine the adsorption layer’s geometric parameters such as surface coverage and surface-bound monomer fraction of polymer, which correlate with the molecular dynamics (MD) simulations. We demonstrated that the configuration polylysine adopts on the membrane surface (tall or planar) depends on the polymer/membrane charge ratio. Both theory and MD indicate a decrease in the anionic lipid content, alongside with a decrease in the bound monomer fraction and corresponding increase in the extension length of the adsorbed polymers. Research article Computational design of shape memory polymer nanocomposites Polymer, Volume 217, 2021, Article 123476 Show abstract The goal of this work is to understand the underlying mechanisms that govern shape memory of polymer nanocomposites at the molecular level and to utilize them for novel synthetic shape memory polymer (SMP) materials for reconfigurable structures. In this study, we have performed coarse-grained molecular dynamics simulations of buckypaper (BP)/epoxy nanocomposites with a focus on their mechanical and shape memory performances, specifically on prediction of the Young's modulus of the material as a function of carbon nanotube (CNT) loading. Our results demonstrate that the Young's modulus linearly increases with CNT volume fraction below 0.16 (40 wt%) followed by a sharp upsurge of the modulus at higher loading where the onset of entanglements of nanotubes was determined. Additionally, we found a significantly greater increase of the modulus at T > T g compared with the values below the glass transition temperature T g for all considered systems. The simulation suggests that incorporation of BP restricts relaxation of network strands of the polymer matrix and leads to resistance in the recovery process of composites. Research article Investigation of hydration of potassium carbonate via reactive force-field molecular dynamics simulations Journal of Energy Storage, Volume 39, 2021, Article 102601 Show abstract A reactive force field potential was applied to H<sub>2</sub>O/K<sub>2</sub>CO<sub>3</sub> systems, and its effectiveness was verified through first-principles calculations and experimental data. Thereafter, molecular dynamics simulations were conducted to investigate the sorption of water molecules onto the K<sub>2</sub>CO<sub>3</sub> (001) surface for water coverages from 0.5–3.0 monolayers (ML) at 0.5-ML intervals. As the water coverage was increased, the water molecule order on the K<sub>2</sub>CO<sub>3</sub> (001) surface decreased, the number of water layers changed from one to three, and some water molecules passed through the first atomic layer of the surface while interacting with the second/third ones. Owing to the differences in water–surface and water–water interactions, the minimum and maximum self-diffusion coefficients appeared at 0.5 ML and 1.0 ML, respectively. At high water coverage, temperature negatively affected water molecule sorption, indicating that reduced temperatures improved the sorption properties of the H<sub>2</sub>O/K<sub>2</sub>CO<sub>3</sub> (001) system. Two types of hydrogen bonds—those between water and the surface (HB<sub>1</sub>) and those between water molecules (HB<sub>2</sub>)—were formed in the H<sub>2</sub>O/K<sub>2</sub>CO<sub>3</sub> (001) system, and they competed with each other in water sorption on the K<sub>2</sub>CO<sub>3</sub> (001) surface. For practical applications, the formation of HB<sub>1</sub>- and HB<sub>2</sub>-type hydrogen bonds should be promoted and inhibited, respectively, to improve the sorption effects and avoid hydrolysis. Research article FaVAD: A software workflow for characterization and visualizing of defects in crystalline structures Computer Physics Communications, Volume 262, 2021, Article 107816 Show abstract The analysis of defects and defect dynamics in crystalline materials is important for fundamental science and for a wide range of applied engineering. With increasing system size the analysis of molecular-dynamics simulation data becomes non-trivial. Here, we present a workflow for semi-automatic identification and classification of defects in crystalline structures, combining a new approach for defect description with several already existing open-source software packages. Our approach addresses the key challenges posed by the often relatively tiny volume fraction of the modified parts of the sample, thermal motion and the presence of potentially unforeseen atomic configurations (defect types) after irradiation. The local environment of any atom is converted into a rotation-invariant descriptive vector (‘fingerprint’), which can be compared to known defect types and also yields a distance metric suited for classification. Vectors which cannot be associated to known structures indicate new types of defects. As proof-of-concept we apply our method on an iron sample to analyze the defects caused by a collision cascade induced by a 10 keV primary-knock-on-atom. The obtained results are in good agreement with reported literature values. Program Title: Fingerprinting and Visualization Analyzer of Defects (FaVAD). CPC Library link to program files: https://doi.org/10.17632/bmv9kxkzg3.1 Developer’s repository link: http://gitlab.mpcdf.mpg.de/NMPP/favad.git Licensing provisions: GPLv3. Programming language: Python 3, Fortran 90, and C ++ . Nature of problem : The analysis of damage and damage evolution in crystalline materials is important for fundamental science and for a wide range of applied engineering. Defects in materials on an atomic level are commonly analyzed by Wigner–Seitz or topology Voronoi tessellation based methods. However, these approaches exhibit specific shortcoming, especially at elevated sample temperatures. In order to improve upon that, a more robust and quantifiable identification and classification approach of known as well as of unpredicted defect structures is desirable. Solution method: A fingerprint-like method is proposed to analyze in detail the damage in a material augmented with a probabilistic interpretation. It is based on the calculation of a descriptor vector for each atom in the sample. These vectors represent in a compact form the individual environments of the atoms. For standard types of defects (i.e. interstitial atoms) the corresponding descriptor vectors can be precomputed and used for rapid classification. Unexpected or less common defect types can be identified by applying a principal component analysis to the descriptor vectors. Vacancies in the material are identified by computing the radii of the largest empty spheres which can be embedded into the sample, followed by a thresholding process. This new method is easy to use and requires only modest computational resources. Finally, the classified defects are visualized using the open source software VisIt. Additional comments including restrictions and unusual features: The descriptor vectors are computed using the command line interface of QUIP with the Gaussian Approximation potential (GAP) package. The analysis of the sample is done using Python scripts which make extensive use of the NumPy package. A modified KDTree2 code is employed to calculate the location of single vacancies and voids. The program VisIt is used for the visualization of the classified point defects in the sample. We provide a Dockerfile for automatically creating a portable Docker container which installs all the programs together with a Python script to analyze as an example a damaged iron molecular dynamics sample. A shell script to install the programs locally in a Linux-based server or desktop environment is included also. Research article viewSq, a Visual Molecular Dynamics (VMD) module for calculating, analyzing, and visualizing X-ray and neutron structure factors from atomistic simulations Computer Physics Communications, Volume 264, 2021, Article 107881 Show abstract viewSq is a Visual Molecular Dynamics (VMD) module for calculating structure factors ( S ( q ) ) and partial structure factors for any user-selected atomic selections ( S s e l 1 , s e l 2 ( q ) ) derived from computer simulation trajectories, as well as quantifying, analyzing, and visualizing atomic contributions to them. viewSq offers radial distribution functions ( g ( r ) ), S ( q ) and S s e l 1 , s e l 2 ( q ) with and without X-ray atomic form factors or neutron scattering lengths, partial radial distribution functions ( g s e l 1 , s e l 2 ( r ) ), as well as decompositions of S ( q ) and S s e l 1 , s e l 2 ( q ) into various positive and negative components (each of which indicates periodic atomic ordering). Additionally, viewSq plots as a function of distance r the Fourier transform summands used to transform g ( r ) to S ( q ) , allowing understanding of the atom-specific distances around atomic centers that contribute to S ( q ) , S s e l 1 , s e l 2 ( q ) , and their various positive and negative components. viewSq will also rank atoms by their contributions to S ( q ) , S s e l 1 , s e l 2 ( q ) , and the positive and negative components, and uses VMD to visualize those atoms interactively. Another feature performs the same rankings for atoms within a cutoff distance of a user-selected central atom, allowing the fundamental contributions atoms make with each other to be quantified and interactively visualized. Analysis may be done for any user-selected range of wavenumber ( q ) for a single structure or a small ensemble of structures. viewSq’s features are illustrated by using a single frame from an MD simulation of a box of water. Program Title: viewSq CPC Library link to program files: https://doi.org/10.17632/w5bxkfsj4d.1 Developer’s repository link: https://github.com/tmackoy/viewSq Licensing provisions: MIT Programming language: Tcl, Python Nature of problem: Total scattering, the measurement of a complete diffraction pattern including Bragg and diffuse scattering of radiation, is a powerful family of techniques for investigating the structures of liquids and soft matter [1, 2, 3, 4, 5, 6]. Data are typically derived from X-ray or neutron scattering and reported as a plot of intensity or structure factor ( S ( q ) ), as a function of wavenumber ( q ). So S ( q ) summarizes in a one-dimensional plot the positions in three-dimensional space of all atomic species, each of which scatters X-rays and neutrons differently. Thus, an experimental S ( q ) can be difficult to interpret [7, 8, 9, 10, 11, 12, 13], although recent advances have been made [14, 15, 16]. Computer simulations (e.g. molecular dynamics (MD) or Monte Carlo simulations) complement experiment by allowing calculated S ( q ) to be calculated from a structural model [17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28] and decomposed into partial structure factors ( S s e l 1 , s e l 2 ( q ) ), each associated with user-defined atomic selections [29, 30, 31, 32]. While S s e l 1 , s e l 2 ( q ) are informative, they are also difficult to analyze for determining the structural ordering underlying S ( q ) . Not only are S s e l 1 , s e l 2 ( q ) difficult to conceptualize, but each S s e l 1 , s e l 2 ( q ) is the sum of positive and negative components, which each indicate periodic atomic ordering. Moreover, these components partially or nearly completely cancel and are often large in magnitude compared with S ( q ) and S s e l 1 , s e l 2 ( q ) . Numerous computer programs exist to calculate S ( q ) (e.g. by performing the Fourier transform of the radial distribution function ( g ( r ) )) derived from molecular simulations [17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]. Publicly available computer programs focus on accurately reproducing the experimental S ( q ) and/or incorporating the simulated S ( q ) into a structure refinement algorithm. viewSq not only calculates S ( q ) and S s e l 1 , s e l 2 ( q ) , but it is unique because it also emphasizes interpreting and visualizing atomic contributions to S ( q ) and S s e l 1 , s e l 2 ( q ) . Solution method: While several tools exist for calculating S ( q ) and S s e l 1 , s e l 2 ( q ) from atomistic simulations [17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28], viewSq is to our knowledge the first program that also provides positive and negative components of S ( q ) and S s e l 1 , s e l 2 ( q ) , and allows ready visualization of atomic contributions to S ( q ) , S s e l 1 , s e l 2 ( q ) , and the positive and negative components of S ( q ) and S s e l 1 , s e l 2 ( q ) . Options contained in this release of viewSq include: 1. Select a single structure or an ensemble of structures to analyze. 2. Select one q or a range of q to analyze. 3. Calculate g ( r ) and S ( q ) using any number of trajectory snapshots. 4. Select chemically relevant atom subsets for calculation of partial radial distribution functions ( g s e l 1 , s e l 2 ( r ) ) and S s e l 1 , s e l 2 ( q ) . 5. Separately sum positive and negative Fourier transform summands constituting S ( q ) and S s e l 1 , s e l 2 ( q ) , to determine positive and negative components of S ( q ) and S s e l 1 , s e l 2 ( q ) . 6. Display summands composing S ( q ) or S s e l 1 , s e l 2 ( q ) as a function of distance from atomic centers (or a user selection of atomic centers). 7. Quantify compositions of g ( r ) , g s e l 1 , s e l 2 ( r ) , or summands for any combination of r . 8. Rank atoms by their contributions to S ( q ) and S s e l 1 , s e l 2 ( q ) . 9. Display atoms making the largest contributions to S ( q ) or S s e l 1 , s e l 2 ( q ) at selected values of q . This can be done for the entire simulation box or with respect to one central atom. 10. Calculations and analysis can be accomplished using X-ray scattering atomic form factors, neutron scattering lengths, or neither. Features 5–9 are currently unique to viewSq and provide a suite of tools for analyzing and visualizing S ( q ) and S s e l 1 , s e l 2 ( q ) from atomistic simulations. Thus, viewSq is a Visual Molecular Dynamics [33] (VMD) module that provides a uniquely powerful interactive experience to interpret peaks in S ( q ) calculated for X-ray scattering, neutron scattering, or independent of atomic interactions with radiation. viewSq was designed as a Visual Molecular Dynamics (VMD) module because VMD can readily load, analyze, and visualize the types and sizes of trajectories often used for S ( q ) studies. Additional comments including restrictions and unusual features: Because viewSq pre-calculates a variety of quantities in order to provide a unique analysis and interactive visualization experience, the memory, time, and disk usage are higher than other programs for simple S ( q ) and S s e l 1 , s e l 2 ( q ) calculations. Additionally, the memory requirements grow with number of atoms and might be restrictive even on machines with 64+ GB RAM for simulations with over 25,000 atoms. Advantages to viewSq include its unique features and visualizations that are not available in programs which solely calculate S ( q ) and S s e l 1 , s e l 2 ( q ) . viewSq is only compatible with rectangular simulation boxes. Research article Spinney : Post-processing of first-principles calculations of point defects in semiconductors with Python Computer Physics Communications, Volume 264, 2021, Article 107946 Show abstract Understanding and predicting the thermodynamic properties of point defects in semiconductors and insulators would greatly aid in the design of novel materials and allow tuning the properties of existing ones. As a matter of fact, first-principles calculations based on density functional theory (DFT) and the supercell approach have become a standard tool for the study of point defects in solids. However, in the dilute limit, of most interest for the design of novel semiconductor materials, the “raw“ DFT calculations require an extensive post-processing. Spinney is an open-source Python package developed with the aim of processing first-principles calculations to obtain several quantities of interest, such as the chemical potential limits that assure the thermodynamic stability of the defect-laden system, defect charge transition levels, defect formation energies, including electrostatic corrections for finite-size effects, and defect and carrier concentrations. In this paper we demonstrate the capabilities of the Spinney code using c-BN, GaN:Mg, TiO 2 and ZnO as examples. Program Title: Spinney CPC Library link to program files: https://doi.org/10.17632/2xp4ddwmgx.1 Developer’s repository link: https://gitlab.com/Marrigoni/spinney Code Ocean capsule: https://codeocean.com/capsule/4970623 Licensing provisions : MIT Programming language: Python 3 External libraries: NumPy [1], SciPy [2], Pandas [3], Matplotlib [4], ASE [5] Nature of problem: Post-processing of first-principles calculations in order to obtain important properties of defect laden systems in the dilute-limit: chemical potential values ensuring thermodynamic stability, thermodynamic charge transition levels, defect formation energies and corrections thereof using state-of-the-art corrections schemes for electrostatic finite-size effects, equilibrium defect and carriers concentrations. Solution method: Flexible low-level interface for allowing the post-processing of the raw fist-principles data provided by any computer code. High-level interface for parsing and post-processing the first-principles data produced by the popular computer codes VASP and WIEN2k. Additional comments including restrictions and unusual features: An extensive documentation is available at: https://spinney.readthedocs.io <sup> ☆ </sup> The review of this paper was arranged by Prof. Stephan Fritzsche. <sup> ☆☆ </sup> This paper and its associated computer program are available via the Computer Physics Communications homepage on ScienceDirect ( http://www.sciencedirect.com/science/journal/00104655 ). View full text © 2021 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cpc.2021.108032", "PubYear": 2021, "Volume": "266", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Frontier Materials, Deakin University, Geelong, VIC 3216, Australia;Corresponding authors"}, {"AuthorId": 2, "Name": "Alper Kiziltas", "Affiliation": "Research and Innovation Center, Ford Motor Company, Dearborn, MI, 48124, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Research and Innovation Center, Ford Motor Company, Dearborn, MI, 48124, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for Frontier Materials, Deakin University, Geelong, VIC 3216, Australia;Corresponding authors"}], "References": []}, {"ArticleId": 88312399, "Title": "Multistage Open-Shop-Problem: Schedules for N Tasks on K Machines with Different Technical Characteristics for an Arbitrary order of Tasks", "Abstract": "The statements and mathematical models of the open — shop-problem are considered: performing N tasks on K machines with an arbitrary order of processing each task on all machines. Unlike most publications on solving this problem, the task posed is solved in a multi-stage formulation under the conditions of a sequential chain of sections and workshops of the enterprise, as well as in the presence of restrictions on the start and end times of tasks and the permissible operating times of machines. In addition, for scheduling systems of enterprises, it is extremely important to consider this task in a multi-stage setting, i.e. in a sequential chain of plots and workshops. The properties of admissible and optimal schedules, as well as algorithms of exact and approximate methods for solving these problems by successive optimization algorithms are investigated. At the early stages of solving the problem, the fact of incompatibility of the system of constraints of the task and the definition of a subset of tasks or resources, the time range of which should be expanded, is established. The described algorithms for solving the problem are illustrated by numerical examples.", "Keywords": "", "DOI": "10.17587/it.27.249-258", "PubYear": 2021, "Volume": "27", "Issue": "5", "JournalId": 55442, "JournalTitle": "INFORMACIONNYE TEHNOLOGII", "ISSN": "1684-6400", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Aachen, Germany"}], "References": []}, {"ArticleId": 88312401, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-6423(21)00066-6", "PubYear": 2021, "Volume": "208", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [], "References": []}, {"ArticleId": 88312424, "Title": "A discrete-event simulation model for the Bitcoin blockchain network with strategic miners and mining pool managers", "Abstract": "As the first and most famous cryptocurrency-based blockchain technology, Bitcoin has attracted tremendous attention from both academic and industrial communities in the past decade. A Bitcoin network is comprised of two interactive parties: individual miners and mining pool managers, each of which strives to maximize its own utility. In particular, individual miners choose which mining pool to join and decide on how much mining power to commit under limited constraints on the mining budget and mining power capacity; managers of mining pools determine how to allocate the mining reward and how to adjust the membership fee. In this work we investigate the miners’ and mining pool managers’ decisions in repeated Bitcoin mining competitions by building a Monte-Carlo discrete-event simulation model. Our simulation model (i) captures the behavior of these two parties and how their decisions affect each other, and (ii) characterizes the system-level dynamics of the blockchain in terms of the mining difficulty level and total mining power. In addition, we study the sensitivity of system performance metrics with respect to various control parameters. Our analysis may provide useful guidelines to mining activity participants in the Bitcoin network.", "Keywords": "Blockchain ; Discrete-event simulation ; Bitcoin mining policy ; Mining competition", "DOI": "10.1016/j.cor.2021.105365", "PubYear": 2021, "Volume": "134", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, North Carolina State University, Raleigh, NC 27606, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, North Carolina State University, Raleigh, NC 27606, USA"}, {"AuthorId": 3, "Name": "Hong Wan", "Affiliation": "Department of Industrial and Systems Engineering, North Carolina State University, Raleigh, NC 27606, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Operations Research Graduate Program, North Carolina State University, Raleigh, NC 27607, USA"}], "References": [{"Title": "A systematic literature review on machine learning applications for sustainable agriculture supply chain performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "119", "Issue": "", "Page": "104926", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 88312436, "Title": "Improved political optimizer for complex landscapes and engineering optimization problems", "Abstract": "Political Optimizer (PO) is a recently proposed meta-heuristic with excellent convergence speed and exploitation capability. However, it is found that PO prematurely converges for complex problems because of not giving enough time to the exploration. In this paper, the exploration capability and balance of PO are improved by making multiple modifications to propose an Improved Political Optimizer (IPO). To improve the exploration capability, the condition of an equal number of parties and constituencies is relaxed, and switching with a random member of a random party is incorporated in the party-switching phase. Moreover, the balance between exploration and exploitation is enhanced by modifying the position-updating strategy (RPPUS) in the election campaign phase and replacing the tunable party-switching rate with a self-adaptive parameter. The exploitation is further improved by utilizing the best solution of the population in the parliamentary affairs phase. In addition to improvement in PO, this paper also highlights a correction in the party-switching phase of the original PO. The performance of IPO is evaluated using 30 CEC-2014 benchmarks, 29 CEC-BC-2017 benchmarks, and 6 mechanical engineering problems. It is shown through non-parametric statistical Wilcoxon’s rank-sum test that IPO significantly outperforms PO. Moreover, IPO is also compared with 10 of the well-cited and 14 latest optimization algorithms published in 2020. It is shown by using the Friedman mean-rank test that IPO secures the first rank for both types of benchmark functions. Moreover, the comparison of IPO with PO and a few well-known algorithms for 6 of the engineering problems shows that IPO performs better or equivalently to the compared optimization algorithms.", "Keywords": "Political optimizer ; Global optimization ; Optimization algorithm ; Improved algorithm ; Meta-heuristic ; Engineering optimization", "DOI": "10.1016/j.eswa.2021.115178", "PubYear": 2021, "Volume": "182", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, GIFT University, Gujranwala, Pakistan;FAST School of Computing, National University of Computer and Emerging Sciences, Lahore, Pakistan;Corresponding author at: Department of Computer Science, GIFT University, Gujranwala, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FAST School of Computing, National University of Computer and Emerging Sciences, Lahore, Pakistan"}], "References": [{"Title": "An enhanced moth flame optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "2315", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An improved invasive weed optimization algorithm for solving dynamic economic dispatch problems with valve-point effects", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "805", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Black Widow Optimization Algorithm: A novel meta-heuristic approach for solving engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103249", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Improved GWO for large-scale function optimization and MLP optimization in cancer identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "1305", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Manta ray foraging optimization: An effective bio-inspired optimizer for engineering applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103300", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Equilibrium optimizer: A novel optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105190", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An improved whale optimization algorithm for forecasting water resources demand", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105925", "JournalTitle": "Applied Soft Computing"}, {"Title": "Barnacles Mating Optimizer: A new bio-inspired algorithm for solving engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103330", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An Improved Moth-Flame Optimization algorithm with hybrid search phase", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105277", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Improved Salp Swarm Algorithm based on opposition based learning and novel local search algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113122", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Group teaching optimization algorithm: A novel metaheuristic method for solving global optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "113246", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Balancing composite motion optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "250", "JournalTitle": "Information Sciences"}, {"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Chimp optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113338", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Political Optimizer: A novel socio-inspired meta-heuristic for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105709", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A new Newton metaheuristic algorithm for discrete performance-based design optimization of steel moment frames", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "234", "Issue": "", "Page": "106250", "JournalTitle": "Computers & Structures"}, {"Title": "Multi-population differential evolution-assisted Harris hawks optimization: Framework and case studies", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "175", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Marine Predators Algorithm: A nature-inspired metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113377", "JournalTitle": "Expert Systems with Applications"}, {"Title": "FBI inspired meta-optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106339", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel and effective optimization algorithm for global optimization and its engineering applications: Turbulent Flow of Water-based Optimization (TFWO)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103666", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A mayfly optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "106559", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Population size in Particle Swarm Optimization", "Authors": "<PERSON>; <PERSON><PERSON>sla<PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "100718", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Lévy flight distribution: A new metaheuristic algorithm for solving engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "103731", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Gradient-based optimizer: A new metaheuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "131", "JournalTitle": "Information Sciences"}, {"Title": "Search and rescue optimization algorithm: A new optimization method for solving constrained engineering optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113698", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improved Cuckoo Search algorithmic variants for constrained nonlinear optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102865", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Heap-based optimizer inspired by corporate rank hierarchy for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113702", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Giza Pyramids Construction: an ancient-inspired metaheuristic algorithm for optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "1743", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Archimedes optimization algorithm: a new metaheuristic algorithm for solving optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1531", "JournalTitle": "Applied Intelligence"}, {"Title": "Red fox optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114107", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Chaotic random spare ant colony optimization for multi-threshold image segmentation of 2D Kapur entropy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106510", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A carnivorous plant algorithm for solving global optimization problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106833", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evolutionary biogeography-based whale optimization methods with communication structure: Towards measuring the balance", "Authors": "<PERSON><PERSON><PERSON> Tu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106642", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Orthogonal learning covariance matrix for defects of grey wolf optimizer: Insights, balance, diversity, and feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106684", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Double adaptive weights for stabilization of moth flame optimizer: Balance analysis, engineering cases, and medical diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106728", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Political Optimizer Based Feedforward Neural Network for Classification and Function Approximation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "429", "JournalTitle": "Neural Processing Letters"}, {"Title": "A prescription of methodological guidelines for comparing bio-inspired optimization algorithms", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "100973", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 88312530, "Title": "Once again platform liability: on the edge of the ‘Uber’ and ‘Airbnb’ cases", "Abstract": "Online platforms are considered as very powerful economic agents often tending to obtain oligopolistic or even monopolistic positions in the market. In this respect, the liability of platform operators has been constantly discussed among scholars. The sharpest issue in this respect is whether the platform operator may be held liable towards a platform customer for the violations caused by platform suppliers. Unfortunately, this issue has not been duly addressed yet. However, recently adopted CJEU judgements in Asociación Profesional Elite Taxi v Uber Systems Spain, SL (2017) and in Airbnb Ireland (2019) cases may be helpful in this regard. Although the mentioned judgments do not refer to liability issues directly, they still are indirectly linked to the latter. In this article I analyse the approaches provided by the Court of Justice of the European Union (CJEU) in the mentioned cases and discuss their applicability to private disputes, in particular, to disputes on the liability of platform operators. I suggest that under the current regulatory regime established by European secondary legislation these approaches may be extrapolated to liability issues.", "Keywords": "Business user; Customer; E-commerce; Online platforms; Platform liability; Platform operator; Sharing economy", "DOI": "10.14763/2021.2.1559", "PubYear": 2021, "Volume": "10", "Issue": "2", "JournalId": 74600, "JournalTitle": "Internet Policy Review", "ISSN": "2197-6775", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-B<PERSON>us", "Affiliation": "<PERSON><PERSON><PERSON> Law University, Ukraine"}], "References": []}, {"ArticleId": 88312534, "Title": "Multi-Frequency Interference Detection and Mitigation Using Multiple Adaptive IIR Notch Filter with Lattice Structure", "Abstract": "Radio Frequency Interferences (RFI), such as strong Continuous Wave Interferences (CWI), can influence the Quality of Service (QoS) of communications, increasing the Bit Error Rate (BER) and decreasing the Signal-to-Noise Ratio (SNR) in any wireless transmission, including in a Digital Video Broadcasting (DVB-S2) receiver. Therefore, this paper presents an algorithm for detecting and mitigating a Multi-tone Continuous Wave Interference (MCWI) using a Multiple Adaptive Notch Filter (MANF), based on the lattice form structure. The Adaptive Notch Filter (ANF) is constructed using the second-order IIR NF. The approach consists in developing a robust low-complexity algorithm for removing unknown MCWI. The MANF model is a multistage model, with each stage consisting of two ANFs: the adaptive IIR notch filter Hl(z) and the adaptive IIR notch filter HN(z), which can detect and mitigate CWI. In this model, the ANF is used for estimating the Jamming-to-Signal Ratio (JSR) and the frequency of the interference (w(0)) by using an LMS-based algorithm. The depth of the notch is then adjusted based on the estimation of the JSR. In contrast, the ANF HN(z) is used to mitigate the CW interference. Simulation results show that the proposed ANF is an effective method for eliminating/reducing the effects of MCWI, and yields better system performance than full suppression (kN=1) for low JSR values, and mostly the same performance for high JSR values. Moreover, the proposed can detect low and high JSR and track hopping frequency interference and provides better Bit error ratio (BER) performance compared to the case without an IIR notch filter.", "Keywords": "Component;Radio Frequency Interference (RFI);Multiple Adaptive Notch Filter (MANF);Jamming-to-Signal Ratio (JSR);Quality of Service (QoS);BER", "DOI": "10.4236/jcc.2021.95005", "PubYear": 2021, "Volume": "9", "Issue": "5", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lassena Laboratory, Department of Electrical Engineering, école de Technologie Supérieure (éTS), Montreal, Canada ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Lassena Laboratory, Department of Electrical Engineering, école de Technologie Supérieure (éTS), Montreal, Canada ."}], "References": []}, {"ArticleId": 88312676, "Title": "Slice Function Placement Impact on the Performance of URLLC with Multi-Connectivity", "Abstract": "<p>Network slicing has emerged as a promising technical solution to ensure the coexistence of various 5G services. While the 5G architecture evolution for supporting slicing has been exhaustively studied, the architectural option impacts on RAN resource allocation efficiency remain unclear. This article fills a gap in this area by evaluating the impact of architecture choices on the quality of service of different services in the new 5G ecosystem, focusing on ultra-reliable low-latency communication applications. We propose architectural options based on the placement of the entities responsible for implementing these functions. We then assess their impact on the radio resource allocation flexibility when slices span two radio access technologies with redundant coverage. Our numerical experiments showed that the slice management function placement plays a pivotal role in choosing an adequate radio resource allocation scheme for URLLC slices.</p>", "Keywords": "5G; network slicing; multi-connectivity; quality of service; ultra-reliable low-latency communication (URLLC); resource allocation; redundancy 5G ; network slicing ; multi-connectivity ; quality of service ; ultra-reliable low-latency communication (URLLC) ; resource allocation ; redundancy", "DOI": "10.3390/computers10050067", "PubYear": 2021, "Volume": "10", "Issue": "5", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centrale Supélec, Université Paris Saclay, 91190 Gif-Sur-Yvette, France↑Orange Labs, 92320 <PERSON><PERSON><PERSON><PERSON>, France↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "Salah <PERSON>", "Affiliation": "Centrale Supélec, Université Paris Saclay, 91190 Gif-Sur-Yvette, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Orange Labs, 92320 Châtillon, France"}], "References": []}, {"ArticleId": 88312892, "Title": "Techniques for inferring context-free Lindenmayer systems with genetic algorithm", "Abstract": "Lindenmayer systems (L-systems) are a formal grammar system, where the most notable feature is a set of rewriting rules that are used to replace every symbol in a string in parallel; by repeating this process, a sequence of strings is produced. Some symbols in the strings may be interpreted as instructions for simulation software. Thus, the sequence can be used to model the steps of a process. Currently, creating an L-system for a specific process is done by hand by experts through much effort. The inductive inference problem attempts to infer an L-system from such a sequence of strings generated by an unknown system; this can be thought of as an intermediate step to inferring from a sequence of images. This paper evaluates and analyzes different genetic algorithm encoding schemes and mathematical properties for the L-system inductive inference problem. A new tool, the Plant Model Inference Tool for Deterministic Context-Free L-systems (PMIT-D0L) is implemented based on these techniques. PMIT-D0L is successfully evaluated on 28 known L-systems created by experts with alphabets up to 31 symbols, and PMIT-D0L can successfully infer even the largest of these L-systems in less than a few seconds. It is also evaluated and can correctly infer any system in a larger test set of algorithmically created L-systems with much larger alphabets.", "Keywords": "Lindenmayer systems ; Plant modelling ; Inductive inference ; Genetic Algorithm", "DOI": "10.1016/j.swevo.2021.100893", "PubYear": 2021, "Volume": "64", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Saskatchewan, Saskatoon, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Saskatchewan, Saskatoon, Canada"}], "References": [{"Title": "Inverse Procedural Modeling of Branching Structures by Inferring L-Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 88312896, "Title": "Deep learning-based apple detection using a suppression mask R-CNN", "Abstract": "Robotic apple harvesting has received much research attention in the past few years due to growing shortage and rising cost in labor. One key enabling technology towards automated harvesting is accurate and robust apple detection, which poses great challenges as a result of the complex orchard environment that involves varying lighting conditions and foliage/branch occlusions. This letter reports on the development of a novel deep learning-based apple detection framework named Suppression Mask R-CNN. Specifically, we first collect a comprehensive apple orchard dataset for \"Gala\" and \"Blondee\" apples, using a color camera, under different lighting conditions (overcast and front lighting vs. back lighting). We then develop a novel suppression Mask R-CNN for apple detection, in which a suppression branch is added to the standard Mask R-CNN to suppress non-apple features generated by the original network. Comprehensive evaluations are performed, which show that the developed suppression Mask R-CNN network outperforms state-of-the-art models with a higher F1-score of 0.905 and a detection time of 0.25 second per frame on a standard desktop computer.", "Keywords": "Vision system ; Fruit detection ; Deep learning ; Robotic harvesting ; Image segmentation", "DOI": "10.1016/j.patrec.2021.04.022", "PubYear": 2021, "Volume": "147", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Michigan State University, East Lansing, MI, 48824, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Michigan State University, East Lansing, MI, 48824, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Michigan State University, East Lansing, MI, 48824, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Agricultural Research Service (ARS), U.S. Department of Agriculture (USDA), East Lansing, MI, 48824, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Michigan State University, East Lansing, MI, 48824, USA"}], "References": [{"Title": "Faster R-CNN for multi-class fruit detection using a robotic vision system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "107036", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 88312902, "Title": "Selective detection of peroxycarboxylic acid by thiocarbonyl compounds in aqueous solution", "Abstract": "In this study, two fluorescent probes for efficient detection of peroxycarboxylic acid were designed and synthesized. An OFF–ON probe THPY was composed of Pyonin B skeleton and a thioketone group, while the ratiometric probe THCA was based on 4-methylcoumarin skeleton with a thioester group. Both of them showed good selectivity and sensitivity toward peroxycarboxylic acid over other analytes. Especially, detection systems got good operation in aqueous solution containing common ions. The detection mechanism was achieved via thiocarbonyl oxidative desulfurization, which eliminated the inter system crossing (ISC) effect caused by thiocarbonyl structure. The detection limits of THPY were calculated to be 534 nM for peracetic acid (PAA) and 73.6 nM for m-chloroperoxybenzoic acid (mCPBA), and those of THCA were 216 nM for PAA and 230 nM for mCPBA, respectively. The satisfactory results were obtained in the test of actual samples and test strips. All in all, the research produced an effective and stable detection system for peroxycarboxylic acid, and it also proved the possibility of designing probe by controlling ISC effect.", "Keywords": "Fluorescence probe ; Thiocarbonyl oxidation desulfurization ; Inter system crossing (ISC) ; Peroxycarboxylic acid sensor", "DOI": "10.1016/j.snb.2021.130081", "PubYear": 2021, "Volume": "343", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry, Chemical Engineering and Material Science, Soochow University, 199 Ren’Ai Road, Suzhou, 215123, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry, Chemical Engineering and Material Science, Soochow University, 199 Ren’Ai Road, Suzhou, 215123, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry, Chemical Engineering and Material Science, Soochow University, 199 Ren’Ai Road, Suzhou, 215123, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry, Chemical Engineering and Material Science, Soochow University, 199 Ren’Ai Road, Suzhou, 215123, China;Jiangsu Key Laboratory of Medical Optics, Suzhou Institute of Biomedical Engineering and Technology, Chinese Academy of Sciences, Suzhou, 215163, China"}], "References": [{"Title": "A novel red-emissive probe for colorimetric and ratiometric detection of hydrazine and its application in plant imaging", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "307", "Issue": "", "Page": "127640", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 88312904, "Title": "Identification of cell-type-specific marker genes from co-expression patterns in tissue samples", "Abstract": "Motivation <p>Marker genes, defined as genes that are expressed primarily in a single-cell type, can be identified from the single-cell transcriptome; however, such data are not always available for the many uses of marker genes, such as deconvolution of bulk tissue. Marker genes for a cell type, however, are highly correlated in bulk data, because their expression levels depend primarily on the proportion of that cell type in the samples. Therefore, when many tissue samples are analyzed, it is possible to identify these marker genes from the correlation pattern.</p> Results <p>To capitalize on this pattern, we develop a new algorithm to detect marker genes by combining published information about likely marker genes with bulk transcriptome data in the form of a semi-supervised algorithm. The algorithm then exploits the correlation structure of the bulk data to refine the published marker genes by adding or removing genes from the list.</p> Availability and implementation <p>We implement this method as an R package markerpen, hosted on CRAN (https://CRAN.R-project.org/package=markerpen).</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab257", "PubYear": 2021, "Volume": "37", "Issue": "19", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics and Data Science, Carnegie Mellon University, Pittsburgh, PA 15213, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biostatistics, University of Pittsburgh, Pittsburgh, PA 15261, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Data Science, Carnegie Mellon University, Pittsburgh, PA 15213, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Data Science, Carnegie Mellon University, Pittsburgh, PA 15213, USA;Computational Biology Department, Carnegie Mellon University, Pittsburgh, PA 15213, USA"}], "References": [{"Title": "Using multiple measurements of tissue to estimate subject- and cell-type-specific gene expression", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "782", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 88312989, "Title": "An Expedient Way to Organize Passwords﻿", "Abstract": "There are billions of passwords in the world today, with more being created every hour of every day, making them a bane of the modern era. The usage of passwords is becoming more common despite the inadequacies [1]. This is making it more tedious and overwhelming for the users to manage, with the passwords being long and unique. In this paper, we propose a solution to securely manage and organize passwords while requiring the user to keep track of only a single password. As our solution is in the form of a browser extension, there is no need for server-side changes. Unlike other password managers, our extension is a lightweight application and is highly resistant to brute force attacks. We discuss the need for a password manager, the construction of our extension along with the security overview.", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i05.002", "PubYear": 2021, "Volume": "10", "Issue": "5", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "K Nanda Kishore﻿", "Affiliation": ""}], "References": []}, {"ArticleId": 88312992, "Title": "Agro Expert Using Google API﻿", "Abstract": "Agriculture is the backbone of Indian economy. Mostly crops are loss due to the erroneous selection of the crop, climate change. The farmers are generally not aware of the requirements of the crops. i.e. The minerals, soil moisture & other soil requirements one more problem that a farmer generally encounters is the pest & diseases that can affect the crops they grow which they are generally unaware of in an early stage. This are the problems of farmer is addressed in our paper and we have tried to solve it with the help of Agro Expert using Google API system by the help of our model. We predict the suitable crop to the farmers and also detect the pest and may affect as suggest the pest control technique. In this paper we have applied collaborative filtering algorithm, aprori algorithm, linear regration prediction technique.", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i05.001", "PubYear": 2021, "Volume": "10", "Issue": "5", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>.﻿", "Affiliation": ""}], "References": []}, {"ArticleId": 88313249, "Title": "Bootstrap aggregation ensemble learning-based reliable approach for software defect prediction by using characterized code feature", "Abstract": "<p>To ensure software quality, software defect prediction plays a prominent role for the software developers and practitioners. Software defect prediction can assist us with distinguishing software defect modules and enhance the software quality. In present days, many supervised machine learning algorithms have proved their efficacy to identify defective modules. However, those are limited to prove their major significance due to the limitations such as the adaptation of parameters with the environment and complexity. So, it is important to develop a key methodology to improve the efficiency of the prediction module. In this paper, an ensemble learning technique called Bootstrap aggregating has been proposed for software defect prediction object-oriented modules. The proposed method's accuracy, recall, precision, F -measure, and AUC-ROC efficiency were compared to those of many qualified machine learning algorithms. Simulation results and performance comparison are evident that the proposed method outperformed well compared to other approaches.</p>", "Keywords": "Ensemble learning; Software defect prediction; Software reliability; Machine learning", "DOI": "10.1007/s11334-021-00399-2", "PubYear": 2021, "Volume": "17", "Issue": "4", "JournalId": 18069, "JournalTitle": "Innovations in Systems and Software Engineering", "ISSN": "1614-5046", "EISSN": "1614-5054", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, <PERSON><PERSON> University of Technology, Burla, India"}, {"AuthorId": 2, "Name": "H. S. <PERSON>", "Affiliation": "Department of Information Technology, <PERSON><PERSON> University of Technology, Burla, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Aditya Institute of Technology and Management (AITAM), Tekkali, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Application, <PERSON><PERSON> University of Technology, Burla, India"}], "References": [{"Title": "Advancement from neural networks to deep learning in software effort estimation: Perspective of two decades", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "", "Page": "100288", "JournalTitle": "Computer Science Review"}, {"Title": "COSTE: Complexity-based OverSampling TEchnique to alleviate the class imbalance problem in software defect prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "", "Page": "106432", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 88313344, "Title": "Laptop displays performance: Compliance assessment with visual ergonomics requirements", "Abstract": "The use of display devices such as smartphones, tablets, laptops is now massive and continuous in everyday life. It, therefore, becomes increasingly important to be aware of the performance of these devices, not only in terms of the tasks to be performed but also in terms of interaction with humans and therefore knows any possible effect on the ergonomics of vision. Following previous research activities conducted by the authors on the assessment of the visual ergonomics at video display terminal workstations, the aim of this study is to evaluate the ergonomics of human-system interaction of laptop displays. In details, a sample of 57 laptop displays is analyzed in accordance with the requirements of the EN ISO 9241-3xx series of international standards related to the display luminance, luminance ratio, contrast non-uniformity. An extensive luminance measurement campaign was carried out using a special pattern that allowed to measure the luminance in 13 different areas of the displays. The results obtained with this activity showed a great luminance variability between different displays. Almost all the displays are able to emit high levels of display luminance, and almost all the displays meet the requirement of contrast non-uniformity. However, several devices did not meet the recommended values of luminance ratio. Furthermore, the authors created a simplified graph to allow a rapid evaluation of the performance of the displays. This method could be periodically used in practice in order to evaluate the residual performance level.", "Keywords": "Laptop display performance ; Visual ergonomics requirements ; Display luminance ; Luminance measurements ; Luminance uniformity ; Human-system interaction", "DOI": "10.1016/j.displa.2021.102019", "PubYear": 2021, "Volume": "68", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Energy, Systems, Territory and Constructions Engineering (DESTeC), University of Pisa, Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Energy, Systems, Territory and Constructions Engineering (DESTeC), University of Pisa, Pisa, Italy;Corresponding author at: Department of Energy, Systems, Territory and Constructions Engineering (DESTeC), Largo Lucio <PERSON> snc, 56122 Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Energy, Systems, Territory and Constructions Engineering (DESTeC), University of Pisa, Pisa, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Architecture, Izmir Institute of Technology, Turkey"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Astronautical, Electrical and Energy Engineering (DIAEE), Sapienza University of Rome, Rome, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Astronautical, Electrical and Energy Engineering (DIAEE), Sapienza University of Rome, Rome, Italy"}], "References": [{"Title": "A prospective longitudinal study of mobile touch screen device use and musculoskeletal symptoms and visual health in adolescents", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "85", "Issue": "", "Page": "103028", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 88313346, "Title": "Feasibility of Longest Prefix Matching using Learned Index Structures", "Abstract": "<p>This paper revisits longest prefix matching in IP packet forwarding because an emerging data structure, learned index, is recently presented. A learned index uses machine learning to associate key-value pairs in a key-value store. The fundamental idea to apply a learned index to an FIB is to simplify the complex longest prefix matching operation to a nearest address search operation. The size of the proposed FIB is less than half of an existing trie-based FIB while it achieves the computation speed nearly equal to the trie-based FIB. Moreover, the computation speed of the proposal is independent of the length of IP prefixes, unlike trie-based FIBs.</p>", "Keywords": "forwarding information base; longest prefix matching; packet forwarding", "DOI": "10.1145/3466826.3466842", "PubYear": 2021, "Volume": "48", "Issue": "4", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osaka University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "KDDI Research, Inc."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Osaka University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "KDDI Research, Inc."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Osaka University"}], "References": []}, {"ArticleId": 88313349, "Title": "Blockchain Privacy Through Merge Avoidance and Mixing Services", "Abstract": "<p>Cryptocurrencies typically aim at preserving the privacy of their users. Different cryptocurrencies preserve privacy at various levels, some of them requiring users to rely on strategies to raise the privacy level to their needs. Among those strategies, we focus on two of them: merge avoidance and mixing services. Such strategies may be adopted on top of virtually any blockchain-based cryptocurrency. In this paper, we show that whereas optimal merge avoidance leads to an NP-hard optimization problem, incentive-compatible mixing services are subject to a certain class of impossibility results. Together, our results contribute to the body of work on fundamental limits of privacy mechanisms in blockchainbased cryptocurrencies.</p>", "Keywords": "", "DOI": "10.1145/3466826.3466831", "PubYear": 2021, "Volume": "48", "Issue": "4", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "UNIRIO, Rio de Janeiro, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "UFRJ, Rio de Janeiro, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "UFRJ, Rio de Janeiro, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UNIRIO, Rio de Janeiro, Brazil"}], "References": []}, {"ArticleId": 88313534, "Title": "GSCFN: A graph self-construction and fusion network for semi-supervised brain tissue segmentation in MRI", "Abstract": "In this paper, we propose a graph self-construction and fusion network (GSCFN) for semi-supervised brain tissue segmentation in Magnetic Resonance Imaging (MRI) by fusing multiple types of image features. Compared to the use of a single feature, various features bring complementary information and can contribute to a better graph representation with a great discriminative power increase. But to do so, two problems need to be solved. The first one consists in effectively inferring a graph from a Magnetic Resonance (MR) image so as to implicitly encode the segmentation information and the second in fully leveraging various features. To solve both problems, we propose an original brain MR image semi-supervised segmentation framework, called graph self-construction and fusion network. This one relies on two parts. In the first one, a graph self-construction network is utilized to obtain various graph representations of an MR image depending on different features. In the second, a multi-graph convolution network is proposed for the fusion of multiple graphs and features as well as for the classification of supervoxels which are treated as graph nodes. Experiments on the BrainWeb18 dataset and the Internet Brain Segmentation Repository 18 dataset validate the superiority of our scheme compared with approaches based on a single feature type, and some other state-of-the-art methods. The ablation experiment indicates that the proposed GSCFN can produce more accurate and reliable segmentation by seamlessly integrates multiple types of features.", "Keywords": "Magnetic resonance imaging ; Brain tissue segmentation ; Supervoxels ; Graph convolutional network", "DOI": "10.1016/j.neucom.2021.05.047", "PubYear": 2021, "Volume": "455", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Lab of Image Science and Technology, School of Computer Science and Engineering, Southeast University, Nanjing 210096, China;Key Laboratory of Computer Network and Information Integration, Southeast University, Ministry of Education;Centre de Recherche en Information Biomédicale Sino-français, Nanjing 210096, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lab of Image Science and Technology, School of Computer Science and Engineering, Southeast University, Nanjing 210096, China;Key Laboratory of Computer Network and Information Integration, Southeast University, Ministry of Education;Centre de Recherche en Information Biomédicale Sino-français, Nanjing 210096, China"}, {"AuthorId": 3, "Name": "Youyong Kong", "Affiliation": "Lab of Image Science and Technology, School of Computer Science and Engineering, Southeast University, Nanjing 210096, China;Key Laboratory of Computer Network and Information Integration, Southeast University, Ministry of Education;Centre de Recherche en Information Biomédicale Sino-français, Nanjing 210096, China;International Joint Research Laboratory of Information Display and Visualization, Southeast University, Ministry of Education, Nanjing 210096, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lab of Image Science and Technology, School of Computer Science and Engineering, Southeast University, Nanjing 210096, China;Key Laboratory of Computer Network and Information Integration, Southeast University, Ministry of Education;Centre de Recherche en Information Biomédicale Sino-français, Nanjing 210096, China;International Joint Research Laboratory of Information Display and Visualization, Southeast University, Ministry of Education, Nanjing 210096, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Optics and Electronics, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 6, "Name": "Huazhong Shu", "Affiliation": "Lab of Image Science and Technology, School of Computer Science and Engineering, Southeast University, Nanjing 210096, China;Key Laboratory of Computer Network and Information Integration, Southeast University, Ministry of Education;Centre de Recherche en Information Biomédicale Sino-français, Nanjing 210096, China;International Joint Research Laboratory of Information Display and Visualization, Southeast University, Ministry of Education, Nanjing 210096, China;Corresponding author"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IMT Atlantique, Inserm, LaTIM UMR1101, Brest 29000, France"}], "References": []}, {"ArticleId": 88313538, "Title": "Polynomial-time algorithms to solve the single-item capacitated lot sizing problem with a 1-breakpoint all-units quantity discount", "Abstract": "This study investigates the single-item capacitated lot sizing problem considering a 1-breakpoint all-units quantity discount. Assuming the fixed ordering cost and the undiscounted unit purchase price to be non-increasing over the periods, the lot sizing problem under study is a special case of that with piecewise concave production cost function investigated by <PERSON><PERSON> et al. (2014) . <PERSON><PERSON>’s DP algorithm solves our problem with a time complexity of O(T<sup>7</sup>) , where T denotes the number of periods. It, however, fails in the case of large-sized instances in acceptable runtimes due to its higher complexity. Hence, the present study is a response to the need for more efficient algorithms to overcome this shortcoming. First, some properties of the optimal solution are proved. Second, these properties are exploited in an implicit enumeration exact algorithm. Third, certain speed-up techniques are employed to reduce the time complexity of the proposed algorithm from O(T<sup>5</sup>) to O(T<sup>4</sup>) . Finally, a heuristic algorithm is presented for the problem. The proposed algorithms are compared with <PERSON><PERSON>’s DP algorithm and the commercial solver used to solve some test problems. Based on the runtimes observed, the exact algorithms proposed in this study are found to outperform both <PERSON><PERSON>’s DP one and the commercial solver. Moreover, the heuristic algorithm developed herein is found to be faster than both the exact algorithms in finding optimal solutions to most problem instances.", "Keywords": "Capacitated lot sizing problem ; All-units quantity discount ; Polynomial exact algorithm ; Implicit enumeration algorithm", "DOI": "10.1016/j.cor.2021.105373", "PubYear": 2021, "Volume": "134", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Isfahan University of Technology, Isfahan 84156-83111, Iran;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Isfahan University of Technology, Isfahan 84156-83111, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Isfahan University of Technology, Isfahan 84156-83111, Iran"}], "References": [{"Title": "Joint optimization of dynamic pricing and lot-sizing decisions with nonlinear demands: Theoretical and computational analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "115", "Issue": "", "Page": "104862", "JournalTitle": "Computers & Operations Research"}, {"Title": "Dynamic lot-sizing model under perishability, substitution, and limited storage capacity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "122", "Issue": "", "Page": "104978", "JournalTitle": "Computers & Operations Research"}, {"Title": "A mixed integer programming formulation for the stochastic lot sizing problem with controllable processing times", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "132", "Issue": "", "Page": "105302", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 88313546, "Title": "An approach for offloading in mobile cloud computing to optimize power consumption and processing time", "Abstract": "Smart phones are widely used but they still suffer from constrained resources in term of their processing capabilities, memory capacity and battery capacity. Mobile Cloud Computing (MCC) has recently emerged to overcome the shortcomings of the standalone smartphones, where Cloud Computing (CC) is leveraged for processing capabilities and memory capacity, while the smartphone would use minimal battery power. However, task offloading from smartphones to the cloud remains an active area of research, to achieve optimized performance and resource utilization and enhance the overall Quality of Service (QoS). In this paper, we propose an approach where two servers are used alternatively, First Upload Round (FUR) offloading and Second Upload Round (SUR) offloading; both supported by a decision engine system. This approach shows better performance and less energy consumption over competing state-of-the-art approaches. The proposed approach shows reduction of the power consumption, 4G is the most improved, for example, for the file size 10 Mb the reduction of the power consumption was 93 % on 4G, compared to Wi-Fi 85 %.", "Keywords": "Cloudlet ; Mobile cloud computing ; Offloading power consumption ; Processing time", "DOI": "10.1016/j.suscom.2021.100562", "PubYear": 2021, "Volume": "31", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Digital Tech, Staffordshire University, UK;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Built Environment, Anglia Ruskin University, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Sharjah, United Arab Emirates;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Digital Tech, Staffordshire University, UK"}], "References": [{"Title": "Binary cuckoo search metaheuristic-based supercomputing framework for human behavior analysis in smart home", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "4", "Page": "2479", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 88313628, "Title": "Foreword to the Special Issue on Configurable Systems", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10664-021-09964-6", "PubYear": 2021, "Volume": "26", "Issue": "4", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Univ. Lille, CNRS, Inria, Centrale Lille, UMR 9189 CRIStAL, Lille, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Software Systems Engineering, Johannes Kepler University Linz, Linz, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Software Engineering and Programming Languages, University of Ulm, Ulm, Germany"}], "References": []}, {"ArticleId": 88313632, "Title": "An empirical study on the use of SZZ for identifying inducing changes of non-functional bugs", "Abstract": "<p>Non-functional bugs, e.g., performance bugs and security bugs, bear a heavy cost on both software developers and end-users. For example, IBM estimates the cost of a single data breach to be millions of dollars. Tools to reduce the occurrence, impact, and repair time of non-functional bugs can therefore provide key assistance for software developers racing to fix these issues. Identifying bug-inducing changes is a critical step in software quality assurance. In particular, the SZZ approach is commonly used to identify bug-inducing commits. However, the fixes to non-functional bugs may be scattered and separate from their bug-inducing locations in the source code. The nature of non-functional bugs may therefore make the SZZ approach a sub-optimal approach for identifying bug-inducing changes. Yet, prior studies that leverage or evaluate the SZZ approach do not consider non-functional bugs, leading to potential bias on the results. In this paper, we conduct an empirical study on the results of the SZZ approach when used to identify the inducing changes of the non-functional bugs in the NFBugs dataset. We eliminate a majority of the bug-inducing commits as they are not in the same method or class level. We manually examine whether each identified bug-inducing change is indeed the correct bug-inducing change. Our manual study shows that a large portion of non-functional bugs cannot be properly identified by the SZZ approach. By manually identifying the root causes of the falsely detected bug-inducing changes, we uncover root causes for false detection that have not been found by previous studies. We evaluate the identified bug-inducing changes based on three criteria from prior research, i.e., the earliest bug appearance, the future impact of changes, and the realism of bug introduction. We find that prior criteria may be irrelevant for non-functional bugs. Our results may be used to assist in future research on non-functional bugs, and highlight the need to complement SZZ to accommodate the unique characteristics of non-functional bugs.</p>", "Keywords": "Bug inducing changes SZZ; Non-functional bugs; Mining software repositories", "DOI": "10.1007/s10664-021-09970-8", "PubYear": 2021, "Volume": "26", "Issue": "4", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Concordia University, Montreal, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Concordia University, Montreal, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Science and Electrical Engineering, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Concordia University, Montreal, Canada"}], "References": [{"Title": "How different are different diff algorithms in Git?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "1", "Page": "790", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 88313707, "Title": "Polyadic Cyclic Codes over a Non-Chain Ring", "Abstract": "Let f(u) and g(v) be two polynomials of degree k and l respectively, not both linear which split into distinct linear factors over Fq. Let be a finite commutative non-chain ring. In this paper, we study polyadic codes and their extensions over the ring R. We give examples of some polyadic codes which are optimal with respect to Griesmer type bound for rings. A Gray map is defined from which preserves duality. The Gray images of polyadic codes and their extensions over the ring R lead to construction of self-dual, isodual, self-orthogonal and complementary dual (LCD) codes over Fq. Some examples are also given to illustrate this.", "Keywords": "Polyadic Codes and Their Extensions;Griesmer Bound;Gray Map;Self-Dual and Self-Orthogonal Codes;Isodual Codes;LCD Codes", "DOI": "10.4236/jcc.2021.95004", "PubYear": 2021, "Volume": "9", "Issue": "5", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Advanced Study in Mathematics, Panjab University, Chandigarh, India ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Advanced Study in Mathematics, Panjab University, Chandigarh, India ."}], "References": []}, {"ArticleId": 88313724, "Title": "The promise of financial services regulatory theory to address disinformation in content recommender systems", "Abstract": "This article argues that the European regulatory approach to disinformation online is stymied by inappropriate regulatory theories. On that basis, this article seeks to advance an alternative theoretical approach, inspired by the contemporary European paradigm of financial services regulation. It outlines how the key theories underpinning financial services regulation could engender policy solutions that are both more rights-protective and more responsive to the role played by content recommender systems in compounding the policy problem of disinformation online. It assesses the extent to which these alternative regulatory theories manifest in the draft EU Digital Services Act.", "Keywords": "Content recommender systems; Digital Services Act; Disinformation; Regulatory theory", "DOI": "10.14763/2021.2.1558", "PubYear": 2021, "Volume": "10", "Issue": "2", "JournalId": 74600, "JournalTitle": "Internet Policy Review", "ISSN": "2197-6775", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 88313755, "Title": "Toward a Lingua Franca for Deterministic Concurrent Systems", "Abstract": "<p>Many programming languages and programming frameworks focus on parallel and distributed computing. Several frameworks are based on actors, which provide a more disciplined model for concurrency than threads. The interactions between actors, however, if not constrained, admit nondeterminism. As a consequence, actor programs may exhibit unintended behaviors and are less amenable to rigorous testing. We show that nondeterminism can be handled in a number of ways, surveying dataflow dialects, process networks, synchronous-reactive models, and discrete-event models. These existing approaches, however, tend to require centralized control, pose challenges to modular system design, or introduce a single point of failure. We describe “reactors,” a new coordination model that combines ideas from several of these approaches to enable determinism while preserving much of the style of actors. Reactors promote modularity and allow for distributed execution. By using a logical model of time that can be associated with physical time, reactors also provide control over timing. Reactors also expose parallelism that can be exploited on multicore machines and in distributed configurations without compromising determinacy.</p>", "Keywords": "", "DOI": "10.1145/3448128", "PubYear": 2021, "Volume": "20", "Issue": "4", "JournalId": 17571, "JournalTitle": "ACM Transactions on Embedded Computing Systems", "ISSN": "1539-9087", "EISSN": "1558-3465", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Berkeley, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "TU Dresden, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Texas at Dallas, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of California, Berkeley, USA"}], "References": []}, {"ArticleId": 88313849, "Title": "WGLSM: An end-to-end line matching network based on graph convolution", "Abstract": "Line matching plays an essential role in Structure from Motion (SFM) and Simultaneous Localization and Mapping (SLAM), especially in low-texture scenes, where feature points are hard to be detected. In this paper, we present a new method by combining Convolutional Neural Networks and Graph Convolutional Networks to match line segments in pairs of images. We design a graph-based method to predict the assignment matrix of two feature sets with solving a relaxed optimal transport problem. In contrast to handcrafted line matching algorithms, our approach learns the line segment features and performs matching simultaneously through end-to-end weakly supervised training. The experiment results show that our method outperforms the state-of-the-art techniques and is robust to various image transformations. Besides, the generalization experiment illustrates that our method has good generalization ability without fine-tuning. The code of our work is available at https://github.com/mameng1/GraphLineMatching .", "Keywords": "Deep learning ; Line segment matching ; Graph Convolutional Networks ; Learnable line descriptor", "DOI": "10.1016/j.neucom.2021.04.125", "PubYear": 2021, "Volume": "453", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Xidian University, Xi’an, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 4, "Name": "Changshuai Cai", "Affiliation": "School of Telecommunication Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Xidian University, Xi’an, China"}], "References": []}, {"ArticleId": 88313911, "Title": "GridTools: A framework for portable weather and climate applications", "Abstract": "Weather forecasts and climate projections are of tremendous importance for economical and societal reasons. Software implementing weather and climate models is complex to develop and hard to maintain, and requires a large range of different competencies, ranging from environmental sciences, numerical methods, to low level programming. In order to manage this complexity we developed GridTools, a set of software libraries targeted at weather and climate model developers. By separating the model description (front-end) from its efficient implementation on the target platform (back-end), GridTools allows the implementation of performance-portable simulations on a variety of platforms, such as multicore and GPU-accelerated systems. We discuss the application of GridTools to the regional weather and climate model COSMO and show performance results on simple benchmarks as well as on COSMO.", "Keywords": "C++ ; Weather ; Climate ; Library ; Embedded DSL", "DOI": "10.1016/j.softx.2021.100707", "PubYear": 2021, "Volume": "15", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Swiss National Supercomputing Centre, ETH Zurich, Lugano, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Swiss National Supercomputing Centre, ETH Zurich, Lugano, Switzerland;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Swiss National Supercomputing Centre, ETH Zurich, Lugano, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Federal Institute of Meteorology and Climatology MeteoSwiss, Zurich, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Swiss National Supercomputing Centre, ETH Zurich, Lugano, Switzerland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Swiss National Supercomputing Centre, ETH Zurich, Lugano, Switzerland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Federal Institute of Meteorology and Climatology MeteoSwiss, Zurich, Switzerland;Vulcan Inc, Seattle, United States of America"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Swiss National Supercomputing Centre, ETH Zurich, Lugano, Switzerland"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Swiss National Supercomputing Centre, ETH Zurich, Lugano, Switzerland;Institute for Theoretical Physics, ETH, Zurich, Switzerland"}], "References": []}, {"ArticleId": 88314219, "Title": "ДОСЛІДЖЕННЯ МЕТОДІВ ЗБЕРЕЖЕННЯ ІНФОРМАЦІЇ У СХОВИЩАХ ДАНИХ", "Abstract": "Робота присвячена аналізу теоретичних та методологічних основ побудови ефективного сховища даних для найбільш раціонального збереження інформації. Методи дослідження. В роботі використані такі методи наукових досліджень: експеримент, аналіз результатів діяльності. Із теоретичних методів дослідження використані: аналіз, синтез, порівняння. Основні результати дослідження. Досліджено способи і методи збереження інформації у сховищах даних. Розгля- нуто основні типи програмно-апаратної архітектури сховищ даних, визначено їх переваги та недоліки. Результатом дослідження стала класифікація методів збереження інформації, яка допомагає проектувальнику створити сховище даних з найменшими витратами. Представлені основні компоненти інформаційного сховища даних, що дозволило побудувати типову структуру сховища даних. Проведено порівняльний аналіз методів моделювання сховищ даних, який дозволяє користувачу скористатися на практиці більш ефективним методом при проектуванні та розробці схо- вищ даних для збереження інформації в організаціях і на підприємствах [1]. Наукова новизна. Розвиток інформаційних ресурсів і засобів доступу до них, стрімкий розвиток україномов- ного контенту Інтернету є факторами, які змінили спосіб і підхід до збереження великих обсягів інформації. Нау- ковою новизною цієї теми є використання сучасних сховищ даних для збереження інформації в вищих навчальних закладах освіти. Ця тема є актуальною оскільки якісне збереження інформації впливає на організацію роботи не лише окремого підрозділу організації, а й усієї організації. Швидкість і миттєва можливість звернення до потрібної інформації визна- чає якість роботи користувачів в організації, непрямим чином впливає на якість і собівартість продукції, що випус- кається або послуги, які надаються. Практична значимість. Збільшення, з кожним роком, обсягів інформації, яку потрібно зберігати і обробляти при- звело до необхідності проектування та розробки сховищ даних. Ця тема є дуже актуальною для вивчення тепер, коли сховища даних усе активніше починають впроваджуватися у різних сферах людської діяльності, таких як освіта, сіль- ське господарство, навчання, медицина, економіка, зв’язок, безпека охоронних систем, обробка інформації тощо. Проведена класифікація може стати основою для розширення кількості сфер використання сховищ даних. Результати дослідження можна використовувати у навчальному процесі для наочного представлення переваг та недоліків мето- дів збереження інформації в сховищах даних, принципів їх використання у промисловій та не промисловій сферах</p>", "Keywords": "багатовимірне моделювання;моделювання тимчасових даних;моделювання «звід даних»;схема  «зірка»;схема «сніжинка»;схеми з декількома таблицями фактів", "DOI": "10.35546/2313-0687.2020.27.54-68", "PubYear": 2020, "Volume": "", "Issue": "27", "JournalId": 65739, "JournalTitle": "Problems of information technologies", "ISSN": "1998-7005", "EISSN": "2313-0687", "Authors": [{"AuthorId": 1, "Name": "Раїса Захарченко", "Affiliation": "Херсонський національний технічний університет"}, {"AuthorId": 2, "Name": "Леон<PERSON>д Захарченко", "Affiliation": "Херсонський національний технічний університет"}, {"AuthorId": 3, "Name": "Тетяна Кірюшатова", "Affiliation": "Херсонський національний технічний університет"}, {"AuthorId": 4, "Name": "<PERSON>г<PERSON><PERSON>о", "Affiliation": "Херсонський національний технічний університет"}], "References": []}, {"ArticleId": 88314223, "Title": "ДОСЛІДЖЕННЯ МЕТОДІВ ЗМЕНШЕННЯ НАВЧАЛЬНОЇ ВИБІРКИ ДАНИХ", "Abstract": "Значна кількість методів машинного навчання мають обмеження на об’єм даних з якими вони можуть працювати. Зазвичай ці обмеження проявляють себе як надмірне споживання розрахункових ресурсів, або пам’яті. Так, як більшість алгоритмів машинного навчання мають розрахункову складність більшу ніж O(n), при значному об’єму вхідних даних, ці алгоритми не зможуть знайти рішення за розумний час. Зменшення навчальної вибірки для цих алгоритмів підвищить швидкість їх роботі пропорційно до розрахункової складності алгоритмів. У статі проаналізовано методи зменшення навчальної вибірки для деяких алгоритмів машинного навчання. Виміряно вплив зменшення навчальної вибірки на швидкодію та точність алгоритмів машинного навчання. Метою даного дослідження є дослідження впливу різних алгоритмів зменшення начальної вибірки на взаємну точність різних моделей машинного навчання при прогнозуванні сонячної інсоляції. Основні результати дослідження. Досліджено вплив начальної вибірки при наявності надлишкового об’єму даних на швидкодію алгоритмів машинного навчання, та на їх точність. Виміряно вплив прокляття розміреності при вико- ристанні значно зменшеної навчальної вибірки. Науковою новизною є порівняння методів зменшення навчальної вибірки для передбачення сонячної інсоляції.", "Keywords": "машинне навчання;швидкодія;дані;сонячна інсоляція", "DOI": "10.35546/2313-0687.2020.27.98-107", "PubYear": 2020, "Volume": "", "Issue": "27", "JournalId": 65739, "JournalTitle": "Problems of information technologies", "ISSN": "1998-7005", "EISSN": "2313-0687", "Authors": [{"AuthorId": 1, "Name": "Яків Повод", "Affiliation": "Херсонський національний технічний університет"}, {"AuthorId": 2, "Name": "Володи<PERSON><PERSON><PERSON>", "Affiliation": "Херсонський національний технічний університет"}], "References": []}, {"ArticleId": 88314264, "Title": "Parental Acceptance of Children’s Storytelling Robots: A Projection of the Uncanny Valley of AI", "Abstract": "<p>Parent–child story time is an important ritual of contemporary parenting. Recently, robots with artificial intelligence (AI) have become common. Parental acceptance of children’s storytelling robots, however, has received scant attention. To address this, we conducted a qualitative study with 18 parents using the research technique design fiction. Overall, parents held mixed, though generally positive, attitudes toward children’s storytelling robots. In their estimation, these robots would outperform screen-based technologies for children’s story time. However, the robots’ potential to adapt and to express emotion caused some parents to feel ambivalent about the robots, which might hinder their adoption. We found three predictors of parental acceptance of these robots: context of use, perceived agency, and perceived intelligence. Parents’ speculation revealed an uncanny valley of AI: a nonlinear relation between the human likeness of the artificial agent’s mind and affinity for the agent. Finally, we consider the implications of children’s storytelling robots, including how they could enhance equity in children’s access to education, and propose directions for research on their design to benefit family well-being.</p>", "Keywords": "artificial intelligence; design fiction; Parent-Child Storytelling; social robotics; technology acceptance; uncanny valley", "DOI": "10.3389/frobt.2021.579993", "PubYear": 2021, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "United States"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "United States"}], "References": [{"Title": "The uncanny of mind in a machine: Humanoid robots as tools, agents, and experiencers", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "274", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Combating COVID-19—The role of robotics in managing public health and infectious diseases", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "40", "Page": "eabb5589", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 88314302, "Title": "ВДОСКОНАЛЕННЯ ВІЗУАЛІЗАЦІЇ 3D МОДЕЛІ СЦЕНАРІЇВ РОЗВИТКУ НАДЗВИЧАЙНИХ СИТУАЦІЙ ІЗ ЗАСТОСУВАННЯМ VR ШОЛОМУ OSVR HDR2", "Abstract": "В даний час тривимірна візуалізація активно застосовується в різних сферах людської діяльності, у тому числі для моделювання надзвичайних ситуації (НС). Існує значна кількість програмних продуктів для 3D моде- лювання різних НС. Оскільки НС виникає несподівано і розвивається спонтанно, діяти потрібно оперативно і точно. Маючи 3D-мо- дель потенційно небезпечного об’єкта можна оцінити зону можливих руйнувань, змоделювати саму НС; а також розробити заходи щодо запобігання та план ліквідації НС, стосовно до даного конкретного об’єкту чи місця, де прогнозується НС. Методи дослідження. В роботі використані методи наукових досліджень такі як: експеримент, аналіз результатів діяльності. Із теоретичних методів дослідження використані: аналіз, синтез, порівняння. Основні результати дослідження. Створена 3D модель та візуалізація проведення аварійно рятувальних робіт в умовах НС, яка забезпечує можливість прийняття оптимального рішення відносно: оперативного доступу до об’єк- тів електропостачання, газопостачання, водопостачання. Запропонований спосіб візуалізації сценаріїв розвитку над- звичайних ситуацій із застосуванням шолому OSVR HDR2. Наукова новизна. Створена 3D модель та візуалізація проведення аварійно рятувальних робіт в умовах НС, яка забезпечує можливість прийняття оптимального рішення із застосуванням шолому OSVR HDR2. Створена методика і система 3D-моделювання проектних рішень, яка дозволяє оцінювати адекватність отриманих проектних рішень вихідним вимогам на створення 3D ГІС і можливість корекції для їх поліпшення. Практична значимість. Основні результати дослідження представляють цінність при проектуванні і створенні 3D ГІС в областях застосування, пов’язаних з необхідністю відображення ситуаційної обстановки на основі запропо- нованих в статті методик, алгоритмів, засобів автоматизації проектування і моделювання.", "Keywords": "3D ГІС модель;візуалізація;прийняття оптимального рішення;шолом OSVR HDR2", "DOI": "10.35546/2313-0687.2020.27.89-97", "PubYear": 2020, "Volume": "", "Issue": "27", "JournalId": 65739, "JournalTitle": "Problems of information technologies", "ISSN": "1998-7005", "EISSN": "2313-0687", "Authors": [{"AuthorId": 1, "Name": "Володи<PERSON><PERSON><PERSON>", "Affiliation": "Херсонський національний технічний університет"}, {"AuthorId": 2, "Name": "Дмитро Чорний", "Affiliation": "Херсонський національний технічний університет"}], "References": []}, {"ArticleId": 88314475, "Title": "Abstraction models for verifying resource adequacy of IMA systems at concept level", "Abstract": "Complex cyber-physical systems can be difficult to analyze for resource adequacy (e.g., bandwidth and buffer size) at the concept development stage since relevant models are hard to create. During this period, details about the functions to be executed or the platforms in the architecture are partially unknown. This is especially true for Integrated Modular Avionics (IMA) systems, for which life-cycles span over several decades, with potential changes to functionality in the future. This work aims to identify abstractions for representing data exchanges among functions realized in networked IMA systems and investigates how these can be represented in formal models and analyzed with exact guarantees. Timed automata (TA) are a relevant choice for modeling since communication resource adequacy is directly related to potential network delays. We explore two alternatives in modeling with TA, a direct one representing every process using a TA template, and a more abstract one representing every computation device with a TA template. While the first approach represents process-to-process data exchanges, the modified approach reduces the state space by representing all processes currently allocated to a single computing element to obtain scalability gains. Both approaches are flexible since the templates presented can be instantiated to represent different types of network topologies and communication patterns. The instantiated TA models are used to illustrate an use case and analyzed with the UPPAAL model checker to verify that a given platform instance supports the desired system functions in terms of network bandwidth and buffer size adequacy, thereby messages reaching their final destination with freshness guarantees. Both abstraction levels are shown to be suitable for verifying the intended properties, but the more abstract one demonstrates a 67% improvement in verification time and a 66% reduction in state space during verification. The more abstract approach is also applied to a real-world example from an earlier publication, with a much larger state space and a more complex structure, to illustrate the ability to reuse the approach in multiple use cases.", "Keywords": "IMA system ; Conceptual analysis ; Network resource adequacy ; Timed automata ; UPPAAL", "DOI": "10.1016/j.scico.2021.102654", "PubYear": 2021, "Volume": "208", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Computer and Information Science, Linköping University, Sweden;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Computer and Information Science, Linköping University, Sweden"}], "References": []}, {"ArticleId": 88314543, "Title": "Semi-autonomous avatar enabling unconstrained parallel conversations –seamless hybrid of WOZ and autonomous dialogue systems–", "Abstract": "Many people are now engaged in remote conversations for a wide variety of scenes such as interviewing, counseling, and consulting, but there is a limited number of skilled experts. We propose a novel framework of parallel conversations with semi-autonomous avatars, where one operator collaborates with several remote robots or agents simultaneously. The autonomous dialogue system mostly manages the conversation, but switches to the human operator when necessary. This framework circumvents the requirement for autonomous systems to be completely perfect. Instead, we need to detect dialogue breakdown or disengagement. We present a prototype of this framework for attentive listening. GRAPHICAL", "Keywords": "Semi-autonomous dialogue ; parallel conversations ; conversational avatar ; spoken dialogue system ; attentive listening", "DOI": "10.1080/01691864.2021.1928549", "PubYear": 2021, "Volume": "35", "Issue": "11", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Kyoto, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Undergraduate School of Informatics and Mathematical Science, Kyoto University, Kyoto, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Kyoto, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Kyoto, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Kyoto, Japan"}], "References": [{"Title": "Engagement in Human-Agent Interaction: An Overview", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "92", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 88314668, "Title": "On the Understandability of Language Constructs to Structure the State and Behavior in Abstract State Machine Specifications: A Controlled Experiment", "Abstract": "Abstract State Machine (ASM) theory is a well-known state-based formal method to analyze and specify software and hardware systems. As in other state-based formal methods, the proposed modeling languages for ASMs still lack easy-to-comprehend abstractions to structure state and behavior aspects of specifications. Modern object-oriented languages offer a variety of advanced language constructs, and most of them either offer interfaces, mixins, or traits in addition to classes and inheritance. Our goal is to investigate these language constructs in the context of state-based formal methods using ASMs as a representative of this kind of formal methods. We report on a controlled experiment with 105 participants to study the understandability of the three language constructs in the context of ASMs. Our hypotheses are influenced by the debate of object-oriented communities. We hypothesized that the understandability (measured by correctness and duration variables) shows significantly better understanding for interfaces and traits compared to mixins, as well as at least a similar or better understanding for traits compared to interfaces. The results indicate that understandability of interfaces and traits show a similar good understanding, whereas mixins shows a poorer understanding. We found a significant difference for the correctness of understanding comparing interfaces with mixins.", "Keywords": "Abstract State Machines ; Empirical software engineering ; Understandability ; Language constructs ; Controlled experiment", "DOI": "10.1016/j.jss.2021.110987", "PubYear": 2021, "Volume": "178", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Vienna, Faculty of Computer Science, Research Group Software Architecture, Währingerstraße 29, 1090 Vienna, Austria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Vienna, Faculty of Computer Science, Research Group Software Architecture, Währingerstraße 29, 1090 Vienna, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Vienna, Faculty of Computer Science, Research Group Software Architecture, Währingerstraße 29, 1090 Vienna, Austria"}], "References": [{"Title": "Specifying with Interface and Trait Abstractions in Abstract State Machines: A Controlled Experiment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 88314673, "Title": "An extensive study on smell-aware bug localization", "Abstract": "Bug localization is an important aspect of software maintenance because it can locate modules that should be changed to fix a specific bug. Our previous study showed that the accuracy of the information retrieval (IR)-based bug localization technique improved when used in combination with code smell information. Although this technique showed promise, the study showed limited usefulness because of the small number of: (1) projects in the dataset, (2) types of smell information, and (3) baseline bug localization techniques used for assessment. This paper presents an extension of our previous experiments on Bench4BL, the largest bug localization benchmark dataset available for bug localization. In addition, we generalized the smell-aware bug localization technique to allow different configurations of smell information, which were combined with various bug localization techniques. Our results confirmed that our technique can improve the performance of IR-based bug localization techniques for the class level even when large datasets are processed. Furthermore, because of the optimized configuration of the smell information, our technique can enhance the performance of most state-of-the-art bug localization techniques.", "Keywords": "Bug localization ; Code smell ; Information retrieval", "DOI": "10.1016/j.jss.2021.110986", "PubYear": 2021, "Volume": "178", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Tokyo Institute of Technology, Tokyo, 152–8550, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Tokyo Institute of Technology, Tokyo, 152–8550, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Tokyo Institute of Technology, Tokyo, 152–8550, Japan;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Tokyo Institute of Technology, Tokyo, 152–8550, Japan"}], "References": []}, {"ArticleId": 88314714, "Title": "Introduction to the Special Issue on Source Code Analysis and Manipulation 2018", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.jss.2020.110702", "PubYear": 2021, "Volume": "178", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Victoria, Victoria, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, East Carolina University, Greenville, NC, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, University of Szeged, Szeged, Hungary"}], "References": []}, {"ArticleId": 88314716, "Title": "RETRACTED ARTICLE: COVID-19 pandemic and unemployment rate: A hybrid unemployment rate prediction approach for developed and developing countries of Asia", "Abstract": "<p>Unemployment remains a serious issue for both developed and developing countries and a driving force to lose their monetary and financial impact. The estimation of the unemployment rate has drawn researchers' attention in recent years. This investigation's key objective is to inquire about the impact of COVID-19 on the unemployment rate in selected, developed and developing countries of Asia. For experts and policymakers, effective prediction of the unemployment rate is an influential test that assumes an important role in planning the monetary and financial development of a country. Numerous researchers have recently utilized conventional analysis tools for unemployment rate prediction. Notably, unemployment data sets are nonstationary. Therefore, modeling these time series by conventional methods can produce an arbitrary mistake. To overcome the accuracy problem associated with conventional approaches, this investigation assumes intelligent-based prediction approaches to deal with the unemployment data and to predict the unemployment rate for the upcoming years more precisely. These intelligent-based unemployment rate strategies will force their implications by repeating diversity in the unemployment rate. For illustration purposes, unemployment data sets of five advanced and five developing countries of Asia, essentially Japan, South Korea, Malaysia, Singapore, Hong Kong, and five agricultural countries (i.e., Pakistan, China, India, Bangladesh and Indonesia) are selected. The hybrid ARIMA-ARNN model performed well among all hybrid models for advanced countries of Asia, while the hybrid ARIMA-ANN outperformed for developing countries aside from China, and hybrid ARIMA-SVM performed well for China. Furthermore, for future unemployment rate prediction, these selected models are utilized. The result displays that in developing countries of Asia, the unemployment rate will be three times higher as compared to advanced countries in the coming years, and it will take double the time to address the impacts of Coronavirus in developing countries than in developed countries of Asia.</p><p>© The Author(s), under exclusive licence to Springer-Verlag GmbH Germany, part of Springer Nature 2021.</p>", "Keywords": "Accomplishment;Asia;COVID-19;Financial development;Hybrid modelling approach;Nonlinear unemployment rate;Prediction", "DOI": "10.1007/s00500-021-05871-6", "PubYear": 2023, "Volume": "27", "Issue": "1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Huanghuai University, Henan, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, Hazara University Mansehra, Dhodial, Pakistan. ;School of Statistics, Jiangxi University of Finance and Economics, Nanchang, 330013 China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information, College of Science At Zulfi, Majmaah University, PO Box 66, Al-Majmaah, 11952 Saudi Arabia."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, College of Science Al-Zulfi, Majmaah University, PO Box 66, Al-Majmaah, 11952 Saudi Arabia."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Statistics, Hazara University Mansehra, Dhodial, Pakistan."}], "References": [{"Title": "Prediction of Unemployment Rates with Time Series and Machine Learning Techniques", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "2", "Page": "673", "JournalTitle": "Computational Economics"}, {"Title": "Unemployment Rate Forecasting: A Hybrid Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "1", "Page": "183", "JournalTitle": "Computational Economics"}, {"Title": "Unemployment Rate Forecasting: A Hybrid Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "1", "Page": "183", "JournalTitle": "Computational Economics"}, {"Title": "Efficient deep learning approach for augmented detection of Coronavirus disease", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "14", "Page": "11423", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 88314728, "Title": "Community of practice: converting IT graduate students into specialists via professional knowledge sharing", "Abstract": "Purpose \nThe paper aims to highlight how an applied learning framework or “community of practice” (CoP) combined with a traditional theoretical course of study enables the identification of teaching-learning processes which facilitate knowledge transfer from practitioners to graduate information technology (IT) students for quicker integration in the labour market.\n \n \n Design/methodology/approach \nCoPs are identified based on cluster analysis according to <PERSON><PERSON>b’s Learning Style Inventory (1984), with data obtained through a survey. Empirical research is applied to the CoP developed within a non-formal learning framework, principal new actors being IT specialists linked to graduate IT students and teachers on a traditional university course. Graduate IT students can gain knowledge of the ideal employee and the social and emotional skills needed to integrate with the IT labour market.\n \n \n Findings \nThe K-Means algorithm helps to identify clusters of graduate IT students displaying necessary knowledge acceptance behaviour to convert them into specialists. The results of the cluster analysis show different learning styles of the labour force, providing an overview of candidate selection methods and the knowledge, skills and attitudes expected by users.\n \n \n Research limitations/implications \nAlthough the research adds value to the existing literature on learning styles and the knowledge and core skills needed by IT specialists, it was limited to an emerging market.\n \n \n Originality/value \nThe study provides a preliminary overview of graduate IT students’ attitudes from an emerging market to the re-engineering of academic learning contexts to facilitate professional knowledge transfer, converting them into IT practitioners and integrating them in the labour market of an emerging economy.", "Keywords": "Cluster analysis;Graduate IT students;Community of practice (CoP);Higher education;Knowledge sharing;Non-formal learning context;Knowledge;Skills and attitudes;Knowledge management;Measurement;Information technology", "DOI": "10.1108/K-10-2020-0711", "PubYear": 2022, "Volume": "51", "Issue": "2", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business Informatics, Faculty of Economics and Business, Babeș-Bolyai University , Cluj-Napoca, Romania"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Marketing, Facultatea de Stiinte Economice si Gestiunea Afacerilor, Universitatea Babeș-Bolyai , Cluj-Napoca, Romania"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Letters, Babeș-Bolyai University , Cluj-Napoca, Romania"}], "References": []}, {"ArticleId": 88314734, "Title": "An allergy of society: on the question of how a societal “lockdown” becomes possible", "Abstract": "Purpose \nGiven the form of functional differentiation of modern society, a far-reaching coordination of functional systems as a dissolution of their heterarchical relationship to each other, as was apparently possible in the social “lockdown” during the corona pandemic, should have been extremely unlikely. The purpose of this study is to explain how this was nevertheless achieved.\n \n \n Design/methodology/approach \nFrom the perspective of systems theory, social action in principle does not present itself as a problem but as a solution to (latent) social problems. In the sociological analysis presented here, it is therefore precisely a matter of uncovering or pointing out those (changed) social structures in which a social “lockdown” appears as a solution.\n \n \n Findings \nThe paper explains that with the emergence of social media through applications such as Facebook, Instagram, Twitter, and TikTok, a new force is establishing itself at the level of society as a system. It is one that is characterized by being highly vulnerable to moral communication. A susceptibility to morality manifests, on the one hand, through an individual differentiation of society made possible by social media – for example, in the emerging Chinese social credit system – and, on the other hand, through the specific communicative structures of the social media themselves. It is argued that social media, in the form of a moral authority with a lasting effect on society as a whole, make a significant contribution to realizing the social “lockdown.”\n \n \n Originality/value \nThe originality of the paper results from the fact that the emergence of a new social phenomenon (“lockdown”) is explained.", "Keywords": "Communications technologies;Systems theory;Social systems;Social networks;Second-order cybernetics;Autopoiesis", "DOI": "10.1108/K-11-2020-0797", "PubYear": 2022, "Volume": "51", "Issue": "5", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Next Society Institute, Kazimieras Simonavičius University , Vilnius, Lithuania"}], "References": [{"Title": "COVID-19. <PERSON><PERSON><PERSON><PERSON> of a superfluous crisis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "5", "Page": "1621", "JournalTitle": "Kybernetes"}]}, {"ArticleId": 88314797, "Title": "Sensorized Foam Actuator with Intrinsic Proprioception and Tunable Stiffness Behavior for Soft Robots", "Abstract": "<p>Soft robots require actuators with integrated sensing components that perceive unstructured, dynamic environments without compromising their performance. However, many soft robotic systems still rely on external sensors, which affect the functionality, response time, and payload. To overcome these issues, herein a sensorized foam actuator (SFA) with a foam core that acts as both an actuator and a proprioception-sensing element is developed. The integrated modules can sense direct actuation and passive deformation due to extrinsic stresses through a specific pore shape evolution, which leads to a distinct variation in the resistivity pattern. In addition, a fiber-reinforced skin encapsulating the SFA facilitates a fast and efficient response. The SFA is able to lift more than 500 times its own weight with a load-withstanding capacity of 235 N, linear contraction up to 70% strain, and a recovery speed of 13.3 mm s<sup>−1</sup>. In addition, the SFA is lightweight (34 g), has low hysteresis (<4%), and can self-sense its current deformation state. As proof of concept, various soft robotic applications are presented such as compression piston-like motion, modular inchworm-like crawling locomotion, and a robotic trunk-like manipulation. </p>", "Keywords": "integrated design;porous materials;proprioception;soft actuators and sensors;soft robotic applications;variable stiffness", "DOI": "10.1002/aisy.202100022", "PubYear": 2021, "Volume": "3", "Issue": "6", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The BioRobotics Institute, Scuola Superiore Sant’Anna, Viale Rinaldo Piaggio 34, Pisa, 56025 Italy; Center for Micro-BioRobotics – Bioinspired Soft Robotics Laboratory, Istituto Italiano di Tecnologia, Viale Rinaldo Piaggio 34, Pontedera, 56025 Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Micro-BioRobotics – Bioinspired Soft Robotics Laboratory, Istituto Italiano di Tecnologia, Viale Rinaldo <PERSON>ag<PERSON> 34, Pontedera, 56025 Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Micro-BioRobotics – Bioinspired Soft Robotics Laboratory, Istituto Italiano di Tecnologia, Viale Rinaldo Piaggio 34, Pontedera, 56025 Italy; Faculty of Engineering Technology, University of Twente, Horst – Ring 217, Enschede, 7500 AE The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Micro-BioRobotics – Bioinspired Soft Robotics Laboratory, Istituto Italiano di Tecnologia, Viale Rinaldo <PERSON>ag<PERSON> 34, Pontedera, 56025 Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Center for Micro-BioRobotics – Bioinspired Soft Robotics Laboratory, Istituto Italiano di Tecnologia, Viale Rinaldo <PERSON>ag<PERSON> 34, Pontedera, 56025 Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Center for Micro-BioRobotics – Bioinspired Soft Robotics Laboratory, Istituto Italiano di Tecnologia, Viale Rinaldo <PERSON>ag<PERSON> 34, Pontedera, 56025 Italy"}], "References": [{"Title": "Passive Morphological Adaptation for Obstacle Avoidance in a Self-Growing Robot Produced by Additive Manufacturing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "85", "JournalTitle": "Soft Robotics"}, {"Title": "A vision for future bioinspired and biohybrid robots", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "38", "Page": "eaba6893", "JournalTitle": "Science Robotics"}, {"Title": "CLASH—A Compliant Sensorized Hand for Handling Delicate Objects", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "138", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 88314877, "Title": "Interactive Programmatic Modeling", "Abstract": "<p>Modeling and computational analyses are fundamental activities within science and engineering. Analysis activities can take various forms, such as simulation of executable models, formal verification of model properties, or inference of hidden model variables. Traditionally, tools for modeling and analysis have similar workflows: (i) a user designs a textual or graphical model or the model is inferred from data, (ii) a tool performs computational analyses on the model, and (iii) a visualization tool displays the resulting data. This article identifies three inherent problems with the traditional approach: the recomputation problem, the variable inspection problem, and the model expressiveness problem. As a solution, we propose a conceptual framework called Interactive Programmatic Modeling. We formalize the interface of the framework and illustrate how it can be used in two different domains: equation-based modeling and probabilistic programming.</p>", "Keywords": "", "DOI": "10.1145/3431387", "PubYear": 2021, "Volume": "20", "Issue": "4", "JournalId": 17571, "JournalTitle": "ACM Transactions on Embedded Computing Systems", "ISSN": "1539-9087", "EISSN": "1558-3465", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "KTH Royal Institute of Technology, Sweden"}], "References": []}, {"ArticleId": 88314896, "Title": "Augmented Reality of 3D Content Application in Common Operational Picture Training System for Army", "Abstract": "The purpose of this article is to discuss how to improve the ways the Taiwanese Ground Forces use the common operational picture (COP) for training to improve the efficacy of tactical training and understanding of the combat field. Augmented Reality (AR) has made huge progress in the past decade and is widely applied in education, industrial sector, medical industry, military industry. More attention is focused on training simulations for soldiers and improving battlefield perception systems. The application of AR in tactical training is still an emerging realm of research but has a huge potential. Related studies have shown the application and technical limitations of AR technology in the military field. Then, using a user-oriented research method, a tactical training system based on 3D augmented reality is proposed and constructs a system prototype to combine virtual information with the real environment and provide the advantages of intuition, interaction, and information visualization. The experiment had an experimental group and control group, and an independent sample “t” was tested for evaluation and comparison. The experimental results show that the understanding of battlefield situations and tactical actions in the AR-based tactical teaching system has changed significantly. The system prototype and experimental content provide useful contributions to the tactical teaching of the Army Academy, and it can be used as a reference for the development of AR in future military applications. Disclosure of potential conflict of interest No potential conflict of interest was reported by the authors. Additional information Funding This study is supported in part by the Ministry of Science and Technology of the Republic of China under contract numbers MOST 108-2511-H-606 −001. Notes on contributors <PERSON><PERSON><PERSON><PERSON> is an assistant professor in the Department of Applied Arts, Fu Hsing Kang College, NDU.ROC and teaches courses in digital art, computer graphics, and 3D computer animation. Currently working on the fields of technology art and augmented reality applications. Chien-Hsu Chen Chien-Hsu Chen is a Professor of Industrial Design at National Cheng Kung University, Life Member of the Ergonomic Society of Taiwan, and the Taiwan Institute of Kansei. He got Ph.D. degree from University of Texas, Arlington, the USA in 1996. His research is ergonomic, interaction design, and augmented reality application.", "Keywords": "", "DOI": "10.1080/10447318.2021.1917865", "PubYear": 2021, "Volume": "37", "Issue": "20", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Arts, Fu Hsing Kang College, Taipei City, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Design, National Cheng Kung University, Tainan, Taiwan"}], "References": []}, {"ArticleId": 88314921, "Title": "Distributed Intelligence in the Internet of Things: Challenges and Opportunities", "Abstract": "Widespread adoption of smart IoT devices is accelerating research for new techniques to make IoT applications secure, scalable, energy-efficient, and capable of working in mission-critical use cases, which require an ability to function offline. In this context, the novel combination of distributed ledger technology (DLT) and distributed intelligence (DI) is seen as a practical route towards the decentralisation of IoT architectures. This paper surveys DI techniques in IoT and commences by briefly explaining the need for DI, by proposing a comprehensive taxonomy of DI in IoT. This taxonomy is then used to review existing techniques and to investigate current challenges that require careful attention and consideration. Based on the taxonomy, IoT DI techniques can be classified into five categories based on the factors that support distributed functionality and data acquisition: cloud-computing, mist-computing, distributed-ledger-technology, service-oriented-computing and hybrid. Existing techniques are compared and categorized mainly based on related challenges, and the level of intelligence supported. We evaluate more than thirty current research efforts in this area. We define many significant functionalities that should be supported by DI frameworks and solutions. Our work assists system architects and developers to select the correct low-level communication techniques in an integrated IoT-to-DLT-to-cloud system architecture. The benefits and shortcomings of different DI approaches are presented, which will inspire future work into automatic hybridization and adaptation of DI mechanisms. Finally, open research issues for distributed intelligence in IoT are discussed.", "Keywords": "Internet of Things (IoT); Distributed intelligence (DI); Cloud-computing; Mist-computing; Distributed-ledger technology; Service-oriented-computing; Hybrid", "DOI": "10.1007/s42979-021-00677-7", "PubYear": 2021, "Volume": "2", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Engineering, University of Huddersfield, Huddersfield, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Engineering, University of Huddersfield, Huddersfield, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing and Engineering, University of Huddersfield, Huddersfield, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computing and Engineering, University of Huddersfield, Huddersfield, UK"}], "References": [{"Title": "A Survey of Blockchain-Based Strategies for Healthcare", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Exploration of Solar Cell Materials for Developing Novel PUFs in Cyber-Physical Systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "6", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 88314960, "Title": "Evolution of urban forms observed from space", "Abstract": "Multiple driving forces shape cities. These forces include the costs of transporting goods and people, the types of predominant local industries, and the policies that govern urban planning. Here, we examine how agglomeration and dispersion change with increasing population and population density. We study the patterns in the evolution of urban forms and analyze the differences between developed and developing countries. We analyze agglomeration across 233 European and 258 Chinese cities using nighttime luminosity data. We find a universal inverted U-shape curve for the agglomeration metric (Lasym index). Cities attain their maximum agglomeration level at an intermediate density, above which dispersion increases. Our findings may guide strategic urban planning for the timely adoption of appropriate development policies.", "Keywords": "Urban form;Agglomeration;Lasym;Inverted U-shape;European;Chinese", "DOI": "10.1140/epjds/s13688-021-00283-w", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 5454, "JournalTitle": "EPJ Data Science", "ISSN": "", "EISSN": "2193-1127", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data61, Commonwealth Scientific and Industrial Research Organisation (CSIRO), Melbourne, Australia;Faculty of Information Technology, Monash University, Melbourne, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Data61, Commonwealth Scientific and Industrial Research Organisation (CSIRO), Melbourne, Australia;Faculty of Information Technology, Monash University, Melbourne, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Humans and Machines, Max Planck Institute for Human Development, Berlin, Germany;Media Lab, Massachusetts Institute of Technology, Cambridge, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Data61, Commonwealth Scientific and Industrial Research Organisation (CSIRO), Melbourne, Australia;Humans and Machines, Max Planck Institute for Human Development, Berlin, Germany;Media Lab, Massachusetts Institute of Technology, Cambridge, USA"}], "References": []}, {"ArticleId": 88315075, "Title": "A TDMA protocol with reinforcement learning slot selection for MANETs", "Abstract": "With the rapid development of wireless communication and the advantage of infrastructure-less technologies, mobile ad hoc networks (MANETs) have attracted great attention on military and rescue applications. Medium access control is an important issue in MANETs. Contention-based MAC protocols (e.g., CSMA) do not ensure a reliable transmission due to the possibility of collisions. On the contrary, schedule-based MAC protocols (e.g., TDMA) can solve the collision problem with a scheduled transmission plan. However, under an infrastructure-less environment, it is non-trivial for each node to determine its own transmission plan. This work investigates how to use reinforcement learning (RL) to help nodes determine their transmission plans in a TDMA protocol. More precisely, we design a cross-layer TDMA protocol with a RL-based slot selection algorithm. We have validated the proposed protocol by the ns-3 network simulator. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "MAC; MANETs; Medium access control; Mobile ad hoc networks; Ns-3; Reinforcement learning; Slot assignment; TDMA; Time division multiple access", "DOI": "10.1504/IJAHUC.2021.115123", "PubYear": 2021, "Volume": "37", "Issue": "1", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Black hole attack prevention scheme using a blockchain-block approach in SDN-enabled WSN", "Abstract": "The success of software-defined networking (SDN) technology in the wired environment has accelerated the research on the deployment of SDN in wireless sensor networks (WSN). Since the wireless environment has a shared nature, it is more vulnerable to routing attacks. Most of the existing security protocols cannot be implemented for WSN, as the wireless environment has physical restrictions and the nodes in the network have limited energy, processing capacity, and memory. This paper investigates the possible threats in SDN-enabled WSN and analyses the black hole attack in detail. In the paper, we have also proposed a novel lightweight security model that exploits the blockchain-block approach to be able to protect the flow table in each node, which is the main target of possible routing attacks. We have generated an unchangeable fingerprint called the signature token for the flow entries with a secret key belonging to each node. Thanks to this token, not only have the flow entries been secured against tampering but also data packets have been transmitted securely along the path and secured against replay attacks and man-in-the-middle attacks. The results show that the proposed security model can be a promising solution for securing SDN-enabled WSN structures. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Black hole attack; Blockchain; Flow table security; Internet of things; IoT; SDN; Software-defined networking; Wireless sensor network; WSN", "DOI": "10.1504/IJAHUC.2021.115125", "PubYear": 2021, "Volume": "37", "Issue": "1", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer and Information Sciences, Computer Engineering Department, Sakarya University, Sakarya, 54187, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Artificial Intelligence Systems Research and Application Centre, Sakarya University, Sakarya, 54187, Turkey"}], "References": []}, {"ArticleId": 88315122, "Title": "Time and energy-efficient hybrid job scheduling scheme for mobile cloud computing empowered wireless sensor networks", "Abstract": "The rapid growth of emerging sensory data processing and delivery application poses a significant challenge to researchers due to the WSN’s resource limitations. To minimise the WSN’s job processing time, highly resourceful cloud computing technology has become a potential solution. To minimise the WSNs battery usage and job execution time, the development of a proper job scheduling scheme has emerged as a potential research challenge for cloud empowered WSN’s by taking into account different job and resource characteristics. In this paper, we propose a time and energy-efficient hybrid job scheduling scheme for WSNs job execution that not only assigns sensor-cloud resources but also network timeslot resources. We present an analytical model and compare our proposed scheme’s performance with traditional contention and reservation-based scheme. Our experimental results demonstrate that the proposed scheme supersedes the state of the art up to approximately 28.05% in the metric of schedule length. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Cloud computing; Computational complexity; Contention; Energy consumption; Job execution time; Job scheduling; Reservation; Schedule length; Wireless sensor networks; WSNs", "DOI": "10.1504/IJAHUC.2021.115124", "PubYear": 2021, "Volume": "37", "Issue": "1", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Chittagong University of Engineering and Technology, Chittagong, 4349, Bangladesh"}], "References": []}, {"ArticleId": 88315125, "Title": "Tree reconstruction using energy aware sink mobility to prolong network lifetime in WSN", "Abstract": "Sensor nodes residing near a sink receives more traffic than other nodes and depletes their battery quickly which leads to an energy hole problem. The objective is to collect data from all over the sensor field, aggregate and forward the same to a mobile sink to extend the network lifetime. A sensor network is logically divided into subgrids and each is identified uniquely and the sink is aware of the number of sensors under each subgrid. During data collection, a mobile sink move towards a potential subgrid so that the energy of all the sensor nodes is equally utilised. It is implemented through reconstructing the network and new routing paths are generated. The sink moves to a new subgrid where sensor nodes have adequate residual energy and starts collecting data, which prolongs the network lifetime. Parameters like average lifetime of nodes, packet delivery ratio and throughput of the network are considered for performance analysis. Residual energy obtained from the simulation shows that the lifetime of the network is improved. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Lifetime; Mobile sink; Residual energy; Subgrid; Tree reconstruction; Wireless sensor networks; WSNs", "DOI": "10.1504/IJAHUC.2021.115126", "PubYear": 2021, "Volume": "37", "Issue": "1", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of MCA, JNN College of Engineering, Shivamogga, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, UVCE, Bengaluru, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 88315140, "Title": "Robust predictive iterative learning control for linear time‐varying systems", "Abstract": "<p>A novel control technique is proposed by combining iterative learning control (ILC) with model predictive control (MPC) for a trajectory tracking problem of linear time-varying systems (LTVSs) in this paper. By introducing prediction mechanism along both of the iteration and time directions instead of only one direction, the control law is successively updated along two directions by learning from the predictive and compensatory information. The new control law can improve convergence speed and effectively suppress the uncertainties in initial states, external disturbances, and model mismatches. The convergence conditions are rigorously derived and analyzed as well. An example is given to illustrate the effectiveness of the obtained results.</p>", "Keywords": "iterative learning control;linear time-varying systems;model predictive control;prediction and compensation mechanism", "DOI": "10.1002/asjc.2477", "PubYear": 2022, "Volume": "24", "Issue": "1", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Automation, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Automation, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Nanjing University of Science and Technology, Nanjing, China"}], "References": [{"Title": "Delay‐dependent conditions for finite time stability of linear time‐varying systems with delay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "924", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": ********, "Title": "Crypto-Hotwire", "Abstract": "<p>Blockchains and cryptocurrencies disrupted the conversion of energy into a medium of exchange. Numerous applications for blockchains and cryptocurrencies are now envisioned for purposes ranging from inventory control to banking applications. Naturally, in order to mine in an economically viable way, regions where energy is plentiful and cheap, e.g., close to hydroelectric plants, are sought. The possibility of converting energy into cash, however, also opens up opportunities for a new kind of cyber attack aimed at illegally mining cryptocurrencies by stealing energy. In this work, we indicate, using data from January and February of 2018 from our university, that such a threat is real, and present a projection of the gains derived from these attacks.</p>", "Keywords": "", "DOI": "10.1145/3466826.3466830", "PubYear": 2021, "Volume": "48", "Issue": "4", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "UFRJ, Rio de Janeiro, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "UFRJ, Rio de Janeiro, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "UFRJ, Rio de Janeiro, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UFRJ, Rio de Janeiro, Brazil"}], "References": []}, {"ArticleId": 88315341, "Title": "A MEMS-based electromagnetic membrane actuator utilizing bonded magnets with large displacement", "Abstract": "In a previous study, microfabricated bonded magnets have been utilized in MEMS-based electromagnetic membrane actuators (EMMAs) to drive micropumps. However, to maintain the flexibility of the membrane the packing density of the spin-coated bonded magnets needed to be small (6 vol%) and this together with a strong self-demagnetization effect meant that the force generated was weak. As a result, the maximum displacement achieved was only several microns. To overcome this problem, we proposed fabricating bonded magnets using micro compression molding with a soft membrane mold in order to realize both a high packing density and a flexible membrane. The grooves (0.3 mm × 5 mm × t0.3 mm) in the PDMS membrane were filled with a mixture of NdFeB magnetic powder and wax powder. A packing density of 50 vol% for the bonded magnets was realized without undue influence on the flexibility of the membrane. A fine-pitch magnetization pattern was also used to decrease the self-demagnetization effect and thereby improve actuator performance. The experimental results show that the maximum force generated and the maximum displacement achieved using the fabricated EMMAs (12 mm × 12 mm × t1.1 mm) were 2.2 mN and 100 μm, respectively, at the power consumption of 4 W.", "Keywords": "MEMS ; Electromagnetic membrane actuator ; Bonded magnet ; Fine-pitch magnetization ; Large displacement", "DOI": "10.1016/j.sna.2021.112834", "PubYear": 2021, "Volume": "330", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Tokyo Institute of Technology, 4259 Nagatsuta-cho, Midori-ku, Yokohama, 226-8503, Japan"}, {"AuthorId": 2, "Name": "Dong <PERSON>", "Affiliation": "State Key Laboratory of Fluid Power and Mechatronic Systems, Zhejiang University, Hangzhou, 310027, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory for Future Interdisciplinary Research of Science and Technology (FIRST), Institute of Innovative Research (IIR), Tokyo Institute of Technology, 4259 Nagatsuta-cho, Midori-ku, Yokohama, 226-8503, Japan;Corresponding author"}], "References": []}, {"ArticleId": 88315370, "Title": "Automated DDOS attack detection in software defined networking", "Abstract": "Software-Defined Networking (SDN) is a networking paradigm that has redefined the term network by making the network devices programmable. SDN helps network engineers to monitor the network expeditely, control the network from a central point, identify malicious traffic and link failure in easy and efficient manner. Besides such flexibility provided by SDN, it is also vulnerable to attacks such as DDoS which can halt the complete network. To mitigate this attack, the paper proposes to classify the benign traffic from DDoS attack traffic by using machine learning technique. The major contribution of this paper is identification of novel features for DDoS attack detections. Novel features are logged into CSV file to create the dataset and machine learning algorithms are trained on the created SDN dataset. Various work which has already been done for DDoS attack detection either used a non-SDN dataset or the research data is not made public. A novel hybrid machine learning model is utilized to perform the classification. Results show that the hybrid model of Support Vector classifier with Random Forest (SVC-RF) classifies the traffic with the highest testing accuracy of 98.8% with a very low false alarm rate.", "Keywords": "Machine learning ; Software-defined-networking ; DDOS Attack detection ; Traffic protocol ; SDN Traffic classification ; Mininet ; 00–01 ; 99-00", "DOI": "10.1016/j.jnca.2021.103108", "PubYear": 2021, "Volume": "187", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, SEAS, Bennett University, Greater Noida, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, SEAS, Bennett University, Greater Noida, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, SEAS, Bennett University, Greater Noida, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Thapar Institute of Engineering and Technology, India and Department of Computer Science and Information Engineering, Asia University, Taiwan and King <PERSON> University, Saudi Arabia;School of Computer Science, University of Petroleum and Energy Studies, Dehradun, Utttarakhand, India;Corresponding author"}], "References": [{"Title": "Data-driven software defined network attack detection : State-of-the-art and perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "65", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 88315399, "Title": "udkm1Dsim – a Python toolbox for simulating 1D ultrafast dynamics in condensed matter", "Abstract": "The udkm1Dsim toolbox is a collection of Python classes and routines to simulate the thermal, structural, and magnetic dynamics after laser excitation as well as the corresponding X-ray scattering response in one-dimensional samples, such as multilayers. The toolbox provides the capabilities to define arbitrary layered structures on the atomic level including a rich database of element-specific physical properties. The excitation of dynamics is represented by an N -temperature-model which is commonly applied in ultrafast physics. Structural dynamics due to thermal stresses are calculated by a linear-chain model of masses and springs. The implementation of specific magnetic dynamics can be easily accomplished by the user employing a generalized magnetization interface class. The resulting X-ray diffraction response is computed by kinematical or dynamical X-ray theory which can also include polarization-dependent magnetic scattering. The udkm1Dsim toolbox is highly modular and allows for injecting user-defined inputs at any step within the simulation procedure. New version program summary Program Title: udkm1Dsim CPC Library link to program files: https://doi.org/10.17632/bnzw823v6y.1 Developer's repository link: https://github.com/dschick/udkm1Dsim Code Ocean capsule: https://codeocean.com/capsule/8131941 Licensing provisions: MIT Programming language: Python Journal reference of previous version: Comput. Phys. Commun. 185 (2) (February 2014) 651–660 Does the new version supersede the previous version?: Yes Reasons for the new version: The toolbox has been ported from MATLAB (MathWorks Inc.) to Python and is based exclusively on free and open-source components. Moreover, new features have been added that allow for a broader applicability of the toolbox. Summary of revisions: Porting to Python. Introduction of amorphous layers in the sample structures. Add magnetization property to atoms and layers. Multilayer formalism to calculate laser absorption. New magnetization class to allow for user-defined magnetization dynamics . New resonant magnetic X-ray scattering employing dynamical X-ray theory. Calculation of X-ray scattering as function of photon energy and scattering vector. Nature of problem: Simulate the thermal, structural, and magnetic dynamics of 1D layered sample structures due to an ultrafast laser excitation and compute the corresponding transient (magnetic) X-ray scattering response. Solution method: The program provides an object-oriented toolbox for building arbitrary layered 1D crystalline/amorphous sample structures including a rich database of element-specific parameters. The excitation, thermal transport , and lattice dynamics are simulated utilizing SciPy's ODE solver. Magnetization dynamics can be introduced by the user employing a magnetization interface class. The dynamical (magnetic) X-ray scattering is computed by a matrix formalism that can be parallelized. Additional comments including restrictions and unusual features: The program is restricted to 1D layered sample structures. Phonon dynamics only include longitudinal acoustic phonons (sound waves). Magnetization dynamics have to be defined by the user. X-ray scattering only allows for symmetrical and co-planar geometries due to the 1D nature of the toolbox. The program is highly modular and allows the inclusion of user-defined inputs at any time of the simulation procedure. Introduction The investigation of electronic, magnetic, and structural dynamics in solid state physics has made great progress during the last decades due to the increasing availability of ultrashort electron and light pulses in a broad spectral range from THz to hard X-rays at large-scale facilities as well as in the lab. One of the major goals of these experiments is to follow the coupling of different degrees of freedom on the relevant time and length scales. Recent examples reveal the complexity of these experiments even for following only two sub-systems, such as the coupling of electron and lattice dynamics [1], [2], [3], of electron and spin dynamics [4], [5], [6], or the coupling of spin and lattice dynamics [7], [8], [9]. In order to understand and interpret such experimental data, one relies on a bunch of simulations for modeling and fitting, which are available as software tool-kits or as published formalisms. A list of required dynamic simulations might include the simplistic N -temperature model (NTM) as initially proposed by Anisimov et al. [10] as well as its modifications and implementations [1], [11], [12], coherent phonon dynamics in a masses-and-spring model [13], [14], molecular dynamics for simulations of spin lattice coupling [15], [16], magnetic simulations combining a 2-temperature-model with the Landau-Lifschitz-Bloch equation [17], or full magnetic simulations [18], [19], [20], [21]. Other aspects can be the spatial absorption profile of the laser excitation in the sample [22], [23] and, moreover, the calculation of the actual observable, e.g. the scattered light intensity in the framework of kinematical or dynamical scattering theories [24], [25], [26], [27], [28], [29] with possible inclusion of resonant and magnetic scattering effects. The implementation of the above mentioned formalisms or the usage and adaption of available external software packages is very time-consuming and each piece of software covers only a very limited aspect of real time-resolved experiments. To that end, the need for a generic, modular, and open-source toolbox that allows for combining many of the above mentioned functionalities is obvious. The udkm1Dsim toolbox allows to create arbitrary one-dimensional (1D) structures made of crystalline and/or amorphous layers, including stochiometric mixtures, typically on the nanometer length-scale. These 1D structures hold all relevant material information such as structural, elastic, thermal, magnetic, and optical parameters. The toolbox allows for calculating thermal, structural, and magnetic dynamics on these 1D structures utilizing an NTM, a linear masses-and-springs-model, as well as an interface for user-defined magnetization dynamics, respectively. Finally, different types of light-scattering theories can be applied to retrieve the static as well as transient response from these sample structures due to the above mentioned dynamics, similar to real pump-probe experiments. With that the generally non-linear dependence of the actual observable (scattered light intensity) and the physical quantity of interest (temperature, strain, magnetization, ...) can be revealed. This includes also methods to apply realistic instrumental broadening to the simulated results for better comparison with experimental data. For now the udkm1Dsim toolbox focus on light scattering from the extreme ultra-violet (XUV) up to hard X-rays but can be easily extended to larger wavelength radiation or totally different probing techniques that can be modeled in 1D. The original matlab (MathWorks Inc.) udkm1Dsim toolbox [30] has been successfully used in 40 publications (Feb. 2021) and is still available for download [31]. However, due to Python's increasing performance, popularity, and availability the new version of the udkm1Dsim toolbox has been ported to this programming language. At the same time, the project has moved to github.com/dschick/udkm1Dsim to provide full version control, issue, and feature tracking, as well as project management capabilities in order to allow for better collaboration between users and developers. This also includes automatic code validation and unit testing, as well as source-code-based generation of the documentation at udkm1Dsim.readthedocs.org as part of the continuous integration (CI) concept. Despite of the major step of porting the udkm1Dsim toolbox to Python there are several new features included in this release while only a few minor features of the old version have been dropped. Accordingly, existing simulations in matlab (MathWorks Inc.) are easily portable to this new Python version as the general concept and syntax of the toolbox has been unaffected. The new functionalities of the release include: the introduction of amorphous layers in addition to crystalline unit cells; magnetic properties of atoms including magnetic scattering factors; a multilayer formalism for calculating laser absorption profiles [22], [23]; a new magnetization dynamics module; a dynamical magnetic scattering formalism [28]; as well as a unified interface to calculate light scattering as function of scattering vector and photon energy. Most of the underlying physics have already been described in the initial version of the udkm1Dsim toolbox [30] as well as in the API documentation of the corresponding modules. Therefore we will concentrate mainly on the new features/physics as well as on the slight changes of the workflow in Python. After a general description of the implementation and workflow of the udkm1Dsim toolbox, we will introduce the new release features within an exemplary simulation of laser-induced dynamics in a magnetically-coupled superlattice of Fe and Cr layers. As a convention throughout this document all code is written in typewriter font ( code = [1, 2, 3] ). Figures Workflow of the udkm1Dsim toolbox. (Sub-)Module names are italic and class names are in bold letters. All classes of the simulations module require a Structure object on initialization. Experimental scheme of the udkm1Dsim toolbox. The co-planar scattering plane contains the scattering vector qz→=k→out−k→in. The magnetization m→ of the individual Atom objects can be defined in 3D by ... Show all figures Section snippets Implementation & workflow The structure of the udkm1Dsim toolbox as a Python module tries to reflect the physical reality of the modeled experiments, see Fig. 1. In the beginning a Structure object needs to be build out of any number and combination of AmorphousLayer and/or UnitCell objects which themselves need to consist of one or more Atom / AtomMixed objects. The Structure holds all relevant physical parameters which are required for the actual static and dynamic simulations. Within the simulations module different Example We choose to simulate the laser-driven structural and magnetic dynamics in an antiferromagnetically (AFM) coupled Fe/Cr superlattice (SL) [33] as an example to demonstrate the recent capabilities of the udkm1Dsim toolbox. The test sample consists of 20x(Fe+Cr) on a Si substrate with a layer thickness of d Fe = d Cr = 1 nm. In the Fe/Cr SL the individual Fe layers are ferromagnetically aligned in the plane of the sample. In dependence of the Cr layer thickness the adjacent Fe layers can align Conclusions & outlook With the porting of the udkm1Dsim toolbox from matlab (MathWorks Inc.) to Python several new features have been added to the toolbox. The implementation of magnetic properties as well as the resonant magnetic scattering formalism drastically extends its outreach. The toolbox allows for carrying out a full set of static and dynamics simulations on one and the same sample structure without switching between packages or even programming languages. At the same time it relies to 100% on free and Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgement We would like to acknowledge the discussions with Marc Herzog, Stéphane Grenier, Lukas Alber, Valentino Scalera, Vivek Unikandanunni, and Stefano Bonetti. We further thank Loïc Le Guyader for providing us with the source code for the optical multilayer formalism as well as Lisa-Marie Kern, Martin Borchert, and Martin Hennecke for extensive testing of the toolbox. References (42) B. Henke et al. At. Data Nucl. Data Tables (1993) A. Nefedov et al. Superlattices Microstruct. (2005) D. Schick et al. Comput. Phys. Commun. (2014) M. Elzo et al. J. Magn. Magn. Mater. (2012) P.-W. Ma et al. Comput. Phys. Commun. (2016) L. Alber et al. Comput. Phys. Commun. (2021) K. Ohta et al. Appl. Opt. (1990) B. Watts Opt. Express (2014) D.L. Windt Comput. Phys. (1998) L. Waldecker et al. Phys. Rev. X (2016) J. Als-Nielsen et al. Elements of Modern X-Ray Physics (2011) A. von Reppert et al. Struct. Dyn. (2016) C. Stamm et al. Nat. Mater. (2007) B. Koopmans et al. Nat. Mater. (2010) S. Macke et al. J. Phys. Condens. Matter (2014) C. Thomsen et al. Phys. Rev. B (1986) W. You et al. Phys. Rev. Lett. (2018) SPILADY - a spin-lattice dynamics simulation program U. Atxitia et al. J. Phys. D, Appl. Phys. (2017) View more references Cited by (0) Recommended articles (6) Research article From Feynman rules to conserved quantum numbers, III Computer Physics Communications, Volume 260, 2021, Article 107740 Show abstract The present article complements the earlier ones in this series. The first part contains various results on the constituent system C κ ( M ) of a graph model M , and on its feasibility system I η ( M ) (which comprises a number of identities that define the number conservation rules). Those results include the general form of the (particle) number conservation rules in models without explicit propagator mixing. A few types of graph models (including self-conjugate and quasi-normal models) are defined. Oversimplifying, a model M is self-conjugate if it has a certain (weak) combinatorial type of C-symmetry, and M is quasi-normal if I η ( M ) fully determines for which multisets of coloured, unlinked half-edges there is a graph in M with that exact multiset. It is shown not only that every self-conjugate model is quasi-normal, but also that some extensions of self-conjugate models are still quasi-normal. Most conveniently, self-conjugate models (and even some extended models) may be recognized in polynomial time. These results seem to lead to the following conclusion: for many (possibly ‘most’ of the) consistent, relevant QFT models, a complete correlation function 〈 F 〉 is graphical — i.e. there exist Feynman diagram(s) for 〈 F 〉 — if and only if 〈 F 〉 is allowed by the number conservation rules (i.e. by a complete system of such rules). Since these rules can be computed in polynomial time then, for many QFT models, deciding whether 〈 F 〉 is graphical also takes polynomial time. Research article Analysis of positron profiling data using e + DSc computer code Computer Physics Communications, Volume 264, 2021, Article 107937 Show abstract The Green’s function method was applied to solve the one-dimensional positron diffusion equation for a system consisting of up to four layers that contain defects with different trapping rates. These allow us to obtain the analytical relationships valid for the evaluation of data obtained from variable energy positron measurements. They have been implemented in user-friendly free computer code available to users. Fitting strategies are presented to extract the relevant physical parameters. The code was used to determine positron diffusion length in samples of polycrystalline pure, well-annealed iron, depleted uranium, and titanium. Program Title: e+DSC-1 CPC Library link to program files : https://doi.org/10.17632/jxpj25kjvr.1 Licensing provisions : MIT license Programming language: Microsoft Visual Basic 2015 External routines/libraries: Accord.NET Nature of problem: The program enables the analysis of data obtained from a variable energy positron beam. The shape parameter of the annihilation line as a function of the incident positron energy is evaluated using a positron diffusion trapping model in which the positron trapping rate function is expressed as a four-step function. Solution method: The one-dimensional diffusion equation was solved by the Green’s function method. This allows the use of an exact form for the positron implantation profile, which is used in the program as the Makhov’s function. The parameters of this function are obtained from the MC simulation with GEANT4 and stored in the program. Research article AICON2: A program for calculating transport properties quickly and accurately Computer Physics Communications, Volume 266, 2021, Article 108027 Show abstract Calculating the transport properties, such as electrical conductivity, has been a great challenge in materials modeling fields because of its complexity. We have implemented an algorithm to calculate the electronic transport properties using the generalized Kane band model and perturbation theory in the framework of the relaxation time approximation. Three scattering mechanisms affect the total relaxation time: acoustic phonon scattering, polar optical phonon scattering, and ionized impurity scattering. All the necessary parameters can be calculated from first principles. The capability of the program was tested on a group of semiconductors, and the obtained results show reasonable agreement with experiment. The program works fast, and is robust and especially appropriate for high-throughput screening of thermoelectric materials. Program title: AICON2 CPC Library link to program files: https://doi.org/10.17632/s9b8y8t92c.2 Code Ocean capsule: https://codeocean.com/capsule/7509547 Licensing provisions: GPLv3 Programming language: Python3 External routines/libraries: Numpy, Scipy, spglib, pymatgen, atomate, emc Nature of problem: Calculation of electrical and thermal conductivity from first principles requires a large number of computing resources in order to construct electron-phonon coupling matrix elements, integrate over the Brillouin zone and construct high-order force constants matrix. Solution method: Combining the perturbation theory with the deformation potential theory to calculate the electronic transport properties, using the modified Debye-Callaway model to calculate the phonon transport properties. Research article Implementation of a Bayesian secondary structure estimation method for the SESCA circular dichroism analysis package Computer Physics Communications, Volume 266, 2021, Article 108022 Show abstract Circular dichroism spectroscopy is a structural biology technique frequently applied to determine the secondary structure composition of soluble proteins. Our recently introduced computational analysis package SESCA aids the interpretation of protein circular dichroism spectra and enables the validation of proposed corresponding structural models. To further these aims, we present the implementation and characterization of a new Bayesian secondary structure estimation method in SESCA, termed SESCA_bayes. SESCA_bayes samples possible secondary structures using a Monte Carlo scheme, driven by the likelihood of estimated scaling errors and non-secondary-structure contributions of the measured spectrum. SESCA_bayes provides an estimated secondary structure composition and separate uncertainties on the fraction of residues in each secondary structure class. It also assists efficient model validation by providing a posterior secondary structure probability distribution based on the measured spectrum. Our presented study indicates that SESCA_bayes estimates the secondary structure composition with a significantly smaller uncertainty than its predecessor, SESCA_deconv, which is based on spectrum deconvolution. Further, the mean accuracy of the two methods in our analysis is comparable, but SESCA_bayes provides more accurate estimates for circular dichroism spectra that contain considerable non-SS contributions. Program Title: SESCA_bayes CPC Library link to program files: https://doi.org/10.17632/5nnsbn6ync.1 Developer's repository link: https://www.mpibpc.mpg.de/sesca Licensing provisions: GPLv3 Programming language: Python Nature of problem: The circular dichroism spectrum of a protein is strongly correlated with its secondary structure composition. However, determining the secondary structure from a spectrum is hindered by non-secondary structure contributions and by scaling errors due the uncertainty of the protein concentration. If not taken properly into account, these experimental factors can cause considerable errors when conventional secondary-structure estimation methods are used. Because these errors combine with errors of the proposed structural model in a non-additive fashion, it is difficult to assess how much uncertainty the experimental factors introduce to model validation approaches based on circular dichroism spectra. Solution method: For a given measured circular dichroism spectrum, the SESCA_bayes algorithm applies Bayesian statistics to account for scaling errors and non-secondary structure contributions and to determine the conditional secondary structure probability distribution. This approach relies on fast spectrum predictions based on empirical basis spectrum sets and joint probability distribution maps for scaling factors and non-secondary structure distributions. Because SESCA_bayes estimates the most probable secondary structure composition based on a probability-weighted sample distribution, it avoids the typical fitting errors that occur during conventional spectrum deconvolution methods. It also estimates the uncertainty of circular dichroism based model validation more accurately than previous methods of the SESCA analysis package. Research article YAM2: Yet another library for the M 2 variables using sequential quadratic programming Computer Physics Communications, Volume 264, 2021, Article 107967 Show abstract The M 2 variables are devised to extend M T 2 by promoting transverse masses to Lorentz-invariant ones and making explicit use of on-shell mass relations. Unlike simple kinematic variables such as the invariant mass of visible particles, where the variable definitions directly provide how to calculate them, the calculation of the M 2 variables is undertaken by employing numerical algorithms. Essentially, the calculation of M 2 corresponds to solving a constrained minimization problem in mathematical optimization, and various numerical methods exist for the task. We find that the sequential quadratic programming method performs very well for the calculation of M 2 , and its numerical performance is even better than the method implemented in the existing software package for M 2 . As a consequence of our study, we have developed and released yet another software library, YAM2 , for calculating the M 2 variables using several numerical algorithms. Program title: YAM2 CPC Library link to program files: https://doi.org/10.17632/4g7wfd5fpb.1 Developer’s repository link: https://github.com/cbpark/YAM2 Licensing provisions: BSD 3-Clause Programming language: C ++ Nature of problem: The value and the solution of the M 2 variables can be obtained from the optimality and feasibility conditions of the nonlinearly constrained minimization problem in numerical optimization. To perform the calculation properly, one should employ suitable numerical algorithms with the appropriate formulation of the variables, having in mind the algorithmic efficiency and the computational cost. Solution method: There exist various numerical methods for solving constrained optimization problems. We have chosen the sequential quadratic programming method with the derivative-dependent quasi-Newton algorithm since it performs very efficiently to find the local minimum using derivative information. The method has been codified by using the implementation of the numerical algorithms in the NLopt library [1]. The new library also includes the routines using other algorithms for calculating M 2 , such as the augmented Lagrangian method. References: S. G. Johnson, “The NLopt nonlinear-optimization package,” https://github.com/stevengj/nlopt Research article MAELAS: MAgneto-ELAStic properties calculation via computational high-throughput approach Computer Physics Communications, Volume 264, 2021, Article 107964 Show abstract In this work, we present the program MAELAS to calculate magnetocrystalline anisotropy energy, anisotropic magnetostrictive coefficients and magnetoelastic constants in an automated way by Density Functional Theory calculations. The program is based on the length optimization of the unit cell proposed by Wu and Freeman to calculate the magnetostrictive coefficients for cubic crystals. In addition to cubic crystals, this method is also implemented and generalized for other types of crystals that may be of interest in the study of magnetostrictive materials. As a benchmark, some tests are shown for well-known magnetic materials. Program Title: MAELAS CPC Library link to program files : https://doi.org/10.17632/gxcdg3z7t6.1 Developer’s repository link: https://github.com/pnieves2019/MAELAS Code Ocean capsule : https://codeocean.com/capsule/0361425 Licensing provisions: BSD 3-clause Programming language: Python3 Nature of problem: To calculate anisotropic magnetostrictive coefficients and magnetoelastic constants in an automated way based on Density Functional Theory methods. Solution method: In the first stage, the unit cell is relaxed through a spin-polarized calculation without spin-orbit coupling. Next, after a crystal symmetry analysis, a set of deformed lattice and spin configurations are generated using the pymatgen library [1]. The energy of these states is calculated by the first-principles code VASP [3], including the spin-orbit coupling. The anisotropic magnetostrictive coefficients are derived from the fitting of these energies to a quadratic polynomial [2]. Finally, if the elastic tensor is provided [4], then the magnetoelastic constants are also calculated. Additional comments including restrictions and unusual features: This version supports the following crystal systems: Cubic (point groups 432, 4 ̄ 3 m , m 3 ̄ m ), Hexagonal ( 6 m m , 622, 6 ̄ 2 m , 6 ∕ m m m ), Trigonal (32, 3 m , 3 ̄ m ), Tetragonal ( 4 m m , 422, 4 ̄ 2 m , 4 ∕ m m m ) and Orthorhombic (222, 2 m m , m m m ). References: [1] S. P. Ong, W. D. Richards, A. Jain, G. Hautier, M. Kocher, S. Cholia, D. Gunter, V. L. Chevrier, K. A. Persson, and G. Ceder, Comput. Mater. Sci. 68, 314 (2013). [2] R. Wu, A. J. Freeman, Journal of Applied Physics 79, 6209–6212 (1996). [3] G. Kresse, J. Furthmüller, Phys. Rev. B 54 (1996) 11169. [4] S. Zhang and R. Zhang, Comput. Phys. Commun. 220, 403 (2017). <sup> ☆ </sup> The review of this paper was arranged by Prof. Blum Volker. <sup> ☆☆ </sup> This paper and its associated computer program are available via the Computer Physics Communications homepage on ScienceDirect ( http://www.sciencedirect.com/science/journal/00104655 ). View full text © 2021 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cpc.2021.108031", "PubYear": 2021, "Volume": "266", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Max-Born-Institut für Nichtlineare Optik und Kurzzeitspektroskopie, Max-Born-Straße 2A, 12489 Berlin, Germany"}], "References": [{"Title": "NTMpy: An open source package for solving coupled parabolic differential equations in the framework of the three-temperature model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "265", "Issue": "", "Page": "107990", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 88315417, "Title": "On the Usage of Generative Models for Network Anomaly Detection in Multivariate Time-Series", "Abstract": "<p>Despite the many attempts and approaches for anomaly de- tection explored over the years, the automatic detection of rare events in data communication networks remains a com- plex problem. In this paper we introduce Net-GAN, a novel approach to network anomaly detection in time-series, us- ing recurrent neural networks (RNNs) and generative ad- versarial networks (GAN). Different from the state of the art, which traditionally focuses on univariate measurements, Net-GAN detects anomalies in multivariate time-series, ex- ploiting temporal dependencies through RNNs. Net-GAN discovers the underlying distribution of the baseline, multi- variate data, without making any assumptions on its nature, offering a powerful approach to detect anomalies in com- plex, difficult to model network monitoring data. We further exploit the concepts behind generative models to conceive Net-VAE, a complementary approach to Net-GAN for net- work anomaly detection, based on variational auto-encoders (VAE). We evaluate Net-GAN and Net-VAE in different monitoring scenarios, including anomaly detection in IoT sensor data, and intrusion detection in network measure- ments. Generative models represent a promising approach for network anomaly detection, especially when considering the complexity and ever-growing number of time-series to monitor in operational networks.</p>", "Keywords": "anomaly detection; deep learning; generative models; multivariate time-series", "DOI": "10.1145/3466826.3466843", "PubYear": 2021, "Volume": "48", "Issue": "4", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IIE-FING, UDELAR, Uruguay"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "AIT Austrian Institute of Technology"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IIE-FING, UDELAR, Uruguay"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IIE-FING, UDELAR, Uruguay"}], "References": []}, {"ArticleId": 88315423, "Title": "Extending the Möbius Modeling Environment with the Advanced Replication Operator", "Abstract": "<p>Mobius is well known as a modeling and evaluation environment for performance and dependability indicators. It has been conceived in a modular and flexible fashion, to be easily expanded to incorporate new features, formalisms and tools. The need of modeling systems characterized by a large population of heterogeneous interacting components, which are nowadays more and more common in a variety of application contexts, provided the opportunity to focus on a new operator to efficiently manage non-anonymous replication, as requested for these systems. This tool paper presents the implementation of a new replication operator, called Advanced Rep, in Mobius. Efficiency of Advanced Rep is evaluated against a recently developed alternative solution.</p>", "Keywords": "composition operator; dependability; mobius modeling tool; performance; stochastic model", "DOI": "10.1145/3466826.3466846", "PubYear": 2021, "Volume": "48", "Issue": "4", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ISTI-CNR, Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ISTI-CNR, Pisa, Italy"}, {"AuthorId": 3, "Name": "Felicita Di Giandomenico", "Affiliation": "ISTI-CNR, Pisa, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ISTI-CNR"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "ITI-CSL, UIUC"}], "References": []}, {"ArticleId": 88315469, "Title": "Improving the Accuracy in Copy-Move Image Detection: A Model of Sharpness and Blurriness", "Abstract": "<p>The paper suggests a model based on the sharpness and blurriness to confirm the exact tampered areas from the suspicious ones which are detected from similar regions. In copy-move image detection, most research focus on comparing and finding areas with similar properties on the image. Actually, the same areas are not certainly done by copy-move manipulation, they may be the image texture. A model from the sharpness at the collage borderlines and the blurriness inside the image area is built to determine if the areas are really caused by the copy-move manipulation. The combination of feature extraction using oriented FAST and rotated BRIEF (ORB) and tampered region confirmation using a logistic regression model with 98% on accuracy proves the efficiency of the proposed methods.</p>", "Keywords": "ORB (oriented FAST and rotated BRIEF); Feature points; Copy-move detection; Sharpness; Blur; Feature descriptor; Logistic regression", "DOI": "10.1007/s42979-021-00682-w", "PubYear": 2021, "Volume": "2", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "International University, Ho Chi Minh City, Vietnam;Vietnam National University, Ho Chi Minh City, Vietnam"}, {"AuthorId": 2, "Name": "Tu-<PERSON><PERSON>", "Affiliation": "International University, Ho Chi Minh City, Vietnam;Vietnam National University, Ho Chi Minh City, Vietnam"}, {"AuthorId": 3, "Name": "Phuong<PERSON><PERSON><PERSON>", "Affiliation": "International University, Ho Chi Minh City, Vietnam;Vietnam National University, Ho Chi Minh City, Vietnam"}], "References": []}, {"ArticleId": 88315522, "Title": "FlexSched: Efficient scheduling techniques for concurrent kernel execution on GPUs", "Abstract": "<p>Nowadays, GPU clusters are available in almost every data processing center. Their GPUs are typically shared by different applications that might have different processing needs and/or different levels of priority. In this scenario, concurrent kernel execution can leverage the use of devices by co-executing kernels having a different or complementary resource utilization profile. A paramount issue in concurrent kernel execution on GPU is to obtain a suitable distribution of streaming multiproccessor (SM) resources among co-executing kernels to fulfill different scheduling aims. In this work, we present a software scheduler, named FlexSched , that employs a run-time mechanism with low overhead to perform intra-SM cooperative thread arrays (a.k.a. thread block) allocation of co-executing kernels. It also implements a productive online profiling mechanism that allows dynamically changing kernels resource assignation attending to the instant performance achieved for co-running kernels. An important characteristic of our approach is that off-line kernel analysis to establish the best resource assignment of co-located kernels is not required. Thus, it can run in any system where new applications must be immediately scheduled. Using a set of nine applications (13 kernels), we show our approach improves the co-execution performance of recent slicing methods. Moreover, our approach obtains a co-execution speedup of 1.40 (\times ) while slicing method just achieves 1.29 (\times ) . In addition, we test FlexSched in a real scheduling scenario where new applications are launched as soon as GPU resources become available. In this scenario, FlexSched reduces the average overall execution time by a factor of 1.25 (\times ) with respect to the time obtained when proprietary hardware (HyperQ) is employed. Finally, FlexSched is also used to implement scheduling policies that guarantee maximum turnaround time for latency sensitive applications while achieving high resource use through kernel co-execution.</p>", "Keywords": "GPU scheduling; Concurrent kernel execution; Online profiling; Simultaneous multikernel", "DOI": "10.1007/s11227-021-03819-z", "PubYear": 2022, "Volume": "78", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>Al<PERSON>", "Affiliation": "Department of Computer Architecture, University of Málaga, Málaga, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Architecture, University of Málaga, Málaga, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Architecture, University of Málaga, Málaga, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Architecture, University of Málaga, Málaga, Spain"}], "References": []}, {"ArticleId": 88315683, "Title": "A neuroimaging dataset on response inhibition and selective attention in adults and children with and without ADHD", "Abstract": "In this article we describe the dataset titled “Response inhibition and selective attention in adults and children with and without ADHD” which is publicly available on OpenNeuro.org. This dataset is comprised of neuroimaging and standardized cognitive assessment scores from 11 adults, 12 children diagnosed with Attention Deficit Hyperactivity Disorder (ADHD) and 15 age matched children without ADHD. Functional Magnetic Resonance Imaging (fMRI) data were collected while participants completed selective attention and response inhibition tasks designed and balanced for within or cross-task comparisons. Previous research utilizing this dataset has yet to explore associations between brain function and cognitive assessment scores or differences in neural processes across stimuli features making this dataset valuable for its future contributions to the field as well as replication of prior findings.", "Keywords": "fMRI ; Inhibition ; ADHD ; Children ; Selective attention ; Development", "DOI": "10.1016/j.dib.2021.107158", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Marisa N. Lytle", "Affiliation": "Department of Psychology, The Pennsylvania State University, University Park, PA, 16802, USA;Corresponding authors;@lytle_marisa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Radiology, NorthShore University HealthSystem, Evanston, Illinois, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology and Human Development, Vanderbilt University, Nashville, TN, USA;@DrJamesBooth"}], "References": []}, {"ArticleId": 88315684, "Title": "Belief base change as priority change: A study based on dynamic epistemic logic", "Abstract": "AGM's belief revision is one of the main paradigms in the study of belief change operations. In this context, belief bases (prioritised bases) have been primarily used to specify the agent's belief state. While the connection of iterated AGM-like operations and their encoding in dynamic epistemic logics have been studied before, few works considered how well-known postulates from iterated belief revision theory can be characterised by means of belief bases. Particularly, it has been shown that some postulates can be characterised through transformations in priority graphs, while others may not be represented that way. This work investigates the expressive power of prioritized bases, employing priority graphs as a representation for an agent's belief state and of transformations on such bases as representations for iterated relational belief change operators. As such, we propose a new representation for an agent's belief state, which we show can be used to characterize different iterated belief change postulates not previously representable using transformations on priority graphs.", "Keywords": "Dynamic epistemic logic ; Belief change ; Belief base change ; Preference change", "DOI": "10.1016/j.jlamp.2021.100689", "PubYear": 2021, "Volume": "122", "Issue": "", "JournalId": 781, "JournalTitle": "Journal of Logical and Algebraic Methods in Programming", "ISSN": "2352-2208", "EISSN": "2352-2216", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematics and Statistics, Federal University of Bahia, UFBA, Av. <PERSON><PERSON><PERSON>, S/N, Ondina, Salvador, BA, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, Federal University of Rio Grande do Sul, UFRGS Av. Bento Gonçalves, 9500, Porto Alegre, RS, Brazil"}], "References": []}, {"ArticleId": 88315725, "Title": "The role of scanning velocity on laser cladding of NiTi alloy onto stainless steel 316L substrate", "Abstract": "<p>In this research, Ni<sub>50.8</sub>Ti<sub>49.2</sub> pre-alloyed powder was used to produce NiTi shape memory alloy coating onto stainless steel 316L substrate. Micro direct metal deposition (μDMD) process was employed to produce coatings with the thickness of 200 μm. To investigate the effect of scanning velocity and the capability of the process, three different coatings were fabricated with three scanning velocities. The effects of scanning velocity variations were investigated by scanning electron microscopy and X-ray diffraction. The results revealed that a good and precise coating can be fabricated when the scanning velocity and the laser power are 200 mm/min and 25 W, respectively, while with the scanning velocity of 120 mm/min, crack formation is inevitable, and with the scanning velocity of 150 mm/min, it would be difficult to achieve a coating with accurate thickness of 200 μm.</p>", "Keywords": "Shape memory alloys; Additive manufacturing; Micro direct metal deposition; Scanning electron microscopy", "DOI": "10.1007/s00170-021-07112-4", "PubYear": 2021, "Volume": "117", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Materials and Metallurgical Engineering Department, Amirkabir University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Engineeering Department, University of Padova, Padova, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Materials and Metallurgical Engineering Department, Amirkabir University of Technology, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Materials and Metallurgical Engineering Department, Amirkabir University of Technology, Tehran, Iran"}], "References": []}, {"ArticleId": 88315735, "Title": "New developments in defecatory studies based on biomechatronics", "Abstract": "<b  >Introduction</b> Defecation is a complex process that is difficult to study and analyze directly. In anorectal disease conditions, the defecation process may be disturbed, resulting in symptoms including fecal incontinence and constipation. Current state-of-the-art technology measures various aspects of anorectal function but detailed analysis is impossible because they are stand-alone tests rather than an integrated multi-dimensional test. <b  >Objectives</b> The need for physiologically-relevant and easy-to-use diagnostic tests for identifying underlying mechanisms is substantial. We aimed to advance the field with integrated technology for anorectal function assessment. <b  >Methods</b> We developed a simulated stool named Fecobionics that integrates several tests to assess defecation pressures, dimensions, shape, orientation and bending during evacuation. A novelty is that pressures are measured in axial direction, i.e. in the direction of the trajectory. Using this novel tool, we present new analytical methods to calculate physiologically relevant parameters during expulsion in normal human subjects. <b  >Results</b> Data are reported from 28 human subjects with progressively more advanced versions of Fecobionics. A new concept utilizes the rear-front pressure (preload-afterload) diagram for computation of novel defecation indices. Fecobionics obtained physiological data that cannot be obtained with current state-of-the-art technologies. <b  >Conclusion</b> Fecobionics measures well known parameters such as expulsion time and pressures as well as new metrics including defecation indices. The study suggests that Fecobionics is effective in evaluation of key defecatory parameters and well positioned as an integrated technology for assessment of anorectal function and dysfunction.", "Keywords": "Anal sphincter relaxation;Anorectal physiology;Bionics;Defecation;Fecobionics", "DOI": "10.1016/j.jare.2021.05.005", "PubYear": 2022, "Volume": "35", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "California Medical Innovations Institute, San Diego, CA, United States. ;Department of Surgery, The Chinese University of Hong Kong, Shatin, Hong Kong."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "California Medical Innovations Institute, San Diego, CA, United States. ;School of Microelectronics and Communication Engineering, Chongqing University, Shapingba, Chongqing 400044, China."}, {"AuthorId": 3, "Name": "<PERSON>.<PERSON><PERSON> Chen", "Affiliation": "Department of Surgery, The Chinese University of Hong Kong, Shatin, Hong Kong."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Surgery, The Chinese University of Hong Kong, Shatin, Hong Kong."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Surgery, The Chinese University of Hong Kong, Shatin, Hong Kong."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Surgery, The Chinese University of Hong Kong, Shatin, Hong Kong."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Surgery, The Chinese University of Hong Kong, Shatin, Hong Kong."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Surgery, The Chinese University of Hong Kong, Shatin, Hong Kong."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "GIOME Institute, Hong Kong."}, {"AuthorId": 10, "Name": "G.S. <PERSON>", "Affiliation": "California Medical Innovations Institute, San Diego, CA, United States."}], "References": [{"Title": "Mechanophysiological analysis of anorectal function using simulated feces in human subjects", "Authors": "Daming Sun; Donghua Liao; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "", "Page": "245", "JournalTitle": "Journal of Advanced Research"}]}, {"ArticleId": 88315863, "Title": "Optimization Algorithm to Sequence the Management Processes in Information Technology Departments", "Abstract": "<p>The most important standard in technology services management is the Information Technology Infrastructure Library (ITIL). The literature review developed shows that one of the most important questions to answer is finding the sequence of processes to be implemented, mainly in small companies with few resources. The purpose of this paper is to show a methodology that defines an optimal specific sequence of processes for each small company depending on internal and external parameters. The main contribution of this paper is a proven methodology to obtain a particular sequence of ITIL processes specifically adapted to each company, based on a mathematical and statistical model that uses data from a web survey. Its application generates an optimal sequence of ITIL processes. The methodology has been applied with successful results in a real case, and it shows specific benefits over the previous approaches. The main learning objective of this research is a proven method to obtain an optimal sequence of processes for the implementation of ITIL in small companies. Finally, some future works are presented.</p>", "Keywords": "ITIL; sequence; processes; small company; methodology ITIL ; sequence ; processes ; small company ; methodology", "DOI": "10.3390/computation9050060", "PubYear": 2021, "Volume": "9", "Issue": "5", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Escuela de Ciencias e Ingeniería, Universidad a Distancia de Madrid (UDIMA), Ctra A Coruña km38, 5 Vía de Servicio and Collado <PERSON>, 28400 Madrid, Spain Academic Editor: <PERSON><PERSON>"}], "References": [{"Title": "COVID-19 and Its Impacts on Managing Information Systems", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "4", "Page": "267", "JournalTitle": "Information Systems Management"}, {"Title": "IT architecture flexibility and IT governance decentralisation as drivers of IT-enabled dynamic capabilities and competitive performance: The moderating effect of the external environment", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "5", "Page": "512", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Methodology to Improve Services in Small IT Centers: Application to Educational Centers", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "8", "JournalTitle": "Computers"}]}, {"ArticleId": ********, "Title": "Event-oriented linkable and traceable anonymous authentication and its application to voting", "Abstract": "In the pursuit of anonymous authentication schemes that balance anonymity and accountability, various authentication schemes have been proposed. However, most of existing schemes cannot achieve linkability while holding public traceability. In this paper, we introduce a new variant of anonymous authentication called event-oriented linkable and traceable anonymous authentication ( EOLTAA ) and provide a generic construction. In an EOLTAA scheme, a message to be authenticated binds an event . If two different messages binding the same event are authenticated by an identical user, anyone can link the two authentications and further reveal the user’s identity. Then we formally define the security requirements of EOLTAA , including unforgeability, anonymity, linkability and public traceability. We give a generic construction satisfying the security requirements. With this new authentication scheme, we construct the first decentralized, anonymous, linkable and publicly traceable e-voting based on blockchain.", "Keywords": "Anonymous authentication ; Linkability ; Traceability ; Voting ; Blockchain", "DOI": "10.1016/j.jisa.2021.102865", "PubYear": 2021, "Volume": "60", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Jinan University, Guangzhou 510632, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Jinan University, Guangzhou 510632, China;Corresponding author"}, {"AuthorId": 3, "Name": "Yong<PERSON> Wu", "Affiliation": "College of Information Science and Technology, Jinan University, Guangzhou 510632, China"}], "References": [{"Title": "Efficient, Coercion-free and Universally Verifiable Blockchain-based Voting", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "174", "Issue": "", "Page": "107234", "JournalTitle": "Computer Networks"}, {"Title": "Blockchain voting: Publicly verifiable online voting protocol without trusted tallying authorities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Surya Nepal", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "859", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 88315898, "Title": "Identity-based outsider anonymous cloud data outsourcing with simultaneous individual transmission for IoT environment", "Abstract": "The integration of the Internet of Things (IoT) and cloud computing has become an attractive cloud-oriented big data processing paradigm, which is playing an important role in efficiency and productivity for digitalization of numerous IoT enabled industries. However, cloud-assisted IoT is also becoming an increasingly attractive target for various cyber-attacks, including the authenticity of outsourcing data, untrustworthiness of third parties, and data security and privacy. As a potential and promising solution to securely outsource data, we present the first construction for an efficient cloud data outsourcing system with simultaneous individual transmission preserving outsider anonymity of the subscribed consumer set. In our system, a data owner generates personalized data for each of the consumers in a group and transmits a common encrypted data for the group in such a way that the subscribed consumer set is completely hidden from the outsiders. Personalized data can be recovered only by an authorized consumer, while the common data can be decrypted by all the authorized consumer in that group. The communication bandwidth is compact in our construction, and the decryption algorithm requires significantly less computation cost. We design our scheme using asymmetric bilinear map over the prime order group to prevent fault attacks on symmetric bilinear map. Our construction is built in identity-based setting without any non-standard q - type security assumption and does not use random oracles . Our scheme enjoys adaptive security against an indistinguishable chosen-plaintext attack under the hardness of the standard decisional bilinear Diffie–<PERSON> exponent problem. Furthermore, our design supports an exponential number of consumers as the size of the valid identity set grows exponentially with the security parameter, whereas it is only polynomial in the security parameter for the existing cloud data outsourcing systems. In particular, the implementation and performance analysis explicates the advantages of our design for resource-constrained IoT enabled frameworks.", "Keywords": "Internet of Things ; Cloud computing ; Anonymity and privacy ; Remote e-healthcare", "DOI": "10.1016/j.jisa.2021.102870", "PubYear": 2021, "Volume": "60", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "Mriganka Mandal", "Affiliation": "Department of Mathematics, Indian Institute of Technology Kharagpur, Kharagpur 721302, India;Graduate School of Information Science and Technology, The University of Tokyo, Tokyo, Japan;Corresponding author at: Graduate School of Information Science and Technology, The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Kharagpur, Kharagpur 721302, India"}], "References": []}, {"ArticleId": 88315924, "Title": "Network Traffic Anomaly Detection via Deep Learning", "Abstract": "<p>Network intrusion detection is a key pillar towards the sustainability and normal operation of information systems. Complex threat patterns and malicious actors are able to cause severe damages to cyber-systems. In this work, we propose novel Deep Learning formulations for detecting threats and alerts on network logs that were acquired by pfSense, an open-source software that acts as firewall on FreeBSD operating system. pfSense integrates several powerful security services such as firewall, URL filtering, and virtual private networking among others. The main goal of this study is to analyse the logs that were acquired by a local installation of pfSense software, in order to provide a powerful and efficient solution that controls traffic flow based on patterns that are automatically learnt via the proposed, challenging DL architectures. For this purpose, we exploit the Convolutional Neural Networks (CNNs), and the Long Short Term Memory Networks (LSTMs) in order to construct robust multi-class classifiers, able to assign each new network log instance that reaches our system into its corresponding category. The performance of our scheme is evaluated by conducting several quantitative experiments, and by comparing to state-of-the-art formulations.</p>", "Keywords": "pfSense software; semi-supervised anomaly detection; deep feature learning; long short term memory networks; convolutional neural networks; pfSense software; suricata network logs anomaly detection pfSense software ; semi-supervised anomaly detection ; deep feature learning ; long short term memory networks ; convolutional neural networks ; pfSense software ; suricata network logs anomaly detection", "DOI": "10.3390/info12050215", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 <PERSON><PERSON><PERSON>, Greece↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "Terpsichori-<PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 Chalkida, Greece"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 Chalkida, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Intrasoft International S.A., L-1253 Luxembourg, Luxembourg"}, {"AuthorId": 5, "Name": "Sofia Tsekeridou", "Affiliation": "Intrasoft International S.A., 19002 Athens, Greece"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 Chalkida, Greece"}], "References": [{"Title": "Machine Learning Models for Secure Data Analytics: A taxonomy and threat model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "406", "JournalTitle": "Computer Communications"}, {"Title": "Using a Long Short-Term Memory Recurrent Neural Network (LSTM-RNN) to Classify Network Attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "243", "JournalTitle": "Information"}, {"Title": "Network intrusion detection with a novel hierarchy of distances between embeddings of hash IP addresses", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "219", "Issue": "", "Page": "106887", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 88315950, "Title": "Systemic optimization and coordination of the work volume financing to ensure production and technical maintenance of oil and gas facilities", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2021-5(574)-12-16", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Oil and Gas “Gubkin University”"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Oil and Gas “Gubkin University”"}], "References": []}, {"ArticleId": 88315977, "Title": "A Fuzzy Logic Adaptive Image Resize (Resolution) Level using Cross-Layering in Wireless Multimedia Sensor Networks", "Abstract": "The increasing interest in wireless sensor networks with the rapid growth in micro-electronics technology has made it possible to deliver multimedia content over Wireless Multimedia Sensor Networks (WMSNs). There are several main peculiarities that make the delivery of multimedia content over WMSN challenging. Most of these are due to the processing, timing, and other quality of service requirements. Furthermore, WMSNs are susceptible to rapid degradation since they deal with large amount of data that require processing and transmission power. The traditional protocol stack architectures were designed to follow a strictly layered approach that may not support the essential requirements of these networks. In order to overcome the challenges posed by the limited bandwidth and resources associated with WMSN, the integration of functionality between different layers should be exploited. In this paper, a cross-layer design approach is proposed to overcome such challenges. In the proposed approach, the concept of cross-layering and fuzzy logic have been exploited to monitor the network conditions and control the amount of the multimedia data in order to utilize the available resources efficiently and improve the applications quality of service (QoS). The simulation results have shown better resource utilization, stability, and fairness in quality metrics consideration. The proposed approach has shown to be efficient compared to the conventional scheme in terms of bandwidth utilization, power consumption, delay, loss, and images quality. Moreover, the proposed approach has proved its efficiency compared to other adaptive methods especially in term of the quality of the received images at the receiver side.", "Keywords": "Image resize;WMSN;Cross-layer;Fuzzy", "DOI": "10.5120/ijca2021921308", "PubYear": 2021, "Volume": "183", "Issue": "3", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 88315992, "Title": "Collusion resistant secret sharing scheme for secure data storage and processing over cloud", "Abstract": "Shamir secret sharing (SSS) is considered as a promising method for outsourcing the data securely due to its ability to support privacy preserving data processing while ensuring data availability. Major drawbacks of original SSS scheme are its susceptibility to collusion attack and high storage overhead. Hence in this paper, we first propose a modified SSS scheme (MSSS) which can resist collusion attack and provide adequate security even with two shares. However, the storage overhead of this scheme is high when it is extended to ensure data availability and integrity in cloud storage systems. Therefore, a modified ramp secret sharing (MRSS) with reduced storage overhead compared to MSSS scheme is also proposed in this paper. The proposed schemes can be employed for any privacy preserving data processing application which involve linear operations on the data. In this paper, in order to demonstrate the capability of proposed schemes to support privacy preserving data processing, Haar discrete wavelet transform (DWT) computation on medical images is considered as an example as DWT is widely used in feature extraction for disease diagnosis from pathological images. We present an algorithm for computing Haar DWT from medical image shares. The security of the proposed scheme is evaluated through mathematical cryptanalysis and resistance against various statistical attacks. The performance analysis shows that shared domain DWT offers same accuracy levels as that of plaintext domain.", "Keywords": "Data security ; Cloud computing ; Privacy preserving ; Secret sharing ; Homomorphic computation", "DOI": "10.1016/j.jisa.2021.102869", "PubYear": 2021, "Volume": "60", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "Lakshmi V.S.", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Calicut, Kerala, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>thi S.", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Calicut, Kerala, India"}, {"AuthorId": 3, "Name": "Deepthi P.P.", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Calicut, Kerala, India"}], "References": [{"Title": "Cloud computing security taxonomy: From an atomistic to a holistic view", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "620", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 88315996, "Title": "Scaling & fuzzing: Personal image privacy from automated attacks in mobile cloud computing", "Abstract": "The mobile cloud computing (MCC) paradigm provides a range of useful services to smart phone users and enhances the user experience significantly. But, as MCC requires the data to be offloaded to an external server, there are serious concerns regarding the privacy of the users’ personal data such as images. For instance, a cloud server could perform image segmentation on user images to extract interesting artifacts such as restaurants, user’s clothing preferences, tourist locations, participation in social events and so on, which characterize the user’s personal life. The leakage of such private information could lead to milder consequences like targeted advertising or more serious consequences like identity theft. In this work, to protect the privacy of user images, we describe a privacy-preserving image filtering for mobile cloud computing that protects against automated inference attacks based on techniques like image segmentation. The key intuition of our approach is to leverage the inherent properties of the discrete Fourier transform (DFT), which transforms each image pixel into a complex value real and imaginary parts which can be processed independently. By dividing the image in this manner, we are able to process distinct parts of the image on different non-colluding servers and aggregate the results at the client. Furthermore, to prevent information leakage at individual servers, we obfuscate the data sent to any given server using an efficient reversible transformation. We prove our approach to be secure under the semi-honest model and non-colluding servers where at least one server does not collude with the rest of the servers. In comparison to the existing paradigm of outsourced privacy preserving computation, i.e., processing encrypted data using homomorphic encryption, our approach employs easy-to-implement obfuscation techniques without any key management overhead at the client. Using experimental evaluation as well as information theoretic leakage evaluation, we show that our approach is efficient and suitable for users of mobile devices.", "Keywords": "Mobile cloud ; Privacy ; Computation ; Image ; Filtering", "DOI": "10.1016/j.jisa.2021.102850", "PubYear": 2021, "Volume": "60", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Malaviya National Institute of Technology Jaipur, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Malaviya National Institute of Technology Jaipur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mahindra École Centrale, Hyderabad, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Jammu, India"}], "References": [{"Title": "A nifty collaborative analysis to predicting a novel tool (DRFLLS) for missing values estimation", "Authors": "<PERSON><PERSON><PERSON>; Ayad F. <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "1", "Page": "555", "JournalTitle": "Soft Computing"}, {"Title": "Privacy-preserving self-serviced medical diagnosis scheme based on secure multi-party computation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "101701", "JournalTitle": "Computers & Security"}, {"Title": "QuickDedup: Efficient VM deduplication in cloud computing environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "18", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 88315999, "Title": "FUPE: A security driven task scheduling approach for SDN-based IoT–Fog networks", "Abstract": "Fog computing is a paradigm to overcome the cloud computing limitations which provides low latency to the users’ applications for the Internet of Things (IoT). Software-defined networking (SDN) is a practical networking infrastructure that provides a great capability in managing network flows. SDN switches are powerful devices, which can be used as fog devices/fog gateways simultaneously. Hence, fog devices are more vulnerable to several attacks. TCP SYN flood attack is one of the most common denial of service attacks, in which a malicious node produces many half-open TCP connections on the targeted computational nodes so as to break them down. Motivated by this, in this paper, we apply SDN concepts to address TCP SYN flood attacks in IoT–fog networks . We propose FUPE, a security-aware task scheduler in IoT–fog networks. FUPE puts forward a fuzzy-based multi-objective particle swarm Optimization approach to aggregate optimal computing resources and providing a proper level of security protection into one synthetic objective to find a single proper answer. We perform extensive simulations on IoT-based scenario to show that the FUPE algorithm significantly outperforms state-of-the-art algorithms. The simulation results indicate that, by varying the attack rates, the number of fog devices, and the number of jobs, the average response time of FUPE improved by 11% and 17%, and the network utilization of FUPE improved by 10% and 22% in comparison with Genetic and Particle Swarm Optimization algorithms, respectively.", "Keywords": "Internet of Things (IoT) ; Fog computing ; Software defined networking (SDN) ; Resource management ; Multi-objective particle swarm optimization (MOPSO) ; Fuzzy logic", "DOI": "10.1016/j.jisa.2021.102853", "PubYear": 2021, "Volume": "60", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technologies (DIETI), University of Napoli ”Federico II”, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "GIC & 6GIC, Institute for Communication Systems (ICS), University of Surrey, Guildford, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Bu-Ali <PERSON> university, computer engineering department, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Arak university, computer engineering department, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technologies (DIETI), University of Napoli ”Federico II”, Italy;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technologies (DIETI), University of Napoli ”Federico II”, Italy"}], "References": [{"Title": "Find my trustworthy fogs: A fuzzy-based trust evaluation framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "562", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "BigTrustScheduling: Trust-aware big data task scheduling approach in cloud computing environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "1079", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 88316132, "Title": "Deep-MEG: spatiotemporal CNN features and multiband ensemble classification for predicting the early signs of Alzheimer’s disease with magnetoencephalography", "Abstract": "In this paper, we present the novel Deep-MEG approach in which image-based representations of magnetoencephalography (MEG) data are combined with ensemble classifiers based on deep convolutional neural networks. For the scope of predicting the early signs of Alzheimer’s disease (AD), functional connectivity (FC) measures between the brain bio-magnetic signals originated from spatially separated brain regions are used as MEG data representations for the analysis. After stacking the FC indicators relative to different frequency bands into multiple images, a deep transfer learning model is used to extract different sets of deep features and to derive improved classification ensembles. The proposed Deep-MEG architectures were tested on a set of resting-state MEG recordings and their corresponding magnetic resonance imaging scans, from a longitudinal study involving 87 subjects. Accuracy values of 89% and 87% were obtained, respectively, for the early prediction of AD conversion in a sample of 54 mild cognitive impairment subjects and in a sample of 87 subjects, including 33 healthy controls. These results indicate that the proposed Deep-MEG approach is a powerful tool for detecting early alterations in the spectral–temporal connectivity profiles and in their spatial relationships.", "Keywords": "Alzheimer’s disease; Deep CNN feature transfer; Functional connectivity; Deep learning; Ensemble classification; Magnetoencephalography", "DOI": "10.1007/s00521-021-06105-4", "PubYear": 2021, "Volume": "33", "Issue": "21", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Roma, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Cognitive and Computational Neuroscience UCM-UPM Centre for Biomedical Technology, Madrid, Spain;Department of Experimental Psychology, Universidad Complutense of Madrid, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Roma, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Roma, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Laboratory of Cognitive and Computational Neuroscience UCM-UPM Centre for Biomedical Technology, Madrid, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Laboratory of Cognitive and Computational Neuroscience UCM-UPM Centre for Biomedical Technology, Madrid, Spain;Department of Experimental Psychology, Universidad Complutense of Madrid, Madrid, Spain"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Roma, Italy"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Roma, Italy"}], "References": [{"Title": "A deep convolutional neural network model for automated identification of abnormal EEG signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "15857", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Convolutional neural networks for classification of music-listening EEG: comparing 1D convolutional kernels with 2D kernels and cerebral laterality of musical influence", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "13", "Page": "8867", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Automated heartbeat classification based on deep neural network with multiple input layers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "105036", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A review of deep learning with special emphasis on architectures, applications and recent trends", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105596", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multiscale-based multimodal image classification of brain tumor using deep learning method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "5543", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 88316273, "Title": "Online multi-object tracking using multi-function integration and tracking simulation training", "Abstract": "<p>Recently, with the development of deep-learning, the performance of multi-object tracking algorithms based on deep neural networks has been greatly improved. However, most methods separate different functional modules into multiple networks and train them independently on specific tasks. When these network modules are used directly, they are not compatible with each other effectively, nor can they be better adapted to the multi-object tracking task, which leads to a poor tracking effect. Therefore, a network structure is designed to aggregate the regression of objects between frames and the extraction of appearance features into one model to improve the harmony between various functional modules of multi-object tracking. To improve the support for the multi-object tracking task, an end-to-end training method is also proposed to simulate the multi-object tracking process during the training and expand the training data by using the historical position of the target combined with the prediction of the motion model. A metric loss that can take advantage of the historical appearance features of the target is also used to train the extraction module of appearance features to improve the temporal correlation of extracted appearance features. Evaluation results on the MOTChallenge benchmark datasets show that the proposed approach achieves state-of-the-art performance.</p>", "Keywords": "Deep learning; Neural network; Computer vision; Multi-object tracking; Object detection", "DOI": "10.1007/s10489-021-02457-5", "PubYear": 2022, "Volume": "52", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Computer Science, Jiangnan University, Wuxi, China;Key Laboratory of Advanced Process Control for Light Industry (Jiangnan University), Ministry of Education, Wuxi, China"}, {"AuthorId": 2, "Name": "Hongwei Ge", "Affiliation": "School of Artificial Intelligence and Computer Science, Jiangnan University, Wuxi, China;Key Laboratory of Advanced Process Control for Light Industry (Jiangnan University), Ministry of Education, Wuxi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Computer Science, Jiangnan University, Wuxi, China;Key Laboratory of Advanced Process Control for Light Industry (Jiangnan University), Ministry of Education, Wuxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Medical Image Processing Group, Department of Radiology, University of Pennsylvania, Philadelphia, USA"}, {"AuthorId": 5, "Name": "Shuzhi Su", "Affiliation": "School of Computer Science and Engineering, Anhui University of Science & Technology, Huainan, China"}], "References": [{"Title": "HOTA: A Higher Order Metric for Evaluating Multi-object Tracking", "Authors": "<PERSON><PERSON><PERSON> Lu<PERSON>n; Aljos̆a Os̆ep; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "2", "Page": "548", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 88316316, "Title": "SkePU 3: Portable High-Level Programming of Heterogeneous Systems and HPC Clusters", "Abstract": "We present the third generation of the C++-based open-source skeleton programming framework SkePU. Its main new features include new skeletons, new data container types, support for returning multiple objects from skeleton instances and user functions, support for specifying alternative platform-specific user functions to exploit e.g. custom SIMD instructions, generalized scheduling variants for the multicore CPU backends, and a new cluster-backend targeting the custom MPI interface provided by the StarPU task-based runtime system. We have also revised the smart data containers’ memory consistency model for automatic data sharing between main and device memory. The new features are the result of a two-year co-design effort collecting feedback from HPC application partners in the EU H2020 project EXA2PRO, and target especially the HPC application domain and HPC platforms. We evaluate the performance effects of the new features on high-end multicore CPU and GPU systems and on HPC clusters.", "Keywords": "High-level parallel programming; Heterogeneous computing; Skeleton programming; Co-design approach; Cluster computing", "DOI": "10.1007/s10766-021-00704-3", "PubYear": 2021, "Volume": "49", "Issue": "6", "JournalId": 12927, "JournalTitle": "International Journal of Parallel Programming", "ISSN": "0885-7458", "EISSN": "1573-7640", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "PELAB, Department of Computer and Information Science, Linköping University, Linköping, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "PELAB, Department of Computer and Information Science, Linköping University, Linköping, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PELAB, Department of Computer and Information Science, Linköping University, Linköping, Sweden"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "PELAB, Department of Computer and Information Science, Linköping University, Linköping, Sweden"}], "References": []}, {"ArticleId": 88316516, "Title": "Improved tunicate swarm algorithm: Solving the dynamic economic emission dispatch problems", "Abstract": "This study proposes improved tunicate swarm algorithm (ITSA) for solving and optimizing the dynamic economic emission dispatch (DEED) problem. The DEED optimization target is to reduce the fuel cost and pollutant emission of the power system. In addition, DEED is a complex optimization problem and contains multiple optimization goals. To strengthen the ability of the ITSA algorithm for solving DEED, the tent mapping is employed to generate initial population for improving the directionality in the optimization process. Meanwhile, the gray wolf optimizer is used to generate the global search vector for improving global exploration ability, and the Levy flight is introduced to expand the search range. Three test systems containing 5, 10 and 15 generator units are employed to verify the solving performance of ITSA. The test results show that the ITSA algorithm can provide a competitive scheduling plan for test systems containing different units. ITSA proposed algorithm gives the optimal economic and environmental dynamic dispatch scheme for achieving more precise dispatch strategy.", "Keywords": "Soft-computing ; Improved tunicate swarm algorithm ; Power system ; Dynamic economic emission dispatch ; Fuel cost", "DOI": "10.1016/j.asoc.2021.107504", "PubYear": 2021, "Volume": "108", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Reliability and Intelligence of Electrical Equipment, Hebei University of Technology, Tianjin 300130, China;Key Laboratory of Electromagnetic Field and Electrical Apparatus Reliability of Hebei Province, Hebei University of Technology, Tianjin 300130, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Reliability and Intelligence of Electrical Equipment, Hebei University of Technology, Tianjin 300130, China;Key Laboratory of Electromagnetic Field and Electrical Apparatus Reliability of Hebei Province, Hebei University of Technology, Tianjin 300130, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Innovation and Circular Economy, Asia University, Taiwan;Department of Medical Research, China Medical University Hospital, China Medical University, Taichung, Taiwan;Faculty of Economics and Management, Universiti Kebangsaan Malaysia, Malaysia;Corresponding author at: Institute of Innovation and Circular Economy, Asia University, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Reliability and Intelligence of Electrical Equipment, Hebei University of Technology, Tianjin 300130, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty Research Centre for Business in Society, Coventry University, United Kingdom"}], "References": [{"Title": "Solution to economic emission dispatch problem including wind farms using Exchange Market Algorithm Method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106044", "JournalTitle": "Applied Soft Computing"}, {"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An improved particle swarm optimization with clone selection principle for dynamic economic emission dispatch", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "20", "Page": "15249", "JournalTitle": "Soft Computing"}, {"Title": "Hybrid Particle Swarm and Grey Wolf Optimizer and its application to clustering optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107061", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improved elephant herding optimization using sine–cosine mechanism and opposition based learning for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "172", "Issue": "", "Page": "114607", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 88316539, "Title": "WITHDRAWN: An Energy Efficient Hybrid Priority Assigned Laxity Algorithm for Load Balancing in Fog Computing", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.suscom.2021.100566", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A load balancing and optimization strategy (LBOS) using reinforcement learning in fog computing environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "4951", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Distributed load balancing for heterogeneous fog computing infrastructures in smart cities", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "67", "Issue": "", "Page": "101221", "JournalTitle": "Pervasive and Mobile Computing"}]}, {"ArticleId": 88316575, "Title": "Cooperative computation offloading and resource allocation for delay minimization in mobile edge computing", "Abstract": "Mobile edge computing (MEC) is a promising paradigm, which brings computation resources in proximity to mobile devices and allows the tasks of mobile devices to be offloaded to MEC servers with low latency. The joint problem of cooperative computation task offloading and resource allocation is a challenging issue. The joint problem of cooperative computation task offloading scheme and resource assignment in MEC is investigated in this paper, where the vertical cooperation among mobile devices, mobile edge server nodes and mobile cloud server nodes is considered, and the horizontal computation cooperation between edge nodes is considered as well. A computation offloading decision, cooperative selection, power allocation and CPU cycle frequency assignment problem is formulated. The objective is to minimize the latency while guaranteeing the constraint of transmission power, energy consumption and CPU cycle frequency. The formulated latency optimization problem is a nonconvex mixed-integer problem in general, which has binary variables and continuous variables. In order to solve the formulated problem. A joint iterative algorithm based on the Lagrangian dual decomposition, ShengJin Formula method, and monotonic optimization method is proposed. The CPU cycle frequence allocation is handled by the ShengJin Formula method due to the cubic equation of one variable about the CPU frequence allocation. The transmission power assignment is handled by the monotonic optimization method. In the algorithm convergence with different number of tasks, the proposed algorithm can quickly and effectively reach the convergence state and getting the minimum task execution delay. Numerical results demonstrate that the proposed algorithm outperforms the Full MEC, Full Local and Full Cloud three schemes in terms of execution latency.", "Keywords": "Mobile edge computing ; Cooperative computation offloading ; Delay minimization ; Resource allocation", "DOI": "10.1016/j.sysarc.2021.102167", "PubYear": 2021, "Volume": "118", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Central South University of Forestry and Technology, Changsha, 410004, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Central South University of Forestry and Technology, Changsha, 410004, China;Corresponding authors;@qq.com"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Central South University of Forestry and Technology, Changsha, 410004, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha 410010, China"}], "References": [{"Title": "Efficient computation offloading for Internet of Vehicles in edge computing-assisted 5G networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "4", "Page": "2518", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 88316595, "Title": "Augmented reality applications for K-12 education: A systematic review from the usability and user experience perspective", "Abstract": "In the past two decades, we have witnessed soaring efforts in applying Augmented Reality (AR) technology in education. Several systematic literature reviews (SLRs) were conducted to study AR educational applications (AREAs) and associated methodologies, primarily from the pedagogical rather than from the human–computer interaction (HCI) perspective. These reviews vary in goal, scale, scope, technique, outcome and quality. To bridge the gaps identified in these SLRs, ours is to meet fourfold objectives: to ground the analysis deeper in the usability and user experience (UX) core concepts and methods; to study the learning effect and usability/UX of AREAs and their relations by learner age; to reflect on the prevailing SLR process and propose improvement; to draw implications for the future development of AREAs. Our searches in four databases returned 714 papers of which 42, together with 7 from three existing SLRs, were included in the final analysis. Several intriguing findings have been identified: (i) the insufficient grounding in usability/UX frameworks indicates that there seems a disconnection between the HCI and technology-enhanced learning community; (ii) a lack of innovative AR-specific usability/UX evaluation methods and the continuing reliance on questionnaire may hamper the advances of AREAs; (iii) the learner age seems not a significant factor in determining the perceived usability and UX or the learning effect of AREAs; (iv) a limited number of studies at home suggests the missed opportunity of mobilizing parents to support children to deploy AREAs in different settings; (v) the number of AREAs for children with special needs remains disappointedly low; (vi) the threat of predatory journals to the quality of bibliometric sources amplifies the need for a robust approach to the quality assessment for SLR and transparency of interim results. Implications of these issues for future research and practice on AREAs are drawn.", "Keywords": "Augmented reality ; Education ; Usability ; User experience ; Systematic review", "DOI": "10.1016/j.ijcci.2021.100321", "PubYear": 2021, "Volume": "30", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, University of Leicester, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Informatics, University of Leicester, UK"}], "References": []}, {"ArticleId": 88316625, "Title": "A Survey on Cache Timing Channel Attacks for Multicore Processors", "Abstract": "<p>Cache timing channel attacks has attained a lot of attention in the last decade. These attacks exploits the timing channel created by the significant time gap between cache and main memory accesses. It has been successfully implemented to leak the secret key of various cryptography algorithms. The latest advancements in cache attacks also exploit other micro-architectural components such as hardware prefetchers, branch predictor, and replacement engine, in addition to the cache memory. Detection of these attacks is a difficult task as the attacker process running in the processor must be detected before significant portion of the attack is complete. The major challenge for mitigation and defense mechanisms against these attacks is maintaining the system performance while disabling or avoiding these attacks. The overhead caused by detection, mitigation and defense mechanism must not be significant to system’s performance. This paper discusses the research carried out in three aspects of cache security: cache timing channel attacks, detection techniques of these attacks, and defense mechanisms in details.</p>", "Keywords": "Cache security; Cache timing channel attack; Side-channel attack; Covert channel attack; Detection techniques; Defense mechanisms", "DOI": "10.1007/s41635-021-00115-3", "PubYear": 2021, "Volume": "5", "Issue": "2", "JournalId": 7099, "JournalTitle": "Journal of Hardware and Systems Security", "ISSN": "2509-3428", "EISSN": "2509-3436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Ropar, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Ropar, Punjab, India"}], "References": []}, {"ArticleId": 88316626, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-9473(21)00091-8", "PubYear": 2021, "Volume": "160", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [], "References": []}, {"ArticleId": 88316669, "Title": "A Systematic Review of Blogging : Opportunities and Challenges", "Abstract": "<p>By this research, we want to display the key factors of how effective blogging as technology is. We collected information curated by several researchers on the same for understanding their point of view as well. By doing this we found out what makes blogging the best way of sharing information online. We also analyzed the technological aspect of modern-day blogging which includes modern technologies being used, privacy rules and control as well as data analytics. By these approaches, we showed how blogging is not only the best medium for sharing information but can also help in other fields as well like marketing, education, data analysis, community development. With this research, we also wanted to highlight the downsides of using blogging or micro-blogging.</p>", "Keywords": "", "DOI": "10.32628/CSEIT2172133", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}], "References": []}, {"ArticleId": 88316670, "Title": "IoT Based Waste Tracking and Collection", "Abstract": "<p>Waste collection and resource management on time has been always a challenge rather efficient collection of waste by corporation. This project aims to achieve this goal with help of Internet of Things and mobile application and intend to retrieve data from citizens to help us efficiently manage collection triggers of collection vehicle and resources according to need, so that productivity of waste collection increases. Data suggest that in urban as well as rural area management struggles with the efficiency and resource planning as there exist few or no management system which will fit all scenarios. As we know data is new age oil. We intend to generate data that can be useful in various aspects ranging from analysis of public health to resource planning and as proof of concept we will demonstrate how we can efficiently utilize resources i.e vehicle.</p>", "Keywords": "", "DOI": "10.32628/CSEIT217319", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Datta Meghe College of Engineering, Airoli, Navi Mumbai, Maharashtra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Datta Meghe College of Engineering, Airoli, Navi Mumbai, Maharashtra, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Datta Meghe College of Engineering, Airoli, Navi Mumbai, Maharashtra, India"}, {"AuthorId": 4, "Name": "Prof. <PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Datta Meghe College of Engineering, Airoli, Navi Mumbai, Maharashtra, India"}], "References": []}, {"ArticleId": 88316672, "Title": "A survey on : Online Social Networking Attacks Detection Techniques", "Abstract": "<p>Today's due the popularity of internet number of users are increase on every social media platform. In recent research found that 80% of youth depend on social media to make new friends , share photos. Through this they get popularity and large number of user base and become influencers . Most of the social media platform are providing different privacy and security . Still attacker find out the way to breech the security, privacy and confidently of users and companies or organizations using several techniques . This paper highlight the major security issues phasing by many social networking web applications. Also identify the solution based on attacks in different literature . At last, we discuss open research issues</p>", "Keywords": "", "DOI": "10.32628/CSEIT21732", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IEEE Member, Chandigarh University, Punjab, India"}], "References": []}, {"ArticleId": 88316756, "Title": "Emotion Based Smart Music Player", "Abstract": "<p>Every individual human might have completely different faces; however, their expressions tell us the same story and it notably plays a significant role in extraction of an individual’s emotions and behavior. Music is the purest form of art and a medium of expression, which is known to have a greater connection with a person’s emotions. It has a novel ability to lift one’s mood. This project system focuses on building an efficient music player which works on emotion of user using facial recognition techniques. The facial features extracted will generate a system thereby reducing the effort and time involved in doing it manually. Facial data is captured by employing a camera. The emotion module makes use of deep learning techniques to spot the exact mood relative to that expression. The accuracy of mood detection module in the system for real time footage is above 80%; while for static pictures it is 95 to one hundred percent. Therefore, it brings out higher accuracy relating to time and performance.</p>", "Keywords": "", "DOI": "10.32628/CSEIT2172130", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "B. Tech. Scholar, Department of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "B. Tech. Scholar, Department of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "B. Tech. Scholar, Department of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "B. Tech. Scholar, Department of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}], "References": []}, {"ArticleId": 88316758, "Title": "Weather Forecast through Data Mining", "Abstract": "<p>Weather Forecasting is the attempt to predict the weather conditions based on parameters such as temperature, wind, humidity and rainfall. These parameters will be considered for experimental analysis to give the desired results. Data used in this project has been collected from various government institution sites. The algorithm used to predict weather includes Neural Networks(NN), Random Forest, Classification and Regression tree (C &RT), Support Vector Machine, K-nearest neighbor. The correlation analysis of the parameters will help in predicting the future values. This web based application we will have its own chat bot where user can directly communicate about their query related to Weather Forecast and can have experience of two-way communication.</p>", "Keywords": "", "DOI": "10.32628/CSEIT217318", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering , Krishna Engineering College, Ghaziabad, Uttar Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering , Krishna Engineering College, Ghaziabad, Uttar Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering , Krishna Engineering College, Ghaziabad, Uttar Pradesh, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering , Krishna Engineering College, Ghaziabad, Uttar Pradesh, India"}, {"AuthorId": 5, "Name": "Dr. <PERSON><PERSON><PERSON>", "Affiliation": "Professor, Department of Computer Science and Engineering, Krishna Engineering College, Ghaziabad, Uttar Pradesh, India"}], "References": []}, {"ArticleId": 88316761, "Title": "Intelligent Drive Assistance System", "Abstract": "<p>The report proposes the research conducted and the project made in the field of computer engineering to develop a system for driver drowsiness detection to prevent accidents from happening because of driver fatigue and sleepiness. The report proposed the results and solutions on the limited implementation of the various techniques that are introduced in the project. Whereas the implementation of the project give the real world idea of how the system works and what changes can be done in order to improve the utility of the overall system. Furthermore, the paper states the overview of the observations made by the authors in order to help further optimization in the mentioned field to achieve the utility at a better efficiency for a safer road. A person driving needs to be able to focus on driving at all instances. Any prolonged or sudden complications to the person driving the vehicle can cause serious accidents/damages. To ignore the importance of this could result in severe physical injuries, deaths and economic losses. Road incidents remain the leading type of fatal work-related event, carrying tremendous personal, social, and economic costs. While employers with a fixed worksite can observe and interact directly with workers in an effort to promote safety and reduce risk, employers with workers who operate a motor vehicle as part of their job have fewer options. Drowsiness detection system is regarded as an effective tool to reduce the number of road accidents. This project proposes a non-intrusive approach for detecting drowsiness in drivers, using Computer Vision. Developing various technologies for monitoring and preventing drowsiness while driving is a major trend and challenge in the domain of accident avoidance systems. This project proposes a non-intrusive approach for detecting drowsiness in drivers, using Computer Vision. Developing various technologies for monitoring and preventing drowsiness while driving is a major trend and challenge in the domain of accident avoidance systems. Haar face detection algorithm is used to capture frames of image as input and then the detected face as output.</p>", "Keywords": "", "DOI": "10.32628/CSEIT217312", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Vidyalankar Institute of Technology, Mumbai Maharashtra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Vidyalankar Institute of Technology, Mumbai Maharashtra, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Professor, Department of Information Technology, Vidyalankar Institute of Technology, Mumbai Maharashtra, India"}], "References": []}, {"ArticleId": 88316807, "Title": "A Novel Approach for Bone Age Assessment using Deep Learning", "Abstract": "<p>In this paper, we propose a detailed approach to create a Bone age assessment model. Bone age assessment is a common medical practice in the assessment of child development, who are less than 18 years of age. In this proposed model, the Xception architecture is being used for transfer learning. Using feature extraction and transfer learning, the pre-trained convolutional neural network were custom trained. The dataset used for training the model is obtained from the Kaggle RNSA Bone Age dataset containing 12811 male and female bone images of different age groups. Finally, we were able to attain a mean absolute error (MAE) of 8.175 months in male and female patients, which aligns with our initial goal of achieving MAE in under a year.</p>", "Keywords": "", "DOI": "10.32628/CSEIT21731", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON>shan B. Poojary", "Affiliation": "Student, Department of Electronics and Telecommunication Engineering, K.J. Somaiya Institute of Engineering and Information Technology, University of Mumbai, Mumbai, Maharashtra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Student, Department of Electronics and Telecommunication Engineering, K.J. Somaiya Institute of Engineering and Information Technology, University of Mumbai, Mumbai, Maharashtra, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Student, Department of Electronics and Telecommunication Engineering, K.J. Somaiya Institute of Engineering and Information Technology, University of Mumbai, Mumbai, Maharashtra, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Student, Department of Electronics and Telecommunication Engineering, K.J. Somaiya Institute of Engineering and Information Technology, University of Mumbai, Mumbai, Maharashtra, India"}, {"AuthorId": 5, "Name": "Dr. <PERSON><PERSON><PERSON>", "Affiliation": "Professor, Department of Electronics and Telecommunication Engineering, K.J. Somaiya Institute of Engineering and Information Technology, University of Mumbai, Mumbai, Maharashtra, India"}], "References": []}, {"ArticleId": 88316810, "Title": "Uni Connect, Connecting Students", "Abstract": "<p>Interactive Web 2.0 Internet-based Applications are known as social media. According to statistica, almost over 3.6 Billion people are active in Social media around the world. Social networking is a form of internet-based communication that allows users to send and receive content quickly. Nearly 51% of the youth over the world uses social media. One can take this fact to cover their precious time from entertaining them to educating them about the current affairs. Social media can also help university students wish to contact their alumni to grab an opportunity in their dream company. One can also use it to promote their skill set and make other recruiters have a spotlight for them.</p>", "Keywords": "", "DOI": "10.32628/CSEIT2172121", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Lovely Professional University, Phagwara, Punjab, India"}], "References": []}, {"ArticleId": 88316832, "Title": "Community Application", "Abstract": "<p>Community development is basically the task wherein people belonging to a particular community come together and help in strengthening the civil society by categorising the actions of their respective communities, and their perspectives in building an economic, social and environmental approach. The community development workers help communities to bring about the required social change, thus improving the quality of life in their stipulated areas. Nowadays, online applications have become a vital component of any industry. Businesses can now be grown effortlessly and have become easier by using web applications and also in achieving the goals in a minimal span of time. The evolution of any web application involves the identification of product requirements, plotting, coding, and examining using the required technologies and frameworks. The applications are built on top of frameworks so that the elementary requirements in the development activity of the web applications are already initiated and can be fulfilled accordingly.</p>", "Keywords": "", "DOI": "10.32628/CSEIT21737", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "Prof. <PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> (Computer Network Engineering), Ph.D. (Pursuing), Department of Computer Engineering, SPPU/MMCOE, Pune, Maharashtra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, SPPU/MMCOE, Pune, Maharashtra, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, SPPU/MMCOE, Pune, Maharashtra, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, SPPU/MMCOE, Pune, Maharashtra, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, SPPU/MMCOE, Pune, Maharashtra, India"}], "References": []}, {"ArticleId": 88316834, "Title": "Review and Further Prospects of Plant Disease Detection Using Machine Learning", "Abstract": "<p>Identifying of the plant diseases is essential in prevention of yield and volume losses in agriculture Product. Studies of plant diseases mean studies of visually observable patterns on the plant. Health surveillance and detecting diseases in plants is essential for sustainable development agriculture. It is very difficult to monitor plant diseases manually. It requires a lot of experiences in work, expertise in these field plant diseases and also requires excessive processing time. Therefore; image processing is used to detect plant diseases. Disease detection includes steps such as acquisition, image Pre-processing, image segmentation, feature extraction and Classification. We describe these methods for the detection of plant diseases on the basis of their leaf images; automatic detection of plant disease is done by the image processing and machine learning. The different leaf images of plant disease are collected and feature extracted of the various machine learning methods.</p>", "Keywords": "", "DOI": "10.32628/CSEIT217324", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Lovely Professional University, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Lovely Professional University, Punjab, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Lovely Professional University, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Lovely Professional University, Punjab, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Lovely Professional University, Punjab, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Lovely Professional University, Punjab, India"}], "References": []}, {"ArticleId": 88316885, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0020-0190(21)00057-0", "PubYear": 2021, "Volume": "170", "Issue": "", "JournalId": 5748, "JournalTitle": "Information Processing Letters", "ISSN": "0020-0190", "EISSN": "1872-6119", "Authors": [], "References": []}, {"ArticleId": 88316973, "Title": "Offset Detection of Grate Trolley’s Side Plate Based on YOLOv4", "Abstract": "<p>Side plate offset is one of the grate system faults. If it is not dealt with in time, some accidents will occur and economic losses will be made. Aiming at the problems like time-consuming, labour-wasting, and low intelligent by the side plate offset detection method manually, an autoside plate offset detection method is proposed, based on You Only Look Once version 4 (YOLOv4). Two cameras were fixed to collect the image information of the grate trolley’s side plate. With reference to the grate trolley’s operation, the offset judgment rules were set. YOLOv4 object detection algorithm was used to detect the side plate and trolley’s chassis frame in video frame images. A baseline was set according to the position information of the trolley’s chassis frame output by detection, and then, the position intervals between side plates and the baseline could be determined by calculation. According to the judgment rules, the scheme in this paper could detect the offset fault of the trolley’s side plate timely, and an alarm would be made automatically when faults are detected. Our video images of the trolley’s side plate were collected and sorted in Baogang Group sintering plant for testing. In this experiment, no error judgment was made, and the average detection and judgment time was 0.024 s. In this paper, rather than manually, the real-time automatic detection was realized to detect the offset fault of the trolley’s side plate so as to provide a new solution for offset detection of the grate trolley’s side plate.</p>", "Keywords": "", "DOI": "10.1155/2021/5552206", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Pattern Recognition and Intelligent Image Processing, School of Information Engineering, Inner Mongolia University of Science and Technology, Baotou 014010, China;School of Information Engineering, Inner Mongolia University of Science and Technology, Baotou 014010, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Pattern Recognition and Intelligent Image Processing, School of Information Engineering, Inner Mongolia University of Science and Technology, Baotou 014010, China;School of Information Engineering, Inner Mongolia University of Science and Technology, Baotou 014010, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Inner Mongolia University of Science and Technology, Baotou 014010, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Inner Mongolia University of Science and Technology, Baotou 014010, China"}], "References": [{"Title": "A smart surface inspection system using faster R-CNN in cloud-edge computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101037", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Asymmetric multi-stage CNNs for small-scale pedestrian detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "409", "Issue": "", "Page": "12", "JournalTitle": "Neurocomputing"}, {"Title": "Towards automatic visual inspection: A weakly supervised learning method for industrial applicable object detection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "121", "Issue": "", "Page": "103232", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 88317055, "Title": "A power allocation algorithm based on cooperative Q-learning for multi-agent D2D communication networks", "Abstract": "In the multi-agent device to device (D2D) communication networks, the scene of the multi-agent will change due to its mobility. To address communication interference and energy overconsumption problems caused by the lack of adaptability to changeful scenes, a power allocation algorithm based on scenes adaptive cooperative Q-learning (SACL) is proposed in the paper. Specifically, the scene variable is added into the state space, and the reward function in the algorithm is improved to achieve a larger system capacity with less power. Then, in order to improve the convergence speed of SACL algorithm, the balance factor is introduced based on the location distribution of multiple agents, and a fast scene adaptive reinforcement learning (FSACL) algorithm is proposed. Simulation experiments verify the adaptability of SACL and FSACL algorithm when the scene is changed. Compared with traditional cooperative Q-learning algorithm (CL) and independent Q-learning (IL) algorithms, the SACL and FSACL algorithm can obtain larger system capacity with smaller power to some extent. In addition, the FSACL algorithm converges faster than the CL, IL and the SACL algorithm.", "Keywords": "Power allocation ; Cooperative Q-learning ; Multiple agents ; D2D communication", "DOI": "10.1016/j.phycom.2021.101370", "PubYear": 2021, "Volume": "47", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, 150001, China"}, {"AuthorId": 2, "Name": "Guangzhen Si", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, 150001, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, 150001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Communication Engineering, Harbin Engineering University, Harbin, 150001, China"}], "References": []}, {"ArticleId": 88317326, "Title": "The Online Social Network and User Innovation in the Context of an Online Innovation Platform", "Abstract": "<p>The wide use of social media technology boosts many online innovation platforms, providing effective communication channels for innovation spreading among online users. From the social network perspective, this paper investigates the impact of online interactive relations on user innovation by holistically examining online relations from relational and structural embeddedness, qualified by both the ego-centered and the entire network, respectively. User interaction data from LEGO Ideas are used to empirically test the effects of relational and structural characteristics of online social networks on users’ idea contributions. The results for relational characteristics reveal that the number of online ties has an inverted U-shaped relationship with user innovation, the strength of online ties positively affects user innovation, and neighbor characteristics cannot affect user innovation. For structural characteristics, both centrality and bridge location positively affect user innovation. The findings provide reasonable suggestions for both online users and innovation platforms.</p>", "Keywords": "", "DOI": "10.4018/JOEUC.20211101.oa7", "PubYear": 2021, "Volume": "33", "Issue": "6", "JournalId": 11438, "JournalTitle": "Journal of Organizational and End User Computing", "ISSN": "1546-2234", "EISSN": "1546-5012", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Engineering, Shandong Jianzhu University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Shandong University of Finance and Economics, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Shandong University, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Shandong University, Jinan, China"}], "References": []}, {"ArticleId": 88317349, "Title": "Targeting subjective engagement in experimental therapeutics for digital mental health interventions", "Abstract": "Engagement is a multifaceted construct and a likely mechanism by which digital interventions achieve clinical improvements. To date, clinical research on digital mental health interventions (DMHIs) has overwhelmingly defined engagement and assessed its association with clinical outcomes through the objective/behavioral metrics of use of or interactions with a DMHI, such as number of log-ins or time spent using the technology. However, engagement also entails users' subjective experience. Research is largely lacking that tests the relationship between subjective metrics of engagement and clinical outcomes. The purpose of this study is to present a proof-of-concept exploratory evaluation of the association between subjective engagement measures of a mobile DMHI with changes in depression and anxiety. Adult primary care patients ( N = 146) who screened positive for depression or anxiety were randomized to receive a DMHI, IntelliCare, immediately or following an 8-week waitlist. Subjective engagement was measured via the Usefulness, Satisfaction, and Ease of Use (USE) Questionnaire. Across both conditions, results showed that individuals who perceived a mobile intervention as more useful, easy to use and learn, and satisfying had greater improvements in depression and anxiety over eight weeks. Findings support our proposed experimental therapeutics framework that hypothesizes objective/behavioral and subjective engagement metrics as mechanisms that lead to changes in clinical outcomes, as well as support directing intervention design efforts for DMHIs to target the user experience.", "Keywords": "Experimental therapeutics ; Digital mental health ; Engagement ; Subjective engagement ; Depression ; Anxiety", "DOI": "10.1016/j.invent.2021.100403", "PubYear": 2021, "Volume": "25", "Issue": "", "JournalId": 11817, "JournalTitle": "Internet Interventions", "ISSN": "2214-7829", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Behavioral Intervention Technologies, Northwestern University Feinberg School of Medicine, Chicago, IL, USA;Department of Medical Social Sciences, Northwestern University Feinberg School of Medicine, Chicago, IL, USA;Corresponding author at: Center for Behavioral Intervention Technologies, Northwestern University Feinberg School of Medicine, 750 N Lake Shore Dr, 10th Floor, Chicago, IL, 60611, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Behavioral Intervention Technologies, Northwestern University Feinberg School of Medicine, Chicago, IL, USA;Department of Preventive Medicine, Northwestern University Feinberg School of Medicine, Chicago, IL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Behavioral Intervention Technologies, Northwestern University Feinberg School of Medicine, Chicago, IL, USA;Department of Medical Social Sciences, Northwestern University Feinberg School of Medicine, Chicago, IL, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Psychiatry, University of Arkansas for Medical Sciences, Little Rock, AR, USA;Translational Research Institute, University of Arkansas for Medical Sciences, Little Rock, AR, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departments of Psychiatry and Internal Medicine, Rush University Medical Center, Chicago, IL, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Communication Studies, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Center for Behavioral Intervention Technologies, Northwestern University Feinberg School of Medicine, Chicago, IL, USA;Department of Preventive Medicine, Northwestern University Feinberg School of Medicine, Chicago, IL, USA"}], "References": []}, {"ArticleId": 88317392, "Title": "Estimation of important binding sites in compounds that interact with proteins", "Abstract": "<p>Proteins are one of the important substances in understanding biological activity, and many of them express the function by binding to other proteins or small molecules (ligands) on the molecular surface. This interaction often occurs in the hollows (pockets) on the molecular surface of the protein. It is known that when pockets are similar in structure and physical properties, they are likely to express similar functions and to bind similar ligands. Therefore, exploring the similarity of the structure and physical properties in pockets is very useful because it leads to the discovery of new ligands that are likely to bind. In addition, exploring the important structure when binding to the protein significant spot in the ligand will provide useful knowledge for the development of new ligands. In this study, we propose a method to search for proteins containing pockets that are structurally and physically similar to significant spot in the pocket of the analyzed protein, and to extract significant spots in the ligands that bind to them. We use feature points as data. Feature points are the 3-dimensional points that are extracted from 3D structure data of proteins with feature values quantifying hydrophobicity and electrostatic potential. The corresponding feature points are extracted by comparing structurally and physically the pockets of the search target proteins with the significant spot of the analyzed protein. By evaluating the similarity based on the comparison results of the feature values given to the extracted feature points, we search for proteins that are similar to the analyzed protein. From the ligands that bind to the searched proteins, atoms that are near the protein pocket and similar to the atoms in ligand binding to the analyzed protein are extracted. The site constituted by the extracted atoms is defined as a significant spot in the ligand. As a result of classifying ligands binding to the protein by using the extracted significant spot in the ligand, the effectiveness of the proposed method was confirmed.</p><p>Copyright © 2021 Elsevier Ltd. All rights reserved.</p>", "Keywords": "Important binding site;Ligand;Protein", "DOI": "10.1016/j.compbiolchem.2021.107511", "PubYear": 2021, "Volume": "93", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Intelligence Engineering, Kobe University, Japan."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Intelligence Engineering, Kobe University, Japan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Center for Data Science Education and Research, Shiga University, Japan."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Intelligence Engineering, Kobe University, Japan. Electronic address:  ."}], "References": []}, {"ArticleId": 88317403, "Title": "A variable neighborhood search approach for the resource-constrained multi-project collaborative scheduling problem", "Abstract": "Multi-mode and multi-skill project scheduling problems widely exist in the real-world industry. Many efforts have been devoted to the multi-mode project scheduling problem and the multi-skill project scheduling problem. However, the integrated multi-mode and multi-skill project scheduling problem was seldom considered. This paper studies the resource-constrained multi-project scheduling problem in the development of high-end equipment and develops an integrated multi-mode and multi-skill scheduling model with different employee abilities. Based on the characteristics of high-end equipment development, we propose several optimal properties and design an effective heuristic algorithm. Since the resource-constrained project scheduling problem is NP-hard, three neighborhoods for the problem are constructed and a Variable Neighborhood Search Algorithm (VNS) is developed to solve the problem in a reasonable time. Finally, computational experiments are carried out to validate the performance of the proposed algorithm.", "Keywords": "Multi-mode ; Multi-project ; Heuristic algorithm ; VNS", "DOI": "10.1016/j.asoc.2021.107480", "PubYear": 2021, "Volume": "107", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Longqing Cui", "Affiliation": "School of Management, Hefei University of Technology, Hefei 230009, PR China;Key Laboratory of Process Optimization and Intelligent Decision-making of the Ministry of Education, Hefei 230009, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei 230009, PR China;Key Laboratory of Process Optimization and Intelligent Decision-making of the Ministry of Education, Hefei 230009, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei 230009, PR China;Key Laboratory of Process Optimization and Intelligent Decision-making of the Ministry of Education, Hefei 230009, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Hefei University of Technology, Hefei 230009, PR China"}], "References": [{"Title": "An interval-stochastic programming based approach for a fully uncertain multi-objective and multi-mode resource investment project scheduling problem with an application to ERP project implementation", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113189", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hybrid evolutionary algorithm for large-scale project scheduling problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "106567", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 88317507, "Title": "Development of a software package to improve the efficiency of corporate control over compliance with industrial, environmental and energy security requirements at the Gazprom Group facilities", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2021-5(574)-6-11", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom Gaznadzor"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom Gaznadzor"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom Gaznadzor"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom Gaznadzor"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom Gaznadzor"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Oil and Gas “Gubkin University”"}], "References": []}, {"ArticleId": 88317509, "Title": "Territorial design of oil and gas producing regions", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2021-5(574)-17-24", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Federal Research Centre “Computer Science and Control” of the RAS"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Russian Transport University"}], "References": []}, {"ArticleId": 88317510, "Title": "Probabilistic alarms setting for indirectly measured technological parameters", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2021-5(574)-25-29", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "V.A. Trapeznikov Institute of Control Sciences of RAS"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center of Industrial Analytics"}], "References": []}]