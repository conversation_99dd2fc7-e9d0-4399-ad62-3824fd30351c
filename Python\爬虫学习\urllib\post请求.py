# post请求

import urllib.request
# 请求路径
url = 'https://fanyi.baidu.com/sug'
# 请求头
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36',
}
# 请求参数
data = {
    'kw': 'girl'
}

# post请求的参数 必须要进行编码
data = urllib.parse.urlencode(data).encode('utf-8')

# post请求的参数 不会拼接在url的后面 而是必须放在请求对象的定制的参数中 即data
request = urllib.request.Request(url=url, headers=headers, data=data)

# 发送请求
response = urllib.request.urlopen(request)

# 获取响应内容
content = response.read().decode('utf-8')

# 字符串--> json
import json
obj = json.loads(content)

# 打印响应内容
print(obj)

# post请求的参数必须编码
# 参数放在请求对象的编码中