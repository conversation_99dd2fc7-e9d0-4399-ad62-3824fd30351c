[{"ArticleId": 142804589, "Title": "Enhancing remote sensing image analysis: optimization of a hybrid deep network through HHO algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-20499-y", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A hybrid Harris Hawks optimization algorithm with simulated annealing for feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "593", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Adaptive multi-level feature fusion and attention-based network for arbitrary-oriented object detection in remote sensing imagery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "451", "Issue": "", "Page": "67", "JournalTitle": "Neurocomputing"}, {"Title": "Remote sensing image super-resolution and object detection: Benchmark and state of the art", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "116793", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An enhanced soft-computing based strategy for efficient feature selection for timely breast cancer prediction: Wisconsin Diagnostic Breast Cancer dataset case", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "31", "Page": "76607", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Nature-Inspired Algorithms-Based Optimal Features Selection Strategy for COVID-19 Detection Using Medical Images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "4", "Page": "761", "JournalTitle": "New Generation Computing"}]}, {"ArticleId": 142804669, "Title": "Let Me Hold Your Hand: Effects of Anthropomorphism and Touch Behavior on Self-Disclosure Intention, Attachment, and Cerebral Activity Towards AI Mental Health Counselors", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2446502", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Northeastern University, Shenyang, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business Administration, Northeastern University, Shenyang, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Business Administration, Northeastern University, Shenyang, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Northeastern University, Shenyang, People’s Republic of China"}], "References": [{"Title": "Using computer automated systems to conduct personal interviews: Does the mere presence of a human face inhibit disclosure?", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "106197", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Robot Reciprocation of Hugs Increases Both Interacting Times and Self-disclosures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "353", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Understanding robots", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "46", "Page": "eabe2987", "JournalTitle": "Science Robotics"}, {"Title": "Exploring the Privacy Concerns in Using Intelligent Virtual Assistants under Perspectives of Information Sensitivity and Anthropomorphism", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "512", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "What makes an AI device human-like? The role of interaction quality, empathy and perceived psychological anthropomorphic characteristics in the acceptance of artificial intelligence in the service industry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "122", "Issue": "", "Page": "106855", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Artificial intelligence service recovery: The role of empathic response in hospitality customers’ continuous usage intention", "Authors": "Xingyang Lv; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "106993", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Medical practitioner's adoption of intelligent clinical diagnostic decision support systems: A mixed-methods study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "7", "Page": "103524", "JournalTitle": "Information & Management"}, {"Title": "A meta-analysis on the effectiveness of anthropomorphism in human-robot interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "58", "Page": "eabj5425", "JournalTitle": "Science Robotics"}, {"Title": "Attitudes and perspectives towards the preferences for artificial intelligence in psychotherapy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "133", "Issue": "", "Page": "107273", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Tools or peers? Impacts of anthropomorphism level and social role on emotional attachment and disclosure tendency towards intelligent agents", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "107415", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Influence of Rapport and Social Presence with an AI Psychotherapy Chatbot on Users’ Self-Disclosure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "7", "Page": "1620", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Effects of robot gaze and voice human-likeness on users’ subjective perception, visual attention, and cerebral activity in voice conversations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "107645", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Counseling Chatbot Design: The Effect of Anthropomorphic Chatbot Characteristics on User Self-Disclosure and Companionship", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "11", "Page": "2781", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Exploring how politeness impacts the user experience of chatbots for mental health support", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "184", "Issue": "", "Page": "103181", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "User Preferences on AI Psychotherapy Based on Moderating Effects of Individual Personality Traits: Employing a Clustering Analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "41", "Issue": "2", "Page": "1010", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Understanding User Preferences in Developing a Mental Healthcare AI Chatbot: A Conjoint Analysis Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Different dimensions of anthropomorphic design cues: How visual appearance and conversational style influence users’ information disclosure tendency towards chatbots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "190", "Issue": "", "Page": "103320", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 142804698, "Title": "BiaCanDet: Bioelectrical impedance analysis for breast cancer detection with space-time attention neural network", "Abstract": "Breast cancer is a life-threatening disease and early detection plays a crucial role in improving survival rates. However, current detection technologies face challenges such as invasiveness, radiation exposure, high costs, and limited accessibility. To address these issues, a novel breast cancer detection system called BiaCanDet based on bioelectrical impedance analysis (BIA) signal and space–time attention network is proposed for the first time. The BiaCanDet system utilizes non-invasive BIA to measure signal changes in electrical impedance and phase angle (PHA) based on the normal and tumour breast tissue. We collect the first BIABC dataset using BIA devices, which includes 2,444 samples from normal, benign, and malignant breast tissues. Professional physicians collect and annotate the samples through repeated tissue sampling with needle electrodes, combined with pathological analysis to ensure accuracy. This paper proposes a novel space–time attention network (STABFNet) for the complex bioelectrical impedance signal to obtain robust cancer signal feature, which designs the impedance phase enhancement attention (IPEA) model to improve recognition performance. The experimental results on the BIABC dataset confirm that the STABFNet network achieve the 93.37%, 90.64%, 89.37%, and 89.90% in terms of accuracy, precision, recall, and f1-score, respectively. Additionally, the system also demonstrates real-time detection capabilities in the lightweight mobile terminal. The proposed system and methodology introduce a pioneering approach for breast cancer detection that is low-cost and convenient, making it a promising solution for widespread adoption that has the potential to revolutionize early breast cancer detection worldwide.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126223", "PubYear": 2025, "Volume": "269", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Artificial Intelligence, Wuhan Textile University, Wuhan, 430200, China;School of Electrical and Electronic Engineering, Nanyang Technological University, 50 Nanyang Ave, 639798, Singapore;Engineering Research Center of Hubei Province for Clothing Information, Wuhan Textile University, Wuhan, 430200, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Artificial Intelligence, Wuhan Textile University, Wuhan, 430200, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Artificial Intelligence, Wuhan Textile University, Wuhan, 430200, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Electrical Engineering, Wuhan Textile University, Wuhan, 430200, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Electronics and Electrical Engineering, Wuhan Textile University, Wuhan, 430200, China"}, {"AuthorId": 6, "Name": "Minghua Jiang", "Affiliation": "School of Computer Science and Artificial Intelligence, Wuhan Textile University, Wuhan, 430200, China;Engineering Research Center of Hubei Province for Clothing Information, Wuhan Textile University, Wuhan, 430200, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Thyroid & Breast Surgery, Zhongnan Hospital, Wuhan University, Wuhan, 430200, China;Corresponding authors"}], "References": [{"Title": "Digital mammogram classification using 2D-BDWT and GLCM features with FOA-based feature selection approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "11", "Page": "7029", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep learning-based breast cancer classification through medical imaging modalities: state of the art and research challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "1655", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Neural networks model based on an automated multi-scale method for mammogram classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "208", "Issue": "", "Page": "106465", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep transfer with minority data augmentation for imbalanced breast cancer dataset", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106759", "JournalTitle": "Applied Soft Computing"}, {"Title": "MetaMed: Few-shot medical image classification using gradient-based meta-learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108111", "JournalTitle": "Pattern Recognition"}, {"Title": "Segmentation information with attention integration for classification of breast tumor in ultrasound image", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Li", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108427", "JournalTitle": "Pattern Recognition"}, {"Title": "BTS-ST: Swin transformer network for segmentation and classification of multimodality breast cancer images", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "267", "Issue": "", "Page": "110393", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "GTSNet: Flexible architecture under budget constraint for real-time human activity recognition from wearable sensor", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "", "Page": "106543", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Classification of tumor in one single ultrasound image via a novel multi-view learning strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "109776", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-view stereoscopic attention network for 3D tumor classification in automated breast ultrasound", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "120969", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Summarization of scholarly articles using BERT and BiGRU: Deep learning-based extractive approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "9", "Page": "101739", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 142804797, "Title": "Enhanced Clustering Techniques for Marine Ad-Hoc Networks Using Dynamic PFCM", "Abstract": "", "Keywords": "", "DOI": "10.54216/FPA.180118", "PubYear": 2025, "Volume": "18", "Issue": "1", "JournalId": 91518, "JournalTitle": "Fusion: Practice and Applications", "ISSN": "2770-0070", "EISSN": "2692-4048", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Networks, College of Information Technology, University of Babylon, Babil, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Sciences Department, University of Technology, Baghdad, Iraq"}], "References": []}, {"ArticleId": 142804871, "Title": "Integrated GCN–BiGRU–TPE Agricultural Product Futures Prices Prediction Based on Multi-graph Construction", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10614-024-10832-w", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A long-term prediction approach based on long short-term memory neural networks with automatic parameter optimization by Tree-structured Parzen Estimator and applied to time-series data of NPP steam generators", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106116", "JournalTitle": "Applied Soft Computing"}, {"Title": "Futures price prediction of agricultural products based on machine learning", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "837", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A graph neural network-based stock forecasting method utilizing multi-source heterogeneous data fusion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "30", "Page": "43753", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "GCN-based stock relations analysis for stock market prediction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e1057", "JournalTitle": "PeerJ Computer Science"}, {"Title": "GCNET: Graph-based prediction of stock price movement using graph convolutional network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105452", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Optimal forecast combination based on PSO-CS approach for daily agricultural future prices forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109833", "JournalTitle": "Applied Soft Computing"}, {"Title": "Integrated GCN-LSTM stock prices movement prediction based on knowledge-incorporated graphs construction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "1", "Page": "161", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 142804874, "Title": "The Design of Visual, Cognitive, and Physical Modalities in VR Games for Older Adults: A Systematic Review", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2439573", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Human Sciences, Osaka University, Osaka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Human Sciences, Osaka University, Osaka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Human-Engaged Computing, Kochi University of Technology, Kochi, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Human Sciences, Osaka University, Osaka, Japan"}], "References": []}, {"ArticleId": 142804875, "Title": "Modelling evapotranspiration in urban green stormwater infrastructures: importance of sensitivity analysis and calibration strategies with a hydrological model", "Abstract": "Evapotranspiration (ET) is crucial for urban runoff management, the cooling efficiency of green stormwater infrastructure (GSI), and vegetation resilience. This research investigates the ability of a commonly used hydrological ET scheme, implemented in HYDRUS-1D, to accurately replicate ET fluxes within GSI, including green roofs (GRs) and rain gardens (RGs), in the Paris region, France. Application of the Sobol sensitivity analysis method indicates that, vegetation height and stomatal resistance are key elements in Penman-Monteith potential ET calculations, while substrate water retention parameters are essential for actual ET simulations. Soil cover fraction, substrate pressure head during the anaerobic phase, and interception parameter also influence ET. Calibration using extensive datasets (water content, ET, drainage) demonstrates improved model accuracy for GRs with thicker substrates compared to those with thinner substrates and for RG setups. Drainage calibration ensures long-term ET simulation accuracy, while calibration with water content or ET observations is recommended during prolonged dry periods.", "Keywords": "", "DOI": "10.1016/j.envsoft.2025.106319", "PubYear": 2025, "Volume": "185", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Cerema, Equipe TEAM, 12 rue Teisserenc de Bort, F 78190, Trappes, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cerema, Equipe TEAM, 12 rue Teisserenc de Bort, F 78190, Trappes, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cerema, Equipe TEAM, 12 rue Teisserenc de Bort, F 78190, Trappes, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, Ecole des Ponts, Université Paris Est Creteil, F 77455, Marne-la-Vallee, France"}], "References": [{"Title": "The Future of Sensitivity Analysis: An essential discipline for systems modeling and policy support", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "137", "Issue": "", "Page": "104954", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Modeling bioretention stormwater systems: Current models and future research needs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "105146", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Approaches for green roof dynamic model analysis using GSA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "7", "Page": "613", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 142804914, "Title": "A consensus optimization mechanism with Q-learning-based distributed PSO for large-scale group decision-making", "Abstract": "Current industrial product design evaluation faces multiple challenges including shortened research and development (R&D) cycles, increased technical complexity and expanding expert teams, which exacerbate problems of incomplete information, uncertainty, and expert preference conflicts. Existing evaluation methods are difficult to capture linguistic ambiguity effectively and exhibit low consensus efficiency and optimization performance in large-scale group decision making (LSGDM). To address these challenges, this paper proposes a Q-learning-based distributed particle swarm optimization (QLDPSO) consensus mechanism for industrial product design evaluation. The proposed approach utilizes probabilistic linguistic term sets (PLTSs) to express expert preferences and capture evaluation uncertainties. An automated consensus optimization model is developed to eliminate preference conflicts, improve consensus efficiency and minimize time and effort spent on repeated negotiations by identifying optimization objectives and adjustment ranges. To overcome slow convergence and local optima issues in high-dimensional optimization, the method integrates Q-learning with distributed PSO, dividing the population into collaboratively evolving subpopulations and dynamically adjusting subpopulation sizes through reinforcement learning to balance exploration and exploitation. Finally, the proposed algorithm was validated through an aeroengine design case study and compared with existing algorithms. The experimental results demonstrate that the QLDPSO consensus optimization mechanism significantly improves both the consensus optimization efficiency and evaluation accuracy in LSGDM scenarios, offering an innovative and practical solution for design alternative selection of complex industrial products.", "Keywords": "", "DOI": "10.1016/j.swevo.2024.101841", "PubYear": 2025, "Volume": "93", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "Qingyang Jia", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha, Hunan 410073, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha, Hunan 410073, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha, Hunan 410073, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha, Hunan 410073, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha, Hunan 410073, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Collaborative Intelligence Systems, Ministry of Education, Xidian University, Xi'an 710071, China"}], "References": [{"Title": "Reaching a minimum adjustment consensus in social network group decision-making", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "30", "JournalTitle": "Information Fusion"}, {"Title": "Novel chaotic grouping particle swarm optimization with a dynamic regrouping strategy for solving numerical optimization tasks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105568", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Micro-Genetic algorithm with fuzzy selection of operators for multi-Objective optimization: μFAME", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "100818", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Consensus model based on probability K-means clustering algorithm for large scale group decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "1609", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A study on a Q-Learning algorithm application to a manufacturing assembly problem", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "426", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "An integrated method for multiattribute group decision making with probabilistic linguistic term sets", "Authors": "G<PERSON><PERSON>; <PERSON><PERSON>; Xue‐Biao Li", "PubYear": 2021, "Volume": "36", "Issue": "11", "Page": "6871", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A non-threshold consensus model based on the minimum cost and maximum consensus-increasing for multi-attribute large group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "90", "JournalTitle": "Information Fusion"}, {"Title": "Enhanced multi-swarm cooperative particle swarm optimizer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "69", "Issue": "", "Page": "100989", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Linguistic Z-numbers and cloud model weighted ranking technology and its application in concept evaluation of information axiom", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "5", "Page": "6061", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Reinforcement Learning-based control using Q-learning and gravitational search algorithm with experimental validation on a nonlinear servo system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "583", "Issue": "", "Page": "99", "JournalTitle": "Information Sciences"}, {"Title": "Particle Swarm Optimization Algorithm and Its Applications: A Systematic Review", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "5", "Page": "2531", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "A reinforcement learning level-based particle swarm optimization algorithm for large-scale optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "602", "Issue": "", "Page": "298", "JournalTitle": "Information Sciences"}, {"Title": "A dynamic large-scale multiple attribute group decision-making method with probabilistic linguistic term sets based on trust relationship and opinion correlation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "612", "Issue": "", "Page": "257", "JournalTitle": "Information Sciences"}, {"Title": "A probabilistic linguistic opinion dynamics method based on the DeGroot model for emergency decision-making in response to COVID-19", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "173", "Issue": "", "Page": "108677", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Reaching consensus in group decision making with non-reciprocal pairwise comparison matrices", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "10", "Page": "12888", "JournalTitle": "Applied Intelligence"}, {"Title": "Building a consensus for the best-worst method in group decision-making with an optimal allocation of information granularity", "Authors": "Jindong Qin; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "630", "JournalTitle": "Information Sciences"}, {"Title": "RL-GA: A Reinforcement Learning-based Genetic Algorithm for Electromagnetic Detection Satellite Scheduling Problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "77", "Issue": "", "Page": "101236", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Application of MADM methods in Industry 4.0: A literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "177", "Issue": "", "Page": "109075", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Reinforcement learning-based particle swarm optimization with neighborhood differential mutation strategy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "78", "Issue": "", "Page": "101274", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A global optimization feedback model with PSO for large scale group decision making in hesitant fuzzy linguistic environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "228", "Issue": "", "Page": "120320", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Research on path planning of autonomous vehicle based on RRT algorithm of Q-learning and obstacle distribution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "5", "Page": "1266", "JournalTitle": "Engineering Computations"}, {"Title": "PBRL-TChain: A performance-enhanced permissioned blockchain for time-critical applications based on reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "154", "Issue": "", "Page": "301", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Reinforcement learning-assisted evolutionary algorithm: A survey and research opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "86", "Issue": "", "Page": "101517", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A dynamic multi-objective evolutionary algorithm based on <PERSON><PERSON><PERSON><PERSON> distance and intra-cluster individual correlation rectification", "Authors": "<PERSON><PERSON> G<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "678", "Issue": "", "Page": "120922", "JournalTitle": "Information Sciences"}, {"Title": "A PSO-algorithm-based dual consensus method for large-scale group decision making and its application in medical imaging equipment purchasing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "162", "Issue": "", "Page": "111862", "JournalTitle": "Applied Soft Computing"}, {"Title": "An adaptive Q-learning based particle swarm optimization for multi-UAV path planning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "13-14", "Page": "7931", "JournalTitle": "Soft Computing"}, {"Title": "Consensus reaching process in large-scale group decision making based on opinion leaders", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "509", "JournalTitle": "Procedia Computer Science"}, {"Title": "An adaptive interval many-objective evolutionary algorithm with information entropy dominance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "91", "Issue": "", "Page": "101749", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 142804916, "Title": "Optimizing portfolio selection through stock ranking and matching: A reinforcement learning approach", "Abstract": "Predicting asset movements with machine learning (ML) algorithms remains a complex challenge, particularly in selecting optimal models or designing effective ensemble strategies. This study presents a novel methodology that synergizes reinforcement learning (RL) with advanced ML algorithms—LSTM, XGBoost, and Deep RankNet—to improve prediction accuracy and portfolio construction. The approach incorporates hyperparameter optimization, innovative feature engineering, and a comprehensive comparison of algorithmic performance. RL serves a dual role as both an ensemble strategy and a dynamic learning layer, enabling a 15% increase in cumulative returns compared to traditional ensemble techniques. This advancement highlights RL’s capacity to refine predictions and enhance risk assessment by adaptively integrating outputs from diverse algorithms. Beyond demonstrating superior performance, the study provides actionable insights for practitioners seeking to construct effective, risk-sensitive trading portfolios.", "Keywords": "", "DOI": "10.1016/j.eswa.2025.126430", "PubYear": 2025, "Volume": "269", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Supply Chain and Business Technology Management, MB 12-107, <PERSON> of Business, Concordia University, 1450 Guy, Montreal, Quebec H3G-1M8, Canada;https://orcid.org/0000-0002-6567-0481"}], "References": [{"Title": "Optimizing LSTM for time series prediction in Indian stock market", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2091", "JournalTitle": "Procedia Computer Science"}, {"Title": "Stock Closing Price Prediction using Machine Learning Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "599", "JournalTitle": "Procedia Computer Science"}, {"Title": "Portfolio optimization with return prediction using deep learning and machine learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113973", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Mean–variance portfolio optimization using machine learning-based stock price prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106943", "JournalTitle": "Applied Soft Computing"}, {"Title": "Portfolio management system in equity market neutral using reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "8119", "JournalTitle": "Applied Intelligence"}, {"Title": "Exponential Gradient with Momentum for Online Portfolio Selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115889", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An unsupervised learning framework for marketneutral portfolio", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116308", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An online portfolio strategy based on trend promote price tracing ensemble learning algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Hong-Ming <PERSON>", "PubYear": 2022, "Volume": "239", "Issue": "", "Page": "107957", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "How to make machine select stocks like fund managers? Use scoring and screening model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "116629", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Stock Ranking with Multi-Task Learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "116886", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Machine learning models predicting returns: Why most popular performance metrics are misleading and proposal for an efficient metric", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "116970", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Mean–variance portfolio optimization with deep learning based-forecasts for cointegrated stocks", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "117005", "JournalTitle": "Expert Systems with Applications"}, {"Title": "BiCuDNNLSTM-1dCNN — A hybrid deep learning-based predictive model for stock price prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117123", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multiagent-based deep reinforcement learning for risk-shifting portfolio management", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108894", "JournalTitle": "Applied Soft Computing"}, {"Title": "GPM: A graph convolutional network based reinforcement learning framework for portfolio management", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "498", "Issue": "", "Page": "14", "JournalTitle": "Neurocomputing"}, {"Title": "Deep learning in stock portfolio selection and predictions", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121404", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Ensemble reinforcement learning: A survey", "Authors": "<PERSON><PERSON><PERSON>; Ponnuthurai Nagaratnam Suganthan; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "149", "Issue": "", "Page": "110975", "JournalTitle": "Applied Soft Computing"}, {"Title": "Soft imitation reinforcement learning with value decomposition for portfolio management", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "151", "Issue": "", "Page": "111108", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 142805422, "Title": "Cholec80-Boxes: Bounding Box Labelling Data for Surgical Tools in Cholecystectomy Images", "Abstract": "<p>Surgical data analysis is crucial for developing and integrating context-aware systems (CAS) in advanced operating rooms. Automatic detection of surgical tools is an essential component in CAS, as it enables the recognition of surgical activities and understanding the contextual status of the procedure. Acquiring surgical data is challenging due to ethical constraints and the complexity of establishing data recording infrastructures. For machine learning tasks, there is also the large burden of data labelling. Although a relatively large dataset, namely the Cholec80, is publicly available, it is limited to the binary label data corresponding to the surgical tool presence. In this work, 15,691 frames from five videos from the dataset have been labelled with bounding boxes for surgical tool localisation. These newly labelled data support future research in developing and evaluating object detection models, particularly in the laparoscopic image data analysis domain.</p>", "Keywords": "", "DOI": "10.3390/data10010007", "PubYear": 2025, "Volume": "10", "Issue": "1", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Technical Medicine (ITeM), Furtwangen University, 78054 Villingen-Schwenningen, Germany; Innovation Center Computer Assisted Surgery (ICCAS), University of Leipzig, 04103 Leipzig, Germany"}, {"AuthorId": 2, "Name": "Nour Aldeen Jalal", "Affiliation": "Innovation Center Computer Assisted Surgery (ICCAS), University of Leipzig, 04103 Leipzig, Germany"}, {"AuthorId": 3, "Name": "Herag Arabian", "Affiliation": "Institute of Technical Medicine (ITeM), Furtwangen University, 78054 Villingen-Schwenningen, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Technical Medicine (ITeM), Furtwangen University, 78054 Villingen-Schwenningen, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Technical Medicine (ITeM), Furtwangen University, 78054 Villingen-Schwenningen, Germany; Department of Mechanical Engineering, University of Canterbury, Christchurch 8041, New Zealand"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering, German Jordanian University, Amman 11180, Jordan"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Innovation Center Computer Assisted Surgery (ICCAS), University of Leipzig, 04103 Leipzig, Germany"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Technical Medicine (ITeM), Furtwangen University, 78054 Villingen-Schwenningen, Germany"}], "References": [{"Title": "Surgical Tool Classification & Localisation Using Attention and Multi-feature Fusion Deep Learning Approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "2", "Page": "5626", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 142805617, "Title": "DeepSCNN: a simplicial convolutional neural network for deep learning", "Abstract": "<p>Graph convolutional neural networks (GCNs) are deep learning methods for processing graph-structured data. Usually, GCNs mainly consider pairwise connections and ignore higher-order interactions between nodes. Recently, simplices have been shown to encode not only pairwise relations between nodes but also encode higher-order interactions between nodes. Researchers have been concerned with how to design simplicial-based convolutional neural networks. The existing simplicial neural networks can achieve good performance in tasks such as missing value imputation, graph classification, and node classification. However, due to issues of gradient vanishing, over-smoothing, and over-fitting, they are typically limited to very shallow models. Therefore, we innovatively propose a simplicial convolutional neural network for deep learning (DeepSCNN). Firstly, simplicial edge sampling technology (SES) is introduced to prevent over-fitting caused by deepening network layers. Subsequently, initial residual connection technology is added to simplicial convolutional layers. Finally, to verify the validity of the DeepSCNN, we conduct missing data imputation and node classification experiments on citation networks. Additionally, we compare the experimental performance of the DeepSCNN with that of simplicial neural networks (SNN) and simplicial convolutional networks (SCNN). The results show that our proposed DeepSCNN method outperforms SNN and SCNN.</p>", "Keywords": "Deep graph convolutional network; Simplicial complex; Simplicial neural network; Deep simplicial convolutional network", "DOI": "10.1007/s10489-024-06121-6", "PubYear": 2025, "Volume": "55", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Chunyang Tang", "Affiliation": "College of Computer, Qinghai Normal University, Xining, China; The State Key Laboratory of Tibetan Intelligent Information Processing and Application, Xining, China; Tibetan Information Processing and Machine Translation Key Laboratory of Qinghai Province, Xining, China"}, {"AuthorId": 2, "Name": "Zhonglin Ye", "Affiliation": "College of Computer, Qinghai Normal University, Xining, China; The State Key Laboratory of Tibetan Intelligent Information Processing and Application, Xining, China; Tibetan Information Processing and Machine Translation Key Laboratory of Qinghai Province, Xining, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer, Qinghai Normal University, Xining, China; The State Key Laboratory of Tibetan Intelligent Information Processing and Application, Xining, China; Tibetan Information Processing and Machine Translation Key Laboratory of Qinghai Province, Xining, China; Corresponding author."}, {"AuthorId": 4, "Name": "Libing Bai", "Affiliation": "College of Computer, Qinghai Normal University, Xining, China; The State Key Laboratory of Tibetan Intelligent Information Processing and Application, Xining, China; Tibetan Information Processing and Machine Translation Key Laboratory of Qinghai Province, Xining, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer, Qinghai Normal University, Xining, China; The State Key Laboratory of Tibetan Intelligent Information Processing and Application, Xining, China; Tibetan Information Processing and Machine Translation Key Laboratory of Qinghai Province, Xining, China"}], "References": [{"Title": "EduCross: Dual adversarial bipartite hypergraph learning for cross-modal retrieval in multimodal educational slides", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "109", "Issue": "", "Page": "102428", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 142805677, "Title": "Low-cost 3D human body reconstruction under limited views and girth measurement for the apparel customisation", "Abstract": "<p>To enhance the convenience of human body 3D modelling, this study proposes a low-cost method for 3D body reconstruction under limited views, aiming to easily acquire client body size information through smart phone photography. The human body photos of the front, side and back view are captured, and background removal is performed using the U<sup>2</sup>-Net human segmentation model. The PIFuHD model is utilised to obtain single-view point cloud patches, which are then mapped onto 2D images. A point cloud registration approach that incorporates regional segmentation and contour supervision is employed to reconstruct a complete human body model. B-spline curves are employed to fit key girths for obtaining perimeter measurements, and the effectiveness of girth measurements is validated using body data from 80 subjects. The results indicate that the proposed method exhibits closer proximity to manual measurements compared to 3D scanning, with average absolute errors within 2 cm.</p>", "Keywords": "Body reconstruction;apparel customisation;body modelling;limited views;point clouds registration", "DOI": "10.1080/00140139.2024.2449113", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Fashion Design & Engineering, Zhejiang Sci-Tech University, Hangzhou, Zhejiang, China"}, {"AuthorId": 2, "Name": "Hainan Zong", "Affiliation": "School of Fashion Design & Engineering, Zhejiang Sci-Tech University, Hangzhou, Zhejiang, China"}, {"AuthorId": 3, "Name": "Pinghua Xu", "Affiliation": "School of Fashion Design & Engineering, Zhejiang Sci-Tech University, Hangzhou, Zhejiang, China;Digital Intelligence Style and Creative Design Research Center, Key Research Center of Philosophy and Social Sciences of Zhejiang Province, Zhejiang Sci-Tech University, Hangzhou, Zhejiang, China;Key Laboratory of Silk Culture Heritage and Products Design Digital Technology, Ministry of Culture and Tourism, Hangzhou, Zhejiang, China;Tongxiang Research Institute, Zhejiang Sci-Tech University, Tongxiang, Zhejiang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Wenzhou Technician Institute, Wenzhou, Zhejiang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Fashion Design & Engineering, Zhejiang Sci-Tech University, Hangzhou, Zhejiang, China"}], "References": [{"Title": "Parametric human modelling to determine body surface area covered by sun-protective clothing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "3", "Page": "293", "JournalTitle": "Ergonomics"}, {"Title": "U2-Net: Going deeper with nested U-structure for salient object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "107404", "JournalTitle": "Pattern Recognition"}, {"Title": "Ear shape categorization for ergonomic product design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "", "Page": "102962", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Obtaining the Similarity Value of Human Body Motions Through Their Sub Motions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "4", "Page": "59", "JournalTitle": "International Journal of Software Innovation"}, {"Title": "3D standard avatar creation of Korean women in their twenties and thirties by body types for apparel industry", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103081", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Assessing the female figure identification technique’s reliability as a body shape classification system", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "8", "Page": "1035", "JournalTitle": "Ergonomics"}, {"Title": "Prediction of military combat clothing size using decision trees and 3D body scan data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "95", "Issue": "", "Page": "103435", "JournalTitle": "Applied Ergonomics"}, {"Title": "Parametric 3D modeling of young women's lower bodies based on shape classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "84", "Issue": "", "Page": "103142", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "A study on segmentation and refinement of key human body parts by integrating manual measurements", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "1", "Page": "60", "JournalTitle": "Ergonomics"}, {"Title": "Detailed 3D human body reconstruction from multi-view images combining voxel super-resolution and learned implicit representation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "6", "Page": "6739", "JournalTitle": "Applied Intelligence"}, {"Title": "Towards efficient and photorealistic 3D human reconstruction: A brief survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "11", "JournalTitle": "Visual Informatics"}, {"Title": "Individualized generation of young women's crotch curve based on body images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "90", "Issue": "", "Page": "103296", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Analysis of hallux valgus angles automatically extracted from 3D foot scans taken in North America, Europe, and Asia", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "8", "Page": "1164", "JournalTitle": "Ergonomics"}, {"Title": "A cluster-based law enforcement body armor sizing system: Concept, procedure, and design practice", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "117", "Issue": "", "Page": "104201", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 142805693, "Title": "On combining active learning and deep learning from label proportions in low-budget regimes", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13748-024-00361-w", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 7519, "JournalTitle": "Progress in Artificial Intelligence", "ISSN": "2192-6352", "EISSN": "2192-6360", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Active learning from label proportions via pSVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "464", "Issue": "", "Page": "227", "JournalTitle": "Neurocomputing"}, {"Title": "A Survey of Deep Active Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 142805744, "Title": "An objective-guided multi-strategy evolutionary algorithm for multi-objective coalition formation", "Abstract": "The coalition formation (CF) problem is crucial for reasonably organizing agents with diverse and complementary capabilities to address complex scenarios in collaborative environments. While CF has received some research attention, the multi-objective coalition formation (MOCF) problem remains relatively unexplored and presents significant challenges. In the context of disaster relief and emergency response, this paper delves into the MOCF problem and constructs the mathematical model, which minimizes both the latest arrival time and the total cost of coalition members under mission-specific capability constraints. To tackle this, this paper proposes an innovative objective-guided multi-strategy evolutionary algorithm (OGMSEA) for effective capability aggregation regarding mission requirements and objective trade-offs, which leverages the problem characteristics of multiple objectives and lower-bound constraints. The initialization strategies leverage various objective weights to generate a uniformly distributed and extensive set of initial solutions. The repair strategies restore unsatisfied coalitions by evaluating the alignment of idle agents with the remaining capability requirements and optimization objectives. The restart strategies reconstruct repetitive solutions to maintain the population diversity. Comprehensive experiments demonstrate OGMSEA’s superior performance in terms of applicability and adaptability, better achieving inverted generational distance and hypervolume metrics across 135 various cases compared with advanced algorithms. In large-scale complex scenarios (e.g., more than 100 agents, 10 missions, and a demand-supply ratio on capabilities of 0.5), OGMSEA consistently achieves a high-quality Pareto front due to its well-designed strategies. Additionally, a forest fire scenario is constructed and addressed by forming firefighting coalitions, demonstrating the practical applicability of this study.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109961", "PubYear": 2025, "Volume": "143", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, 100081, China;National Key Lab of Autonomous Intelligent Unmanned Systems, Beijing, 100081, China;Corresponding author at: School of Automation, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, 100081, China;National Key Lab of Autonomous Intelligent Unmanned Systems, Beijing, 100081, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Signal and Communication Research Institute, China Academy of Railway Sciences Corporation Limited, Beijing, 100081, China"}], "References": [{"Title": "Coalition formation with dynamically changing externalities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "103577", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multi-agent coalition formation by an efficient genetic algorithm with heuristic initialization and repair strategy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "100686", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Dynamic grouping of heterogeneous agents for exploration and strike missions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "1", "Page": "86", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "A survey on applications of coalition formation in multi‐agent systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "11", "Page": "e6876", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Nucleolus based cost allocation methods for a class of constrained lane covering games", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "172", "Issue": "", "Page": "108583", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A practical tutorial on solving optimization problems via PlatEMO", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "518", "Issue": "", "Page": "190", "JournalTitle": "Neurocomputing"}, {"Title": "A Concurrent Mission-Planning Methodology for Robotic Swarms Using Collaborative Motion-Control Strategies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "108", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Predefined-time distributed multiobjective optimization for network resource allocation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "7", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "A hybrid modified-NSGA-II VNS algorithm for the Multi-Objective Critical Disruption Path Problem", "Authors": "<PERSON><PERSON><PERSON> Granata; <PERSON><PERSON>", "PubYear": 2023, "Volume": "160", "Issue": "", "Page": "106363", "JournalTitle": "Computers & Operations Research"}, {"Title": "Pareto and decomposition based approaches for the multi-objective home health care routing and scheduling problem with lunch breaks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "128", "Issue": "", "Page": "107502", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Solving the multi-objective job shop scheduling problems with overtime consideration by an enhanced NSGA-Ⅱ", "Authors": "<PERSON><PERSON><PERSON> Shi; <PERSON><PERSON>", "PubYear": 2024, "Volume": "190", "Issue": "", "Page": "110001", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": *********, "Title": "Research Articles", "Abstract": "", "Keywords": "", "DOI": "10.3233/ICO-2020-28-1-s02", "PubYear": 2021, "Volume": "28", "Issue": "1", "JournalId": 24898, "JournalTitle": "Integrated Computer-Aided Engineering", "ISSN": "1069-2509", "EISSN": "1875-8835", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "AI Advances in ICU with an Emphasis on Sepsis Prediction: An Overview", "Abstract": "<p>Artificial intelligence (AI) is increasingly applied in a wide range of healthcare and Intensive Care Unit (ICU) areas to serve—among others—as a tool for disease detection and prediction, as well as for healthcare resources’ management. Since sepsis is a high mortality and rapidly developing organ dysfunction disease afflicting millions in ICUs and costing huge amounts to treat, the area can benefit from the use of AI tools for early and informed diagnosis and antibiotic administration. Additionally, resource allocation plays a crucial role when patient flow is increased, and resources are limited. At the same time, sensitive data use raises the need for ethical guidelines and reflective datasets. Additionally, explainable AI is applied to handle AI opaqueness. This study aims to present existing clinical approaches for infection assessment in terms of scoring systems and diagnostic biomarkers, along with their limitations, and an extensive overview of AI applications in healthcare and ICUs in terms of (a) sepsis detection/prediction and sepsis mortality prediction, (b) length of ICU/hospital stay prediction, and (c) ICU admission/hospitalization prediction after Emergency Department admission, each constituting an important factor towards either prompt interventions and improved patient wellbeing or efficient resource management. Challenges of AI applications in ICU are addressed, along with useful recommendations to mitigate them. Explainable AI applications in ICU are described, and their value in validating, and translating predictions in the clinical setting is highlighted. The most important findings and future directions including multimodal data use and Transformer-based models are discussed. The goal is to make research in AI advances in ICU and particularly sepsis prediction more accessible and provide useful directions on future work.</p>", "Keywords": "", "DOI": "10.3390/make7010006", "PubYear": 2025, "Volume": "7", "Issue": "1", "JournalId": 51955, "JournalTitle": "Machine Learning and Knowledge Extraction", "ISSN": "", "EISSN": "2504-4990", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre of Excellence, CYENS, Nicosia 1016, Cyprus"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre of Excellence, CYENS, Nicosia 1016, Cyprus"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre of Excellence, CYENS, Nicosia 1016, Cyprus"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 176 76 Kallithea, Greece"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 176 76 Kallithea, Greece"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 176 76 Kallithea, Greece"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 176 76 Kallithea, Greece"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre of Excellence, CYENS, Nicosia 1016, Cyprus;Faculty of Pure and Applied Sciences, Open University of Cyprus, Latsia 2220, Cyprus"}, {"AuthorId": 9, "Name": "Eleni Politi", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, 176 76 Kallithea, Greece"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "UBITECH Limited, Limassol 3071, Cyprus"}, {"AuthorId": 11, "Name": "<PERSON>ant<PERSON>", "Affiliation": "UBITECH Limited, Limassol 3071, Cyprus"}, {"AuthorId": 12, "Name": "Fr<PERSON><PERSON> Garcia", "Affiliation": "Research & Development Department, 3aHealth, Strovolos 2020, Cyprus"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research & Development Department, 3aHealth, Strovolos 2020, Cyprus"}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": "State Health Services Organization, Aglantzia 2100, Cyprus"}, {"AuthorId": 15, "Name": "<PERSON><PERSON>", "Affiliation": "State Health Services Organization, Aglantzia 2100, Cyprus"}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": "State Health Services Organization, Aglantzia 2100, Cyprus"}, {"AuthorId": 17, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of New Mexico, Albuquerque, NM 87106, USA"}, {"AuthorId": 18, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre of Excellence, CYENS, Nicosia 1016, Cyprus"}, {"AuthorId": 19, "Name": "<PERSON>", "Affiliation": "Centre of Excellence, CYENS, Nicosia 1016, Cyprus"}], "References": [{"Title": "Predictive modelling and analytics for diabetes using a machine learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1/2", "Page": "90", "JournalTitle": "Applied Computing and Informatics"}, {"Title": "BioBERT: a pre-trained biomedical language representation model for biomedical text mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1234", "JournalTitle": "Bioinformatics"}, {"Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "336", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "HealthFog: An ensemble deep learning based Smart Healthcare System for Automatic Diagnosis of Heart Diseases in integrated IoT and fog computing environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "187", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "Heart Disease Prediction using Machine Learning Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "6", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "An interpretable deep-learning model for early prediction of sepsis in the emergency department", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "100196", "JournalTitle": "Patterns"}, {"Title": "Machine Learning Algorithms For Breast Cancer Prediction And Diagnosis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "191", "Issue": "", "Page": "487", "JournalTitle": "Procedia Computer Science"}, {"Title": "An integrated optimization and machine learning approach to predict the admission status of emergency patients", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117314", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Early prediction of sepsis using double fusion of deep features and handcrafted features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Mingzhou Chen", "PubYear": 2023, "Volume": "53", "Issue": "14", "Page": "17903", "JournalTitle": "Applied Intelligence"}, {"Title": "Artificial Intelligence Ethics and Challenges in Healthcare Applications: A Comprehensive Review in the Context of the European GDPR Mandate", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "3", "Page": "1023", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "Machine learning model for healthcare investments predicting the length of stay in a hospital & mortality rate", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "9", "Page": "27121", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An explainable decision model based on extended belief-rule-based systems to predict admission to the intensive care unit during COVID-19 breakout", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "149", "Issue": "", "Page": "110961", "JournalTitle": "Applied Soft Computing"}, {"Title": "Systematic review and network meta-analysis of machine learning algorithms in sepsis prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "245", "Issue": "", "Page": "122982", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An Intelligent Model and Methodology for Predicting Length of Stay and Survival in a Critical Care Hospital Unit", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "2", "Page": "34", "JournalTitle": "Informatics"}, {"Title": "LoSNet: A Tailored Deep Neural Network Framework for Precise Length of Stay Prediction in Disease-Specific Hospitalization", "Authors": "Veningston K; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "235", "Issue": "", "Page": "2599", "JournalTitle": "Procedia Computer Science"}, {"Title": "Evaluation Metrics for Generative Models: An Empirical Study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "6", "Issue": "3", "Page": "1531", "JournalTitle": "Machine Learning and Knowledge Extraction"}]}, {"ArticleId": 142805802, "Title": "Waging warfare against states: the deployment of artificial intelligence in cyber espionage", "Abstract": "Cyber espionage has significantly been viewed as a risk towards nation-states, especially in the area of security and protection of Critical National Infrastructures. The race against digitisation has also raised concerns about how emerging technologies are defining how cyber activities are linked to waging warfare between States. Real-world crimes have since found a place in cyberspace, and with high connectivity, has exposed various actors to various risks and vulnerabilities, including cyber espionage. Cyber espionage has always been a national security issue as it does not only target States but also affects public–private networks, corporations and individuals. The challenge of crimes committed within the cyber realm is how the nature of cybercrimes distorts the dichotomy of state responsibility in responding to cyber threats and vulnerabilities. Furthermore, the veil of anonymity and emerging technologies such as artificial intelligence have further provided opportunities for a larger scale impact on the state for such crime. The imminent threat of cyber espionage is impacting the economic and political interactions between nation-states and changing the nature of modern conflict. Due to these implications, this paper will discuss the current legal landscape governing cyber espionage and the impact of the use of artificial intelligence in the commission of such crimes.", "Keywords": "Cyber espionage; Artificial intelligence; Cyber operations; Warfare; International law", "DOI": "10.1007/s43681-024-00628-x", "PubYear": 2025, "Volume": "5", "Issue": "1", "JournalId": 79924, "JournalTitle": "AI and Ethics", "ISSN": "2730-5953", "EISSN": "2730-5961", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Law, Faculty of Management, Law and Social Sciences, University of Bradford, Bradford, UK; Corresponding author."}], "References": [{"Title": "On the strategic consequences of digital espionage", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "3", "Page": "429", "JournalTitle": "Journal of Cyber Policy"}, {"Title": "Deceiving AI-based malware detection through polymorphic attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "143", "Issue": "", "Page": "103751", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 142806172, "Title": "Exposing JPEG compression footprints by using second-order statistical analysis", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-20580-6", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Alok K<PERSON>", "Affiliation": ""}], "References": [{"Title": "Low-complexity fake face detection based on forensic similarity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "3", "Page": "353", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 142806239, "Title": "Global output feedback stabilization of stochastic uncertain nonlinear systems with large constant delays", "Abstract": "", "Keywords": "", "DOI": "10.1080/23307706.2024.2448473", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 30013, "JournalTitle": "Journal of Control and Decision", "ISSN": "2330-7706", "EISSN": "2330-7714", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics Science, Liaocheng University, Liaocheng, Shandong Province, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics Science, Liaocheng University, Liaocheng, Shandong Province, People's Republic of China"}], "References": [{"Title": "Distributed adaptive control for high-order stochastic nonlinear systems with unknown control gains and uncertainties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "4", "Page": "484", "JournalTitle": "Journal of Control and Decision"}, {"Title": "Memoryless output feedback control for a class of stochastic nonlinear systems with large delays in the state and input", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "171", "Issue": "", "Page": "105431", "JournalTitle": "Systems & Control Letters"}, {"Title": "Adaptive output feedback reinforcement learning control for continuous time switched stochastic nonlinear systems with unknown control coefficients and full-state constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "55", "Issue": "2", "Page": "332", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": 142806280, "Title": "Cancer Detection Using Artificial Intelligence: A Paradigm in Early Diagnosis", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11831-024-10209-0", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "K. E. Ch Vidyasagar", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "An integrated design of particle swarm optimization (PSO) with fusion of features for detection of brain tumor", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "150", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deep learning for lung Cancer detection and classification", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7731", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "MRI brain tumor detection using optimal possibilistic fuzzy C-means clustering algorithm and adaptive k-nearest neighbor classifier", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2867", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "AI-based smart prediction of clinical disease using random forest classifier and Naive Bayes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "5", "Page": "5198", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Deep CNN for Brain Tumor Classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "671", "JournalTitle": "Neural Processing Letters"}, {"Title": "A hybrid artificial bee colony with whale optimization algorithm for improved breast cancer diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "20", "Page": "13667", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An automatic method for segmentation of liver lesions in computed tomography images using deep neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; Jon<PERSON>on <PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "115064", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Segmentation and classification of renal tumors based on convolutional neural network", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "412", "JournalTitle": "Journal of Radiation Research and Applied Sciences"}, {"Title": "Semantic consistency generative adversarial network for cross-modality domain adaptation in ultrasound thyroid nodule classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "10369", "JournalTitle": "Applied Intelligence"}, {"Title": "Breast Cancer Detection and Classification using Deep Learning Xception Algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "7", "Page": "223", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}]}, {"ArticleId": 142806416, "Title": "Advanced colon cancer detection: Integrating context-aware multi-image fusion (Camif) in a multi-stage framework", "Abstract": "Colon cancer begins in the large intestine, often evolving from benign polyps into malignant cancer. Early detection through screening is vital for effective treatment and better survival rates. Risk factors include age, genetics, diet, and lifestyle, with symptoms like changes in bowel habits and blood in the stool, though early stages may be asymptomatic. This work proposed a comprehensive multi classes detection and classification of colon cancer. In this work we used publicly available Curated Colon Dataset to diagnose conditions such as esophagitis, ulcerative colitis, polyps, and normal cases. The proposed approach uses advanced deep learning models to integrate pre-processing, segmentation, and classification. The process begins with pre-processing, which involves resizing, contrast enhancement, noise reduction, and normalization of pixel values. This work proposes a Context-Aware Multi-Image Fusion (CA-MIF) technique in the preprocessing phase to improve the visibility of blood vessels and tissue texture, enhancing diagnostic accuracy. The processed images are then input to a U-Net++ model for segmentation, generating masks highlighting regions of interest, including the colon and affected areas. Post-segmentation, image enhancement techniques further refine the quality and clarity of the images. Enhanced images are then classified using the ResNet-50 model, trained to categorize images into four distinct classes: esophagitis, ulcerative colitis, polyps, and normal. In the classification phase, cancerous classes (ulcerative colitis and polyps) undergo additional segmentation using DeepLabv3+. Model 1 (DeepLabv3+) is applied to ulcerative colitis, generating detailed masks to analyze affected regions, while Model 2 (DeepLabv3+) is used for polyps. For the U-Net++ and DeepLabv3+ models, evaluation measures are segmentation accuracy, precision, recall, and F1 score; for the ResNet-50 model, these metrics are classification accuracy, precision, recall, and F1 score. When it comes to detecting and differentiating between malignant and non-cancerous illnesses, the framework achieves great accuracy., demonstrating its effectiveness and potential for clinical applications in medical image analysis. The results indicate the proposed method’s high efficacy, achieving an F1 score of 99.31. It also demonstrated strong performance metrics with a specificity of 99.91, sensitivity of 99.10, accuracy of 98.18, and a Dice coefficient of 99.82, highlighting its robust capability in accurately detecting colon cancer.", "Keywords": "Colorectal; Deeplabv3+; PCNN; ResNet50; CLAHE; U-NET++; CA-MIF", "DOI": "10.1016/j.eij.2025.100609", "PubYear": 2025, "Volume": "29", "Issue": "", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, <PERSON><PERSON> Engineering College (Autonomous), Kurnool, India"}], "References": [{"Title": "Distribution-Sensitive Information Retention for Accurate Binary Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "1", "Page": "26", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Predicting Colorectal Cancer Using Machine and Deep Learning Algorithms: Challenges and Opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "2", "Page": "74", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Colorectal Cancer Segmentation Algorithm Based on Deep Features from Enhanced CT Images", "Authors": "<PERSON>; <PERSON><PERSON> Lu; <PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "2", "Page": "2495", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 142806476, "Title": "SPEMix: a lightweight method via superclass pseudo-label and efficient mixup for echocardiogram view classification", "Abstract": "Introduction <p>In clinical, the echocardiogram is the most widely used for diagnosing heart diseases. Different heart diseases are diagnosed based on different views of the echocardiogram images, so efficient echocardiogram view classification can help cardiologists diagnose heart disease rapidly. Echocardiogram view classification is mainly divided into supervised and semi-supervised methods. The supervised echocardiogram view classification methods have worse generalization performance due to the difficulty of labeling echocardiographic images, while the semi-supervised echocardiogram view classification can achieve acceptable results via a little labeled data. However, the current semi-supervised echocardiogram view classification faces challenges of declining accuracy due to out-of-distribution data and is constrained by complex model structures in clinical application.</p> Methods <p>To deal with the above challenges, we proposed a novel open-set semi-supervised method for echocardiogram view classification, SPEMix, which can improve performance and generalization by leveraging out-of-distribution unlabeled data. Our SPEMix consists of two core blocks, DAMix Block and SP Block. DAMix Block can generate a mixed mask that focuses on the valuable regions of echocardiograms at the pixel level to generate high-quality augmented echocardiograms for unlabeled data, improving classification accuracy. SP Block can generate a superclass pseudo-label of unlabeled data from the perspective of the superclass probability distribution, improving the classification generalization by leveraging the superclass pseudolabel.</p> Results <p>We also evaluate the generalization of our method on the Unity dataset and the CAMUS dataset. The lightweight model trained with SPEMix can achieve the best classification performance on the publicly available TMED2 dataset.</p> Discussion <p>For the first time, we applied the lightweight model to the echocardiogram view classification, which can solve the limits of the clinical application due to the complex model architecture and help cardiologists diagnose heart diseases more efficiently.</p>", "Keywords": "lightweight; superclass pseudo-label; semi-supervised; open-set; echocardiogram view classification", "DOI": "10.3389/frai.2024.1467218", "PubYear": 2025, "Volume": "7", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "Shizhou Ma", "Affiliation": "College of Aulin, Northeast Forestry University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Control Engineering, Northeast Forestry University, China"}, {"AuthorId": 3, "Name": "Del<PERSON> Li", "Affiliation": "College of Computer and Control Engineering, Northeast Forestry University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Department of Ultrasound, Harbin Medical University Cancer Hospital, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Control Engineering, Northeast Forestry University, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cardiovascular Surgery, First Affiliated Hospital With Nanjing Medical University, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Control Engineering, Northeast Forestry University, China; Corresponding author."}], "References": [{"Title": "Semisupervised Deep Learning for Image Classification With Distribution Mismatch: A Survey", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "6", "Page": "1015", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Adversarial image perturbations with distortions weighted by color on deep neural networks", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "9", "Page": "13779", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Detecting textual adversarial examples through text modification on text classification systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "16", "Page": "19161", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 142806622, "Title": "SPECIAL SECTION: Digital Government and Sustainable Development Goals", "Abstract": "", "Keywords": "", "DOI": "10.3233/IPO-2024-29-1-s01", "PubYear": 2024, "Volume": "29", "Issue": "1", "JournalId": 28496, "JournalTitle": "Information Polity", "ISSN": "1570-1255", "EISSN": "1875-8754", "Authors": [], "References": []}, {"ArticleId": 142806623, "Title": "Editorial", "Abstract": "", "Keywords": "", "DOI": "10.3233/ICO-2021-28-2-s01", "PubYear": 2021, "Volume": "28", "Issue": "2", "JournalId": 24898, "JournalTitle": "Integrated Computer-Aided Engineering", "ISSN": "1069-2509", "EISSN": "1875-8835", "Authors": [], "References": []}, {"ArticleId": 142806675, "Title": "FUR-HABE: A Hierarchical CP-ABE Scheme With Traceable Fine-Grained User Revocation for Cloud Storage", "Abstract": "<p>An effective method to protect cloud data is access control. But, the efficiency of key distribution by a single authority is low, and it is difficult to achieve dynamic attribute revocation when system properties are shared by multiple users. Existing attribute revocation mechanisms face challenges in terms of functional complexity and computational efficiency, which hinder their practical application. To address these issues, this paper put forward a Hierarchical CP-ABE scheme with Traceable Fine-grained User Revocation for Cloud Storage (FUR-HABE). In this scheme, most of the decryption calculations are outsourced to cloud servers. It employs a layered key authorization mechanism to provide flexible and scalable key delegation. Additionally, the scheme supports key encapsulation key (KEK) attribute revocation and user revocation to accommodate different revocation needs, enabling flexible revocation.</p>", "Keywords": "", "DOI": "10.4018/IJISP.365602", "PubYear": 2025, "Volume": "19", "Issue": "1", "JournalId": 13525, "JournalTitle": "International Journal of Information Security and Privacy", "ISSN": "1930-1650", "EISSN": "1930-1669", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hebei University, China"}], "References": [{"Title": "Extended File Hierarchy Access Control Scheme with Attribute-Based Encryption in Cloud Computing", "Authors": "JIGUO LI; <PERSON>ING<PERSON><PERSON> CHEN; <PERSON>ICHEN ZHANG", "PubYear": 2021, "Volume": "9", "Issue": "2", "Page": "983", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Revocable identity-based encryption with server-aided ciphertext evolution", "Authors": "<PERSON><PERSON> Sun; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "815", "Issue": "", "Page": "11", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Flexible revocation in ciphertext-policy attribute-based encryption with verifiable ciphertext delegation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "14", "Page": "22251", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Revocation in attribute-based encryption for fog-enabled internet of things: A systematic survey", "Authors": "<PERSON>-<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "", "Page": "100827", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 142806773, "Title": "Emotional Analysis of Tourism Reviews Based on Long Short-Term Memory and Fuzzy Control Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1007/s40815-024-01890-1", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A heuristic fuzzy algorithm for assessing and managing tourism sustainability", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4027", "JournalTitle": "Soft Computing"}, {"Title": "Inbound tourism demand forecasting framework based on fuzzy time series and advanced optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106320", "JournalTitle": "Applied Soft Computing"}, {"Title": "Tourism Attraction Selection with Sentiment Analysis of Online Reviews Based on Probabilistic Linguistic Term Sets and the IDOCRIW-COCOSO Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "1", "Page": "295", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Tourism destination management using sentiment analysis and geo-location information: a deep learning approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "241", "JournalTitle": "Information Technology & Tourism"}, {"Title": "Customer reviews sentiment-based analysis and clustering for market-oriented tourism services and products development or positioning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "199", "JournalTitle": "Procedia Computer Science"}, {"Title": "Sensory and Emotional Smart Cultural Tourism: a conceptual paper", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "283", "JournalTitle": "Procedia Computer Science"}, {"Title": "Using machine learning algorithms for predicting real estate values in tourism centers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Al<PERSON>ş", "PubYear": 2023, "Volume": "27", "Issue": "5", "Page": "2601", "JournalTitle": "Soft Computing"}, {"Title": "An interactive visualization of location-based reviews using word cloud and OpenStreetMap for tourism applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "2", "Page": "235", "JournalTitle": "Spatial Information Research"}]}, {"ArticleId": 142806855, "Title": "Efficient Data Augmentation Methods for Crop Disease Recognition in Sustainable Environmental Systems", "Abstract": "<p>Crop diseases significantly threaten agricultural productivity, leading to unstable food supply and economic losses. The current approaches to automated crop disease recognition face challenges such as limited datasets, restricted coverage of disease types, and inefficient feature extraction, which hinder their generalization across diverse crops and disease patterns. To address these challenges, we propose an efficient data augmentation method to enhance the performance of deep learning models for crop disease recognition. By constructing a new large-scale dataset comprising 24 different classes, including both fruit and leaf samples, we intend to handle a variety of disease patterns and improve model generalization capabilities. Geometric transformations and color space augmentation techniques are applied to validate the efficiency of deep learning models, specifically convolution and transformer models, in recognizing multiple crop diseases. The experimental results show that these augmentation techniques improve classification accuracy, achieving F1 scores exceeding 98%. Feature map analysis further confirms that the models effectively capture key disease characteristics. This study underscores the importance of data augmentation in developing automated, energy-efficient, and environmentally sustainable crop disease detection solutions, contributing to more sustainable agricultural practices.</p>", "Keywords": "", "DOI": "10.3390/bdcc9010008", "PubYear": 2025, "Volume": "9", "Issue": "1", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Gachon University, Seongnam 13120, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Gachon University, Seongnam 13120, Republic of Korea"}], "References": []}, {"ArticleId": 142806923, "Title": "A Soft Wearable Robot with an Adjustable Twisted String Actuator and a Two‐Stage Transmission Mechanism for Manual Handling Tasks", "Abstract": "", "Keywords": "", "DOI": "10.1002/aisy.202400700", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering Yonsei University  Seoul 03722 Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering Chung‐Ang University  Seoul 06974 Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering Yonsei University  Seoul 03722 Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering Yonsei University  Seoul 03722 Republic of Korea"}], "References": [{"Title": "Body-powered variable impedance: An approach to augmenting humans with a passive device by reshaping lifting posture", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "57", "Page": "eabe1243", "JournalTitle": "Science Robotics"}, {"Title": "A compact, compliant, and biomimetic robotic assistive glove driven by twisted string actuators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "381", "JournalTitle": "International Journal of Intelligent Robotics and Applications"}]}, {"ArticleId": 142807143, "Title": "The fuzzy hypergraph neural network model based on sparse k-nearest neighborhood granule", "Abstract": "The Hypergraph Neural Network (HGNN) model, widely applied in hyperedge prediction and node classification, encodes high-order data correlation in hypergraph structure. However, the classical HGNN model cannot eliminate the heterogeneity of data. To address the issue, the fuzzy hypergraph neural network model (FHGNN) based on sparse k -nearest neighborhood granule is proposed in this paper, where the framework consists of three procedures: Hyperedge modeling, Fuzzy hypergraph construction, and Fuzzy hypergraph convolution. First, the hyperedge granule model is constructed based on k -nearest neighbors of nodes, viewed through the lens of granular computing. Moreover, we assigned different optimal k values to nodes based on the proposed sparse constraint function. We construct the hyperedge model of optimal k nodes with low heterogeneity, eliminating heterogeneity of data. Second, we introduced <PERSON><PERSON><PERSON><PERSON> (D–S) evidence theory to fuse different belief functions for describing the fuzzy membership between nodes and hyperedge. Third, we constructed the fuzzy hypergraph convolution model based on fuzzy membership. The weight of nodes with high heterogeneity is low, reducing data heterogeneity. The experimental results on citation network and visual object datasets demonstrate that the proposed FHGNN model outperforms other comparison methods. Based on the result of the schizophrenic dataset, the proposed FHGNN model eliminates the heterogeneity of data, improving diagnosis accuracy in schizophrenia patients and providing a new direction for the HGNN model optimization.", "Keywords": "", "DOI": "10.1016/j.asoc.2025.112721", "PubYear": 2025, "Volume": "170", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Nantong University, Nantong, 226019, China;School of Artificial Intelligence and Computer Science, Nantong University, Nantong, 226019, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Computer Science, Nantong University, Nantong, 226019, China;Faculty of Data Science, City University of Macau, 999078, Macao Special Administrative Region of China;Corresponding author at: School of Artificial Intelligence and Computer Science, Nantong University, Nantong, 226019, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Computer Science, Nantong University, Nantong, 226019, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Computer Science, Nantong University, Nantong, 226019, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Nantong University, Nantong, 226019, China;School of Artificial Intelligence and Computer Science, Nantong University, Nantong, 226019, China"}], "References": [{"Title": "Hypergraph membrane system based F 2 fully convolutional neural network for brain tumor segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106454", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hypergraph convolution and hypergraph attention", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107637", "JournalTitle": "Pattern Recognition"}, {"Title": "FC–HAT: Hypergraph attention network for functional brain network classification", "Authors": "Junzhong Ji; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1301", "JournalTitle": "Information Sciences"}, {"Title": "Fuzzy hypergraph network for recommending top-K profitable stocks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "613", "Issue": "", "Page": "239", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic hypergraph neural networks based on key hyperedges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "616", "Issue": "", "Page": "37", "JournalTitle": "Information Sciences"}, {"Title": "A new Bayesian network model for risk assessment based on cloud model, interval type-2 fuzzy sets and improved D-S evidence theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "618", "Issue": "", "Page": "336", "JournalTitle": "Information Sciences"}, {"Title": "A decomposition-based hybrid ensemble CNN framework for driver fatigue recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Ponnuthurai Nagaratnam Suganthan", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "833", "JournalTitle": "Information Sciences"}, {"Title": "Feature Selection for Unbalanced Distribution Hybrid Data Based on ${k}$-Nearest Neighborhood Rough Set", "Authors": "<PERSON><PERSON> Xu; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "1", "Page": "229", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "PN-GCN: Positive-negative graph convolution neural network in information system to classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "632", "Issue": "", "Page": "411", "JournalTitle": "Information Sciences"}, {"Title": "Explainable and programmable hypergraph convolutional network for imaging genetics data fusion", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101950", "JournalTitle": "Information Fusion"}, {"Title": "Brain-inspired GCN: Modularity-based Siamese simple graph convolutional networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "657", "Issue": "", "Page": "119971", "JournalTitle": "Information Sciences"}, {"Title": "Multi-objective compression for CNNs via evolutionary algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "661", "Issue": "", "Page": "120155", "JournalTitle": "Information Sciences"}, {"Title": "Stock trend prediction based on dynamic hypergraph spatio-temporal network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "154", "Issue": "", "Page": "111329", "JournalTitle": "Applied Soft Computing"}, {"Title": "FE-RNN: A fuzzy embedded recurrent neural network for improving interpretability of underlying neural network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "663", "Issue": "", "Page": "120276", "JournalTitle": "Information Sciences"}, {"Title": "Self-supervised hypergraph neural network for session-based recommendation supported by user continuous topic intent", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "154", "Issue": "", "Page": "111406", "JournalTitle": "Applied Soft Computing"}, {"Title": "Improved stochastic configuration networks with vision patch fusion method for industrial image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "670", "Issue": "", "Page": "120570", "JournalTitle": "Information Sciences"}, {"Title": "A Quantum Group Decision Model for Meteorological Disaster Emergency Response Based on D-S evidence theory and Choquet Integral", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "674", "Issue": "", "Page": "120707", "JournalTitle": "Information Sciences"}, {"Title": "Adaptive Three-way KNN Classifier Using Density-based Granular Balls", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "678", "Issue": "", "Page": "120858", "JournalTitle": "Information Sciences"}, {"Title": "GGT-SNN: Graph learning and Gaussian prior integrated spiking graph neural network for event-driven tactile object recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "677", "Issue": "", "Page": "120998", "JournalTitle": "Information Sciences"}, {"Title": "Knowledge graph-driven mountain railway alignment optimization integrating karst hazard assessment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "167", "Issue": "", "Page": "112421", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 142807244, "Title": "Enhanced end-to-end regression algorithm for autonomous road damage detection", "Abstract": "<p>To address challenges such as variations in lighting, weather, and the size and shape of cracks and potholes, we propose an enhanced end-to-end regression algorithm for autonomous road damage detection. This method balances computational efficiency and accuracy by incorporating feature extraction structures to improve performance in scenarios involving multiple damage types, shadows, and fine-grained feature variations. The proposed model integrates a down-sampling structure for dimensionality reduction and feature extraction, an inverted residual mobile block for feature fusion, and an attention mechanism with multi-scale features for multi-scale detail extraction. Additionally, the integration of a Decoupled Head structure enhances bounding box localization. Experimental results show that the proposed method outperforms YOLOv5s (You Only Look Once version 5 small), achieving a 2.9% improvement in the F1 score and a 4% improvement in the mean average precision. Further validation through visualization experiments in seven challenging road scenarios, including varying lighting and environmental conditions, highlights the model’s superior detection accuracy, completeness, and robustness.</p>", "Keywords": "Road damage detection; YOLO series; Attention mechanism; Deep learning; Structural inspection", "DOI": "10.1007/s11227-024-06871-7", "PubYear": 2025, "Volume": "81", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China"}, {"AuthorId": 4, "Name": "Fanruo Li", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China"}], "References": [{"Title": "Recognition of road cracks based on multi-scale Retinex fused with wavelet transform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "", "Page": "100193", "JournalTitle": "Array"}, {"Title": "SHREC 2022: Pothole and crack detection in the road pavement using images and RGB-D data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "107", "Issue": "", "Page": "161", "JournalTitle": "Computers & Graphics"}, {"Title": "DenseSPH-YOLOv5: An automated damage detection model based on DenseNet and Swin-Transformer prediction head-enabled YOLOv5 with attention mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "102007", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An improved fire detection approach based on YOLO-v8 for smart cities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "28", "Page": "20939", "JournalTitle": "Neural Computing and Applications"}, {"Title": "LE-YOLOv5: A Lightweight and Efficient Road Damage Detection Algorithm Based on Improved YOLOv5", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Automatic augmentation and segmentation system for three-dimensional point cloud of pavement potholes by fusion convolution and transformer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102378", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "From global challenges to local solutions: A review of cross-country collaborations and winning strategies in road damage detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102388", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "The study of recognizing ripe strawberries based on the improved YOLOv7-Tiny model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "41", "Issue": "5", "Page": "3155", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 142807296, "Title": "Editorials", "Abstract": "", "Keywords": "", "DOI": "10.3233/ICO-2020-28-1-s01", "PubYear": 2021, "Volume": "28", "Issue": "1", "JournalId": 24898, "JournalTitle": "Integrated Computer-Aided Engineering", "ISSN": "1069-2509", "EISSN": "1875-8835", "Authors": [], "References": []}, {"ArticleId": 142807461, "Title": "Re‐identification of patterned animals by multi‐image feature aggregation and geometric similarity", "Abstract": "Image‐based re‐identification of animal individuals allows gathering of information such as population size and migration patterns of the animals over time. This, together with large image volumes collected using camera traps and crowdsourcing, opens novel possibilities to study animal populations. For many species, the re‐identification can be done by analysing the permanent fur, feather, or skin patterns that are unique to each individual. In this paper, the authors study pattern feature aggregation based re‐identification and consider two ways of improving accuracy: (1) aggregating pattern image features over multiple images and (2) combining the pattern appearance similarity obtained by feature aggregation and geometric pattern similarity. Aggregation over multiple database images of the same individual allows to obtain more comprehensive and robust descriptors while reducing the computation time. On the other hand, combining the two similarity measures allows to efficiently utilise both the local and global pattern features, providing a general re‐identification approach that can be applied to a wide variety of different pattern types. In the experimental part of the work, the authors demonstrate that the proposed method achieves promising re‐identification accuracies for Saimaa ringed seals and whale sharks without species‐specific training or fine‐tuning.", "Keywords": "", "DOI": "10.1049/cvi2.12337", "PubYear": 2025, "Volume": "19", "Issue": "1", "JournalId": 11350, "JournalTitle": "IET Computer Vision", "ISSN": "1751-9632", "EISSN": "1751-9640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Vision and Pattern Recognition Laboratory Department of Computational Engineering Lappeenranta‐Lahti University of Technology LUT  Lappeenranta Finland;Department of Computer Science Rensselaer Polytechnic Institute  Troy New York USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Vision and Pattern Recognition Laboratory Department of Computational Engineering Lappeenranta‐Lahti University of Technology LUT  Lappeenranta Finland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Vision and Pattern Recognition Laboratory Department of Computational Engineering Lappeenranta‐Lahti University of Technology LUT  Lappeenranta Finland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science Rensselaer Polytechnic Institute  Troy New York USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Vision and Pattern Recognition Laboratory Department of Computational Engineering Lappeenranta‐Lahti University of Technology LUT  Lappeenranta Finland;Department of Computer Science Rensselaer Polytechnic Institute  Troy New York USA"}], "References": []}, {"ArticleId": 142807663, "Title": "TMVF: Trusted Multi-View Fish Behavior Recognition with correlative feature and adaptive evidence fusion", "Abstract": "Utilizing multi-view learning to analyze fish behavior is crucial for fish disease early warning and developing intelligent feeding strategies. Trusted multi-view classification based on Dempster–Shafer Theory (DST) can effectively resolve view conflicts and significantly improve accuracy. However, these DST-based methods often assume that view source domain data are “independent”, and ignore the associations between different views, this can lead to inaccurate fusion and decision errors. To address this limitation, this paper proposes a Trusted Multi-View Fish (TMVF) Behavior Recognition Model that leverages adaptive fusion of associative feature evidence. TMVF employs a Multi-Source Composite Backbone (MSCB) at the feature level to integrate learning across different visual feature dimensions, providing non-independent feature vectors for deeper associative distribution learning. Additionally, a Trusted Association Multi-view (TAMV) Feature Fusion Module is introduced at the vector evidence level. TAMV utilizes a cross-association fusion method to capture the deeper associations between feature vectors rather than treating them as independent sources. It also employs a Dirichlet distribution for more reliable predictions, addressing conflicts between views. To validate TMVF’s performance, a real-world Multi-view Fish Behavior Recognition Dataset (MFBR) with top, underwater, and depth color views was constructed. Experimental results demonstrated TAMV’s superior performance on both the SynDD2 and MFBR datasets. Notably, TMVF achieved an accuracy of 98.48% on SynDD2, surpassing the Frame-flexible network (FFN) by 9.94%. On the MFBR dataset, TMVF achieved an accuracy of 96.56% and an F1-macro score of 94.31%, outperforming I3d+resnet50 by 10.62% and 50.4%, and the FFN by 4.5% and 30.58%, respectively. This demonstrates the effectiveness of TMVF in multi view tasks such as human and animal behavior recognition. The code will be publicly available on GitHub ( https://github.com/crazysboy/TMVF ).", "Keywords": "", "DOI": "10.1016/j.inffus.2024.102899", "PubYear": 2025, "Volume": "118", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center for Information Technology in Agriculture, Beijing, 100097, China;Information Technology Research Center, Beijing Academy of Agriculture and Forestry Sciences, Beijing, 100097, China;National Engineering Laboratory for Agriproduct Quality Traceability, Beijing, 100097, China;College Of Information Engineering, Northwest A&F University, Yangling, 712100, Shaanxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center for Information Technology in Agriculture, Beijing, 100097, China;Information Technology Research Center, Beijing Academy of Agriculture and Forestry Sciences, Beijing, 100097, China;National Engineering Laboratory for Agriproduct Quality Traceability, Beijing, 100097, China"}, {"AuthorId": 3, "Name": "Chunjiang Zhao", "Affiliation": "National Engineering Research Center for Information Technology in Agriculture, Beijing, 100097, China;Information Technology Research Center, Beijing Academy of Agriculture and Forestry Sciences, Beijing, 100097, China;National Engineering Laboratory for Agriproduct Quality Traceability, Beijing, 100097, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Engineering Research Center for Information Technology in Agriculture, Beijing, 100097, China;Information Technology Research Center, Beijing Academy of Agriculture and Forestry Sciences, Beijing, 100097, China;National Engineering Laboratory for Agriproduct Quality Traceability, Beijing, 100097, China;Corresponding author at: National Engineering Research Center for Information Technology in Agriculture, Beijing, 100097, China"}], "References": [{"Title": "Computer Vision Models in Intelligent Aquaculture with Emphasis on Fish Detection and Behavior Analysis: A Review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "4", "Page": "2785", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Learnable graph convolutional network and feature fusion for multi-view learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "109", "JournalTitle": "Information Fusion"}, {"Title": "Learnable graph convolutional network and feature fusion for multi-view learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "109", "JournalTitle": "Information Fusion"}, {"Title": "Multi-view domain-adaptive representation learning for EEG-based emotion recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "104", "Issue": "", "Page": "102156", "JournalTitle": "Information Fusion"}, {"Title": "Semi-supervised classification with pairwise constraints: A case study on animal identification from video", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "104", "Issue": "", "Page": "102188", "JournalTitle": "Information Fusion"}, {"Title": "Trustworthy multi-view clustering via alternating generative adversarial representation learning and fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "107", "Issue": "", "Page": "102323", "JournalTitle": "Information Fusion"}, {"Title": "Evidential dissonance measure in robust multi-view classification to resist adversarial attack", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "113", "Issue": "", "Page": "102605", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 142807716, "Title": "Optimizing PID Controller Design for Rotor Systems Suspended by Active Magnetic Bearings", "Abstract": "", "Keywords": "", "DOI": "10.4173/mic.2024.3.3", "PubYear": 2024, "Volume": "45", "Issue": "3", "JournalId": 22211, "JournalTitle": "Modeling, Identification and Control: A Norwegian Research Bulletin", "ISSN": "0332-7353", "EISSN": "1890-1328", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Atte Putkonen", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142807724, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1002/aaai.12175", "PubYear": 2025, "Volume": "46", "Issue": "1", "JournalId": 18861, "JournalTitle": "AI Magazine", "ISSN": "0738-4602", "EISSN": "0738-4602", "Authors": [], "References": []}, {"ArticleId": 142807791, "Title": "EMD empowered neural network for predicting spatio-temporal non-stationary channel in UAV communications", "Abstract": "<p>This paper introduces a novel prediction method for spatio-temporal non-stationary channels between unmanned aerial vehicles (UAVs) and ground control vehicles, essential for the fast and accurate acquisition of channel state information (CSI) to support UAV applications in ultra-reliable and low-latency communication (URLLC). Specifically, an empirical mode decomposition (EMD)-empowered spatio-temporal attention neural network is proposed, referred to as EMD-STANN. The STANN sub-module within EMD-STANN is designed to capture the spatial correlation and temporal dependence of CSI. Furthermore, the EMD component is employed to handle the non-stationary and nonlinear dynamic characteristics of the UAV-to-ground control vehicle (U2V) channel, thereby enhancing the feature extraction and refinement capabilities of the STANN and improving the accuracy of CSI prediction. Additionally, we conducted a validation of the proposed EMD-STANN model across multiple datasets. The results indicated that EMD-STANN is capable of effectively adapting to diverse channel conditions and accurately predicting channel states. Compared to existing methods, EMD-STANN exhibited superior predictive performance, as indicated by its reduced root mean square error (RMSE) and mean absolute error (MAE) metrics. Specifically, EMD-STANN achieved a reduction of 24.66% in RMSE and 25.46% in MAE compared to the reference method under our simulation conditions. This improvement in prediction accuracy provides a solid foundation for the implementation of URLLC applications.</p>", "Keywords": "Neural network; EMD; Channel prediction; UAV; Non-stationary", "DOI": "10.1007/s10489-024-06165-8", "PubYear": 2025, "Volume": "55", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Southwest University of Science and Technology, Mianyang, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Southwest University of Science and Technology, Mianyang, China"}, {"AuthorId": 3, "Name": "Hong Jiang", "Affiliation": "School of Information Engineering, Southwest University of Science and Technology, Mianyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Pretoria, Pretoria, South Africa"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Southwest University of Science and Technology, Mianyang, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Southwest University of Science and Technology, Mianyang, China"}], "References": [{"Title": "Deep Learning based Wireless Channel Prediction: 5G Scenario", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "2626", "JournalTitle": "Procedia Computer Science"}, {"Title": "A non-stationary channel prediction method for UAV communication network with error compensation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106206", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 142807799, "Title": "Effect of critical process parameters on the impact properties of thick-walled PA66GF30 composites obtained by microcellular injection molding (MIM)", "Abstract": "<p>One noticeable trend in the modification of structural materials is the reduction of their density and the substitution of steel and aluminum alloys with lightweight composite materials. Microcellular injection molding (MIM) allows obtaining molded parts with reduced density while maintaining the expected mechanical properties. In the paper, the influence of key MIM process parameters on the impact strength of PA66GF30 composites with continuous measurement of force on a pendulum was investigated. Additionally, the obtained results were referred to structural analyses. Thick-walled moldings with a finely porous structure, free from surface defects, were obtained. It was found that the uniform distribution of small pores with an average size ranging from 15 to 25 µm had a positive effect on the decrease of the maximum force required to destroy the sample by about 20% and the increase of impact strength by 8.6% in relation to solid, thick-walled elements made of PA66GF30. These effects and the significant influence of injection rate on the impact strength of porous PA66GF30 moldings were also confirmed using one-way ANOVA.</p>", "Keywords": "Microcellular injection molding; Impact strength; Polyamide composites; Microporous structure; Lightweight material", "DOI": "10.1007/s00170-024-14910-z", "PubYear": 2025, "Volume": "136", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Manufacturing Techniques, Faculty of Mechanical Engineering, Bydgoszcz University of Science and Technology, Bydgoszcz, Poland; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Manufacturing Techniques, Faculty of Mechanical Engineering, Bydgoszcz University of Science and Technology, Bydgoszcz, Poland"}], "References": []}, {"ArticleId": 142807813, "Title": "A deep residual sequence autoencoder for future state estimation and aiding prognostics and diagnostics in machines: a case study of mechanical rolling elements", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-024-10756-4", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Perkgoz Cahit", "Affiliation": ""}], "References": [{"Title": "Improved inception-residual convolutional neural network for object recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "279", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Fault prognostics by an ensemble of Echo State Networks in presence of event based measurements", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103346", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Potential, challenges and future directions for deep learning in prognostics and health management applications", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103678", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "IDP-Seq2Seq: identification of intrinsically disordered regions based on sequence to sequence learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "21", "Page": "5177", "JournalTitle": "Bioinformatics"}, {"Title": "Towards multi-model approaches to predictive maintenance: A systematic literature survey on diagnostics and prognostics", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "539", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "End-to-end CNN + LSTM deep learning approach for bearing fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "2", "Page": "736", "JournalTitle": "Applied Intelligence"}, {"Title": "Remaining useful life (RUL) prediction of internal combustion engine timing belt based on vibration signals and artificial neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>at Ghobadian", "PubYear": 2021, "Volume": "33", "Issue": "13", "Page": "7785", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A survey of machine-learning techniques for condition monitoring and predictive maintenance of bearings in grinding machines", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "103380", "JournalTitle": "Computers in Industry"}, {"Title": "Condition monitoring and life prediction of the turning tool based on extreme learning machine and transfer learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>o Hu; Xiangyang Xu", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "3399", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Remaining useful life prediction in prognostics using multi-scale sequence and Long Short-Term Memory network⋆", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "57", "Issue": "", "Page": "101508", "JournalTitle": "Journal of Computational Science"}, {"Title": "Artificial intelligence in prognostics and health management of engineering systems", "Authors": "<PERSON> Ochella; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "108", "Issue": "", "Page": "104552", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Ensembles of probabilistic LSTM predictors and correctors for bearing prognostics using industrial standards", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "491", "Issue": "", "Page": "575", "JournalTitle": "Neurocomputing"}, {"Title": "A two-stage intrusion detection system with auto-encoder and LSTMs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108768", "JournalTitle": "Applied Soft Computing"}, {"Title": "VisPro: a prognostic SqueezeNet and non-stationary Gaussian process approach for remaining useful life prediction with uncertainty quantification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "17", "Page": "14683", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Classification of apple images using support vector machines and deep residual networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "16", "Page": "12073", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 142807904, "Title": "A new multivariate decomposition-ensemble approach with denoised neighborhood rough set for stock price forecasting over time-series information system", "Abstract": "<p>The uncertainty of the stock market is the foundation for investors to obtain returns. Driven by interests, stock price forecasting has become a research hotspot. However, as the high latitude, highly volatile, and noisy, forecasting the stock prices has become a highly challenging task. The existing stock price forecasting methods only study low latitude data, which is unable to reflect the cumulative effect of multiple factors on stock price. To effectively address the high latitude, high volatility, and noise of stock price, a time-series information system (TSIS) forecasting approach for stock price is proposed. Aiming at dynamically depicting the real-world decision-making scenarios from a finer granularity, the TSIS is constructed based on the information systems. Then, a denoised neighborhood rough set (DNRS) model based on the TSIS is proposed by local density factor to achieve the purpose of feature selection, which can weaken the impact of noise on sample data. Subsequently, the multivariate empirical mode decomposition (MEMD) and multivariate kernel extreme learning machine (MKELM) are employed to decompose and forecast. Finally, the proposed TSIS forecasting approach is applied to stock price. Experimental results show that the TSIS forecasting approach for stock price has excellent performance and can be provided in the quantitative trading of stock market.</p>", "Keywords": "Neighborhood rough set; Multivariate empirical mode decomposition; Time-series information system; Stock price forecasting", "DOI": "10.1007/s10489-024-06070-0", "PubYear": 2025, "Volume": "55", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Juncheng Bai", "Affiliation": "School of Economics and Management, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "Bingzhen Sun", "Affiliation": "School of Economics and Management, Xidian University, Xi’an, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Xidian University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Dampness Syndrome of Chinese Medicine, The Second Affiliated Hospital of Guangzhou University of Chinese Medicine, Guangzhou, China; Corresponding author."}], "References": [{"Title": "A multilevel neighborhood sequential decision approach of three-way granular computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "538", "Issue": "", "Page": "119", "JournalTitle": "Information Sciences"}, {"Title": "A novel fuzzy rough set model with fuzzy neighborhood operators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "266", "JournalTitle": "Information Sciences"}, {"Title": "An investigation on Wu-Leung multi-scale information systems and multi-expert group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "114542", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel approach to attribute reduction based on weighted neighborhood rough sets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "220", "Issue": "", "Page": "106908", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Attribute reduction methods in fuzzy rough set theory: An overview, comparative experiments, and new directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107353", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel hybrid feature selection method considering feature interaction in neighborhood rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107167", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A hybrid approach of adaptive wavelet transform, long short-term memory and ARIMA-GARCH family models for the stock index prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115149", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A three-way decision method based on fuzzy rough set models under incomplete environments", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Bingzhen Sun", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "22", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic updating approximations of local generalized multigranulation neighborhood rough set", "Authors": "Weihua Xu; Ke<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "8", "Page": "9148", "JournalTitle": "Applied Intelligence"}, {"Title": "A noise-aware fuzzy rough set approach for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109092", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-step-ahead stock price index forecasting using long short-term memory model with multivariate empirical mode decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "297", "JournalTitle": "Information Sciences"}, {"Title": "Forecasting the realized volatility of stock price index: A hybrid model integrating CEEMDAN and LSTM", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117736", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel probabilistic hesitant fuzzy rough set based multi-criteria decision-making method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "489", "JournalTitle": "Information Sciences"}, {"Title": "Forecasting stock volatility and value-at-risk based on temporal convolutional networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "117951", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fuzzy time series model based on red–black trees for stock index forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "109323", "JournalTitle": "Applied Soft Computing"}, {"Title": "Forecasting turning points in stock price by applying a novel hybrid CNN-LSTM-ResNet model fed by 2D segmented images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105464", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A prospect-regret theory-based three-way decision model with intuitionistic fuzzy numbers under incomplete multi-scale decision information systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "119144", "JournalTitle": "Expert Systems with Applications"}, {"Title": "3WC-D: A feature distribution-based adaptive three-way clustering method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "12", "Page": "15561", "JournalTitle": "Applied Intelligence"}, {"Title": "A novel multi-label feature selection method with association rules and rough set", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "299", "JournalTitle": "Information Sciences"}, {"Title": "Essential tensor learning for multimodal information-driven stock movement prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "262", "Issue": "", "Page": "110262", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Feature Selection for Unbalanced Distribution Hybrid Data Based on ${k}$-Nearest Neighborhood Rough Set", "Authors": "<PERSON><PERSON> Xu; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "1", "Page": "229", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Intelligent forecasting model of stock price using neighborhood rough set and multivariate empirical mode decomposition", "Authors": "Juncheng Bai; <PERSON><PERSON><PERSON>; Bingzhen Sun", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106106", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Information granules-based long-term forecasting of time series via BPNN under three-way decision framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "634", "Issue": "", "Page": "696", "JournalTitle": "Information Sciences"}, {"Title": "Stock index forecasting based on multivariate empirical mode decomposition and temporal convolutional networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110356", "JournalTitle": "Applied Soft Computing"}, {"Title": "Regret theory-based multivariate fusion prediction system and its application to interest rate estimation in multi-scale information systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101860", "JournalTitle": "Information Fusion"}, {"Title": "MAP-FCRNN: Multi-step ahead prediction model using forecasting correction and RNN model with memory functions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "646", "Issue": "", "Page": "119382", "JournalTitle": "Information Sciences"}, {"Title": "Bridging the gap between AI and the industry — A study on bearing fault detection in PMSM-driven systems using CNN and inverter measurement", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "106834", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Matrix-based feature selection approach using conditional entropy for ordered data set with time-evolving features", "Authors": "<PERSON><PERSON> Xu; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "279", "Issue": "", "Page": "110947", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Shared neighbors rough set model and neighborhood classifiers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "244", "Issue": "", "Page": "122965", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Shared neighbors rough set model and neighborhood classifiers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "244", "Issue": "", "Page": "122965", "JournalTitle": "Expert Systems with Applications"}, {"Title": "MBSSA-Bi-AESN: Classification prediction of bi-directional adaptive echo state network based on modified binary salp swarm algorithm and feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "2", "Page": "1706", "JournalTitle": "Applied Intelligence"}, {"Title": "MBSSA-Bi-AESN: Classification prediction of bi-directional adaptive echo state network based on modified binary salp swarm algorithm and feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "2", "Page": "1706", "JournalTitle": "Applied Intelligence"}, {"Title": "A local rough set method for feature selection by variable precision composite measure", "Authors": "Ke<PERSON> Yuan; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "155", "Issue": "", "Page": "111450", "JournalTitle": "Applied Soft Computing"}, {"Title": "GA-FCFNN: A new forecasting method combining feature selection methods and feedforward neural networks using genetic algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "669", "Issue": "", "Page": "120566", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 142807916, "Title": "Microcomputer Controlled Color Identification Robot Arm", "Abstract": "", "Keywords": "", "DOI": "10.9790/0661-2606024157", "PubYear": 2024, "Volume": "26", "Issue": "6", "JournalId": 10445, "JournalTitle": "IOSR Journal of Computer Engineering", "ISSN": "2278-8727", "EISSN": "2278-0661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>, Ikechukwu Kingsely", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>wogbaga, Nweso <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Ituma, Chinagolum", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>, <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142808005, "Title": "Seru scheduling problem with lot streaming and worker transfers: A multi-objective approach", "Abstract": "Seru production system (SPS) offers the flexibility of job shop production environments with the efficiency of traditional assembly lines. The SPSs are particularly attractive to industries characterized by high product variety and micro production volumes, and effective utilization of production and workforce resources is a critical challenge for SPSs. This paper addresses the seru scheduling problem with lot streaming and worker transfers for a SPS using a multi-objective approach. To this end, first, a multi-objective mixed-integer linear programming (MILP) model is developed for the minimization of makespan, average flow time, and maximum workload imbalance. Six different algorithms based on non-dominating sorting genetic algorithm II (NSGA-II) are developed , each corresponding to an operational setting dictated by the lot streaming and worker transfers strategies in effect. A design of experiment (DoE) framework is utilized to generate realistic problem instances based on the several controllable factors and their levels. Analysis of comprehensive computational results demonstrates the effectiveness of the proposed algorithm (NS2) in finding high-quality and diversified solutions by simultaneous utilization of lot streaming with variable-sized sublots and worker transfers. The results indicate that the performance improvement achieved by the NS2 ranges between 10% and 20% compared to other algorithms. Furthermore, Analysis of Variance (ANOVA) confirms the significance of the number of workers and number of serus as critical parameters for the design or redesign of SPSs. Drawing on these findings, managerial insights are provided regarding the impact of lot streaming and worker transfers on SPS performance. This study offers practical and theoretical insights for decision-makers seeking to enhance SPS performance and bridge the gap between the conceptual analysis and practical implementation of SPSs.", "Keywords": "", "DOI": "10.1016/j.cor.2024.106967", "PubYear": 2025, "Volume": "177", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Istanbul Technical University, Department of Industrial Engineering, 34469 Istanbul, Turkey;University of Florida, Supply Chain and Logistics Engineering Laboratory, Department of Industrial and Systems Engineering, Gainesville, FL, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>mer Faruk Yılmaz", "Affiliation": "Karadeniz Technical University, Department of Industrial Engineering, 61080 Trabzon, Turkey;University of Florida, Center for Applied Optimization, Department of Industrial and Systems Engineering, Gainesville, FL, USA"}, {"AuthorId": 3, "Name": "<PERSON>f <PERSON>", "Affiliation": "University of Florida, Supply Chain and Logistics Engineering Laboratory, Department of Industrial and Systems Engineering, Gainesville, FL, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Istanbul Technical University, Department of Industrial Engineering, 34469 Istanbul, Turkey"}], "References": [{"Title": "An integrated bi-objective U-shaped assembly line balancing and parts feeding problem: optimization model and exact solution method", "Authors": "<PERSON>mer Faruk Yılmaz", "PubYear": 2022, "Volume": "90", "Issue": "7-9", "Page": "679", "JournalTitle": "Annals of Mathematics and Artificial Intelligence"}]}, {"ArticleId": 142808115, "Title": "Superconvergence of triquadratic finite elements for the second-order elliptic equation with variable coefficients", "Abstract": "This study focuses on superconvergence of the tensor-product quadratic finite element (so-called triquadratic finite element) in a regular family of rectangular partitions of the domain for the second-order elliptic equation with variable coefficients in three dimensions. In this paper, we first introduce a variable coefficients elliptic boundary value problem and its finite elements discretization, as well as some important functions such as the discrete Green's function and discrete derivative <PERSON>'s function. Then, an interpolation operator of project type is given, by which we derive a interpolation fundamental estimate (so-called weak estimate) for general variable coefficients elliptic equations. Finally, combining the weak estimate and estimates for the discrete Green's function and discrete derivative <PERSON>'s function, we get superconvergence estimates for derivatives and function values of the finite element approximation in the pointwise sense of the L ∞ -norm.", "Keywords": "", "DOI": "10.1016/j.camwa.2025.01.001", "PubYear": 2025, "Volume": "181", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Guangdong University of Petrochemical Technology, Maoming 525000, China"}], "References": []}, {"ArticleId": 142808136, "Title": "Prioritizing Circular Economy actions for the decarbonization of manufacturing companies: The c-readiness tool", "Abstract": "Climate change is pushing manufacturing companies to adopt sustainable solutions for reducing their carbon emissions. Circular Economy emerged as a suitable strategy for the decarbonization of industrial organizations, offering the potential to decouple economic growth from natural resource extraction and waste generation. However, achieving circularity requires significant transformation in several areas, and some Circular Economy actions may be more effective than others in reducing carbon emissions, depending on the product and company carbon footprint structure. Facing low awareness and limited resources, manufacturing companies frequently fail in understanding where to start in approaching such a systemic transition. Despite these challenges, the literature overlooks the linkages between Circular Economy initiatives and their potential for reducing carbon emissions, and in particular how Circular Economy actions can be prioritized for decarbonization purposes. To fill these gaps, this paper develops an original and systemic tool (C-Readiness) for assessing the readiness of manufacturing companies for the Circular Economy, and for prioritizing Circular Economy actions for decarbonization. The tool is developed based on a literature review and critical comparison of existing tools for assessing circularity readiness at the micro level. The tool has been applied to four manufacturing companies, to showcase its potential in designing Circular Economy-based decarbonization paths. This paper contributes to the literature on strategic Circular Economy implementation in manufacturing companies by integrating circularity readiness evaluations with quantitative carbon footprint assessments. It provides a structured approach and a simple yet effective tool to help industrial organizations reduce their environmental impact through Circular Economy practices.", "Keywords": "Circular Economy; Manufacturing; Readiness assessment; Maturity model; Decarbonization; Sustainability; Net-Zero; Carbon footprint; Climate change", "DOI": "10.1016/j.cie.2025.110876", "PubYear": 2025, "Volume": "201", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Brescia, Via Branze 38, 25123 Brescia, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Brescia, Via Branze 38, 25123 Brescia, Italy"}], "References": [{"Title": "A quantitative and holistic circular economy assessment framework at the micro level", "Authors": "<PERSON><PERSON>; Efstrati<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "160", "Issue": "", "Page": "107697", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "To what extent are circular economy strategies accounted in science-based targets for carbon emission reduction?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "197", "Issue": "", "Page": "110594", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": *********, "Title": "A matheuristic approach for an integrated lot-sizing and scheduling problem with a period-based learning effect", "Abstract": "This research investigates a multi-product capacitated lot-sizing and scheduling problem incorporating a novel learning effect, namely the period-based learning effect. This is inspired by a real case in a core analysis laboratory under a job shop setting. Accordingly, a Mixed-Integer Linear Programming (MILP) model is extended based on the big-bucket formulation, optimizing the total tardiness and overtime costs. Given the complexity of the problem, a cutting plane method is employed to simplify the model. Afterward, three matheuristic methods based on the rolling horizon approach are devised, incorporating two lower bounds and a local search heuristic. Furthermore, a post-processing approach is implemented to incorporate lot-streaming possibility. Computational experiments demonstrate: 1) the simplified model performs effectively in terms of both solution quality and computational time; and 2) although the model encounters challenges with large-scale instances, the proposed matheuristic methods achieve satisfactory outcomes; and 3) it can be inferred that the complexity of the models and solution methods are independent of the learning effect; however, the value of learning effect may impact the performance of the lower bounds; 4) in manufacturing settings, where the lot-streaming is possible, incorporating post-processing can drastically improve the objective function; 5) the impact of the period-based learning effect in the results is significant, and the model’s sensitivity to time-based parameters (e.g., learning rate) is more than cost-based ones (e.g., tardiness cost).", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126234", "PubYear": 2025, "Volume": "269", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Czech Institute of Informatics, Robotics, and Cybernetics, Czech Technical University in Prague, Prague, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Engineering, College of Engineering, University of Tehran, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>Moghaddam", "Affiliation": "School of Industrial Engineering, College of Engineering, University of Tehran, Tehran, Iran;Research Center of Performance and Productivity Analysis, Istinye University, Istanbul, Turkey"}, {"AuthorId": 4, "Name": "Zdeněk Hanzálek", "Affiliation": "Czech Institute of Informatics, Robotics, and Cybernetics, Czech Technical University in Prague, Prague, Czech Republic;Corresponding author"}], "References": [{"Title": "Effective algorithms for single-machine learning-effect scheduling to minimize completion-time-based criteria with release dates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "156", "Issue": "", "Page": "113445", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An effective multi-start iterated greedy algorithm to minimize makespan for the distributed permutation flowshop scheduling problem with preventive maintenance", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114495", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A combinatorial evolutionary algorithm for unrelated parallel machine scheduling problem with sequence and machine-dependent setup times, limited worker resources and learning effect", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "175", "Issue": "", "Page": "114843", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Research on flexible job-shop scheduling problem in green sustainable manufacturing based on learning effect", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "6", "Page": "1725", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Minimization of maximum lateness in a flowshop learning effect scheduling with release dates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107309", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Solving a parallel-line capacitated lot-sizing and scheduling problem with sequence-dependent setup time/cost and preventive maintenance by a rolling horizon method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "108041", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A two-level lot sizing and scheduling problem applied to a cosmetic industry", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107837", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Multi-agent reinforcement learning based on graph convolutional network for flexible job shop scheduling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "1", "Page": "75", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Integrated lot-sizing and scheduling: Mitigation of uncertainty in demand and processing time by machine learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Zdeněk Hanzálek", "PubYear": 2023, "Volume": "118", "Issue": "", "Page": "105676", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A multi-objective optimization algorithm for flow shop group scheduling problem with sequence dependent setup time and worker learning", "Authors": "Djazia <PERSON>; Fayçal Belkaid", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "120878", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Matheuristic for the lot-sizing and scheduling problem in integrated pulp and paper production", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "192", "Issue": "", "Page": "110183", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Flexible job-shop scheduling problem with variable lot-sizing: An early release policy-based matheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "193", "Issue": "", "Page": "110290", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 142808165, "Title": "Numerical Simulation of Thermal Counterflow in Superfluid Helium: Investigating the Effect of Rotation and Heat Flux on the Surface of the Cylinder", "Abstract": "This study focuses on the numerical simulation of the thermal counterflow of helium superfluid around a cylinder. To model helium superfluid behavior, two-fluid equations, incorporating the <PERSON><PERSON>-<PERSON><PERSON> mutual friction, were employed. The simulation utilized the PIMPLE (Pressure Implicit with Splitting of Operator) algorithm, which couples the velocities of the normal and superfluid components with pressure, thereby enhancing numerical stability. This approach made it possible to simulate the thermal counterflow around the rotating cylinder and heat flux on its surface, including both heating and cooling effects. The research aims to explore the impact of rotation and heat flux on separation angles, drag and lift forces, and in principle, the overall pattern of the flow. The primary objective is to gain a more profound insight into the behavior of superfluid helium and optimize its applications in both research and industrial contexts. The findings reveal that, in contrast to classical fluids, the influences of various factors do not adhere to a consistent rule. Rotation and cooling/heating were observed to significantly affect separation and the aerodynamic forces. However, the nature of this impact can vary across different scenarios. In some cases, rotation increases the separation angle, while in others, it completely eliminates separation. Consequently, the effect of rotation on the drag force coefficient exhibits substantial variation depending on the specific problem at hand. For instance, in one problem, the drag force coefficient increases from approximately 0.7 to about 1.4 due to rotation, whereas in another, it decreases from around 0.8 to approximately 0.1. Additionally, rotation leads to a drag force coefficient of approximately 1.3 in one specific scenario. Furthermore, this study has demonstrated that the rotation of the cylinder induces asymmetry in the mass flow rates of the components. For instance, in one case, the cylinder's rotation resulted in approximately 86 % of the superfluid component passing from one side of the cylinder. Overall, cooling tends to reduce the separation angle and drag force coefficient, while heating has the opposite effect and increases them. For example, in one scenario, cooling causes the drag force coefficient to drop from 0.8 to about 0.1, whereas heating elevates above 1.6.", "Keywords": "", "DOI": "10.1016/j.cpc.2024.109495", "PubYear": 2025, "Volume": "310", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Excellence in Energy Conversion (CEEC), Mechanical Engineering Department , Sharif University of Technology , Tehran , Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center of Excellence in Energy Conversion (CEEC), Mechanical Engineering Department , Sharif University of Technology , Tehran , Iran;Corresponding author"}], "References": []}, {"ArticleId": 142808181, "Title": "Effects of an occupational soft-back exoskeleton during order picking: a field study in logistics", "Abstract": "<p>The use of exoskeletons is increasingly considered as a solution to reduce workers' exposure to physical risk factors, such as low-back disorders. The aim of this study was to evaluate the effects of the CORFOR<sup>®</sup> occupational soft-back exoskeleton on trunk muscle activity and kinematics during an order picking manual task performed in the field. 10 workers, with at least 4 weeks' experience using the exoskeleton, performed a 1.5-hour order picking task with and without the exoskeleton. Trunk muscle activity, upper-body kinematics and the exoskeleton's acceptance were assessed. <i>Erector spinae</i> muscle activity was significantly reduced by 7.5% with the use of the exoskeleton. Moreover, trunk flexor muscles activity, trunk kinematics, or low-back pain were not affected. Further, the acceptance of the exoskeleton was rated as favourable. Thus, at least in the test company, the integration of the CORFOR<sup>®</sup> exoskeleton for order picking tasks is promising.</p>", "Keywords": "EMG;Ergonomics;biomechanics;exoskeleton", "DOI": "10.1080/00140139.2024.2447867", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DevAH, Université de Lorraine, Nancy, France;Lidl France, Châtenay-Malabry, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Lidl France, Châtenay-Malabry, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DevAH, Université de Lorraine, Nancy, France;Faculty of Sport Sciences, Université de Lorraine, Nancy, France"}], "References": [{"Title": "Effects of industrial back-support exoskeletons on body loading and user experience: an updated systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "6", "Page": "685", "JournalTitle": "Ergonomics"}, {"Title": "Occupational exoskeletons: A roadmap toward large-scale adoption. Methodology and challenges of bringing exoskeletons to workplaces", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "e11", "JournalTitle": "Wearable Technologies"}, {"Title": "Effects of back-support exoskeletons with different functional mechanisms on trunk muscle activity and kinematics", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "", "Page": "e12", "JournalTitle": "Wearable Technologies"}, {"Title": "The Adoption of Occupational Exoskeletons: From Acceptability to Situated Acceptance, Questionnaire Surveys", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2025, "Volume": "41", "Issue": "2", "Page": "1446", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 142808327, "Title": "Encouraging Sustainable Choices Through Socially Engaged Persuasive Recycling Initiatives: A Participatory Action Design Research Study", "Abstract": "<p>Human-Computer Interaction (HCI) research has illuminated how technology can influence users’ awareness of their environmental impact and the potential for mitigating these impacts. From hot water saving to food waste reduction, researchers have systematically and widely tried to find pathways to speed up achieving sustainable development goals through persuasive technology interventions. However, motivating users to adopt sustainable behaviors through interactive technologies presents significant psychological, cultural, and technical challenges in creating engaging and long-lasting experiences. Aligned with this perspective, there is a dearth of research and design solutions addressing the use of persuasive technology to promote sustainable recycling behavior. Guided by a participatory design approach, this investigation focuses on the design opportunities for leveraging persuasive and human-centered Internet of Things (IoT) applications to enhance user engagement in recycling activities. The assumption is that one pathway to achieve this goal is to adopt persuasive strategies that may be incorporated into the design of sustainable applications. The insights gained from this process can then be applied to various sustainable HCI scenarios and therefore contribute to HCI’s limited understanding in this area by providing a series of design-oriented research recommendations for informing the development of persuasive and socially engaged recycling platforms. In particular, we advocate for the inclusion of educational content, real-time interactive feedback, and intuitive interfaces to actively engage users in recycling activities. Moreover, recognizing the cultural context in which the technology is socially situated becomes imperative for the effective implementation of smart devices to foster sustainable recycling practices. To this end, we present a case study that seeks to involve children and adolescents in pro-recycling activities within the school environment.</p>", "Keywords": "", "DOI": "10.3390/informatics12010005", "PubYear": 2025, "Volume": "12", "Issue": "1", "JournalId": 40001, "JournalTitle": "Informatics", "ISSN": "", "EISSN": "2227-9709", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Postgraduate Program in Informatics, Federal University of Rio de Janeiro, Rio de Janeiro 21941-916, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Postgraduate Program in Informatics, Federal University of Rio de Janeiro, Rio de Janeiro 21941-916, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Systems Engineering and Computer Science Program, Federal University of Rio de Janeiro, Rio de Janeiro 21941-972, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, University of Jyväskylä, P.O. Box 35, FI-40014 Jyväskylä, Finland"}], "References": [{"Title": "Ethnography, CSCW and Ethnomethodology", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "2", "Page": "189", "JournalTitle": "Computer Supported Cooperative Work (CSCW)"}, {"Title": "The Flaky Accretions of Infrastructure: Sociotechnical Systems, Citizenship, and the Water Supply", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Persuasive Apps for Sustainable Waste Management: A Comparative Systematic Evaluation of Behavior Change Strategies and State-of-the-Art", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "167", "JournalTitle": "Frontiers in Artificial Intelligence"}]}, {"ArticleId": 142808343, "Title": "Network-based intrusion detection: a comparative analysis of machine learning approaches for improved security", "Abstract": "", "Keywords": "", "DOI": "10.1080/23742917.2024.2447119", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 16753, "JournalTitle": "Journal of Cyber Security Technology", "ISSN": "2374-2917", "EISSN": "2374-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Central University of Kashmir, Ganderbal, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Instrumentation Technology, University of Kashmir, Srinagar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Srinagar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Central University of Kashmir, Ganderbal, India;Department of Electronics and Instrumentation Technology, University of Kashmir, Srinagar, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Central University of Kashmir, Ganderbal, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Technology, University of Kashmir, Srinagar, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Srinagar, India"}], "References": [{"Title": "Deep learning for cyber security intrusion detection: Approaches, datasets, and comparative study", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "", "Page": "102419", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Deep learning methods in network intrusion detection: A survey and an objective comparison", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "169", "Issue": "", "Page": "102767", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Designing a Network Intrusion Detection System Based on Machine Learning for Software Defined Networks", "Authors": "<PERSON><PERSON><PERSON>; Mohammed J<PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "5", "Page": "111", "JournalTitle": "Future Internet"}, {"Title": "A Novel Deep Learning-Based Intrusion Detection System for IoT Networks", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "2", "Page": "34", "JournalTitle": "Computers"}, {"Title": "Multimodal imputation-based stacked ensemble for prediction and classification of air quality index in Indian cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "114", "Issue": "", "Page": "109098", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Multimodal imputation-based stacked ensemble for prediction and classification of air quality index in Indian cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "114", "Issue": "", "Page": "109098", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 142808460, "Title": "Supervised contrastive learning enhances MHC-II peptide binding affinity prediction", "Abstract": "Accurate prediction of major histocompatibility complex (MHC)-peptide binding affinity can improve our understanding of cellular immune responses and guide personalized immunotherapies. Nevertheless, the existing deep learning-based approaches for predicting MHC-II peptide interactions fall short of satisfactory performance and offer restricted model interpretability. In this study, we propose a novel deep neural network, termed ConBoTNet, to address the above issues by introducing the designed supervised contrastive learning and bottleneck transformer extractors. Specifically, the supervised contrastive learning pre-training enhances the model’s representative and generalizable capabilities on MHC-II peptides by pulling positive pairs closer and pushing negative pairs further in the feature space, while the bottleneck transformer module focuses on MHC-II peptide interactions to precisely identify binding cores and anchor positions in an unsupervised manner. Extensive experiments on benchmark datasets under 5-fold cross-validation, leave-one-molecule-out validation, independent testing, T-cell epitope identification, and binding core prediction settings highlighted the superiority of our proposed ConBoTNet over current state-of-the-art methods. Data distribution analysis in the latent feature space demonstrated that supervised contrastive learning can aggregate MHC-II-peptide samples with similar affinity labels and learn common features of similar affinity. Additionally, we interpreted the trained neural network by associating the attention weights with peptides and innovatively find both well-established and potential peptide motifs. This work not only introduces an innovative tool for accurately predicting MHC-II peptide affinity, but also provides new insights into a new paradigm for modeling essential biological interactions, advancing data-driven discovery in biomedicine.", "Keywords": "", "DOI": "10.1016/j.eswa.2025.126463", "PubYear": 2025, "Volume": "269", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, 200 Xiaolingwei, Nanjing 210094 China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Yangzhou University, Yangzhou 225100 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Enginnering, Jingdezhen Ceramic University, Jingdezhen 333403 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Monash Biomedicine Discovery Institute and Department of Biochemistry and Molecular Biology, Monash University, Melbourne, VIC 3800, Australia;Monash Data Futures Institute, Monash University, Melbourne, VIC 3800, Australia;School of Life Sciences and Biotechnology, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Monash Biomedicine Discovery Institute and Department of Biochemistry and Molecular Biology, Monash University, Melbourne, VIC 3800, Australia;Monash Data Futures Institute, Monash University, Melbourne, VIC 3800, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Public Health and Preventive Medicine, Monash University, Melbourne, Victoria, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Monash Biomedicine Discovery Institute and Department of Biochemistry and Molecular Biology, Monash University, Melbourne, VIC 3800, Australia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Monash Biomedicine Discovery Institute and Department of Biochemistry and Molecular Biology, Monash University, Melbourne, VIC 3800, Australia;Monash Data Futures Institute, Monash University, Melbourne, VIC 3800, Australia;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, 200 Xiaolingwei, Nanjing 210094 China;Corresponding authors"}], "References": [{"Title": "MHCAttnNet: predicting MHC-peptide bindings for MHC alleles classes I and II using an attention-based deep neural model", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "Supplement_1", "Page": "i399", "JournalTitle": "Bioinformatics"}, {"Title": "Deep learning-based prediction of the T cell receptor–antigen binding specificity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "10", "Page": "864", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "DeepMHCII: a novel binding core-aware deep interaction model for accurate MHC-II peptide binding affinity prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "Supplement_1", "Page": "i220", "JournalTitle": "Bioinformatics"}, {"Title": "Characterizing the interaction conformation between T-cell receptors and epitopes with deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Pei<PERSON> Feng", "PubYear": 2023, "Volume": "5", "Issue": "4", "Page": "395", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 142808461, "Title": "Using LLM-supported lecture summarization system to improve knowledge recall and student satisfaction", "Abstract": "Large Language Models (LLMs) are a useful summarization tool for helping students remember the main ideas and central claims of a lecture. However, due to limitations in current techniques, they are unable to deal with lecture’s complex structures, which contain multiple knowledge themes. To address this issue, we proposed an original theme-based lecture summary (TLS) system that combines graph-based theme segmentation algorithms and LLMs, consisting of data preparation, theme segmentation, summary generation, and consistency evaluation stages. To assess the effectiveness of the system, an education intervention was implemented using a mixed-methods research design. Forty-six undergraduate students took part in an authentic lecture context for eight weeks. The quantitative study used a 2 × 2 factorial design, with intervention conditions including lecture type (conceptual vs. procedural) and TLS-generated summaries (with vs. without). Then, eight of the participants were chosen for a focus-group interview to learn about student satisfaction. The study finds that: 1) TLS-generated summaries have a significant positive influence on knowledge recall; 2) lecture type has no effect on the intervention outcomes of TLS-generated summaries, regardless of whether it is conceptually or procedurally oriented; and 3) TLS-generated summaries can increase student satisfaction. The current study broadens the use of LLMs in authentic educational settings to promote student success.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126371", "PubYear": 2025, "Volume": "269", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Education, Southwest University, Chongqing 400715 China;Corresponding author at: Tianshen Road 2, Beibei District, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Education, Southwest University, Chongqing 400715 China;<PERSON><PERSON> is now a student. The university did not provide an institutional e-mail for her"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Education, Southwest University, Chongqing 400715 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Education, Southwest University, Chongqing 400715 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing 401331 China"}], "References": [{"Title": "Automatic text summarization: A comprehensive survey", "Authors": "Waf<PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113679", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Automatic Text Summarization Methods: A Comprehensive Review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Recent Advances in Natural Language Processing via Large Pre-trained Language Models: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Scaffolding Computational Thinking with ChatGPT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "", "Page": "1628", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": 142808465, "Title": "The integration of shared renewable resources considering setup times for the parallel machine scheduling problem", "Abstract": "With the advent of Industry 5.0, the rise of advanced technologies, and the fast deployment of human–machine collaborative systems, there is a need for novel approaches to optimizing resources and increasing overall production efficiency. In this regard, we investigate a parallel machine scheduling problem (PMS) where various renewable resources switch among unrelated parallel machines during the operational plan. Each machine can be equipped with a specified number of resources, one at a time, on its left and right sides. We propose a mathematical model for the renewable-resource-constrained parallel machine scheduling problem that minimizes total setup times. We further incorporate realistic features such as non-simultaneous start times of machines, machine eligibility, and due date constraints for jobs. We propose a hybrid meta-heuristic algorithm to solve large instances by employing a novel constructive heuristic and the simulated annealing algorithm. Several instances are tested to validate the solution approach and underline its efficiency for large-sized ones. Through the case of a wiring harness manufacturer, we provide managerial insights on the benefit of either increasing the number of sharing renewable resources or increasing the number of machines in PMS problems, which can reduce total setup times by up to 19%.", "Keywords": "", "DOI": "10.1016/j.cie.2024.110828", "PubYear": 2025, "Volume": "200", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Yazd University, Yazd, Iran;R&D Department, Noavar Ertebat Sanat Company (NESCO), Qazvin, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Centre of Excellence in Supply Chain and Transportation (CESIT), Kedge Business School, France;Corresponding author.;Present address: 40 Av. des Terroirs de, France, 75012 Paris.;Permanent address: 680 Cr de la Libération, 33405 Talence"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Centre of Excellence in Supply Chain and Transportation (CESIT), Kedge Business School, France"}], "References": [{"Title": "Estimation of machine setup and changeover times by survival analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "107026", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Unrelated parallel machine scheduling with new criteria: Complexity and models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "132", "Issue": "", "Page": "105291", "JournalTitle": "Computers & Operations Research"}, {"Title": "Exact and metaheuristic approaches for unrelated parallel machine scheduling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "5", "Page": "507", "JournalTitle": "Journal of Scheduling"}, {"Title": "Hybrid tabu search algorithm for unrelated parallel machine scheduling in semiconductor fabs with setup times, job release, and expired times", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "165", "Issue": "", "Page": "107915", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Exact and heuristic algorithms for the parallel machine total completion time scheduling problem with dual resources, ready times, and sequence-dependent setup times", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "143", "Issue": "", "Page": "105787", "JournalTitle": "Computers & Operations Research"}, {"Title": "A hybrid genetic algorithm for parallel machine scheduling with setup times: A comparative study of metaheuristics on large problem instances", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "7", "Page": "2059", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Unrelated parallel machine scheduling with eligibility constraints and delivery times to minimize total weighted tardiness", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "149", "Issue": "", "Page": "105999", "JournalTitle": "Computers & Operations Research"}, {"Title": "Unrelated parallel machines scheduling with dependent setup times in textile industry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "174", "Issue": "", "Page": "108736", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An improved mixed-integer programming approach for bi-objective parallel machine scheduling and location", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "174", "Issue": "", "Page": "108813", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 142808710, "Title": "Camouflaged object detection via boundary refinement", "Abstract": "<p>In camouflaged object detection (COD), wholly and accurately segmenting the foreground from the background is a major focus of research. However, the similarity in color and texture between foreground targets and the background makes it difficult to distinguish them. Despite numerous deep learning networks utilizing different approaches for camouflaged object detection, precisely identifying object contours and enhancing the model’s resistance to interference from similar backgrounds remains a challenging problem. To address this issue, this paper proposes a camouflaged object detection network named boundary refinement network (BRNet) that achieves a fine-grained description of object contours by utilizing boundary semantic information constraints. Firstly, the multi-level asymmetric convolution module (MACM) is designed to enhance feature representation within the backbone architecture via a sequence of asymmetric convolutions and cross-layer connections. Additionally, the Boundary Constraint Guided Module (BCGM) is proposed to impose constraints on foreground shape and refine constrained foreground contours. Lastly, we introduce the Boundary Fusion Extraction Module (BFEM), which enables interaction between boundaries and objects in an additional dimension, leading to the generation of prediction results. Extensive quantitative and qualitative experiments conducted on three datasets demonstrate that BRNet performs well on the camouflaged object detection task, achieving superior results compared to 21 state-of-the-art approaches.</p>", "Keywords": "Camouflaged object detection; Boundary constraint; Boundary refinement; 3D convolution", "DOI": "10.1007/s00530-024-01662-9", "PubYear": 2025, "Volume": "31", "Issue": "1", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China"}, {"AuthorId": 2, "Name": "Chen<PERSON> Shen", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Henan University, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Eurasia International School of Henan University, Kaifeng, China; Corresponding author."}], "References": [{"Title": "Deep Learning for Generic Object Detection: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "261", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Boundary guidance network for camouflage object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "104283", "JournalTitle": "Image and Vision Computing"}, {"Title": "Fast Camouflaged Object Detection via Edge-based Reversible Re-calibration Network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108414", "JournalTitle": "Pattern Recognition"}, {"Title": "PVT v2: Improved baselines with Pyramid Vision Transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "415", "JournalTitle": "Computational Visual Media"}, {"Title": "Camouflaged object detection via Neighbor Connection and Hierarchical Information Transfer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "221", "Issue": "", "Page": "103450", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Boundary-guided context-aware network for camouflaged object detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "20", "Page": "15075", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Alternate guidance network for boundary-aware camouflaged object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "4", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Depth alignment interaction network for camouflaged object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 142808806, "Title": "Digital twin and the asset administration shell: An Analysis of the Three Types of AASs and their Feasibility for Digital Twin Engineering", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10270-024-01255-0", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "VREDI: virtual representation for a digital twin application in a work-center-level asset administration shell", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "501", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Low-code development and model-driven engineering: Two sides of the same coin?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "2", "Page": "437", "JournalTitle": "Software & Systems Modeling"}, {"Title": "Process-aware digital twin cockpit synthesis from event logs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "", "Page": "101121", "JournalTitle": "Journal of Computer Languages"}, {"Title": "A Cross-Domain Systematic Mapping Study on Software Engineering for Digital Twins", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "111361", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Asset Administration Shell in Manufacturing: Applications and Relationship with Digital Twin", "Authors": "Tasni<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "10", "Page": "2533", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Digital twin composition in smart manufacturing via Markov decision processes", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "149", "Issue": "", "Page": "103916", "JournalTitle": "Computers in Industry"}, {"Title": "Towards asset administration shell-based continuous engineering in process industries", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "71", "Issue": "8", "Page": "689", "JournalTitle": "at - Automatisierungstechnik"}, {"Title": "Quo <PERSON> modeling?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "1", "Page": "7", "JournalTitle": "Software & Systems Modeling"}, {"Title": "Implementing Digital Twin and Asset Administration Shell Models for a Simulated Sorting Production System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "2", "Page": "11880", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Preface to the JOT issue on 18th European Conference on Modelling Foundations and Applications (ECMFA 2022).", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "3", "Page": "1", "JournalTitle": "The Journal of Object Technology"}, {"Title": "Generating customized low-code development platforms for digital twins", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "", "Page": "101117", "JournalTitle": "Journal of Computer Languages"}]}, {"ArticleId": 142808809, "Title": "Dara: distribution-aware representation alignment for semi-supervised domain adaptation in image classification", "Abstract": "<p>Semi-supervised domain adaptation (SSDA) aims to adapt a model trained on an annotated source domain to a related, but different, target domain with limited labeled and abundant unlabeled data. However, current self-training approaches often produce incorrect pseudo-labels due to the lack of calibration. To alleviate this issue, we propose a simple yet effective approach, termed Distribution-Aware Representation Alignment (DARA), to address the SSDA problem. Our approach introduces a distribution calibration strategy that reduces spurious pseudo-labels and enhances pseudo-label quality by normalizing the current probability distribution with the holistic class mean. Based on this calibration, we apply probability-level and feature-level representation alignments to reduce domain discrepancy. The probability-level alignment merges the source domain’s ground-truth labels with corrected pseudo-labels from the target domain to supervise image mixtures. The feature-level alignment identifies matching features from both domains based on shared label predictions, enforcing consistent labels and alignment. By building the representation alignment upon distribution calibration, our approach can effectively reduce confirmation bias and domain shift, thus improving the generalization from the source domain to the target domain. Comprehensive experiments on standard SSDA benchmarks ( i.e. , Office-31, Office-Home, and DomainNet) demonstrate the superiority of DARA and the effectiveness of its components.</p>", "Keywords": "Semi-supervised domain adaptation; Distribution calibration; Representation alignment", "DOI": "10.1007/s11227-024-06886-0", "PubYear": 2025, "Volume": "81", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Hangzhou Vocational & Technical College, Hangzhou, China; College of Education Science, Ludong University, Yantai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Sciences, China Jiliang University, Hangzhou, China; Corresponding author."}, {"AuthorId": 3, "Name": "Laishui Lv", "Affiliation": "College of Information Engineering, China Jiliang University, Hangzhou, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Technology, Beijing Forestry University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "ICOSI Laboratory, Department of Computer Science and Mathematics, University of Abbes Laghrour, Khenchela, Algeria"}, {"AuthorId": 6, "Name": "Shanzhou Niu", "Affiliation": "School of Mathematics and Computer Science, Gannan Normal University, Ganzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Hangzhou Dianzi University, Hangzhou, China"}], "References": [{"Title": "Cooperative density-aware representation learning for few-shot visual recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "471", "Issue": "", "Page": "208", "JournalTitle": "Neurocomputing"}, {"Title": "An unsupervised domain adaptation model based on dual-module adversarial training", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "475", "Issue": "", "Page": "102", "JournalTitle": "Neurocomputing"}, {"Title": "BDLA: Bi-directional local alignment for few-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "1", "Page": "769", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep domain adaptation via joint transfer networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "489", "Issue": "", "Page": "441", "JournalTitle": "Neurocomputing"}, {"Title": "Unsupervised few-shot image classification via one-vs-all contrastive learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "7", "Page": "7833", "JournalTitle": "Applied Intelligence"}, {"Title": "Unsupervised Domain Adaptation via Deep Conditional Adaptation Network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109088", "JournalTitle": "Pattern Recognition"}, {"Title": "Prototype-Guided Feature Learning for Unsupervised Domain Adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109154", "JournalTitle": "Pattern Recognition"}, {"Title": "Riemannian representation learning for multi-source domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "109271", "JournalTitle": "Pattern Recognition"}, {"Title": "ICCL: Independent and Correlative Correspondence Learning for few-shot image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Laishui Lv", "PubYear": 2023, "Volume": "266", "Issue": "", "Page": "110412", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "PrintShear: Shear Input Based on Fingerprint Deformation", "Authors": "Jinyang Yu; Jianjiang Feng; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Inter-domain mixup for semi-supervised domain adaptation", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; Yizhou Yu", "PubYear": 2024, "Volume": "146", "Issue": "", "Page": "110023", "JournalTitle": "Pattern Recognition"}, {"Title": "WCAL: Weighted and center-aware adaptation learning for partial domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "130", "Issue": "", "Page": "107740", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A survey of class-imbalanced semi-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "113", "Issue": "8", "Page": "5057", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 142808810, "Title": "LogSD: log anomaly detection via topic words awareness semantic augmentation and category-guided Mixup data augmentation", "Abstract": "<p>Log anomaly detection is necessary for the reliability of enterprise systems. The existing research typically employs word vector weighted aggregation or concatenation to represent log semantics, which is easy to lose the word order relationship or ignore the influence of each word. Besides, system anomalies are contingent, which means that the number of normal logs is much greater. This imbalanced data relationship limits the performance of the deep learning model and increases the likelihood of false alarms. Therefore, we propose a log anomaly detection approach via semantic and data augmentation, named LogSD. It first designs a topic word awareness semantic representation method, which synthesizes the relative distance and semantic similarity between topic words and other words, maintains the uniqueness of word order while enriching the log semantic features, and achieves semantic augmentation. Next, LogSD proposes a category-guided Mixup data augmentation strategy to generate pseudo-samples toward abnormal logs in the feature space. Meanwhile, a novel contrastive learning approach is designed to constrain the features of pseudo-samples, reduce noise interference, and achieve data augmentation. Through extensive experiments on two datasets, it is confirmed that LogSD outperforms the state-of-art methods, achieving an average F1-score higher than 0.97.</p>", "Keywords": "Anomaly detection; Topic word; Log semantic; Mixup; Contrastive learning", "DOI": "10.1007/s11227-024-06850-y", "PubYear": 2025, "Volume": "81", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, China; Corresponding author."}], "References": [{"Title": "LogNADS: Network anomaly detection scheme based on log semantics representation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "390", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "LightLog: A lightweight temporal convolutional network for log anomaly detection on the edge", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "108616", "JournalTitle": "Computer Networks"}, {"Title": "Representation learning from noisy user-tagged data for sentiment classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "12", "Page": "3727", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "LayerLog: Log sequence anomaly detection based on hierarchical semantics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109860", "JournalTitle": "Applied Soft Computing"}, {"Title": "BiLSTM deep neural network model for imbalanced medical data of IoT systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "489", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Anomaly detection of policies in distributed firewalls using data log analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "17", "Page": "19473", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "MLog: Mogrifier LSTM-Based Log Anomaly Detection Approach Using Semantic Representation", "Authors": "Yuanyuan Fu; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "5", "Page": "3537", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "A robust Wide & Deep learning framework for log-based anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "111314", "JournalTitle": "Applied Soft Computing"}, {"Title": "Semi-Supervised Log Anomaly Detection Based on Bidirectional Temporal Convolution Network", "Authors": "<PERSON><PERSON><PERSON>; Xian <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "140", "Issue": "", "Page": "103808", "JournalTitle": "Computers & Security"}, {"Title": "LogGT: Cross-system log anomaly detection via heterogeneous graph feature and transfer learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "251", "Issue": "", "Page": "124082", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Interpretable Spatial–Temporal Graph Convolutional Network for System Log Anomaly Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "62", "Issue": "", "Page": "102803", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 142808862, "Title": "Nature Inspired Optimization For Real World Problems", "Abstract": "", "Keywords": "", "DOI": "10.9790/0661-2606025862", "PubYear": 2024, "Volume": "26", "Issue": "6", "JournalId": 10445, "JournalTitle": "IOSR Journal of Computer Engineering", "ISSN": "2278-8727", "EISSN": "2278-0661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142808997, "Title": "Revolutionizing online shopping with FITMI: a realistic virtual try-on solution", "Abstract": "In today’s digital age, consumers increasingly rely on online shopping for convenience and accessibility. However, a significant drawback of online shopping is the inability to physically try on clothing before purchasing. This limitation often leads to uncertainty regarding fit and style, resulting in customer post-purchase dissatisfaction and higher return rates. Research indicates that online items are three times more likely to be returned than in-store ones, especially during the pandemic. To address this challenge, we propose a virtual try-on method called FITMI, an enhanced Latent Diffusion Textual Inversion model for virtual try-on purposes. The proposed architecture aims to bridge the gap between traditional in-store try-ons and online shopping by offering users a realistic and interactive virtual try-on experience. Although virtual try-on solutions already exist, recent advancements in artificial intelligence have significantly enhanced their capabilities, enabling more sophisticated and realistic virtual try-on experiences than ever before. Building on these advancements, FITMI surpasses ordinary virtual try-ons relying on generative adversarial networks, often producing unrealistic outputs. Instead, FITMI utilizes latent diffusion models to generate high-quality images with detailed textures. As a web application, FITMI facilitates virtual try-ons by seamlessly integrating images of users with garments from catalogs, providing a true-to-life representation of how the items would look. This approach differentiates us from competitors. FITMI is validated using two widely recognized benchmarks: the Dress-Code and Viton-HD datasets. Additionally, FITMI acts as a trusted style advisor, enhancing the shopping experience by recommending complementary items to elevate the chosen garment and suggesting similar options based on user preferences.", "Keywords": "FITMI virtual try-on; Generative architectures; Latent diffusion models", "DOI": "10.1007/s00521-024-10843-6", "PubYear": 2025, "Volume": "37", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Tassneam M<PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computers and Artificial Intelligence, Helwan University, Cairo, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computers and Artificial Intelligence, Helwan University, Cairo, Egypt"}, {"AuthorId": 3, "Name": "Salwa O. Slim", "Affiliation": "Department of Computer Science, Faculty of Computers and Artificial Intelligence, Helwan University, Cairo, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Information Systems, Faculty of Computers and Information, Damanhour University, Damanhour, Egypt; Department of Information Systems, Faculty of Computers and Information, Kafrelsheikh University, Kafrelsheikh, Egypt"}], "References": []}, {"ArticleId": 142809084, "Title": "Fusion Data Framework for Enhanced Outlier Detection Integrating Statistical and Machine Learning Techniques for Retail Analytics", "Abstract": "", "Keywords": "", "DOI": "10.54216/FPA.180119", "PubYear": 2025, "Volume": "18", "Issue": "1", "JournalId": 91518, "JournalTitle": "Fusion: Practice and Applications", "ISSN": "2770-0070", "EISSN": "2692-4048", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tashkent State University of Economics, Tashkent city, Uzbekistan"}], "References": []}, {"ArticleId": 142809094, "Title": "IBing: An Efficient Interleaved Bidirectional Ring All-Reduce Algorithm for Gradient Synchronization", "Abstract": "<p>Ring all-reduce is currently the most commonly used collective communication technique in the fields of data parallel and distributed computing. It consists of three phases: communication establishment, data transmission, and data processing at each step. However, this method may suffer from increased communication latency as the number of computation nodes increases, excessive communication steps and data processing procedures can lead to insufficient bandwidth utilization.</p><p>To address this issue, this paper proposes an Interleaved Bidirectional Ring (IBing) all-reduce method, which uses specially crafted communication operations to improve communication efficiency by reducing the effects of both communication establishment and data processing time. IBing reduces the number of communication steps by half compared to the Ring all-reduce. The results of extensive experiments indicate that the proposed IBing design can reduce total communication consumption by an average of 8.49% and up to 49.73%.</p>", "Keywords": "", "DOI": "10.1145/3711818", "PubYear": 2025, "Volume": "22", "Issue": "1", "JournalId": 10048, "JournalTitle": "ACM Transactions on Architecture and Code Optimization", "ISSN": "1544-3566", "EISSN": "1544-3973", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan University,  Changsha, China"}], "References": [{"Title": "HSAC-ALADMM: an asynchronous lazy ADMM algorithm based on hierarchical sparse allreduce communication", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "8", "Page": "8111", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A survey on federated learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106775", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "MT-3000: a heterogeneous multi-zone processor for HPC", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "2", "Page": "150", "JournalTitle": "CCF Transactions on High Performance Computing"}, {"Title": "A Communication Efficient ADMM-based Distributed Algorithm Using Two-Dimensional Torus Grouping AllReduce", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "1", "Page": "61", "JournalTitle": "Data Science and Engineering"}]}, {"ArticleId": 142809104, "Title": "A Bayesian regularization intelligent computing scheme for the fractional dengue virus model", "Abstract": "This research’s goal is to investigate the numerical assessments of a fractional order dengue viral model (FO-DVM) by using the artificial intelligence procedure of Bayesian regularization neural networks (BRNNs). The FO derivatives present more precise results as compared to integer order for solving the DVM. The dynamics of the mathematical DVM form is considered into five classes. The computing stochastic BRNNs approach is presented for three variations with the selection of the data as testing 13%, authentication 11% and training 76% together with sixteen hidden neurons. The result’s comparison is accessible in the form of overlapping, which is based on the BRNNs approach and reference Adam solutions. However, minor absolute error around 10<sup>-05</sup> to 10<sup>-07</sup> enhances the worth of the proposed solver. The BRNNs approach is used to minimize the mean square error for the mathematical FO-DVM. The obtained measurements of error histograms values, and regression coefficient calculated as 1 are presented to verify the efficiency of stochastic BRNNs approach.", "Keywords": "Fractional order; Dengue virus model; Bayesian regularization; Artificial intelligence; Neural networks; Adam method", "DOI": "10.1016/j.eij.2024.100606", "PubYear": 2025, "Volume": "29", "Issue": "", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, SoS-Engineering and Technology, Guru <PERSON> Vishwavidyalaya, Bilaspur (Chhattisgarh), India;Corresponding authors"}, {"AuthorId": 2, "Name": "Pattarasinee Bhattarakosol", "Affiliation": "Department of Mathematics and Computer Science, Faculty of Science, Chulalongkorn University, Bangkok 10330, Thailand;Corresponding authors"}], "References": [{"Title": "Integrated intelligent computing paradigm for nonlinear multi-singular third-order Emden–Fowler equation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "8", "Page": "3417", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Solving a novel designed second order nonlinear Lane–<PERSON>den delay differential model using the heuristic techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107105", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Novel Stochastic Framework for the MHD Generator in Ocean", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "2", "Page": "3383", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Optimizing epileptic seizure recognition performance with feature scaling and dropout layers", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "6", "Page": "2835", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Design of stochastic neural networks for the fifth order system of singular engineering model", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "108141", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Feature reduction for hepatocellular carcinoma prediction using machine learning algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "A novel radial basis neural network for the Zika virus spreading model", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "112", "Issue": "", "Page": "108162", "JournalTitle": "Computational Biology and Chemistry"}]}, {"ArticleId": 142809111, "Title": "Mesoporous NiO nanosheets inherited from elm samara enable good comprehensive sensing of hydrazine gas at different low temperatures", "Abstract": "It is urgently needed to fabricate one chemoresistive sensor for good comprehensive sensing of highly dangerous hydrazine (N<sub>2</sub>H<sub>4</sub>) at low temperature due to no report. Herein, we chose discarded elm samara (ES) as synthetic template and simply immersed it in nickel nitrate solution. After calcining the immersed template in air at 600 °C, biomimetic NiO nanosheets (NiO-600) replicated by small-size nanoparticles were synthesized, which possess large specific surface area, narrow mesopore distribution, rich oxygen vacancies, and the presence of high-valence Ni<sup>3+</sup> ions. The above synergistic actions can expose more active site, promote rapid transport and adsorption of N<sub>2</sub>H<sub>4</sub> and its chemical reaction with adsorbed oxygen species in sensing layer, effectively improving the low-temperature comprehensive sensing ability of NiO nanosheets to N<sub>2</sub>H<sub>4</sub>. At near-room temperature of 50 °C, the response value of NiO-600 sensor to 100 ppm N<sub>2</sub>H<sub>4</sub> is 270, while its response value to the same concentration of N<sub>2</sub>H<sub>4</sub> is as high as 327.6 at low 92 °C, which is 13.8 times higher than that of template-free NiO-FT nanoparticles. Meanwhile, the sensor exhibits fast recovery characteristics, good humidity resistance and stability. In addition, we have explored its enhanced low-temperature N<sub>2</sub>H<sub>4</sub> sensing mechanism in detail.", "Keywords": "", "DOI": "10.1016/j.snb.2025.137246", "PubYear": 2025, "Volume": "428", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 4, "Name": "Yuan-Yuan Sun", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin 150080, China;Corresponding authors"}], "References": [{"Title": "MOF-derived polyhedral NiMoO4@NiO p-p heterostructure as an effective bridge for regulating carriers enhanced sensitivity and selectivity to trimethylamine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "343", "Issue": "", "Page": "130115", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Ionic liquid-assisted synthesis of 2D porous lotus root slice-shaped NiO nanomaterials for selective and highly sensitive detection of N2H4", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "359", "Issue": "", "Page": "131529", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Rapid and accurate detection of highly toxic NO2 gas based on catkins biomass-derived porous In2O3 microtubes at low temperature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "361", "Issue": "", "Page": "131692", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Graphitic carbon-doped SnO2 nanosheets-wrapped tubes for chemiresitive ppb-level nitric oxide sensors operated near room temperature", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> Lv", "PubYear": 2023, "Volume": "374", "Issue": "", "Page": "132822", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Low-temperature and high-selectivity butanone sensor based on porous Fe2O3 nanosheets synthesized by phoenix tree leaf template", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "377", "Issue": "", "Page": "133054", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Pr2Sn2O7/NiO heterojunction for ultra-fast and low operating temperature to NO2 gas sensing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "349", "Issue": "", "Page": "114100", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Biotemplate synthesis of NiO/ZnO tubes rich in oxygen vacancies for enhanced sensing detection of hydrazine at low temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "385", "Issue": "", "Page": "133684", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Sn2+ doped NiO hollow nanofibers to improve triethylamine sensing characteristics through tuning oxygen defects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "387", "Issue": "", "Page": "133801", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Biotemplate-assisted synthesis of CuO hierarchical tubes for highly chemiresistive detection of dimethylamine at room temperature", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "389", "Issue": "", "Page": "133896", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Mesoporous In2O3/ZnO heterogeneous microtubes replicated from waste willow catkins for high response and rapid detection of NO2 gas at low temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "400", "Issue": "", "Page": "134880", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Natural catkins-derived single-crystal WO3 tubes rich in oxygen vacancies for high response and rapid detection of hydrazine at near room temperature", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON> Lv", "PubYear": 2024, "Volume": "401", "Issue": "", "Page": "134939", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Trace carbon nitride-decorated SnO2 lamella boosting ultra-high response and fast recovery of NO2 gas at low temperature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "403", "Issue": "", "Page": "135214", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Corn stigma template-assisted synthesis of single crystal MoO3 elliptical nanosheets for chemiresistive detection of dibutylamine vapor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "408", "Issue": "", "Page": "135480", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 142809245, "Title": "Author index Volume 15 (2024)", "Abstract": "", "Keywords": "", "DOI": "10.1142/S1793962324990010", "PubYear": 2024, "Volume": "15", "Issue": "6", "JournalId": 12484, "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing", "ISSN": "1793-9623", "EISSN": "1793-9615", "Authors": [], "References": []}, {"ArticleId": 142809404, "Title": "Solving Nonlinear Energy Supply and Demand System Using Physics-Informed Neural Networks", "Abstract": "<p>Nonlinear differential equations and systems play a crucial role in modeling systems where time-dependent factors exhibit nonlinear characteristics. Due to their nonlinear nature, solving such systems often presents significant difficulties and challenges. In this study, we propose a method utilizing Physics-Informed Neural Networks (PINNs) to solve the nonlinear energy supply–demand (ESD) system. We design a neural network with four outputs, where each output approximates a function that corresponds to one of the unknown functions in the nonlinear system of differential equations describing the four-dimensional ESD problem. The neural network model is then trained, and the parameters are identified and optimized to achieve a more accurate solution. The solutions obtained from the neural network for this problem are equivalent when we compare and evaluate them against the Runge–<PERSON>tta numerical method of order 5(4) (RK45). However, the method utilizing neural networks is considered a modern and promising approach, as it effectively exploits the superior computational power of advanced computer systems, especially in solving complex problems. Another advantage is that the neural network model, after being trained, can solve the nonlinear system of differential equations across a continuous domain. In other words, neural networks are not only trained to approximate the solution functions for the nonlinear ESD system but can also represent the complex dynamic relationships between the system’s components. However, this approach requires significant time and computational power due to the need for model training. Furthermore, as this method is evaluated based on experimental results, ensuring the stability and convergence speed of the model poses a significant challenge. The key factors influencing this include the manner in which the neural network architecture is designed, such as the selection of hyperparameters and appropriate optimization functions. This is a critical and highly complex task, requiring experimentation and fine-tuning, which demand substantial expertise and time.</p>", "Keywords": "", "DOI": "10.3390/computation13010013", "PubYear": 2025, "Volume": "13", "Issue": "1", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Scientific Research Department, Irkutsk National Research Technical University, 664074 Irkutsk, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematics, Henan Academy of Sciences, Zhengzhou 450046, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Scientific Research Department, Irkutsk National Research Technical University, 664074 Irkutsk, Russia; Applied Mathematics Department, Melentiev Energy Systems Institute, Siberian Branch of Russian Academy of Sciences, 664003 Irkutsk, Russia; School of Electrical Engineering and Automation, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Scientific Research Department, Irkutsk National Research Technical University, 664074 Irkutsk, Russia; School of Electrical Engineering and Automation, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Harbin Institute of Technology, Harbin 150001, China"}], "References": []}, {"ArticleId": 142809503, "Title": "A comprehensive survey on impact of applying various technologies on the internet of medical things", "Abstract": "<p>This paper explores the transformative impact of the Internet of Medical Things (IoMT) on healthcare. By integrating medical equipment and sensors with the internet, IoMT enables real-time monitoring of patient health, remote patient care, and individualized treatment plans. IoMT significantly improves several healthcare domains, including managing chronic diseases, patient safety, and drug adherence, resulting in better patient outcomes and reduced expenses. Technologies like blockchain, Artificial Intelligence (AI), and cloud computing further boost IoMT’s capabilities in healthcare. Blockchain enhances data security and interoperability, AI analyzes massive volumes of health data to find patterns and make predictions, and cloud computing offers scalable and cost-effective data processing and storage. Therefore, this paper provides a comprehensive review of the Internet of Things (IoT) and IoMT-based edge-intelligent smart healthcare, focusing on publications published between 2018 and 2024. The review addresses numerous studies on IoT, IoMT, AI, edge and cloud computing, security, Deep Learning, and blockchain. The obstacles facing IoMT are also covered in this paper, including interoperability issues, regulatory compliance, and privacy and data security concerns. Finally, recommendations for further studies are provided.</p>", "Keywords": "Internet of Medical Things (IoMT); Smart healthcare; Deep Learning (DL); Blockchain; Artificial Intelligence (AI); Cloud computing", "DOI": "10.1007/s10462-024-11063-z", "PubYear": 2025, "Volume": "58", "Issue": "3", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "Shorouk E. El-deep", "Affiliation": "Faculty of Computers and Information, Kafrelsheikh University, Kafrelsheikh, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Faculty of Computers and Information, Kafrelsheikh University, Kafrelsheikh, Egypt; Faculty of Computers and Information, Damanhour University, Damanhour, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Information Systems, Sohag University, Sohag, Egypt; Corresponding author."}], "References": [{"Title": "Intelligence in the Internet of Medical Things era: A systematic review of current and future trends", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "644", "JournalTitle": "Computer Communications"}, {"Title": "Securing internet of medical things systems: Limitations, issues and recommendations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "581", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Resource-efficient fast prediction in healthcare data analytics: A pruned Random Forest regression approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "5", "Page": "1187", "JournalTitle": "Computing"}, {"Title": "A deep learning based medical image segmentation technique in Internet-of-Medical-Things domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "135", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Detection and diagnosis of chronic kidney disease using deep learning-based heterogeneous modified artificial neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "17", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Sensors for internet of medical things: State-of-the-art, security and privacy issues, challenges and future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "111", "JournalTitle": "Computer Communications"}, {"Title": "An end-to-end deep learning model for human activity recognition from highly sparse body sensor data in Internet of Medical Things environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "3", "Page": "2237", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "SDN orchestration to combat evolving cyber threats in Internet of Medical Things (IoMT)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "697", "JournalTitle": "Computer Communications"}, {"Title": "Perspective: Wearable Internet of Medical Things for Remote Tracking of Symptoms, Prediction of Health Anomalies, Implementation of Preventative Measures, and Control of Virus Spread During the Era of COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "84", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "A Novel Lightweight Deep Learning-Based Histopathological Image Classification Model for IoMT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "1", "Page": "205", "JournalTitle": "Neural Processing Letters"}, {"Title": "A decentralized framework for device authentication and data security in the next generation internet of medical things", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "146", "JournalTitle": "Computer Communications"}, {"Title": "HealthBlock: A secure blockchain-based healthcare data management system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "200", "Issue": "", "Page": "108500", "JournalTitle": "Computer Networks"}, {"Title": "Smart healthcare IoT applications based on fog computing: architecture, applications and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "5", "Page": "3805", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "BACKM-EHA: A Novel Blockchain-enabled Security Solution for IoMT-based E-healthcare Applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "A smart secure healthcare monitoring system with Internet of Medical Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "107969", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A survey on COVID-19 impact in the healthcare domain: worldwide market implementation, applications, security and privacy issues, challenges and future prospects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; W<PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "1027", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Brain image identification and classification on Internet of Medical Things in healthcare system using support value based deep neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108196", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A quantum blockchain-enabled framework for secure private electronic medical records in Internet of Medical Things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "612", "Issue": "", "Page": "942", "JournalTitle": "Information Sciences"}, {"Title": "Fuzzy-logic-based IoMT framework for COVID19 patient monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "176", "Issue": "", "Page": "108941", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "The stream data warehouse: Page replacement algorithms and quality of service metrics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "212", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Secure data authentication and access control protocol for industrial healthcare system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "4853", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A blockchain-based secure Internet of medical things framework for stress detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "628", "Issue": "", "Page": "377", "JournalTitle": "Information Sciences"}, {"Title": "A systematic review of trustworthy and explainable artificial intelligence in healthcare: Assessment of quality, bias risk, and data fusion", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "96", "Issue": "", "Page": "156", "JournalTitle": "Information Fusion"}, {"Title": "Survey of Machine Learning based intrusion detection methods for Internet of Medical Things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "110227", "JournalTitle": "Applied Soft Computing"}, {"Title": "Personal HealthCare of Things: A novel paradigm and futuristic approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "User privacy prevention model using supervised federated learning‐based block chain approach for internet of Medical Things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Federated learning for secure IoMT-applications in smart healthcare systems: A comprehensive review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "110658", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Energy and Latency Optimization in Edge-Fog-Cloud Computing for the Internet of Medical Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "1", "Page": "1299", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "An efficient privacy-preserving authentication scheme with enhanced security for IoMT applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "208", "Issue": "", "Page": "171", "JournalTitle": "Computer Communications"}, {"Title": "TFAS: two factor authentication scheme for blockchain enabled IoMT using PUF and fuzzy extractor", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "1", "Page": "865", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Heart Diseases Diagnosis Using Chaotic Harris Hawk Optimization with E-CNN for IoMT Framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "52", "Issue": "2", "Page": "500", "JournalTitle": "Information Technology And Control"}, {"Title": "BCSoM: Blockchain-based certificateless aggregate signcryption scheme for Internet of Medical Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "48", "JournalTitle": "Computer Communications"}, {"Title": "Feature selection based machine learning models for 5G network slicing approximation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "237", "Issue": "", "Page": "110093", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 142809529, "Title": "Fostering effective hybrid human-LLM reasoning and decision making", "Abstract": "<p>The impressive performance of modern Large Language Models (LLMs) across a wide range of tasks, along with their often non-trivial errors, has garnered unprecedented attention regarding the potential of AI and its impact on everyday life. While considerable effort has been and continues to be dedicated to overcoming the limitations of current models, the potentials and risks of human-LLM collaboration remain largely underexplored. In this perspective, we argue that enhancing the focus on human-LLM interaction should be a primary target for future LLM research. Specifically, we will briefly examine some of the biases that may hinder effective collaboration between humans and machines, explore potential solutions, and discuss two broader goals—mutual understanding and complementary team performance—that, in our view, future research should address to enhance effective human-LLM reasoning and decision-making.</p>", "Keywords": "hybrid intelligence; biases; human-AI collaboration; LLMs; mutual understanding; complementary team performance", "DOI": "10.3389/frai.2024.1464690", "PubYear": 2025, "Volume": "7", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering and Computer Science, University of Trento, Italy; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Informatics, University of Edinburgh, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, University of Edinburgh, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Computer Science, University of Trento, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Mind/Brain Sciences, University of Trento, Italy"}], "References": [{"Title": "To Trust or to Think", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Survey of Hallucination in Natural Language Generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "12", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Shortcut Learning of Large Language Models in Natural Language Understanding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "67", "Issue": "1", "Page": "110", "JournalTitle": "Communications of the ACM"}, {"Title": "Explainability for Large Language Models: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Explainable Artificial Intelligence (XAI) 2.0: A manifesto of open challenges and interdisciplinary research directions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "106", "Issue": "", "Page": "102301", "JournalTitle": "Information Fusion"}, {"Title": "Users do not trust recommendations from a large language model more than AI-sourced snippets", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "6", "Issue": "", "Page": "", "JournalTitle": "Frontiers in Computer Science"}]}, {"ArticleId": 142809643, "Title": "Hate Speech Detection Research in South Asian Languages: A Survey of Tasks, Datasets and Methods", "Abstract": "Social media has over the years emerged as a powerful platform for communicating and sharing views, thoughts, and opinions. However, at the same time it is being abused by certain individuals to spread hate against individuals, communities, religions, and so on. Such content can lead to serious issues of mental health, online well-being, and social order. Therefore, it is very important to have automated methods and approaches for detecting such content from the large volume of posts in social media. Recently there has been several efforts to develop computational approaches toward this end, however, most of these efforts are directed toward content in English language. Only recently studies have started focusing on low resource languages, including those from South Asia. This article attempts to present a detailed and comprehensive survey of hate speech related research in South Asian languages. The various definitions and terms related to Hate speech in different social media platforms are discussed first. The different tasks in the hate speech research, available datasets, and the popular computational approaches used in the South-Asian languages are surveyed in detail. Major patterns identified and the practical implications are presented and discussed, along with a discussion of challenges and opportunities of further research in the area.", "Keywords": "", "DOI": "10.1145/3711710", "PubYear": 2025, "Volume": "24", "Issue": "3", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Banaras Hindu University, Varanasi, India;School of Computer Science Engineering and Technology, Bennett University, Noida, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science, Banaras Hindu University, Varanasi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jindal Global Business School, OP Jindal Global University, Sonipat, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science, Banaras Hindu University, Varanasi, India;Computer Science, University of Delhi, New Delhi, India"}], "References": [{"Title": "Towards Cyberbullying-free social media in smart cities: a unified multi-modal approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "15", "Page": "11059", "JournalTitle": "Soft Computing"}, {"Title": "Detection of Hate Speech Text in Hindi-English Code-mixed Data", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "737", "JournalTitle": "Procedia Computer Science"}, {"Title": "Multi-input integrative learning using deep neural networks and transfer learning for cyberbullying detection in real-time code-mix data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "6", "Page": "2027", "JournalTitle": "Multimedia Systems"}, {"Title": "Resources and benchmark corpora for hate speech detection: a systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "2", "Page": "477", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Roman Urdu toxic comment classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "4", "Page": "971", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Bangla hate speech detection on social media using attention-based recurrent neural network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "1", "Page": "578", "JournalTitle": "Journal of Intelligent Systems"}, {"Title": "An Evaluation of Multilingual Offensive Language Identification Methods for the Languages of India", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "306", "JournalTitle": "Information"}, {"Title": "Identifying vulgarity in Bengali social media textual content", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e665", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Tackling cyber-aggression: Identification and fine-grained categorization of aggressive texts on social media using weighted ensemble of transformers", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "490", "Issue": "", "Page": "462", "JournalTitle": "Neurocomputing"}, {"Title": "Cyberbullying detection: advanced preprocessing techniques & deep learning architecture for Roman Urdu data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "160", "JournalTitle": "Journal of Big Data"}, {"Title": "Abusive Bangla comments detection on Facebook using transformer-based deep learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Hate speech and offensive language detection in Dravidian languages using deep ensemble framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101386", "JournalTitle": "Computer Speech & Language"}, {"Title": "Natural language processing and machine learning based cyberbullying detection for Bangla and Romanized Bangla texts", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "20", "Issue": "1", "Page": "89", "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)"}, {"Title": "Offensive language detection in Tamil YouTube comments by adapters and cross-domain knowledge transfer", "Authors": "<PERSON><PERSON>ramanian; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "76", "Issue": "", "Page": "101404", "JournalTitle": "Computer Speech & Language"}, {"Title": "A Literature Review of Textual Hate Speech Detection Methods and Datasets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "273", "JournalTitle": "Information"}, {"Title": "Offence Detection in Dravidian Languages Using Code-Mixing Index-Based Focal Loss", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "5", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "ToxLex_bn: A curated dataset of bangla toxic language derived from Facebook comment", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "", "Page": "108416", "JournalTitle": "Data in Brief"}, {"Title": "How can we detect Homophobia and Transphobia? Experiments in a multilingual code-mixed setting for social media governance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "2", "Page": "100119", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "A Framework for Online Hate Speech Detection on Code-mixed Hindi-English Text and Hindi Text in Devanagari", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Identification of offensive language in Urdu using semantic and embedding models", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e1169", "JournalTitle": "PeerJ Computer Science"}, {"Title": "HateCircle and Unsupervised Hate Speech Detection incorporating Emotion and Contextual Semantic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Offensive language identification in dravidian languages using MPNet and CNN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "1", "Page": "100151", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "A literature survey on multimodal and multilingual automatic hate speech identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "1203", "JournalTitle": "Multimedia Systems"}, {"Title": "Detecting Dravidian Offensive Posts in MIoT: A Hybrid Deep Learning Framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A systematic review of hate speech automatic detection using natural language processing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "546", "Issue": "", "Page": "126232", "JournalTitle": "Neurocomputing"}, {"Title": "THAR- Targeted Hate Speech Against Religion: A high-quality Hindi-English code-mixed Dataset with the Application of Deep Learning Models for Automatic Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Misogynistic Attitude Detection in YouTube comments and replies: A high-quality dataset and algorithmic models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "89", "Issue": "", "Page": "101682", "JournalTitle": "Computer Speech & Language"}, {"Title": "TABHATE: A Target-based hate speech detection dataset in Hindi", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}]}, {"ArticleId": 142809657, "Title": "From Bench to Brain: A Metadata-driven Approach to Research Data Management in a Collaborative Neuroscientific Research Center", "Abstract": "", "Keywords": "", "DOI": "10.5334/dsj-2025-002", "PubYear": 2025, "Volume": "24", "Issue": "", "JournalId": 11331, "JournalTitle": "Data Science Journal", "ISSN": "", "EISSN": "1683-1470", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142809943, "Title": "Extract, model, refine: improved modelling of program verification tools through data enrichment", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10270-024-01232-7", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A taxonomy for classifying runtime verification tools", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "255", "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)"}, {"Title": "Human values in software development artefacts: A case study on issue discussions in three Android applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "141", "Issue": "", "Page": "106731", "JournalTitle": "Information and Software Technology"}, {"Title": "Blended modeling in commercial and open-source model-driven software engineering tools: A systematic study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "1", "Page": "415", "JournalTitle": "Software & Systems Modeling"}, {"Title": "(Re)Use of Research Results (Is Rampant)", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "2", "Page": "75", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 142809963, "Title": "An integrated modeling, verification, and code generation for uncrewed aerial systems: less cost and more efficiency", "Abstract": "<p>Uncrewed Aerial Systems (UASs) are widely implemented in safety-critical fields such as industrial production, military operations, and disaster relief. Due to the diversity and complexity of implementation scenarios, UASs have become increasingly intricate. The challenge of designing and implementing highly reliable UASs while effectively controlling development costs and improving efficiency has been a pressing issue faced by academia and industry. To address this challenge, this article aims to examine an integrated method for modeling, verification, and code generation for UASs. This article begins to utilize Architecture Analysis and Design Language (AADL) to model UASs, proposing generic UAS models. Then, formal specifications describe a system's safety properties and functions based on these models. Finally, this article introduces a method to generate flight controller codes for UASs based on the verified models. Experiments demonstrate its effectiveness in pinpointing potential vulnerabilities in UASs during the early design phase and generating viable flight controller codes from the verified models. The proposed approach can also improve the efficiency of designing and verifying high-reliability UASs.</p>", "Keywords": "Architecture analysis and design language (AADL);Formal verification;System reliability;Uncrewed aerial systems (UAS)", "DOI": "10.7717/peerj-cs.2575", "PubYear": 2025, "Volume": "11", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Information System Security, AMS, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics and Information, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Information System Security, AMS, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Information System Security, AMS, Beijing, China"}], "References": [{"Title": "Runtime Assurance for Autonomous Aerospace Systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "12", "Page": "2205", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "A novel fault diagnosis in sensors of quadrotor unmanned aerial vehicle", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "10", "Page": "14081", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Unmanned aerial vehicles (UAVs): practical aspects, applications, open challenges, security issues, and future trends", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "109", "JournalTitle": "Intelligent Service Robotics"}]}, {"ArticleId": 142810062, "Title": "The role of generative AI in academic and scientific authorship: an autopoietic perspective", "Abstract": "The integration of generative artificial intelligence (AI), particularly large language models like ChatGPT, presents new challenges as well as possibilities for scientific authorship. This paper draws on social systems theory to offer a nuanced understanding of the interplay between technology, individuals, society and scholarly authorial practices. This contrasts with orthodoxy, where individuals and technology are treated as essentialized entities. This approach offers a critique of the binary positions of sociotechnological determinism and accelerationist instrumentality while still acknowledging that generative AI presents profound challenges to existing practices and meaning making in scientific scholarship. This holistic treatment of authorship, integrity, and technology involves comprehending the historical and evolutionary entanglement of scientific individuality, scientific practices, and meaning making with technological innovation. This addresses current needs for more robust theoretical approaches to address the challenges confronted by academicians, institutions, peer review, and publication processes. Our analysis aims to contribute to a more sophisticated discourse on the ethical and practical implications of AI in scientific research.", "Keywords": "", "DOI": "10.1007/s00146-024-02174-w", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Meaning–thinking–AI", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "5", "Page": "2213", "JournalTitle": "AI & SOCIETY"}, {"Title": "The technology triad: disruptive AI, regulatory gaps and value change", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "4", "Page": "1051", "JournalTitle": "AI and Ethics"}, {"Title": "Co-constructing knowledge with generative AI tools: Reflections from a CSCL perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "4", "Page": "607", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "Generative AI can fabricate advanced scientific visualizations: ethical implications and strategic mitigation framework", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "AI and Ethics"}]}, {"ArticleId": 142810151, "Title": "A content-style control network with style contrastive learning for underwater image enhancement", "Abstract": "<p>Underwater image enhancement (UIE) aims to solve or mitigate issues such as color cast, low contrast, and blurred details in underwater images, having wide applications in fields such as ocean exploration, marine resource development, underwater archaeology, and underwater robotics. However, many UIE methods still fail to adequately address the issue of poor domain generalization ability. One possible reason is that they lack the integration of image content and style control. Additionally, they may generate low visual quality enhanced images due to unrealistic content and unattractive styles, especially methods based on Cycle-Consistent Generative Adversarial Network (CycleGAN). To address these issues, we propose a Content-Style Control Network with Style Contrastive Learning (CSC-SCL). Firstly, to enhance the domain generalization ability of the model in handling underwater images with different distortion distributions, we design a generator to extract domain-invariant features by adaptively adjusting the degree of content and style changes at the feature level without requiring domain labels. Then, to enhance visual quality in terms of both content and style, we design content loss and style contrastive learning, which are used to ensure the authenticity of the image content and produce more pleasing styles, respectively. Extensive experiments on three datasets demonstrate that our method outperforms most existing methods in terms of both visual quality and quantitative metrics. Our code will be released at https://github.com/xjq-flare/CSC-SCL .</p>", "Keywords": "Underwater image enhancement; CycleGAN; Domain generalization; Contrastive learning", "DOI": "10.1007/s00530-024-01642-z", "PubYear": 2025, "Volume": "31", "Issue": "1", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, People’s Republic of China; Engineering and Research Center of Embedded Systems Integration (Northwestern Polytechnical University), Ministry of Education, Xi’an, People’s Republic of China; National Engineering Laboratory for Integrated Aero-Space-Ground-Ocean Big Data Application Technology, Xi’an, People’s Republic of China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}], "References": [{"Title": "Correlation-aware adversarial domain adaptation and generalization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107124", "JournalTitle": "Pattern Recognition"}, {"Title": "A hybrid algorithm for underwater image restoration based on color correction and image sharpening", "Authors": "<PERSON><PERSON> Meng; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "6", "Page": "1975", "JournalTitle": "Multimedia Systems"}, {"Title": "Bayesian retinex underwater image enhancement", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104171", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Hierarchical attention aggregation with multi-resolution feature learning for GAN-based underwater image enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "", "Page": "106743", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Underwater image enhancement method based on a cross attention mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "Multimedia Systems"}, {"Title": "HCLR-Net: Hybrid Contrastive Learning Regularization with Locally Randomized Perturbation for Underwater Image Enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Sun; <PERSON><PERSON><PERSON> Li", "PubYear": 2024, "Volume": "132", "Issue": "10", "Page": "4132", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Towards domain adaptation underwater image enhancement and restoration", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "30", "Issue": "2", "Page": "1", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 142810161, "Title": "FLAME: fire detection in videos combining a deep neural network with a model-based motion analysis", "Abstract": "Among the catastrophic natural events posing hazards to human lives and infrastructures, fire is the phenomenon causing more frequent damages. Thanks to the spread of smart cameras, video fire detection is gaining more attention as a solution to monitor wide outdoor areas where no specific sensors for smoke detection are available. However, state-of-the-art fire detectors assure a satisfactory Recall but exhibit a high false-positive rate that renders the application practically unusable. In this paper, we propose FLAME, an efficient and adaptive classification framework to address fire detection from videos. The framework integrates a state-of-the-art deep neural network for frame-wise object detection, in an automatic video analysis tool. The advantages of our approach are twofold. On the one side, we exploit advances in image detector technology to ensure a high Recall. On the other side, we design a model-based motion analysis that improves the system’s Precision by filtering out fire candidates occurring in the scene’s background or whose movements differ from those of the fire. The proposed technique, able to be executed in real-time on embedded systems, has proven to surpass the methods considered for comparison on a recent literature dataset representing several scenarios. The code and the dataset used for designing the system have been made publicly available by the authors at ( https://mivia.unisa.it/large-fire-dataset-with-negative-samples-lfdn/ ).", "Keywords": "Fire detection; Video classification; Object detection; Object tracking", "DOI": "10.1007/s00521-024-10963-z", "PubYear": 2025, "Volume": "37", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information and Electrical Engineering and Applied Mathematics (DIEM), University of Salerno, Fisciano, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information and Electrical Engineering and Applied Mathematics (DIEM), University of Salerno, Fisciano, Italy; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology (DIETI), University of Napoli Federico II, Napoli, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology (DIETI), University of Napoli Federico II, Napoli, Italy"}], "References": [{"Title": "Background subtraction in real applications: Challenges, current models and future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "", "Page": "100204", "JournalTitle": "Computer Science Review"}, {"Title": "Real-time video fire/smoke detection based on CNN in antifire surveillance systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "3", "Page": "889", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "A deep learning framework for autonomous flame detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "448", "Issue": "", "Page": "205", "JournalTitle": "Neurocomputing"}, {"Title": "A fire monitoring and alarm system based on channel-wise pruned YOLOv3", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "2", "Page": "1833", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A hybrid deep learning model by combining convolutional neural network and recurrent neural network to detect forest fire", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "27", "Page": "38643", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An automatic fire detection system based on deep convolutional neural networks for low-power, resource-constrained devices", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "18", "Page": "15349", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A hybrid method for fire detection based on spatial and temporal patterns", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "13", "Page": "9349", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An improved fire detection approach based on YOLO-v8 for smart cities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "28", "Page": "20939", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A Comprehensive Review of YOLO Architectures in Computer Vision: From YOLOv1 to YOLOv8 and YOLO-NAS", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "4", "Page": "1680", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "Fire and smoke detection from videos: A literature review under a novel taxonomy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "255", "Issue": "", "Page": "124783", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": *********, "Title": "Research Articles", "Abstract": "", "Keywords": "", "DOI": "10.3233/ICO-2021-28-2-s02", "PubYear": 2021, "Volume": "28", "Issue": "2", "JournalId": 24898, "JournalTitle": "Integrated Computer-Aided Engineering", "ISSN": "1069-2509", "EISSN": "1875-8835", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "Efficient Deployment Approach in WSNs Using Heuristic Technique", "Abstract": "", "Keywords": "", "DOI": "10.54216/JCIM.150224", "PubYear": 2025, "Volume": "15", "Issue": "2", "JournalId": 91522, "JournalTitle": "Journal of Cybersecurity and Information Management", "ISSN": "2769-7851", "EISSN": "2690-6775", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science for Women-Computer Science Dept, University of Babylon, Babylon, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Science for Women-Computer Science Dept, University of Babylon, Babylon, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science for Women-Computer Science Dept, University of Babylon, Babylon, Iraq"}], "References": []}, {"ArticleId": 142810297, "Title": "Multiobjective multi-space collaboration model for addressing spectral variability in hyperspectral image unmixing", "Abstract": "Hyperspectral remote sensing image unmixing poses a significant challenge, especially in the precise identification of pure spectral signatures (endmembers). This identification is a multifaceted optimization problem, often leading to locally optimal solutions given the inherent complexity and high dimensionality of hyperspectral images. A single objective function proves insufficient to model the rich spectral variability of endmembers. Recent advances in multiobjective evolutionary optimization for endmember bundle extraction (EBE) have illuminated the potential of multiobjective optimization techniques to address spectral variability. Yet, issues like repeated endmembers and high dimensionality have not been adequately addressed in the evolutionary process. This article introduces the Multiobjective Multi-Space Collaboration (MOMSC) model, coupled with a multiobjective genetic algorithm, to bridge these gaps. Within MOMSC, a novel multiple subspace generation strategy is devised, targeting the unveiling of spectral diversity across varied feature spaces and enhancing the capture of total spectral variability. Trios of these generated subspaces are integrated to form a multiobjective EBE framework. Given the unique nature of endmembers, tailored strategies in the genetic algorithm, such as gene segment pool allocation and spatial crowding distance calculation, are proposed to counteract high dimensionality. An innovative replacement mechanism is also proposed, refining the search space for subsequent subspace groups, circumventing the repeated endmember dilemma, and bolstering the odds of acquiring more endmember variabilities. Experimental results from three real hyperspectral images validate the effectiveness of our proposed MOMSC approach.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112679", "PubYear": 2025, "Volume": "170", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Geography and Planning, Sun Yat-sen University, Guangzhou, 510006, China;<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Subtropical Building and Urban Science, Shenzhen University, Shenzhen, 518060, China;<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Civil and Transportation Engineering, Shenzhen University, Shenzhen, 518060, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geography and Planning, Sun Yat-sen University, Guangzhou, 510006, China;Corresponding author"}], "References": [{"Title": "Multi-fidelity evolutionary multitasking optimization for hyperspectral endmember extraction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107713", "JournalTitle": "Applied Soft Computing"}, {"Title": "An enhanced fast non-dominated solution sorting genetic algorithm for multi-objective problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "585", "Issue": "", "Page": "441", "JournalTitle": "Information Sciences"}, {"Title": "Individual tree segmentation and tree species classification in subtropical broadleaf forests using UAV-based LiDAR, hyperspectral, and ultrahigh-resolution RGB data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113143", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Dynamic hybrid mechanism-based differential evolution algorithm and its application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118834", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel adaptive weight algorithm based on decomposition and two-part update strategy for many-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "615", "Issue": "", "Page": "323", "JournalTitle": "Information Sciences"}, {"Title": "First evaluation of fire severity retrieval from PRISMA hyperspectral data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "295", "Issue": "", "Page": "113670", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 142810358, "Title": "Fundamental investigations on temperature development in ultra-precision turning", "Abstract": "Ultra-precision machining represents a key technology for manufacturing optical components in medical, aerospace and automotive industry. Dedicated single crystal diamond tools enable the production of innovative optical surfaces and components with high dimensional accuracies and low surface roughness values in a wide range of airborne sensing and imaging applications concerning space telescopes, fast steering mirrors, laser communication and high-energy laser systems. Despite the high mechanical hardness of single crystal diamonds, temperature-induced wear of the diamond tools occurs during the process. In order to increase the economic efficiency of ultra-precision turning, the characterisation and interpretation of cutting temperatures are of utmost importance. According to the state-of-the-art, there are no precise methods for online temperature monitoring during the process at the cutting edge with regard to the requirements for resolution accuracy, response time and accessibility to the cutting edge. For this purpose, a dedicated cutting edge temperature measurement system based on ion-implanted boron-doped single crystal diamonds as a highly sensitive temperature sensor for ultra-precision turning was developed. To enable highly sensitive temperature measurements, ion implantation was used for partial and specific boron doping close to the cutting edge of single crystal diamond tools. Within the investigations, a resolution accuracy of 0.29 °C ≤  a \n R  ≤ 0.39 °C could be proven for the developed cutting edge temperature measurement system. In addition, a total measurement uncertainty of u \n M  = 0.098 °C was determined for the sensor accuracy  a \n S in the investigated process area. For a rake angle range of 0° ≤ γ 0  ≤ −30°, reaction times of 420 ms ≤  t \n R  ≤ 440 ms were further determined. Using the developed cutting edge temperature measurement system enables a holistic view of the temperature development during ultra-precision machining, whereby a correlation between the measured cutting temperatures and the chip formation mechanisms depending on the applied process parameters could be identified. Within the investigations, the highest measured temperatures of ϑ M  = 50.18 °C and simulated maximum temperatures of ϑ S,max  = 183.12 °C were determined at a cutting speed of v \n c  = 350 m/min, a cutting depth of a \n p  = 35 µm as well as a feed of f  = 35 µm using a rake angle of γ 0  = −30°. The most uniform chips with the smoothest surfaces were identified within the chip analysis using a cutting speed of v \n c  = 50 m/min, a cutting depth of a \n p  = 5 µm and a feed of f  = 35 µm with a measured temperature of ϑ M  = 21.30 °C and a simulated temperature of ϑ S  = 38.47 °C in the examined finishing area. According to the results, it was also shown that the cutting edge temperature measurement system with ion-implanted diamonds can be used for both electrically conductive and non-conductive materials. This provides the fundamentals for further research works to identify the complex temperature-induced wear behaviour of single crystal diamonds in ultra-precision turning and serves as the basis for self-optimising and self-learning ultra-precision machine tools.", "Keywords": "Temperature sensor; Ion implantation; Boron-doped diamonds; Ultra-precision machining", "DOI": "10.1007/s00170-024-14846-4", "PubYear": 2025, "Volume": "136", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Machine Tools and Factory Management IWF, Technische Universität Berlin, Berlin, Germany; Fraunhofer Institute for Production Systems and Design Technology IPK, Berlin, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Machine Tools and Factory Management IWF, Technische Universität Berlin, Berlin, Germany; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Machine Tools and Factory Management IWF, Technische Universität Berlin, Berlin, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Machine Tools and Factory Management IWF, Technische Universität Berlin, Berlin, Germany; Fraunhofer Institute for Production Systems and Design Technology IPK, Berlin, Germany"}], "References": [{"Title": "Cutting temperature measurement and prediction in machining processes: comprehensive review and future perspectives", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "5-6", "Page": "2849", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 142810431, "Title": "Mathematical modelling and dynamic optimization of phase-locked loop systems using hybrid PSO-gradient descent approach", "Abstract": "", "Keywords": "", "DOI": "10.1080/21642583.2024.2448636", "PubYear": 2025, "Volume": "13", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology Arunachal Pradesh, Jote, Arunachal Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology Arunachal Pradesh, Jote, Arunachal Pradesh, India"}], "References": []}, {"ArticleId": 142810688, "Title": "Blockchain-Driven Innovation in Fashion Supply Chain Contractual Party Evaluations As An Emerging Collaboration Model", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.bcra.2024.100266", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 83117, "JournalTitle": "Blockchain: Research and Applications", "ISSN": "2096-7209", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Internet of Things and Blockchain Technology in Apparel Manufacturing Supply Chain Data Management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "450", "JournalTitle": "Procedia Computer Science"}, {"Title": "A survey on the adoption of blockchain in IoT: challenges and solutions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "100006", "JournalTitle": "Blockchain: Research and Applications"}, {"Title": "A survey of consensus algorithms in public blockchain systems for crypto-currencies", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "103035", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Blockchain-enabled Sustainability Labeling in the Fashion Industry", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>rø<PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "280", "JournalTitle": "Procedia Computer Science"}, {"Title": "Redefining food safety traceability system through blockchain: findings, challenges and open issues", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "14", "Page": "21243", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 142810758, "Title": "Enhancing nighttime vehicle detection with day-to-night style transfer and labeling-free augmentation", "Abstract": "<p xml:lang=\"fr\"><p>Deep learning-based object detection models perform well under daytime conditions but face significant challenges at night, primarily because they are predominantly trained on daytime images. Additionally, training with nighttime images presents another challenge: Even human annotators struggle to accurately label objects in low-light conditions. This issue is particularly pronounced in transportation applications, such as detecting vehicles and other objects of interest on rural roads at night, where street lighting is often absent, and headlights may introduce undesirable glare. In this study, we addressed these challenges by introducing a novel framework for labeling-free data augmentation, leveraging synthetic data generated by the Car Learning to Act (CARLA) simulator for day-to-night image style transfer. Specifically, the framework incorporated the efficient attention Generative Adversarial Network for realistic day-to-night style transfer and used CARLA-generated synthetic nighttime images to help the model learn the vehicle headlight effect. To evaluate the efficacy of the proposed framework, we fine-tuned the state-of-the-art object detection model with an augmented dataset curated for rural nighttime environments, achieving significant improvements in nighttime vehicle detection. This novel approach was simple yet effective, offering a scalable solution to enhance deep learning-based detection systems in low-visibility environments and extended the applicability of object detection models to broader real-world contexts.</p></p>", "Keywords": "", "DOI": "10.3934/aci.2025002", "PubYear": 2025, "Volume": "5", "Issue": "1", "JournalId": 94718, "JournalTitle": "Applied Computing and Intelligence", "ISSN": "2771-392X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Yong<PERSON> Huang", "Affiliation": ""}, {"AuthorId": 4, "Name": "Jidong J. Yang", "Affiliation": ""}], "References": [{"Title": "Vision-based On-Road Nighttime Vehicle Detection and Tracking Using Taillight and Headlight Features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "29", "JournalTitle": "Journal of Computer and Communications"}, {"Title": "CARLA: Car Learning to Act — An Inside Out", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "742", "JournalTitle": "Procedia Computer Science"}, {"Title": "Real-time vehicle detection algorithm based on a lightweight You-Only-Look-Once (YOLOv5n-L) approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119108", "JournalTitle": "Expert Systems with Applications"}, {"Title": "EnsembleNet: a hybrid approach for vehicle detection and estimation of traffic density based on faster R-CNN and YOLO models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "6", "Page": "4755", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Cross-domain car detection model with integrated convolutional block attention mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "104834", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 142810773, "Title": "Efficient scaling of large language models with mixture of experts and 3D analog in-memory computing", "Abstract": "<p>Large language models (LLMs), with their remarkable generative capacities, have greatly impacted a range of fields, but they face scalability challenges due to their large parameter counts, which result in high costs for training and inference. The trend of increasing model sizes is exacerbating these challenges, particularly in terms of memory footprint, latency and energy consumption. Here we explore the deployment of 'mixture of experts' (MoEs) networks-networks that use conditional computing to keep computational demands low despite having many parameters-on three-dimensional (3D) non-volatile memory (NVM)-based analog in-memory computing (AIMC) hardware. When combined with the MoE architecture, this hardware, utilizing stacked NVM devices arranged in a crossbar array, offers a solution to the parameter-fetching bottleneck typical in traditional models deployed on conventional von-Neumann-based architectures. By simulating the deployment of MoEs on an abstract 3D AIMC system, we demonstrate that, due to their conditional compute mechanism, MoEs are inherently better suited to this hardware than conventional, dense model architectures. Our findings suggest that MoEs, in conjunction with emerging 3D NVM-based AIMC, can substantially reduce the inference costs of state-of-the-art LLMs, making them more accessible and energy-efficient.</p><p>© 2025. The Author(s), under exclusive licence to Springer Nature America, Inc.</p>", "Keywords": "", "DOI": "10.1038/s43588-024-00753-x", "PubYear": 2025, "Volume": "5", "Issue": "1", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IBM Research Europe, Rüschlikon, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IBM Research Europe, Rüschlikon, Switzerland."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IBM Research Europe, Rüschlikon, Switzerland."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "IBM Research Europe, Rüschlikon, Switzerland."}, {"AuthorId": 5, "Name": "HsinYu Tsai", "Affiliation": "IBM Research - Almaden, San Jose, CA, USA."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "IBM Research - Almaden, San Jose, CA, USA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Micron Technology, Folsom, CA, USA."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Micron Technology, Novi, MI, USA."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "IBM Research Europe, Rüschlikon, Switzerland."}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "IBM Research Europe, Rüschlikon, Switzerland."}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "IBM Thomas J. Watson Research Center, Yorktown Heights, NY, USA."}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "IBM Research Europe, Rüschlikon, Switzerland"}], "References": [{"Title": "3D-aCortex: an ultra-compact energy-efficient neurocomputing platform based on commercial 3D-NAND flash memories", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "014001", "JournalTitle": "Neuromorphic Computing and Engineering"}]}, {"ArticleId": 142810904, "Title": "Identifikasi Varietas Kayu Menggunakan Radially Average Power Spectrum Value Dan Orde Satu", "Abstract": "<p>Kayu merupakan material yang banyak dimanfaatkan dalam berbagai aspek kehidupan sehari-hari untuk kebutuhan manusia. Pengelompokan varietas kayu didasarkan beberapa parameter, diantaranya adalah sifat dan ciri-ciri struktur kayu. Kapasitas para ahli kayu untuk mengidentifikasi varietas kayu secara visual sering kali tidak akurat. Untuk mengatasi masalah ini, teknologi digunakan dalam bidang pengolahan citra digital untuk menganalisa tekstur kayu, untuk dapat mengklasifikasikannya ke dalam kelas-kelas tertentu. Penelitian ini mengusulkan penerapan cara ekstraksi atribut pada citra digital dengan menggunakan teknologi pengolahan citra digital menggunakan metode  radially average power spectrum value (RAPSV) dan orde satu, yang membuat pengukuran nilai parameter untuk mengekstrak fitur dari tekstur kayu. Hasil dari parameter radially average power spectrum value (RAPSV) dan orde satu, digunakan sebagai data untuk klasifikasi varietas kayu menggunakan metode support vektor machine (SVM). Penelitian ini menggunakan 4.200 citra dengan resolusi 1000x1000 piksel. Citra dibagi dalam data latih dan data uji menggunakan 10-fold validation. Pembandingan hasil diakukan dengan memproses citra dengan tahap perbaikani citra dan yang tidak menggunakan tahap perbaikan serta mengetahui efek dari ekstraksi fitur RAPSV. Penelitian ini menunjukkan bahwa tahap baikan citra dapat memberikan kenaikan akurasi sebesar 1% sedangkan penambahan fitur RAPSV memberikan kontribusi kenaikan akurasi sebesar 18% terhadap ekstraksi fitur orde satu.</p>", "Keywords": "", "DOI": "10.31598/sintechjournal.v7i3.1690", "PubYear": 2024, "Volume": "7", "Issue": "3", "JournalId": 56356, "JournalTitle": "SINTECH (Science and Information Technology) Journal", "ISSN": "2598-7305", "EISSN": "2598-9642", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142811099, "Title": "LibRPA: A Software Package for Low-scaling First-principles Calculations of Random Phase Approximation Electron Correlation Energy Based on Numerical Atomic Orbitals", "Abstract": "LibRPA is a software package designed for efficient calculations of random phase approximation (RPA) electron correlation energies from first principles using numerical atomic orbital (NAOs). Leveraging a localized resolution of identity (LRI) technique, LibRPA achieves O ( N 2 ) or better scaling behavior, making it suitable for large-scale calculation of periodic systems. Implemented in C++ and Python with MPI/OpenMP parallelism, LibRPA integrates seamlessly with NAO-based density functional theory (DFT) packages through flexible file-based and API-based interfaces. In this work, we present the theoretical framework, algorithm, software architecture, and installation and usage guide of LibRPA. Performance benchmarks, including the parallel efficiency with respect to the computational resources and the adsorption energy calculations for molecules on graphene, demonstrate its nearly ideal scalability and numerical reliability. LibRPA offers a useful tool for RPA-based calculations for large-scale extended systems. <b >Program summary</b> Program title: LibRPA CPC Library link to program files: https://doi.org/10.17632/kdwm5vzgk6.1 Developer's repository link: https://github.com/Srlive1201/LibRPA Licensing provisions: LGPL Programming language: C++, Fortran, Python Nature of problem: Calculating RPA electron correlation energies is computationally expensive, typically scaling as O ( N 4 ) with system size, hindering its application to large-scale materials science problems. Solution method: LibRPA utilizes the Localized Resolution of Identity (LRI) technique, reducing computational scaling to O ( N 2 ) or better. Implemented in C++ and Python with MPI/OpenMP parallelization, it integrates with NAO-based DFT packages, facilitating efficient and accurate RPA calculations for large-scale periodic systems.", "Keywords": "", "DOI": "10.1016/j.cpc.2024.109496", "PubYear": 2025, "Volume": "309", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CAS Key Laboratory of Quantum Information, University of Science and Technology of China, Hefei, 230026, Anhui, China;Institute of Physics, Chinese Academy of Sciences, Beijing, 100190, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics, Chinese Academy of Sciences, Beijing, 100190, China;The NOMAD Laboratory at the FHI Molecular Physics Department of the Max-Planck-Gesellschaft, Berlin-Dahlem, 14195, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics, Chinese Academy of Sciences, Beijing, 100190, China;Songshan Lake Materials Laboratory, Dongguan, 523808, Guangdong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "CAS Key Laboratory of Quantum Information, University of Science and Technology of China, Hefei, 230026, Anhui, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics, Chinese Academy of Sciences, Beijing, 100190, China;Corresponding author"}], "References": []}, {"ArticleId": 142811118, "Title": "An empirical study on knowledge workers’ subjective perspectives on digital platform work", "Abstract": "", "Keywords": "", "DOI": "10.1080/14626268.2024.2448124", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 2725, "JournalTitle": "Digital Creativity", "ISSN": "1462-6268", "EISSN": "1744-3806", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LUT Business School, LUT University, Lahti, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "LUT Business School, LUT University, Lappeenranta, Finland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LUT Business School, LUT University, Lahti, Finland"}], "References": [{"Title": "Platformic Management, Boundary Resources for Gig Work, and Worker Autonomy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "1-2", "Page": "153", "JournalTitle": "Computer Supported Cooperative Work (CSCW)"}, {"Title": "Exploring the boundaries and processes of digital platforms for knowledge work: A review of information systems research", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "101694", "JournalTitle": "The Journal of Strategic Information Systems"}, {"Title": "Dynamics of flexible work and digital platforms: Task and spatial flexibility in the platform economy", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "1", "Page": "100052", "JournalTitle": "Digital Business"}]}, {"ArticleId": 142811154, "Title": "Finite Element Analysis of Occupant Risk in Vehicular Impacts into Cluster Mailboxes", "Abstract": "<p>The deployment of cluster mailboxes (CMs) in the U.S. has raised safety concerns for passengers in potential vehicular crashes involving CMs. This study investigated the crashworthiness of two types of CMs through nonlinear finite element simulations. Two configurations of CM arrangements were considered: a single- and a dual-unit setup. These CM designs were tested on flat-road conditions with and without a curb. A 2010 Toyota Yaris and a 2006 Ford F250, both in compliance with the Manual for Assessing Safety Hardware (MASH), were employed in the analysis. The simulations incorporated airbag models, seatbelt restraint systems, and a Hybrid III 50th percentile adult male dummy. The investigations focused on evaluating the safety of vehicle occupants in 32 impact scenarios and under MASH Test Level 1 conditions (with an impact speed of 50 km/h). The simulation results provided insights into occupant risk and determined the primary failure mode of the CMs. No components of the mailboxes were found intruding into the vehicle’s occupant compartment. For all considered cases, the safety factors remained within allowable limits, indicating only a marginal risk of potential injury to occupants posed by the considered CMs.</p>", "Keywords": "", "DOI": "10.3390/computation13010012", "PubYear": 2025, "Volume": "13", "Issue": "1", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Tekirdag Namik Kemal University, Tekirdag 59030, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanics of Materials and Structures, Faculty of Civil and Environmental Engineering, Gdansk University of Technology, 80-222 Gdansk, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanics of Materials and Structures, Faculty of Civil and Environmental Engineering, Gdansk University of Technology, 80-222 Gdansk, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Manhattan University, Riverdale, NY 10471, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Liberty University, Lynchburg, VA 24515, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Liberty University, Lynchburg, VA 24515, USA"}], "References": []}, {"ArticleId": 142811262, "Title": "A Novel Multi-hop Query Answering System Based on a Large Knowledge Graph and Distributed Computing", "Abstract": "An automated query answering system\n (QAS) \n is a very useful application in organizations. Therefore, there is a lot of research to build, develop and improve it. In this article, we present a method to build a multi-hop query answering system\n (MQAS) \n based on a large knowledge graph\n (KG) \n ,\n Bidirectional Encoder Representations from Transformers (BERT) \n , and the indexing structure\n K-Dimensional Tree (KD-Tree) \n . The large KG provides knowledge for MQAS. BERT is used to transform questions into vectors. KD-Tree helps find the right answers quickly. On the other hand, we also propose a solution for distributed indexing of large vector spaces by building a distributed indexing structure called\n Distributed KD-Tree (DKD-Tree) \n . In addition, we also present experiments and evaluation results to demonstrate the effectiveness of our solution.", "Keywords": "", "DOI": "10.1145/3711824", "PubYear": 2025, "Volume": "24", "Issue": "3", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Phan Hong", "Affiliation": "Hoa Sen University,  Ho Chi Minh City, Viet Nam"}, {"AuthorId": 2, "Name": "Phuc Do", "Affiliation": "Information Technology, University of Information Technology (UIT), VNU-HCM, Vietnam, Ho Chi Minh City, Viet Nam"}], "References": [{"Title": "DMTree: A Novel Indexing Method for Finding Similarities in Large Vector Sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "639", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "High Performance Hadoop Distributed File System", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "119", "JournalTitle": "International Journal of Networked and Distributed Computing"}, {"Title": "Ontology-Mediated SPARQL Query Answering over Knowledge Graphs", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "", "Page": "100177", "JournalTitle": "Big Data Research"}, {"Title": "The coefficient of determination R-squared is more informative than SMAPE, MAE, MAPE, MSE and RMSE in regression analysis evaluation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e623", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 142811464, "Title": "Isles of autonomy: the rise of intelligent technologies", "Abstract": "<p>A critical metaphor for the development, implementation and penetration of autonomous machine systems into the world of human work is presented. Most especially, the '<i>Isles of Autonomy'</i> concept is articulated which argues that the expropriation of human pre-eminence will be marked by a series of threshold events, some of which are, even now becoming evident. In particular, it indicates that there will be a watershed event in which differing and distinct expressions of applied autonomous systems will spontaneously coalesce to produce an emergent, general artificial intelligence. The latter may well be unrelated to the original goals, aims and constraints of the disparate entities that have joined together. This threshold will be a harbinger of cascading unifications in which an unrestrained aggregate will assume <i>de facto</i> control over disparate work domains. The nature of such a development, most especially in light of associated human roles, is here evaluated. While emergent systems possess no necessary privilege, neither are their non-linear properties and behaviours directly inferable from their componential elements. The demi-sesquicentennial (75th) marking of the future of a science that is focused most especially on the predominance of human, work, is considered in light of these impending forces of change.</p>", "Keywords": "Automation;autonomous systems;human users;obligatory necessity", "DOI": "10.1080/00140139.2024.2447863", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Psychology, and Institute for Simulation and Training, University of Central Florida, Orlando, FL, USA"}], "References": [{"Title": "Human-Centered Artificial Intelligence: Reliable, Safe & Trustworthy", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "6", "Page": "495", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Human–Autonomy Teaming: A Review and Analysis of the Empirical Literature", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "5", "Page": "904", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Connecting ethics and epistemology of AI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "4", "Page": "1585", "JournalTitle": "AI & SOCIETY"}, {"Title": "Quintessential Solutions to Existential Problems : How Human Factors and Ergonomics Can and Should Address the Imminent Challenges of Our Times", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "6", "Page": "1657", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Are humans still necessary?", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "11", "Page": "1711", "JournalTitle": "Ergonomics"}]}, {"ArticleId": 142811499, "Title": "Multi-modal multi-objective wolf pack algorithm with circumferential scouting and intra-niche interactions", "Abstract": "The multi-objective wolf pack algorithm faces issues in solving multi-modal multi-objective optimization problems, such as the dominance of high-performing individuals, excessive reliance on the lead wolf, and ineffective learning among certain individuals, which result in poor diversity and convergence. Therefore, this paper presents a multi-modal multi-objective wolf pack algorithm with circumferential scouting and intra-niche interactions (MMOWPA-CN). To enhance algorithm's local search capability, a circumferential scouting technique is proposed. It divides the subpopulation using two different non-dominated levels of individuals, allowing individuals within the subpopulation to search in all directions, thereby improving the exploration capability. To prevent population from generating aggregation phenomenon, the neighbourhood elite bootstrapping strategy is introduced. It utilizes the neighbourhood lead wolf and the historical optimal solutions stored in archive to constrain individual movements, guiding them toward sparser areas and ensuring decision space's diversity. Additionally, in the decision space, a mechanism of intra-niche interactions is designed to avoid ineffective learning among individuals on different fronts. It allows individuals to interact with each other with information in a restricted area, ensuring the convergence of the algorithm. Comparative experiments on 17 test functions and one practical application have shown that MMOWPA-CN outperforms seven state-of-the-art algorithms, demonstrating its stronger optimization capabilities.", "Keywords": "", "DOI": "10.1016/j.swevo.2024.101842", "PubYear": 2025, "Volume": "93", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Nanchang Institute of Technology, Nanchang, 330099, China;Nanchang Key Laboratory of IoT Perception and Collaborative Computing for Smart City, Nanchang Institute of Technology, Nanchang, 330099, China;Nanchang Electric Power Key Facilities Intelligent Identification Engineering Technology Research Center, Nanchang, 330096, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Nanchang Institute of Technology, Nanchang, 330099, China;Nanchang Key Laboratory of IoT Perception and Collaborative Computing for Smart City, Nanchang Institute of Technology, Nanchang, 330099, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan, Hubei, 430074, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Nanchang Institute of Technology, Nanchang, 330099, China;Nanchang Key Laboratory of IoT Perception and Collaborative Computing for Smart City, Nanchang Institute of Technology, Nanchang, 330099, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Nanjing University of Information Science and Technology, Nanjing, 210044, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Nanchang Institute of Technology, Nanchang, 330099, China;Nanchang Key Laboratory of IoT Perception and Collaborative Computing for Smart City, Nanchang Institute of Technology, Nanchang, 330099, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "UniSA STEM, University of South Australia, Adelaide, SA, 5000, Australia"}], "References": [{"Title": "Preference-inspired coevolutionary algorithm with active diversity strategy for multi-objective multi-modal optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "1148", "JournalTitle": "Information Sciences"}, {"Title": "A clustering-based differential evolution algorithm for solving multimodal multi-objective optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100788", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Peeking beyond peaks: Challenges and research potentials of continuous multimodal multi-objective optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "105489", "JournalTitle": "Computers & Operations Research"}, {"Title": "An efficient slime mould algorithm for solving multi-objective optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115870", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-strategy ensemble firefly algorithm with equilibrium of convergence and diversity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108938", "JournalTitle": "Applied Soft Computing"}, {"Title": "A practical tutorial on solving optimization problems via PlatEMO", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "518", "Issue": "", "Page": "190", "JournalTitle": "Neurocomputing"}, {"Title": "Multi population-based chaotic differential evolution for multi-modal and multi-objective optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109909", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multimodal multi-objective optimization: Comparative study of the state-of-the-art", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "77", "Issue": "", "Page": "101253", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An Effective and Adaptable K-means Algorithm for Big Data Cluster Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "109404", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-objective firefly algorithm with adaptive region division", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110796", "JournalTitle": "Applied Soft Computing"}, {"Title": "A multi-modal multi-objective evolutionary algorithm based on dual decomposition and subset selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "84", "Issue": "", "Page": "101431", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Two-stage evolutionary algorithm with fuzzy preference indicator for multimodal multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "85", "Issue": "", "Page": "101480", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Growing Neural Gas Network-based surrogate-assisted Pareto set learning for multimodal multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "87", "Issue": "", "Page": "101541", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A dynamic-speciation-based differential evolution with ring topology for constrained multimodal multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "677", "Issue": "", "Page": "120879", "JournalTitle": "Information Sciences"}, {"Title": "Four development stages of collective intelligence", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "25", "Issue": "7", "Page": "903", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "Hierarchical learning multi-objective firefly algorithm for high-dimensional feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "165", "Issue": "", "Page": "112042", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 142811792, "Title": "Effectiveness of ICT in Investment Policy Decision-making for Economic Entities", "Abstract": "<p>The purpose of the study is to investigate the effectiveness of using information and communication technologies (ICT) to optimize investment policy decision-making for economic entities. The research tasks include analyzing the potential of ICT in enhancing investment decisions, performing a SWOT analysis of Kazakhstan's agricultural sector, comparing ICT use in the agricultural sectors of the United States and Kazakhstan, and developing strategies for integrating ICT in Kazakhstan's agricultural sector. The technical problem is to develop and implement ICT solutions that enhance investment policy decision-making in dynamic environments. The mathematical methods used include statistical analysis, optimization techniques, and predictive modeling to evaluate the effectiveness of ICT and develop implementation strategies in the agricultural sector. The study found significant potential for using modern information and communication technologies to enhance the investment policy decision-making of economic entities. These technologies facilitate the optimization of operations in a dynamic environment, improving production, marketing, business activity, and ultimately boosting the investment appeal of the entity.</p>", "Keywords": "", "DOI": "10.47839/ijc.23.4.3764", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142811797, "Title": "Information Technology of Thermodynamic Interaction of Laser Radiation Quality Upgrade while Drying Book Blocks", "Abstract": "<p>The article deals with the information technology of the structure and objects of thermodynamic interaction of book block drying processes. The article analyzes the literature on the selected research topic. It is found that to improve the quality of drying processes, convective-radiation drying with interruptions in irradiation of the spines of book blocks is a promising direction, which contributes to more intensive and faster moisture removal. A method for activating the drying processes of book blocks based on models of interaction 'glue - paper' with the use of infrared, ultraviolet, laser emitters is developed. The design of laser emitters for optimizing and intensifying the drying processes of book blocks is also developed by the authors and the necessary energy and spatial characteristics are substantiated. The paper presents the technical characteristics of the studied offset paper. The paper characterizes the images of the paper structure by thickness and, accordingly, the functions of laser beam power distribution when passing through the layers of paper sheets are obtained as a result of experiments.</p>", "Keywords": "", "DOI": "10.47839/ijc.23.4.3758", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>iia Lysa", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "The ethical thread: AI’s role in the tapestry of fashion", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-02151-3", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "“It wouldn't happen to me”: Privacy concerns and perspectives following the Cambridge Analytica scandal", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "102498", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": *********, "Title": "ACQC-LJP: Apollonius circle-based quantum clustering using Lennard-Jones potential", "Abstract": "Quantum Clustering (QC) is widely regarded as a powerful method in unsupervised learning problems. This method forms a potential function using a wave function as a superposition of Gaussian probability functions centered at data points. Clusters are then identified by locating the minima of the potential function. However, QC is highly sensitive to the kernel bandwidth parameter in the <PERSON><PERSON><PERSON><PERSON><PERSON> equation, which controls the shape of the Gaussian kernel, and affects the potential function's minima. This paper proposes an Apollonius Circle-based Quantum Clustering (ACQC) method using Lennard-Jones Potential (LJP), entitled ACQC-LJP, to address this limitation. ACQC-LJP introduces a novel approach to clustering by leveraging LJP to screen dense points and constructing Apollonius circle-based neighborhood groups, enabling the extraction of adaptive kernel bandwidths to effectively resolve the kernel bandwidth issue. Experimental results on real-world and synthetic datasets demonstrate that ACQC-LJP improves cluster detection accuracy by 50% compared to the original QC and by 10% compared to the ACQC method. Furthermore, the computational cost is reduced by more than 90% through localized calculations. ACQC-LJP outperforms state-of-the-art methods on diverse datasets, including those with small sample sizes, high feature variability, and imbalanced class distributions. These findings highlight the method's robustness and effectiveness across various challenging scenarios, marking it as a significant advancement in unsupervised learning. All the implementation source codes of ACQC-LJP are available at https://github.com/NAbdolmaleki/ACQC-LJP .", "Keywords": "", "DOI": "10.1016/j.patcog.2025.111342", "PubYear": 2025, "Volume": "161", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Faculty of Electrical and Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Faculty of Electrical and Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology and Computer Engineering, Azarbaijan Shahid Madani University, Tabriz, Iran;Artificial Intelligence and Machine Learning Research Laboratory, Azarbaijan Shahid Madani University, Tabriz, Iran;Corresponding author at: Faculty of Information Technology and Computer Engineering, Azarbaijan Shahid Madani University, Tabriz, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Technical and Vocational University (TVU), Tehran, Iran"}], "References": [{"Title": "Probabilistic quantum clustering", "Authors": "<PERSON><PERSON>-<PERSON><PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105567", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A systematic density-based clustering method using anchor points", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "400", "Issue": "", "Page": "352", "JournalTitle": "Neurocomputing"}, {"Title": "Density decay graph-based density peak clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "224", "Issue": "", "Page": "107075", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "CGFFCM: Cluster-weight and Group-local Feature-weight learning in Fuzzy C-Means clustering algorithm for color image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "108005", "JournalTitle": "Applied Soft Computing"}, {"Title": "A comprehensive survey of clustering algorithms: State-of-the-art machine learning applications, taxonomy, challenges, and future research prospects", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "104743", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A geometric-based clustering method using natural neighbors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "610", "Issue": "", "Page": "694", "JournalTitle": "Information Sciences"}, {"Title": "ACQC: Apollonius Circle‐based Quantum Clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "", "Page": "101877", "JournalTitle": "Journal of Computational Science"}, {"Title": "Advances in Quantum Machine Learning and Deep Learning for Image Classification: A Survey", "Authors": "<PERSON><PERSON>; <PERSON>; A<PERSON><PERSON>", "PubYear": 2023, "Volume": "560", "Issue": "", "Page": "126843", "JournalTitle": "Neurocomputing"}, {"Title": "visClust: A visual clustering algorithm based on orthogonal projections", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110136", "JournalTitle": "Pattern Recognition"}, {"Title": "Quantum clustering with k-Means: A hybrid approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "992", "Issue": "", "Page": "114466", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Survey of spectral clustering based on graph theory", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "151", "Issue": "", "Page": "110366", "JournalTitle": "Pattern Recognition"}, {"Title": "Efficient and robust clustering based on backbone identification", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "155", "Issue": "", "Page": "110635", "JournalTitle": "Pattern Recognition"}, {"Title": "Flexible density peak clustering for real-world data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "156", "Issue": "", "Page": "110772", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 142812162, "Title": "Realization of the physical to virtual connection for digital twin of construction crane", "Abstract": "A digital twin is an integrated multi-physics representation of a complex physical entity. This article develops the physical-to-virtual connection of the digital twin and proposes a framework for the construction of a tower crane digital twin. The main contributions of this paper include development of tower crane monitoring dataset, tower crane detection and tower crane operation mode recognition. By annotating >20,000 tower crane images in 583 tower crane videos, a tower crane image recognition dataset and a tower crane operating mode dataset are established. Yolov5x algorithm is used in the tower crane detection, and the test set detection accuracy is 93.85 %. After comparing the LSTM and CNN algorithms, 3DResNet algorithm is selected for tower crane operational mode recognition. The dataset is augmented by rotating the image and the final recognition accuracy reaches 87 %. These models can be installed on CCTV to monitor operational status of tower crane in real time and transfer relevant information to the virtual model. The tower crane in the virtual space completes the action of the physical tower crane, thereby realizing the physical-to-virtual mapping in the digital twin.", "Keywords": "Physical to virtual connection; Digital twin; Construction crane; Object detection", "DOI": "10.1016/j.jii.2025.100779", "PubYear": 2025, "Volume": "44", "Issue": "", "JournalId": 43270, "JournalTitle": "Journal of Industrial Information Integration", "ISSN": "2467-964X", "EISSN": "2452-414X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Lancaster University, Lancaster LA1 4YW, UK;Shanghai Waterway Engineering Design & Consulting Co., Ltd Pudong New District Shanghai 200120, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory for Digital Maintenance of Buildings and Infrastructure, School of Naval Architecture, Ocean and Civil Engineering, Shanghai Jiao Tong University, Shanghai 200240, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Engineering, Lancaster University, Lancaster LA1 4YW, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Waterway Engineering Design & Consulting Co., Ltd Pudong New District Shanghai 200120, PR China"}, {"AuthorId": 5, "Name": "Jianqiao YE", "Affiliation": "School of Engineering, Lancaster University, Lancaster LA1 4YW, UK;Corresponding author"}], "References": [{"Title": "Development of the simulation model for Digital Twin applications in historical masonry buildings: The integration between numerical and experimental reality", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "238", "Issue": "", "Page": "106282", "JournalTitle": "Computers & Structures"}, {"Title": "Multi-scale evolution mechanism and knowledge construction of a digital twin mimic model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102123", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Building a right digital twin with model engineering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "151", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Digital twin enhanced fault prediction for the autoclave with insufficient data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "350", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A review on anchor assignment and sampling heuristics in deep learning-based object detection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "506", "Issue": "", "Page": "96", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 142812172, "Title": "Tiny keys hold big secrets: On efficiency of Pairing-Based Cryptography in IoT", "Abstract": "Pairing-Based Cryptography (PBC) is a sub-field of elliptic curve cryptography that has been used to design ingenious security protocols including Short Signatures (SS), Identity-Based Encryption (IBE), and Attribute-Based Encryption (ABE). These protocols have extremely promising applications in diverse scenarios, including Internet of Things (IoT), which usually involves computing devices with limited processing, memory, and energy capabilities. Many studies in the literature evaluated the performance of PBC on typical IoT devices, giving promising results, and showing that a large class of constrained devices can run PBC schemes. However, in the last years, new advancements in Number Field Sieve algorithms threatened the security of PBC, so that all protocols must be re-parametrized with larger keys to maintain the same security level as before. Therefore, past literature reporting PBC performance on IoT devices must be redone because optimistic, and it is not clear whether present IoT devices will bear PBC. In this paper we evaluate the performance of some prominent PBC schemes on a very constrained device, namely the Zolertia RE-Mote platform, which is equipped with an ARM Cortex-M3 processor. From our experiments, the usage of IBE and SS schemes is still possible on IoT devices, but the security level is limited to 80 or 100 bits. Reaching greater security levels leads to higher execution times, which might not be compatible with many IoT applications. The usage of ABE is efficient only with IoT-oriented schemes, which offer good performance at the cost of a limited policy expressiveness.", "Keywords": "Pairing-based cryptography; Internet of things; Short signature; Identity-based encryption; Attribute-based encryption; Zolertia RE-mote", "DOI": "10.1016/j.iot.2025.101489", "PubYear": 2025, "Volume": "30", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering, University of Pisa, Largo Lucio <PERSON> 1, 56122, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering, University of Pisa, Largo Lucio Lazzarino 1, 56122, Italy"}], "References": [{"Title": "Performance evaluation of Attribute-Based Encryption on constrained IoT devices", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "151", "JournalTitle": "Computer Communications"}, {"Title": "The Contiki-NG open source operating system for next generation IoT devices", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "", "Page": "101089", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 142812192, "Title": "Exploring acridine-quinolinium conjugates as near-infrared photosensitizers for the Amyloid-β photo-oxidation", "Abstract": "Photo-oxidation of amyloid-β (Aβ) is an emerging and promising approach for the treatment of Alzheimer's disease (AD), which reduces the neurotoxicity of Aβ by oxidizing natural Aβ using highly efficient small-molecule photosensitizers that generate singlet oxygen (<sup>1</sup>O<sub>2</sub>) and inhibit its folding into β-structures or depolymerize Aβ aggregates. Therefore, developing efficient photosensitizers, especially near-infrared photosensitizers, is important and urgent for treating AD. In this work, to highly improve <sup>1</sup>O<sub>2</sub> generation efficiency of D-A photosensitizer QM21 previously reported by our group, by changing the donor to acridine and introducing a second acceptor, we rationally designed and synthesized the first examples of eight acridine-quinolinium-based conjugates to construct D-A and A-D-A type near-infrared photosensitizers (PXZ). These photosensitizers displayed long emission wavelengths, large Stokes shifts, significant fluorescence turn-on upon binding to Aβ aggregates, and excellent singlet oxygen production efficiency compared to QM21 due to acridine as the donor. Interestingly, through introducing a second acceptor, the A-D-A type photosensitizers with showed higher singlet oxygen production efficiency than those of the D-A types. Among them, PCZ series exhibited high singlet oxygen production efficiency, especially PCZ-2. It was verified at the cellular level that PCZ-2 could oxidize Aβ aggregates under light conditions and enhance their clearance via the microglia lysosomal pathway, thus reducing the neurotoxicity of Aβ aggregates. Therefore, this work provides a new approach for designing potential Aβ photosensitizers.", "Keywords": "", "DOI": "10.1016/j.snb.2025.137233", "PubYear": 2025, "Volume": "428", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "MOE International Joint Research Laboratory on Synthetic Biology and Medicines, School of Biology and Biological Engineering, South China University of Technology, Guangzhou 510006, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Analytical Science, Guangzhou Key Laboratory of Sensing Materials and Devices, Guangdong Engineering Technology Research Center for Photoelectric Sensing Materials and Devices, c/o School of Chemistry and Chemical Engineering, Guangzhou University, Guangzhou 510006, PR China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "MOE International Joint Research Laboratory on Synthetic Biology and Medicines, School of Biology and Biological Engineering, South China University of Technology, Guangzhou 510006, PR China;Corresponding authors"}], "References": [{"Title": "N-methylbenzothiazolium based theranostics as fluorescence imaging and photo-oxidation agents for Amyloid-β", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "414", "Issue": "", "Page": "135932", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 142812204, "Title": "Trabajo, acoso moral y estrategias defensivas en el transporte público", "Abstract": "<p lang=\"pt\"><p>Este estudo caracterizou o assédio moral e os aspectos do contexto de trabalho que favorecem essa violência, e identificou as estratégias de defesa utilizadas por trabalhadores de Transporte Coletivo Urbano de uma capital do sul do país. Na etapa quantitativa, 382 trabalhadores responderam à Escala Laboral de Assédio Moral e a questões relacionadas à violência, com posterior análise estatística. Na segunda etapa, dez trabalhadores foram entrevistados e realizou-se a análise de conteúdo. A presença de atos hostis revelou situações frequentes de abuso de poder e as condições e a organização do trabalho apareceram como fatores facilitadores do assédio moral no trabalho. Os trabalhadores demonstraram dificuldades em estruturar estratégias de enfrentamento, vivenciando o sofrimento de forma solitária. Estratégias de defesa concentraram-se na manutenção do emprego e no não enfrentamento da violência. Retratar o contexto de trabalho dessa categoria evidencia um cenário de violência naturalizada e desestruturação do coletivo de trabalhadores.</p></p> <p lang=\"es\"><p>Este estudio caracterizó el acoso y los aspectos del contexto laboral que favorecen esta violencia, e identificó las estrategias de defensa utilizadas por los trabajadores del transporte público urbano en una capital del sur del país. En la etapa cuantitativa, 382 trabajadores respondieron la Escala Laboral de Acoso Moral y preguntas relacionadas con la violencia, con posterior análisis estadístico. En la segunda etapa se entrevistó a diez trabajadores y se realizó un análisis de contenido. La presencia de actos hostiles reveló frecuentes situaciones de abuso de poder y las condiciones y organización del trabajo aparecieron como factores facilitadores del acoso moral. Los trabajadores demostraron dificultades en la estructuración de estrategias de enfrentamiento, experimentando el sufrimiento solo. Estrategias defensivas enfocadas en mantener el empleo y no enfrentar la violencia. Retratar el contexto de trabajo de esta categoría muestra un escenario de violencia naturalizada y desorganización del colectivo de trabajadores.</p></p>", "Keywords": "", "DOI": "10.5433/2236-6407.2023.v14.48100", "PubYear": 2023, "Volume": "14", "Issue": "", "JournalId": 10562, "JournalTitle": "Estudos Interdisciplinares em Psicologia", "ISSN": "", "EISSN": "2236-6407", "Authors": [{"AuthorId": 1, "Name": "Júlia Gonçalves", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142812211, "Title": "Caracterización de autores de violencia", "Abstract": "<p lang=\"pt\"><p>O objetivo deste estudo foi delinear o perfil de homens autores de violência doméstica (HAV) contra mulheres, atendidos nos Núcleos de Atendimento à Família e aos Autores de Violência Doméstica (NAFAVDs) do Distrito Federal. Foi realizada pesquisa quantitativa por meio da análise documental de 559 Formulários de Acolhimento de HAV utilizados em 2018 e 2019. Verificaram-se as seguintes características predominantes dos HAV: negros, com ensino médio completo, emprego formal e consumidores de bebida alcoólica. O tipo de violência mais perpetrada pelos HAV foi a violência psicológica. Não houve diferença significativa entre HAV negros e brancos em relação ao tipo de violência doméstica cometida. Tampouco houve diferença significativa entre o grau de escolaridade e violência.. É imprescindível que as intervenções propostas para HAV considerem as interseccionalidades e as especificidades que conformam a complexidade da violência contra mulheres.</p></p> <p lang=\"es\"><p>El objetivo de este estudio fue esbozar el perfil de hombres agressores culpables de violencia intrafamiliar (HAV) y atendidos en los Centros de Atención a la Familia y Autores de Violencia Doméstica del Distrito Federal de Brasília. La investigación cuantitativa se elaboró a través del análisis documental de 559 formularios de atención a hombres agressores, realizados en 2018 y 2019. Se encontraron las siguentes caracteristicas predominantes: hombres negros, con estudios secundarios completos y con empleo formal y consumidores de bebidas alcohólicas. La violencia más común en los casos fue la violencia psicologica. El tipo de violencia más perpetrado por HAV fue la violencia psicológica. No hubo diferencia significativa entre hombres blancos y negros en relación con el tipo de violencia doméstica cometida. Tampoco hubo diferencia significativa entre la violencia y el nivel de educación. Es fundamental que las intervenciones propuestas para atender a HAV consideren las interseccionalidades y especificidades que dan forma a la complejidad de la violencia contra las mujeres.</p></p>", "Keywords": "", "DOI": "10.5433/2236-6407.2023.v14.47729", "PubYear": 2023, "Volume": "14", "Issue": "", "JournalId": 10562, "JournalTitle": "Estudos Interdisciplinares em Psicologia", "ISSN": "", "EISSN": "2236-6407", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Valeska Zanello", "Affiliation": ""}], "References": []}, {"ArticleId": 142812212, "Title": "Participación comunitaria em la Justicia Restaurativa: revisión integrativa", "Abstract": "<p lang=\"pt\"><p>A justiça restaurativa (JR) propõe um novo paradigma sobre justiça fundamentado em uma abordagem comunitária que implica na participação da vítima, ofensor(a) e comunidade. O objetivo deste trabalho foi a realização de uma revisão integrativa sobre a participação da comunidade na JR. Foram selecionados 11 artigos a partir do levantamento nas bases de dados: Biblioteca Virtual em Saúde (BVS), PsycINFO, PubMED, Scopus, Web of Science. Os resultados indicam a adoção de concepções tradicionais de comunidade circunscritas à delimitação geográfica e das relações interpessoais. A participação comunitária tem sido descrita em sua dimensão interpessoal e em sua dimensão organizacional e agente de transformação, assumindo o papel de protagonista da JR. Portanto, os desafios da participação comunitária na JR envolvem superar a dimensão interpessoal ao ampliar a compreensão da comunidade para a sociedade civil e incorporar as questões estruturais que atravessam as injustiças sociais.</p></p> <p lang=\"es\"><p>La Justicia Restaurativa (JR) propone un nuevo paradigma acerca de la justicia razonado en un enfoque comunitario que implica la participación de la víctima, del agresor y de la comunidad. El objetivo del estudio fue una revisión integradora acerca de la participación comunitaria en la JR, fueron seleccionados 11 artículos de las búsquedas en las bases de datos: Biblioteca Virtual en Salud (BVS), PsycINFO, PubMED, Scopus y Web of Science. Los resultados apuntan la adopción de concepciones de comunidad circunscritos a la demarcación geográfica, a la relación interpersonal y a la sociedad civil.  La participación comunitaria describirse en sus dimensiones interpersonal y organizacional, mediadora de transformación, haciéndose cargo del protagonista de la JR. Por lo tanto, los desafíos de la participación comunitaria abarcan sobrellevar la dimensión intrapersonal ampliando el entendimiento de comunidad para la sociedad civil y agregar las cuestiones estructurales qué atraviesan las injusticias sociales.</p></p>", "Keywords": "", "DOI": "10.5433/2236-6407.2023.v14.47991", "PubYear": 2023, "Volume": "14", "Issue": "", "JournalId": 10562, "JournalTitle": "Estudos Interdisciplinares em Psicologia", "ISSN": "", "EISSN": "2236-6407", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142812214, "Title": "Estigma social y lepra: identificación de conocimiento como estrategia de educación sanitaria", "Abstract": "<p lang=\"pt\"><p>A hanseníase é marcada por fatores sociais e culturais que contribuíram à estigmatização. Este estudo busca identificar o conhecimento a respeito da doença, por meio de um instrumento tipo Likert. Os resultados demonstraram (N = 112) que a transmissão foi o tópico mais controverso, pois 51,8% dos participantes declararam conhecer o meio de transmissão; ao mesmo tempo que 59,4% sinalizaram que a transmissão se dá por contato com a pele. O estudo também evidencia a necessidade de conhecimento sobre o tratamento e sintomas da doença, pois 33% declararam conhecer o tratamento; e 56,3% declararam conhecer pouco os sintomas. Os participantes demonstraram não depositar estigmatização do corpo ao observar lesões cutâneas, assim como não associam crenças religiosas a uma justificativa de manifestação da doença ou como recurso de cura. Esse trabalho aponta a importância do conhecimento como estratégia de educação em saúde para desmistificação de estigmas socialmente construídos.</p></p> <p lang=\"es\"><p>La lepra está marcada por factores sociales y culturales que contribuyeron a su estigmatización. Este estudio pretende identificar el conocimiento sobre la enfermedad, mediante un instrumento tipo Likert. Los resultados mostraron (N = 112) que la transmisión era el tema más controvertido, ya que el 51,8% de los participantes declararon conocer los medios de transmisión; al mismo tiempo, el 59,4% señalaron que la transmisión se produce por contacto cutáneo. El estudio también destaca la necesidad de conocer el tratamiento y los síntomas de la enfermedad, ya que el 33% declaró conocer el tratamiento y el 56,3% declaró saber poco sobre los síntomas. Los participantes demuestran no depositar estigmatización del cuerpo al observar lesiones cutáneas, así como no asociar creencias religiosas a una justificación de la manifestación de la enfermedad o como recurso curativo. Este estudio señala la importancia del conocimiento como estrategia de educación sanitaria para desmitificar los estigmas construidos socialmente.</p></p>", "Keywords": "", "DOI": "10.5433/2236-6407.2023.v14.48115", "PubYear": 2023, "Volume": "14", "Issue": "", "JournalId": 10562, "JournalTitle": "Estudos Interdisciplinares em Psicologia", "ISSN": "", "EISSN": "2236-6407", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Valdinei Santos de Aguiar Junior", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142812217, "Title": "El ser creativo em la posmodernidad: la invitación em “El Principito”", "Abstract": "<p lang=\"pt\"><p>A criatividade é fundamental para a subjetividade, sendo demarcadora de uma vida que vale a pena ser vivida. Seu desenvolvimento é dependente e condicionado por diversos processos que ocorrem na infância dos sujeitos. À vista disso, surge a questão: as novas formas de subjetivação que emergem da pós-modernidade afetam tais processos e, se afetam negativamente, podem ser manejadas? Para compreender melhor a correlação entre a pós-modernidade e o desenvolvimento subjetivo empregou-se uma metodologia de ordem psicanalítica teórico-reflexiva, pautada<PERSON> em <PERSON>, <PERSON><PERSON><PERSON>, Lacan e autores da sociologia, utilizando a obra “O Pequeno Príncipe” como fio condutor das construções propostas. A partir desta investigação, nota-se que a pós-modernidade produz danos em aspectos fundamentais do desenvolvimento subjetivo como um empobrecimento do registro simbólico, um individualismo exacerbado, uma cultura narcisista e o adoecimento do laço social. Nessa perspectiva, conclui-se que uma possível saída para o adoecimento subjetivo pós-moderno é o (re)enlace do laço social.</p></p> <p lang=\"es\"><p>La creatividad es fundamental para la subjetividad, siendo el marcador de una vida que vale la pena vivir. Su desarrollo es dependiente y condicionado por varios procesos que ocurren en la infancia de los sujetos. Ante esto, surge la pregunta: ¿las nuevas formas de subjetivación que emergen de la posmodernidad afectan dichos procesos y, si lo hacen negativamente, pueden ser gestionadas? Para comprender mejor la correlación entre posmodernidad y desarrollo subjetivo, se utilizó una metodología psicoanalítica teórico-reflexiva, basada en Freud, Winnicott, Lacan y autores de sociología, utilizando la obra “El Principito” como hilo conductor de las propuestas de construcción. De esta investigación se desprende que la posmodernidad causa daños a aspectos fundamentales del desarrollo subjetivo como un empobrecimiento del registro simbólico, un individualismo exacerbado, una cultura narcisista y el debilitamiento del vínculo social. Desde esta perspectiva, se concluye que una posible salida a la enfermedad subjetiva posmoderna es el (re)compromiso del vínculo social.</p></p>", "Keywords": "", "DOI": "10.5433/2236-6407.2023.v14.51789", "PubYear": 2023, "Volume": "14", "Issue": "", "JournalId": 10562, "JournalTitle": "Estudos Interdisciplinares em Psicologia", "ISSN": "", "EISSN": "2236-6407", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 142812264, "Title": "Comprehensive Dataset for Fault Detection and Diagnosis in Inverter-Driven Permanent Magnet Synchronous Motor Systems", "Abstract": "<p>This work introduces a new, comprehensive dataset for Fault Detection and Diagnosis (FDD) in inverter-driven Permanent Magnet Synchronous Motor (PMSM) systems. Despite the increasing significance of AI-driven FDD techniques, the domain suffers from a lack of publicly accessible, real-world datasets for algorithm development and evaluation. Our contribution fills this gap by offering a comprehensive, multi-sensor dataset obtained from a bespoke experimental apparatus. The dataset includes different fault cases, such as open-circuit faults, short-circuit faults, and overheating conditions in the inverter switches. The dataset incorporates 8 raw sensor measurements and 15 derived features, recorded at 10 Hz, amounting to 10,892 samples across 9 operational conditions (one normal, eight fault types). By keeping this dataset publicly accessible, we seek to accelerate research in AI-driven fault identification and diagnosis for electric drive systems.</p><p>© 2025 The Authors.</p>", "Keywords": "Dataset;Diagnosis;Experimental setup;Fault detection;Inverter-driven;Machine learning;PMSM", "DOI": "10.1016/j.dib.2025.111286", "PubYear": 2025, "Volume": "58", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "École Nationale Supérieure d'Électricité et de Mécanique, Hassan II university of Casablanca, Morocco. ;Institut Supérieur d'Etudes Maritimes, Casablanca, Morocco."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>i", "Affiliation": "Faculty of Sciences, Hassan II University of Casablanca, Morocco."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory of Engineering, Industrial Management and Innovation, Hassan First University, Morocco."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Sciences, Hassan II University of Casablanca, Morocco."}], "References": []}]