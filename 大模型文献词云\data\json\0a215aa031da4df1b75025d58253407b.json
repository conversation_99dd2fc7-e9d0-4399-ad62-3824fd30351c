[{"ArticleId": 117611127, "Title": "The Effect of Dynamic Effects and Color Transparency of AR-HUD Navigation Graphics on Driving Behavior Regarding Inattentional Blindness", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2400376", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "Guanhua Hou", "Affiliation": "School of Art and Design, Guangzhou University, Guangzhou, China;Pan Tianshou College of Architecture, Art and Design, Ningbo University, Ningbo, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Pan Tianshou College of Architecture, Art and Design, Ningbo University, Ningbo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Art and Design, Guangzhou University, Guangzhou, China"}], "References": [{"Title": "Assessing Distraction Potential of Augmented Reality Head-Up Displays for Vehicle Drivers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "5", "Page": "852", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Determining the impact of augmented reality graphic spatial location and motion on driver behaviors", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "103510", "JournalTitle": "Applied Ergonomics"}, {"Title": "Inattentional Blindness in Augmented Reality Head-Up Display-Assisted Driving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "9", "Page": "837", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "How does navigating with Augmented Reality information affect drivers’ glance behaviour in terms of attention allocation?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "Effects of button colour and background on augmented reality interfaces", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "4", "Page": "663", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Effect of Transparency Levels and Real-World Backgrounds on the User Interface in Augmented Reality Environments", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "16", "Page": "4265", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Assessing Gender Perception Differences in Color Combinations in Digital Visual Interfaces Using Eye tracking – The Case of HUD", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "20", "Page": "6591", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 117611139, "Title": "A Novel Linear Discriminant Analysis Based on Alternate Ratio Sum Minimization", "Abstract": "Linear discriminant analysis (LDA) and its variants are popular supervised dimension reduction methods, which have been widely used to handle high-dimensional data. Since most of previous LDA methods are developed based on the trace ratio (TR) criterion, they usually obtain the low-dimensional data feature with weak discriminative ability, due to the projections with small variance. The ratio sum (RS) criterion was developed to alleviate this drawback. However, the conventional ratio sum is formulated to maximize the arithmetic mean of items which suffers from the domination of the largest objectives and might deteriorate the recognition accuracy in practical applications. In this paper, a novel ratio sum minimization based linear discriminant analysis (RSM-LDA) method is proposed in this paper. Specifically, a new ratio sum minimization (RSM) criterion is developed, which is based on the properties of the harmonic mean and effectively avoids the dominance problem to discover more discriminative features of the data. However, obtaining a closed solution for the RSM-LDA problem is challenging. For this purpose, three optimization methods are used to solve the optimization problem of RSM-LDA, and the inherent relationships between the methods are discussed. Experimental results demonstrate that the proposed RSM-LDA method has better performance in classification tasks on several datasets, when compared with some comparison methods based dimensionality reduction methods. In addition, combined with other experimental results, the effectiveness of RSM-LDA was verified.", "Keywords": "", "DOI": "10.1016/j.ins.2024.121444", "PubYear": 2025, "Volume": "689", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Guangdong University of Technology, Guangzhou, Guangdong 510006, China;PengCheng Laboratory, Shenzhen, Guangdong 518000, China;Institute of Advanced Photonics Technology, Guangzhou, Guangdong 510006, China;Key Laboratory of Photonic Technology for Integrated Sensing and Communication, Ministry of Education of China, Guangzhou, Guangdong 510006, China;Guangdong Provincial Key Laboratory of Information Photonics Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Optics and Electronics (iOPEN), and the Key Laboratory of Intelligent Interaction and Applications (Ministry of Industry and Information Technology), Northwestern Polytechnical University, Shanxi, Xi'an 710072, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-sen University, Guangzhou 510006, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Optics and Electronics (iOPEN), and the Key Laboratory of Intelligent Interaction and Applications (Ministry of Industry and Information Technology), Northwestern Polytechnical University, Shanxi, Xi'an 710072, China;Corresponding author"}], "References": [{"Title": "Local structured feature learning with dynamic maximum entropy graph", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107673", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 117611241, "Title": "Retraction Note: A novel data privacy-preserving protocol for multi-data users by using genetic algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-024-10156-9", "PubYear": 2024, "Volume": "28", "Issue": "S1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON> of Engineering, Karur, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, M<PERSON> of Engineering, Karur, India"}], "References": []}, {"ArticleId": 117611399, "Title": "A GLR-like Parsing Algorithm for Three-Valued Interpretations of Boolean Grammars with Strong Negation", "Abstract": "Boolean grammars generalize context-free rewriting by extending the possibilities when dealing with different rules for the same nonterminal symbol. By allowing not only disjunction (as in the case of usual context-free grammars), but also conjunction and negation as possible connections between different rules with the same left-hand side, they are able to simplify the description of context-free languages and characterize languages that are not context-free. The use of negation, however, leads to the possibility of introducing rules that interplay in such a way which is problematic to handle in the classical, two-valued logical setting. Here we define a three valued interpretation to deal with such contradictory grammars using a method introduced originally in the context of logic programming, and present an algorithm to determine the membership status of strings with respect to the resulting three valued languages.", "Keywords": "", "DOI": "10.4204/EPTCS.407.3", "PubYear": 2024, "Volume": "407", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Debrecen, Faculty of Informatics"}, {"AuthorId": 2, "Name": "György Vaszil", "Affiliation": "University of Debrecen, Faculty of Informatics"}], "References": []}, {"ArticleId": 117611436, "Title": "Label distribution learning for compound facial expression recognition in‐the‐wild: A comparative study", "Abstract": "Human emotional states encompass both basic and compound facial expressions. However, current works primarily focus on basic expressions, consequently neglecting the broad spectrum of human emotions encountered in practical scenarios. Compound facial expressions involve the simultaneous manifestation of multiple emotions on an individual's face. This phenomenon reflects the complexity and richness of human states, where facial features dynamically convey a combination of feelings. This study embarks on a pioneering exploration of Compound Facial Expression Recognition (CFER), with a distinctive emphasis on leveraging the Label Distribution Learning (LDL) paradigm. This strategic application of LDL aims to address the ambiguity and complexity inherent in compound expressions, marking a significant departure from the dominant Single Label Learning (SLL) and Multi‐Label Learning (MLL) paradigms. Within this framework, we rigorously investigate the potential of LDL for a critical challenge in Facial Expression Recognition (FER): recognizing compound facial expressions in uncontrolled environments. We utilize the recently introduced RAF‐CE dataset, meticulously designed for compound expression assessment. By conducting a comprehensive comparative analysis pitting LDL against conventional SLL and MLL approaches on RAF‐CE, we aim to definitively establish LDL's superiority in handling this complex task. Furthermore, we assess the generalizability of LDL models trained on RAF‐CE by evaluating their performance on the EmotioNet and RAF‐DB Compound datasets. This demonstrates their effectiveness without domain adaptation. To solidify these findings, we conduct a comprehensive comparative analysis of 12 cutting‐edge LDL algorithms on RAF‐CE, S‐BU3DFE, and S‐JAFFE datasets, providing valuable insights into the most effective LDL techniques for FER in‐the‐wild.", "Keywords": "", "DOI": "10.1111/exsy.13724", "PubYear": 2025, "Volume": "42", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut Supérieur d'Informatique, Research Team on Intelligent Systems in Imaging and Artificial Vision (SIIVA), LR16ES06 Laboratoire de recherche en Informatique, Modélisation et Traitement de l'Information et de la Connaissance (LIMTIC) Université de Tunis El Manar  Ariana Tunisia;Higher Institute of Technological Studies of Mahdia  Mahdia Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Supérieur d'Informatique, Research Team on Intelligent Systems in Imaging and Artificial Vision (SIIVA), LR16ES06 Laboratoire de recherche en Informatique, Modélisation et Traitement de l'Information et de la Connaissance (LIMTIC) Université de Tunis El Manar  Ariana Tunisia;Ecole Nationale d'Ingénieurs de Carthage Université de Carthage  Tunis‐Carthage Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut Supérieur d'Informatique, Research Team on Intelligent Systems in Imaging and Artificial Vision (SIIVA), LR16ES06 Laboratoire de recherche en Informatique, Modélisation et Traitement de l'Information et de la Connaissance (LIMTIC) Université de Tunis El Manar  Ariana Tunisia;Ecole Nationale d'Ingénieurs de Carthage Université de Carthage  Tunis‐Carthage Tunisia"}], "References": [{"Title": "Decomposition-Fusion for Label Distribution Learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "64", "JournalTitle": "Information Fusion"}, {"Title": "A genetic programming-based feature selection and fusion for facial expression recognition", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107173", "JournalTitle": "Applied Soft Computing"}, {"Title": "Subject-dependent selection of geometrical features for spontaneous emotion recognition", "Authors": "Ones Sidhom; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "2", "Page": "2635", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Real emotion seeker: recalibrating annotation for facial expression recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "1", "Page": "139", "JournalTitle": "Multimedia Systems"}, {"Title": "Multichannel convolutional neural network for human emotion recognition from in-the-wild facial expressions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "11", "Page": "5693", "JournalTitle": "The Visual Computer"}, {"Title": "Three-phases hybrid feature selection for facial expression recognition", "Authors": "Ones Sidhom; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "6", "Page": "8094", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Joint recognition of basic and compound facial expressions by mining latent soft labels", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110173", "JournalTitle": "Pattern Recognition"}, {"Title": "Facial Emotion Recognition in-the-Wild Using Deep Neural Networks: A Comprehensive Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Challenges and Emerging Trends for Machine Reading of the Mind from Facial Expressions", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 117611462, "Title": "Evolving intra-and inter-session graph fusion for next item recommendation", "Abstract": "Next-item recommendation aims to predict users’ subsequent behaviors using their historical sequence data. However, sessions are often anonymous, short, and time-varying, making it challenging to capture accurate and evolving item representations. Existing methods using static graphs may fail to model the evolving semantics of items over time. To address this problem, we propose the Evolving Intra-session and Inter-session Graph Neural Network (EII-GNN) to capture the evolving item semantics by fusing global and local graph information. EII-GNN utilizes a global dynamic graph to model inter-session item transitions and update item embeddings at each timestamp. It also constructs a per-session graph with shortcut edges to learn complex intra-session patterns. To personalize recommendations, a history-aware GRU applies the user’s past sessions. We fuse the inter-session graph, intra-session graph, and history embeddings to obtain the session representation for final recommendation. Our model performed well in experiments with three real-world data sets against its state-of-the-art counterparts.", "Keywords": "", "DOI": "10.1016/j.inffus.2024.102691", "PubYear": 2025, "Volume": "114", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Management, National Yang Ming Chiao Tung University, No. 1001, Daxue Rd. East Dist., Hsinchu 300093, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>ao<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Service Science, National Tsing Hua University, 101, Section 2, Kuang-Fu Road, Hsinchu 300044, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management and Finance, National Yang Ming Chiao Tung University, No. 1001, Daxue Rd. East Dist., Hsinchu 300093, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management and Finance, National Yang Ming Chiao Tung University, No. 1001, Daxue Rd. East Dist., Hsinchu 300093, Taiwan;Corresponding author"}], "References": [{"Title": "A Survey on Bias and Fairness in Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Attentive gated graph sequence neural network-based time-series information fusion for financial trading", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "261", "JournalTitle": "Information Fusion"}, {"Title": "Dual-grained human mobility learning for location-aware trip recommendation with spatial–temporal graph knowledge fusion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "46", "JournalTitle": "Information Fusion"}, {"Title": "Multi-dimensional shared representation learning with graph fusion network for Session-based Recommendation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "205", "JournalTitle": "Information Fusion"}, {"Title": "Reinforced PU-learning with Hybrid Negative Sampling Strategies for Recommendation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Hierarchical Reinforcement Learning for Conversational Recommendation With Knowledge Graph Reasoning and Heterogeneous Questions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "5", "Page": "3439", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Heterogeneous and clustering-enhanced personalized preference transfer for cross-domain recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101892", "JournalTitle": "Information Fusion"}, {"Title": "Prompt-based and weak-modality enhanced multimodal recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "101", "Issue": "", "Page": "101989", "JournalTitle": "Information Fusion"}, {"Title": "Modified node2vec and attention based fusion framework for next POI recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "101", "Issue": "", "Page": "101998", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 117611516, "Title": "MaRTIn – Massive Recursive Tensor Integration", "Abstract": "We present MaRTIn , an extendable all-in-one package for calculating amplitudes up to two loops in an expansion in external momenta or using the method of infrared rearrangement. Renormalisable and non-renormalisable models can be supplied by the user; an implementation of the Standard Model is included in the package. In this manual, we discuss the scope and functionality of the software, and give instructions of its use.", "Keywords": "Loop calculation; Perturbation theory; Momentum expansion; Infrared rearrangement", "DOI": "10.1016/j.cpc.2024.109372", "PubYear": 2025, "Volume": "306", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Physics, University of Cincinnati, Cincinnati, OH 45221, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Max Planck Computing and Data Facility, D-85748 Garching, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Physics, TU Dortmund, D-44221 Dortmund, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Physics, University of Cincinnati, Cincinnati, OH 45221, USA;Department of Physics, TU Dortmund, D-44221 Dortmund, Germany;Corresponding author at: Department of Physics, TU Dortmund, D-44221 Dortmund, Germany"}], "References": [{"Title": "FIRE6: <PERSON><PERSON>man Integral REduction with modular arithmetic", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "247", "Issue": "", "Page": "106877", "JournalTitle": "Computer Physics Communications"}, {"Title": "Forcer, a Form program for the parametric reduction of four-loop massless propagator diagrams", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>.<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "253", "Issue": "", "Page": "107198", "JournalTitle": "Computer Physics Communications"}, {"Title": "Integral reduction with Kira 2.0 and finite field methods", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "266", "Issue": "", "Page": "108024", "JournalTitle": "Computer Physics Communications"}, {"Title": "tapir: A tool for topologies, amplitudes, partial fraction decomposition and input for reductions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "282", "Issue": "", "Page": "108544", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 117611700, "Title": "Design and FPGA implementation of nested grid multi-scroll chaotic system", "Abstract": "Conventional multi-scroll chaotic systems are often constrained by the number of attractors and the complexity of generation, making it challenging to meet the increasing demands of communication and computation. This paper revolves around the modified <PERSON><PERSON>’s system. By modifying its differential equation and introducing traditional nonlinear functions, such as the step function sequence and sawtooth function sequence. A nested grid multi-scroll chaotic system (NGMSCS) can be established, capable of generating nested grid multi-scroll attractors. In contrast to conventional grid multi-scroll chaotic attractors, scroll-like phenomena can be initiated outside the grid structure, thereby revealing more complex dynamic behavior and topological features. Through the theoretical design and analysis of the equilibrium point of the system and its stability, the number of saddle-focused equilibrium points of index 2 is further expanded, which can generate (2 N+2) × M attractors, and the formation mechanism is elaborated and verified in detail. In addition, the generation of an arbitrary number of equilibrium points in the y -direction is achieved by transforming the x and y variables, which can generate M×(2 N+2) attractors, increasing the complexity of the system. The system’s dynamical properties are discussed in depth via time series plots, L<PERSON><PERSON>nov exponents, Poincaré cross sections, 0–1 tests, bifurcation diagrams, and attraction basins. The existence of attractors is confirmed through numerical simulations and FPGA-based hardware experiments.", "Keywords": "Grid multi-scroll chaotic attractor; <PERSON><PERSON>’s system; FPGA; Equilibrium point", "DOI": "10.1016/j.jksuci.2024.102186", "PubYear": 2024, "Volume": "36", "Issue": "8", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Guo<PERSON> Yu", "Affiliation": "School of Electrical Engineering College, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering College, Heilongjiang University, Harbin 150080, China;Corresponding author"}, {"AuthorId": 3, "Name": "Jiale Xi", "Affiliation": "School of Electrical Engineering College, Heilongjiang University, Harbin 150080, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering College, Heilongjiang University, Harbin 150080, China"}], "References": [{"Title": "Finite-time tracking control of heterogeneous multi-AUV systems with partial measurements and intermittent communication", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "67", "Issue": "5", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 117611713, "Title": "Modelling framework to predict shallow-to-deep geometries of milled pockets by incorporating the effect of waterjet flow on nonplanar target", "Abstract": "<p>Milling shallow-to-deep features in titanium alloy presents machinability challenges when a conventional process is employed. In this context, maskless milling by abrasive waterjet (AWJ) can be a better alternative due to its unique capabilities. Unlike conventional milling, assuming a linear summation of kerf cross-section profiles (CPs) while following the raster jet path, in AWJ milling will not yield an accurate prediction of surface geometry CP due to the nonlinearities involved in material erosion by multiple phenomena ( e.g. , reflected jets). The unavailability of measurement techniques for gaining insights into this complex AWJ interaction with the target at the milling zone for studying the flow behavior and the associated erosion hindered the development of accurate models for AWJ milling. This necessitates the need for the AWJ milling models that incorporate the abovementioned nonlinearities to yield reliable predictions. Towards this, the present work proposes a novel modelling framework to predict the geometrical characteristics (CP, maximum erosion depth ( h <sub>max</sub>), top width ( W <sub>P</sub>), and CP area ( A <sub>P</sub>)) of AWJ milled feature in Ti-6Al-4V alloy, considering pocket as a case study under varying jet’s overlap percentage ( JO %) and jet traverse rate ( V <sub>f</sub>). To get insights into the changes in the AWJ flow behavior and its interactions upon impinging the nonplanar target (NPT) post-first jet sweep and subsequent jet sweeps, computational fluid dynamics (CFD) simulations are performed. Results show an increasing asymmetric stagnation zone at the NPT relative to the planar target (PT), with the increased JO % and V <sub> f </sub> leading to strong tangential flow, causing intense jet reflection and unwanted erosion. These understandings are utilized to mathematically model the unexplained erosion caused by the reflected jets, enabling accurate prediction of CP, h <sub>max</sub>, W <sub>P</sub>, and A <sub>P</sub>. The proposed model accurately captured the nonlinearities that arose in h <sub>max</sub>, W <sub>P</sub>, and A <sub>P</sub> with a maximum error of 10%, 11.8%, and 14.8%, respectively. In addition, the validated shapes of the pocket CPs result in an average mean absolute error of 42 µm, which agrees well with experimentally obtained ones. The validation results corroborate the hypothesis that the reflected jets caused upon impinging on the NPT compared to PT significantly contribute to unexplained erosion during milling other than the erosion due to the primary jets.</p>", "Keywords": "AWJ milling; CFD simulations; Offset-jet passes; Stagnation zone; Tangential flows; Reflected-jets erosion", "DOI": "10.1007/s00170-024-14364-3", "PubYear": 2024, "Volume": "134", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Indian Institute of Technology Madras, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Indian Institute of Technology Madras, Chennai, India; Corresponding author."}], "References": [{"Title": "An investigation into the abrasive waterjet milling circular pocket on titanium alloy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "11-12", "Page": "4503", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Modelling and simulation of controlled depth abrasive water jet machining (AWJM) for roughing passes of free-form surfaces", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "11-12", "Page": "3581", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "High-efficiency abrasive water jet milling of aspheric RB-SiC surface based on BP neural network depth control models", "Authors": "Hong<PERSON> Deng; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "7-8", "Page": "3133", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Integration of CFD simulated abrasive waterjet flow dynamics with the material removal model for kerf geometry prediction in overlapped erosion on Ti-6Al-4V alloy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "127", "Issue": "", "Page": "102788", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "A generic model for prediction of kerf cross-sectional profile in multipass abrasive waterjet milling at macroscopic scale by considering the jet flow dynamics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "127", "Issue": "5-6", "Page": "2815", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 117611781, "Title": "Cross-device free-text keystroke dynamics authentication using federated learning", "Abstract": "<p>Free-text keystroke dynamics, the unique typing patterns of an individual, have been applied for the security of mobile devices by providing the non-intrusive and continuous user authentication. Existing authentication approaches mainly concentrate on the keystroke dynamics when operating a specific device, and overlook the generality of keystroke dynamics for cross-device user authentication. To tackle this problem, in this paper, we propose an efficient federated free-text keystroke dynamics mechanism to mitigate the difference in keyboards for cross-device authentication. Specifically, we explore and analyze the keystroke features of various keyboards and extract cross-device keystroke features. To protect user privacy, their type of rhythm information must be kept locally. We utilize federated learning based on the auxiliary model to train the authentication model. Our proposed solution was evaluated on a large-scale data set with 168,000 users. The experimental results show that our proposed solution performs well with great robustness across different types of keyboards.</p>", "Keywords": "Free-text keystroke dynamics; Cross devices; Editing features; Federated learning", "DOI": "10.1007/s00779-024-01832-6", "PubYear": 2024, "Volume": "28", "Issue": "3-4", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, China"}], "References": [{"Title": "Discriminative Power of Typing Features on Desktops, Tablets, and Phones for User Identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Privacy and Security"}, {"Title": "Freely typed keystroke dynamics-based user authentication for mobile devices based on heterogeneous features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107556", "JournalTitle": "Pattern Recognition"}, {"Title": "Dynamic keystroke pattern analysis and classifiers with competence for user recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106902", "JournalTitle": "Applied Soft Computing"}, {"Title": "Continuous authentication using deep neural networks ensemble on keystroke dynamics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e525", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 117611782, "Title": "Performance-complementary colorimetric/electrochemical bimodal detection of Hg2+ based on analyte-accelerated peroxidase-mimicking activity of GO-AuNPs", "Abstract": "As a highly toxic heavy metal pollutant, mercury ions cause substantial risks to the environment and human health, and developing high-performance and convenient analytical approaches becomes quite important. Here we propose a performance-complementary colorimetric/electrochemical dual-mode Hg<sup>2+</sup> sensing method based on the analyte accelerating the peroxidase-mimetic activity of gold nanoparticles (AuNPs) loaded on graphene oxide (GO). The GO-AuNPs composite was readily fabricated via mechanically mixing AuNPs and GO with controllable proportions, exhibiting a weak peroxidase-like activity in catalyzing the oxidation reaction of 3,3′,5,5′-tetramethylbenzidine (TMB). Upon the introduction of Hg<sup>2+</sup>, it combined with AuNPs rapidly, significantly enhancing the peroxidase-mimicking activity of GO-AuNPs. By capturing the color signal of the generated blue oxTMB and the electro-oxidation response of residual TMB, a “turn-on” colorimetric and “turn-off” electrochemical bimodal method was established for Hg<sup>2+</sup> measurement, providing a linear range of 10–60 μg/L in colorimetric detection and 0.001–20 μg/L in electrochemical analysis. Different from common bimodal assays mainly improving detecting reliability due to their cross-validation ability, our dual-mode sensor exhibits the unique feature of complementary sensitivity and detection range, thus enabling its flexible use in analyzing different levels of the target in a broad scope with no requirement for sample enrichment and dilution.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136598", "PubYear": 2025, "Volume": "422", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, Fudan University, Shanghai 200433, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 5, "Name": "Jinjin Liu", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 8, "Name": "Qingzhen Tian", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 9, "Name": "Shu Li", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China;State Environmental Protection Key Laboratory of Monitoring for Heavy Metal Pollutants, Hunan Ecology and Environment Monitoring Center, Changsha 410019, China;Fujian Key Laboratory of Functional Marine Sensing Materials, Minjiang University, Fuzhou 350108, China;Corresponding author at: School of Public Health, Hengyang Medical School, University of South China, Hengyang 421001, China"}], "References": [{"Title": "Silica-based nanoenzymes for rapid and ultrasensitive detection of mercury ions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "330", "Issue": "", "Page": "129304", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Hollow C@MoS2 nanotubes with Hg2+-triggered oxidase-like catalysis: A colorimetric method for detection of Hg2+ ions in wastewater", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "361", "Issue": "", "Page": "131725", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Polyhedral MnSe microparticles with specific Hg2+-suppressed oxidase-like activity: Toward a green and low-cost turn-off method for Hg2+ detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "382", "Issue": "", "Page": "133539", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Matrix redox interference-free nanozyme-amplified detection of Hg2+ using thiol-modified phosphatase-mimetic nanoceria", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "401", "Issue": "", "Page": "135030", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 117611913, "Title": "Design of Intrusion Attack Simulation Verification System for Advanced Persistent Threat Targeting Electric Network", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.149190", "PubYear": 2024, "Volume": "14", "Issue": "9", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "雪梅 刘", "Affiliation": ""}], "References": []}, {"ArticleId": 117612031, "Title": "Biologically inspired tonic and bursting LIF neuron model for spiking neural network: a CMOS implementation", "Abstract": "<p>The human brain has been encompassed by neurons and synapses, where the signal is propagated as a spike. This perception seeks to create hardware systems called neural cores that are comprised of artificial bio-neurons and synapses; these systems resemble the brain’s functions and emulate the different spiking patterns to operate the neuromorphic processors. Through this motivation, the paper presents a low-power Schmitt trigger-based Leaky Integrate and Fire (LIF) neuron model that offers significantly less energy per spike and showcases the dynamic spike frequency and refractory periods that are regulated by the membrane and reset capacitance in addition to refractory circuitry. The incorporation of a hysteresis comparator, e.g., the Schmitt trigger, enhances noise immunity and facilitates dynamic threshold adjustments, enabling faster switching and reducing energy consumption. The neuron models are simulated in Cadence Virtuoso GPDK 45 nm Technology to obtain dynamic tonic and burst spiking patterns; subsequently, the significantly smaller spike pulse width of the proposed model is measured from the dynamic pattern as 1 . 867 ns , and the refractory period is measured as 0 . 2 ns respectively. This proposed model consumes less energy, 524 . 415 aJ per spike, under the 1 V power supply and 1 ms step pulse. Typically, the spike pulses have different shapes and magnitudes based on the functions of membrane potential, which are applicable to realize the spiking behavior of SNNs.</p>", "Keywords": "", "DOI": "10.1007/s00542-024-05755-3", "PubYear": 2025, "Volume": "31", "Issue": "2", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of ECE, National Institute of Technology Meghalaya, Shillong, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of ECE, National Institute of Technology Meghalaya, Shillong, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, National Institute of Technology Meghalaya, Shillong, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, National Institute of Technology Meghalaya, Shillong, India; Corresponding author."}], "References": [{"Title": "Adiabatic logic-based strong ARM comparator for ultra-low power applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "4", "Page": "929", "JournalTitle": "Microsystem Technologies"}, {"Title": "An area and energy efficient LIF neuron model with spike frequency adaptation mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "465", "Issue": "", "Page": "350", "JournalTitle": "Neurocomputing"}, {"Title": "Exploring Neuromorphic Computing Based on Spiking Neural Networks: Algorithms to Hardware", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "12", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Single SiGe Transistor Based Energy-Efficient Leaky Integrate-and-Fire Neuron for Neuromorphic Computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "6", "Page": "6997", "JournalTitle": "Neural Processing Letters"}, {"Title": "Neuromorphic electronics for robotic perception, navigation and control: A survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "106838", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Power-efficient VLSI realization of decimal convolution algorithms for resource-constrained environments: a design perspective in CMOS and double-gate CMOS technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2025, "Volume": "31", "Issue": "2", "Page": "313", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 117612054, "Title": "Adaptive recommendation method for network resources based on improved transfer learning", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2024.141359", "PubYear": 2024, "Volume": "74", "Issue": "1/2", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117612106, "Title": "Issue Information", "Abstract": "<p>No abstract is available for this article.</p>", "Keywords": "", "DOI": "10.1002/jsid.1244", "PubYear": 2024, "Volume": "32", "Issue": "9", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [], "References": []}, {"ArticleId": 117612192, "Title": "ICDDPM: Image-conditioned denoising diffusion probabilistic model for real-world complex point cloud single view reconstruction", "Abstract": "Point cloud single-view reconstruction (PC-SVR) generates high-quality point clouds from low-cost 2D images, offering a cost-effective solution to the expensive and inefficient acquisition of point cloud for real-world scene typically achieved through expensive LiDAR systems. However, current models that generate point clouds from real-world 2D images (e.g. maritime vessels) still have shortcomings in terms of quality and model generalization. In this paper, we proposed a image-conditioned denoising diffusion probabilistic model (ICDDPM) for real-world complex point cloud single view reconstruction to address these issues. We re-designed the structure of classic diffusion model, use latent shape vectors to seamlessly integrate 2D image encoder, point cloud encoder, and conditional diffusion model, to cater PC-SVR task. By guiding the diffusion process with the 2D images, which serve as crucial conditional information, ICDDPM achieves end-to-end point cloud generation with superior quality. 2D images are also employed as input in the reverse diffusion process to further achieve point cloud generation. We conducted qualitative and quantitative experiments on synthetic dataset ShapeNet and real-world dataset PASCAL3D+ (focused on experiments of vessel point cloud data specifically). The results indicate that the ICDDPM model demonstrates superior performance compared to state-of-the-art models. It is capable of generating point clouds with a greater level of global and local details from various 2D image data. Additionally, the model exhibits strong generalization abilities and requires fewer computational resources.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125370", "PubYear": 2025, "Volume": "259", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, National University of Defense Technology, Hefei, China;State Key Laboratory of Pulsed Power Laser Technology, National University of Defense Technology, Hefei, China;Anhui Province Key Laboratory of Electronic Restriction, National University of Defense Technology, Hefei, China;Information Security Research Center, Hefei Comprehensive National Science Center, Hefei, China"}, {"AuthorId": 2, "Name": "Yihua Hu", "Affiliation": "College of Electronic Engineering, National University of Defense Technology, Hefei, China;State Key Laboratory of Pulsed Power Laser Technology, National University of Defense Technology, Hefei, China;Anhui Province Key Laboratory of Electronic Restriction, National University of Defense Technology, Hefei, China;Information Security Research Center, Hefei Comprehensive National Science Center, Hefei, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, National University of Defense Technology, Hefei, China;State Key Laboratory of Pulsed Power Laser Technology, National University of Defense Technology, Hefei, China;Anhui Province Key Laboratory of Electronic Restriction, National University of Defense Technology, Hefei, China;Information Security Research Center, Hefei Comprehensive National Science Center, Hefei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, National University of Defense Technology, Hefei, China;State Key Laboratory of Pulsed Power Laser Technology, National University of Defense Technology, Hefei, China;Anhui Province Key Laboratory of Electronic Restriction, National University of Defense Technology, Hefei, China;Information Security Research Center, Hefei Comprehensive National Science Center, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Anhui Provincial Military Region, Chinese PLA, Hefei, China"}], "References": [{"Title": "Pix2Vox++: Multi-scale Context-aware 3D Object Reconstruction from Single and Multiple Images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "12", "Page": "2919", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "RCL-Learning: ResNet and convolutional long short-term memory-based spatiotemporal air pollutant concentration prediction model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "118017", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Progressive generation of 3D point clouds with hierarchical consistency", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Jizhou Huang", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109200", "JournalTitle": "Pattern Recognition"}, {"Title": "A surrogate-assisted evolutionary algorithm based on multi-population clustering and prediction for solving computationally expensive dynamic optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "223", "Issue": "", "Page": "119815", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Noising-Denoising Framework for Point Cloud Upsampling via Normalizing Flows", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "109569", "JournalTitle": "Pattern Recognition"}, {"Title": "Robust multi-task learning network for complex LiDAR point cloud data preprocessing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121552", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 117612199, "Title": "The effect of fiber activation on the surface properties of a photocatalytic composite material based on it", "Abstract": "", "Keywords": "", "DOI": "10.15828/2075-8545-2024-16-4-329-341", "PubYear": 2024, "Volume": "16", "Issue": "4", "JournalId": 36948, "JournalTitle": "Nanotechnologies in Construction: A Scientific Internet-Journal", "ISSN": "", "EISSN": "2075-8545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "S.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117612251, "Title": "Investigation of molten pool geometry and flow field based on powder-scale modeling in laser directed energy deposition", "Abstract": "<p>Laser directed energy deposition (DED-LB) technology is currently facing challenges in controlling geometry accuracy of metal components. There is an urgent need to develop a numerical model that is more efficient and accurate compared to existing models and to analyze the dynamic behavior of the melt pool under the interaction of process parameters during DED-LB process to optimize parameters with fewer experimental studies to reduce costs. Therefore, a three-dimensional powder-scale multi-physics model is proposed during the single-track DED-LB process in this study. The powder particles are added by a Lagrangian particle model in this model without the need for assuming that the powder particles entering the molten pool are added by the mass source term, which improves model accuracy and efficiency. The effects of energy per unit mass (EUM, J g<sup>−1</sup>), mass per unit length (MUL, g mm<sup>−1</sup>), and standoff distance (SD, mm) intensities on transient molten pool motion and flow field are comprehensively investigated. It is discovered that the proposed model is able to predict the single-track height, width, depth, and dilution rate, or less than 9% relative error from experimentation, providing a useful tool for track geometry to predict. Furthermore, the flow velocity, width, and depth of melt pool also gradually are improved with higher EUM, MUL, and SD intensity and fluctuate within a small range. However, the height is improved and then decreases with higher SD intensity, and is improved with higher EUM or MUL. Overall, this study contributes to developing an optimizing process approach to improve formation accuracy for DED-LB manufacturing.</p>", "Keywords": "Laser-based directed energy deposition; Powder-scale modeling; Melt pool motion; Multiphysics model", "DOI": "10.1007/s00170-024-14344-7", "PubYear": 2024, "Volume": "134", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China; Ningbo Institute of Dalian University of Technology, Ningbo, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China; Corresponding author."}, {"AuthorId": 3, "Name": "Wanyang Li", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 5, "Name": "Zongyu Ma", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Wang", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 8, "Name": "Guangda Hu", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, College of Engineering, Shantou University, Shantou, China"}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian, People’s Republic of China; State Key Laboratory of High-Performance Precision Manufacturing, Dalian University of Technology, Dalian, People’s Republic of China"}], "References": [{"Title": "Process monitoring and machine learning for defect detection in laser-based metal additive manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "4", "Page": "1407", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 117612309, "Title": "Towards a Cybersecurity Culture-Behaviour Framework: A Rapid Evidence Review", "Abstract": "A strong organisational cybersecurity culture (CSC) is critical to the success of any cybersecurity effort, and understanding and measuring CSC is essential if it is to succeed. To facilitate the framing and measurement of CSC we conducted a rapid evidence assessment (REA) to synthesise relevant studies on CSC. The systematic search identified 1,768 records. 59 studies were eligible for the final synthesis. Thematic analysis of the CSC definitions in the included studies highlighted that CSC should not be viewed solely as a technical problem but as a management issue too; CSC requires top management involvement and role modelling, with full organisational support for the desired employee behaviours. We identify both theoretically and empirically derived models of CSC in the REA, along with a range of methods to develop and test these models. Integrative analysis of these models provides detailed information about CSC dimensions, including employee attitudes towards CS; compliance with policies; the role of security education, training and awareness; monitoring of behaviour and top management commitment. The evidence indicates that CSC should be understood both in the context of the wider organisational culture as well as in the shared employee understanding of CS that leads to behaviour. Based on the findings of this review, we propose a novel integrated framework of CSC consisting of cultural values, the culture-to-behaviour link, and behaviour itself. We also make measurement recommendations based on this CSC framework, ranging from simple, broad-brush tools through to suggestions for multi-dimensional measures, which can be applied in a variety of sectors and organisations.", "Keywords": "Cybersecurity culture; Evidence synthesis; Cybersecurity behaviour; Competing values framework; AMC framework", "DOI": "10.1016/j.cose.2024.104110", "PubYear": 2025, "Volume": "148", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Waikato;Corresponding author at: School of Psychology, University of Waikato, Private Bag 3105, Hamilton 3240, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Waikato"}], "References": [{"Title": "Defining organisational information security culture—Perspectives from academia and industry", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101713", "JournalTitle": "Computers & Security"}, {"Title": "A systematic review of scales for measuring information security culture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "133", "JournalTitle": "Information and Computer Security"}, {"Title": "Developing a cyber security culture: Current practices and future needs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "102387", "JournalTitle": "Computers & Security"}, {"Title": "The determinants of an information security policy compliance culture in organisations: the combined effects of organisational and behavioural factors", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "4", "Page": "583", "JournalTitle": "Information and Computer Security"}, {"Title": "Organizational and team culture as antecedents of protection motivation among IT employees", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102774", "JournalTitle": "Computers & Security"}, {"Title": "Cultivating security culture for information security success: A mixed-methods study based on anthropological perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "60", "Issue": "3", "Page": "103751", "JournalTitle": "Information & Management"}, {"Title": "COVID-19 pandemic-induced organisational cultural shifts and employee information security compliance behaviour: a South African case study", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "2", "Page": "221", "JournalTitle": "Information and Computer Security"}, {"Title": "A systematic literature review of how cybersecurity-related behavior has been assessed", "Authors": "<PERSON><PERSON>; Sokratis K. <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "4", "Page": "463", "JournalTitle": "Information and Computer Security"}]}, {"ArticleId": 117612329, "Title": "Fabrication of submillimetre structures on pure titanium by non-aqueous electrolyte jet machining", "Abstract": "<p>Titanium and its alloys are widely utilized in the field of aerospace and medical devices, owing to their impressive high-temperature durability and excellent corrosion resistance. Many researchers have machined millimeter-scale features on titanium using electrochemical jet machining (EJM). However, achieving submillimetre scale features on titanium with high efficiency and surface integrity is still a hindrance. In this research, a small nozzle with a diameter of 200 μm has been proposed for EJM of pure titanium (TA2) with NaCl-ethylene glycol (EG) solution. This non-aqueous electrolyte is able to prevent the formation of oxide layers during the machining process. In the experimental part, the utilization of EJM in processing dimples and channels on TA2 has been examined. The direct current mode and the pulsed current mode have been compared in terms of dimensions, surface finish, and material removal localization. It is found that by utilizing suitable parameters, EJM with non-aqueous electrolyte is suitable to manufacture submillimetre structures on pure titanium with high precision and high surface quality.</p>", "Keywords": "Electrochemical jet machining; Non-aqueous electrolyte; TA2; Submillimetre structures", "DOI": "10.1007/s00170-024-14371-4", "PubYear": 2024, "Volume": "134", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Micro/Nano Optomechatronics Engineering, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China; Shenzhen Key Laboratory of High Performance Nontraditional Manufacturing, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Micro/Nano Optomechatronics Engineering, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China; Shenzhen Key Laboratory of High Performance Nontraditional Manufacturing, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Micro/Nano Optomechatronics Engineering, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China; Shenzhen Key Laboratory of High Performance Nontraditional Manufacturing, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Micro/Nano Optomechatronics Engineering, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China; Shenzhen Key Laboratory of High Performance Nontraditional Manufacturing, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Micro/Nano Optomechatronics Engineering, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China; Shenzhen Key Laboratory of High Performance Nontraditional Manufacturing, College of Mechatronics and Control Engineering, Shenzhen University, Shenzhen, China"}], "References": []}, {"ArticleId": 117612358, "Title": "DBSCAN SMOTE LSTM: Effective Strategies for Distributed Denial of Service Detection in Imbalanced Network Environments", "Abstract": "<p>In detecting Distributed Denial of Service (DDoS), deep learning faces challenges and difficulties such as high computational demands, long training times, and complex model interpretation. This research focuses on overcoming these challenges by proposing an effective strategy for detecting DDoS attacks in imbalanced network environments. This research employed DBSCAN and SMOTE to increase the class distribution of the dataset by allowing models using LSTM to learn time anomalies effectively when DDoS attacks occur. The experiments carried out revealed significant improvement in the performance of the LSTM model when integrated with DBSCAN and SMOTE. These include validation loss results of 0.048 for LSTM DBSCAN and SMOTE and 0.1943 for LSTM without DBSCAN and SMOTE, with accuracy of 99.50 and 97.50. Apart from that, there was an increase in the F1 score from 93.4% to 98.3%. This research proved that DBSCAN and SMOTE can be used as an effective strategy to improve model performance in detecting DDoS attacks on heterogeneous networks, as well as increasing model robustness and reliability.</p>", "Keywords": "", "DOI": "10.3390/bdcc8090118", "PubYear": 2024, "Volume": "8", "Issue": "9", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Satya W<PERSON>na Christian University, Salatiga 50711, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Satya W<PERSON>na Christian University, Salatiga 50711, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Satya W<PERSON>na Christian University, Salatiga 50711, Indonesia"}], "References": [{"Title": "Detection of DDoS attacks with feed forward based deep neural network model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114520", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Network Intrusion Detection System using Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "239", "JournalTitle": "Procedia Computer Science"}, {"Title": "Network intrusion detection using machine learning approaches: Addressing data imbalance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "30", "JournalTitle": "IET Cyber-Physical Systems: Theory & Applications"}, {"Title": "An efficient deep learning technique for facial emotion recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "2", "Page": "1649", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Two density-based sampling approaches for imbalanced and overlapping data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108217", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Weighted Ensemble with one-class Classification and Over-sampling and Instance selection (WECOI): An approach for learning from imbalanced data streams", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "61", "Issue": "", "Page": "101614", "JournalTitle": "Journal of Computational Science"}, {"Title": "PF-SMOTE: A novel parameter-free SMOTE for imbalanced datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "498", "Issue": "", "Page": "75", "JournalTitle": "Neurocomputing"}, {"Title": "Modeling DDOS attacks in sdn and detection using random forest classifier", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "4", "Page": "229", "JournalTitle": "Journal of Cyber Security Technology"}, {"Title": "An evolutionary KNN model for DDoS assault detection using genetic algorithm based optimization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "35", "Page": "83005", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 117612380, "Title": "Various Types of Comet Languages and their Application in External Contextual Grammars", "Abstract": "In this paper, we continue the research on the power of contextual grammars with selection languages from subfamilies of the family of regular languages. We investigate various comet-like types of languages and compare such language families to some other subregular families of languages (finite, monoidal, nilpotent, combinational, (symmetric) definite, ordered, non-counting, power-separating, suffix-closed, commutative, circular, or union-free languages). Further, we compare the language families defined by these types for the selection with each other and with the families of the hierarchy obtained for external contextual grammars. In this way, we extend the existing hierarchy by new language families.", "Keywords": "", "DOI": "10.4204/EPTCS.407.9", "PubYear": 2024, "Volume": "407", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Generative Capacity of Contextual Grammars with Subregular Selection Languages*", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "1-2", "Page": "123", "JournalTitle": "Fundamenta Informaticae"}, {"Title": "Closure Properties of Subregular Languages Under Operations", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Foundations of Computer Science"}, {"Title": "Strictly Locally Testable and Resources Restricted Control Languages in Tree-Controlled Grammars", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "386", "Issue": "", "Page": "253", "JournalTitle": "Electronic Proceedings in Theoretical Computer Science"}, {"Title": "Merging two Hierarchies of Internal Contextual Grammars with Subregular Selection", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "388", "Issue": "", "Page": "125", "JournalTitle": "Electronic Proceedings in Theoretical Computer Science"}]}, {"ArticleId": 117612470, "Title": "Haploinsufficiency of intraflagellar transport protein 172 causes autism-like behavioral phenotypes in mice through BDNF", "Abstract": "<p><b>INTRODUCTION</b>:Primary cilia are hair-like solitary organelles growing on most mammalian cells that play fundamental roles in embryonic patterning and organogenesis. Defective cilia often cause a suite of inherited diseases called ciliopathies with multifaceted manifestations. Intraflagellar transport (IFT), a bidirectional protein trafficking along the cilium, actively facilitates the formation and absorption of primary cilia. IFT172 is the largest component of the IFT-B complex, and its roles in Bardet-Biedl Syndrome (BBS) have been appreciated with unclear mechanisms.</p><p><b>OBJECTIVES</b>:We performed a battery of behavioral tests with Ift172 haploinsufficiency (Ift172<sup>+/-</sup>) and WT littermates. We use RNA sequencing to identify the genes and signaling pathways that are differentially expressed and enriched in the hippocampus of Ift172<sup>+/-</sup> mice. Using AAV-mediated sparse labeling, electron microscopic examination, patch clamp and local field potential recording, western blot, luciferase reporter assay, chromatin immunoprecipitation, and neuropharmacological approach, we investigated the underlying mechanisms for the aberrant phenotypes presented by Ift172<sup>+/-</sup> mice.</p><p><b>RESULTS</b>:Ift172<sup>+/-</sup> mice displayed excessive self-grooming, elevated anxiety, and impaired cognition. RNA sequencing revealed enrichment of differentially expressed genes in pathways relevant to axonogenesis and synaptic plasticity, which were further confirmed by less spine density and synaptic number. Ift172<sup>+/-</sup> mice demonstrated fewer parvalbumin-expressing neurons, decreased inhibitory synaptic transmission, augmented theta oscillation, and sharp-wave ripples in the CA1 region. Moreover, Ift172 haploinsufficiency caused less BDNF production and less activated BDNF-TrkB signaling pathway through transcription factor Gli3. Application of 7,8-Dihydroxyflavone, a potent small molecular TrkB agonist, fully restored BDNF-TrkB signaling activity and abnormal behavioral phenotypes presented by Ift172<sup>+/-</sup> mice. With luciferase and chip assays, we provided further evidence that Gli3 may physically interact with BDNF promoter I and regulate BDNF expression.</p><p><b>CONCLUSIONS</b>:Our data suggest that Ift172 per se drives neurotrophic effects and, when defective, could cause neurodevelopmental disorders reminiscent of autism-like disorders.</p><p>Copyright © 2024. Production and hosting by Elsevier B.V.</p>", "Keywords": "Autism spectrum disorders;BDNF;Bardet Biedl Syndrome;Intraflagellar transport protein 172;Primary cilia;Sonic Hedgehog", "DOI": "10.1016/j.jare.2024.08.041", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry, National Clinical Research Center for Mental Disorders and National Center for Mental Disorders, The Second Xiangya Hospital, Central South University, Changsha 410011, China; Department of Psychiatry, Fujian Medical University Affiliated Fuzhou Neuropsychiatric Hospital, Fuzhou 350005, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurology, Xiangya Hospital of Central South University, Changsha 410008, China; National Clinical Research Center for Geriatric Disorder, Central South University, Changsha, China; Engineering Research Center of Human Province in Cognitive Impairment Disorders, Changsha 410008, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Pharmaceutical Science, Zhejiang University of Technology, Hangzhou 310014, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Medical Neurobiology and MOE Frontiers Center for Brain Science, Institutes of Brain Science, Fudan University, 200433 Shanghai, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry, National Clinical Research Center for Mental Disorders and National Center for Mental Disorders, The Second Xiangya Hospital, Central South University, Changsha 410011, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry, National Clinical Research Center for Mental Disorders and National Center for Mental Disorders, The Second Xiangya Hospital, Central South University, Changsha 410011, China."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Medicine, Medical Sciences & Nutrition, Institute of Medical Sciences, University of Aberdeen, Foresterhill, AB25 2ZD Aberdeen, Scotland, UK."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Medical Neurobiology and MOE Frontiers Center for Brain Science, Institutes of Brain Science, Fudan University, 200433 Shanghai, China."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Psychiatry, National Clinical Research Center for Mental Disorders and National Center for Mental Disorders, The Second Xiangya Hospital, Central South University, Changsha 410011, China"}], "References": []}, {"ArticleId": 117612512, "Title": "Frontmatter", "Abstract": "", "Keywords": "", "DOI": "10.1515/auto-2024-frontmatter9", "PubYear": 2024, "Volume": "72", "Issue": "9", "JournalId": 16752, "JournalTitle": "at - Automatisierungstechnik", "ISSN": "0178-2312", "EISSN": "2196-677X", "Authors": [], "References": []}, {"ArticleId": 117612522, "Title": "Analysis of instructors’ intention to use and experience of using cognitive training robots for older adults with mild cognitive impairment", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10209-024-01141-y", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1397, "JournalTitle": "Universal Access in the Information Society", "ISSN": "1615-5289", "EISSN": "1615-5297", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "<PERSON><PERSON>’s acceptance of companion robots from the perspective of user factors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "4", "Page": "935", "JournalTitle": "Universal Access in the Information Society"}, {"Title": "Survey dataset on open and distance learning students’ intention to use social media and emerging technologies for online facilitation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "", "Page": "105929", "JournalTitle": "Data in Brief"}, {"Title": "How Ethical Issues Raised by Human–Robot Interaction can Impact the Intention to use the Robot?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "4", "Page": "1103", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 117612577, "Title": "A Microrobotic Design for the Spontaneous Tracing of Isochemical Contours in the Environment", "Abstract": "Microrobotic platforms hold significant potential to advance a variety of fields, from medicine to environmental sensing. Herein, minimally functional robotic entities modeled on readily achievable state‐of‐the‐art features in a modern lab or cleanroom are computationally simulated. Inspired by <PERSON><PERSON> and <PERSON> ( Phys Rev Res . 2019;1(3):1–5), it is shown that the simple combination of unidirectional steering connected to a single environmental (chemical) sensor along with constant propulsion gives rise to highly complex functions of significant utility. Such systems can trace the contours orthogonal to arbitrary chemical gradients in the environment. Also, pairs of such robots that are additionally capable of emitting the same chemical signal are shown to exhibit coupled relative motion. When the pair has unidirectional steering in opposite directions within the 2D plane (i.e., counter‐rotating), they move in parallel trajectories to each other. Alternatively, when steering is in the same direction (corotation), the two move in the same epicyclical trajectory. In this way, the chirality of the unidirectional steering produces two distinct emergent phenomena. The behavior is understood as a ratchet mechanism that exploits the differential in the radii of curvature corresponding to different spatial locations. Applications to environmental detection, remediation, and monitoring are discussed.", "Keywords": "", "DOI": "10.1002/aisy.202400002", "PubYear": 2024, "Volume": "6", "Issue": "10", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering Massachusetts Institute of Technology  Cambridge MA 02139 USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering Massachusetts Institute of Technology  Cambridge MA 02139 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering Massachusetts Institute of Technology  Cambridge MA 02139 USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering Massachusetts Institute of Technology  Cambridge MA 02139 USA"}], "References": [{"Title": "Chemically programmable microrobots weaving a web from hormones", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Zdeň<PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "11", "Page": "711", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Micrometer-sized electrically programmable shape-memory actuators for low-power microrobotics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "52", "Page": "eabe6663", "JournalTitle": "Science Robotics"}, {"Title": "Increasingly Intelligent Micromachines", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "1", "Page": "", "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems"}, {"Title": "Microscopic robots with onboard digital control", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "70", "Page": "eabq2296", "JournalTitle": "Science Robotics"}, {"Title": "Colloidal State Machines as Smart Tracers for Chemical Reactor Analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "9", "Page": "2300130", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 117612615, "Title": "Front Cover", "Abstract": "", "Keywords": "", "DOI": "10.1109/TAI.2024.3449734", "PubYear": 2024, "Volume": "5", "Issue": "9", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [], "References": []}, {"ArticleId": 117612648, "Title": "Unsupervised multi-view graph representation learning with dual weight-net", "Abstract": "Unsupervised multi-view graph representation learning (UMGRL) aims to capture the complex relationships in the multi-view graph without human annotations, so it has been widely applied in real-world applications. However, existing UMGRL methods still face the issues as follows: (i) Previous UMGRL methods tend to overlook the importance of nodes with different influences and the importance of graphs with different relationships, so that they may lose discriminative information in nodes with large influences and graphs with important relationships. (ii) Previous UMGRL methods generally ignore the heterophilic edges in the multi-view graph to possibly introduce noise from different classes into node representations. To address these issues, we propose a novel bi-level optimization UMGRL framework with dual weight-net. Specifically, the lower-level optimizes the parameters of encoders to obtain node representations of different graphs, while the upper-level optimizes the parameters of the dual weight-net to adaptively and dynamically capture the importance of node level, graph level, and edge level, thus obtaining discriminative fused representations for downstream tasks. Moreover, theoretical analysis demonstrates that the proposed method shows a better generalization ability on downstream tasks, compared to previous UMGRL methods. Extensive experimental results verify the effectiveness of the proposed method on public datasets, in terms of different downstream tasks, compared to numerous comparison methods.", "Keywords": "", "DOI": "10.1016/j.inffus.2024.102669", "PubYear": 2025, "Volume": "114", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Electronic Science and Technology of China, Chengdu, 611731, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Electronic Science and Technology of China, Chengdu, 611731, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "School of Computer Science and Technology, University of Electronic Science and Technology of China, Chengdu, 611731, China;Shenzhen Institute for Advanced Study, University of Electronic Science and Technology of China, Shenzhen, 518000, China;Corresponding author"}], "References": [{"Title": "Interpretable learning based Dynamic Graph Convolutional Networks for Alzheimer’s Disease analysis", "Authors": "Yong<PERSON> Zhu; Junbo <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "53", "JournalTitle": "Information Fusion"}, {"Title": "Sufficient dimension reduction for average causal effect estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "3", "Page": "1174", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Graph Barlow Twins: A self-supervised representation learning framework for graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "256", "Issue": "", "Page": "109631", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey on fairness-aware recommender systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101906", "JournalTitle": "Information Fusion"}, {"Title": "Neighbor group structure preserving based consensus graph learning for incomplete multi-view clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101917", "JournalTitle": "Information Fusion"}, {"Title": "UNTIE: Clustering analysis with disentanglement in multi-view information fusion", "Authors": "<PERSON><PERSON>; Ya<PERSON> Ren; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101937", "JournalTitle": "Information Fusion"}, {"Title": "Contrastive sentence representation learning with adaptive false negative cancellation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "102", "Issue": "", "Page": "102065", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 117612710, "Title": "Exploring Pedagogical Dynamics <i>via</i> Teachers’ Interactions with Digital Textbook Platforms", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2400413", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Education, Southwest University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Education, Southwest University, Chongqing, China"}, {"AuthorId": 3, "Name": "Yi Dai", "Affiliation": "School of Education, City University of Macau, Macau, China"}], "References": [{"Title": "Application and theory gaps during the rise of Artificial Intelligence in Education", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "100002", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Successful design and delivery of online professional development for teachers: A systematic review of the literature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "104158", "JournalTitle": "Computers & Education"}, {"Title": "Understanding Students’ Cyberslacking Behaviour in e-Learning Environments: Is Student Engagement the Key?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "13", "Page": "2573", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Pre-service teachers' perceptions and intentions regarding the use of chatbots through statistical and lag sequential analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "", "Page": "100119", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Analysis on the Promotion of Classroom Atmosphere in Multimodal English Teaching Based on Human-Computer Interaction", "Authors": "<PERSON>z<PERSON> Shu; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "13", "Page": "3516", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 117612721, "Title": "Image Steganography based on Fernet Symmetric Encryption and Odd-Even <PERSON>l Modification", "Abstract": "<p>The combination of steganography and cryptographic approaches for information concealing has piqued the curiosity of numerous researchers. We also integrate those two methods in our research. Using Fernet symmetric encryption and odd-even pixel alteration, this research presents a novel method for image steganography that distributes the data equally throughout the image. To create a stego image, the sender uses a password to encrypt a secret message embedded into an image's red channel. The stego image resembles the original cover image, while the underlying secret data is concealed from human view. The main goals of the system—encryption, consistent data concealing, and message retrieval—were all successfully met. Before integrating the secret data into the image, it can be easily and successfully secured using symmetric encryption using the Fernet cipher. By adding another layer of security to the system, the password feature makes it harder for unauthorized users to access hidden data. By altering image pixels according to whether they are odd or even, the odd-even pixel modification-based steganography approach makes it possible to conceal data. Based on the experiment's results, it can be concluded that this approach embeds concise messages with a high visual quality.</p>", "Keywords": "", "DOI": "10.18535/ijecs/v13i08.4874", "PubYear": 2024, "Volume": "13", "Issue": "8", "JournalId": 17925, "JournalTitle": "International Journal Of Engineering And Computer Science", "ISSN": "", "EISSN": "2319-7242", "Authors": [{"AuthorId": 1, "Name": "Habiba Sultana", "Affiliation": "Department of Computer Science and Engineering, Jatiya Kabi Kazi Nazrul Islam University, Mymensingh 2220, Bangladesh"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jatiya Kabi Kazi Nazrul Islam University, Mymensingh 2220, Bangladesh"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jatiya Kabi Kazi Nazrul Islam University, Mymensingh 2220, Bangladesh"}], "References": []}, {"ArticleId": 117612742, "Title": "Development of a Small-Sized Urban Cable Conduit Inspection Robot", "Abstract": "<p>Cable conduits are crucial for urban power transmission and distribution systems. However, current conduit robots are often large and susceptible to tilting issues, which hampers the effective and intelligent inspection of these conduits. Therefore, there is an urgent need to develop a smaller-sized conduit inspection robot to address these challenges. Based on an in-depth analysis of the characteristics of the cable conduit working environment and the associated functional requirements, this study successfully developed a small-scale urban cable conduit inspection robot prototype. This development was grounded in relevant design theories, simulation analyses, and experimental tests. The test results demonstrate that the robot’s bracing module effectively prevents tilting within the conduit. Additionally, the detection module enables comprehensive 360-degree conduit inspections, and the vacuuming module meets the negative pressure requirements for efficient absorption of dust and foreign matter. The robot has met the expected design goals, effectively enhanced the automation of the cable conduit construction process, and improved the quality control of cable laying.</p>", "Keywords": "", "DOI": "10.3390/act13090349", "PubYear": 2024, "Volume": "13", "Issue": "9", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wenzhou Power Supply Company, State Grid Zhejiang Electric Power Co., Ltd., Wenzhou 325000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Wenzhou Power Supply Company, State Grid Zhejiang Electric Power Co., Ltd., Wenzhou 325000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Wenzhou Power Supply Company, State Grid Zhejiang Electric Power Co., Ltd., Wenzhou 325000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Engineering Research Center for Advanced Manufacturing & Intelligent Operation and Maintenance of Electric Power Machinery, North China Electric Power University, Baoding 071003, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Huang", "Affiliation": "Wenzhou Power Supply Company, State Grid Zhejiang Electric Power Co., Ltd., Wenzhou 325000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Wenzhou Power Construction Co., Ltd., Wenzhou 325000, China"}], "References": [{"Title": "Design and Kinematic Characteristic Analysis of a Spiral Robot for Oil and Gas Pipeline Inspections", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "6", "Page": "240", "JournalTitle": "Actuators"}, {"Title": "Multi-gait snake robot for inspecting inner wall of a pipeline", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "2", "Page": "100156", "JournalTitle": "Biomimetic Intelligence and Robotics"}]}, {"ArticleId": *********, "Title": "Pengaruh Kuantitas Korpus Terhadap A<PERSON> Penerjemah Statistik Bahasa Indonesia ke Bahasa Jawa Ngoko", "Abstract": "<p>Bahasa memungkinkan seseorang untuk menyampaikan perasaan, pikiran, gagasan, atau konsep mereka. Sosiologi menganggap bahasa sebagai sistem lambang yang terdiri dari bunyi. Bahasa manusiawi, dapat dipilih, berkembang, beragam, dan dinamis. Bentuknya yang sangat beragam dapat menjadi hambatan ketika bahasa digunakan untuk berkomunikasi dengan orang-orang di seluruh dunia. <PERSON>un, saat ini ada teknologi mesin penerjemah yang dapat membantu orang memahami berbagai jenis bahasa. Teknik mesin penerjemah statistik adalah metode teknologi mesin penerjemah yang menggunakan model statistik berdasarkan hasil analisis korpus paralel. Korpus paralel berisi salinan teks dari bahasa sumber dan bahasa target yang diinginkan. Penelitian ini menguji pengaruh kuantitas korpus terhadap nilai akurasi mesin penerjemah statistik Bahasa Indonesia ke Bahasa Jawa Ngoko. Penelitian dimulai dengan pengumpulan data, pembuatan 3000 pasang kalimat teks korpus, penggunaan mesin penerjemah statistik, pengujian dan evaluasi hasil terjemahan, dan analisis hasil. Hasil analisis pengujian menunjukkan bahwa jumlah korpus yang diberikan dapat mempengaruhi nilai akurasi. Pada pengujian pertama, dengan menggunakan korpus pelatihan yang sama dengan korpus uji, penambahan 200 pasangan kalimat menyebabkan penurunan nilai akurasi rata-rata sebesar -0.00221%. Sebaliknya, pada pengujian kedua, dengan menggunakan korpus pelatihan yang berbeda dari korpus uji, penambahan 200 pasangan kalimat menyebabkan peningkatan nilai akurasi rata-rata sebesar 0.00813%. Kesimpulannya, kualitas dan pemisahan korpus pelatihan dari korpus uji sangat mempengaruhi performa mesin penerjemah statistik, dan penambahan data tambahan dapat meningkatkan akurasi jika diterapkan dengan benar.</p>", "Keywords": "<PERSON><PERSON> pener<PERSON> statistik;<PERSON><PERSON>;Bahasa Indonesia;Bahasa <PERSON>;kuantitas korpus", "DOI": "10.26418/jp.v10i2.73082", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tanjungpura University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tanjungpura University"}], "References": []}, {"ArticleId": 117612869, "Title": "Utilizing Deep improved ResNet50 for Brain Tumor Classification Based MRI", "Abstract": "", "Keywords": "", "DOI": "10.1109/OJCS.2024.3453924", "PubYear": 2024, "Volume": "5", "Issue": "", "JournalId": 68233, "JournalTitle": "IEEE Open Journal of the Computer Society", "ISSN": "", "EISSN": "2644-1268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, School of Computing, University Technology of Malaysia Johor Bahru, Skudai, Johor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UTM-IRDA MaGICX, Institute of Human Centered Engineering, Universiti Teknologi Malaysia Johor Bahru, Skudai, Johor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> R<PERSON>", "Affiliation": "Islamic University, Najaf, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Altoosi University College, Computer Science, Najaf, Iraq"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Safwa University College, Department of Medical Instrumentation Techniques, Engineering, Karbala, Iraq"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, School of Computing, University Technology of Malaysia Johor Bahru, Skudai, Johor, Malaysia"}], "References": [{"Title": "Deep feature learning for histopathological image classification of canine mammary tumors and human breast cancer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "405", "JournalTitle": "Information Sciences"}, {"Title": "A robust system for road sign detection and classification using LeNet architecture based on convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "9", "Page": "6721", "JournalTitle": "Soft Computing"}, {"Title": "Efficient and low complex architecture for detection and classification of Brain Tumor using RCNN with Two Channel CNN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "6229", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Brain Tumor Detection and Segmentation Using RCNN", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "3", "Page": "5005", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Deep Learning Model for Automatic Classification and Prediction of Brain Tumor", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Journal of Sensors"}, {"Title": "RETRACTED ARTICLE: CNN deep learning-based image to vector depiction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "13", "Page": "20283", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 117612873, "Title": "Implementasi Sistem Celengan Elektronik Menggunakan Metode K-Nearest Neighbor Berbasis Arduino MEGA 2560", "Abstract": "<p>Celengan merupakan wadah yang digunakan untuk menyimpan uang sebagai sarana menabung. Pada celengan tradisional, satu-satunya cara untuk mengambil uang yang ditabung adalah dengan memecahkan celengan tersebut. Sedangkan celengan modern umumnya sudah dilengkapi dengan kunci atau kode pin sehingga lebih mudah mengambil uang. <PERSON><PERSON>, jika pengguna tidak dapat mengendalikan pengeluaran, celengan ini memerlukan waktu lebih lama untuk terisi sampai penuh. <PERSON><PERSON> itu, kedua celengan ini tidak dapat menghitung total uang yang terkumpul secara otomatis. Pada penelitian ini, dibangun sistem celengan elektronik yang dapat mengatur target uang tabungan serta dapat mengetahui total uang dalam tabungan. Untuk mengetahui nominal uang yang dimasukkan ke dalam celengan, diterapkan klasifikasi nominal uang dengan metode K-NN (K-Nearest Neighbor). Sensor yang digunakan ialah sensor warna TCS3200 guna membaca nilai RGB uang dan dua sensor infrared untuk mendeteksi uang yang masuk dan jika celengan sudah penuh. Data dari sensor TCS3200 digunakan sebagai data uji untuk klasifikasi K-NN. Uang yang digunakan yaitu pecahan 10.000, 20.000, 50.000 dan 100.000 dari tahun emisi 2016 dan 2022. Berdasarkan hasil uji terhadap 40 data menggunakan confusion matrix, diperoleh tingkat akurasi tertinggi sistem senilai 95% pada data latih campuran dengan nilai K=5.</p>", "Keywords": "<PERSON>las<PERSON><PERSON>si; Nominal Uang; K-Nearest Neighbor; Celengan Elektronik; TCS3200", "DOI": "10.26418/jp.v10i2.81315", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Tanjungpura"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Tanjungpura"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Tanjungpura"}], "References": []}, {"ArticleId": 117612986, "Title": "Multivariate to Bivariate Reduction for Noncommutative Polynomial Factorization", "Abstract": "Based on <PERSON>'s theorem, we show that multivariate noncommutative polynomial factorization is deterministic polynomial-time reducible to the factorization of bivariate noncommutative polynomials. More precisely, 1. Given an n -variate noncommutative polynomial f ∈ F 〈 X 〉 over a field F as an arithmetic circuit, computing a complete factorization of f into irreducible factors is deterministic polynomial-time reducible to factorization of a noncommutative bivariate polynomial g ∈ F 〈 x , y 〉 ; the reduction transforms f into a circuit for g , and given a complete factorization of g , the reduction recovers a complete factorization of f in polynomial time. The reduction works both in the white-box and the black-box setting. 2. We show over the field of rationals that bivariate linear matrix factorization problem for 4 × 4 matrices is at least as hard as factoring square-free integers and for 3 × 3 matrices it is in polynomial time.", "Keywords": "", "DOI": "10.1016/j.ic.2024.105223", "PubYear": 2024, "Volume": "301", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematical Sciences, Chennai, India;Chennai Mathematical Institute, Siruseri, Kelambakkam, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vishwakarma Institute of Technology, Pune, India;Corresponding author"}], "References": []}, {"ArticleId": 117613012, "Title": "Meta-transfer learning-based method for multi-fault analysis and assessment in power system", "Abstract": "<p>As one of the largest and most complex artificial systems in the world, power systems present challenges for statistical analysis under multi-fault contingencies that alter the network topology. This paper proposes a meta-transfer learning-based (MTL) method, where base-learners are designed to learn the power flow mapping relationships for different network topologies, and a meta-learner is developed to guide the updating of structural parameters in base-learners in response to topological changes. The proposed MTL model quickly generates base-learners to adapt to new topologies and enables large-scale statistical analysis of multi-fault contingencies in power systems. The efficacy of the proposed method has been validated using the benchmark Institute of Electrical and Electronics Engineers (IEEE) 39-bus and 300-bus systems, specifically focusing on multi-fault contingencies. The results indicate that the proposed MTL model not only achieves high accuracy in dynamic topologies but also provides adaptive initial parameters for few/zero-shot learning within each topology.</p>", "Keywords": "Meta-transfer learning; Complex network; Multi-fault contingency; Statistical analysis; Power flow", "DOI": "10.1007/s10489-024-05772-9", "PubYear": 2024, "Volume": "54", "Issue": "23", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zheng", "Affiliation": "Polytechnic Institute, Zhejiang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Polytechnic Institute, Zhejiang University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Polytechnic Institute, Zhejiang University, Hangzhou, China; Corresponding author."}], "References": [{"Title": "Network embedding: Taxonomies, frameworks and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "", "Page": "100296", "JournalTitle": "Computer Science Review"}, {"Title": "An improved version of salp swarm algorithm for solving optimal power flow problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "5", "Page": "4027", "JournalTitle": "Soft Computing"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Adaptive Meta Transfer Learning with Efficient Self-Attention for Few-Shot Bearing Fault Diagnosis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "2", "Page": "949", "JournalTitle": "Neural Processing Letters"}, {"Title": "Power flow analysis via typed graph neural networks", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON>-<PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105567", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 117613041, "Title": "An open source in silico workflow to assist in the design of fusion proteins", "Abstract": "<p>Fusion proteins have the potential to become the new norm for targeted therapeutic treatments. Highly specific payload delivery can be achieved by combining custom targeting moieties, such as V<sub>HH</sub> domains, with active parts of proteins that have a particular activity not naturally targeted to the intended cells. Conversely, novel drug products may make use of the highly specific targeting properties of naturally occurring proteins and combine them with custom payloads. When designing such a product, there is rarely a known structure for the final construct which makes it difficult to assess molecular behaviour that may ultimately impact therapeutic outcome. Considering the time and cost of expressing a construct, optimising the purification procedure, obtaining sufficient quantities for biophysical characterisation, and performing structural studies in vitro, there is an enormous benefit to conduct in silico studies ahead of wet lab work. By following a repeatable, streamlined, and fast workflow of molecular dynamics assessment, it is possible to eliminate low-performing candidates from costly experimental work. There are, however, many aspects to consider when designing a novel fusion protein and it is crucial not to overlook some elements. In this work, we suggest a set of user-friendly, open-source methods which can be used to screen fusion protein candidates from the sequence alone. We used the light chain and translocation domain of botulinum toxin A (BoNT/A) fused with a selected V<sub>HH</sub> domain, termed here LC-H<sub>N</sub>-V<sub>HH</sub>, as a case study for a general approach to designing, modelling, and simulating fusion proteins. Its behaviour in silico correlated well with initial in vitro work, with SEC HPLC showing multiple protein states in solution and a dynamic protein shifting between these states over time without loss of material.</p><p>Copyright © 2024 The Authors. Published by Elsevier Ltd.. All rights reserved.</p>", "Keywords": "Computational biology;Fusion protein;Molecular dynamics", "DOI": "10.1016/j.compbiolchem.2024.108209", "PubYear": 2024, "Volume": "113", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biochemical Engineering, University College London, London, United Kingdom."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biochemical Engineering, University College London, London, United Kingdom."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSEN Bioinnovation, 5th Floor, The Point, 37 North Wharf Road, London W2 1AF, United Kingdom."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSEN Bioinnovation, 5th Floor, The Point, 37 North Wharf Road, London W2 1AF, United Kingdom."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biochemical Engineering, University College London, London, United Kingdom"}], "References": []}, {"ArticleId": 117613078, "Title": "Robust Control of Exo-Abs, a Wearable Platform for Ubiquitous Respiratory Assistance", "Abstract": "<p>Existing noninvasive breathing assist options compatible with out-of-hospital settings are limited and not appropriate to enable essential everyday activities, thereby deteriorating the quality of life. In our prior work, we developed the Exo-Abs, a novel wearable robotic platform for ubiquitous assistance of respiratory functions in patients with respiratory deficiency. This paper concerns the development of a model-based closed-loop control algorithm for the Exo-Abs to automate its breathing assistance. To facilitate model-based development of closed-loop control algorithms, we developed a control-oriented mathematical model of the Exo-Abs. Then, we developed a robust absolutely stabilizing gain-scheduled proportional-integral control algorithm for automating the breathing assistance with the Exo-Abs, by (i) solving a linear matrix inequality formulation of the Lyapunov stability condition against sector-bounded uncertainty and interindividual variability in the mechanics of the abdomen and the lungs and (ii) augmenting it with a heuristic yet effective gain scheduling algorithm. Using in silico evaluation based on realistic and plausible virtual patients, we demonstrated the efficacy and robustness of the automated breathing assistance of the Exo-Abs under a wide range of variability in spontaneous breathing and Exo-Abs efficiency: the absolutely stabilizing gain-scheduled proportional-integral control resulted in small exhalation trajectory tracking error (&lt;30 ml) with smooth actuation, which was superior to (i) its proportional-integral control counterpart in tracking efficacy and to (ii) its proportional-integral-derivative control counterpart in chattering.</p>", "Keywords": "Trajectories (Physics);Gain scheduling;Modeling;Errors;Lung;Control algorithms;Uncertainty;Lyapunov stability;Dynamics (Mechanics);Stability;Linear matrix inequalities;Design;Robustness;Robust control;Algorithms;Engines;Motors", "DOI": "10.1115/1.4066266", "PubYear": 2025, "Volume": "147", "Issue": "2", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Maryland , College Park, MD 20742"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, MIT , Cambridge, MA 02139; Massachusetts Institute of Technology"}, {"AuthorId": 3, "Name": "Kyujin Cho", "Affiliation": "Department of Mechanical Engineering, Seoul National University , Seoul 08826, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Maryland , College Park, MD 20742"}], "References": []}, {"ArticleId": 117613137, "Title": "Provably-stable neural network-based control of nonlinear systems", "Abstract": "In recent years, Neural Networks (NNs) have been employed to control nonlinear systems due to their potential capability in dealing with situations that might be difficult for conventional nonlinear control schemes. However, to the best of our knowledge, the current literature on NN-based control lacks theoretical guarantees for stability and tracking performance. This precludes the application of NN-based control schemes to systems where stringent stability and performance guarantees are required. To address this gap, this paper proposes a systematic and comprehensive methodology to design provably-stable NN-based control schemes for affine nonlinear systems. Rigorous analysis is provided to show that the proposed approach guarantees stability of the closed-loop system with the NN in the loop. Also, it is shown that the resulting NN-based control scheme ensures that system states asymptotically converge to a neighborhood around the desired equilibrium point, with a tunable proximity threshold. The proposed methodology is validated and evaluated via simulation studies on an inverted pendulum.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109252", "PubYear": 2024, "Volume": "138", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Materials Engineering, Washington State University, Pullman, WA 99164, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Materials Engineering, Washington State University, Pullman, WA 99164, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Materials Engineering, Washington State University, Pullman, WA 99164, USA;Corresponding author"}], "References": [{"Title": "A robust neural network approximation-based prescribed performance output-feedback controller for autonomous underwater vehicles with actuators saturation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "103382", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Lyapunov-based continuous-time nonlinear control using deep neural network applied to underactuated systems", "Authors": "Rosana C.B. Rego; <PERSON><PERSON><PERSON> de Araújo", "PubYear": 2022, "Volume": "107", "Issue": "", "Page": "104519", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 117613145, "Title": "Improvement of emotion recognition methods based on neural networks", "Abstract": "This article analyzes the use of microexpressions – subtle facial movements that are difficult for the human eye to notice, and even more difficult to immediately analyze, even specialists in the field do not always succeed in this perfectly, because their speed is only 1/5 to 1/3 of a second, for assessment of psychological state using artificial intelligence methods. The research is aimed at improving the analysis of micro-mimicry for accurate identification of emotions and psychological state. An overview of implemented technological solutions based on CNN was conducted, and a method for their improvement was found. An experimental test conducted on video recordings of people experiencing various emotions showed the high accuracy of the developed method in recognizing emotions and psychological state. Despite the challenges of the scarcity of microexpression datasets and the subtlety of facial movements, the paper presents a CapsuleNet model for microexpression recognition, builds a system architecture, and conducts testing. By combining three main data sets (SMIC, CASME II and SAMM) into a unified cross-database, the method developed in the work tests the possibility of generalization of the model by different subject characteristics. The performance of CapsuleNet, evaluated by cross-baseline benchmarking and Leave-One-Object-Out validation, significantly outperforms the baseline (LBP-TOP) and other improved of an CNN models. The paper shows that the performance of the developed model, determined by unweighted average recall and F1 scores, outperforms both the LBP-TOP baseline and other state-of-the-art CNN models. In a comprehensive microexpression recognition system. First, we process the data to identify the peak frames in the sequences and isolate the face region in these frames. These processed face images are then moved to CapsuleNet for the classification. The results of the work is to develop and complement methods of emotional artificial intelligence, offering new insights into micromimic assessment of psychological states that affect mental health, human-computer interaction, and social robotics. This technology has potential for development and expansion. This is an additional opportunity for companies that work with people and it is important for them to monitor their productivity, as it is directly related to the psychological state.", "Keywords": "", "DOI": "10.23939/ujit2024.01.058", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 86518, "JournalTitle": "Ukrainian Journal of Information Technology", "ISSN": "2707-1898", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117613173, "Title": "ANODYNE: Mitigating backdoor attacks in federated learning", "Abstract": "Federated learning (FL) allows participants to jointly train a model without leaking their sensitive datasets. The server is designed to have no visibility into how these updates are generated for privacy protection. Despite its benefits, FL is vulnerable to backdoor attacks, in which the compromised participants upload malicious model updates so that the backdoored model will misbehave for the chosen subtask. Existing defenses against backdoor attacks cannot handle state-of-the-art backdoor attacks that insert the backdoor in all rounds. To address these issues, we propose ANODYNE, a defense framework that hierarchically filters and clips the local model updates to mitigate the effect of backdoor attacks. ANODYNE decomposes the high-dimensional gradients into low-dimensional sub-vectors to improve detection performance and avoid the curse of dimensionality. Meanwhile, ANODYNE computes four different sub-vector metrics from a spatial–temporal perspective to enhance the robustness of our method. Our evaluation of ANODYNE on three datasets and three models demonstrates that ANODYNE competes over existing defenses under backdoor attacks.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125359", "PubYear": 2025, "Volume": "259", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Defense Technology, 109 Deya Road, Kaifu District, Changsha, 410000, Hunan, China"}, {"AuthorId": 2, "Name": "Jiang<PERSON>g Shi", "Affiliation": "National University of Defense Technology, 109 Deya Road, Kaifu District, Changsha, 410000, Hunan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Defense Technology, 109 Deya Road, Kaifu District, Changsha, 410000, Hunan, China;Corresponding author"}], "References": []}, {"ArticleId": 117613268, "Title": "Pattern formation in reaction-diffusion information propagation model on multiplex simplicial complexes", "Abstract": "Turing patterns for explaining spatial distribution in nature mostly focus on continuous media and existing networks, but only few attempts exist to study them on systems with high-order interactions. Considering that high-order interactions have a particularly significant impact on rumor propagation, this study establishes a generalized reaction-diffusion rumor propagation model based on a multiplex network, where simplicial complexes are employed to describe high-order structures. It aims to provide the spatial distribution patterns of the population participating in rumor propagation and identify the structural factors that affect such patterns. We theoretically provide the necessary conditions for Turing instability in single-layer and multiplex networks by considering high-order interactions. In the numerical simulation, we demonstrate that the Turing pattern could be controlled by adjusting the diffusion coefficient, high-order structure intensity, and average degree of the network. The results indicate that: (i) in a single-layer network, the Turing pattern only exists when high-order interactions appear, and the difference in diffusion rate plays a decisive role, (ii) in a multiplex network, the Turing pattern can still be observed under the same diffusion rates, which are affected by the difference in higher-order intensity between the two layers, and (iii) in existing networks, the average degree of the network has an important impact on Turing pattern. These findings contribute toward comprehending the impact of network structure on pattern formation, particularly the high-order interactions on the Turing pattern.", "Keywords": "", "DOI": "10.1016/j.ins.2024.121445", "PubYear": 2025, "Volume": "689", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Science, Harbin Institute of Technology, Shenzhen, 518055, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Harbin Institute of Technology, Shenzhen, 518055, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Science, Harbin Institute of Technology, Shenzhen, 518055, PR China;Corresponding author"}], "References": [{"Title": "Turing instability induced by complex networks in a reaction–diffusion information propagation model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "762", "JournalTitle": "Information Sciences"}, {"Title": "Complex dynamic analysis of a reaction-diffusion network information propagation model with non-smooth control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "1141", "JournalTitle": "Information Sciences"}, {"Title": "Influence maximization algorithm based on group trust and local topology structure", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "564", "Issue": "", "Page": "126936", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 117613293, "Title": "Deterministic 3-Server on a Circle and the Limitation of Canonical Potentials", "Abstract": "The deterministic k -server conjecture states that there is a k -competitive deterministic algorithm for the k -server problem for any metric space. We show that the work function algorithm is 3-competitive for the 3-server problem on circle metrics, a case left open by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (2021). Our analysis follows the existing framework but introduces a new potential function which may be viewed as a relaxation of the counterpart by <PERSON><PERSON> and <PERSON><PERSON> (2021). We further notice that the new potential function and many existing ones can be rewritten in a canonical form. Through a computer-aided verification, however, we find that no such canonical potential function can resolve the deterministic 3-server conjecture for general metric spaces under the current analysis framework.", "Keywords": "", "DOI": "10.1016/j.tcs.2024.114844", "PubYear": 2024, "Volume": "1020", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Corresponding authors"}], "References": [{"Title": "Chasing Convex Bodies with Linear Competitive Ratio", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "5", "Page": "1", "JournalTitle": "Journal of the ACM"}]}, {"ArticleId": 117613322, "Title": "Dataset on the Decolorization of Naphthol Green B using a UV/Sulfite system: Optimization by response surface methodology", "Abstract": "Naphthol Green B (NGB) is a synthetic azo dye widely used in various industries, including textiles and leathers. NGB poses significant environmental and ecological concerns when released into natural water systems. This paper investigates the decolorization of NGB using UV/sulfite system. The % decolorization of NGB was optimized using 3<sup>2</sup> Full Factorial Design (FFD), and the ANOVA results show that the model has a good fit for the data (R<sup>2</sup> = 99.54 %, R<sup>2</sup><sub>(adj)</sub> = 98.76 %) and the significant factors contributing to the % decolorization are A, B, A<sup>2</sup>, and B<sup>2</sup> where A = mM sulfite and B = pH. The model predicted ≥100 % decolorization with the optimum conditions 12 mM sulfite and pH 10. An actual experiment was conducted to verify the response, resulting in 96.2 % decolorization which is in good agreement with the model.", "Keywords": "Response surface methodology; Advanced oxidation/reduction process; UV/sulfite; Naphthol Green B (NGB)", "DOI": "10.1016/j.dib.2024.110924", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, College of Science, De La Salle University, 2401 Taft Ave, Malate, Manila 1004, Metro Manila, Philippines"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, College of Science, De La Salle University, 2401 Taft Ave, Malate, Manila 1004, Metro Manila, Philippines"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, College of Science, De La Salle University, 2401 Taft Ave, Malate, Manila 1004, Metro Manila, Philippines;Department of Chemistry, College of Science, Adamson University, 900 San Marcelino St, Ermita, Manila 1000, Metro Manila, Philippines;Corresponding author at: Department of Chemistry, College of Science, De La Salle University, 2401 Taft Ave, Malate, Manila 1004, Metro Manila, Philippines"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, College of Science, De La Salle University, 2401 Taft Ave, Malate, Manila 1004, Metro Manila, Philippines;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, College of Science, De La Salle University, 2401 Taft Ave, Malate, Manila 1004, Metro Manila, Philippines"}], "References": []}, {"ArticleId": 117613410, "Title": "Computing Typology: Generative Design for Creating Housing Solutions from Type Analysis in Bogota", "Abstract": "The article delves into the integration of a typological discourse within contemporary design practice, offering insights into a generative system aimed at processing design scenarios from typological analyses. By identifying architectural types within the context of Bogota’s periphery, this research showcases the potential of computational methods to formalize typological principles in design. It also explores the possibilities of digital design to facilitate the production of customized housing solutions on a mass scale. This study contributes to the debate about the role of typology in design practice, highlighting the possibilities inherent in leveraging computational tools to enhance architectural synthesis. Additionally, it investigates the potential of a digital design framework to offer alternative solutions to the housing crisis, addressing the growing demand for affordable, high-quality housing in Latin America.", "Keywords": "Typological design; Digital design; Generative design; Customized mass-housing; Bogota", "DOI": "10.1007/s00004-024-00796-7", "PubYear": 2025, "Volume": "27", "Issue": "1", "JournalId": 7250, "JournalTitle": "Nexus Network Journal", "ISSN": "1590-5896", "EISSN": "1522-4600", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Cif<PERSON> Q<PERSON>", "Affiliation": "Universidad de La Salle, Bogotá, Colombia; Corresponding author."}], "References": []}, {"ArticleId": 117613440, "Title": "Forecasting of soil moisture using machine learning in smart agriculture systems", "Abstract": "Growing crops in modern conditions is a complex task and practically combines the practices of experience and the latest methods, including information technology, which has become part of the concept of \"smart farming\". An important factor in the stable predicted yield is the level of soil moisture, which is the result of changes in climatic factors such as air temperature, soil temperature, intensity of solar radiation, rainfall, wind speed, etc. A methodology for processing real historical indicators of climate change in a certain geographical area with subsequent training and application of machine learning models to predict soil moisture is proposed. To build a machine learning model, the following algorithms were selected and studied: the algorithm of regression trees, random forest, linear regression, M5P algorithms and the K* algorithm. The data source for training the models is the open information resource International Soil Moisture Network (ISMN) from ismn.earth/en. , which provides data on soil moisture and temperature, air temperature, and rainfall. Other data was used from the Open Meteo information service, which provides a free API and allows you to get historical data and weather forecast in specified coordinates during specified days.  A data structure was developed to train the model for further prediction of soil moisture.  An architecture has been developed and a software system for predicting soil moisture based on machine learning algorithms has been created using the Spring Framework, the WEKA library and Java FX with the ability to select and study the appropriate algorithms. Experiments have been carried out and the results of the duration of model training have been presented, while the algorithms of regression trees and linear regression require the least training time. A comparison of algorithms is made according to the following criteria: learning speed, cross-testing speed, prediction speed, testing performance indicators for real historical data.  Based on the results of the study, conclusions are drawn about individual algorithms, the feasibility of using them to predict soil moisture based on climatic indicators.  The obtained results will make it possible to evaluate and select the best models of machine learning in the design of the information and analytical system \"smart agriculture\" for forecasting soil moisture.", "Keywords": "", "DOI": "10.23939/ujit2024.01.026", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 86518, "JournalTitle": "Ukrainian Journal of Information Technology", "ISSN": "2707-1898", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Machine learning applications in production lines: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106773", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 117613442, "Title": "Pengenalan Setengah Wajah menggunakan Arsitektur Xception pada Metode Convolutional Neural Network", "Abstract": "<p>Penggunaan teknologi pengenalan wajah merupakan cara untuk mengidentifikasi seseorang berdasarkan ciri-ciri wajah. Penelitian ini berfokus pada pengenalan setengah wajah bagian atas dalam kondisi dimana hanya setengah wajah tersebut yang dapat diakses atau terlihat. Menggunakan metode yang melibatkan arsitektur Xception pada Convolutional Neural Network (CNN) untuk mengekstraksi fitur kompleks dari setengah wajah, termasuk dahi, alis dan mata. Data yang digunakan berasal dari absensi karyawan, termasuk 1020 dataset wajah tidak menggunakan masker dan 114 dataset wajah yang menggunakan masker. Penelitan ini menggunakan skenario pembagian data latih dan data uji dengan rasio 95:5, 90:10, 85:15, dan 80:20. Hasil penelitian menunjukkan nilai accuracy, precission, recall, dan f1-score terbaik terdapat pada pembagian data 95:15 yang masing-masing bernilai 95%, 96%, 96%, dan 95%. Hasil ini dapat digunakan untuk kontribusi pengembangan model pengenalan wajah dengan akurasi yang tinggi terutama dalam situasi di mana hanya informasi sebagian wajah yang dapat diakses.</p>", "Keywords": "Pengenalan Wajah; <PERSON><PERSON><PERSON> Wajah;  Xception; Convolutional Neural Network (CNN)", "DOI": "10.26418/jp.v10i2.73447", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Mercubuana"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Mercubuana"}], "References": []}, {"ArticleId": 117613471, "Title": "Ultrasensitive xylene sensor based on RuO2-modified BiVO4 nanosheets", "Abstract": "Two-dimensional (2D) BiVO<sub>4</sub> nanosheets (NSs), featuring distinctive chemical properties and dangling-bond-rich surfaces, are promising for developing high-performance gas sensors. However, the previously reported 2D BiVO<sub>4</sub> NSs-based devices suffer from a low responsivity and poor selectivity. In this work, we introduce RuO<sub>2</sub> nanoparticles to boost the gas sensing properties of 2D BiVO<sub>4</sub> NSs-based sensor. Intriguingly, the RuO<sub>2</sub>-decoration changes the selectivity of the corresponding gas sensor from acetone to xylene. Compared to the pristine BiVO<sub>4</sub> sensor, the RuO<sub>2</sub>-BiVO<sub>4</sub> sensor displays a reduced optimal operating temperature of 260℃ and exhibits a response of 19.0–5 ppm xylene, reaching a five-fold enhancement. Simultaneously, the RuO<sub>2</sub>-BiVO<sub>4</sub> sensor also exhibits a rapid response and recovery time of 10 and 25 s respectively, as well as robust long-term stability with less than 5 % variation in response after a storage of 60 days. The improvement in sensing performance can be attributed to the RuO<sub>2</sub>-decoration from three aspects: heterojunction construction, surface state modulation and gas oxidation catalysis on the surface. This work not only provides a high-performance xylene sensing material but also provides insightful comprehension regarding the modification effects of RuO<sub>2</sub>.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136623", "PubYear": 2025, "Volume": "422", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physics, Jilin University, Changchun 130012, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry, Jilin University, Changchun 130012, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of neurology, The First Clinical Hospital, Norman Bethune College of Medicine of Jilin University, Changchun 130021, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry, Jilin University, Changchun 130012, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Microelectronics, Northwestern Polytechnical University, Xi’an 710072, China;Yangtze River Delta Research Institute of NPU, Taicang 215400, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Public Technology Center, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo 315201, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry, Jilin University, Changchun 130012, China"}, {"AuthorId": 8, "Name": "Fengdong Qu", "Affiliation": "School of Microelectronics, Northwestern Polytechnical University, Xi’an 710072, China;Yangtze River Delta Research Institute of NPU, Taicang 215400, China;Corresponding author at: School of Microelectronics, Northwestern Polytechnical University, Xi’an 710072, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "College of Chemistry, Jilin University, Changchun 130012, China;Corresponding author"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry, Jilin University, Changchun 130012, China"}], "References": [{"Title": "Porous nanosheets assembled Co3O4 hierarchical architectures for enhanced BTX (Benzene, Toluene and Xylene) gas detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "315", "Issue": "", "Page": "128120", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Novel p-n heterojunction of BiVO4/Cu2O decorated with rGO for low concentration of NO2 detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "320", "Issue": "", "Page": "128284", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "rGO decorated NiO-BiVO4 heterojunction for detection of NO2 at low temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "128912", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Ultrafast H2 gas nanosensor for ppb-level H2 gas detection based on GaN honeycomb nanonetwork", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129079", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "In2O3 nanocubes modified with RuO2 for detection of TXM vapors containing benzyl group", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "338", "Issue": "", "Page": "129731", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "MOF-derived Au-loaded Co3O4 porous hollow nanocages for acetone detection", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130182", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly sensitive and selective xylene sensor based on p-p heterojunctions composites derived from off-stoichiometric cobalt tungstate", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "351", "Issue": "", "Page": "130973", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Ultrafast response and high-sensitivity acetone gas sensor based on porous hollow Ru-doped SnO2 nanotubes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "352", "Issue": "", "Page": "131061", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A n-butanol gas sensor with enhanced gas sensing performance based on Co‐doped BiVO4 polyhedrons", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "354", "Issue": "", "Page": "131221", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Pt-modified BiVO4 nanosheets for enhanced acetone sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "389", "Issue": "", "Page": "133853", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 117613597, "Title": "Enhanced threat intelligence framework for advanced cybersecurity resilience", "Abstract": "The increasing severity of cyber-attacks against organizations emphasizes the necessity for efficient threat intelligence. This article presents a novel multi-layered architecture for threat intelligence that integrates diverse data streams, including corporate network logs, open-source intelligence, and dark web monitoring, to offer a comprehensive overview of the cybersecurity threat landscape. Our approach, distinct from previous studies, uniquely integrates these varied features into the machine-learning algorithms (XGBoost, Gradient Boosting, LightGBM, Extra Trees, Random Forest, Decision Tree, K-Nearest Neighbor, Gaussian Naive Bayes, Support Vector Machine, Linear Discriminant Analysis, Logistic Regression, ridge Classifier, AdaBoost and Quadratic Discriminant Analysis) using various feature selection algorithms (information gain, correlation coefficient, chi-square, fisher score, forward wrapper, backward wrapper, Ridge classifier) to enhance real-time threat detection and mitigation. The practical LITNET-2020 dataset was utilized to evaluate the proposed architecture. Extensive testing against real-world cyber-attacks, including malware and phishing, demonstrated the robustness of the architecture, achieving exceptional results. Specifically, XGBoost demonstrated the highest performance with a detection accuracy of 99.98%, precision of 99.97%, and recall of 99.96%, Significantly surpassing traditional methods. Gradient Boosting and LightGBM also exhibited excellent performance, with accuracy, precision, and recall values of 99.97%. Our findings underscore the effectiveness of our architecture in significantly improving an organization’s capability to identify and counteract online threats in real-time. By developing a comprehensive threat intelligence framework, this study advances the field of cybersecurity, providing a robust tool for enhancing organizational resilience against cyber-attacks.", "Keywords": "Cybersecurity; Threat intelligence; Network intrusion; Mitigation and response; Cyber attacks; Data breaches; Threat landscape", "DOI": "10.1016/j.eij.2024.100521", "PubYear": 2024, "Volume": "27", "Issue": "", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [{"AuthorId": 1, "Name": "Moutaz Al<PERSON>ab", "Affiliation": "Department of Intelligent System, Faculty of Artificial Intelligence, Albalqa Applied University, Al-Salt, 19117, Jordan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology Department, Al-Huson University College, Albalqa Applied University, Irbid, 19117, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>-Arenas", "Affiliation": "Department of Computer Engineering, Automatics and Robotics, University of Granada, Granada, 18071, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of CSE, SRM University, Kattankulathur, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computing and Data Sciences, Oryx Universal College with Liverpool John Moores University, Qatar"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Real Time Computer Systems, Kaunas University of Technology, Kaunas, 19117, Lithuania"}], "References": [{"Title": "A Hybrid Efficient Distributed Clustering Algorithm Based Intrusion Detection System to Enhance Security in MANET", "Authors": "CR Rathish; K Karpagavadivu; P Sindhuja", "PubYear": 2021, "Volume": "50", "Issue": "1", "Page": "45", "JournalTitle": "Information Technology And Control"}, {"Title": "An optimal defensive deception framework for the container‐based cloud with deep reinforcement learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "3", "Page": "178", "JournalTitle": "IET Information Security"}, {"Title": "Cloud-Based Business Process Security Risk Management: A Systematic Review, Taxonomy, and Future Directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "12", "Page": "160", "JournalTitle": "Computers"}, {"Title": "A Comprehensive Review of Deep Learning Techniques for the Detection of (Distributed) Denial of Service Attacks", "Authors": "<PERSON>. <PERSON>; P. <PERSON><PERSON>; S. <PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "1", "Page": "180", "JournalTitle": "Information Technology And Control"}, {"Title": "Intrusion detection and prevention in fog based IoT environments: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "109154", "JournalTitle": "Computer Networks"}, {"Title": "An integrated cyber security risk management framework and risk predication for the critical infrastructure protection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "18", "Page": "15241", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Decision Tree with Pearson Correlation-based Recursive Feature Elimination Model for Attack Detection in IoT Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "4", "Page": "771", "JournalTitle": "Information Technology And Control"}, {"Title": "Artificial intelligence for cybersecurity: Literature review and future research directions", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "97", "Issue": "", "Page": "101804", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 117613694, "Title": "Learning attention characterization based on head pose sight estimation", "Abstract": "<p>The degree of students’ attentiveness in the classroom is known as learning attention and is the main indicator used to portray students’ learning status in the classroom. Studying smart classroom time-series image data and analyzing students’ attention to learning are important tools for improving student learning effects. To this end, this paper proposes a learning attention analysis algorithm based on the head pose sight estimation.The algorithm first employs multi-scale hourglass attention to enable the head pose estimation model to capture more spatial pose features.It is also proposed that the multi-classification multi-regression losses guide the model to learn different granularity of pose features, making the model more sensitive to subtle inter-class distinction of the data;Second, a sight estimation algorithm on 3D space is innovatively adopted to compute the coordinates of the student’s sight landing point through the head pose; Finally, a model of sight analysis over the duration of a knowledge point is constructed to characterize students’ attention to learning. Experiments show that the algorithm in this paper can effectively reduce the head pose estimation error, accurately characterize students’ learning attention, and provide strong technical support for the analysis of students’ learning effect. The algorithm demonstrates its potential application value and can be deployed in smart classrooms in schools.</p>", "Keywords": "Learning attention; Multi-classification multi-regression; Head pose; Sight estimation; Learning effect analysis", "DOI": "10.1007/s11042-024-20204-z", "PubYear": 2024, "Volume": "83", "Issue": "38", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication, Guilin University of Electronic Technology, Guilin, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Security, Guilin University of Electronic Technology, Guilin, China"}], "References": [{"Title": "Improving head pose estimation using two-stage ensembles with top-k regression", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "103827", "JournalTitle": "Image and Vision Computing"}, {"Title": "Improving head pose estimation using two-stage ensembles with top-k regression", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "103827", "JournalTitle": "Image and Vision Computing"}, {"Title": "NGDNet: Nonuniform Gaussian-label distribution learning for infrared head pose estimation and on-task behavior understanding in the classroom", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "436", "Issue": "", "Page": "210", "JournalTitle": "Neurocomputing"}, {"Title": "A novel approach for driver fatigue detection based on visual characteristics analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "527", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Head pose estimation using facial-landmarks classification for children rehabilitation games", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "406", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 117613810, "Title": "Computational Fluid Dynamics (CFD)- Deep Neural Network (DNN) Model to Predict Hydrodynamic Parameters in Rectangular and Cylindrical Bubble Columns", "Abstract": "Bubble columns are omnipresent in the chemical, bio-chemical, petrochemicals, petroleum industries, but their design and scale-up is complex owing to its complex hydrodynamics. Liquid velocity and gas holdup is one of the critical hydrodynamic parameters which effects the mixing, heat and mass transfer in bubble columns. CFD is widely recognized as a powerful tool for estimating critical hydrodynamic parameters but requires significant computational resources, time and expertise. These limitations restrict its practical use in hydrodynamic simulations that need real-time processing involving large-scale simulations of bubble columns. To overcome these limitations, CFD-DNN model is developed to predict the time averaged gas holdup and axial liquid velocity at various operating conditions. The DNN model was trained using CFD data that was produced for rectangular (with dimensions L=0.2 m, W=0.05 m, H=1.2 m) and cylindrical (with a diameter of 0.19 m) bubble columns. The data covers a range of operating conditions and various flow regimes. The superficial gas velocity for the rectangle column was selected at 1.33 and 7.3 mm/s, whereas for the cylindrical bubble column, it was fixed at 0.02 and 0.12 m/s. The CFD-DNN model was validated against the experimental and the CFD data from the literature. Further, the model was tested for new data that the CFD-DNN model has not seen with existing literature and showed good agreement with their data and it reflects the excellent generalization ability of the model. The proposed CFD-DNN approach improves current CFD models by providing shorter computing time, decreasing computational expenses, and reducing the expertise in CFD simulations. The accuracy of the developed CFD-DNN model was evaluated using different metrics for gas holdup and axial liquid velocity. For rectangular bubble columns, the model achieved MSE of 0.0001 for gas holdup and 0.0007 for axial liquid velocity. Similarly, for cylindrical bubble columns, the MSE values were 0.0009 for gas holdup and 0.0006 for axial liquid velocity.", "Keywords": "Bubble column; CFD; Machine learning (ML); Neural networks; CFD-DNN", "DOI": "10.1016/j.dche.2024.100185", "PubYear": 2024, "Volume": "13", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology, Jammu, J&K, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology, Jammu, J&K, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology, Jammu, J&K, India;Corresponding author"}], "References": [{"Title": "Hidden representations in deep neural networks: Part 2. Regression problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106895", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 117613837, "Title": "Code and Data Repository for Clustering Then Estimation of Spatio-Temporal Self-Exciting Processes", "Abstract": "", "Keywords": "spatio-temporal self-exciting point process; maximum likelihood estimation; clustering algorithm", "DOI": "10.1287/ijoc.2022.0351.cd", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "Haoting <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>han", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117613918, "Title": "Multi-objective Ant Colony Optimization: Review", "Abstract": "<p>Ant colony optimization (ACO) algorithm is one of the most popular swarm-based algorithms inspired by the behavior of an ant colony to find the shortest path for food. The multi-objective ACO (MOACO) is a modified variant of ACO introduced to deal with multi-objective optimization problems (MOPs). The MOACO is seeking to find a set of solutions that achieve trade-offs between the different objectives, which help the decision-makers select the most appreciated solution according to their preferences. Recently, a large number of MOACO research works have been published in the literature, reaching 384 research papers according to the SCOPUS database. In this review paper, 189 different research works of MOACOs published in only scientific journals are considered. Through this research, researchers will gain insights into the expansion of MOACO, the theoretical foundations of MOPs and the MOACO algorithm, various MOACO variants documented in existing literature will be reviewed, and the specific application domains where MOACO has been implemented will be summarized. The critical discussion of the MOACO advantages and limitations is analyzed to provide better insight into the main research gaps in this domain. Finally, the conclusion and some possible future research directions of MOACO are also given in this work. </p>", "Keywords": "", "DOI": "10.1007/s11831-024-10178-4", "PubYear": 2025, "Volume": "32", "Issue": "2", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Al-Aqsa University, Gaza, Palestine; Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, King <PERSON> School of Information Technology, The University of Jordan, Amman, Jordan; Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates; Information Technology Department, College of Engineering and Information Technology, Ajman University, Ajman, United Arab Emirates; Department of Information Technology, Al-Huson University College, Al-Balqa Applied University, Irbid, Jordan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates"}, {"AuthorId": 7, "Name": "Oussama S. <PERSON>", "Affiliation": "Management Information System Department, Al-Aqsa University, Gaza, Palestine"}], "References": [{"Title": "A multi-objective ant colony optimization algorithm for community detection in complex networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "5", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A decomposition-based ant colony optimization algorithm for the multi-objective community detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "173", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Energy enhancement using Multiobjective Ant colony optimization with Double Q learning algorithm for IoT based cognitive radio networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "481", "JournalTitle": "Computer Communications"}, {"Title": "A Hybrid Ant Colony Optimization and Simulated Annealing Algorithm for Multi-Objective Scheduling of Cellular Manufacturing Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "1", "JournalTitle": "International Journal of Applied Metaheuristic Computing"}, {"Title": "Ant Colony Optimization for Multi-Objective Multicast Routing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "3", "Page": "1159", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A multi-objective decomposition-based ant colony optimisation algorithm with negative pheromone", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>g <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "827", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Improved VGG model-based efficient traffic sign recognition for safe driving in 5G scenarios", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "3069", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A dynamic configuration with a shared knowledge centre for multi-objective ant colony optimisation algorithms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "6", "Page": "541", "JournalTitle": "International Journal of Intelligent Systems Technologies and Applications"}, {"Title": "Efficient Task Scheduling in Cloud Computing using Multi-objective Hybrid Ant Colony Optimization Algorithm for Energy Efficiency", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "450", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Modified A* Algorithm integrated with ant colony optimization for multi-objective route-finding; case study: Yazd", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107877", "JournalTitle": "Applied Soft Computing"}, {"Title": "GenACO a multi-objective cached data offloading optimization based on genetic algorithm and ant colony optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Low-Energy Secure Routing Protocol for WSNs Based on Multiobjective Ant Colony Optimization Algorithm", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Sensors"}, {"Title": "An efficient multi-objective ant colony optimization for task allocation of heterogeneous unmanned aerial vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "58", "Issue": "", "Page": "101545", "JournalTitle": "Journal of Computational Science"}, {"Title": "Applying ontology learning and multi-objective ant colony optimization method for focused crawling to meteorological disasters domain knowledge", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116741", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An ant colony optimization algorithm with evolutionary experience-guided pheromone updating strategies for multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "117151", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multiobjective Optimization Management of Construction Engineering Based on Ant Colony Algorithm", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Journal of Control Science and Engineering"}, {"Title": "New Solar MPPT Control Technique Based on Incremental Conductance and Multi-Objective Ant Colony Optimization", "Authors": "Fathallah Rerhrhaye; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "113", "JournalTitle": "International Review of Automatic Control (IREACO)"}, {"Title": "Hybrid multi-objective method based on ant colony optimization and firefly algorithm for renewable energy sources", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "", "Page": "100810", "JournalTitle": "Sustainable Computing: Informatics and Systems"}, {"Title": "Improved ant colony algorithm in path planning of a single robot and multi-robots with multi-objective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "3", "Page": "1313", "JournalTitle": "Evolutionary Intelligence"}]}, {"ArticleId": 117613983, "Title": "Power-Line Road Segmentation Menggunakan Improved Residual Networks", "Abstract": "<p>Pemetaan jalan menjadi komponen penting dalam pengembangan infrastruktur jalan untuk mendukung kebutuhan mobilitas yang semakin kompleks. Pemetaan jalan memiliki cakupan areal yang sangat luas dan pendataannya cukup sulit, namun pemetaan jalan masih dilakukan secara manual. Pemetaan jalan dapat memalui citra satelit, namun karena dilakukan secara manual maka memerlukan waktu yang cukup lama. Dalam perkembangannya dibutuhkan pemetaan jalan secara otomatis salah satunya dengan menggunakan machine learning berdasarkan fitur-fitur dari citra satelit yang telah ditentukan. Namun  pengambilan fitur atau informasi yang diperoleh dari citra satelit memiliki kendala yang cukup sulit untuk diatasi, antara lain persamaan warna dan bentuk yang mempunyai kemiripan. <PERSON><PERSON> karena itu pada penelitian ini diajukan metode deep learning berbasis U-Net dengan susunan residual block untuk mengatasi permasalahan tersebut. Data yang digunakan pada penelitian ini adalah Massachusetts Road Dataset berupa data citra satelit beresolusi tinggi. Pemetaan jalan dilakukan dengan menggunnakan metode Improved Residual Networks. Hasil pengujian model menunjukkan nilai Precision 81.6%, Recall 77.9%, Accuracy 98.1%, dan F1-score 79.7%. Kinerja tersebut lebih baik dari sejumlah penelitian sebelumnya.</p>", "Keywords": "Improved Residual Networks;Pemetaan Jalan;Massachusetts Road Dataset;Deep Learning;Citra Satelit", "DOI": "10.26418/jp.v10i2.77946", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}], "References": []}, {"ArticleId": 117614034, "Title": "Penerapan Framework COBIT 2019 pada Evaluasi Tata Kelola Teknologi Informasi di Yayasan Pendidikan", "Abstract": "<p>Pen<PERSON>tian ini bertujuan untuk mengevaluasi tata kelola teknologi informasi di yayasan pendidikan, yaitu Yayasan Bethel Area (YBA) yang menaungi sekolah PAUD Satria Tunas Bangsa. Urgensi penerapan COBIT 2019 pada tata kelola yayasan pendidikan ini adalah dalam kepentingan menghasilkan layanan TI yang lebih sesuai dengan kebutuhan organisasi. Evaluasi menjadi sangat diperlukan karena analisis tata kelola TI yang belum pernah dilakukan dan keadaan yayasan yang menunjukkan adanya kebutuhan organisasi yang tinggi untuk pemanfaatan TI dalam penyelenggaraan pendidikan di masa mendatang. Riset ini menerapkan COBIT 2019 sebagai kerangka kerja. Setelah menyelesaikan tahapan riset menggunakan 11 faktor pada design factor toolkit dan menyebarkan kuesioner sesuai panduan RACI, maka penelitian ini menyimpulkan bahwa YBA perlu memberi perhatian khusus pada tiga layanan tata kelola TI, yaitu pengelolaan program (BAI01), pengelola<PERSON> perubahan organisasi (BAI05), dan pengelolaan proyek (BAI11). Kapabilitas pengelolaan program (BAI01) pada YBA berada pada level 2, menunjukkan bahwa proses ini telah mencapai tujuannya melalui penerapan aktivitas dasar, namun aktivitas tersebut telah lengkap. Adapun kapabilitas pengelolaan perubahan organisasi (BAI05) dan pengelolaan proyek (BAI11) masih pada level 1, menjelaskan bahwa kedua proses ini kurang lebih mencapai tujuannya melalui pelaksanaan aktivitas, meskipun belum lengkap, serta masih pada tahapan aktvitas awal dan belum terlalu terorganisir. Untuk meningkatkan kapabilitas layanan pengelolaan program (BAI01) dan pengelolaan proyek (BAI11), rekomendasi untuk yayasan adalah pembentukan fungsi manajemen proyek TI, yang fokus pada operasional TI, serta bertugas menyusun dan membangun infrastruktur tata kelola TI, termasuk didalamnya adalah panduan, standar dan kebijakan. Sedangkan untuk meningkatkan kapabilitas pengelolaan perubahan organisasi (BAI05), saran bagi yayasan adalah mengevaluasi rentang dan implikasi perubahan, mengidentifikasi pihak yang terkena dampak dan tingkat keterlibatan, serta mengidentifikasi faktor eksternal baik itu ancaman maupun peluang.</p>", "Keywords": "COBIT 2019;Tata Kelola TI;Proses Objektif;Tingkat Kapabilitas;Yayasan", "DOI": "10.26418/jp.v10i2.79297", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}], "References": []}, {"ArticleId": 117614042, "Title": "Reconfiguration of vertex-disjoint shortest paths on graphs", "Abstract": "We introduce and study reconfiguration problems for (internally) vertex-disjoint shortest paths:Given two tuples of internally vertex-disjoint shortest paths for fixed terminal pairs in an unweighted graph, we are asked to determine whether one tuple can be transformed into the other by exchanging a single vertex of one shortest path in the tuple at a time, so that all intermediate results remain tuples of internally vertex-disjoint shortest paths.We also study the shortest variant of the problem, that is, we wish to minimize the number of vertex-exchange steps required for such a transformation, if exists. These problems generalize the well-studied Shortest Path Reconfiguration problem. In this paper, we analyze the complexity of these problems from the viewpoint of graph classes, and give some interesting contrast.", "Keywords": "reconfiguration problems;Shortest Path Reconfiguration", "DOI": "10.7155/jgaa.v28i3.2973", "PubYear": 2024, "Volume": "28", "Issue": "3", "JournalId": 16217, "JournalTitle": "Journal of Graph Algorithms and Applications", "ISSN": "", "EISSN": "1526-1719", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tohoku University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kyushu Institute of Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tohoku University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Japan Advanced Institute of Science and Technology"}], "References": []}, {"ArticleId": 117614057, "Title": "Multidimensional time series motif group discovery based on matrix profile", "Abstract": "With the continuous advancements in sensor technology and the increasing capabilities for data collection and storage, the acquisition of time series data across diverse domains has become significantly easier. Consequently, there is a growing demand for identifying potential motifs within multidimensional time series. The introduction of the Matrix Profile (MP) structure and the mSTOMP algorithm enables the detection of multidimensional motifs in large-scale time series datasets. However, the Matrix Profile (MP) does not provide information regarding the frequency of occurrence of these motifs. As a result, it is challenging to determine whether a motif appears frequently or to identify the specific time periods during which it typically occurs, thereby limiting further analysis of the discovered motifs. To address this limitation, we proposed Index Link Motif Group Discovery (ILMGD) algorithm, which uses index linking to rapidly merge and group multidimensional motifs. Based on the results of the ILMGD algorithm, we can determine the frequency and temporal positions of motifs, facilitating deeper analysis. Our proposed method requires minimal additional parameters and reduces the need for extensive manual intervention. We validate the effectiveness of our algorithm on synthetic datasets and demonstrate its applicability on three real-world datasets, highlighting how it enables a comprehensive understanding of the discovered motifs.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112509", "PubYear": 2024, "Volume": "304", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Danyang Cao", "Affiliation": "College of Information science and Technology, North China University of Technology, No 5, Jin Yuan Zhuang Road Beijing, Beijing, 100144, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information science and Technology, North China University of Technology, No 5, Jin Yuan Zhuang Road Beijing, Beijing, 100144, China"}], "References": [{"Title": "Time series motifs discovery under DTW allows more robust discovery of conserved structure", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "3", "Page": "863", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Multivariate time series clustering based on complex network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "107919", "JournalTitle": "Pattern Recognition"}, {"Title": "Artificial intelligence and healthcare: Forecasting of medical bookings through multi-source time-series fusion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "74", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "Frequent pattern mining from multivariate time series data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "194", "Issue": "", "Page": "116435", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Time Series Compression Survey", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "10", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Practical joint human-machine exploration of industrial time series using the matrix profile", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "1", "Page": "1", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "MASS: distance profile of a query over a time series", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "38", "Issue": "3", "Page": "1466", "JournalTitle": "Data Mining and Knowledge Discovery"}]}, {"ArticleId": 117614095, "Title": "A New Notion of Regularity: Finite State Automata Accepting Graphs", "Abstract": "Analogous to regular string and tree languages, regular languages of directed acyclic graphs (DAGs) are defined in the literature. Although called regular, those DAG-languages are more powerful and, consequently, standard problems have a higher complexity than in the string case. Top-down as well as bottom-up deterministic DAG languages are subclasses of the regular DAG languages. We refine this hierarchy by providing a weaker subclass of the deterministic DAG languages. For a DAG grammar generating a language in this new DAG language class, or, equivalently, a DAG-automaton recognizing it, a classical deterministic finite state automaton (DFA) can be constructed. As the main result, we provide a characterization of this class. The motivation behind this is the transfer of techniques for regular string languages to graphs. Trivially, our restricted DAG language class is closed under union and intersection. This permits the application of minimization and hyper-minimization algorithms known for DFAs. This alternative notion of regularity coins at the existence of a DFA for recognizing a DAG language.", "Keywords": "", "DOI": "10.4204/EPTCS.407.2", "PubYear": 2024, "Volume": "407", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Bremen, Germany"}], "References": [{"Title": "A faster algorithm for maximum independent set on interval filament graphs", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "199", "JournalTitle": "Journal of Graph Algorithms and Applications"}, {"Title": "4Ward: A relayering strategy for efficient training of arbitrarily complex directed acyclic graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "568", "Issue": "", "Page": "127058", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 117614204, "Title": "WBDPR: a way for big data provenance relationship", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCSE.2024.141374", "PubYear": 2024, "Volume": "27", "Issue": "5", "JournalId": 8309, "JournalTitle": "International Journal of Computational Science and Engineering", "ISSN": "1742-7185", "EISSN": "1742-7193", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117614305, "Title": "Investigating the Perception of Facial Anonymization Techniques in 360° Videos", "Abstract": "<p>In this work, we investigate facial anonymization techniques in 360° videos and assess their influence on the perceived realism, anonymization effect, and presence of participants. In comparison to traditional footage, 360° videos can convey engaging, immersive experiences that accurately represent the atmosphere of real-world locations. As the entire environment is captured simultaneously, it is necessary to anonymize the faces of bystanders in recordings of public spaces. Since this alters the video content, the perceived realism and immersion could be reduced. To understand these effects, we compare non-anonymized and anonymized 360° videos using blurring, black boxes, and face-swapping shown either on a regular screen or in a head-mounted display (HMD).</p><p>Our results indicate significant differences in the perception of the anonymization techniques. We find that face-swapping is most realistic and least disruptive, however, participants raised concerns regarding the effectiveness of the anonymization. Furthermore, we observe that presence is affected by facial anonymization in HMD condition. Overall, the results underscore the need for facial anonymization techniques that balance both photo-realism and a sense of privacy.</p>", "Keywords": "", "DOI": "10.1145/3695254", "PubYear": 2024, "Volume": "21", "Issue": "4", "JournalId": 12654, "JournalTitle": "ACM Transactions on Applied Perception", "ISSN": "1544-3558", "EISSN": "1544-3965", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The University of Tokyo, JSPS Research Fellow, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo Institute of Technology, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The University of Tokyo, Japan"}], "References": [{"Title": "A Snapshot of Bystander Attitudes about Mobile Live-Streaming Video in Public Settings", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "10", "JournalTitle": "Informatics"}, {"Title": "Effects of dynamic field-of-view restriction on cybersickness and presence in HMD-based virtual reality", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "433", "JournalTitle": "Virtual Reality"}, {"Title": "If and how do 360° videos fit into education settings? Results from a scoping review of empirical research", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "5", "Page": "1199", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Being there to Learn: Narrative Style and Cross-platform Comparison for 360-degree Educational Videos", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Tourgether360: Collaborative Exploration of 360° Videos using Pseudo-Spatial Navigation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Demographic differences in presence across seven studies", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "3", "Page": "2297", "JournalTitle": "Virtual Reality"}, {"Title": "360-degree video for virtual place-based research: A review and research agenda", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "106", "Issue": "", "Page": "102044", "JournalTitle": "Computers, Environment and Urban Systems"}]}, {"ArticleId": 117614392, "Title": "An efficient multi-modal sensors feature fusion approach for handwritten characters recognition using S<PERSON><PERSON>y values and deep autoencoder", "Abstract": "Handwriting is essential for the development of fine motor skills in children. Handwritten character recognition has the potential to facilitate natural human–machine interactions, aiding in the digitization of handwritten text for educational environments such as smart classrooms. Electromyography (EMG), a widely recognized biosignal, captures complex electrical patterns generated by muscle activity during handwriting movements, offering detailed insights into neuromuscular function. This study proposes an efficient multi-modal handwritten character recognition pipeline integrating physiological (EMG) and Inertial Measurement Unit (IMU) sensors. EMG signals provide valuable information about muscle function and activation patterns, while IMU sensors track motion and orientation associated with handwriting. The proposed system employs feature fusion, combining data from both sensor types. A cooperative game theory-based feature ranking method and a modified deep auto-encoder architecture are utilized for enhanced data representation and feature extraction. A novel dataset comprising 26 isolated handwritten English alphabets written on a whiteboard was collected for experimental validation. The proposed pipeline demonstrates high efficiency, achieving a classification accuracy of 99.01% for the isolated handwritten characters. Additional performance metrics, including the Matthews correlation coefficient (98.77) and Kappa Score (98.97), were assessed to validate the model’s effectiveness. The fusion of EMG and IMU data enhances system robustness, offering significant potential for digitizing handwritten notes in smart classrooms and for clinical handwriting analysis, including the diagnosis and monitoring of Alzheimer’s disease", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109225", "PubYear": 2024, "Volume": "138", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology (BHU), Varanasi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology (BHU), Varanasi, India;Corresponding author"}], "References": [{"Title": "Performance evaluation of classifiers for the recognition of offline handwritten Gurmukhi characters and numerals: a study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "2075", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "WiFi Sensing with Channel State Information", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Writer identification system for pre-segmented offline handwritten Devanagari characters using k-NN and SVM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "13", "Page": "10111", "JournalTitle": "Soft Computing"}, {"Title": "FingerDraw", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Remote sensing image captioning via Variational Autoencoder and Reinforcement Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "105920", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "From local explanations to global understanding with explainable AI for trees", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "56", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Acoustic-based sensing and applications: A survey", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "181", "Issue": "", "Page": "107447", "JournalTitle": "Computer Networks"}, {"Title": "The OnHW Dataset", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Recognition of online handwritten Gurmukhi characters using recurrent neural network classifier", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "8", "Page": "6329", "JournalTitle": "Soft Computing"}, {"Title": "DeepNetDevanagari: a deep learning model for Devanagari ancient character recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "13", "Page": "20671", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A review of multimodal human activity recognition with special emphasis on classification, applications, challenges and future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "223", "Issue": "", "Page": "106970", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "UrduDeepNet: offline handwritten Urdu character recognition using deep neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15229", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improved recognition results of offline handwritten Gurumukhi characters using hybrid features and adaptive boosting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "17", "Page": "11589", "JournalTitle": "Soft Computing"}, {"Title": "Multi-sensor information fusion based on machine learning for real applications in human activity recognition: State-of-the-art and research challenges", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "241", "JournalTitle": "Information Fusion"}, {"Title": "Handwriting-Assistant", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Recognition of offline handwritten Urdu characters using RNN and LSTM models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "2", "Page": "2053", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A reliable and efficient machine learning pipeline for american sign language gesture recognition using EMG sensors", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "15", "Page": "23833", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "DMHC: Device-free multi-modal handwritten character recognition system with acoustic signal", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "264", "Issue": "", "Page": "110314", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 117614420, "Title": "Biochemical Approaches for Decoding the Information Stored with Metabolites", "Abstract": "Molecular data storage is an emerging supplement to conventional information storage methods. In addition to the information storage with DNA or synthetic polymers, a recently proposed strategy that uses small molecule mixtures as information carriers has sparked interest since it is a synthesis-free method. Here, we demonstrate two methods for decoding digital information stored in two sets of small-molecule metabolites. The information is stored as binary code, 0 or 1, based on the absence or presence of each particular metabolite in the mixture. In the first approach, the information was encoded by five metabolites (sarcosine, cholesterol, xanthine, glucose, and galactose) and stored as mixture solutions in a 384-well microplate. The information was read out by cascade enzymatic reactions that can specifically convert the metabolites to a colorimetric product, which was then assayed by a microplate reader. We stored a 60×32-pixel picture (1920 bits) with 384 mixtures of small molecules with a reading accuracy of 96.68 %, and also demonstrated the encoding of a 220-bit text message. Alternatively, another set of six metabolites (bilirubin, uric acid, urea, creatinine, glucose, and triglyceride) was deployed to encode a 78×102-pixel picture (7956 bits) with 1326 mixtures, which was decoded automatically by a biochemistry analyzer with a 98.96 % accuracy. This work showcases that common metabolites can be used as digital molecules for information storage, and the information decoding can be done by accessible instruments in an ordinary biochemical laboratory.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136618", "PubYear": 2025, "Volume": "422", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Chemical Resource Engineering, Beijing University of Chemical Technology, Beijing 100029, China;Beijing Advanced Innovation Center for Soft Matter Science and Engineering, Beijing University of Chemical Technology, Beijing 100029, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Chemical Resource Engineering, Beijing University of Chemical Technology, Beijing 100029, China;Beijing Advanced Innovation Center for Soft Matter Science and Engineering, Beijing University of Chemical Technology, Beijing 100029, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Chemical Resource Engineering, Beijing University of Chemical Technology, Beijing 100029, China;Beijing Advanced Innovation Center for Soft Matter Science and Engineering, Beijing University of Chemical Technology, Beijing 100029, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Advanced Innovation Center for Soft Matter Science and Engineering, Beijing University of Chemical Technology, Beijing 100029, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Chemical Resource Engineering, Beijing University of Chemical Technology, Beijing 100029, China;Beijing Advanced Innovation Center for Soft Matter Science and Engineering, Beijing University of Chemical Technology, Beijing 100029, China;Corresponding author at: State Key Laboratory of Chemical Resource Engineering, Beijing University of Chemical Technology, Beijing 100029, China"}], "References": []}, {"ArticleId": 117614463, "Title": "A succinct state-of-the-art survey on green cloud computing: Challenges, strategies, and future directions", "Abstract": "Cloud computing is a method of providing various computing services, including software, hardware, databases, data storage, and infrastructure, to the public through the Internet. The rapid expansion of cloud computing services has raised significant concerns over their environmental impact. Cloud computing services should be designed in a green manner, efficient in energy consumption, virtualized, consolidated, and eco-friendly. Green Cloud Computing (GCC) is a significant field of study that focuses on minimizing the environmental impact and energy usage of cloud infrastructures. This survey provides a comprehensive overview of the current state of GCC, focusing on the challenges, strategies, and future directions. The review study begins by identifying important challenges in GCC from practical implementations, identifying GCC-introduced environmental protection and prevention initiatives, and expressing the demand for long-term technical progression. It then addresses GCC’s primary concerns, such as energy efficiency, resource management, operational costs, and carbon emissions, and categorizes implementations according to algorithms, architectures, frameworks, general issues, and models and methodologies. Furthermore, enhancements in virtualization, multi-tenancy, and consolidation have been identified, analyzed, and accurately portrayed to address the advancements in GCC. Finally, the survey outlines future research directions and opportunities for advancing the field of GCC, including the development of novel algorithms, technologies for energy harvesting, and energy-efficient and eco-friendly solutions. By providing a comprehensive overview of GCC, this survey aims to serve as documentation for further evolving new emerging technological approaches in the GCC environment.", "Keywords": "", "DOI": "10.1016/j.suscom.2024.101036", "PubYear": 2024, "Volume": "44", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Barishal, Karnakathi, Barishal sadar, Barishal, 8200, Bangladesh;Daffodil International University, Daffodil Smart City, Birulia, Savar, Dhaka, 1216, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Barishal, Karnakathi, Barishal sadar, Barishal, 8200, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jagannath University, 9-10 Chitta Ranjan Avenue, Sadarghat, Dhaka, 1100, Bangladesh;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Barishal, Karnakathi, Barishal sadar, Barishal, 8200, Bangladesh"}], "References": [{"Title": "An approach toward design and development of an energy-aware VM selection policy with improved SLA violation in the domain of green cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "9", "Page": "7374", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A novel energy-aware resource management technique using joint VM and container consolidation approach for green computing in cloud data centers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "102127", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Improved chaotic binary grey wolf optimization algorithm for workflow scheduling in green cloud computing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "1997", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Deep learning-based multivariate resource utilization prediction for hotspots and coldspots mitigation in green cloud data centers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "4", "Page": "5806", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A New Edge Computing Architecture for IoT and Multimedia Data Management", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "2", "Page": "89", "JournalTitle": "Information"}, {"Title": "RESCUE: Enabling green healthcare services using integrated IoT‐edge‐fog‐cloud computing environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "7", "Page": "1615", "JournalTitle": "Software: Practice and Experience"}]}, {"ArticleId": 117614561, "Title": "Research on PolSAR Image Classification Method Based on Vision Transformer Considering Local Information", "Abstract": "In response to the problem of inadequate utilization of local information in PolSAR image classification using Vision Transformer in existing studies, this paper proposes a Vision Transformer method considering local information, LIViT. The method replaces image patch sequence with polarimetric feature sequence in the feature embedding, and uses convolution for mapping to preserve image spatial detail information. On the other hand, the addition of the wavelet transform branch enables the network to pay more attention to the shape and edge information of the feature target and improves the extraction of local edge information. The results in Wuhan, China and Flevoland, Netherlands show that considering local information when using Vision Transformer for PolSAR image classification effectively improves the image classification accuracy and shows better advantages in PolSAR image classification.", "Keywords": "Vision Transformer;PolSAR;Image Classification;LIViT", "DOI": "10.4236/jcc.2024.129002", "PubYear": 2024, "Volume": "12", "Issue": "9", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "China Center for Resources Satellite Data and Application, Beijing, China ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Center for Resources Satellite Data and Application, Beijing, China ."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Center for Resources Satellite Data and Application, Beijing, China ."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "China Center for Resources Satellite Data and Application, Beijing, China ."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "China Center for Resources Satellite Data and Application, Beijing, China ."}], "References": []}, {"ArticleId": 117614588, "Title": "FSRM-DDIE :  few-shot learning methods based on relation metrics for the prediction of drug-drug interaction events", "Abstract": "<p>Drug-drug interaction (DDI) prediction aims to predict and evaluate potential interactions between different drugs, assisting healthcare professionals in optimizing drug therapy, enhancing treatment outcomes, and minimizing adverse effects from drug combinations. Traditional research has extensively focused on whether two drugs interact, but predicting the specific events or effects resulting from these interactions may be more effective in understanding the underlying mechanisms of drug combinations. However, data scarcity in drug research significantly hampers the effectiveness of computational models. To address these challenges, we propose FSRM-DDIE, a novel few-shot drug-drug interaction events (DDIE) prediction model. This metric-based meta-learning framework first learns the features of a DDIE using a feature extractor fusing a graph neural network and an auto-encoder Siamese network. Subsequently, a relation metrics module is proposed to capture similar relations between events for classification. By employing meta-learning, our model could perform effectively even with fewer and rare events. Through the comparative experiments, FSRM-DDIE outperforms state-of-the-art methods, demonstrating its potential for accurately predicting DDIE and providing options for understanding drug-drug interactions despite data limitations. In addition, we discuss limitations of the methodology and possible future research trends.</p>", "Keywords": "Drug-drug interaction events; Few-shot learning; Molecular graph; Graph neural network", "DOI": "10.1007/s10489-024-05832-0", "PubYear": 2024, "Volume": "54", "Issue": "23", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao, China"}, {"AuthorId": 2, "Name": "Dongjiang Niu", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, Qingdao, China; Corresponding author."}], "References": [{"Title": "A multimodal deep learning framework for predicting drug–drug interaction events", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "15", "Page": "4316", "JournalTitle": "Bioinformatics"}, {"Title": "A Comprehensive Survey of Few-shot Learning: Evolution, Applications, Challenges, and Opportunities", "Authors": "<PERSON><PERSON><PERSON> Song; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "13s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "GGI-DDI: Identification for key molecular substructures by granule learning to interpret predicted drug–drug interactions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "240", "Issue": "", "Page": "122500", "JournalTitle": "Expert Systems with Applications"}, {"Title": "MSFF-MA-DDI: Multi-Source Feature Fusion with Multiple Attention blocks for predicting Drug–Drug Interaction events", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "108", "Issue": "", "Page": "108001", "JournalTitle": "Computational Biology and Chemistry"}, {"Title": "SRR-DDI: A drug–drug interaction prediction model with substructure refined representation learning based on self-attention mechanism", "Authors": "Dongjiang Niu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "285", "Issue": "", "Page": "111337", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Drug-drug interaction relation extraction based on deep learning: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Model-agnostic generation-enhanced technology for few-shot intrusion detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "4", "Page": "3181", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 117614591, "Title": "Transforming recommender system by integrating attention-based neural network for multimodal sentiment analysis using artificial intelligence", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2024.141373", "PubYear": 2024, "Volume": "74", "Issue": "1/2", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "S.K. <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117614601, "Title": "Prescribed performance actuator-tolerance control for path following of unmanned surface vessels via the triggered adaptive line-of-sight guidance", "Abstract": "The initial conditions beyond the prescribed region and the actuator faults are the crucial issues for the prescribed performance path following control in a low-speed harbor transportation mission. This paper proposes a new prescribed performance path following control strategy for an unmanned surface vessel (USV) by utilization of a shifting function, actuator fault-compensating technique and a triggered adaptive line-of-sight (TALOS) guidance. The main features are described as three points: (i) A TALOS guidance principle is developed on basis of the line-of-sight (LOS) guidance and the triggered strategy, leads to a discrete guidance signal, which is followed by an USV. This can reduce the calculation and transmission frequency of the guidance law. (ii) An exponential-based prescribed performance control (PPC) with consideration of a shifting function is designed to address an explicit limitation where the initial state error may exceed the prescribed boundary. (iii) The actuator faults are compensated by using the neural networks (NNs). According the former three points, the stability of the proposed algorithm is proved through the <PERSON><PERSON><PERSON><PERSON> theorem. Finally, simulation studies are carried out via a harbor navigation mission with usage of automatic identification system (AIS) path information and a comparative example, where the effectiveness and the advantages of the proposed scheme are evaluated in presence of the external disturbances.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2024.109617", "PubYear": 2024, "Volume": "120", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Liu", "Affiliation": "Department of Automation, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Department of Automation, Shanghai Jiao Tong University, Shanghai 200240, China;Corresponding author"}], "References": [{"Title": "Robust adaptive sliding mode control for path tracking of unmanned agricultural vehicles", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "108", "Issue": "", "Page": "108693", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Adaptive backstepping control of primary permanent magnet linear motor via radial basis function neural network and command filter", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "109", "Issue": "", "Page": "108774", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Deep reinforcement learning-based controller for dynamic positioning of an unmanned surface vehicle", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "110", "Issue": "", "Page": "108858", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Research on obstacle avoidance of multi-AUV cluster formation based on virtual structure and artificial potential field method★", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "117", "Issue": "", "Page": "109250", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 117614612, "Title": "Certifying Induced Subgraphs in Large Graphs", "Abstract": "We introduce I/O-efficient certifying algorithms for the recognition of bipartite, split, threshold, bipartite chain, and trivially perfect graphs. When the input graph is a member of the respective class, the certifying algorithm returns a certificate that characterizes this class.Otherwise, it returns a forbidden induced subgraph as a certificate for non-membership.On a graph with $n$ vertices and $m$ edges, our algorithms take $\\mathcal O(\\text{sort}(n + m))$ I/Os in the worst case for split, threshold and trivially perfect graphs.In the same complexity bipartite and bipartite chain graphs can be certified with high probability.We provide implementations and an experimental evaluation for split and threshold graphs.", "Keywords": "I/O-efficient certifying algorithms", "DOI": "10.7155/jgaa.v28i3.2971", "PubYear": 2024, "Volume": "28", "Issue": "3", "JournalId": 16217, "JournalTitle": "Journal of Graph Algorithms and Applications", "ISSN": "", "EISSN": "1526-1719", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Goethe University Frankfurt and Frankfurt Institute for Advanced Studies"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Goethe University Frankfurt and Frankfurt Institute for Advanced Studies"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Liverpool and Athena Research Center"}], "References": []}, {"ArticleId": 117614626, "Title": "Computation of tight bounds for the worst-case end-to-end delay on Avionics Full-Duplex Switched Ethernet", "Abstract": "Avionics Full-Duplex Switched Ethernet (AFDX) is a fault-tolerant real-time communication bus for safety–critical applications in aircraft. AFDX configures communication channels, denoted as virtual links (VLs), ensuring bounded message delays through traffic shaping at both end-systems and switches. Effective AFDX network design necessitates computing the worst-case end-to-end delay of time-critical VLs to meet specified message deadlines. This paper presents a new method for calculating tight bounds on the worst-case end-to-end delay for each VL in an AFDX network. We introduce the new notion of an extended uninterrupted transmission interval, which is the prerequisite for computing the worst-case queuing delay at switches. Adding up these queuing delays along the path of each VL between end-systems yields a tight upper bound on the worst-case end-to-end delay. The correctness of our results is formally proved, and comprehensive simulation experiments on different example networks confirm the tightness of our bound. These simulations also demonstrate the superior performance of our method compared to existing approaches that offer more pessimistic as well as optimistic results.", "Keywords": "", "DOI": "10.1016/j.sysarc.2024.103278", "PubYear": 2024, "Volume": "156", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "Zeynep <PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Middle East Technical University, Ankara, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Middle East Technical University, Ankara, Turkey"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Middle East Technical University, Ankara, Turkey"}], "References": [{"Title": "A time-predictable open-source TTEthernet end-system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "101744", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Worst-case traversal time analysis of TSN with multi-level preemption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "102079", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Constructive or Optimized: An Overview of Strategies to Design Networks for Time-Critical Applications", "Authors": "V<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 117614678, "Title": "Enhancing Education through Blended Learning: A Multifaceted Approach", "Abstract": "", "Keywords": "", "DOI": "10.6025/pca/2024/13/2/91-98", "PubYear": 2024, "Volume": "13", "Issue": "2", "JournalId": 62123, "JournalTitle": "Progress in Computing Applications", "ISSN": "2278-6465", "EISSN": "2278-6473", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> N", "Affiliation": ""}], "References": []}, {"ArticleId": 117614722, "Title": "MAFormer: A cross-channel spatio-temporal feature aggregation method for human action recognition", "Abstract": "<p>Human action recognition has been widely used in fields such as human–computer interaction and virtual reality. Despite significant progress, existing approaches still struggle with effectively integrating hierarchical information and processing data beyond a certain frame count. To address these challenges, we introduce the Multi-AxisFormer (MAFormer) model, which is organized in terms of spatial, temporal, and channel dimensions of the action sequence, thereby enhancing the model’s understanding of correlations and intricate structures among and within features. Drawing on the Transformer architecture, we propose the Cross-channel Spatio-temporal Aggregation (CSA) structure for more refined feature extraction and the Multi-Axis Attention (MAA) module for more comprehensive feature aggregation. Moreover, the integration of Rotary Position Embedding (RoPE) boosts the model’s extrapolation and generalization abilities. MAFormer surpasses the known state-of-the-art on multiple skeleton-based action recognition benchmarks with the accuracy of 93.2% on NTU RGB+D 60 cross-subject split, 89.9% on NTU RGB+D 120 cross-subject split, and 97.2% on N-UCLA, offering a novel paradigm for hierarchical modeling in human action recognition.</p>", "Keywords": "", "DOI": "10.3233/AIC-240260", "PubYear": 2024, "Volume": "37", "Issue": "4", "JournalId": 20835, "JournalTitle": "AI Communications", "ISSN": "0921-7126", "EISSN": "1875-8452", "Authors": [{"AuthorId": 1, "Name": "Hong<PERSON> Huang", "Affiliation": "Computer School, Beijing Information Science & Technology University, Beijing, China;Institute of Computing Intelligence, Beijing Information Science & Technology University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer School, Beijing Information Science & Technology University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer School, Beijing Information Science & Technology University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer School, Beijing Information Science & Technology University, Beijing, China"}], "References": [{"Title": "Graph transformer network with temporal kernel attention for skeleton-based action recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "240", "Issue": "", "Page": "108146", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Swin-Fusion: Swin-Transformer with Feature Fusion for Human Action Recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "8", "Page": "11109", "JournalTitle": "Neural Processing Letters"}, {"Title": "A new framework for deep learning video based Human Action Recognition on the edge", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122220", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 117614796, "Title": "Klasifikasi Hoax Berita Politik Menggunakan Algoritma Long Short-Term Memory (LSTM) dengan Penambahan Fitur Embedding Global Vector (GloVe)", "Abstract": "<p>Berita online telah berkembang sangat pesat terutama pada waktu yang mendekati pesta politik. Tingginya volume dokumen teks dipicu oleh aktivitas dari berbagai sumber berita. Karena banyaknya jumlah berita yang tertampung ke dalam website, terkadang berita yang diposting tidak sesuai dengan kenyataan atau dapat disebut berita hoax. Deteksi berita hoax menjadi penting agar pengguna tidak terjebak informasi palsu yang terkandung dalam berita hoax. Untuk itu diperlukan sebuah sistem cerdas yang dapat mendeteksi berita hoax bertema politik secara otomatis. Penelitian ini mengevaluasi Teknik deep learning menggunakan LSTM dengan penambahan fitur Word Embedding Global Vector (GloVe) dan membandingkan hasil dari penelitian sebelumnya yang menggunakan beberapa algoritma deep learning tanpa penambahan fitur. Peneliti menggunakan dataset yang terdiri dari 1300 dokumen berita hoax dan 8234 dokumen berita valid. Karena jumlah komposisi antara kelas hoax dan valid tidak seimbang, maka peneliti menerapkan teknik Synthetic Minority Over-Sampling Technique (SMOTE) untuk meningkatkan representasi kelas minoritas. Hasil empiris menunjukan bahwa akurasi klasifikasi dari LSTM dengan fitur tambahan word embedding GloVe didapatkan akurasi sebesar 99.8%, dimana mampu mengungguli hasil penelitian sebelumnya dengan akurasi 95.1% yang tanpa penggunaan word embedding. Peningkatan performa ini tidak lepas dari penambahan fitur word embedding GloVe sebagai inisialisasi bobot untuk lapisan embedding dalam model LSTM, dimana mampu memberikan representasi yang lebih baik pada kata-kata dalam dataset.</p>", "Keywords": "Long Short-Term Memmory (LSTM);Klasifikasi;Word Embedding;Global Vector (GloVe);Politik;Berita;Indonesia;SMOTE", "DOI": "10.26418/jp.v10i2.76042", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Erliawan K.", "Affiliation": "Universitas Muhammadiyah <PERSON>"}, {"AuthorId": 3, "Name": "Christian Sri <PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>"}], "References": []}, {"ArticleId": 117614801, "Title": "Profit Comparison of Computer System with Hardware Redundancy Subject to Different Repair Activities", "Abstract": "<p>In this research paper, main concentrate of the authors on the profit comparison of computer system with hardware redundancy by introducing the concept of priority to software up-gradation, hardware preventive maintenance (PM) and hardware maximum repair time (MRT). The system fails independently from normal mode. All the repair activities such as hardware repair, software up-gradation, hardware preventive maintenance before failure and hardware replacement after maximum repair time are carried out by a single server immediately on need basis. All random variables are statistically independent. The negative exponential distribution is taken for the failure time of the component while the distributions of repair time, up-gradation time, preventive maintenance and replacement time are assumed arbitrary with different probability density functions. Semi-Markov process and regenerative point technique are used. The behaviour of profits of the system models have been examined for different parameters and costs.</p>", "Keywords": "", "DOI": "10.18535/ijecs/v13i08.4872", "PubYear": 2024, "Volume": "13", "Issue": "8", "JournalId": 17925, "JournalTitle": "International Journal Of Engineering And Computer Science", "ISSN": "", "EISSN": "2319-7242", "Authors": [{"AuthorId": 1, "Name": "VIKRAM MUNDAY", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117614823, "Title": "Peramalan Harga Emas Menggunakan Pendekatan Long-Short Term Memory (LSTM)", "Abstract": "<p>Inventasi menjadi salah satu pilihan masyarakat untuk mengelola kelebihan dana agar nilainya meningkat dikemudian hari. Emas menjadi salah satu komoditi yang sering dijadikan instrumen investasi favorit. Harga emas yang fluktuatif menimbulkan efek kerugian bagi investor. Peramalan harga emas dimasa yang akan datang menjadi penting untuk meminimalisir resiko kerugian. Pendekatan machine learning lebih baik dibandingkan inferensial seperti Autoregressive Integrated Moving Average (ARIMA) dalam meramalkan data yang fluktuatif. Metode dengan pendekatan machine learning seperti Long-Short Term Memory (LSTM) memiliki performasi yang baik pada data yang fluktuatif. Metode LSTM digunakan untuk untuk meramalkan harga emas. Penelitian ini membagi data training dan data testing sebesar 80% dan 20%. Metode evaluasi model Mean Absolute Percentage Error (MAPE) digunakan untuk melihat kebaikan model. <PERSON><PERSON>tian ini menerapkan enam scenario tunning parameter. Parameter terbaik metode LSTM yaitu learning rate 0,01, neuron 10, dan Epoch 100 dengan nilai MAPE sebesar 3,499%. Hasil MAPE pada data training dan data testing tidak menunjukan terjadinya overfitting atau underfitting pada metode LSTM terbaik. Hasil peramalan tiga puluh periode cenderung fluktuatif, terjadi kenaikan yang signifikan pada periode ke dua puluh empat ke dua puluh lima.</p>", "Keywords": "<PERSON><PERSON>;<PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON> Term Memory;<PERSON><PERSON><PERSON>", "DOI": "10.26418/jp.v10i2.78332", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>ng"}, {"AuthorId": 2, "Name": "Syifa Aulia", "Affiliation": "Universitas Muhammadiyah <PERSON>ng"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>ng"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah <PERSON>ng"}], "References": []}, {"ArticleId": 117614840, "Title": "Human–robot collaborative handling of curtain walls using dynamic motion primitives and real-time human intention recognition", "Abstract": "Human–robot collaboration fully leverages the strengths of both humans and robots, which is crucial for handling large, heavy objects at construction sites. To address the challenges of human–machine cooperation in handling large-scale, heavy objects — specifically building curtain walls — a human–robot collaboration system was designed based on the concept of “human–centered with machine support”. This system allows the handling of curtain walls according to different human intentions. First, a robot trajectory learning and generalization model based on dynamic motion primitives was developed. The operator’s motion intent was then characterized by their speed, force, and torque, with the force impulse introduced to define the operator’s intentions for acceleration and deceleration. Finally, a collaborative experiment was conducted on an experimental platform to validate the robot’s understanding of human handling intentions and to verify its ability to handle curtain wall. Collaboration between humans and robots ensured a smooth and labor-saving handling process.", "Keywords": "Robots; Dynamic motion primitives; Human–machine collaboration; Curtain wall handling", "DOI": "10.1016/j.birob.2024.100183", "PubYear": 2024, "Volume": "4", "Issue": "4", "JournalId": 85921, "JournalTitle": "Biomimetic Intelligence and Robotics", "ISSN": "2667-3797", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Electrical Engineering, Shandong Jianzhu University, Jinan 250101, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Electrical Engineering, Shandong Jianzhu University, Jinan 250101, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong Transportation Institute, Jinan 250031, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan 250012, China"}], "References": [{"Title": "Human-object integrated assembly intention recognition for context-aware human-robot collaborative assembly", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101792", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Robot teaching system based on hand-robot contact state detection and motion intention recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "81", "Issue": "", "Page": "102492", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Model predictive optimization for imitation learning from demonstrations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "163", "Issue": "", "Page": "104381", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Robot skill learning system of multi-space fusion based on dynamic movement primitives and adaptive neural network control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "574", "Issue": "", "Page": "127248", "JournalTitle": "Neurocomputing"}, {"Title": "Learning and generalization of task-parameterized skills through few human demonstrations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "108310", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 117614890, "Title": "StreamTrack: real-time meta-detector for streaming perception in full-speed domain driving scenarios", "Abstract": "<p>Streaming perception is a crucial task in the field of autonomous driving, which aims to eliminate the inconsistency between the perception results and the real environment due to the delay. In high-speed driving scenarios, the inconsistency becomes larger. Previous research has ignored the study of streaming perception in high-speed driving scenarios and the robustness of the model to object’s speed. To fill this gap, we first define the full-speed domain streaming perception problem and construct a real-time meta-detector, StreamTrack. Second, to perform motion trend extraction, Swift Multi-Cost Tracker (SMCT) is proposed for fast and accurate data association. Meanwhile, the Direct-Decoupled Prediction Head (DDPH) is introduced for predicting future locations. Furthermore, we introduce the Uniform Motion Prior Loss (UMPL), which ensures stable learning of the model for rapidly moving objects. Compared with the strong baseline, our model improves the SAsAP (Speed-Adaptive steaming Average Precision) by 15.46 %. Extensive experiments show that our approach achieves state-of-the-art performance in the full-speed domain streaming perception task.</p>", "Keywords": "Streaming perception; Full-speed domain driving scenarios; Meta-detector; Perception delay", "DOI": "10.1007/s10489-024-05748-9", "PubYear": 2024, "Volume": "54", "Issue": "23", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Weizhen Ge", "Affiliation": "Unmanned System Research Institute, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Unmanned System Research Institute, Northwestern Polytechnical University, Xi’an, China; Shaanxi Transportation Holding Group, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Unmanned System Research Institute, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Singapore University of Social Sciences, Clementi, Singapore"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Unmanned System Research Institute, Northwestern Polytechnical University, Xi’an, China; Corresponding author."}], "References": [{"Title": "TTFNeXt for real-time object detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "433", "Issue": "", "Page": "59", "JournalTitle": "Neurocomputing"}, {"Title": "Transformer-based two-source motion model for multi-object tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "9967", "JournalTitle": "Applied Intelligence"}, {"Title": "SSRFD: single shot real-time face detector", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "10", "Page": "11916", "JournalTitle": "Applied Intelligence"}, {"Title": "Recursive least squares method for training and pruning convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "20", "Page": "24603", "JournalTitle": "Applied Intelligence"}, {"Title": "Knowledge distillation for object detection based on Inconsistency-based Feature Imitation and Global Relation Imitation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "566", "Issue": "", "Page": "127060", "JournalTitle": "Neurocomputing"}, {"Title": "KD-SCFNet: Towards more accurate and lightweight salient object detection via knowledge distillation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "572", "Issue": "", "Page": "127206", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 117614971, "Title": "Specular Surface Detection with Deep Static Specular Flow and Highlight", "Abstract": "<p>To apply robot teaching to a factory with many mirror-polished parts, it is necessary to detect the specular surface accurately. Deep models for mirror detection have been studied by designing mirror-specific features, e.g., contextual contrast and similarity. However, mirror-polished parts such as plastic molds, tend to have complex shapes and ambiguous boundaries, and thus, existing mirror-specific deep features could not work well. To overcome the problem, we propose introducing attention maps based on the concept of static specular flow (SSF), condensed reflections of the surrounding scene, and specular highlight (SH), bright light spots, frequently appearing even in complex-shaped specular surfaces and applying them to deep model-based multi-level features. Then, we adaptively integrate approximated mirror maps generated by multi-level SSF, SH, and existing mirror detectors to detect complex specular surfaces. Through experiments with our original data sets with spherical mirrors and real-world plastic molds, we show the effectiveness of the proposed method.</p>", "Keywords": "Specular surface; Deep model; Static specular flow; Highlight", "DOI": "10.1007/s00138-024-01603-6", "PubYear": 2024, "Volume": "35", "Issue": "6", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Systems Engineering, Wakayama University, Wakayama-city, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Systems Engineering, Wakayama University, Wakayama-city, Japan"}], "References": []}, {"ArticleId": *********, "Title": "Taking into Account Opponent’s Arguments in Human-Agent Negotiations", "Abstract": "<p>Autonomous negotiating agents, which can interact with other agents, aim to solve decision-making problems involving participants with conflicting interests. Designing agents capable of negotiating with human partners requires considering some factors, such as emotional states and arguments. For this purpose, we introduce an extended taxonomy of argument types capturing human speech acts during the negotiation. We propose an argument-based automated negotiating agent that can extract human arguments from a chat-based environment using a hierarchical classifier. Consequently, the proposed agent can understand the received arguments and adapt its strategy accordingly while negotiating with its human counterparts. We initially conducted human-agent negotiation experiments to construct a negotiation corpus to train our classifier. According to the experimental results, it is seen that the proposed hierarchical classifier successfully extracted the arguments from the given text. Moreover, we conducted a second experiment where we tested the performance of the designed negotiation strategy considering the human opponent’s arguments and emotions. Our results showed that the proposed agent beats the human negotiator and gains higher utility than the baseline agent.</p>", "Keywords": "", "DOI": "10.1145/3691643", "PubYear": 2025, "Volume": "15", "Issue": "1", "JournalId": 13925, "JournalTitle": "ACM Transactions on Interactive Intelligent Systems", "ISSN": "2160-6455", "EISSN": "2160-6463", "Authors": [{"AuthorId": 1, "Name": "Anıl <PERSON>ğ<PERSON>", "Affiliation": "Özyeğin University, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Özyeğin University, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Özyeğin University, Turkey and Delft University of Technology, The Netherlands"}], "References": [{"Title": "Argumentative Conversational Agents for Online Discussions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "450", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "Search Interface Design and Evaluation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "3-4", "Page": "243", "JournalTitle": "Foundations and Trends® in Information Retrieval"}, {"Title": "The Effect of Appearance of Virtual Agents in Human-Agent Negotiation", "Authors": "<PERSON><PERSON><PERSON>; Cana <PERSON>den; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "683", "JournalTitle": "AI"}]}, {"ArticleId": 117615217, "Title": "Quantitative assessment of the visual quality of digital images based on the laws of human visual perception", "Abstract": "The existing methods of quantitative assessment of the visual quality of digital images are studied. Among the main shortcomings of the studied methods, the following can be singled out. Most of them require a reference image, do not include all the components that affect visual quality and do not take into consideration the laws of human visual perception. It was decided to develop a method for quantitative assessment of the visual quality of images, which will work without a reference image and will take into account the regularities of human visual perception. There are characterized the main regularities of human visual perception, which are used in the development of the technique. A classification of the researched methods of quantitative assessment of image quality is proposed for structuring their analysis. It was decided to investigate methods of quantitative assessment of quality based on statistical analysis of image pixel intensities. There are described factors affecting the quality of images and methods of their control based on changes in the pixel intensity distribution histogram. A generalized expression of quantitative quality assessment based on moments is proposed. A methodology for quantitative assessment of the visual quality of images has been developed, which does not require a reference image and is based on the laws of human visual perception. This method was tested on an image processed by local contrast enhancement and low-pass filtering. The test results showed that the visual perception of image quality coincides with the quantitative assessment of its quality. It is possible to use the proposed method with some modifications to determine the quality of color images. Moreover, a potential avenue for advancing the proposed method involves adapting it for evaluating images afflicted by distortions induced by noise presence.", "Keywords": "", "DOI": "10.23939/ujit2024.01.017", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 86518, "JournalTitle": "Ukrainian Journal of Information Technology", "ISSN": "2707-1898", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "K. A. Ambroziak", "Affiliation": ""}], "References": []}, {"ArticleId": 117615246, "Title": "Open Problems Column", "Abstract": "<p>This issue's Open Problems column we revisit three prior columns for which progress was made on the open problems posed.</p>", "Keywords": "", "DOI": "10.1145/3695887.3695891", "PubYear": 2024, "Volume": "55", "Issue": "3", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Univ of MD at College Park, College Park, MD, USA"}], "References": []}, {"ArticleId": 117615262, "Title": "Integration of cloud technologies into virtual reality", "Abstract": "Investigated the possibilities of integrating cloud technologies into virtual reality in order to further improve software tools and increase its efficiency and accessibility. Analysed architectural and infrastructure solutions for the use of cloud services, processing and storage of large data in virtual environments. Conducted a comprehensive scientific study of the possibilities of integrating cloud technologies into virtual reality in order to increase the efficiency and accessibility of this technological segment. Studied the challenges that arise when integrating cloud technologies, in particular, the problems of delays in data transmission and measures to ensure security and privacy in cloud environments. Considered the necessity of developing a cloud infrastructure in popular cloud services, in particular, Amazon Web Services (AWS), to optimise computing tasks and increase overall efficiency, as well as the possibility of scaling virtual reality technology. Article considers the possibility of creating a solution for interacting with cloud resources and using virtual reality applications. The services of a cloud provider are used to allocate computing resources, create a data warehouse, create and manage a virtual network, install and configure solutions for deploying and configuring server software components, and conduct an experiment. Evaluated the impact of cloud technology integration on the quality and availability of virtual reality, and provide prospects for the development of this association. In addition, considered the possibilities of creating optimised solutions for interacting with cloud resources and using virtual reality applications, and assessed the impact of cloud integration on the quality and availability of virtual reality. Developed a software solution and approach for building a cloud infrastructure, described in detail all the steps of creating and deploying a virtual server and data storage for its interaction with virtual reality systems. Prospects for the development of such an association open up wide horizons for innovative technologies in the information technology segment. The results of the study can become an important source of relevant strategic information for further improvement of cloud-virtual environments and development of innovative technologies in the information technology segment.", "Keywords": "", "DOI": "10.23939/ujit2024.01.109", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 86518, "JournalTitle": "Ukrainian Journal of Information Technology", "ISSN": "2707-1898", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>.<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Development of a virtual reality simulator for a strategy for coordinating cooperative manipulator robots using cloud computing", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "126", "Issue": "", "Page": "103447", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "vSocial: a cloud-based system for social virtual reality learning environment applications in special education", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "16827", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 117615387, "Title": "Analyzing deep reinforcement learning model decisions with <PERSON><PERSON><PERSON><PERSON> additive explanations for counter drone operations", "Abstract": "As the use of drones continues to increase, their capabilities pose a threat to airspace safety when they are misused. Deploying AI models for intercepting these unwanted drones becomes crucial. However, these AI models, such as deep learning models, often operate as “black boxes”, making it hard to trust their decision-making system. This also affects end-users’ confidence in these AI systems. In this paper, the explainability of deep reinforcement learning is investigated and a deep reinforcement learning (DRL) method, double deep Q-network with dueling architecture and prioritized experience replay is applied to train the AI models. To make the AI model decisions more transparent and to understand the reasoning behind the AI decisions for counter-drone systems, Shapley Additive Explanations (SHAP) method is implemented. After training the DRL agent, experience replay is visualized, and the absolute SHAP values are calculated to explain the key factors that influence the deep reinforcement learning agent’s choices. The integration of DRL with explainable AI methods such as SHAP demonstrates significant potential for the advancement of robust and efficient counter-drone systems.", "Keywords": "Explainable AI; SHAP; Dueling network; Prioritised experience replay; C-UAS; UAV; Drones; Airsim", "DOI": "10.1007/s10489-024-05733-2", "PubYear": 2024, "Volume": "54", "Issue": "23", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Telecommunication and Systems Engineering at the Universitat Autònoma de Barcelona (UAB Barcelona) - Intelligent Transportation Systems Research Group, Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Architecture Department, UPC BarcelonaTECH, Castelldefels, Spain; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Architecture Department, UPC BarcelonaTECH, Castelldefels, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Architecture Department, UPC BarcelonaTECH, Castelldefels, Spain"}], "References": [{"Title": "Explainability in deep reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106685", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Aerial filming with synchronized drones using reinforcement learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "12", "Page": "18125", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Explainable AI and Reinforcement Learning—A Systematic Review of Current Approaches and Trends", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "48", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Explainable AI methods on a deep reinforcement learning agent for automatic docking", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "16", "Page": "146", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Explaining Deep Q-Learning Experience Replay with SHapley Additive exPlanations", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "4", "Page": "1433", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "Explainability in Deep Reinforcement Learning: A Review into Current Methods and Applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 117615394, "Title": "Power Quality Enhancement in High-Voltage Transmission Systems Using STATCOM by Type 1 and Type 2 Fuzzy Logic Controllers", "Abstract": "<p>The continuing growth of load demand leads to increased power system complexity and interconnections. During faults and abnormal operating conditions, conventional power systems change the load flow in transmission lines, which may cause voltage drops or swells, and other power quality issues. Voltage regulation is connected to reactive power compensation in system busbars, and fixed capacitor banks are usually used for this purpose, but they are not flexible and dynamic enough to meet the power quality requirements. With power electronics development, flexible alternative current transmission systems (FACTS) became a popular solution for power quality improvement and power transmission capability increment in power systems, and FACTS can be categorized into series and shunt systems. In this study, STATCOM was used to improve the voltage profile in part of Iraq’s high-voltage transmission system during abnormal operating conditions, such as sudden load addition and removal, which causes voltage dip and voltage swell. Type 1 fuzzy logic controller and type 2 fuzzy logic controller were used as the outer controller in the STATCOM; both controllers were able to regulate the voltage magnitude by reducing the voltage dip from 1.32 to 0.4%, voltage swell from 1.02 to 0.5%, and voltage THD at the connecting point did not exceed 3%, indicating that SATACOM with FLC improves the voltage profile under different operating conditions.</p>", "Keywords": "STATCOM; Fuzz logic type 1; Fuzzy logic type 2; Voltage regulation; Power quality", "DOI": "10.1007/s44196-024-00636-z", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "Faissl G. <PERSON>", "Affiliation": "faissl-g-; Department of Electrical Engineering, National School of Engineers of Sfax (ENIS), University of Sfax, Sfax, Tunisia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National School of Engineers of Sfax (ENIS), University of Sfax, Sfax, Tunisia"}], "References": [{"Title": "Fuzzy-based metaheuristic algorithm for optimization of fuzzy controller: fault-tolerant control application", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "599", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}, {"Title": "A metaheuristic approach for interval type-2 fuzzy fractional order fault-tolerant controller for a class of uncertain nonlinear system*", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "4", "Page": "656", "JournalTitle": "Automatika"}, {"Title": "Comparative Analysis Between Two Fuzzy Variants of Harmonic Search Algorithm: Fuzzy Fault Tolerant Control Application", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "507", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Simulation and Comparison Between Fuzzy Harmonic Search and Differential Evolution Algorithm: Type-2 Fuzzy Approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "16", "Page": "412", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 117615437, "Title": "Technical Report Column", "Abstract": "<p>Welcome to the Technical Reports Column. If your institution publishes technical reports that you'd like to have included here, please contact me at the email address above.</p>", "Keywords": "", "DOI": "10.1145/3695887.3695890", "PubYear": 2024, "Volume": "55", "Issue": "3", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Minnesota State University, Mankato, MN, USA"}], "References": []}, {"ArticleId": 117615440, "Title": "Interpretable Spatial–Temporal Graph Convolutional Network for System Log Anomaly Detection", "Abstract": "To ensure seamless information flow and operational integrity, computer systems need effectively to manage their system logs, but the expansion in their scale and complexity makes it hard to detect anomalies. Current methodologies exhibit deficiencies, including inefficiencies in handling abnormal sequences, lack of interpretability, and limited consideration of both temporal and spatial information. To improve, this paper develops a semi-supervised graph neural network model termed the Interpretable Spatial–Temporal Graph Convolutional Network (IST-GCN). By integrating temporal and event similarity perspectives, the IST-GCN harnesses directed and undirected graphs to capture the temporal and spatial aspects of system log events. Hence, the IST-GCN offers temporal and spatial interpretability. Further, a lightweight feature regularization technique is developed to enhance interpretability in both time and space domains, and thus facilitates anomaly detection efficiently. Comprehensive testing verifies that the IST-GCN approach surpasses nearly all state-of-the-art methods across five public log anomaly datasets. On average, IST-GCN improves Average Precision (AP) by approximately 3% and ROC AUC (RC) by about 4% compared to the best-performing baseline methods, underscoring its effectiveness and robustness.", "Keywords": "", "DOI": "10.1016/j.aei.2024.102803", "PubYear": 2024, "Volume": "62", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Institute for Advanced Study, University of Electronic Science and Technology of China, Shenzhen 518110, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shenzhen Institute for Advanced Study, University of Electronic Science and Technology of China, Shenzhen 518110, China;i4AI Ltd, London WCIN 3AX, United Kingdom;Corresponding author at: Shenzhen Institute for Advanced Study, University of Electronic Science and Technology of China, Shenzhen 518110, China"}], "References": [{"Title": "Autoencoder-based anomaly detection for surface defect inspection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101272", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Graph neural networks in node classification: survey and evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "LogUAD: Log Unsupervised Anomaly Detection Based on Word2Vec", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "3", "Page": "1207", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "AutoLog: Anomaly detection by deep autoencoding of system logs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116263", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Anomaly detection for construction vibration signals using unsupervised deep learning and cloud computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101907", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "LAnoBERT: System log anomaly detection based on BERT masked language model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "146", "Issue": "", "Page": "110689", "JournalTitle": "Applied Soft Computing"}, {"Title": "Controlled graph neural networks with denoising diffusion for anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121533", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dynamic link prediction by learning the representation of node-pair via graph neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "122685", "JournalTitle": "Expert Systems with Applications"}, {"Title": "From anomaly detection to classification with graph attention and transformer for multivariate time series", "Authors": "<PERSON><PERSON> Wang; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102357", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Spatio-temporal correlation-based multiple regression for anomaly detection and recovery of unmanned aerial vehicle flight data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102440", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 117615450, "Title": "Localizing AIED: moving beyond North–South narratives to serve contextual needs", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-02047-2", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Towards a new generation of artificial intelligence in China", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "6", "Page": "312", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "A panoramic view and swot analysis of artificial intelligence for achieving the sustainable development goals by 2030: progress and prospects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "9", "Page": "6497", "JournalTitle": "Applied Intelligence"}, {"Title": "Artificial Intelligence (AI) in early childhood education: Curriculum design and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100072", "JournalTitle": "Computers and Education: Artificial Intelligence"}]}, {"ArticleId": 117615538, "Title": "A New Framework for Enhancing VANETs through Layer 2 DLT Architectures with Multiparty Threshold Key Management and PETs", "Abstract": "<p>This work proposes a new architectural approach to enhance the security, privacy, and scalability of VANETs through threshold key management and Privacy Enhancing Technologies (PETs), such as homomorphic encryption and secure multiparty computation, integrated with Decentralized Ledger Technologies (DLTs). These advanced mechanisms are employed to eliminate centralization and protect the privacy of transferred and processed information in VANETs, thereby addressing privacy concerns. We begin by discussing the weaknesses of existing VANET architectures concerning trust, privacy, and scalability and then introduce a new architectural framework that shifts from centralized to decentralized approaches. This transition applies a decentralized ledger mechanism to ensure correctness, reliability, accuracy, and security against various known attacks. The use of Layer 2 DLTs in our framework enhances key management, trust distribution, and data privacy, offering cost and speed advantages over Layer 1 DLTs, thereby enabling secure vehicle-to-everything (V2X) communication. The proposed framework is superior to other frameworks as it improves decentralized trust management, adopts more efficient PETs, and leverages Layer 2 DLT for scalability. The integration of multiparty threshold key management and homomorphic encryption also enhances data confidentiality and integrity, thus securing against various existing cryptographic attacks. Finally, we discuss potential future developments to improve the security and reliability of VANETs in the next generation of networks, including 5G networks.</p>", "Keywords": "", "DOI": "10.3390/fi16090328", "PubYear": 2024, "Volume": "16", "Issue": "9", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Foundation Studies, Gulf College, Muscat 133, Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Informatics, De Montfort University, The Gateway, Leicester LE1 9BH, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Batman Üniversitesi Batı Raman Kampüsü, Batman 72000, Turkey"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Informatics, De Montfort University, The Gateway, Leicester LE1 9BH, UK"}, {"AuthorId": 5, "Name": "Hilal M. Y. Al-Bayatti", "Affiliation": "Academic and Development Department, Applied Science University, Road No 3201, Al Eker 623, Bahrain"}], "References": [{"Title": "An exploratory study of smart contracts in the Ethereum blockchain platform", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "3", "Page": "1864", "JournalTitle": "Empirical Software Engineering"}, {"Title": "B-TSCA: Blockchain Assisted Trustworthiness Scalable Computation for V2I Authentication in VANETs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1386", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Secure multiparty computation", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "1", "Page": "86", "JournalTitle": "Communications of the ACM"}, {"Title": "A comprehensive survey on authentication and privacy-preserving schemes in VANETs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "41", "Issue": "", "Page": "100411", "JournalTitle": "Computer Science Review"}, {"Title": "Distributed ledger technologies in vehicular mobile edge computing: a survey", "Authors": "Ming Jiang; Xingsheng Qin", "PubYear": 2022, "Volume": "8", "Issue": "5", "Page": "4403", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "User-empowered Privacy-preserving Authentication Protocol for Electric Vehicle Charging Based on Decentralized Identity and Verifiable Credential", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Biplab Sikdar", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Management Information Systems"}, {"Title": "A survey of Layer-two blockchain protocols", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "209", "Issue": "", "Page": "103539", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A transparent distributed ledger-based certificate revocation scheme for VANETs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "103569", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 117615596, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1077-3142(24)00252-2", "PubYear": 2024, "Volume": "248", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [], "References": []}, {"ArticleId": 117615663, "Title": "Code and Data Repository for Deep Stacking Kernel Machines for the Data-Driven Multi-Item, One-Warehouse, Multiretailer Problems with Backlog and Lost Sales", "Abstract": "", "Keywords": "data sciences; inventory control; deep learning; support vector machine; one-warehouse multiretailer; censored demands", "DOI": "10.1287/ijoc.2022.0365.cd", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117615690, "Title": "Implementasi Rekomendasi Content Based Filtering dan Apriori Berbasis Android", "Abstract": "<p>Proban Motoparts merupakan sebuah toko retail dan jasa yang bergerak dibidang otomotif dengan menjual beberapa produk suku cadang untuk sepeda motor. Banyaknya variasi produk yang ada di Proban Motoparts, membuat pelanggan merasa kesulitan saat memilih produk yang dibutuhkan. Solusi dari kendala tersebut adalah dengan implementasi sistem rekomendasi produk yang dapat memudahkan pelanggan mendapatkan produk yang mereka butuhkan. Sistem rekomendasi ini menggunakan data histori penjualan manual di salah satu toko Proban Motoparts, dengan total jumlah data transaksi sebanyak 5 transaksi. Implementasi sistem rekomendasi content-based filtering menggunakan algoritma apriori ini untuk menghasilkan produk dengan nilai support tertinggi yang akan direkomendasikan kepada pelanggan. Aplikasi rekomendasi ini memudahkan pelanggan dalam memesan berbagai kebutuhan suku cadang motor yang mereka butuhkan cukup menggunakan perangkat android, hasil persentase pilihan pelanggan diketahui hingga 50.79% memilih sangat setuju dan 35.19% memilih setujuberdasarkan hasil kuesioner dengan 42 responden yang bersedia, dan sebanyak 87.09% merasa puas dengan sistem rekomendasi produk ini.</p>", "Keywords": "Content Based Filtering;Apriori;Android;Sistem Rekomendasi;Ko<PERSON>in", "DOI": "10.26418/jp.v10i2.74383", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Mercu Buana"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Mercu Buana"}], "References": []}, {"ArticleId": 117615707, "Title": "Learning 3D human–object interaction graphs from transferable context knowledge for construction monitoring", "Abstract": "We propose a novel framework for detecting 3D human–object interactions (HOI) in construction sites and a toolkit for generating construction-related human–object interaction graphs. Computer vision methods have been adopted for construction site safety surveillance in recent years. The current computer vision methods rely on videos and images, with which safety verification is performed on common-sense knowledge, without considering 3D spatial relationships among the detected instances. We propose a new method to incorporate spatial understanding by directly inferring the interactions from 3D point cloud data. The proposed model is trained on a 3D construction site dataset generated from our crafted simulation toolkit. The model achieves 54.11% mean interaction over union (mIOU) and 72.98% average mean precision(mAP) for the worker–object interaction relationship recognition. The model is also validated on PiGraphs, a benchmarking dataset with 3D human–object interaction types, and compared against other existing 3D interaction detection frameworks. It was observed that it achieves superior performance from the state-of-the-art model, increasing the interaction detection mAP by 17.01%. Besides the 3D interaction model, we also simulate interactions from industrial surveillance footage using MoCap and physical constraints, which will be released to foster future studies in the domain.", "Keywords": "3D human–object interaction; Scene-understanding; Industrial surveillance; Graph reasoning; Simulation data; Animation", "DOI": "10.1016/j.compind.2024.104171", "PubYear": 2025, "Volume": "164", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, 5000 Forbes Abe, Pittsburgh, PA 15213, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, 5000 Forbes Abe, Pittsburgh, PA 15213, United States of America"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, 5000 Forbes Abe, Pittsburgh, PA 15213, United States of America"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, 5000 Forbes Abe, Pittsburgh, PA 15213, United States of America"}, {"AuthorId": 5, "Name": "Tomotake Furuhata", "Affiliation": "Carnegie Mellon University, 5000 Forbes Abe, Pittsburgh, PA 15213, United States of America"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, 5000 Forbes Abe, Pittsburgh, PA 15213, United States of America"}], "References": [{"Title": "Exploiting multimodal synthetic data for egocentric human-object interaction detection in an industrial scenario", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "242", "Issue": "", "Page": "103984", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 117615715, "Title": "Automatic Wound Image Segmentation with U-Net Model for Smartphone Application", "Abstract": "<p>Pengenalan citra luka memiliki potensi yang sangat penting dalam analisis luka, termasuk klasifikasi jenis luka, identifikasi infeksi, estima<PERSON> penyembuhan, dan penentuan perawatan yang tepat. Salah satu perkembangan terbaru dalam bidang ini adalah segmentasi otomatis pada citra, yang memanfaatkan kemajuan dalam deep learning untuk melakukan ekstraksi citra luka dengan menghilangkan piksel yang tidak relevan dengan luka. <PERSON><PERSON> penelitian ini, kami melakukan evaluasi terhadap kinerja arsitektur U-Net dasar dan membandingkannya dengan tiga model pre-trained yang terkenal, termasuk MobileNet, MobileNetV2, EfficientNetB0, dan NasNet mobile sebagai backbone untuk meningkatkan kualitas segmentasi citra luka</p>", "Keywords": "U-Net; pretrained model; automatic segmentation; MobileNet2; EfficientNetB0", "DOI": "10.26418/jp.v10i2.78548", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 52292, "JournalTitle": "Jurnal Edukasi dan <PERSON> (JEPIN)", "ISSN": "2460-0741", "EISSN": "2548-9364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Bina Sarana Informatika"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas Bina Sarana Informatika"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitas Bina Sarana Informatika"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Bina Sarana Informatika"}], "References": []}, {"ArticleId": 117615910, "Title": "Governing with Intelligence: The Impact of Artificial Intelligence on Policy Development", "Abstract": "<p>As the field of artificial intelligence (AI) continues to evolve, its potential applications in various domains, including public policy development, have garnered significant interest. This research aims to investigate the role of AI in shaping public policies through a qualitative examination of secondary data and an extensive bibliographic review. By analyzing the existing literature, government reports, and relevant case studies, this study seeks to uncover the opportunities, challenges, and ethical considerations associated with leveraging AI in the formulation and implementation of public policies. This research will delve into the potential benefits of AI-driven policy analysis, such as enhanced decision-making processes, data-driven insights, and improved policy outcomes. Additionally, it will explore the risks and concerns surrounding AI’s influence on policy, including potential biases, privacy implications, and the need for transparency and accountability. The findings of this study will contribute to the ongoing discourse on the responsible and effective integration of AI in public policy development, fostering informed decision-making and promoting the ethical use of this transformative technology.</p>", "Keywords": "", "DOI": "10.3390/info15090556", "PubYear": 2024, "Volume": "15", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Policy Studies, Universiti Brunei Darussalam, Bandar Seri Begawan BE1410, Brunei"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Policy Studies, Universiti Brunei Darussalam, Bandar Seri Begawan BE1410, Brunei"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Policy Studies, Universiti Brunei Darussalam, Bandar Seri Begawan BE1410, Brunei"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Artificial Intelligence and Data Science, Sejong University, Seoul 05006, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Artificial Intelligence and Data Science, Sejong University, Seoul 05006, Republic of Korea"}], "References": [{"Title": "Artificial intelligence, transparency, and public decision-making", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "4", "Page": "917", "JournalTitle": "AI & SOCIETY"}, {"Title": "The AI gambit: leveraging artificial intelligence to combat climate change—opportunities, challenges, and recommendations", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "1", "Page": "283", "JournalTitle": "AI & SOCIETY"}]}, {"ArticleId": 117615920, "Title": "Deep learning-based GoogLeNet-embedded no-pooling dimension fully-connected network for short-term wind power prediction", "Abstract": "", "Keywords": "", "DOI": "10.1080/21642583.2024.2399057", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Power Grid Power Dispatch Control Center, Guangzhou, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Guangdong Power Grid Power Dispatch Control Center, Guangzhou, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Power Grid Power Dispatch Control Center, Guangzhou, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Guangdong Power Grid Power Dispatch Control Center, Guangzhou, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Power Grid Power Dispatch Control Center, Guangzhou, People’s Republic of China"}, {"AuthorId": 6, "Name": "Xudong Hu", "Affiliation": "Southern Power Grid Digital Grid Research Institute Co., Guangzhou, People’s Republic of China"}], "References": [{"Title": "Detection of weather images by using spiking neural networks of deep learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "6147", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improving streamflow prediction using a new hybrid ELM model combined with hybrid particle swarm optimization and grey wolf optimization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "230", "Issue": "", "Page": "107379", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Measured and forecasted weather and power dataset for management of an island and grid-connected microgrid", "Authors": "<PERSON><PERSON>; <PERSON>; Jussara F. <PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "", "Page": "107513", "JournalTitle": "Data in Brief"}, {"Title": "Inception-embedded attention memory fully-connected network for short-term wind power prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "110279", "JournalTitle": "Applied Soft Computing"}, {"Title": "Parameter tuning for software fault prediction with different variants of differential evolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121251", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Attention mechanism is useful in spatio-temporal wind speed prediction: Evidence from China", "Authors": "Chengqing Yu; Guangxi Yan; Chengming Yu", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110864", "JournalTitle": "Applied Soft Computing"}, {"Title": "MWDINet: A multilevel wavelet decomposition interaction network for stock price prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122091", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid model based on discrete wavelet transform (DWT) and bidirectional recurrent neural networks for wind speed prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "127", "Issue": "", "Page": "107340", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Application of supervised random forest paradigms based on optimization and post-hoc explanation in underground stope stability prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "154", "Issue": "", "Page": "111388", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 117615928, "Title": "A decentralized control approach in hypergraph distributed optimization decomposition cases", "Abstract": "<p>We study the main decomposition approaches (primal, dual and primal–dual) for a distributed optimization problem from a dynamical system perspective where the couplings among the variables of the optimization problem are described by an undirected, unweighted hypergraph. We conduct stability analysis for the respective dynamical systems of the decomposition cases by using non linear decentralized control theoretic techniques and spectral properties of the respective communication matrices, i.e., the incidence and the Laplacian matrices of the hypergraph. Finally, we provide numerical simulations under a specific coalitional setting that demonstrate the superiority of the hypergraph compared to its respective graph analogue, the clique expansion graph, for the given decomposition algorithms in terms of convergence rate and information transmission efficiency.</p>", "Keywords": "Hypergraphs;Distributed optimization;Decomposition cases;Dynamical systems;Decentralized control", "DOI": "10.1007/s41109-024-00662-y", "PubYear": 2024, "Volume": "9", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering, University of Cambridge, Cambridge, UK; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, University of Canterbury, Christchurch, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering, University of Cambridge, Cambridge, UK"}], "References": [{"Title": "The dilemma of PID tuning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "", "Page": "65", "JournalTitle": "Annual Reviews in Control"}]}, {"ArticleId": 117615934, "Title": "Use of pulp and paper industry waste in binding and cementitious materials technology", "Abstract": "", "Keywords": "", "DOI": "10.15828/2075-8545-2024-16-4-301-309", "PubYear": 2024, "Volume": "16", "Issue": "4", "JournalId": 36948, "JournalTitle": "Nanotechnologies in Construction: A Scientific Internet-Journal", "ISSN": "", "EISSN": "2075-8545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117615948, "Title": "Automatic deep sparse clustering with a dynamic population-based evolutionary algorithm using reinforcement learning and transfer learning", "Abstract": "Clustering data effectively remains a significant challenge in machine learning, particularly when the optimal number of clusters is unknown. Traditional deep clustering methods often struggle with balancing local and global search, leading to premature convergence and inefficiency. To address these issues, we introduce ADSC-DPE-RT (Automatic Deep Sparse Clustering with a Dynamic Population-based Evolutionary Algorithm using Reinforcement Learning and Transfer Learning), a novel deep clustering approach. ADSC-DPE-RT builds on Multi-Trial Vector-based Differential Evolution (MTDE), an algorithm that integrates sparse auto-encoding and manifold learning to enable automatic clustering without prior knowledge of cluster count. However, MTDE's fixed population size can lead to either prolonged computation or premature convergence. Our approach introduces a dynamic population generation technique guided by Reinforcement Learning (RL) and Markov Decision Process (MDP) principles. This allows for flexible adjustment of population size, preventing premature convergence and reducing computation time. Additionally, we incorporate Generative Adversarial Networks (GANs) to facilitate dynamic knowledge transfer between MTDE strategies, enhancing diversity and accelerating convergence towards the global optimum. This is the first work to address the dynamic population issue in deep clustering through RL, combined with Transfer Learning to optimize evolutionary algorithms. Our results demonstrate significant improvements in clustering performance, positioning ADSC-DPE-RT as a competitive alternative to state-of-the-art deep clustering methods.", "Keywords": "", "DOI": "10.1016/j.imavis.2024.105258", "PubYear": 2024, "Volume": "151", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Digital Science, Universiti Brunei Darussalam, Brunei;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Digital Science, Universiti Brunei Darussalam, Brunei"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Digital Science, Universiti Brunei Darussalam, Brunei"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Engineering, Najafabad Branch, Islamic Azad University, Iran"}], "References": [{"Title": "An adaptive hybrid algorithm for social networks to choose groups with independent members", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "695", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "Automatic identification of the number of clusters in hierarchical clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "119", "JournalTitle": "Neural Computing and Applications"}, {"Title": "End-to-end deep representation learning for time series clustering: a comparative study", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "29", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Differential Evolution Algorithm with Hierarchical Fair Competition Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "2", "Page": "1045", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Evolutionary Algorithm with Dynamic Population Size for Constrained Multiobjective Optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "101104", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Self-supervised deep geometric subspace clustering network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "610", "Issue": "", "Page": "235", "JournalTitle": "Information Sciences"}, {"Title": "Automatic Deep Sparse Multi-Trial Vector-based Differential Evolution clustering with manifold learning and incremental technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "104712", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": *********, "Title": "Enhancing next destination prediction: A novel long short-term memory neural network approach using real-world airline data", "Abstract": "In the modern transportation industry, accurate prediction of travelers’ next destinations brings multiple benefits to companies, such as customer satisfaction and targeted marketing. This study focuses on developing a precise model that captures the sequential patterns and dependencies in travel data, enabling accurate predictions of individual travelers’ future destinations. To achieve this, a novel model architecture with a sliding window approach based on Long Short-Term Memory (LSTM) is proposed for destination prediction in the transportation industry. The experimental results highlight satisfactory performance and high scores achieved by the proposed model across different data sizes and performance metrics. Additionally, a comparative analysis highlights the superior ability of the LSTM model to capture complex temporal dependencies in travel data. This research contributes to advancing destination prediction methods, empowering companies to deliver personalized recommendations and optimize customer experiences in the dynamic travel landscape.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109266", "PubYear": 2024, "Volume": "138", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Middle East Technical University, Ankara, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, TED University, Ankara, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Osmaniye Korkut Ata University, Osmaniye, Turkey"}], "References": [{"Title": "Machine learning empowered computer networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "230", "Issue": "", "Page": "109807", "JournalTitle": "Computer Networks"}]}, {"ArticleId": *********, "Title": "Reshaping Academic Library Information Literacy Programs in the Advent of ChatGPT and Other Generative AI Technologies", "Abstract": "", "Keywords": "", "DOI": "10.1080/10875301.2024.2400132", "PubYear": 2025, "Volume": "29", "Issue": "1", "JournalId": 25067, "JournalTitle": "Internet Reference Services Quarterly", "ISSN": "1087-5301", "EISSN": "1540-4749", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University Library, California State University, Los Angeles, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University Library, California State University, Los Angeles, CA, USA"}], "References": []}, {"ArticleId": 117616003, "Title": "Methods and techniques for the information and analytical systems of data protection and conversion assessment", "Abstract": "The goal of the research is to create an algorithm for analyzing vulnerabilities discovered and to enhance the data protection technique based on system penetration testing as a means of evaluating data security. The primary responsibility is to modify established security assessment techniques to fit the evolving technical landscape, namely cloud technologies and security system requirements. The issue of data protection evaluation of electronic documents is discussed in the article, and specifically, an A/B test was conducted. To address this issue, an analysis was conducted on current data protection evaluation methodologies and techniques, and an A/B testing system was constructed. These methods help to determine the level of danger of vulnerabilities, which allows you to effectively and visually determine the overall level of system security by evaluating each vulnerability found. The article examines the practical use of the method, prospects for further development, and features of the effectiveness of penetration testing as a method of assessing the security of information systems and the development of an algorithm for evaluating the vulnerabilities found. Researching the methods and means of security assessment, the work was aimed at creating a convenient and effective tool for assessing the security of data and documents in the network. The theoretical significance of the work is determined by the expansion of scientific knowledge in the field of information and analytical systems of electronic document management. The research aims to explore and apply big data analysis and machine learning techniques to improve data protection. The novelty of the research lies in the synthesis of modern methodologies of A/B testing and analytics within a single platform, which will allow electronic documents to protect data and increase efficiency from hacker attacks. The principles of the method are described along with the main approaches and techniques that contribute to the analysis of hacker attacks on online systems and cloud environments. The components of the method, which provide for data protection, were separately analyzed, and an analysis of system testing was carried out to determine the requirements for the new system and increase the level of protection against data theft. The application of methods and tools for assessing the security of networks, which use a complex of general scientific, experimental, practical, statistical, and mathematical methods for the implementation of algorithms and the formation of the determination of the levels of danger of vulnerabilities, is considered. The method includes data analysis tools, as well as analysis techniques that provide an opportunity to conduct flexible testing of changes on the site with further analysis and impact on conversion, which is important for increasing data protection and meeting consumer needs.", "Keywords": "", "DOI": "10.23939/ujit2024.01.076", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 86518, "JournalTitle": "Ukrainian Journal of Information Technology", "ISSN": "2707-1898", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "RGraph: Asynchronous graph processing based on asymmetry of remote direct memory access", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "2", "Page": "374", "JournalTitle": "Software: Practice and Experience"}, {"Title": "The synthesis method for specifications and requirements in the process of it project reengineering", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Ukrainian Journal of Information Technology"}, {"Title": "Method and means of testing specialized components of a mobile robotics platform at operating clock frequencies", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "2", "Page": "49", "JournalTitle": "Ukrainian Journal of Information Technology"}]}]